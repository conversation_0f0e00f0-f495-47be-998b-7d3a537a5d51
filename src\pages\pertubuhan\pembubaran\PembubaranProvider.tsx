/* eslint-disable react-refresh/only-export-components */
import React, {
  createContext,
  useContext,
  useEffect,
  PropsWithChildren,
  useState,
} from "react";
import { useLocation } from "react-router-dom";
import { FormProvider } from "react-hook-form";
import { useForm, UseFormReturnType } from "@refinedev/react-hook-form";
import { useParams } from "react-router-dom";
import useQuery from "../../../helpers/hooks/useQuery";
import { useSenaraiContext } from "../SenaraiContext";
import { FieldValues } from "react-hook-form";

import { IMeetingDetail, ILiquidationFeedback } from "../../../types";

interface PembuburanContextProps {
  form: UseFormReturnType<FieldValues>;
  meetingDetailData: IMeetingDetail;
  liquidationDetailData: ILiquidationFeedback;
  isFetchingLiquidationDetail: boolean;
  isCanAccessFeedback: boolean;
  isViewOnly: boolean;
  isEditable: boolean;
  handleSetCreated: (value: boolean) => void;
}

const PembubaranContext = createContext<PembuburanContextProps | undefined>(
  undefined
);

export const usePembubaranContext = (): PembuburanContextProps => {
  const context = useContext(PembubaranContext);

  if (!context) {
    throw new Error(
      "usePembubaranContext must be used within a PembubaranProvider"
    );
  }
  return context;
};

const PembubaranProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const location = useLocation();
  const { id, liquidationId } = useParams();
  const { societyDetail } = useSenaraiContext();

  const paths = location.pathname.split("/").filter(Boolean);
  const lastPath = paths[paths.length - 1];

  const [isCreated, setIsCreated] = useState(false);

  const {
    data: liquidationDetailResponse,
    refetch: fetchLiquidationDetail,
    isLoading: isFetchingLiquidationDetail,
    reset: resetLiquidationDetail,
  } = useQuery({
    url: `society/liquidate/${liquidationId}`,
    autoFetch: false,
    onSuccess: (data) => {
      const liquidationData = data?.data?.data || null;

      if (liquidationData) {
        form.setValue("societyId", liquidationData.id);
        Object.entries(liquidationData).forEach(([key, value]) => {
          form.setValue(key as keyof FieldValues, value);
        });

        if (!liquidationData.liquidationDocumentType)
          form.setValue("liquidationDocumentType", 1);
      }
    },
  });

  /**
   * Status codes and their meanings:
   *
   * VIEW_ONLY_STATUSES:
   *  - 2  = MENUNGGU KEPUTUSAN
   *  - 3  = TOLAK
   *  - 4  = LULUS
   *
   * EDITABLE_STATUSES:
   *  - 1  = BELUM DIHANTAR
   *  - 36 = KUIRI
   *
   */

  const VIEW_ONLY_STATUSES = [2, 3, 4];
  const EDITABLE_STATUSES = [1, 36];
  const hasFeedback = lastPath === "feedback";

  const liquidationDetailData = liquidationDetailResponse?.data?.data || null;
  const isCanAccessFeedback =
    liquidationDetailData &&
    liquidationDetailData.applicationStatusCode !== 1 &&
    hasFeedback;
  // && !liquidationDetailData.isSecretary;
  const isViewOnly = VIEW_ONLY_STATUSES.includes(
    liquidationDetailData?.applicationStatusCode
  );
  const isEditable =
    EDITABLE_STATUSES.includes(liquidationDetailData?.applicationStatusCode) ||
    isCreated;

  const {
    data: meetingDetailResponse,
    refetch: fetchMeetingDetail,
    isLoading: isLoadingMeetingDetail,
  } = useQuery({
    url: `society/meeting/${liquidationDetailData?.meetingId}`,
    autoFetch: false,
  });

  const meetingDetailData = meetingDetailResponse?.data?.data || null;

  const form = useForm<FieldValues>({
    defaultValues: {
      id: undefined,
      societyId: id,
      societyNo: societyDetail?.societyNo || null,
      branchId: null,
      branchNo: null,
      meetingId: "",
      meetingDate: "",
      committeeVoteCount: null,
      committeeAttendCount: null,
      committeeAgreeCount: null,
      assets: [],
      liquidationDocumentType: "",
    },
  });

  const handleSetCreated = (value: boolean) => setIsCreated(value);

  useEffect(() => {
    if (societyDetail) {
      const { setValue } = form;
      setValue("societyNo", societyDetail.societyNo);
    }
  }, [societyDetail]);

  useEffect(() => {
    const { setValue } = form;
    if (location?.state?.branchId)
      setValue("branchId", location.state.branchId);
    if (location?.state?.branchNo)
      setValue("branchNo", location.state.branchNo);
  }, [location]);

  useEffect(() => {
    if (liquidationId) {
      fetchLiquidationDetail();
    } else {
      resetLiquidationDetail();
    }
  }, [liquidationId]);

  useEffect(() => {
    if (
      liquidationDetailData &&
      liquidationDetailData.liquidationDocumentType === 1
    )
      fetchMeetingDetail();
  }, [liquidationDetailData]);

  return (
    <PembubaranContext.Provider
      value={{
        form,
        meetingDetailData,
        liquidationDetailData,
        isFetchingLiquidationDetail,
        isCanAccessFeedback,
        isEditable,
        isViewOnly,
        handleSetCreated,
      }}
    >
      <FormProvider {...form}>{children}</FormProvider>
    </PembubaranContext.Provider>
  );
};

export default PembubaranProvider;
