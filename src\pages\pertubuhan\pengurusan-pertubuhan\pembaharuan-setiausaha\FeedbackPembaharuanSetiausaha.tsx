import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useFormContext } from "react-hook-form";
import { useSecretaryReformContext } from "./Provider";

import { Box, Card, Typography, Button, Fade } from "@mui/material";
import SecretaryForm from "./view/SecretaryForm";
import MeetingForm from "./view/meeting-form";
import FeedbackForm from "./view/FeedbackForm";
import CustomSkeleton from "@/components/custom-skeleton";
import { ButtonPrimary } from "@/components";

const formComponents: Record<number, React.ReactNode> = {
  1: <SecretaryForm />,
  2: <MeetingForm />,
  3: <FeedbackForm />,
};

const FeedbackPembaharuanSetiaUsaha = () => {
  const navigate = useNavigate();
  const { watch } = useFormContext();
  const { formStep, societyData, handleBack, handleNext, handleSetFormStep } =
    useSecretaryReformContext();

  const isUserApplicant = watch("isUserApplicant");

  const renderForm = () => formComponents[formStep] || null;

  useEffect(() => {
    if (isUserApplicant) navigate("..");
  }, [isUserApplicant]);

  return (
    <>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "16px", mb: 3 }}>
        <Card
          sx={{
            p: 3,
            backgroundColor: "var(--primary-color)",
            borderRadius: "15px",
            boxShadow: "none",
            height: "100px",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "flex-start",
            position: "relative",
            overflow: "hidden",
            "&::before": {
              content: '""',
              position: "absolute",
              top: 0,
              right: 0,
              width: "150px",
              height: "100%",
              background: `url("/pattern.svg") no-repeat right center / contain`,
              opacity: 0.2,
            },
          }}
        >
          <Box
            sx={{ display: "flex", alignItems: "center", gap: 2, zIndex: 1 }}
          >
            <Typography
              sx={{
                fontFamily: "Poppins",
                fontSize: "20px",
                fontWeight: 600,
                color: "white",
              }}
            >
              {societyData?.societyName}
            </Typography>
          </Box>
          <Typography
            sx={{
              fontFamily: "Poppins",
              fontSize: "14px",
              color: "white",
              mt: 1,
              zIndex: 1,
              opacity: 0.8,
            }}
          >
            {societyData?.societyNo}
          </Typography>
        </Card>
      </Box>

      {societyData ? (
        <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "16px" }}>
          <Fade in={true} timeout={500} key={formStep}>
            <Box>{renderForm()}</Box>
          </Fade>

          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              alignItems: "center",
              gap: 1,
              mt: 3,
              mb: 3,
            }}
          >
            {formStep !== 1 && (
              <Button
                onClick={handleBack}
                sx={{
                  minWidth: "32px",
                  height: "32px",
                  p: 0,
                  borderRadius: "4px",
                  borderColor: "#666",
                  color: "#666",
                }}
              >
                Previous
              </Button>
            )}

            {[1, 2, 3].map((step) => (
              <Button
                key={step}
                variant={formStep === step ? "contained" : "outlined"}
                onClick={() => handleSetFormStep(step)}
                sx={{
                  minWidth: "32px",
                  height: "32px",
                  p: 0,
                  borderRadius: "4px",
                  backgroundColor:
                    formStep === step ? "var(--primary-color)" : "transparent",
                  borderColor: "var(--primary-color)",
                  color: formStep === step ? "white" : "var(--primary-color)",
                  "&:hover": {
                    backgroundColor:
                      formStep === step
                        ? "var(--primary-color)"
                        : "transparent",
                  },
                }}
              >
                {step}
              </Button>
            ))}

            {formStep < 3 && (
              <ButtonPrimary
                onClick={() => handleNext()}
                sx={{
                  minWidth: "32px",
                  height: "32px",
                  p: 1,
                  borderRadius: "4px",
                  borderColor: "#666",
                  color: "#fff",
                }}
              >
                Next
              </ButtonPrimary>
            )}
          </Box>
        </Box>
      ) : (
        <CustomSkeleton />
      )}
    </>
  );
};

export default FeedbackPembaharuanSetiaUsaha;
