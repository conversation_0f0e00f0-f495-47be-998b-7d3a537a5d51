import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { Box, Grid, Typography, IconButton } from "@mui/material";

import DisabledTextField from "../../../../../components/input/DisabledTextField";
import { EyeIcon } from "../../../../../components/icons";
import { useParams } from "react-router-dom";
import { useCustom } from "@refinedev/core";
import { API_URL } from "@/api";
import {
  DocumentUploadType,
  formatDateToDDMMYYYY,
  MeetingTypeOption,
  useQuery,
} from "@/helpers";

interface FormData {
  meetingPurpose: string;
  meetingPlace: string;
  meetingMethod: string;
  platformType: string;
  meetingDate: string;
  startTime: string;
  endTime: string;
  GISInformation: string;
  meetingAddress: string;
  state: string;
  district: string;
  city: string;
  postcode: string;
  totalAttendees: number;
  meetingContent: string;
  openingRemarks: string;
  mattersDiscussed: string;
  otherMatters: string;
  closing: string;
  confirmBy: string;
  meetingMinute: string;
  meetingTime: string;
  meetingTimeTo: string;
  meetingTimeDurationMinutes: number;
}

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
  "& span": {
    color: "red",
  },
};

const MaklumatMesyuaratSection: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams();
  const branchId = id;
  const [MeetingListId, setMeetingListId] = useState();
  const [meetingId, setMeetingId] = useState("");
  const [formData, setFormData] = useState<FormData>({
    meetingPlace: "",
    meetingPurpose: "",
    meetingMethod: "",
    platformType: "",
    meetingDate: "",
    startTime: "",
    endTime: "",
    GISInformation: "",
    meetingAddress: "",
    state: "",
    district: "",
    city: "",
    postcode: "",
    totalAttendees: 0,
    meetingContent: "",
    openingRemarks: "",
    mattersDiscussed: "",
    otherMatters: "",
    closing: "",
    confirmBy: "",
    meetingMinute: "",
    meetingTimeTo: "00",
    meetingTime: "00",
    meetingTimeDurationMinutes: 0,
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const {
    data: existingDocuments,
    refetch,
    isLoading: isLoadingDocument,
  } = useQuery({
    url: "document/documentByParam",
    filters: [
      {
        field: "type",
        operator: "eq",
        value: DocumentUploadType.MEETING,
      },
      {
        field: "branchId",
        operator: "eq",
        value: branchId,
      },
      {
        field: "meetingId",
        operator: "eq",
        value: meetingId,
      },
    ],
    autoFetch: false,
  });

  useEffect(() => {
    if (meetingId) {
      refetch();
    }
  }, [meetingId]);

  const { data: branchMeetingList, isLoading: isLoadingBranchMeetingList } =
    useCustom({
      url: `${API_URL}/society/meeting/findByBranchId/${branchId}`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
      queryOptions: {
        enabled: !!branchId,
        retry: false,
        cacheTime: 0,
        onSuccess: (responseData) => {
          if (responseData?.data?.data[0]?.id) {
            setMeetingListId(responseData?.data?.data[0]?.id);
          }
        },
      },
    });

  const { data: meetingDetails, isLoading: isLoadingMeetingDetails } =
    useCustom({
      url: `${API_URL}/society/meeting//${MeetingListId}`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
      queryOptions: {
        enabled: !!MeetingListId,
        retry: false,
        cacheTime: 0,
      },
    });

  useEffect(() => {
    if (meetingDetails?.data?.data) {
      const meeting = meetingDetails?.data?.data;
      setMeetingId(meeting.id);
      setFormData((prev) => ({
        ...prev,
        meetingType: meeting.meetingType || MeetingTypeOption[0].value,
        meetingPlace: meeting.meetingPlace,
        meetingPurpose: meeting.meetingPurpose, //missing from API
        meetingMethod: meeting.meetingMethod?.toString() || "",
        platformType: meeting.platformType?.toString() || "",
        meetingDate: meeting.meetingDate,
        meetingAddress: meeting.meetingAddress,
        state: meeting.state?.toString() || "",
        district: meeting.district?.toString() || "",
        city: meeting.city,
        postcode: meeting.postcode,
        totalAttendees: meeting.totalAttendees,
        meetingContent: meeting.meetingContent,
        openingRemarks: meeting.openingRemarks,
        mattersDiscussed: meeting.mattersDiscussed,
        otherMatters: meeting.otherMatters,
        closing: meeting.closing,
        providedBy: meeting.providedBy,
        confirmBy: meeting.confirmBy,
        meetingTime: meeting.meetingTime,
        meetingTimeTo: meeting.meetingTimeTo,
        meetingTimeDurationMinutes: meeting.meetingTimeDurationMinutes,
        meetingMemberAttendances: meeting?.meetingMemberAttendances || [],
      }));
    }
  }, [meetingDetails]);

  const formatMinutes = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${String(hours).padStart(2, "0")}:${String(mins).padStart(2, "0")}`;
  };

  const handleViewDocument = (filePath: string) => {
    window.open(filePath, "_blank");
  };

  return (
    <>
      <Box
        sx={{
          backgroundColor: "white",
          padding: "18px 16px",
          borderRadius: "14px",
          marginBottom: 1,
        }}
      >
        {branchId ? (
          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography className="label">{t("date")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={formatDateToDDMMYYYY(formData?.meetingDate) || ""}
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography className="label">{t("time")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    formData?.meetingTimeDurationMinutes
                      ? formatMinutes(formData.meetingTimeDurationMinutes)
                      : "00:00"
                  }
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography className="label">{t("place")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={formData?.meetingPlace || "-"} />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography className="label">{t("attendance")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={formData?.totalAttendees || "-"} />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography className="label">
                    {t("purposeOfTheMeeting")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={formData?.meetingPurpose || "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography className="label">
                    {t("minitMesyuarat")} <span>*</span>
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <Box
                  sx={{
                    border: "1px solid var(--primary-color)",
                    borderRadius: "10px",
                    py: 1,
                    px: 2,
                    display: "flex",
                    alignItems: "center",
                  }}
                  onClick={() => {
                    const element = document.getElementById("meetingMinute");
                    if (element) {
                      element.click();
                    }
                  }}
                >
                  {existingDocuments?.data?.data?.length ? (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        width: "100%",
                      }}
                    >
                      <Typography sx={{ color: "#147C7C" }}>
                        {existingDocuments?.data?.data?.at(-1)?.name}
                      </Typography>
                      <IconButton
                        onClick={() =>
                          handleViewDocument(
                            existingDocuments?.data?.data?.at(-1)?.url
                          )
                        }
                        sx={{ color: "var(--primary-color)" }}
                      >
                        <EyeIcon
                          sx={{
                            fontSize: "1rem",
                            width: "1rem",
                            height: "1rem",
                          }}
                        />
                      </IconButton>
                    </Box>
                  ) : (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        width: "100%",
                      }}
                    >
                      <Typography className="label">
                        {t("noAvailableDocuments")}
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Grid>
            </Grid>
          </Box>
        ) : (
          <Typography className="label">{t("noData")}</Typography>
        )}
      </Box>
    </>
  );
};

export default MaklumatMesyuaratSection;
