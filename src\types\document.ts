export interface IDocumentList {
  createdBy: number;
  createdDate: string;
  modifiedBy: any;
  modifiedDate: string;
  id: number;
  type: string;
  societyId: number;
  societyNo: string;
  branchId: any;
  branchNo: any;
  meetingId: number;
  societyCommitteeId: any;
  societyNonCitizenCommitteeId: any;
  branchCommitteeId?: any;
  appealId: any;
  statementId: any;
  amendmentId: any;
  liquidationId: any;
  feedbackId: any;
  icNo: any;
  name: string;
  note: string;
  url: string;
  doc: string;
  status: number;
}
