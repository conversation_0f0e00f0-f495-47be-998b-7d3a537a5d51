export interface IDocumentList {
  createdBy: string | number;
  createdDate: string;
  modifiedBy: string | number;
  modifiedDate: string;
  id: string | number;
  type: string;
  societyId: string | number;
  societyNo: string;
  branchId: string | number;
  branchNo: any;
  meetingId: string | number;
  societyCommitteeId: string | number;
  societyNonCitizenCommitteeId: string | number;
  branchCommitteeId?: any;
  appealId: string | number;
  statementId: string | number;
  amendmentId: string | number;
  liquidationId: string | number;
  feedbackId: string | number;
  icNo: any;
  name: string;
  note: string;
  url: string;
  doc: string;
  status: number;
}
