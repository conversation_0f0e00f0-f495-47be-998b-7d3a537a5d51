import { Navigate, Outlet, Route } from "react-router-dom";
import PengurusPertubuhanInternalLayout from "../../pages/pengurusan-pertubuhan-internal/PengurusPertubuhanLayout";
import { registerRoutes } from "../../helpers/routeDetector";
import { RouteGuard } from "../../components/RouteGuard";

import KeputusanPertubuhanNav from "../../pages/pengurusan-pertubuhan-internal/View/KeputusanPertubuhanNav";
import MaklumatPertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan";
import SemakanUmum from "../../pages/pengurusan-pertubuhan-internal/SemakanUmum";
import PenyelenggaraPertubuhan from "../../pages/pengurusan-pertubuhan-internal/SelenggaraPertubuhan";
import KeputusanCawangan from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan";
import Rayuan from "../../pages/pengurusan-pertubuhan-internal/Rayuan";
import SenaraiHitam from "../../pages/pengurusan-pertubuhan-internal/SenaraiHitam";
import Kuiri from "../../pages/pengurusan-pertubuhan-internal/Kuiri";
import KeputusanIndukKelulusanSemak from "../../pages/pengurusan-pertubuhan-internal/keputusaninduk-semak";
import SenaraiAjkSemak from "../../pages/pengurusan-pertubuhan-internal/keputusaninduk-semak/views/SenaraiAjkSemak";
import PaparanAjk from "../../pages/pengurusan-pertubuhan-internal/keputusaninduk-semak/views/PaparanAjk";
import KeputusanIndukKelulusan from "../../pages/pengurusan-pertubuhan-internal/keputusanInduk";
import KeputusanIndukPembubaran from "../../pages/pengurusan-pertubuhan-internal/keputusan-induk-pembubaran";
import KeputusanIndukPembaharuanSetiausaha from "../../pages/pengurusan-pertubuhan-internal/keputusan-induk-pembaharuan-setiausaha";
import KeputusanIndukPembaharuanSetiausahaMigrasi from "../../pages/pengurusan-pertubuhan-internal/keputusan-induk-pembaharuan-setiausaha-migrasi";
import KeputusanIndukBukanWargaNegara from "../../pages/pengurusan-pertubuhan-internal/keputusan-induk-bukan-warganegara";
import KeputusanIndukPegawaiAwam from "../../pages/pengurusan-pertubuhan-internal/keputusan-induk-pegawai-awam";
import KeputusanIndukPegawaiHarta from "../../pages/pengurusan-pertubuhan-internal/keputusan-induk-pegawai-harta";
import KeputusanCawangan_Pendaftaran from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/Pendaftaran";
import KeputusanCawangan_LanjutMasa from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/LanjutMasa";
import KeputusanCawangan_PindaanNamaDanAlamat from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PindaanNamaDanAlamat";
import KeputusanCawangan_PegawaiAwam from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PegawaiAwam";
import KeputusanCawangan_PegawaiHarta from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PegawaiHarta";
import KeputusanCawangan_Pembubaran from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/Pembubaran";
import KeputusanCawangan_PermohonanBukanWarganegara from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PermohonanBukanWarganegara";
import KeputusanCawangan_PendaftaranTab from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PendaftaranTab";
import KeputusanCawangan_LanjutMasaTab from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/LanjutMasaTab";
import KeputusanCawangan_PindaNamaAlamatTab from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PindaNamaAlamat";
import KeputusanCawangan_PegawaiAmTab from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PegawaiAmTab";
import KeputusanCawangan_PegawaiHartaTab from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PegawaiHartaTab";
import KeputusanCawangan_PembubaranTab from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PembubaranTab";
import KeputusanCawangan_PermohonanBukanWarganegaraTab from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PermohonanBukanWarganegaraTab";
import MaklumatPertubuhan_Induk_Pertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan";
import MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/kemaskini";
import MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_NamaPertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/kemaskini/Nama";
import MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_AlamatPertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/kemaskini/Alamat";
import MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_AlamatSuratPertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/kemaskini/AlamatSuratMenyurat";
import MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_StatusPertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/kemaskini/Status";
import MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_KategoriPertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/kemaskini/Kategori";
import MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_TarafPertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/kemaskini/Taraf";

import MaklumatInduk from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk";
import MaklumatPertubuhan_Induk_SemakAjk from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/views/Ajk/SemakAJK";
import MaklumatPertubuhan_Induk_PaparAjk from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/views/Ajk/PaparAJK";
import MaklumatPertubuhan_Induk_AhliBukanWarganegara from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/AhliBukanWarganegara";
import MaklumatPertubuhan_Induk_PertukaranSetiausaha from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/PertukaranSetiausaha";
import MaklumatPertubuhan_Induk_PindaanPerlembagaan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/PindaanPerlembagaan";
import MaklumatPertubuhan_Induk_Pembubaran from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pembubaran";
import MaklumatPertubuhan_Induk_Rayuan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Rayuan";
import MaklumatPertubuhan_Induk_PenyataTahunan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/PenyataTahunan";

import MaklumatPertubuhan_Cawangan_Maklumat from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/Cawangan";
import MaklumatPertubuhan_Cawangan_SemakAjk from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/Cawangan/views/Ajk/SemakAJK";
import MaklumatPertubuhan_Cawangan_PaparAjk from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/Cawangan/views/Ajk/PaparAJK";
import MaklumatPertubuhan_Cawangan_PaparBWNAjk from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/Cawangan/views/Ajk/PaparanAjkBukanWn";
import MaklumatPertubuhan_Cawangan_Maklumat_Kemaskini from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/Cawangan/kemaskini";
import MaklumatPertubuhan_Cawangan_Maklumat_Kemaskini_NamaCawangan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/Cawangan/kemaskini/Nama";
import MaklumatPertubuhan_Cawangan_Maklumat_Kemaskini_StatusCawangan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/Cawangan/kemaskini/Status";

import MaklumatPertubuhan_Cawangan_PemegangJawatan_PegawaiAwam from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/PemegangJawatan/PegawaiAwam";
import MaklumatPertubuhan_Cawangan_PemegangJawatan_Juruaudit from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/PemegangJawatan/Juruaudit";
import MaklumatPertubuhan_Cawangan_PemegangJawatan_PemegangAmanah from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/PemegangJawatan/PemegangAmanah";
import MaklumatPertubuhan_Cawangan_PemegangJawatan_PegawaiHarta from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/PemegangJawatan/PegawaiHarta";
import MaklumatPertubuhan_Cawangan_AhliBukanWarganegara from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/AhliBukanWarganegara";

import MaklumatCawangan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan";
import MaklumatPertubuhan_Cawangan_SijilMigrasiCawangan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/SijilMigrasiCawangan";
import KeputusanCawangan_PermohonanBukanWarganegaraJawatanKuasa from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PermohonanBukanWarganegara/JawatanKuasa";

import SemakanUmumPendaftaranLama from "../../pages/pengurusan-pertubuhan-internal/SemakanUmum/noPendaftaranLama";
import SemakanUmumPemegegangJawatan from "../../pages/pengurusan-pertubuhan-internal/SemakanUmum/pemegangJawatan";

import { TabProvider } from "../../contexts/tabProvider";
import KeputusanIndukPindaanUndangUndangInduk from "../../pages/pengurusan-pertubuhan-internal/keputusan-induk-pindaan-undang-undang-induk";
import RayuanKelulusanSemak from "../../pages/pengurusan-pertubuhan-internal/Rayuan/views";
import KuiriPendaftaranPertubuhanIndukTab from "../../pages/pengurusan-pertubuhan-internal/Kuiri/PendaftaranPertubuhanInduk";
import KuiriPendaftaranCawanganTab from "../../pages/pengurusan-pertubuhan-internal/Kuiri/PendaftaranCawangan";
import KuiriPindaanUndangUndangIndukTab from "../../pages/pengurusan-pertubuhan-internal/Kuiri/PindaanUndangUndangInduk";
import KuiriRayuanTab from "../../pages/pengurusan-pertubuhan-internal/Kuiri/Rayuan";
import KuiriPembaharuanSetiausahaTab from "../../pages/pengurusan-pertubuhan-internal/Kuiri/PembaharuanSetiausaha";
import BranchPaparanAjkBukanWn from "@/pages/pengurusan-pertubuhan-internal/KeputusanCawangan/Pendaftaran/views/BranchPaparanAjkBukanWn";
import BranchPaparanAJK from "@/pages/pengurusan-pertubuhan-internal/KeputusanCawangan/Pendaftaran/views/BranchPaparanAjk";
import KeputusanIndukExternalAgency from "@/pages/pengurusan-pertubuhan-internal/keputusan-external-agency";
import KuiriRayuanKelulusanSemak from "@/pages/pengurusan-pertubuhan-internal/Kuiri/Rayuan/views";
import PenyataTahunanBranchDetail from "@/pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/PenyataTahunan";
import KuiriPindaanDetails from "@/pages/pengurusan-pertubuhan-internal/Kuiri/Pindaan";
import { PertubuhanAccordions } from "@/pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/PertubuhanAccordions";
import { PertubuhanMesyuaratById } from "@/pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/views/PertubuhanMesyuaratById";
import { NEW_PermissionNames, pageAccessEnum } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";
import { TabGuard } from "@/layouts/tabGuard";

// const routes = [
//   {
//     path: "form-pembubaran",
//     page: <FormPembubaranInternal />,
//   },
//   {
//     path: "pembubaran-cawangan",
//     page: <KeputusanCawanganList title="Keputusan Cawangan" />,
//   },
//   {
//     path: "rayuan",
//     page: <PengurusanPertubuhanPembubaranList title="Jenis Rayuan" />,
//   },
//   {
//     path: "rayuan",
//     page: <div />,
//   },
//   {
//     path: "senarai-hitam",
//     page: <PengurusanPertubuhanPembubaranList title="Senarai Hitam" />,
//   },
//   {
//     path: "kuiri",
//     page: <PengurusanPertubuhanPembubaranList title="Jenis Kuiri" />,
//   },
//   {
//     path: "maklumat-pertubuhan",
//     page: <PengurusanPertubuhanPembubaranList title="Maklumat Pertubuhan" />,
//   },
//   {
//     path: "penyelenggara-pertubuhan",
//     page: (
//       <PengurusanPertubuhanPembubaranList title="Penyelenggara Pertubuhan" />
//     ),
//   },
// ];

// Layout component to wrap all pengurus-pertubuhan-internal routes with protection
const PengurusPertubuhanInternalGuardLayout = () => {
  if (!AuthHelper.hasAuthority([NEW_PermissionNames.PERTUBUHAN.label])) {
    return <Navigate to="/forbidden" replace />;
  }
  return (
    <RouteGuard
      autoUpdatePortal={true}
      showDebugInfo={process.env.NODE_ENV === "development"}
    >
      <PengurusPertubuhanInternalLayout />
    </RouteGuard>
  );
};

// Register internal routes
// TODO: Team should register specific paths here
registerRoutes({
  // Main route
  "/pengurus-pertubuhan": "shared",

  // Example registrations for main sections (uncomment and modify as needed):
  // '/pengurus-pertubuhan/keputusan-pertubuhan': 'internal',
  "/pengurus-pertubuhan/maklumat-pertubuhan": "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/induk": "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/induk/pertubuhan/view/:id":
    "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/induk/pertubuhan/ajk/:id":
    "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/induk/pertubuhan/view/:id/mesyuarat/:id":
    "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/induk/pertubuhan/ahli-bukan-warganegara/:id/:id":
    "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/induk/pertubuhan/pertukaran-setiausaha/:id":
    "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/induk/pertubuhan/pindaan-perlembagaan/:id/:id":
    "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/induk/pertubuhan/pembubaran/:id/:id":
    "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/induk/pertubuhan/rayuan":
    "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/induk/pertubuhan/penyata-tahunan":
    "internal",
  // '/pengurus-pertubuhan/semakan-umum': 'internal',
  // '/pengurus-pertubuhan/penyelenggara-pertubuhan': 'internal',

  // Example registrations for specific pages (uncomment and modify as needed):
  "/pengurus-pertubuhan/keputusan-pertubuhan": "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk": "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/:id/senarai-ajk-semak":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/:id/paparan-ajk":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan": "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/lanjut-masa":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/lanjut-masa/:id":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pindaan-nama-dan-alamat":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pindaan-nama-dan-alamat/:id":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pindaan-nama-dan-alamat/:id/:id":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pembubaran":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/permohonan-bukan-warganegara":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/permohonan-bukan-warganegara/:id":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pegawai-awam":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pegawai-awam/:id":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pegawai-harta":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pegawai-harta/:id":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/rayuan": "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/rayuan/kelulusan/:id/:id":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/senarai-hitam": "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/kuiri": "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/kuiri/pendaftaran-cawangan":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/kuiri/pendaftaran-cawangan/:id":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/kuiri/pendaftaran-cawangan/:id/cawangan-paparan-ajk/:mid":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/kuiri/pendaftaran-cawangan/:id/cawangan-paparan-ajk-bukan-wn/:mid":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/kuiri/pindaan-undang-undang-induk":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/kuiri/rayuan": "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/kuiri/rayuan/:id": "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/kuiri/pembaharuan-setiausaha":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/kuiri/pendaftaran/:id": "internal",

  // Example registrations for dynamic routes (uncomment and modify as needed):
  // '/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/kelulusan/:id': 'internal',
  // '/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/pembubaran/:id': 'internal',
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pendaftaran/:id":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pendaftaran/:id/cawangan-paparan-ajk/:mid":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pendaftaran/:id/cawangan-paparan-ajk/:id":
    "internal",

  "/pengurus-pertubuhan/maklumat-pertubuhan/cawangan/maklumat/view": "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/cawangan": "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/cawangan/ahli-bukan-warganegara/:societyId/:ajkId":
    "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/cawangan/pertubuhan/penyata-tahunan":
    "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/cawangan/maklumat/view/:id/:id":
    "internal",
  "/pengurus-pertubuhan/maklumat-pertubuhan/cawangan/maklumat/ajk/papar/:id/:id/:id":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/pembubaran/:id":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/pembaharuan-setiausaha/:id":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pembubaran/:id/:branchId":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/kuiri/pembaharuan-setiausaha/:id":
    "internal",
  "/pengurus-pertubuhan/keputusan-pertubuhan/kuiri/pembaharuan-setiausaha-migrasi/:id":
    "internal",
});

export const pengurus_pertubuhan_internal = {
  routes: (
    <Route
      path="pengurus-pertubuhan"
      element={<PengurusPertubuhanInternalGuardLayout />}
    >
      <Route index element={<Navigate to="keputusan-pertubuhan" replace />} />
      <Route path="keputusan-pertubuhan" element={<KeputusanPertubuhanNav />}>
        <Route index element={<Navigate to="keputusan-induk" replace />} />
        <Route
          path="keputusan-induk"
          element={
            <TabGuard
              permissionList={[
                NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                  .children.KEPUTUSAN_INDUK.label,
              ]}
            >
              <KeputusanIndukKelulusan />
            </TabGuard>
          }
        />
        <Route
          path="keputusan-induk"
          element={
            <TabGuard
              permissionList={[
                NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                  .children.KEPUTUSAN_INDUK.label,
              ]}
            >
              <KeputusanIndukKelulusan />
            </TabGuard>
          }
        />
        {/*Tab page*/}
        <Route
          index
          path="keputusan-induk/kelulusan/:id"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                  .children.KEPUTUSAN_INDUK.children
                  .PENDAFTARAN_PERTUBUHAN_INDUK.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <KeputusanIndukKelulusanSemak />
            </TabGuard>
          }
        />
        <Route
          index
          path="keputusan-induk/external-agency/:id"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                  .children.KEPUTUSAN_INDUK.children
                  .PENDAFTARAN_INDUK_MENUNGGU_ULASAN_LUAR.label
              }
              withAccess
              permissionType={pageAccessEnum.Update}
            >
              <KeputusanIndukExternalAgency />
            </TabGuard>
          }
        />
        <Route
          index
          path="keputusan-induk/pindaan-perlembagaan/:amendmentId/:id"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                  .children.KEPUTUSAN_INDUK.children.PINDAAN_PERLEMBAGAAN.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <KeputusanIndukPindaanUndangUndangInduk />
            </TabGuard>
          }
        />
        <Route
          index
          path="keputusan-induk/pembubaran/:id"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                  .children.KEPUTUSAN_INDUK.children.PEMBUBARAN.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <KeputusanIndukPembubaran />
            </TabGuard>
          }
        />
        <Route
          index
          path="keputusan-induk/permohonan-bukan-warganegara/:id"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                  .children.KEPUTUSAN_INDUK.children
                  .PERMOHONAN_BUKAN_WARGANEGARA.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <KeputusanIndukBukanWargaNegara />
            </TabGuard>
          }
        />
        <Route
          index
          path="keputusan-induk/pembaharuan-setiausaha/:id"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                  .children.KEPUTUSAN_INDUK.children.PEMBAHARUAN_SETIAUSAHA
                  .label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <KeputusanIndukPembaharuanSetiausaha />
            </TabGuard>
          }
        />
        <Route
          index
          path="keputusan-induk/pembaharuan-setiausaha-migrasi/:id"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                  .children.KEPUTUSAN_INDUK.children
                  .PEMBAHARUAN_SETIAUSAHA_MIGRASI.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <KeputusanIndukPembaharuanSetiausahaMigrasi />
            </TabGuard>
          }
        />
        <Route
          index
          path="keputusan-induk/pegawai-awam/:id"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                  .children.KEPUTUSAN_INDUK.children.PEGAWAI_AWAM.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <KeputusanIndukPegawaiAwam />
            </TabGuard>
          }
        />
        <Route
          index
          path="keputusan-induk/pegawai-harta/:id"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                  .children.KEPUTUSAN_INDUK.children.PEGAWAI_HARTA.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <KeputusanIndukPegawaiHarta />
            </TabGuard>
          }
        />
        <Route
          path="keputusan-cawangan"
          element={
            <TabGuard
              permissionList={[
                NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                  .children.KEPUTUSAN_CAWANGAN.label,
              ]}
            >
              <Outlet />
            </TabGuard>
          }
        >
          <Route element={<KeputusanCawangan />}>
            <Route
              index
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children.PENDAFTARAN_CAWANGAN
                      .label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanCawangan_PendaftaranTab />
                </TabGuard>
              }
            />
            <Route
              path="pendaftaran/:id"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children.PENDAFTARAN_CAWANGAN
                      .label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanCawangan_Pendaftaran />
                </TabGuard>
              }
            />
            <Route
              path="pendaftaran/:id/cawangan-paparan-ajk/:mid"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children.PENDAFTARAN_CAWANGAN
                      .label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <BranchPaparanAJK />
                </TabGuard>
              }
            />
            <Route
              path="pendaftaran/:id/cawangan-paparan-ajk-bukan-wn/:mid"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children.PENDAFTARAN_CAWANGAN
                      .label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <BranchPaparanAjkBukanWn />
                </TabGuard>
              }
            />
            <Route
              path="lanjut-masa"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children.LANJUT_MASA.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanCawangan_LanjutMasaTab />
                </TabGuard>
              }
            />
            <Route
              path="lanjut-masa/:id"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children.LANJUT_MASA.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanCawangan_LanjutMasa />
                </TabGuard>
              }
            />
            <Route
              path="pindaan-nama-dan-alamat"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children
                      .PINDAAN_NAMA_DAN_ALAMAT.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanCawangan_PindaNamaAlamatTab />
                </TabGuard>
              }
            />
            <Route
              path="pindaan-nama-dan-alamat/:id/:branchAmendmentId"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children
                      .PINDAAN_NAMA_DAN_ALAMAT.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanCawangan_PindaanNamaDanAlamat />
                </TabGuard>
              }
            />
            <Route
              path="pembubaran"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children.PEMBUBARAN_CAWANGAN
                      .label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanCawangan_PembubaranTab />
                </TabGuard>
              }
            />
            <Route
              path="pembubaran/:id/:branchId"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children.PEMBUBARAN_CAWANGAN
                      .label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanIndukPembubaran />
                </TabGuard>
              }
            />
            {/* non-citizen ajk branch view */}
            <Route
              path="permohonan-bukan-warganegara"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children
                      .PERMOHONAN_BUKAN_WARGANEGARA_CAWANGAN.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanCawangan_PermohonanBukanWarganegaraTab />
                </TabGuard>
              }
            />
            <Route
              path="permohonan-bukan-warganegara/:id"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children
                      .PERMOHONAN_BUKAN_WARGANEGARA_CAWANGAN.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanCawangan_PermohonanBukanWarganegara />
                </TabGuard>
              }
            >
              <Route
                path="jawatan-kuasa/:ajkId"
                element={
                  <TabGuard
                    permissionName={
                      NEW_PermissionNames.PERTUBUHAN.children
                        .KEPUTUSAN_PERTUBUHAN.children.KEPUTUSAN_CAWANGAN
                        .children.PERMOHONAN_BUKAN_WARGANEGARA_CAWANGAN.label
                    }
                    withAccess
                    permissionType={pageAccessEnum.Read}
                  >
                    <KeputusanCawangan_PermohonanBukanWarganegaraJawatanKuasa />
                  </TabGuard>
                }
              />
            </Route>
            <Route
              path="pegawai-awam"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children
                      .PEGAWAI_AWAM_CAWANGAN.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanCawangan_PegawaiAmTab />
                </TabGuard>
              }
            />
            <Route
              path="pegawai-awam/:id"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children
                      .PEGAWAI_AWAM_CAWANGAN.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanCawangan_PegawaiAwam />
                </TabGuard>
              }
            />
            <Route
              path="pegawai-harta"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children
                      .PEGAWAI_HARTA_CAWANGAN.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanCawangan_PegawaiHartaTab />
                </TabGuard>
              }
            />
            <Route
              path="pegawai-harta/:id"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KEPUTUSAN_CAWANGAN.children
                      .PEGAWAI_HARTA_CAWANGAN.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanCawangan_PegawaiHarta />
                </TabGuard>
              }
            />
          </Route>
          {/* ajk branch view */}

          <Route
            path="pembubaran/:id"
            element={<KeputusanCawangan_Pembubaran />}
          />
        </Route>
        <Route
          path="rayuan"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                  .children.RAYUAN.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <Rayuan />
            </TabGuard>
          }
        />
        <Route
          index
          path="rayuan/kelulusan/:id/:type"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                  .children.RAYUAN.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <RayuanKelulusanSemak />
            </TabGuard>
          }
        />
        <Route path="senarai-hitam" element={<SenaraiHitam />} />
        <Route path="kuiri" element={<Outlet />}>
          <Route
            element={
              <TabGuard
                permissionList={[
                  NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                    .children.KUIRI.label,
                ]}
              >
                <Kuiri />
              </TabGuard>
            }
          >
            <Route
              index
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KUIRI.children
                      .PENDAFTARAN_PERTUBUHAN_INDUK_KUIRI.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KuiriPendaftaranPertubuhanIndukTab />
                </TabGuard>
              }
            />
            <Route
              path="pendaftaran-cawangan"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KUIRI.children.PENDAFTARAN_CAWANGAN_KUIRI.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KuiriPendaftaranCawanganTab />
                </TabGuard>
              }
            />
            <Route
              path="pendaftaran-cawangan/:id"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KUIRI.children.PENDAFTARAN_CAWANGAN_KUIRI.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KeputusanCawangan_Pendaftaran />
                </TabGuard>
              }
            />
            <Route
              path="pendaftaran-cawangan/:id/cawangan-paparan-ajk/:mid"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KUIRI.children.PENDAFTARAN_CAWANGAN_KUIRI.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <BranchPaparanAJK />
                </TabGuard>
              }
            />
            <Route
              path="pendaftaran-cawangan/:id/cawangan-paparan-ajk-bukan-wn/:mid"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KUIRI.children.PENDAFTARAN_CAWANGAN_KUIRI.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <BranchPaparanAjkBukanWn />
                </TabGuard>
              }
            />
            <Route
              path="pindaan-undang-undang-induk"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KUIRI.children.PINDAAN_PERLEMBAGAAN_KUIRI.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KuiriPindaanUndangUndangIndukTab />
                </TabGuard>
              }
            />
            <Route
              path="rayuan"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KUIRI.children.RAYUAN_KUIRI.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KuiriRayuanTab />
                </TabGuard>
              }
            />
            <Route
              index
              path="rayuan/:id"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KUIRI.children.RAYUAN_KUIRI.label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KuiriRayuanKelulusanSemak />
                </TabGuard>
              }
            />

            <Route
              path="pembaharuan-setiausaha"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                      .children.KUIRI.children.PEMBAHARUAN_SETIAUSAHA_KUIRI
                      .label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <KuiriPembaharuanSetiausahaTab />
                </TabGuard>
              }
            />
          </Route>
          <Route
            path="pendaftaran/:id"
            element={
              <TabGuard
                permissionName={
                  NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                    .children.KUIRI.children.PENDAFTARAN_PERTUBUHAN_INDUK_KUIRI
                    .label
                }
                withAccess
                permissionType={pageAccessEnum.Read}
              >
                <KeputusanIndukKelulusanSemak />
              </TabGuard>
            }
          />

          <Route
            path="pindaan-undang-undang-induk/:amendmentId/:id"
            element={
              <TabGuard
                permissionName={
                  NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                    .children.KUIRI.children.PINDAAN_PERLEMBAGAAN_KUIRI.label
                }
                withAccess
                permissionType={pageAccessEnum.Read}
              >
                <KuiriPindaanDetails />
              </TabGuard>
            }
          />
          <Route
            path="pembaharuan-setiausaha/:id"
            element={
              <TabGuard
                permissionName={
                  NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN
                    .children.KUIRI.children.PEMBAHARUAN_SETIAUSAHA_KUIRI.label
                }
                withAccess
                permissionType={pageAccessEnum.Read}
              >
                <KeputusanIndukPembaharuanSetiausaha />
              </TabGuard>
            }
          />
        </Route>
        {/* ajk semak page */}
        <Route
          path="keputusan-induk/:id/senarai-ajk-semak"
          element={<SenaraiAjkSemak />}
        />
        {/* ajk paparan page */}
        <Route
          path="keputusan-induk/:id/paparan-ajk"
          element={<PaparanAjk />}
        />
      </Route>
      <Route
        path="maklumat-pertubuhan"
        element={
          <TabGuard
            permissionList={[
              NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.label,
            ]}
          >
            <TabProvider>
              <MaklumatPertubuhan />
            </TabProvider>
          </TabGuard>
        }
      >
        <Route index element={<Navigate to="induk" replace />} />
        <Route
          path="induk"
          element={
            <TabGuard
              permissionList={[
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.INDUK.label,
              ]}
            >
              <MaklumatInduk />
            </TabGuard>
          }
        />

        <Route
          path="induk/pertubuhan/view/:id"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.INDUK.children.PERTUBUHAN_INDUK.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <MaklumatPertubuhan_Induk_Pertubuhan />
            </TabGuard>
          }
        >
          <Route
            index
            element={
              <TabGuard
                permissionName={
                  NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                    .children.INDUK.children.PERTUBUHAN_INDUK.label
                }
                //
                withAccess
                permissionType={pageAccessEnum.Read}
              >
                <PertubuhanAccordions />
              </TabGuard>
            }
          />
          <Route
            path="mesyuarat/:meetingId"
            element={
              <TabGuard
                permissionName={
                  NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                    .children.INDUK.children.PERTUBUHAN_INDUK.label
                }
                //
                withAccess
                permissionType={pageAccessEnum.Read}
              >
                <PertubuhanMesyuaratById />
              </TabGuard>
            }
          />
        </Route>

        <Route
          index
          path="induk/pertubuhan/ajk/:id"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.INDUK.children.PERTUBUHAN_INDUK.label
              }
              //
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <MaklumatPertubuhan_Induk_SemakAjk />
            </TabGuard>
          }
        />
        <Route
          index
          path="induk/pertubuhan/ajk/papar"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.INDUK.children.PERTUBUHAN_INDUK.label
              }
              //
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <MaklumatPertubuhan_Induk_PaparAjk />
            </TabGuard>
          }
        />
        <Route
          path="induk/pertubuhan/ahli-bukan-warganegara/:societyId/:ajkId"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.INDUK.children.AHLI_BUKAN_WARGANEGARA_INDUK.label
              }
              //
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <MaklumatPertubuhan_Induk_AhliBukanWarganegara />
            </TabGuard>
          }
        />
        <Route
          index
          path="induk/pertubuhan/pertukaran-setiausaha/:id"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.INDUK.children.PERTUKARAN_SETIAUSAHA.label
              }
              //
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <KeputusanIndukPembaharuanSetiausaha />
            </TabGuard>
          }
        />
        <Route
          index
          path="induk/pertubuhan/pindaan-perlembagaan/:amendmentId/:societyId"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.INDUK.children.PINDAAN_PERLEMBAGAAN_INDUK.label
              }
              //
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <MaklumatPertubuhan_Induk_PindaanPerlembagaan />
            </TabGuard>
          }
        />
        <Route
          index
          path="induk/pertubuhan/pembubaran/:societyId/:liquidationId"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.INDUK.children.PEMBUBARAN_INDUK.label
              }
              //
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <MaklumatPertubuhan_Induk_Pembubaran />
            </TabGuard>
          }
        />
        <Route
          index
          path="induk/pertubuhan/rayuan"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.INDUK.children.RAYUAN_INDUK.label
              }
              //
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <MaklumatPertubuhan_Induk_Rayuan />
            </TabGuard>
          }
        />
        <Route
          index
          path="induk/pertubuhan/penyata-tahunan"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.INDUK.children.PENYATA_TAHUNAN_INDUK.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <MaklumatPertubuhan_Induk_PenyataTahunan />
            </TabGuard>
          }
        />

        {/* <Route
          index
          path="induk/pertubuhan/kemaskini"
          element={<MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini />}
        /> */}
        {/* <Route
          index
          path="induk/pertubuhan/kemaskini/nama-pertubuhan"
          element={
            <MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_NamaPertubuhan />
          }
        />
        <Route
          index
          path="induk/pertubuhan/kemaskini/alamat-pertubuhan"
          element={
            <MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_AlamatPertubuhan />
          }
        />
        <Route
          index
          path="induk/pertubuhan/kemaskini/alamat-surat-pertubuhan"
          element={
            <MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_AlamatSuratPertubuhan />
          }
        />
        <Route
          index
          path="induk/pertubuhan/kemaskini/status-pertubuhan"
          element={
            <MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_StatusPertubuhan />
          }
        />
        <Route
          index
          path="induk/pertubuhan/kemaskini/kategori-pertubuhan"
          element={
            <MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_KategoriPertubuhan />
          }
        />
        <Route
          index
          path="induk/pertubuhan/kemaskini/taraf-pertubuhan"
          element={
            <MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_TarafPertubuhan />
          }
        />
        <Route
          index
          path="induk/pindaan/:id"
          element={<MaklumatPertubuhan_Induk_Pembubaran />}
        /> */}
        <Route
          path="cawangan"
          element={
            <TabGuard
              permissionList={[
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.CAWANGAN.label,
              ]}
            >
              <MaklumatCawangan />
            </TabGuard>
          }
        />
        <Route
          index
          path="cawangan/maklumat/view/:societyId/:branchId"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.CAWANGAN.children.CAWANGAN_DETAIL.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <MaklumatPertubuhan_Cawangan_Maklumat />
            </TabGuard>
          }
        />
        <Route
          index
          path="cawangan/maklumat/ajk/papar/:societyId/:branchId/:ajkId"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.CAWANGAN.children.CAWANGAN_DETAIL.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <MaklumatPertubuhan_Cawangan_PaparAjk />
            </TabGuard>
          }
        />
        <Route
          index
          path="cawangan/maklumat/ajk/BWNpapar/:societyId/:branchId/:ajkId"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.CAWANGAN.children.CAWANGAN_DETAIL.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <MaklumatPertubuhan_Cawangan_PaparBWNAjk />
            </TabGuard>
          }
        />
        <Route
          index
          path="cawangan/maklumat/ajk"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.CAWANGAN.children.CAWANGAN_DETAIL.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <MaklumatPertubuhan_Cawangan_SemakAjk />
            </TabGuard>
          }
        />
        {/* <Route
          index
          path="cawangan/maklumat/kemaskini"
          element={<MaklumatPertubuhan_Cawangan_Maklumat_Kemaskini />}
        /> */}
        {/* <Route
          index
          path="cawangan/maklumat/kemaskini/nama-cawangan"
          element={
            <MaklumatPertubuhan_Cawangan_Maklumat_Kemaskini_NamaCawangan />
          }
        /> */}
        {/* <Route
          index
          path="cawangan/maklumat/kemaskini/status-cawangan"
          element={
            <MaklumatPertubuhan_Cawangan_Maklumat_Kemaskini_StatusCawangan />
          }
        /> */}
        <Route
          index
          path="cawangan/ahli-bukan-warganegara/:societyId/:ajkId"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.CAWANGAN.children.AHLI_BUKAN_WARGANEGARA_CAWANGAN
                  .label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <MaklumatPertubuhan_Cawangan_AhliBukanWarganegara />
            </TabGuard>
          }
        />
        {/* <Route
          index
          path="cawangan/pegawai-awam"
          element={<MaklumatPertubuhan_Cawangan_PemegangJawatan_PegawaiAwam />}
        />
        <Route
          index
          path="cawangan/juruaudit"
          element={<MaklumatPertubuhan_Cawangan_PemegangJawatan_Juruaudit />}
        />
        <Route
          index
          path="cawangan/pemegang-amanah"
          element={
            <MaklumatPertubuhan_Cawangan_PemegangJawatan_PemegangAmanah />
          }
        />
        <Route
          index
          path="cawangan/pegawai-harta"
          element={<MaklumatPertubuhan_Cawangan_PemegangJawatan_PegawaiHarta />}
        />

        <Route
          index
          path="cawangan/sijil-migrasi-cawangan"
          element={<MaklumatPertubuhan_Cawangan_SijilMigrasiCawangan />}
        /> */}
        <Route
          index
          path="cawangan/pertubuhan/penyata-tahunan"
          element={
            <TabGuard
              permissionName={
                NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN
                  .children.CAWANGAN.children.PENYATA_TAHUNAN_CAWANGAN.label
              }
              withAccess
              permissionType={pageAccessEnum.Read}
            >
              <PenyataTahunanBranchDetail />
            </TabGuard>
          }
        />
      </Route>
      <Route path="semakan-umum" element={<SemakanUmum />}>
        <Route index element={<Navigate to="no-pendaftaran-lama" replace />} />
        <Route
          path="no-pendaftaran-lama"
          element={<SemakanUmumPendaftaranLama />}
        />
        <Route
          path="pemegang-jawatan"
          element={<SemakanUmumPemegegangJawatan />}
        />
      </Route>

      {/* unsure of use of this route as its repeated in the same place for the same page  */}
      {/* <Route
        path="maklumat-pertubuhan"
        element={
          <TabProvider>
            <MaklumatPertubuhan />
          </TabProvider>
        }
      /> */}
      <Route
        path="penyelenggara-pertubuhan"
        element={<PenyelenggaraPertubuhan />}
      />
    </Route>
  ),
};
