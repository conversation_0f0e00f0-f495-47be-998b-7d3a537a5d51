import {
  Box,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import TuneIcon from "@mui/icons-material/Tune";
import AddIcon from "@mui/icons-material/Add";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DeleteIcon from "@/assets/svg/icon-trash.svg?react";
import EditIcon from "@mui/icons-material/Edit";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { HideOrDisplayInherit } from "../../../../helpers/enums";

const JuruAudit: React.FC = () => {
  const { t } = useTranslation();
  const [memberList, setmemberList] = useState([
    {
      id: 1,
      namaAhli: "<PERSON> rashid",
      noPengenalanDiri: "123456789",
      emel: "<EMAIL>",
      tarikhLantik: "11/09/2024",
      status: "Aktif",
    },
    {
      id: 2,
      namaAhli: "Hajah",
      noPengenalanDiri: "123456789",
      emel: "<EMAIL>",
      tarikhLantik: "11/09/2024",
      status: "Tidak Aktif",
    },
  ]);
  const navigate = useNavigate();

  const handleDaftarJuruaudit = () => {
    navigate("create");
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  return (
    <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
        mb: 2,
      }}
    >
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography
          sx={{
            color: "#666666",
            fontSize: 14,
            fontWeight: "400 !important",
          }}
        >
          <span style={{ color: "red", fontWeight: "bold" }}>
            {t("peringatan")} :
          </span>{" "}
          Sila pastikan bilangan Juruaudit yang berstatus aktif mengikut
          bilangan di dalam perlembagaan.
        </Typography>
      </Box>

      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          textAlign: "center",
          p: 3,
          mb: 2,
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{
            color: "var(--primary-color)",
            fontSize: 18,
            fontWeight: "500 !important",
          }}
        >
          {t("bilanganJuruauditTerkini")}
        </Typography>
        <Typography
          sx={{
            color: "#666666",
            fontSize: 18,
            fontWeight: "500 !important",
          }}
        >
          {memberList.length} Orang
        </Typography>
      </Box>

      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          gap: 3,
          mb: 2,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: 14,
              fontWeight: "500 !important",
            }}
          >
            Jenis Juruaudit di dalam perlembagaan:{" "}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "600 !important",
            }}
          >
            Dalaman
          </Typography>
        </Box>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: 14,
              fontWeight: "500 !important",
            }}
          >
            Bilangan Juruaudit di dalam perlembagaan:{" "}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "600 !important",
            }}
          >
            {memberList.length} Orang
          </Typography>
        </Box>
      </Box>

      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("auditorList")}
        </Typography>
        <Box sx={{ my: 3 }} display={"flex"} justifyContent={"flex-end"}>
          <ButtonOutline onClick={handleDaftarJuruaudit}>
            {t("registerAuditor")}
          </ButtonOutline>
        </Box>
        <TableContainer
          component={Paper}
          sx={{
            boxShadow: "none",
            border: "1px solid #e0e0e0",
            backgroundColor: "white",
            borderRadius: 2.5 * 1.5,
            p: 1,
            mb: 3,
          }}
        >
          <Table>
            <TableHead>
              <TableRow>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                  }}
                >
                  {t("name")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                  }}
                >
                  {t("idNumber")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                  }}
                >
                  {t("email")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                  }}
                >
                  {t("tarikhLantik")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                  }}
                >
                  {t("status")}
                </TableCell>
                <TableCell
                  align="right"
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                  }}
                ></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {memberList.map((row, index) => (
                <TableRow key={row.id}>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                    }}
                  >
                    {row.namaAhli}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                    }}
                  >
                    {row.emel}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                    }}
                  >
                    {row.noPengenalanDiri}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                    }}
                  >
                    {row.tarikhLantik}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                    }}
                  >
                    {row.status}
                  </TableCell>
                  <TableCell
                    align="right"
                    sx={{ borderBottom: "1px solid #e0e0e0", p: 1 }}
                  >
                    {row.status === "Aktif" ? (
                      <>
                        <IconButton>
                          <EditIcon sx={{ color: "var(--primary-color)" }} />
                        </IconButton>
                        <IconButton>
                          <DeleteIcon />
                        </IconButton>
                      </>
                    ) : (
                      <IconButton>
                        <VisibilityIcon sx={{ color: "#666666" }} />
                      </IconButton>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </Box>
  );
};

export default JuruAudit;
