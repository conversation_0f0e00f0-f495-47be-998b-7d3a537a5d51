import React, { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { useTheme, useMediaQuery, Fade, Grid } from "@mui/material";
import { useCustom } from "@refinedev/core";
import Input from "../../../../components/input/Input";
import { useNavigate, useSearchParams, useParams } from "react-router-dom";
import { API_URL } from "../../../../api";
import { LoadingOverlay } from "../../../../components/loading";
import { parseDateToISO8601 } from "@/helpers";
import { useSelector } from "react-redux";
import { selectCalculatedPayment } from "@/redux/paymentReducer";

export const Online = () => {
  const { t } = useTranslation();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogAlertSuccessSaveOpen, setDialogAlertSuccessSaveOpen] =
    useState(false);

  const navigate = useNavigate();

  const { id } = useParams();
  const societyId = id;

  const [searchParams] = useSearchParams();

  const branchId = searchParams.get("id");

  // Get payment data from Redux
  const paymentRedux = useSelector(selectCalculatedPayment);

  const [branchData, setBranchData] = useState<any>({});
  const { data: branchList, isLoading: isLoadingBranch } = useCustom<any>({
    url: `${API_URL}/society/branch/getById/${branchId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: branchId !== null,
      retry: false,
      cacheTime: 0,
    },
  });

  useEffect(() => {
    if (branchList?.data?.data) {
      setBranchData(branchList?.data?.data);
    }
  }, [branchList]);

  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${societyId}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  // @ts-ignore
  // const societyData?.data?.data? = useSelector((state) => state.societyData.data)

  const getUserDetails = localStorage.getItem("user-details");
  const userName = getUserDetails ? JSON.parse(getUserDetails).name : "";
  const email = getUserDetails ? JSON.parse(getUserDetails).email : "";
  const icNo = getUserDetails
    ? JSON.parse(getUserDetails).identificationNo
    : "";

  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const downloadFile = (filePath: string) => {
    fetch(filePath)
      .then((res) => res.blob())
      .then((blob) => {
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = "Pendaftaran Cawangan (Pembayaran ONLINE).pdf";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      })
      .catch(console.error);
  };

  const handleCetak = async () => {
    try {
      if (societyData?.data?.data?.id) {
        const response = await fetch(`${API_URL}/society/pdf/paymentReceipt`, {
          method: "post",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            paymentType: "Pendaftaran Cawangan (Pembayaran ONLINE)",
            paymentMethod: "o", // O = online, C = counter
            societyId: societyData?.data?.data?.id,
            branchId: branchId,
            registerDateTime: parseDateToISO8601(branchData?.createdDate),
            amount: paymentRedux?.totalAmount,
            referenceNo: branchData?.branchApplicationNo,
          }),
        });

        const data = await response.json();
        if (data.status === "SUCCESS") {
          const url = data?.data?.url;
          downloadFile(url);
        }
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box sx={{ width: "55vw" }}>
        <LoadingOverlay isLoading={isSocietyLoading} />
        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3,
            }}
          >
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: 600,
                    paddingLeft: 2,
                  }}
                >
                  {t("payment")} - {t("pengesahan")}
                </Typography>
              </Box>

              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: 400,
                  paddingLeft: 2,
                }}
              >
                {t("infoPaymentCawangan")}
              </Typography>

              <Grid container spacing={2} pl={4} pt={2} mb={3} mt={3}>
                <Input
                  disabled
                  label={t("organizationName")}
                  value={societyData?.data?.data?.societyName || ""}
                />
                <Input
                  disabled
                  label={t("tarikhPermohonan")}
                  value={branchData?.createdDate}
                  type="datetime"
                />
                <Input
                  disabled
                  label={t("referenceNumber")}
                  // value={societyData?.data?.data?.applicationNo || ""}
                  value={branchData?.branchApplicationNo || ""}
                />
                <Input
                  disabled
                  label={t("paymentMethod")}
                  value={"Pembayaran Online"}
                />
                <Input
                  disabled
                  label={t("paymentType")}
                  value="Pendaftaran cawangan"
                />
                <Input
                  disabled
                  label={t("namePemohon")}
                  value={userName || ""}
                />
                <Input disabled label={t("email")} value={email || ""} />
                <Input
                  disabled
                  label={t("idNumberPlaceholder")}
                  value={icNo || ""}
                />
                <Input disabled label={t("kodOsolAmanah")} value="724999" />
                <Input
                  disabled
                  label={t("jabatan")}
                  value="Jabatan Pendaftaran Pertubuhan"
                />
                <Input
                  disabled
                  label={t("pusatPenerimaan")}
                  value="Ibu Pejabat Jab. Pendataran Pertubuhan"
                />
              </Grid>

              <Typography
                variant="body2"
                sx={{
                  color: "#402DFF",
                  fontSize: 14,
                  paddingLeft: 2,
                }}
              >
                {t("noteOnline")}
              </Typography>
            </Box>

            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
                marginTop: 15,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                onClick={handleCetak}
                sx={{
                  color: "white",
                }}
              >
                {t("cetak")}
              </ButtonPrimary>
              <ButtonPrimary
                onClick={() => navigate(`term?id=${branchId}`)}
                sx={{
                  color: "white",
                }}
              >
                {t("bayar")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>
        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
              backgroundColor: "#fff",
              color: "#000",
              minWidth: fullScreen ? "100%" : "1000px",
              maxWidth: "100%",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogTitle sx={{ pb: 2.5 }}>
            <Box
              sx={{
                backgroundColor: "#e0f2f1",
                px: 2.5,
                py: 0.5,
                borderRadius: 2.5,
              }}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{ fontWeight: "bold", fontSize: 16, color: "black" }}
              >
                {t("submitApplication")}
              </Typography>
              <IconButton onClick={handleCloseDialog} size="small">
                <CloseIcon sx={{ color: "black" }} />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ py: 4 }}>
            <DialogContentText
              id="alert-dialog-description"
              sx={{ color: "black", fontSize: 14, ml: 2 }}
            >
              {t("confirmSubmitApplication")}
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ py: 2, px: 3 }}>
            <ButtonOutline onClick={handleCloseDialog} sx={{ fontSize: 14 }}>
              {t("no")}
            </ButtonOutline>
            <ButtonPrimary color="primary" autoFocus sx={{ fontSize: 14 }}>
              {t("yes")}
            </ButtonPrimary>
          </DialogActions>
        </Dialog>
        <Dialog
          open={dialogAlertSuccessSaveOpen}
          onClose={() => setDialogAlertSuccessSaveOpen(false)}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogContent sx={{ p: 4 }}>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <img src="/success.gif" alt="success" width={200} height={200} />
            </Box>
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontSize: 28, textAlign: "center" }}
            >
              Permohonan berjaya dihantar.
            </Typography>

            <Box
              sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
            >
              <ButtonPrimary
                sx={{ backgroundColor: "#51CA47", borderRadius: "18px" }}
                onClick={() => setDialogAlertSuccessSaveOpen(false)}
              >
                {t("Continue")}
              </ButtonPrimary>
            </Box>
          </DialogContent>
        </Dialog>
      </Box>
    </Box>
  );
};

export default Online;
