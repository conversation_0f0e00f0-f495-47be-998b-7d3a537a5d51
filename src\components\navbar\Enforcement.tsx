import { Box, Grid, styled, Typography, useTheme } from "@mui/material";
import { FC, useCallback, useLayoutEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { NavLink, useLocation, useOutlet } from "react-router-dom";

import { capitalizeWords } from "@/helpers";

import IconAduanSiasatan from "@/assets/svg/icon-aduan-siasatan.svg?react";
import IconPembatalan from "@/assets/svg/icon-pembatalan.svg?react";
import IconSekatanLiabiliti from "@/assets/svg/icon-sekatan-liabiliti.svg?react";
import IconPengurusanFee from "@/assets/svg/icon-pengurusan-fee.svg?react";
import IconPengurusanNotis from "@/assets/svg/icon-pengurusan-notis.svg?react";
import IconPendakwaan from "@/assets/svg/icon-pendakwaan.svg?react";

import { CardAduanInternalDashboard } from "../card/aduan/InternalDashboard";
import DaftarPanduanSideBar from "@/pages/penguatkuasaan/internal/daftar-panduan/SideBar";
import PemeriksaanSideBar from "@/pages/penguatkuasaan/internal/pemeriksaan/SideBar";

interface NavIconProps {
  icon: FC;

  /**
   * @default false
   */
  active?: boolean;
}

const generateNavIcon =
  <Props extends NavIconProps = NavIconProps>({ icon }: Props) =>
  (isActive: boolean) => {
    const StyledIcon = styled(icon)(
      ({ theme }) =>
        isActive && {
          path: {
            stroke: "white",
            fill: theme.palette.primary.main,
          },
        }
    );
    return <StyledIcon />;
  };

export const NavbarEnforcement = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const location = useLocation();
  const outlet = useOutlet();
  const gridRef = useRef<(HTMLDivElement | null)[]>([]);

  // const showNavigation = location.pathname.split("/").length <= 3 || ["aduan-saya", "dalam-siasatan"].some((path) => location.pathname.endsWith(path));

  const showNavigation =
    location.pathname.split("/").length <= 3 ||
    ["aduan-saya", "dalam-siasatan"].some((path) =>
      location.pathname.endsWith(path)
    ) ||
    location.pathname.includes("/daftar_nama_larangan");

  const isDaftarPanduanRoute = location.pathname.endsWith("/daftar-panduan");

  const primary = theme.palette.primary.main;
  const navigations = [
    {
      label: capitalizeWords(t("complaint")),
      destinationPath: "/penguatkuasaan/aduan",
      icon: generateNavIcon({ icon: IconAduanSiasatan }),
    },
    {
      label: t("investigation"),
      destinationPath: "/penguatkuasaan/siasatan",
      icon: generateNavIcon({ icon: IconAduanSiasatan }),
    },
    {
      label: t("cancellation"),
      destinationPath: "/penguatkuasaan/pembatalan",
      icon: generateNavIcon({ icon: IconPembatalan }),
    },
    {
      label: t("liabilityRestriction"),
      destinationPath: "/penguatkuasaan/sekatan_liabiliti",
      icon: generateNavIcon({ icon: IconSekatanLiabiliti }),
    },
    {
      label: t("feeManagement"),
      destinationPath: "/penguatkuasaan/pengurusan_fee",
      icon: generateNavIcon({ icon: IconPengurusanFee }),
    },
    {
      label: t("registerOfOrganizationManagementGuidelines"),
      destinationPath: "/penguatkuasaan/daftar-panduan",
      icon: generateNavIcon({ icon: IconSekatanLiabiliti }),
    },
    {
      label: t("noticeManagement"),
      destinationPath: "/penguatkuasaan/pengurusan-notis",
      icon: generateNavIcon({ icon: IconPengurusanNotis }),
    },
    {
      label: t("redFlag"),
      destinationPath: "/penguatkuasaan/red_flag",
      icon: generateNavIcon({ icon: IconPembatalan }),
    },
    {
      label: t("listOfBannedNames"),
      destinationPath: "/penguatkuasaan/daftar_nama_larangan",
      icon: generateNavIcon({ icon: IconPengurusanNotis }),
    },
    {
      label: t("inspection"),
      destinationPath: "/penguatkuasaan/pemeriksaan",
      icon: generateNavIcon({ icon: IconSekatanLiabiliti }),
    },
    {
      label: t("prosecution"),
      destinationPath: "/penguatkuasaan/pendakwaan",
      icon: generateNavIcon({ icon: IconPendakwaan }),
    },
  ];

  const setGridRef = useCallback((el: HTMLDivElement, index: number) => {
    gridRef.current[index] = el;
  }, []);

  useLayoutEffect(() => {
    const itemsHeight = gridRef.current.map((el) => el?.offsetHeight!);
    const maxHeight = Math.max(...itemsHeight);
    for (const el of gridRef.current) {
      if (el) {
        el.style.height = `${maxHeight}px`;
      }
    }
  }, []);

  return (
    <Grid container spacing={2}>
      <Grid item md={12}>
        <Grid container>
          <Grid item md={9.5}>
            {showNavigation && (
              <Box
                sx={{
                  backgroundColor: "white",
                  borderRadius: "1rem",
                  padding: 2,
                  boxShadow: "0 12px 12px 0 rgba(234, 232, 232, 0.4)",
                }}
              >
                <Typography
                  color={primary}
                  sx={{
                    fontSize: 14,
                    fontWeight: "medium",
                    marginBottom: "1.5rem",
                  }}
                >
                  {t("enforcement_management")}
                </Typography>
                <Grid container spacing={1}>
                  {navigations.map((navItem, index) => (
                    <Grid key={`enforcement-nav-item-${index}`} item sm={2}>
                      <NavLink
                        style={{ textDecoration: "none", height: "100%" }}
                        to={navItem.destinationPath}
                      >
                        {({ isActive }) => {
                          return (
                            <Box
                              ref={(el) =>
                                setGridRef(el as HTMLDivElement, index)
                              }
                              sx={{
                                padding: "0.75rem",
                                paddingTop: "0.5rem !important",
                                borderRadius: "0.5rem",
                                border: `1px solid ${primary}`,
                                backgroundColor: isActive ? primary : "white",
                                position: "relative",
                                display: "flex",
                                flexDirection: "column",
                                justifyContent: "space-between",
                                alignItems: "flex-start",
                                height: "100%", // Set a fixed height for all boxes
                                minHeight: "60px", // Ensure a minimum height
                                paddingBottom: 0,
                                ...(isActive && {
                                  boxShadow:
                                    "4px 6px 12px 0 rgba(102, 102, 102, 0.3)",
                                }),
                              }}
                            >
                              <Typography
                                fontSize={12}
                                color={isActive ? "white" : primary}
                              >
                                {navItem.label}
                              </Typography>
                              {navItem.icon && (
                                <div
                                  style={{
                                    display: "flex",
                                    width: "100%",
                                    alignItems: "flex-end",
                                    justifyContent: "flex-end",
                                    position: "relative",
                                  }}
                                >
                                  {navItem.icon(isActive)}
                                </div>
                              )}
                            </Box>
                          );
                        }}
                      </NavLink>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}
          </Grid>
        </Grid>
      </Grid>
      <Grid item md={12}>
        <Grid container columnSpacing={2}>
          <Grid item md={9.5}>
            {outlet}
          </Grid>
          <Grid item md={2.5}>
            <DaftarPanduanSideBar />
            <CardAduanInternalDashboard />
            <PemeriksaanSideBar />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};
