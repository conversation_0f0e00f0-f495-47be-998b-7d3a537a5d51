import { Box, CircularProgress } from "@mui/material";
import { useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import FasalContentSatu from "./induk/FasalContentSatu";
import FasalContentDua from "./induk/FasalContentDua";
import FasalContentTiga from "./induk/FasalContentTiga";
import FasalContentEmpat from "./induk/FasalContentEmpat";
import FasalContentLima from "./induk/FasalContentLima";
import FasalContentEnam from "./induk/FasalContentEnam";
import FasalContentTujuh from "./induk/FasalContentTujuh";
import FasalContentDelapan from "./induk/FasalContentDelapan";
import FasalContentSembilan from "./induk/FasalContentSembilan";
import FasalContentSepuluh from "./induk/FasalContentSepuluh";
import FasalContentSebelas from "./induk/FasalContentSebelas";
import FasalContentDuaBelas from "./induk/FasalContentDuaBelas";
import FasalContentTigaBelas from "./induk/FasalContentTigaBelas";
import FasalContentEmpatBelas from "./induk/FasalContentEmpatBelas";
import FasalContentLimaBelas from "./induk/FasalContentLimaBelas";
import FasalContentEnamBelas from "./induk/FasalContentEnamBelas";
import FasalContentTujuhBelas from "./induk/FasalContentTujuhBelas";
import FasalContentDelapanBelas from "./induk/FasalDelapanBelas";
import FasalContentSatuCawangan from "./cawangan/FasalContentSatu";
import FasalContentDuaCawangan from "./cawangan/FasalContentDua";
import FasalContentTigaCawangan from "./cawangan/FasalContentTiga";
import FasalContentEmpatCawangan from "./cawangan/FasalContentEmpat";
import FasalContentLimaCawangan from "./cawangan/FasalContentLima";
import FasalContentEnamCawangan from "./cawangan/FasalContentEnam";
import FasalContentTujuhCawangan from "./cawangan/FasalContentTujuh";
import FasalContentDelapanCawangan from "./cawangan/FasalContentDelapan";
import FasalContentSembilanCawangan from "./cawangan/FasalContentSembilan";
import FasalContentSepuluhCawangan from "./cawangan/FasalContentSepuluh";
import FasalContentSebelasCawangan from "./cawangan/FasalContentSebelas";
import FasalContentDuaBelasCawangan from "./cawangan/FasalContentDuaBelas";
import FasalContentTigaBelasCawangan from "./cawangan/FasalContentTigaBelas";
import FasalContentEmpatBelasCawangan from "./cawangan/FasalContentEmpatBelas";
import FasalContentLimaBelasCawangan from "./cawangan/FasalContentLimaBelas";
import FasalContentEnamBelasCawangan from "./cawangan/FasalContentEnamBelas";
import FasalContentTujuhBelasCawangan from "./cawangan/FasalContentTujuhBelas";
import FasalContentDelapanBelasCawangan from "./cawangan/FasalDelapanBelas";
import FasalContentSembilanBelasCawangan from "./cawangan/FasalContentSembilanBelas";
import FasalContentDuaPuluhCawangan from "./cawangan/FasalContentDuaPuluh";
import FasalContentDuaPuluhSatuCawangan from "./cawangan/FasalContentDuaPuluhSatu";
import FasalContentDuaPuluhDuaCawangan from "./cawangan/FasalContentDuaPuluhDua";
import FasalContentDuaPuluhTigaCawangan from "./cawangan/FasalContentDuaPuluhTiga";
import FasalContentSatuFaedah from "./faedah/FasalContentSatu";
import FasalContentDuaFaedah from "./faedah/FasalContentDua";
import FasalContentTigaFaedah from "./faedah/FasalContentTiga";
import FasalContentEmpatFaedah from "./faedah/FasalContentEmpat";
import FasalContentLimaFaedah from "./faedah/FasalContentLima";
import FasalContentEnamFaedah from "./faedah/FasalContentEnam";
import FasalContentTujuhFaedah from "./faedah/FasalContentTujuh";
import FasalContentDelapanFaedah from "./faedah/FasalContentDelapan";
import FasalContentSembilanFaedah from "./faedah/FasalContentSembilan";
import FasalContentSepuluhFaedah from "./faedah/FasalContentSepuluh";
import FasalContentSebelasFaedah from "./faedah/FasalContentSebelas";
import FasalContentDuaBelasFaedah from "./faedah/FasalContentDuaBelas";
import FasalContentTigaBelasFaedah from "./faedah/FasalContentTigaBelas";
import FasalContentEmpatBelasFaedah from "./faedah/FasalContentEmpatBelas";
import FasalContentLimaBelasFaedah from "./faedah/FasalContentLimaBelas";
import FasalContentEnamBelasFaedah from "./faedah/FasalContentEnamBelas";
import FasalContentTujuhBelasFaedah from "./faedah/FasalContentTujuhBelas";
import FasalContentDelapanBelasFaedah from "./faedah/FasalDelapanBelas";
import FasalContentSembilanBelasFaedah from "./faedah/FasalContentSembilanBelas";
import FasalContentDuaPuluhFaedah from "./faedah/FasalContentDuaPuluh";
import { useCustom } from "@refinedev/core";
import { API_URL } from "../../../api";
import { ApplicationStatus, ConstitutionType } from "../../../helpers/enums";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../helpers/utils";
import { useDispatch } from "react-redux";
import { setClauseid, setCurrentFasalData } from "../../../redux/fasalReducer";
import FasalContentSatuBebas from "./Bebas/FasalContentSatu";
import FasalContentDuaBebas from "./Bebas/FasalContentDua";
import FasalContentTigaBebas from "./Bebas/FasalContentTiga";
import FasalContentEmpatBebas from "./Bebas/FasalContentEmpat";
import FasalContentLimaBebas from "./Bebas/FasalContentLima";
import FasalContentEnamBebas from "./Bebas/FasalContentEnam";
import FasalContentOther from "./Bebas/FasalContentOther";
import FasalContentTujuhBebasCawangan from "./BebasCawangan/FasalContentTujuh";
import FasalContentLapanBebasCawangan from "./BebasCawangan/FasalContentLapan";
import FasalContentSembilanBebasCawangan from "./BebasCawangan/FasalContentSembilan";
import FasalContentOtherCawangan from "./BebasCawangan/FasalContentOther";
import FasalContentSatuBebasCawangan from "./BebasCawangan/FasalContentSatu";
import FasalContentDuaBebasCawangan from "./BebasCawangan/FasalContentDua";
import FasalContentTigaBebasCawangan from "./BebasCawangan/FasalContentTiga";
import FasalContentEmpatBebasCawangan from "./BebasCawangan/FasalContentEmpat";
import FasalContentLimaBebasCawangan from "./BebasCawangan/FasalContentLima";
import FasalContentEnamBebasCawangan from "./BebasCawangan/FasalContentEnam";
interface ClauseValueProps {
  definitionName: string;
}
export interface ClauseProps {
  id: number;
  constitutionValues: ClauseValueProps[];
  clauseContent: string;
  clauseModifyContent?: string;
  clauseContentId: string|number;
  clauseNo: string;
  edit: boolean;
  editContentText?: boolean;
  clauseName?: string;
  constitutionTypeId?: string | number;
  clauseDescription?: string;
  checkUpdate?: any;
}

export const Fasal: React.FC = () => {
  const { clauseId } = useParams();
  const ref = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch();
  const [clause, setClause] = useState<ClauseProps>({
    id: 0,
    constitutionValues: [],
    clauseContent: "",
    clauseContentId: "",
    clauseModifyContent: "",
    clauseNo: "",
    edit: false,
    editContentText: false,
  });
  const [activeStep, setActiveStep] = useState(2);
  const fasalRedux = useSelector((state: { fasal: any }) => state.fasal.data);
  const [constitutionType, setConstitutionType] = useState<string | null>(
    fasalRedux.currentAmendmentConstitutionType
  );
  const [constitutionTypeInt, setConstitutionTypeInt] = useState<
    string | number
  >(0);
  const [societyId, setSocietyId] = useState<any>(null);
  const amendmentId = getLocalStorage("amendmentId", null);

  const clauseNumber = Number(clauseId);
  const isBebasType =
    constitutionType === ConstitutionType.Bebas[1] ||
    constitutionType === ConstitutionType.CawanganBebas[1];
  const isAddedBebasFasal =
    (constitutionType === ConstitutionType.Bebas[1] && clauseNumber > 6) ||
    (constitutionType === ConstitutionType.CawanganBebas[1] &&
      clauseNumber > 9);

  //GET CURRENT CONSTITUTION TEMPLATE (DEFAULT)
  const { data: fasalData, isLoading: fasalDataIsLoading } = useCustom({
    url: `${API_URL}/society/constitution/clausecontenttemplate/getByConsTypeAndClauseNo`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        constitutionType: constitutionTypeInt,
        clauseNo: clauseId,
        societyId: societyId,
      },
    },
    queryOptions: {
      cacheTime: 0,
      enabled: !!constitutionTypeInt && !!clauseId,
      onSuccess: (data) => {
        const responseData = data?.data?.data;
        const fasalContent = responseData?.content;
        const fasalId = responseData?.id;
        setClause((prevState) => ({
          ...prevState,
          clauseContent: fasalContent ?? "Failed to fetch Fasal",
          clauseDescription: clauseContent?.description,
          clauseContentId: fasalId ?? 0,
          clauseNo: responseData?.clauseNo,
          constitutionTypeId: responseData?.constitutionTypeId,
          ...(isAddedBebasFasal ? {} : { clauseName: responseData?.name }),
        }));
        dispatch(setClauseid(fasalId));
        dispatch(
          setCurrentFasalData({
            clauseContent: responseData?.content,
          })
        );
      },
    },
  });

  const fasal = fasalData?.data?.data;
  const clauseContentName = fasal?.name;
  const clauseContentId = fasal?.id;
  //GET CURRENT CONSTITUTION CONTENT FOR THIS SOCIETY
  const { data: clauseContentData, isLoading: clauseContentDataIsLoading } =
    useCustom({
      url: `${API_URL}/society/constitutioncontent/get`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        query: {
          societyId: societyId,
          ...(isAddedBebasFasal
            ? {
                clauseNo: clauseId,
                amendmentId: amendmentId,
              }
            : isBebasType
            ? { amendmentId: amendmentId, clauseContentId: clauseContentId }
            : { status: ApplicationStatus["AKTIF"] }),
        },
      },
      queryOptions: {
        cacheTime: 0,
        enabled:
          !!societyId &&
          (!!clauseContentId || !!clauseId) &&
          !!ConstitutionType,
        onSuccess: (data) => {
          const responseData = data?.data?.data?.data[0];
          // For added bebas description
          if (isAddedBebasFasal) {
            setClause((prevState) => ({
              ...prevState,
              clauseName: responseData?.clauseName,
              id: responseData?.id ?? 0,
              constitutionValues: [
                { definitionName: responseData?.description },
              ],
            }));
          } else if (isBebasType) {
            setClause((prevState) => ({
              ...prevState,
              id: responseData?.id,
              clauseModifyContent: responseData?.modifiedTemplate ?? null,
            }));
          }
        },
      },
    });

  const {
    data: clauseDescriptionData,
    isLoading: clauseDescriptionDataIsLoading,
  } = useCustom({
    url: `${API_URL}/society/constitutioncontent/get`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: societyId,
        clauseContentId: clauseContentId,
        amendmentId: amendmentId,
      },
    },
    queryOptions: {
      cacheTime: 0,
      enabled:
        !!societyId && (!!clauseContentId || !!clauseId) && !!ConstitutionType,

      onSuccess: (data) => { 
        const response = data?.data?.data?.data?.[0];
        const getId = data?.data?.data?.data?.at(-1)?.id;
        if (!isBebasType && !isAddedBebasFasal) {
          setClause((prevState) => ({
            ...prevState,
            checkUpdate: response?.checkUpdate,
            clauseDescription: response?.description,
            editContentText: response?.description ? true : false,
            id: getId ?? 0,
          }));
        }
      },
    },
  });

  // =========================
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  //CHECK CURRENT SOCIETY CONSTITUTION TYPE
  const currentSocietyConstitutionType = societyDataRedux?.constitutionType;

  //CHECK CURRENT SOCIETY IS BEBAS / BEBAS CAWANGAN TYPE
  const currentSocietyIsBabas =
    (currentSocietyConstitutionType === ConstitutionType.Bebas[1] &&
      fasalRedux.isBebasAddFasal) ||
    (currentSocietyConstitutionType === ConstitutionType.CawanganBebas[1] &&
      fasalRedux.isBebasAddFasal);

  //=========================

  const BebasName = clauseContentData?.data?.data?.data[0]?.clauseName;
  // const clauseContent = clauseContentData?.data?.data?.data[0];
  const clauseContent = clauseContentData?.data?.data?.data ?? [];

  //GET ALL CURRENT CONSTITUTION CONTENT
  const { data: clauseValueData, isLoading: isClauseValueDataLoading } =
    useCustom({
      url: `${API_URL}/society/constitutionvalue/getAll`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        query: {
          societyId: societyId,
          clauseContentId: clauseContentId,
          amendmentId: amendmentId,
          clauseNo: clauseId,
        },
      },
      queryOptions: {
        cacheTime: 0,
        enabled: !!societyId && !fasalDataIsLoading,
        onSuccess: (data) => {
          const responseData = data?.data?.data;
          const clauseValue = responseData?.data;
          // const clauseValueId = responseData?.data?.[0]?.constitutionContentId;
          setClause((prevState) => ({
            ...prevState,
            // clauseDescription: clauseContent?.description,
            ...(isAddedBebasFasal
              ? {}
              : {
                  constitutionValues: clauseValue ?? [],
                  // id: clauseValueId ?? 0,
                }),
            edit: !!clauseValue,
          }));
        },
      },
    });

  const { id } = useParams();

  useEffect(() => {
    if (id) {
      setSocietyId(id);
    }
    if (constitutionType) {
      for (const [key, value] of Object.entries(ConstitutionType)) {
        if (value[1] === constitutionType) {
          setConstitutionTypeInt(value[0]);
        }
      }
    }
  }, [id, constitutionType]);

  const renderContentInduk = () => {
    switch (clauseId) {
      case "1":
        return (
          <FasalContentSatu
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "2":
        return (
          <FasalContentDua
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "3":
        return (
          <FasalContentTiga
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "4":
        return (
          <FasalContentEmpat
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "5":
        return (
          <FasalContentLima
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "6":
        return (
          <FasalContentEnam
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "7":
        return (
          <FasalContentTujuh
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "8":
        return (
          <FasalContentDelapan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "9":
        return (
          <FasalContentSembilan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "10":
        return (
          <FasalContentSepuluh
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "11":
        return (
          <FasalContentSebelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "12":
        return (
          <FasalContentDuaBelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "13":
        return (
          <FasalContentTigaBelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "14":
        return (
          <FasalContentEmpatBelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "15":
        return (
          <FasalContentLimaBelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "16":
        return (
          <FasalContentEnamBelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "17":
        return (
          <FasalContentTujuhBelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "18":
        return (
          <FasalContentDelapanBelas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      default:
        return <></>;
    }
  };

  const renderContentCawangan = () => {
    switch (clauseId) {
      case "1":
        return (
          <FasalContentSatuCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      case "2":
        return (
          <FasalContentDuaCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      case "3":
        return (
          <FasalContentTigaCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      case "4":
        return (
          <FasalContentEmpatCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      case "5":
        return (
          <FasalContentLimaCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      case "6":
        return (
          <FasalContentEnamCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      case "7":
        return (
          <FasalContentTujuhCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "8":
        return (
          <FasalContentDelapanCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      case "9":
        return (
          <FasalContentSembilanCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      case "10":
        return (
          <FasalContentSepuluhCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      case "11":
        return (
          <FasalContentSebelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "12":
        return (
          <FasalContentDuaBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      case "13":
        return (
          <FasalContentTigaBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "14":
        return (
          <FasalContentEmpatBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      case "15":
        return (
          <FasalContentLimaBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      case "16":
        return (
          <FasalContentEnamBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      case "17":
        return (
          <FasalContentTujuhBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "18":
        return (
          <FasalContentDelapanBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      case "19":
        return (
          <FasalContentSembilanBelasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "20":
        return (
          <FasalContentDuaPuluhCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "21":
        return (
          <FasalContentDuaPuluhSatuCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "22":
        return (
          <FasalContentDuaPuluhDuaCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "23":
        return (
          <FasalContentDuaPuluhTigaCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            asalData={clauseContent}
            name={clauseContentName}
            clause={clause}
          />
        );
      default:
        return <></>;
    }
  };

  const renderContentFaedah = () => {
    switch (clauseId) {
      case "1":
        return (
          <FasalContentSatuFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "2":
        return (
          <FasalContentDuaFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "3":
        return (
          <FasalContentTigaFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "4":
        return (
          <FasalContentEmpatFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "5":
        return (
          <FasalContentLimaFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "6":
        return (
          <FasalContentEnamFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "7":
        return (
          <FasalContentTujuhFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "8":
        return (
          <FasalContentDelapanFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "9":
        return (
          <FasalContentSembilanFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "10":
        return (
          <FasalContentSepuluhFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "11":
        return (
          <FasalContentSebelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "12":
        return (
          <FasalContentDuaBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "13":
        return (
          <FasalContentTigaBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "14":
        return (
          <FasalContentEmpatBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "15":
        return (
          <FasalContentLimaBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      case "16":
        return (
          <FasalContentEnamBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "17":
        return (
          <FasalContentTujuhBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "18":
        return (
          <FasalContentDelapanBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "19":
        return (
          <FasalContentSembilanBelasFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
          />
        );
      case "20":
        return (
          <FasalContentDuaPuluhFaedah
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
          />
        );
      default:
        return <></>;
    }
  };

  const renderContentBebas = () => {
    switch (clauseId) {
      case "1":
        return (
          <FasalContentSatuBebas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      case "2":
        return (
          <FasalContentDuaBebas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      case "3":
        return (
          <FasalContentTigaBebas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      case "4":
        return (
          <FasalContentEmpatBebas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      case "5":
        return (
          <FasalContentLimaBebas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      case "6":
        return (
          <FasalContentEnamBebas
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      default:
        return (
          <FasalContentOther
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={BebasName}
            asalData={clauseContent}
            // currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
    }
  };

  const renderContentBebasCawangan = () => {
    switch (clauseId) {
      case "1":
        return (
          <FasalContentSatuBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      case "2":
        return (
          <FasalContentDuaBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      case "3":
        return (
          <FasalContentTigaBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      case "4":
        return (
          <FasalContentEmpatBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      case "5":
        return (
          <FasalContentLimaBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      case "6":
        return (
          <FasalContentEnamBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      case "7":
        return (
          <FasalContentTujuhBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      case "8":
        return (
          <FasalContentLapanBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      case "9":
        return (
          <FasalContentSembilanBebasCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={clauseContentName}
            asalData={clauseContent}
            currentSocietyIsBabas={currentSocietyIsBabas}
          />
        );
      default:
        return (
          <FasalContentOtherCawangan
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            clause={clause}
            name={BebasName}
            asalData={clauseContent}
          />
        );
    }
  };

  const isLoading =
    fasalDataIsLoading ||
    isClauseValueDataLoading ||
    clauseContentDataIsLoading;

  useEffect(() => {
    if (ref.current) {
      ref.current.scrollIntoView({ block: "start" });
    }
  }, [isLoading]);

  if (isLoading) {
    return (
      <Box sx={{ display: "flex", gap: 2, alignItems: "flex-start" }}>
        <Box
          sx={{
            width: "55vw",
            backgroundColor: "white",
            p: 3,
            borderRadius: "15px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "50vw",
          }}
        >
          <CircularProgress />
        </Box>
      </Box>
    );
  }

  return (
    <Box
      ref={ref}
      sx={{ display: "flex", height: "100%", gap: 2, alignItems: "flex-start" }}
    >
      <Box
        sx={{
          width: "100%",
          // backgroundColor: "white",
          // p: 3,
          borderRadius: "15px",
        }}
      >
        {constitutionType === ConstitutionType.IndukNGO[1] ||
        constitutionType === ConstitutionType.IndukAgama[1]
          ? renderContentInduk()
          : constitutionType === ConstitutionType.FaedahBersama[1]
          ? renderContentFaedah()
          : constitutionType === ConstitutionType.Bebas[1]
          ? renderContentBebas()
          : constitutionType === ConstitutionType.CawanganBebas[1]
          ? renderContentBebasCawangan()
          : renderContentCawangan()}
      </Box>
    </Box>
  );
};

export default Fasal;
