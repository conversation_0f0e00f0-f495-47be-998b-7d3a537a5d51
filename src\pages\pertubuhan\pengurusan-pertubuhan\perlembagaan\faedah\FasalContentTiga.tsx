import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { useSelector } from "react-redux";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import CustomPopover from "../../../../../components/popover";
import { FasalContentProps } from "../Fasal";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import Input from "@/components/input/Input";
interface FasalContentTigaFaedahProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
}

export const FasalContentTigaFaedah: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [matlamatPertubuhan, setMatlamatPertubuhan] = useState("");

  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  //const clause3 = localStorage.getItem("clause3");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause3Data = JSON.parse(clause3);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        setClauseContent(clause.clauseContent);
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause3Data.societyName);
      setMatlamatPertubuhan(clause.constitutionValues[0]?.definitionName ?? "");
      setIsEdit(clause.edit);
    }
  }, [clause]);

  const { id } = useParams();

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!matlamatPertubuhan) {
      errors.matlamatPertubuhan = t("fieldRequired");
    }

    return errors;
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clause")} {id}
        </Typography>

        <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
          <Typography sx={labelStyle}>{t("organizationGoals")}</Typography>
          <CustomPopover
            content={
              <>
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  sx={{ mb: 1, color: "#666666" }}
                >
                  Format
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ lineHeight: 1.5, color: "#666666" }}
                >
                  1. &lt;matlamat 1&gt;
                  <br />
                  2. &lt;matlamat 2&gt;
                </Typography>
              </>
            }
          />
        </Box>
        {/* <TextField
          fullWidth
          value={matlamatPertubuhan}
          variant="outlined"
          multiline
          minRows={3}
          onChange={(e) => {
            setMatlamatPertubuhan(e.target.value);
            setFormErrors((prevErrors) => ({
              ...prevErrors,
              matlamatPertubuhan: "",
            }));
          }}
          error={!!formErrors.matlamatPertubuhan}
          helperText={formErrors.matlamatPertubuhan}
          sx={{
            "& fieldset": { borderRadius: "12px" },
            "& .MuiInputBase-input": { color: "black" },
          }}
        /> */}
        <Input
          isLabel={false}
          value={matlamatPertubuhan}
          isLabelNoSpace={false}
          fullFeatureRichText={true}
          name="decisionNotes"
          type="richText"
          rows={4}
          error={!!formErrors.matlamatPertubuhan}
          helperText={formErrors.matlamatPertubuhan}
          onChange={(e) => {
            setMatlamatPertubuhan(e.target.value);
            setFormErrors((prevErrors) => ({
              ...prevErrors,
              matlamatPertubuhan: "",
            }));
          }}
        />
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent
                clauseContent={matlamatPertubuhan || clauseContent}
              />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: matlamatPertubuhan,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: matlamatPertubuhan,
                  titleName: "Matlamat Pertubuhan",
                },
              ],
              clause: "clause3",
              clauseCount: 3,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentTigaFaedah;
