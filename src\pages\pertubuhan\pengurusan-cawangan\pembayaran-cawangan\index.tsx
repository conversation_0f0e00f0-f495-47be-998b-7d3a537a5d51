import { useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Checkbox from "@mui/material/Checkbox";
import { SelectChangeEvent } from "@mui/material/Select";
import InputLabel from "@mui/material/InputLabel";
import { useTranslation } from "react-i18next";
import { ButtonPrimary } from "../../../../components/button";
import { Select, Option } from "../../../../components/input";
import { Fade, Grid } from "@mui/material";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { ApplicationStatus, PaymentItemCode } from "../../../../helpers/enums";
import { useMutation, useQuery } from "@/helpers";
import { DialogConfirmation } from "@/components";
import { usePaymentService } from "@/helpers/hooks/usePaymentService";
import { useDispatch } from "react-redux";
import { setCalculatedPayment } from "@/redux/paymentReducer";
import { IPaymentCalculationRequest } from "@/services/paymentService";

function PembayaranCawangan() {
  const { t } = useTranslation();

  const [paymentMethod, setPaymentMethod] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { calculatePayment, processPayment } = usePaymentService();

  const [isChecked, setIsChecked] = useState(false);

  // Handle kaunter payment processing
  const handleKaunterPayment = async (calculatedPaymentData: any) => {
    const paymentPayload = {
      societyId: Number(societyId),
      branchId: Number(branchId),
      amount: calculatedPaymentData.totalAmount,
      email: "",
      signature: calculatedPaymentData.signature,
    };

    try {
      await processPayment(paymentPayload);
      navigate(`kaunter?id=${branchId}`);
    } catch (error) {
      console.error("Error processing payment:", error);
      // Handle error appropriately
    }
  };

  const {
    fetchAsync: editSocietyPaymentMethod,
    isLoading: isLoadingEditSocietyPaymentMethod,
  } = useMutation({
    url: "society/branch/update",
    method: "put",
    onSuccess: async () => {
      // Determine the correct item code based on payment method
      const itemCode = paymentMethod === "online"
        ? PaymentItemCode.PENDAFTARAN_CAWANGAN_ONLINE
        : PaymentItemCode.PENDAFTARAN_CAWANGAN_KAUNTER;

      // Call calculate API first
      const calculateRequest: IPaymentCalculationRequest = {
        items: [
          {
            itemCode: itemCode,
            quantity: 1
          }
        ]
      };

      try {
        const calculateResponse = await calculatePayment(calculateRequest);

        if (!calculateResponse?.data) {
          throw new Error("Failed to calculate payment");
        }

        // Store calculated payment in Redux
        dispatch(setCalculatedPayment(calculateResponse.data));

        // Proceed with payment logic
        if (paymentMethod === "online") {
          navigate(`online?id=${branchId}`);
        } else {
          await handleKaunterPayment(calculateResponse.data);
        }
      } catch (error) {
        console.error("Error calculating payment:", error);
        // Handle error appropriately
      }
    },
  });

  const handleChange = (event: SelectChangeEvent) => {
    setPaymentMethod(event.target.value as string);
  };

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsChecked(event.target.checked);
  };

  //const params = new URLSearchParams(window.location.search);
  //const societyId = params.get("id");
  const { id: societyId } = useParams();

  //TODO not recommended as local storage will easily override please take note
  const [searchParams] = useSearchParams();
  const branchId = searchParams.get("id");

  const editSociety = async () => {
    if (branchId) {
      const data = {
        id: Number(branchId),
        applicationStatusCode:
          paymentMethod === "kaunter"
            ? ApplicationStatus.MENUNGGU_BAYARAN_KAUNTER
            : ApplicationStatus.MENUNGGU_BAYARAN_ONLINE,
      };
      await editSocietyPaymentMethod(data);
    }
  };

  const { data: paymentStatus } = useQuery({
    url: "society/admin/integration/payment/status",
  });
  const onlinePaymentEnabled: boolean =
    paymentStatus?.data?.data?.payment?.toLowerCase() == "enabled";
  const alert: boolean = paymentStatus?.data?.data?.alert;

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const handleSubmit = () => {
    handleOpenDialog();
  };

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Fade in={true} timeout={500}>
        <Box
          sx={{
            backgroundColor: "white",
            border: 1,
            borderColor: "grey.300",
            borderRadius: 4,
            p: 3,
          }}
        >
          <Box
            sx={{
              border: 1,
              borderColor: "grey.300",
              borderRadius: 1,
              p: 2,
              mb: 2,
            }}
          >
            <Box sx={{ mb: 3 }}>
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  color: "#00A7A7",
                  fontSize: 16,
                  fontWeight: 600,
                }}
              >
                {t("payment")}
              </Typography>
            </Box>
            <Typography
              variant="body2"
              sx={{
                color: "#666666",
                fontFamily: "Poppins",
                fontSize: 14,
                fontWeight: 400,
                lineHeight: "21px",
                textAlign: "left",
                mb: 2,
              }}
            >
              {t("agreementText")}
            </Typography>
            <Box
              sx={{
                display: "flex",
                alignItems: "flex-start",
                gap: 1,
              }}
            >
              <Checkbox
                id="akuan-setuju-terima"
                checked={isChecked}
                onChange={handleCheckboxChange}
                sx={{
                  color: "#00A7A7",
                  "&.Mui-checked": {
                    color: "#00A7A7",
                  },
                  padding: "0",
                }}
              />
              <InputLabel
                htmlFor="akuan-setuju-terima"
                required
                sx={{
                  color: "#333333",
                  fontSize: 14,
                  fontWeight: 400,
                  lineHeight: 1.4,
                  "& .MuiFormLabel-asterisk": {
                    color: "#FF0000",
                  },
                }}
              >
                {t("agreementAcceptance")}
              </InputLabel>
            </Box>
          </Box>

          <Box
            sx={{
              border: 1,
              borderColor: "grey.300",
              borderRadius: 1,
              p: 2,
              mb: 3,
            }}
          >
            <Box sx={{ mb: 2 }}>
              <Box sx={{ mb: 1 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: 600,
                  }}
                >
                  {t("paymentMethod")}
                </Typography>
              </Box>
              <Grid
                container
                rowSpacing={1}
                columnSpacing={{ xs: 1, sm: 2, md: 3 }}
              >
                <Grid item xs={4}>
                  {/* <Typography>1</Typography> */}
                </Grid>
                <Grid item xs={8}>
                  <Typography
                    sx={{
                      fontFamily: "Poppins, sans-serif",
                      fontSize: "12px",
                      fontWeight: 500,
                      lineHeight: "14px",
                      color: "#FF0000",
                      marginLeft: "15px",
                    }}
                  >
                    {alert}
                  </Typography>
                </Grid>
                <Grid item xs={4}>
                  <InputLabel
                    htmlFor="cara-pembayaran"
                    required
                    sx={{
                      color: "#333333",
                      fontSize: 14,
                      fontWeight: 400,
                      minWidth: "150px",
                      "& .MuiFormLabel-asterisk": {
                        color: "#FF0000",
                      },
                    }}
                  >
                    {t("paymentMethod")}
                  </InputLabel>
                </Grid>
                <Grid item xs={8}>
                  <Select
                    value={paymentMethod}
                    onChange={handleChange}
                    id="cara-pembayaran"
                    t={t}
                    sx={{
                      width: "100%",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#E5E5E5",
                        borderRadius: 1,
                      },
                    }}
                  >
                    <Option value="kaunter">{t("pembayaranKaunter")}</Option>
                    <Option value="online" disabled={!onlinePaymentEnabled}>
                      {t("pembayaranOnline")}
                    </Option>
                  </Select>
                </Grid>
              </Grid>
            </Box>
            <Typography
              variant="body2"
              sx={{
                color: "#666666",
                fontSize: 12,
                marginTop: 10,
                textAlign: "center",
              }}
            >
              {t("paymentNote")}
            </Typography>
          </Box>

          <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
            <ButtonPrimary
              sx={{
                backgroundColor: "#00A7A7",
                "&:hover": {
                  backgroundColor: "#008F8F",
                },
                color: "white",
                borderRadius: 1,
                textTransform: "none",
              }}
              disabled={!isChecked || !paymentMethod}
              onClick={handleSubmit}
            >
              {t("hantar")}
            </ButtonPrimary>
          </Box>
        </Box>
      </Fade>
      <DialogConfirmation
        open={dialogOpen}
        onClose={handleCloseDialog}
        isMutating={isLoadingEditSocietyPaymentMethod}
        onAction={async () => {
          try {
            await editSociety();
          } finally {
            handleCloseDialog();
          }
        }}
        onConfirmationText={t("confirmSubmitApplication")}
      />
    </Box>
  );
}

export default PembayaranCawangan;
