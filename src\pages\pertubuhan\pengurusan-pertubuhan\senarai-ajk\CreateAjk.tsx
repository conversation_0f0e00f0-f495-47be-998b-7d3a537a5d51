/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from "react";
import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { OrganizationStepper } from "../organization-stepper";
import { ApplicationStatus } from "../../../../helpers/enums";
import { useSelector } from "react-redux";
import InfoQACard from "../InfoQACard";
import {
  DefaultCommitteeCitizenSocietyRegistrationInitialValue,
  useFormManagementCommitteeCitizenSocietyRegistrationHandleSubmit,
  useFormManagementCommitteeCitizenSocietyRegistrationInitialValue,
  useFormManagementCommitteeCitizenSocietyRegistrationValidationSchema,
} from "@/controllers";
import { Form } from "formik";
import { FormComitteeCitizenSocietyRegistrationInner } from "@/components/form/comittee/citizen/SocietyRegistrationInner";
import { FormikManualValidationProvider } from "@/contexts/formikManualValidation";

export const CreateAjk = <
  Payload extends DefaultCommitteeCitizenSocietyRegistrationInitialValue = DefaultCommitteeCitizenSocietyRegistrationInitialValue
>() => {
  const { t } = useTranslation();
  const [activeStep, setActiveStep] = useState(3);
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  const { getInitialValue } =
    useFormManagementCommitteeCitizenSocietyRegistrationInitialValue<Payload>();
  const { getValidationSchema } =
    useFormManagementCommitteeCitizenSocietyRegistrationValidationSchema<Payload>();

  const ajkData = getInitialValue();
  const isEdit = ajkData !== null;
  const { handleSubmit, createdId } =
    useFormManagementCommitteeCitizenSocietyRegistrationHandleSubmit<Payload>({
      isEdit,
    });

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  return (
    <Box sx={{ display: "flex", gap: 3 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 2 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#fff",
          borderRadius: "14px",
          width: "100%",
        }}
      >
        <FormikManualValidationProvider
          initialValues={ajkData}
          onSubmit={handleSubmit}
          validationSchema={getValidationSchema()}
          enableReinitialize
        >
          <Form
            style={{
              borderRadius: "14px",
              width: "100%",
            }}
          >
            <FormComitteeCitizenSocietyRegistrationInner
              createdId={createdId}
            />
          </Form>
        </FormikManualValidationProvider>
      </Box>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <OrganizationStepper
          activeStep={activeStep}
          hidePayment={
            societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI
          }
        />
        {societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI ? (
          <Box
            sx={{
              padding: 3,
              backgroundColor: "white",
              borderRadius: "15px",
              maxHeight: "60vh",
              maxWidth: "18vw",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("kuiri")}
            </Typography>
            <Box
              sx={{
                padding: 3,
                backgroundColor: "#DADADA",
                borderRadius: "15px",
                maxHeight: "60vh",
                maxWidth: "18vw",
              }}
            >
              <Typography
                sx={{
                  mb: 8,
                  fontSize: "12px",
                  color: "#666666",
                  fontWeight: "500 !important",
                }}
              >
                {societyDataRedux.queryText}
              </Typography>
            </Box>
          </Box>
        ) : null}
        <InfoQACard />
      </Box>
    </Box>
  );
};

export default CreateAjk;
