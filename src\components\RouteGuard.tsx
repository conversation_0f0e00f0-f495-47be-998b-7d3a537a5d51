import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Typography, Button, Alert, IconButton } from '@mui/material';
import { useTranslation } from 'react-i18next';
import CloseIcon from '@mui/icons-material/Close';
import { useRouteDetection, usePortalNavigation, useAutoPortalUpdate } from '@/helpers/hooks/useRouteDetection';

interface RouteGuardProps {
  children: React.ReactNode;
  showDebugInfo?: boolean;
  autoUpdatePortal?: boolean;
}

/**
 * Component that guards routes based on user portal type
 * Automatically redirects users if they don't have access to the current route
 * Can optionally auto-update user portal based on route detection
 */
export const RouteGuard: React.FC<RouteGuardProps> = ({
  children,
  showDebugInfo = false,
  autoUpdatePortal = false
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const routeDetection = useRouteDetection(autoUpdatePortal);
  const portalNavigation = usePortalNavigation();
  const autoPortalUpdate = useAutoPortalUpdate(autoUpdatePortal);

  // State for controlling debug info visibility
  const [isDebugVisible, setIsDebugVisible] = useState(showDebugInfo);

  // Sync debug visibility with prop changes
  useEffect(() => {
    setIsDebugVisible(showDebugInfo);
  }, [showDebugInfo]);

  useEffect(() => {
    // Auto-redirect if user doesn't have access and route is registered
    if (
      routeDetection.shouldRedirect &&
      routeDetection.routePortalType !== 'unknown'
    ) {
      console.warn(`Access denied to ${routeDetection.currentPath}. Redirecting to ${portalNavigation.defaultRoute}`);
      navigate(portalNavigation.defaultRoute, { replace: true });
    }
  }, [routeDetection.shouldRedirect, routeDetection.currentPath, navigate, portalNavigation.defaultRoute]);

  // Show access denied message for registered routes without access
  if (!routeDetection.hasAccess && routeDetection.routePortalType !== 'unknown') {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Alert severity="warning" sx={{ mb: 2 }}>
          <Typography variant="h6" gutterBottom>
            {t('accessDenied', 'Access Denied')}
          </Typography>
          <Typography variant="body2" sx={{ mb: 2 }}>
            {t('noAccessToRoute', 'You do not have access to this route.')}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Route: {routeDetection.currentPath}<br/>
            Detected as: {routeDetection.routePortalType}<br/>
            Your portal: {routeDetection.userPortalType}
          </Typography>
        </Alert>

        <Button
          variant="contained"
          onClick={() => navigate(portalNavigation.defaultRoute)}
          sx={{ mr: 2 }}
        >
          {t('goToHome', 'Go to Home')}
        </Button>

        <Button
          variant="outlined"
          onClick={() => navigate(-1)}
        >
          {t('goBack', 'Go Back')}
        </Button>
      </Box>
    );
  }

  return (
    <>
      {isDebugVisible && (
        <Box sx={{
          position: 'fixed',
          top: 10,
          right: 10,
          bgcolor: 'background.paper',
          p: 2,
          border: 1,
          borderColor: 'divider',
          borderRadius: 1,
          zIndex: 9999,
          fontSize: '0.75rem',
          maxWidth: 320,
          boxShadow: 3
        }}>
          {/* Header with title and close button */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 1
          }}>
            <Typography variant="caption" component="div" sx={{ fontWeight: 'bold' }}>
              Route Debug Info
            </Typography>
            <IconButton
              size="small"
              onClick={() => setIsDebugVisible(false)}
              sx={{
                p: 0.5,
                ml: 1,
                '&:hover': {
                  bgcolor: 'action.hover'
                }
              }}
            >
              <CloseIcon sx={{ fontSize: '1rem' }} />
            </IconButton>
          </Box>

          {/* Debug information */}
          <Typography variant="caption" component="div">
            <strong>Path:</strong> {routeDetection.currentPath}
          </Typography>
          <Typography variant="caption" component="div">
            <strong>Portal Type:</strong> {routeDetection.routePortalType}
          </Typography>

          {/* Warning for unregistered routes */}
          {routeDetection.routePortalType === 'unknown' && (
            <Typography variant="caption" component="div" sx={{ color: 'warning.main', fontWeight: 'bold' }}>
              ⚠️ <strong>Route Not Registered!</strong> Please add this route to registerRoutes()
            </Typography>
          )}

          <Typography variant="caption" component="div">
            <strong>User Portal:</strong> {routeDetection.userPortalType}
          </Typography>
          <Typography variant="caption" component="div">
            <strong>Has Access:</strong> {routeDetection.hasAccess ? '✅' : '❌'}
          </Typography>
          <Typography variant="caption" component="div">
            <strong>Is Correct Portal:</strong> {routeDetection.isCorrectPortal ? '✅' : '❌'}
          </Typography>
          <Typography variant="caption" component="div">
            <strong>Auto Update:</strong> {autoUpdatePortal ? '✅' : '❌'}
          </Typography>
          {autoUpdatePortal && (
            <Typography variant="caption" component="div">
              <strong>Can Update:</strong> {routeDetection.canUpdatePortal ? '✅' : '❌'}
            </Typography>
          )}

          {/* Action buttons */}
          <Box sx={{ mt: 1, display: 'flex', gap: 0.5 }}>
            <Button
              size="small"
              variant="outlined"
              onClick={() => setIsDebugVisible(false)}
              sx={{ fontSize: '0.6rem', py: 0.25, px: 1 }}
            >
              Hide
            </Button>
            {routeDetection.canUpdatePortal && !routeDetection.isCorrectPortal && (
              <Button
                size="small"
                variant="contained"
                onClick={routeDetection.updatePortalBasedOnRoute}
                sx={{ fontSize: '0.6rem', py: 0.25, px: 1 }}
              >
                Update Portal
              </Button>
            )}
          </Box>
        </Box>
      )}

      {/* Toggle button to show debug info when hidden */}
      {!isDebugVisible && showDebugInfo && (
        <Box sx={{
          position: 'fixed',
          top: 10,
          right: 10,
          zIndex: 9999
        }}>
          <Button
            size="small"
            variant="outlined"
            onClick={() => setIsDebugVisible(true)}
            sx={{
              fontSize: '0.6rem',
              py: 0.5,
              px: 1,
              minWidth: 'auto',
              bgcolor: 'background.paper',
              '&:hover': {
                bgcolor: 'action.hover'
              }
            }}
          >
            Debug
          </Button>
        </Box>
      )}

      {children}
    </>
  );
};

/**
 * Higher-order component for route protection
 */
export const withRouteGuard = <P extends object>(
  Component: React.ComponentType<P>,
  options?: { showDebugInfo?: boolean; autoUpdatePortal?: boolean }
) => {
  return (props: P) => (
    <RouteGuard
      showDebugInfo={options?.showDebugInfo}
      autoUpdatePortal={options?.autoUpdatePortal}
    >
      <Component {...props} />
    </RouteGuard>
  );
};

export default RouteGuard;
