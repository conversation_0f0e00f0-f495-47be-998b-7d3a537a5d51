import React from "react";
import { t } from "i18next";
import { useFormContext } from "react-hook-form";

import { Box, IconButton } from "@mui/material";
import { TrashIcon } from "@/components/icons";
import TextFieldController from "@/components/input/TextFieldController";

import { IMember } from ".";

interface MemberRowProps {
  member: IMember;
  index: number;
  disabledState: boolean;
  isViewOnly: boolean;
  meetingDetail?: boolean;
  isEditable: boolean;
  deletedMembersCount: number;
  handleRemoveMember: (
    id: string | number | null,
    tempId: string | null
  ) => void;
}

const MemberRow: React.FC<MemberRowProps> = React.memo(
  ({
    member,
    index,
    disabledState,
    isViewOnly,
    meetingDetail,
    isEditable,
    deletedMembersCount,
    handleRemoveMember,
  }) => {
    const { control } = useFormContext();
    return (
      <Box
        sx={{
          display: "flex",
          gap: "10px",
          position: "relative",
          marginBottom: 1,
          "&:last-of-type": { marginBottom: 0 },
        }}
      >
        <TextFieldController
          control={control}
          name={`memberAttendances.${index + deletedMembersCount}.name`}
          placeholder={t("name")}
          disabled={disabledState}
        />
        <TextFieldController
          control={control}
          name={`memberAttendances.${index + deletedMembersCount}.position`}
          placeholder={t("position")}
          disabled={disabledState}
        />

        {!isViewOnly && (!meetingDetail || isEditable) && (
          <IconButton
            sx={{
              position: "absolute",
              right: "-15px",
              top: "50%",
              transform: "translateY(-50%)",
              padding: 0,
            }}
            onClick={() =>
              handleRemoveMember(member.id ?? null, member.tempId ?? null)
            }
          >
            <TrashIcon color="red" />
          </IconButton>
        )}
      </Box>
    );
  }
);

export default MemberRow;
