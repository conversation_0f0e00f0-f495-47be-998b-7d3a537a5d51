import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation, useUpdate } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { API_URL } from "../../../../../api";
import { useDispatch } from "react-redux";
import {
  addAjk,
  AJKState,
  selectAJK,
  setAJK,
} from "../../../../../redux/ajkReducer";
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import { useCustom } from "@refinedev/core";
import {
  ApplicationStatusEnum,
  CommiteeEnum,
  ConstitutionType,
} from "../../../../../helpers/enums";
import { Controller, FieldValues, useForm } from "react-hook-form";
import Input from "@/components/input/Input";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import { capitalizeWords } from "@/helpers";
import { DialogConfirmation } from "@/components";
import MessageDialog from "@/components/dialog/message";

interface FasalContentEnamCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentEnamCawangan: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const [showErrorAjk, setShowErrorAjk] = useState(false);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [tempohJawatan, setTempohJawatan] = useState(t("setahun"));
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [kekerapan, setKekerapan] = useState<Number|string>(4);
  const [tempohPelucutan, setTempohPelucutan] = useState("0");
  const [tempohPelucutanWaktu, setTempohPelucutanWaktu] = useState("day");
  const [pengerusi, setPengerusi] = useState(t("chairman"));
  const [jumlahPengerusi, setJumlahPengerusi] = useState<any>("1");
  const [timbalan, setTimbalan] = useState(t("timbalanPengerusi"));
  const [jumlahTimbalan, setJumlahTimbalan] = useState<any>("0");
  const [naib, setNaib] = useState(t("naibPengerusi"));
  const [jumlahNaib, setJumlahNaib] = useState<any>("0");
  const [setiaUsaha, setSetiaUsaha] = useState(t("secretary"));
  const [jumlahSetiaUsaha, setJumlahSetiaUsaha] = useState<any>("1");
  const [penolongSetiaUsaha, setPenolongSetiaUsaha] = useState(
    t("asistantSecretary")
  );
 
  const [jumlahPenolongSetiaUsaha, setJumlahPenolongSetiaUsaha] =
    useState<any>("0");
  const [bendahari, setBendahari] = useState(t("treasurer"));
  const [jumlahBendahari, setJumlahBendahari] = useState<any>("1");
  const [penolongBendahari, setPenolongBendahari] = useState(
    t("asistantTreasurer")
  );
  const [jumlahPenolongBendahari, setJumlahPenolongBendahari] =
    useState<any>("0");
  const [ahliBiasa, setAhliBiasa] = useState(t("ordinaryCommitteeMember"));
  const [jumlahAhliBiasa, setJumlahAhliBiasa] = useState<any>("0");
  const [notisPanggilanMesyuarat, setNotisPanggilanMesyuarat] =
    useState<any>(1);
  const [dialogSaveOpen, setDialogSaveOpen] = useState(false);
  const [isLoadingUpdate, setIsLoadingUpdate] = useState(false);
  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState("");
  //const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [isRequiredConstitution, setIsRequiredConstitution] = useState(true);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();
  const { mutate: preCreateAjk } = useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (
      societyDataRedux?.constitutionType === ConstitutionType.IndukAgama[1] ||
      societyDataRedux?.constitutionType ===
        ConstitutionType.CawanganAgama[1] ||
      societyDataRedux?.constitutionType ===
        ConstitutionType.FaedahBersama[1] ||
      societyDataRedux?.constitutionType === ConstitutionType.CawanganNGO[1]
    ) {
      setIsRequiredConstitution(false);
    } else {
      setIsRequiredConstitution(true);
    }
  }, [societyDataRedux?.constitutionType]);

  useEffect(() => {
    if (clause) { 
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      } 
      setDataId(clause.id);
      if (clause.clauseContentId) { 
        setClauseContentId(clause.clauseContentId);
      } 
      if(clause.constitutionValues.length > 0){ 
      const fieldMappings: Record<string, (value: string) => void> = {
        "Tempoh Pelantikan Jawatankuasa": setTempohJawatan,
        "Jenis Mesyuarat Agung": setPemilihanAjk,
        "Kekerapan Mesyuarat Jawatankuasa": setKekerapan,
        "Bilangan tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa": setTempohPelucutan,
        "Tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa": setTempohPelucutanWaktu,
        "Pengerusi": setPengerusi,
        "Bilangan Pengerusi": setJumlahPengerusi,
        "Timbalan Pengerusi": setTimbalan,
        "Bilangan Timbalan Pengerusi": setJumlahTimbalan,
        "Naib Pengerusi": setNaib,
        "Bilangan Naib Pengerusi": setJumlahNaib,
        "Setiausaha": setSetiaUsaha,
        "Bilangan Setiausaha": setJumlahSetiaUsaha,
        "Penolong Setiausaha": setPenolongSetiaUsaha,
        "Bilangan Penolong Setiausaha": setJumlahPenolongSetiaUsaha,
        "Bendahari": setBendahari,
        "Bilangan Bendahari": setJumlahBendahari,
        "Penolong Bendahari": setPenolongBendahari,
        "Bilangan Penolong Bendahari": setJumlahPenolongBendahari,
        "Ahli Jawatankuasa Biasa": setAhliBiasa,
        "Bilangan Ahli Jawatankuasa Biasa": setJumlahAhliBiasa,
        "Notis Panggilan Mesyuarat": setNotisPanggilanMesyuarat
      };
  
      if(clause.constitutionValues){
        clause.constitutionValues.forEach((item:any) => {
          const setter = fieldMappings[item.titleName];
          if (setter && item.definitionName) {
            setter(item.definitionName);
          }
        });
      }
      setIsEdit(clause.edit);
    }
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id } = useParams();

  const dispatch = useDispatch();

  const ajk = useSelector(selectAJK);
  let clauseContent = clause.clauseContent;

  clauseContent = clauseContent.replaceAll(
    /<<tempoh pelantikan jawatankuasa>>/gi,
    `<b>${tempohJawatan || "<<tempoh pelantikan jawatankuasa>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${pemilihanAjk || "<<jenis mesyuarat agung>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kekerapan mesyuarat>>/gi,
    `<b>${kekerapan || "<<kekerapan mesyuarat>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh membela diri pelucutan>>/gi,
    `<b>${tempohPelucutan || "<<tempoh membela diri pelucutan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<notis panggilan mesyuarat>>/gi,
    `<b>${notisPanggilanMesyuarat || "<<notis panggilan mesyuarat>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jawatan Pengerusi>>/gi,
    `<b>${pengerusi || "<<jawatan Pengerusi>>"}</b>`
  );

  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahPengerusi || '<<jenis mesyuarat>>'}</b>`);

  if (Number(jumlahTimbalan) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Timbalan Pengerusi>>/gi,
      `<b>${timbalan || "<<jawatan Timbalan Pengerusi>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan Timbalan Pengerusi>>/gi,
      `<b>${
        Number(jumlahTimbalan) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahTimbalan} orang` || "<<bilangan Timbalan Pengerusi>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*ii\.\s*<<bilangan Timbalan Pengerusi>>\s*(?:orang\s+)?<<jawatan Timbalan Pengerusi>>\s*[\r\n\t]?/gim,
      "    "
    );
  }

  if (Number(jumlahNaib) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Naib Pengerusi>>/gi,
      `<b>${naib || "<<jawatan Naib Pengerusi>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan Naib Pengerusi>>/gi,
      `<b>${
        Number(jumlahNaib) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahNaib} orang` || "<<bilangan Naib Pengerusi>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*iii\.\s*<<bilangan Naib Pengerusi>>\s*(?:orang\s+)?<<jawatan Naib Pengerusi>>\s*[\r\n\t]?/gim,
      "    "
    );
  }

  clauseContent = clauseContent.replaceAll(
    /<<jawatan Bendahari Agung>>/gi,
    `<b>${bendahari || "<<jawatan Bendahari Agung>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahBendahari || '<<jenis mesyuarat>>'}</b>`);
  if (Number(jumlahPenolongBendahari) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Penolong Bendahari>>/gi,
      `<b>${penolongBendahari || "<<jawatan Penolong Bendahari>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan pen. bendahari>>/gi,
      `<b>${
        Number(jumlahPenolongBendahari) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahPenolongBendahari} orang` || "<<bilangan pen. bendahari>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*vii\.\s*<<bilangan pen. bendahari>>\s*(?:orang\s+)?<<jawatan Penolong Bendahari>>\s*[\r\n]?/gim,
      "    "
    );
  }

  clauseContent = clauseContent.replaceAll(
    /<<jawatan Setiausaha Agung>>/gi,
    `<b>${setiaUsaha || "<<jawatan Setiausaha Agung>>"}</b>`
  );

  if (Number(jumlahPenolongSetiaUsaha) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Penolong Setiausaha>>/gi,
      `<b>${penolongSetiaUsaha || "<<jawatan Penolong Setiausaha>>"}</b>`
    );

    clauseContent = clauseContent.replaceAll(
      /<<bilangan pen. SU>>/gi,
      `<b>${
        Number(jumlahPenolongSetiaUsaha) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahPenolongSetiaUsaha} orang` || "<<bilangan pen. SU>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*v\.\s*<<bilangan pen\. SU>>\s*(?:orang\s+)?<<jawatan Penolong Setiausaha>>\s*[\r\n]?/gim,
      "    "
    );
  }

  if (Number(jumlahAhliBiasa) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Ahli Jawatankuasa Biasa>>/gi,
      `<b>${ahliBiasa || "<<jawatan Ahli Jawatankuasa Biasa>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan ajk>>/gi,
      `<b>${
        Number(jumlahAhliBiasa) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahAhliBiasa} orang` || "<<bilangan ajk>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*viii\.\s*<<bilangan ajk>>\s*(?:orang\s+)?<<jawatan Ahli Jawatankuasa Biasa>>\s*[\r\n]?/gim,
      "\n\n"
    );
  }
   
  clauseContent = clauseContent.replace(
    /<<jawatan telah diubahsuai>>\s*/gi,
    ''
  ); 
  clauseContent = clauseContent.replace(
   /\s*<<penambahan jawatan diubahsuai>>/gi, 
    ``
    );

  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  clauseContent = renumberRomanList(clauseContent);

  function renumberRomanList(content: any) {
    const romanNumerals = [
      "i",
      "ii",
      "iii",
      "iv",
      "v",
      "vi",
      "vii",
      "viii",
      "ix",
      "x",
    ];
    let index = 0;

    return content.replace(
      /^(\s*)(?:i{1,3}|iv|v?i{0,3}|ix|x)\.\s/gim,
      (_: any, indent: any) => {
        const roman = romanNumerals[index++] || `${index}.`;
        return `${indent}${roman}. `;
      }
    );
  }

  const { data: listCommittee } = useCustom({
    url: `${API_URL}/society/committee/getAll`,
    method: "get",
    config: {
      filters: [
        {
          field: "pageNo",
          operator: "eq",
          value: 1,
        },
        {
          field: "pageSize",
          operator: "eq",
          value: 1000,
        },
        {
          field: "societyId",
          operator: "eq",
          value: societyDataRedux.id,
        },
      ],
      // query: {
      //   // societyId: id,
      //   pageNo: 1,
      //   // pageSize: 10,
      // },

      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !isEdit,
    },
  });

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!tempohJawatan) {
      errors.tempohJawatan = t("fieldRequired");
    }
    if (!pemilihanAjk) {
      errors.pemilihanAjk = t("fieldRequired");
    }
    if (!kekerapan) {
      errors.kekerapan = t("fieldRequired");
    }
    if (!tempohPelucutan) {
      errors.tempohPelucutan = t("fieldRequired");
    }
    if (!tempohPelucutanWaktu) {
      errors.tempohPelucutanWaktu = t("fieldRequired");
    }
    if (!jumlahPengerusi) {
      errors.jumlahPengerusi = t("fieldRequired");
    }
    if (!pengerusi) {
      errors.pengerusi = t("fieldRequired");
    }
    if (!jumlahTimbalan && isRequiredConstitution) {
      errors.jumlahTimbalan = t("fieldRequired");
    }
    if (!timbalan && isRequiredConstitution) {
      errors.timbalan = t("fieldRequired");
    }
    if (!jumlahNaib && isRequiredConstitution) {
      errors.jumlahNaib = t("fieldRequired");
    }
    if (!naib && isRequiredConstitution) {
      errors.naib = t("fieldRequired");
    }
    if (!jumlahSetiaUsaha) {
      errors.jumlahSetiaUsaha = t("fieldRequired");
    }
    if (!setiaUsaha) {
      errors.setiaUsaha = t("fieldRequired");
    }
    if (!jumlahPenolongSetiaUsaha && isRequiredConstitution) {
      errors.jumlahPenolongSetiaUsaha = t("fieldRequired");
    }
    if (!penolongSetiaUsaha && isRequiredConstitution) {
      errors.penolongSetiaUsaha = t("fieldRequired");
    }
    if (!jumlahBendahari) {
      errors.jumlahBendahari = t("fieldRequired");
    }
    if (!bendahari) {
      errors.bendahari = t("fieldRequired");
    }
    if (!jumlahPenolongBendahari && isRequiredConstitution) {
      errors.jumlahPenolongBendahari = t("fieldRequired");
    }
    if (!penolongBendahari && isRequiredConstitution) {
      errors.penolongBendahari = t("fieldRequired");
    }
    if (!jumlahAhliBiasa && isRequiredConstitution) {
      errors.jumlahAhliBiasa = t("fieldRequired");
    }
    if (!ahliBiasa && isRequiredConstitution) {
      errors.ahliBiasa = t("fieldRequired");
    }
    return errors;
  };

  const committeeData = listCommittee?.data?.data?.data || [];

  const handleConfirm = () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) { 
      setFormErrors(errors);
      return;
    } 
    if (totalBilangan < 7) { 
      setShowErrorAjk(true);
      return;
    }
    const designationCode = [];
    for (let i = 0; i < jumlahPengerusi; i++) {
      designationCode.push(
        CommiteeEnum[pengerusi as keyof typeof CommiteeEnum]
      );
    }
    for (let i = 0; i < jumlahTimbalan; i++) {
      designationCode.push(CommiteeEnum[timbalan as keyof typeof CommiteeEnum]);
    }
    for (let i = 0; i < jumlahNaib; i++) {
      designationCode.push(CommiteeEnum[naib as keyof typeof CommiteeEnum]);
    }
    for (let i = 0; i < jumlahSetiaUsaha; i++) {
      designationCode.push(
        CommiteeEnum[setiaUsaha as keyof typeof CommiteeEnum]
      );
    }
    for (let i = 0; i < jumlahPenolongSetiaUsaha; i++) {
      designationCode.push(
        CommiteeEnum[penolongSetiaUsaha as keyof typeof CommiteeEnum]
      );
    }
    for (let i = 0; i < jumlahBendahari; i++) {
      designationCode.push(
        CommiteeEnum[bendahari as keyof typeof CommiteeEnum]
      );
    }
    for (let i = 0; i < jumlahPenolongBendahari; i++) {
      designationCode.push(
        CommiteeEnum[penolongBendahari as keyof typeof CommiteeEnum]
      );
    }
    for (let i = 0; i < jumlahAhliBiasa; i++) {
      designationCode.push(CommiteeEnum["Ahli Jawatankuasa Biasa"]);
    }

    //dispatch(setAJK(tempArrayAJK));*/
    if (isEdit || committeeData.length > 0) {
      preCreateAjk(
        {
          url: `${API_URL}/society/committee/updateForRegistration`,
          method: "put",
          values: {
            societyId,
            designationCode,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: false,
        },
        {
          onSuccess(data: any, variables: any, context: any) {},
        }
      );
    } else {
      createClauseContent(
        {
          resource: "society/committee/createForRegistration",
          values: {
            societyId,
            designationCode,
          },
          meta: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: false,
        },
        {
          onSuccess(data: any, variables: any, context: any) {},
        }
      );
    } 
    handleSaveContent({
      i18n,
      societyId,
      societyName: namaPertubuhan,
      dataId,
      isEdit,
      clauseNo: clauseNo,
      clauseName: clauseName,
      createClauseContent,
      editClauseContent,
      description: clauseContent,
      constitutionValues: [
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: tempohJawatan,
          titleName: "Tempoh Pelantikan Jawatankuasa",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: pemilihanAjk,
          titleName: "Jenis Mesyuarat Agung",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: kekerapan,
          titleName: "Kekerapan Mesyuarat Jawatankuasa",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: tempohPelucutan,
          titleName:
            "Bilangan tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: tempohPelucutanWaktu,
          titleName:
            "Tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: pengerusi,
          titleName: "Pengerusi",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: jumlahPengerusi,
          titleName: "Bilangan Pengerusi",
        } ,
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: timbalan,
          titleName: "Timbalan Pengerusi",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: jumlahTimbalan,
          titleName: "Bilangan Timbalan Pengerusi",
        } ,{
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: naib,
          titleName: "Naib Pengerusi",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: jumlahNaib,
          titleName: "Bilangan Naib Pengerusi",
        } ,{
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: setiaUsaha,
          titleName: "Setiausaha",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: jumlahSetiaUsaha,
          titleName: "Bilangan Setiausaha",
        },{
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: penolongSetiaUsaha,
          titleName: "Penolong Setiausaha",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: jumlahPenolongSetiaUsaha,
          titleName: "Bilangan Penolong Setiausaha",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: bendahari,
          titleName: "Bendahari",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: jumlahBendahari,
          titleName: "Bilangan Bendahari",
        },{
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: penolongBendahari,
          titleName: "Penolong Bendahari",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: jumlahPenolongBendahari,
          titleName: "Bilangan Penolong Bendahari",
        },{
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: ahliBiasa,
          titleName: "Ahli Jawatankuasa Biasa",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: jumlahAhliBiasa,
          titleName: "Bilangan Ahli Jawatankuasa Biasa",
        } ,
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: notisPanggilanMesyuarat,
          titleName: "Notis Panggilan Mesyuarat",
        },
      ],
      clause: "clause6",
      clauseCount: 6,
      clauseContentId,
    });
    if (!isEditingContent) {
      setIsLoadingUpdate(false);
      setDialogSaveOpen(false);
    }
  };

  const onSubmit = () => {
    if (clause?.constitutionValues?.length > 0) {
      setDialogSaveOpen(true);
    } else {
      handleConfirm();
    }
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  const totalBilangan =
    parseInt(jumlahPengerusi || 0) +
    parseInt(jumlahTimbalan || 0) +
    parseInt(jumlahNaib || 0) +
    parseInt(jumlahSetiaUsaha || 0) +
    parseInt(jumlahPenolongSetiaUsaha || 0) +
    parseInt(jumlahBendahari || 0) +
    parseInt(jumlahPenolongBendahari || 0) +
    parseInt(jumlahAhliBiasa || 0);

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("positionOfAuthority")}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <Typography sx={labelStyle}>
                {t("jenisMesyuaratAgung")}
                <Typography sx={{ display: "inline", color: "red" }}>
                                        *
              </Typography>
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl error={!!formErrors.pemilihanAjk} fullWidth required>
                <Select
                  size="small"
                  value={pemilihanAjk}
                  displayEmpty
                  onChange={(e) => {
                    setPemilihanAjk(e.target.value as string);
                    if ((e.target.value as string) == t("annual")) {
                      setTempohJawatan(t("setahun"));
                    } else if ((e.target.value as string) == t("biennial")) {
                      setTempohJawatan(t("duaTahun"));
                    }
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      pemilihanAjk: "",
                    }));
                  }}
                >
                  <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                  <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={4}>
              <Typography sx={labelStyle}>{t("electionPeriod")}<Typography sx={{ display: "inline", color: "red" }}>
                                        *
              </Typography></Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl
                error={!!formErrors.tempohJawatan}
                fullWidth
                required
              >
                <Select
                  size="small"
                  value={tempohJawatan}
                  disabled={pemilihanAjk == t("biennial")}
                  displayEmpty
                  onChange={(e) => {
                    setTempohJawatan(e.target.value as string);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      tempohJawatan: "",
                    }));
                  }}
                >
                  <MenuItem value={t("setahun")}>{t("setahun")}</MenuItem>
                  <MenuItem value={t("duaTahun")}>{t("duaTahun")}</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={4}>
              <Typography sx={labelStyle}>{t("meetingFrequency")}
                <Typography sx={{ display: "inline", color: "red" }}>
                                                      *
                </Typography></Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                type="number"
                onKeyDown={(e) => {
                  if (
                    e.key.toLowerCase() === "e" ||
                    e.key === "E" ||
                    e.key === "+" ||
                    e.key === "-"
                  ) {
                    e.preventDefault();
                  }
                }}
                size="small"
                placeholder="4"
                fullWidth
                required
                value={kekerapan}
                error={!!formErrors.kekerapan}
                helperText={formErrors.kekerapan}
                onChange={(e) => {
                  setKekerapan(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kekerapan: "",
                  }));
                }}
                InputProps={{
                  endAdornment: (
                    <Typography sx={{ ...labelStyle, mt: 1 }}>
                      {t("times")}
                    </Typography>
                  ),
                  inputProps: {
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                    min: 0,
                  },
                }}
              />
            </Grid>
            <Grid item />
          </Grid>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={4}>
              <Typography sx={labelStyle}>
                {t("periodOfDefendingOneselfTwo")}
                  <Typography sx={{ display: "inline", color: "red" }}>
                                                        *
                  </Typography>
              </Typography>
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                type="number"
                onKeyDown={(e) => {
                  if (
                    e.key.toLowerCase() === "e" ||
                    e.key === "E" ||
                    e.key === "+" ||
                    e.key === "-"
                  ) {
                    e.preventDefault();
                  }
                }}
                size="small"
                placeholder="0"
                fullWidth
                required
                value={tempohPelucutan}
                error={!!formErrors.tempohPelucutan}
                helperText={formErrors.tempohPelucutan}
                onChange={(e) => {
                  setTempohPelucutan(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    tempohPelucutan: "",
                  }));
                }}
                InputProps={{
                  inputProps: {
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                    min: 0,
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth required>
                <Select
                  size="small"
                  value={tempohPelucutanWaktu}
                  displayEmpty
                  onChange={(e) =>
                    setTempohPelucutanWaktu(e.target.value as string)
                  }
                  readOnly={true}
                >
                  <MenuItem value={"day"} selected={true}>
                    {t("day")}
                  </MenuItem>
                  <MenuItem value={"week"} disabled>
                    {t("week")}
                  </MenuItem>
                  <MenuItem value={"month"} disabled>
                    {t("month")}
                  </MenuItem>
                  <MenuItem value={"year"} disabled>
                    {t("year")}
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={4}>
              <Typography sx={labelStyle}>
                {t("notisPanggilanMesyuarat")}
                     <Typography sx={{ display: "inline", color: "red" }}>
                                                *
                      </Typography>
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                type="number"
                onKeyDown={(e) => {
                  if (
                    e.key.toLowerCase() === "e" ||
                    e.key === "E" ||
                    e.key === "+" ||
                    e.key === "-"
                  ) {
                    e.preventDefault();
                  }
                }}
                size="small"
                placeholder="0"
                fullWidth
                required
                value={notisPanggilanMesyuarat}
                error={!!formErrors.notisPanggilanMesyuarat}
                helperText={formErrors.notisPanggilanMesyuarat}
                onChange={(e) => {
                  setNotisPanggilanMesyuarat(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    notisPanggilanMesyuarat: "",
                  }));
                }}
                InputProps={{
                  inputProps: {
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                    min: 0,
                  },
                }}
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("ahliJawatanKuasa")} 
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={2}>     
              <Typography variant="subtitle1" sx={sectionStyle}>{t("NumberofPositions")}</Typography> 
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" sx={sectionStyle}>{t("JobTitle")}</Typography>
            </Grid>
          </Grid>
          <Grid container spacing={2} >
            <Grid item xs={12} md={2}>
              <TextField
                type="number"
                onKeyDown={(e) => {
                  if (
                    e.key.toLowerCase() === "e" ||
                    e.key === "E" ||
                    e.key === "+" ||
                    e.key === "-"
                  ) {
                    e.preventDefault();
                  }
                }}
                size="small"
                placeholder="0"
                fullWidth
                disabled
                sx={{ background: "#E8E9E8" }}
                required
                value={jumlahPengerusi}
                error={!!formErrors.jumlahPengerusi}
                helperText={formErrors.jumlahPengerusi}
                onChange={(e) => {
                  setJumlahPengerusi(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahPengerusi: "",
                  }));
                }}
                InputProps={{
                  inputProps: {
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                    min: 0,
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl error={!!formErrors.pengerusi} fullWidth required>
                <Select
                  size="small"
                  value={pengerusi}
                  displayEmpty
                  required
                  onChange={(e) => {
                    setPengerusi(e.target.value as string);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      pengerusi: "",
                    }));
                  }}
                >
                  <MenuItem value={t("chairman")}>
                    {t("chairman")}{" "}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </MenuItem>
                  <MenuItem value={t("presiden")}>
                    {t("presiden")}{" "}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </MenuItem>
                  <MenuItem value={t("pengarah")}>
                    {t("pengarah")}{" "}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </MenuItem>
                  <MenuItem value={t("thepresident")}>
                    {t("thepresident")}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </MenuItem>
                </Select>
              </FormControl>
              {formErrors.pengerusi && (
                <FormHelperText sx={{ color: "red" }}>
                  {formErrors.pengerusi}
                </FormHelperText>
              )}
            </Grid>

            <Grid item />
          </Grid>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={2}>
              <TextField
                type="number"
                onKeyDown={(e) => {
                  if (
                    e.key.toLowerCase() === "e" ||
                    e.key === "E" ||
                    e.key === "+" ||
                    e.key === "-"
                  ) {
                    e.preventDefault();
                  }
                }}
                size="small"
                placeholder="0"
                fullWidth
                required={isRequiredConstitution}
                value={jumlahTimbalan}
                error={!!formErrors.jumlahTimbalan}
                helperText={formErrors.jumlahTimbalan}
                onChange={(e) => {
                  setJumlahTimbalan(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahTimbalan: "",
                  }));
                }}
                InputProps={{
                  inputProps: {
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                    min: 0,
                  },
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              {isRequiredConstitution ? (
                <FormControl error={!!formErrors.timbalan} fullWidth required>
                  <Select
                    size="small"
                    value={timbalan}
                    displayEmpty
                    required
                    onChange={(e) => {
                      setTimbalan(e.target.value as string);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        timbalan: "",
                      }));
                    }}
                  >
                    <MenuItem value={t("timbalanPengerusi")}>
                      {t("timbalanPengerusi")}
                    </MenuItem>
                    <MenuItem value={t("vicePresident")}>
                      {t("vicePresident")}
                    </MenuItem>
                    {/* <MenuItem value={t("timbalanPengarah")}>
                      {t("timbalanPengarah")}
                    </MenuItem> */}
                    <MenuItem value={t("timbalanyYangDipertua")}>
                      {t("timbalanyYangDipertua")}
                    </MenuItem>
                  </Select>
                </FormControl>
              ) : (
                <FormControl error={!!formErrors.timbalan} fullWidth>
                  <Select
                    size="small"
                    value={timbalan}
                    displayEmpty
                    onChange={(e) => {
                      setTimbalan(e.target.value as string);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        timbalan: "",
                      }));
                    }}
                  >
                    <MenuItem value={t("timbalanPengerusi")}>
                      {t("timbalanPengerusi")}
                    </MenuItem>
                    <MenuItem value={t("vicePresident")}>
                      {t("vicePresident")}
                    </MenuItem>
                    {/* <MenuItem value={t("timbalanPengarah")}>
                      {t("timbalanPengarah")}
                    </MenuItem> */}
                    <MenuItem value={t("timbalanyYangDipertua")}>
                      {t("timbalanyYangDipertua")}
                    </MenuItem>
                  </Select>
                </FormControl>
              )}

              {formErrors.timbalan && (
                <FormHelperText sx={{ color: "red" }}>
                  {formErrors.timbalan}
                </FormHelperText>
              )}
            </Grid>

            <Grid item />
          </Grid>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={2}>
              <TextField
                type="number"
                onKeyDown={(e) => {
                  if (
                    e.key.toLowerCase() === "e" ||
                    e.key === "E" ||
                    e.key === "+" ||
                    e.key === "-"
                  ) {
                    e.preventDefault();
                  }
                }}
                size="small"
                placeholder="0"
                fullWidth
                required={isRequiredConstitution}
                value={jumlahNaib}
                error={!!formErrors.jumlahNaib}
                helperText={formErrors.jumlahNaib}
                onChange={(e) => {
                  setJumlahNaib(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahNaib: "",
                  }));
                }}
                InputProps={{
                  inputProps: {
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                    min: 0,
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              {isRequiredConstitution ? (
                <FormControl error={!!formErrors.naib} fullWidth required>
                  <Select
                    size="small"
                    value={naib}
                    displayEmpty
                    required
                    onChange={(e) => {
                      setNaib(e.target.value as string);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        naib: "",
                      }));
                    }}
                  >
                    <MenuItem value={t("naibPengerusi")}>
                      {t("naibPengerusi")}
                    </MenuItem>
                    <MenuItem value={t("naibPresiden")}>
                      {t("naibPresiden")}
                    </MenuItem>
                    {/* <MenuItem value={t("naibPengarah")}>
                      {t("naibPengarah")}
                    </MenuItem> */}
                    <MenuItem value={t("naibYangDipertua")}>
                      {t("naibYangDipertua")}
                    </MenuItem>
                  </Select>
                </FormControl>
              ) : (
                <FormControl error={!!formErrors.naib} fullWidth>
                  <Select
                    size="small"
                    value={naib}
                    displayEmpty
                    onChange={(e) => {
                      setNaib(e.target.value as string);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        naib: "",
                      }));
                    }}
                  >
                    <MenuItem value={t("naibPengerusi")}>
                      {t("naibPengerusi")}
                    </MenuItem>
                    <MenuItem value={t("naibPresiden")}>
                      {t("naibPresiden")}
                    </MenuItem>
                    {/* <MenuItem value={t("naibPengarah")}>
                      {t("naibPengarah")}
                    </MenuItem> */}
                    <MenuItem value={t("naibYangDipertua")}>
                      {t("naibYangDipertua")}
                    </MenuItem>
                  </Select>
                </FormControl>
              )}

              {formErrors.naib && (
                <FormHelperText sx={{ color: "red" }}>
                  {formErrors.naib}
                </FormHelperText>
              )}
            </Grid>

            <Grid item />
          </Grid>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={2}>
              <TextField
                type="number"
                onKeyDown={(e) => {
                  if (
                    e.key.toLowerCase() === "e" ||
                    e.key === "E" ||
                    e.key === "+" ||
                    e.key === "-"
                  ) {
                    e.preventDefault();
                  }
                }}
                size="small"
                placeholder="0"
                fullWidth
                required
                disabled
                sx={{ background: "#E8E9E8" }}
                value={jumlahSetiaUsaha}
                onChange={(e) => {
                  setJumlahSetiaUsaha(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahSetiaUsaha: "",
                  }));
                }}
                InputProps={{
                  inputProps: {
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                    min: 0,
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl error={!!formErrors.setiaUsaha} fullWidth required>
                <Select
                  size="small"
                  value={setiaUsaha}
                  displayEmpty
                  required
                  onChange={(e) => {
                    setSetiaUsaha(e.target.value as string);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      setiaUsaha: "",
                    }));
                  }}
                >
                  <MenuItem value={t("secretary")}>
                    {t("secretary")}{" "}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </MenuItem>
                  <MenuItem value={t("generalSecretary")}>
                    {t("generalSecretary")}{" "}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </MenuItem>
                </Select>
              </FormControl>
              {formErrors.setiaUsaha && (
                <FormHelperText sx={{ color: "red" }}>
                  {formErrors.setiaUsaha}
                </FormHelperText>
              )}
            </Grid>

            <Grid item />
          </Grid>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={2}>
              <TextField
                type="number"
                onKeyDown={(e) => {
                  if (
                    e.key.toLowerCase() === "e" ||
                    e.key === "E" ||
                    e.key === "+" ||
                    e.key === "-"
                  ) {
                    e.preventDefault();
                  }
                }}
                size="small"
                placeholder="0"
                fullWidth
                required={isRequiredConstitution}
                error={!!formErrors.jumlahPenolongSetiaUsaha}
                helperText={formErrors.jumlahPenolongSetiaUsaha}
                value={jumlahPenolongSetiaUsaha}
                onChange={(e) => {
                  setJumlahPenolongSetiaUsaha(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahPenolongSetiaUsaha: "",
                  }));
                }}
                InputProps={{
                  inputProps: {
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                    min: 0,
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              {isRequiredConstitution ? (
                <FormControl
                  error={!!formErrors.penolongSetiaUsaha}
                  fullWidth
                  required
                >
                  <Select
                    size="small"
                    value={penolongSetiaUsaha}
                    displayEmpty
                    required
                    onChange={(e) => {
                      setPenolongSetiaUsaha(e.target.value as string);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        penolongSetiaUsaha: "",
                      }));
                    }}
                  >
                    <MenuItem value={t("asistantSecretary")}>
                      {t("asistantSecretary")}
                    </MenuItem>
                    <MenuItem value={t("generalAssistantSecretary")}>
                      {t("generalAssistantSecretary")}
                    </MenuItem>
                  </Select>
                </FormControl>
              ) : (
                <FormControl error={!!formErrors.penolongSetiaUsaha} fullWidth>
                  <Select
                    size="small"
                    value={penolongSetiaUsaha}
                    displayEmpty
                    onChange={(e) => {
                      setPenolongSetiaUsaha(e.target.value as string);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        penolongSetiaUsaha: "",
                      }));
                    }}
                  >
                    <MenuItem value={t("asistantSecretary")}>
                      {t("asistantSecretary")}
                    </MenuItem>
                    <MenuItem value={t("generalAssistantSecretary")}>
                      {t("generalAssistantSecretary")}
                    </MenuItem>
                  </Select>
                </FormControl>
              )}

              {formErrors.penolongSetiaUsaha && (
                <FormHelperText sx={{ color: "red" }}>
                  {formErrors.penolongSetiaUsaha}
                </FormHelperText>
              )}
            </Grid>

            <Grid item />
          </Grid>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={2}>
              <TextField
                type="number"
                onKeyDown={(e) => {
                  if (
                    e.key.toLowerCase() === "e" ||
                    e.key === "E" ||
                    e.key === "+" ||
                    e.key === "-"
                  ) {
                    e.preventDefault();
                  }
                }}
                size="small"
                placeholder="0"
                fullWidth
                required
                disabled
                sx={{ background: "#E8E9E8" }}
                value={jumlahBendahari}
                error={!!formErrors.jumlahBendahari}
                helperText={formErrors.jumlahBendahari}
                onChange={(e) => setJumlahBendahari(e.target.value)}
                InputProps={{
                  inputProps: {
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                    min: 0,
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl required error={!!formErrors.bendahari} fullWidth>
                <Select
                  size="small"
                  value={bendahari}
                  displayEmpty
                  required
                  onChange={(e) => {
                    setBendahari(e.target.value as string);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bendahari: "",
                    }));
                  }}
                >
                  <MenuItem value={t("treasurer")}>
                    {t("bendahari")}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </MenuItem>
                  {/* <MenuItem value={t("chiefTreasurer")}>
                    {t("chiefTreasurer")}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </MenuItem> */}
                  {/* <MenuItem value={t("honoraryTreasurer")}>
                    {t("honoraryTreasurer")}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </MenuItem> */}
                </Select>
              </FormControl>
              {formErrors.bendahari && (
                <FormHelperText sx={{ color: "red" }}>
                  {formErrors.bendahari}
                </FormHelperText>
              )}
            </Grid>

            <Grid item />
          </Grid>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={2}>
              <TextField
                type="number"
                onKeyDown={(e) => {
                  if (
                    e.key.toLowerCase() === "e" ||
                    e.key === "E" ||
                    e.key === "+" ||
                    e.key === "-"
                  ) {
                    e.preventDefault();
                  }
                }}
                size="small"
                placeholder="0"
                fullWidth
                required={isRequiredConstitution}
                value={jumlahPenolongBendahari}
                error={!!formErrors.jumlahPenolongBendahari}
                helperText={formErrors.jumlahPenolongBendahari}
                onChange={(e) => {
                  setJumlahPenolongBendahari(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahPenolongBendahari: "",
                  }));
                }}
                InputProps={{
                  inputProps: {
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                    min: 0,
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              {isRequiredConstitution ? (
                <FormControl
                  error={!!formErrors.penolongBendahari}
                  fullWidth
                  required
                >
                  <Select
                    size="small"
                    value={penolongBendahari}
                    displayEmpty
                    required
                    onChange={(e) => {
                      setPenolongBendahari(e.target.value as string);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        penolongBendahari: "",
                      }));
                    }}
                  >
                    <MenuItem value={t("asistantTreasurer")}>
                      {t("asistantTreasurer")}
                    </MenuItem>
                    {/* <MenuItem value={t("chiefAssistantTreasurer")}>
                      {t("chiefAssistantTreasurer")}
                    </MenuItem> */}
                    {/* <MenuItem value={t("honoraryAssistantTreasurer")}>
                      {t("honoraryAssistantTreasurer")}
                    </MenuItem> */}
                  </Select>
                </FormControl>
              ) : (
                <FormControl error={!!formErrors.penolongBendahari} fullWidth>
                  <Select
                    size="small"
                    value={penolongBendahari}
                    displayEmpty
                    onChange={(e) => {
                      setPenolongBendahari(e.target.value as string);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        penolongBendahari: "",
                      }));
                    }}
                  >
                    <MenuItem value={t("asistantTreasurer")}>
                      {t("asistantTreasurer")}
                    </MenuItem>
                    {/* <MenuItem value={t("chiefAssistantTreasurer")}>
                      {t("chiefAssistantTreasurer")}
                    </MenuItem> */}
                    {/* <MenuItem value={t("honoraryAssistantTreasurer")}>
                      {t("honoraryAssistantTreasurer")}
                    </MenuItem> */}
                  </Select>
                </FormControl>
              )}

              {formErrors.penolongBendahari && (
                <FormHelperText sx={{ color: "red" }}>
                  {formErrors.penolongBendahari}
                </FormHelperText>
              )}
            </Grid>

            <Grid item />
          </Grid>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={2}>
              <TextField
                type="number"
                onKeyDown={(e) => {
                  if (
                    e.key.toLowerCase() === "e" ||
                    e.key === "E" ||
                    e.key === "+" ||
                    e.key === "-"
                  ) {
                    e.preventDefault();
                  }
                }}
                size="small"
                placeholder="0"
                fullWidth
                required={isRequiredConstitution}
                value={jumlahAhliBiasa}
                error={!!formErrors.jumlahAhliBiasa}
                helperText={formErrors.jumlahAhliBiasa}
                onChange={(e) => {
                  setJumlahAhliBiasa(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahAhliBiasa: "",
                  }));
                }}
                InputProps={{
                  inputProps: {
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                    min: 0,
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              {isRequiredConstitution ? (
                <FormControl error={!!formErrors.ahliBiasa} fullWidth required>
                  <Select
                    size="small"
                    value={ahliBiasa}
                    displayEmpty
                    required
                    onChange={(e) => {
                      setAhliBiasa(e.target.value as string);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        ahliBiasa: "",
                      }));
                    }}
                  >
                    <MenuItem value={t("ordinaryCommitteeMember")}>
                      {t("ordinaryCommitteeMember")}
                    </MenuItem>
                  </Select>
                </FormControl>
              ) : (
                <FormControl error={!!formErrors.ahliBiasa} fullWidth>
                  <Select
                    size="small"
                    value={ahliBiasa}
                    displayEmpty
                    onChange={(e) => {
                      setAhliBiasa(e.target.value as string);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        ahliBiasa: "",
                      }));
                    }}
                  >
                    <MenuItem value={t("ordinaryCommitteeMember")}>
                      {t("ordinaryCommitteeMember")}
                    </MenuItem>
                  </Select>
                </FormControl>
              )}

              {formErrors.ahliBiasa && (
                <FormHelperText sx={{ color: "red" }}>
                  {formErrors.ahliBiasa}
                </FormHelperText>
              )}
            </Grid>

            <Grid item />
          </Grid>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={8}>
              <Typography
                sx={{
                  fontWeight: "500 !important",
                  color: "#666666",
                  border: "1px solid #DADADA",
                  borderRadius: "5px",
                  py: 1,
                  display: "flex",
                  justifyContent: "center",
                }}
              >
                {totalBilangan} Bilangan Ahli Jawatankuasa
              </Typography>
            </Grid>
            <Grid item />
          </Grid>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("clauseContent")} {id}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} md={12}>
              <Box
                sx={{
                  p: { xs: 1, sm: 2, md: 3 },
                  border: "1px solid #D9D9D9",
                  backgroundColor: "#FFFFFF",
                  borderRadius: "14px",
                }}
              >
                <FasalDisplayContent clauseContent={clauseContent} />
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>

        <Grid
          item
          xs={12}
          sx={{
            mt: 2,
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            justifyContent: "flex-end",
            gap: 1,
          }}
        >
          <ButtonOutline onClick={() => navigate(-1)}>
            {t("back")}
          </ButtonOutline>
          <ButtonPrimary
            type="submit"
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent
              ? t("saving")
              : clause.constitutionValues.length > 0
              ? t("update")
              : t("save")}
          </ButtonPrimary>
        </Grid>
      </form>
      <MessageDialog
        open={showErrorAjk}
        onClose={() => setShowErrorAjk(false)}
        message={t("moreThan7ajk")}
      />
      <DialogConfirmation
        open={dialogSaveOpen}
        // onClose={() => setDialogSaveOpen(false)}
        onConfirmationText={t("AJKNoUpdateConfirmation")}
        onAction={handleConfirm}
        onClose={() => setDialogSaveOpen(false)}
        isMutating={isLoadingUpdate || isEditingContent}
      />
    </>
  );
};

export default FasalContentEnamCawangan;
