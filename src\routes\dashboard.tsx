import { Route, Outlet } from "react-router-dom";
import { InternalUser } from "../pages/dashboard/internal-user";
// import { ExternalUser } from "../pages/dashboard/external-user";
import { registerRoutes } from "../helpers/routeDetector";
import { RouteGuard } from "../components/RouteGuard";

// Layout component to wrap all dashboard routes with protection
const DashboardLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Outlet />
  </RouteGuard>
);

// Register dashboard routes with their portal types
registerRoutes({
  '/internal-user': 'internal'
});

export const dashboard = {
  routes: (
    <Route element={<DashboardLayout />}>
      <Route path="/internal-user" element={<InternalUser />} />
      {/* <Route path="/external-user" element={<ExternalUser />} /> */}
    </Route>
  ),
};
