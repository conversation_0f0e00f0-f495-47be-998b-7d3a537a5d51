export interface InspectionList {
  id: string;
  investigationId: string;
  inspectionNo: string;
  noticeType: string;
  societyName: string;
  societyNo: string;
  societyId: string;
  inspectorName: string;
  inspectionDate: string;
  inspectionTime: string;
  societyCategory: string;
  societyType: string;
  inspectionPurpose: string;
  inspectionVenue: string;
  status: "Baru" | "Draf" | "Pemeriksaan" | "Menunggu_Syor" | "Selesai";
  noticeForm: string;
  inspectionReport: string;
  assignedInspector: string;
  caseOwner: string;
  flowStage: string;
  inspectionRemarks: string;
  currentDesignationOic: string;
  createdDate: string;
  modifiedDate: string;
  createdBy: number;
  modifiedBy: number;
  createdByName: string;
  modifiedByName: string;
}

export interface InspectionCreateResponse {
  id: string;
  inspectionNo: string;
  noticeType: string;
  societyName: string;
  societyNo: string;
  inspectorName: string;
  inspectionDate: string;
  inspectionTime: string;
  societyCategory: string;
  societyType: string;
  inspectionPurpose: string;
  inspectionVenue: string;
  noticeForm: any;
  inspectionReport: any;
  assignedInspector: any;
  caseOwner: number;
  flowLevel: any;
  inspectionRemarks: any;
  status: string;
  currentDesignationOic: string;
  state: string;
}

export interface InspectionReport {
  id: string;
  inspectionId: string;
  inspectionMembers: string;
  attendanceList: string;
  societyJoiningFee: number;
  societyMembershipReview: string;
  societyMembershipNoFasal: number;
  societyAjkReview: string;
  societyAjkNoFasal: number;
  societyDocumentsChecklist: string;
  currentBankName: string;
  currentAccountNo: string;
  currentBankAddress: string;
  currentBalanceDate: string;
  currentBalance: number;
  savingsBankName: string;
  savingsAccountNo: string;
  savingsBankAddress: string;
  savingsBalanceDate: string;
  savingsBalance: number;
  cashHolderName: string;
  cashReview: string;
  cashNoFasal: number;
  assetReview: string;
  trusteeNames: string;
  expenditureReview: string;
  expenditureNoFasal: number;
  agmNoticeDate: string;
  agmDate: string;
  agmNoticePeriod: string;
  agmAttendanceCount: number;
  agmQuorum: number;
  agmAgenda: string;
  agmReview: string;
  agmNoFasal: number;
  ajkMeeting: string;
  ajkReview: string;
  violation: string;
  violationReview: string;
  recommenders: any;
  recommendationReview: string;
  createdDate: string;
  modifiedDate: string;
  createdBy: string;
  modifiedBy: string;
  createdByName: string;
  modifiedByName: string;
}

export interface IDocumentChecklistInspectionReport {
  id: string;
  code: string;
  name: string;
}
