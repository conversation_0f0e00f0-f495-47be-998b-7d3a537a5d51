import { t } from "i18next";
import { useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import {
  getStateNameById,
  useQuery,
  getMalaysiaAddressList,
  ApplicationStatusEnum,
  formatDate,
} from "@/helpers";
import { CrudFilter } from "@refinedev/core";
import { FieldValues, useForm } from "react-hook-form";

import { Box, Grid, IconButton, Typography } from "@mui/material";
import {
  DataTable,
  IColumn,
  ButtonPrimary,
  FormFieldRow,
  Label,
  SelectFieldController,
  TextFieldController,
} from "@/components";
import ButtonPrevious from "@/components/button/ButtonPrevious";

import { EditIcon } from "@/components/icons";

import { IApiPaginatedResponse, IApprovalSecretaryList } from "@/types";
import ForbiddenPage from "@/pages/forbidden";
import { MaklumatTabProps } from "../maklumatSelectionTabs";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "30px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

function PertukaranSetiausahaTab({ disabled }: MaklumatTabProps) {
  if (disabled) {
    return <ForbiddenPage internal />;
  } else {
    const navigate = useNavigate();

    const {
      data: secretariesApprovalResponse,
      refetch: fetchSecretariesApprovalList,
      isLoading,
    } = useQuery<IApiPaginatedResponse<IApprovalSecretaryList[]>>({
      url: "society/new/secretaries",
      autoFetch: false,
    });

    const totalCount = secretariesApprovalResponse?.data?.data?.total ?? 0;
    const secretariesApprovalList =
      secretariesApprovalResponse?.data?.data?.data ?? [];

    const { control, setValue, watch } = useForm<FieldValues>({
      defaultValues: {
        page: 1,
        pageSize: 10,
        searchQuery: "",
        stateCode: "",
        applicationStatusCode: "",
      },
    });

    const { page, pageSize, searchQuery, stateCode, applicationStatusCode } =
      watch();

    const stateName = useMemo(() => {
      return getMalaysiaAddressList().map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
    }, []);

    const statusPermohonan = Object.entries(ApplicationStatusEnum)
      .filter(
        ([key, value]) =>
          key === "1" ||
          key === "2" ||
          key === "36" ||
          key === "3" ||
          key === "4"
      )
      .map(([key, value]) => ({
        value: key,
        label: t(value),
      }));

    const columns: IColumn[] = [
      { field: "applicantName", headerName: t("namePemohon"), align: "center" },
      { field: "societyName", headerName: t("pertubuhan"), align: "center" },
      {
        field: "societyNo",
        headerName: t("organizationNumber"),
        flex: 1,
      },
      { field: "createdDate", headerName: t("tarikhAlir"), align: "center" },
      {
        field: "transferDate",
        headerName: t("tarikhBayar"),
        align: "center",
        renderCell: ({ row }) =>
          row.transferDate ? formatDate(row.transferDate) : "-",
      },
      {
        field: "roName",
        headerName: t("keputusanCawangan_RO"),
        align: "center",
        renderCell: ({ row }) => (row.roName ? row.roName : "-"),
      },
      {
        field: "state",
        headerName: t("state"),
        align: "center",
        renderCell: ({ row }) => getStateNameById(row.stateCode),
      },
      {
        field: "actions",
        flex: 1,
        headerName: "",
        renderCell: ({ row }) => (
          <IconButton
            onClick={() =>
              navigate(`pertubuhan/pertukaran-setiausaha/${row.id}`, {
                state: { type: true },
              })
            }
            sx={{}}
          >
            <EditIcon color="var(--primary-color)" />
          </IconButton>
        ),
      },
    ];

    const getBaseFilters = (pageSize: number, pageNo: number): CrudFilter[] => [
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: pageNo, operator: "eq" },
    ];

    const buildFilters = (
      pageSize: number,
      pageNo: number,
      options: {
        searchQuery?: string;
        stateCode?: string;
        applicationStatusCode?: any;
      }
    ): CrudFilter[] => {
      const filters = getBaseFilters(pageSize, pageNo);

      if (options.searchQuery) {
        filters.push({
          field: "searchQuery",
          value: options.searchQuery,
          operator: "eq",
        });
      }

      if (options.stateCode) {
        filters.push({
          field: "stateCode",
          value: options.stateCode,
          operator: "eq",
        });
      }

      if (options.applicationStatusCode) {
        filters.push({
          field: "applicationStatusCode",
          value: options.applicationStatusCode,
          operator: "eq",
        });
      }

      return filters;
    };

    const handleChangePage = (newPage: number) => {
      const filters = buildFilters(pageSize, newPage, {
        searchQuery,
        stateCode,
        applicationStatusCode,
      });
      setValue("page", newPage);
      fetchSecretariesApprovalList({ filters });
    };

    const handleChangePageSize = (newPageSize: number) => {
      const filters = buildFilters(newPageSize, 1, {
        searchQuery,
        stateCode,
        applicationStatusCode,
      });
      setValue("pageSize", newPageSize);
      fetchSecretariesApprovalList({ filters });
    };

    const handleClearSearch = () => {
      const filters = getBaseFilters(10, 1);
      setValue("searchQuery", "");
      setValue("stateCode", "");
      setValue("applicationStatusCode", "");
      fetchSecretariesApprovalList({ filters });
    };

    const handleSearch = () => {
      const filters = buildFilters(pageSize, 1, {
        searchQuery,
        stateCode,
        applicationStatusCode,
      });

      setValue("page", 1);
      fetchSecretariesApprovalList({ filters });
    };

    useEffect(() => {
      fetchSecretariesApprovalList({
        filters: getBaseFilters(pageSize, 1),
      });
    }, []);

    return (
      <>
        <Box>
          <Box
            sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
          >
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("senaraiPertubuhan")}
              </Typography>

              <FormFieldRow
                label={<Label text={t("state")} />}
                value={
                  <SelectFieldController
                    options={stateName}
                    control={control}
                    name="stateCode"
                    placeholder="Sila pilih"
                  />
                }
              />
              <FormFieldRow
                label={<Label text={t("applicationStatusCode")} />}
                value={
                  <SelectFieldController
                    options={statusPermohonan}
                    control={control}
                    name="applicationStatusCode"
                    placeholder="Sila pilih"
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("search")} />}
                value={
                  <TextFieldController control={control} name="searchQuery" />
                }
              />

              <Grid container mt={3} spacing={2}>
                <Grid
                  item
                  xs={12}
                  sx={{
                    mt: 2,
                    display: "flex",
                    // flexDirection: isMobile ? "column" : "row",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <ButtonPrevious
                    variant="outlined"
                    sx={{
                      bgcolor: "white",
                      "&:hover": { bgcolor: "white" },
                      // width: isMobile ? "100%" : "auto",
                    }}
                    onClick={handleClearSearch}
                  >
                    {t("previous")}
                  </ButtonPrevious>
                  <ButtonPrimary
                    onClick={handleSearch}
                    variant="contained"
                    sx={{
                      // width: isMobile ? "100%" : "auto",
                      boxShadow: "none",
                    }}
                  >
                    {t("search")}
                  </ButtonPrimary>
                </Grid>
              </Grid>
            </Box>
          </Box>
          {/* ============= */}
          <Box
            sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
          >
            <Box
              sx={{
                textAlign: "center",
                color: "#fff",
                borderRadius: "13px",
                backgroundColor: "var(--primary-color)",
                py: 2,
              }}
            >
              <Typography variant="h5" gutterBottom>
                {totalCount}
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
                {t("rekodDijumpai")}
              </Typography>
            </Box>

            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mt: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("")}
              </Typography>

              <DataTable
                columns={columns}
                rows={secretariesApprovalList}
                page={page}
                isLoading={isLoading}
                rowsPerPage={pageSize}
                totalCount={totalCount}
                onPageChange={handleChangePage}
                onPageSizeChange={handleChangePageSize}
              />
            </Box>
          </Box>
        </Box>
      </>
    );
  }
}

export default PertukaranSetiausahaTab;
