import { Box, FormControl, Grid, IconButton, Typography } from "@mui/material";
import { t } from "i18next";
import { useEffect, useState } from "react";
import { API_URL } from "../../../../api";
import { useNavigate } from "react-router-dom";
import { CrudFilter, useCustom } from "@refinedev/core";
import {
  ApplicationStatusEnum,
  BranchStatusList,
  MALAYSIA,
  StatusPermohonan,
} from "../../../../helpers/enums";
import ButtonPrevious from "../../../../components/button/ButtonPrevious";
import { ButtonPrimary } from "../../../../components/button";
import { EyeIcon } from "../../../../components/icons";
import DataTable, { IColumn } from "@/components/datatable";
import Input from "@/components/input/Input";
import { useQuery } from "@/helpers";
import { MaklumatTabProps } from "../maklumatSelectionTabs";
import ForbiddenPage from "@/pages/forbidden";

function CawanganTab({ disabled }: MaklumatTabProps) {
  if (disabled) {
    return <ForbiddenPage internal />;
  } else {
    const navigate = useNavigate();

    const [displaySenaraiAjk, setDisplaySenaraiAjk] = useState<any[]>([]);
    const [total, setTotal] = useState<number>(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [page, setPage] = useState(1);

    const [formData, setFormData] = useState({
      societyName: "",
      branchName: "",
      statusPermohonan: "",
      state: "",
      carian: "",
    });

    const sectionStyle = {
      color: "var(--primary-color)",
      marginBottom: "30px",
      borderRadius: "16px",
      fontSize: "14px",
      fontWeight: "500 !important",
    };

    const statusPermohonan = Object.entries(StatusPermohonan)
      .filter(([key, value]) => Number(key) !== 0)
      .map(([key, value]) => ({
        value: key,
        label: t(value),
      }));

    const { data: addressList, isLoading: isAddressLoading } = useCustom({
      url: `${API_URL}/society/admin/address/list`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
    });
    const addressData = addressList?.data?.data || [];

    const { refetch: fetchBranch, isLoading } = useQuery({
      url: `society/admin/branch/findAllByParam`,
      filters: [
        { field: "pageNo", operator: "eq", value: page },
        { field: "pageSize", operator: "eq", value: rowsPerPage },
        { field: "searchQuery", operator: "eq", value: formData.carian },
        {
          field: "applicationStatusCode",
          operator: "eq",
          value: formData.statusPermohonan,
        },
        { field: "state", operator: "eq", value: formData.state },
        { field: "societyName", operator: "eq", value: formData.societyName },
        { field: "branchName", operator: "eq", value: formData.branchName },
      ],
      autoFetch: false,
      onSuccess: (data) => {
        const list = data?.data?.data;
        setTotal(list?.total ?? 0);
        setDisplaySenaraiAjk(list?.data ?? []);
      },
    });

    const handleClearSearch = () => {
      setFormData({
        societyName: "",
        branchName: "",
        statusPermohonan: "",
        state: "",
        carian: "",
      });
      setRowsPerPage(10);
      setPage(1);

      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: 10 },
        { field: "pageNo", operator: "eq", value: 1 },
        {
          field: "searchQuery",
          operator: "eq",
          value: null,
        },
        {
          field: "applicationStatusCode",
          operator: "eq",
          value: null,
        },
        {
          field: "state",
          operator: "eq",
          value: null,
        },
        {
          field: "societyName",
          operator: "eq",
          value: null,
        },
        {
          field: "branchName",
          operator: "eq",
          value: null,
        },
      ];
      fetchBranch({ filters });
    };

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();

      if (
        formData.societyName == "" &&
        formData.branchName == "" &&
        formData.statusPermohonan == "" &&
        formData.state == "" &&
        formData.carian == ""
      ) {
      } else {
        fetchBranch();
      }
    };

    const columns: IColumn[] = [
      {
        field: "societyName",
        headerName: t("organizationName"),
        align: "center",
        headerAlign: "center",
        renderCell: (params) => <Box>{params.row.societyName ?? "-"}</Box>,
      },
      {
        field: "name",
        headerName: t("namaCawangan"),
        align: "center",
        headerAlign: "center",
        renderCell: (params) => <Box>{params.row.name ?? "-"}</Box>,
      },
      {
        field: "applicationStatusCode",
        headerName: t("statusPermohonan"),
        cellClassName: "",
        align: "center",
        headerAlign: "center",
        renderCell: (params) => (
          <Box>
            {params.row.applicationStatusCode
              ? t(
                  ApplicationStatusEnum[params.row.applicationStatusCode] ?? "-"
                )
              : "-"}
          </Box>
        ),
      },
      {
        field: "branchNo",
        headerName: t("cawanganNumber"),
        align: "center",
        headerAlign: "center",
        renderCell: (params) => (
          <Box>
            {params.row.branchNo
              ? params.row.branchNo
              : params.row.branchApplicationNo ?? "-"}
          </Box>
        ),
      },
      {
        field: "status",
        headerName: t("statusCawangan"),
        align: "center",
        headerAlign: "center",
        renderCell: (params) => (
          <Box>
            {params.row.status
              ? t(
                  BranchStatusList.find(
                    (status) => status.value === params.row.status
                  )?.label ?? "-"
                )
              : "-"}
          </Box>
        ),
      },
      {
        field: "stateCode",
        headerName: t("state"),
        align: "center",
        headerAlign: "center",
        renderCell: (params) => (
          <Box>
            {params.row.stateCode
              ? addressData.find(
                  (id: any) => id.id === parseInt(params.row.stateCode)
                )?.name
              : "-"}
          </Box>
        ),
      },
      {
        field: "migrateStat",
        headerName: t("statusMigrasi"),
        align: "center",
        headerAlign: "center",
        renderCell: (params) => <Box>{params.row.migrateStat ?? "-"}</Box>,
      },
      {
        field: "actions",
        headerName: "",
        flex: 1,
        align: "right",
        renderCell: (params: any) => {
          const societyId = btoa(params?.row?.societyId?.toString() || "");
          const branchId = btoa(params?.row?.id?.toString() || "");
          return (
            <>
              {/* <IconButton
              sx={{ color: "black" }}
              onClick={() => navigate("maklumat/kemaskini")}
            >
              <EditIcon />
            </IconButton> */}
              <IconButton
                sx={{ color: "black" }}
                onClick={() =>
                  navigate(`maklumat/view/${societyId}/${branchId}`, {
                    state: {
                      branchId: branchId,
                      societyId: societyId,
                    },
                  })
                }
              >
                <EyeIcon />
              </IconButton>
            </>
          );
        },
      },
    ];

    useEffect(() => {
      fetchBranch();
    }, [page, rowsPerPage]);

    return (
      <>
        <Box component="form" onSubmit={handleSubmit}>
          <Box
            sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
          >
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("senaraiCawangan")}
              </Typography>
              <Input
                value={formData.societyName}
                size="small"
                label={t("organizationName")}
                onChange={(e) => {
                  setFormData((prevState) => ({
                    ...prevState,
                    societyName: e.target.value,
                  }));
                }}
              />

              <Input
                value={formData.branchName}
                size="small"
                label={t("namaCawangan")}
                onChange={(e) => {
                  setFormData((prevState) => ({
                    ...prevState,
                    branchName: e.target.value,
                  }));
                }}
              />
              <FormControl fullWidth>
                <Input
                  value={formData.statusPermohonan}
                  size="small"
                  label={t("statusPermohonan")}
                  options={statusPermohonan}
                  onChange={(e) => {
                    setFormData((prevState) => ({
                      ...prevState,
                      statusPermohonan: e.target.value,
                    }));
                  }}
                  type="select"
                />
              </FormControl>

              <FormControl fullWidth>
                <Input
                  value={formData.state}
                  size="small"
                  label={t("negeri")}
                  options={addressData
                    .filter((item: any) => item.pid === MALAYSIA)
                    .map((item: any) => ({
                      label: item.name,
                      value: item.id,
                    }))}
                  type="select"
                  onChange={(e) => {
                    setFormData((prevState) => ({
                      ...prevState,
                      state: e.target.value,
                    }));
                  }}
                />
              </FormControl>

              <Input
                value={formData.carian}
                size="small"
                label={t("Carian")}
                onChange={(e) => {
                  setFormData((prevState) => ({
                    ...prevState,
                    carian: e.target.value,
                  }));
                }}
              />

              <Grid container mt={3} spacing={2}>
                <Grid
                  item
                  xs={12}
                  sx={{
                    mt: 2,
                    display: "flex",
                    // flexDirection: isMobile ? "column" : "row",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <ButtonPrevious
                    variant="outlined"
                    sx={{
                      bgcolor: "white",
                      "&:hover": { bgcolor: "white" },
                      // width: isMobile ? "100%" : "auto",
                    }}
                    onClick={handleClearSearch}
                  >
                    {t("previous")}
                  </ButtonPrevious>
                  <ButtonPrimary
                    type="submit"
                    variant="contained"
                    sx={{
                      // width: isMobile ? "100%" : "auto",
                      boxShadow: "none",
                    }}
                  >
                    {t("search")}
                  </ButtonPrimary>
                </Grid>
              </Grid>
            </Box>
          </Box>
          {/* ============= */}
          <Box
            sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
          >
            <Box
              sx={{
                textAlign: "center",
                color: "#fff",
                borderRadius: "13px",
                backgroundColor: "var(--primary-color)",
                py: 2,
              }}
            >
              <Typography variant="h5" gutterBottom>
                {total}
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
                {t("rekodDijumpai")}
              </Typography>
            </Box>

            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mt: 2,
              }}
            >
              <DataTable
                columns={columns as any[]}
                rows={displaySenaraiAjk}
                page={page}
                rowsPerPage={rowsPerPage}
                totalCount={total}
                isLoading={isLoading}
                onPageChange={(newPage: number) => setPage(newPage)}
                onPageSizeChange={(newRowsPerPage: number) =>
                  setRowsPerPage(newRowsPerPage)
                }
                customNoDataText={t("noRecordForStatusBranch")}
              />
            </Box>
          </Box>
        </Box>
      </>
    );
  }
}

export default CawanganTab;
