import { lazy, Suspense } from "react";
import { Outlet, Route } from "react-router-dom";
import PanduanPengguna from "../pages/landing-page/panduanpengguna";
import MaklumBalas from "../pages/landing-page/maklumbalas";
import Pewartaan from "../pages/landing-page/pewartaan";
import MainLanding from "../pages/landing-page/mainLanding";
import CalendarPublicPage from "../pages/calendar/public/Main";
import CalendarPublicActivitySummary from "../pages/calendar/public/ActivitySummary";
import CalendarPublicActivityListsPage from "../pages/calendar/public/ActivityLists";
import CalendarPublicActivityDetailsPage from "../pages/calendar/public/ActivityDetails";
import MainLandingLayout from "@/pages/landing-page/mainLanding/mainLandingLayout";
import Artikel from "@/pages/artikel";
import TakwimLandingPage from "@/pages/takwim/TakwimLandingPage";
import TakwimActivityPage from "../pages/takwim/ActivityDetails";
import TakwimActivitySummaryPage from "../pages/takwim/ActivitySummary";
import TakwimActivityListsPage from "../pages/takwim/ActivityLists";
import TakwimActivityDetailsPage from "../pages/takwim/ActivityDetails";
import CreateEventPage from "@/pages/takwim/CreateEvent";
import TakwimProvider from "@/contexts/takwimProvider";
import UpdateAttendance from "@/pages/takwim/UpdateAttendance";
import TermaPenggunaan from "@/pages/landing-page/mainLanding/termaPenggunaan";
import { registerRoutes } from "@/helpers/routeDetector";
import { RouteGuard } from "@/components/RouteGuard";

import { PageLoader } from "@/components";

const DokumenJPPM = lazy(() => import("@/pages/dokumen-jppm"));

// Layout components with route protection
const MainLandingGuardedLayout = ({ children }: { children: React.ReactNode }) => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <MainLandingLayout>
      {children}
    </MainLandingLayout>
  </RouteGuard>
);

const TakwimGuardedLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <TakwimProvider>
      <TakwimLandingPage />
    </TakwimProvider>
  </RouteGuard>
);

const PublicPageGuard = ({ children }: { children: React.ReactNode }) => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    {children}
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example route registrations:
  // '/main': 'shared',                    // Main landing page - accessible to all
  // '/artikel/:slug': 'shared',           // Article pages - accessible to all
  // '/panduan-pengguna': 'shared',        // User guide - accessible to all
  // '/maklumbalas': 'shared',             // Feedback - accessible to all
  // '/pewartaan': 'shared',               // Gazette - accessible to all
  // '/takwim': 'shared',                  // Calendar - accessible to all
  // '/takwim/activity': 'shared',         // Calendar activities - accessible to all
  // '/takwim/create-event': 'internal',   // Create event - internal users only
  // '/takwim/edit-event/:eventNo': 'internal', // Edit event - internal users only
  // '/dokumen-jppm': 'shared',            // JPPM documents - accessible to all
  // Add your route registrations here
});


export const landingPage = {
  routes: (
    <>
      <Route
        path="/main"
        element={
          <MainLandingGuardedLayout>
            <MainLanding />
          </MainLandingGuardedLayout>
        }
      />
      <Route
        path="/artikel/:slug"
        element={
          <MainLandingGuardedLayout>
            <Artikel />
          </MainLandingGuardedLayout>
        }
      />
      <Route
        path="/panduan-pengguna"
        element={
          <PublicPageGuard>
            <PanduanPengguna />
          </PublicPageGuard>
        }
      />
      <Route
        path="/maklumbalas"
        element={
          <PublicPageGuard>
            <MaklumBalas />
          </PublicPageGuard>
        }
      />
      <Route
        path="/pewartaan"
        element={
          <PublicPageGuard>
            <Pewartaan />
          </PublicPageGuard>
        }
      />

      {/* Takwim routes */}
      <Route
        path="/takwim"
        element={<TakwimGuardedLayout />}
      >
        <Route index element={<TakwimActivityListsPage />} />
        <Route path="activity" element={<TakwimActivityListsPage />} />
        <Route
          path="activity/:eventNo"
          element={<TakwimActivityDetailsPage />}
        />
        <Route path="create-event" element={<CreateEventPage />} />
        <Route path="edit-event/:eventNo" element={<CreateEventPage />} />
        {/* <Route path="update-attendance" element={<UpdateAttendance />} /> */}
        <Route path="terma-penggunaan" element={<TermaPenggunaan />} />
      </Route>

        <Route
          path="/dokumen-jppm"
          element={
            <Suspense fallback={<PageLoader />}>
              <MainLandingGuardedLayout>
                <DokumenJPPM />
              </MainLandingGuardedLayout>
            </Suspense>
          }
        />

      {/* Attendance and Feedback routes */}
    </>
  ),
};

