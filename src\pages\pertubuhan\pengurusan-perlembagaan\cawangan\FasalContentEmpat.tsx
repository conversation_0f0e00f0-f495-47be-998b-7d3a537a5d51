import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  InputAdornment,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import { capitalizeWords, RegExNumbers, useQuery } from "@/helpers";
import AsalContent from "@/components/asalFasalContent";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
interface FasalContentEmpatCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: [];
  name: string;
}

export const FasalContentEmpatCawangan: React.FC<
  FasalContentEmpatCawanganProps
> = ({ activeStep, setActiveStep, clause, asalData, name }) => {
  const [asal, setAsal] = useState<any>(null);
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);

  const {
    data: religion,
    isLoading: religionListIsLoading,
    refetch: fetchReligionList,
  } = useQuery({
    url: "society/lookup/religion/allActive",
    onSuccess: (data) => {
      const response = data?.data?.data;
      //agama
      setReligionList(response);
    },
  });

  const {
    data: race,
    isLoading: raceListIsLoading,
    refetch: fetchRaceList,
  } = useQuery({
    url: "society/lookup/race/allActive",
    onSuccess: (data) => {
      const response = data?.data?.data;
      //bangsa
      setRaceList(response);
    },
  });

  const jantinaData = [
    { value: "LELAKI", label: "LELAKI" },
    { value: "PEREMPUAN", label: "PEREMPUAN" },
    { value: "LELAKI DAN PEREMPUAN", label: "LELAKI DAN PEREMPUAN" },
  ];

  const [religionList, setReligionList] = useState([]);
  const [raceList, setRaceList] = useState([]);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [kewarganegaraan, setKewarganegaraan] = useState("");
  const [umur, setUmur] = useState("");
  const [kawasan, setKawasan] = useState("");
  const [keturunan, setKeturunan] = useState("");
  const [jantina, setJantina] = useState("");
  const [agama, setAgama] = useState("");
  const [kemahiran, setKemahiran] = useState("");
  const [ahliBersekutu, setAhliBersekutu] = useState("");
  const [ahliKehormat, setAhliKehormat] = useState("");
  const [ahliSeumurHidup, setAhliSeumurHidup] = useState("");
  const [ahliRemaja, setAhliRemaja] = useState("");

  const [dataId, setDataId] = useState<number | null>(null);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const { id, clauseId } = useParams();
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName } = responseData?.data?.data;
        setNamaPertubuhan(societyName);
      },
    },
  });

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!umur) {
      errors.umur = t("fieldRequired");
    }

    if (!kawasan) {
      errors.kawasan = t("fieldRequired");
    }

    if (!keturunan) {
      errors.keturunan = t("fieldRequired");
    }

    if (!jantina) {
      errors.jantina = t("fieldRequired");
    }

    if (!agama) {
      errors.agama = t("fieldRequired");
    }

    return errors;
  };

  const clause4 = localStorage.getItem("clause4");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  const alignMembershipClause = (content: string): string => {
    const tokens = {
      "<<ahli bersekutu>>": "b. Ahli Bersekutu",
      "<<ahli kehormat>>": "c. Ahli Kehormat",
      "<<ahli seumur hidup>>": "d. Ahli Seumur Hidup",
      "<<ahli remaja>>": "e. Ahli Remaja",
    };

    let lines = content.split("\n");

    const newLines: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const trimmed = lines[i].trim().toLowerCase();
 
      const tokenEntry = Object.entries(tokens).find(([token]) =>
        trimmed.includes(token.toLowerCase())
      );

      if (tokenEntry) {
        const [token, label] = tokenEntry;
 
        const prevLine = newLines[newLines.length - 1]?.trim().toLowerCase();
        if (prevLine !== label.toLowerCase()) {
          newLines.push(`    ${label}`);
        }

        newLines.push(`       ${token}`);
      } else { 
        const isLabelLine = Object.values(tokens).some(
          (label) => trimmed === label.toLowerCase()
        );
        const nextLine = lines[i + 1]?.trim().toLowerCase() ?? "";
        const isNextToken = Object.keys(tokens).some((token) =>
          nextLine.includes(token.toLowerCase())
        );

        if (isLabelLine && isNextToken) { 
          continue;
        }

        newLines.push(lines[i]);
      }
    }

    return newLines.join("\n");
  }; 

  // const insertAlignedLabelsAboveTokens = (content: string): string => {
  //   const tokenMap: Record<string, string> = {
  //     "<<ahli bersekutu>>": "b. Ahli Bersekutu",
  //     "<<ahli kehormat>>": "c. Ahli Kehormat",
  //     "<<ahli seumur hidup>>": "d. Ahli Seumur Hidup",
  //     "<<ahli remaja>>": "e. Ahli Remaja",
  //   };
  //   const maxLength = Math.max(...Object.values(tokenMap).map((v) => v.length));

  //   for (const [token, label] of Object.entries(tokenMap)) {
  //     const paddedLabel = "    " + label.padEnd(maxLength, " ");
  //     const regex = new RegExp(`(^\\s*)(${token})`, "gmi");
  //     content = content.replace(regex, `${paddedLabel}$1$2`);
  //   }

  //   return content;
  // };

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause4Data = JSON.parse(clause4);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause4Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause4Data.societyName);
      setKewarganegaraan(clause.constitutionValues[0]?.definitionName);
      setUmur(clause.constitutionValues[1]?.definitionName);
      setKawasan(clause.constitutionValues[2]?.definitionName);
      setKeturunan(clause.constitutionValues[3]?.definitionName);
      setJantina(clause.constitutionValues[4]?.definitionName);
      setAgama(clause.constitutionValues[5]?.definitionName);
      setKemahiran(clause.constitutionValues[6]?.definitionName);
      setAhliBersekutu(clause.constitutionValues[7]?.definitionName);
      setAhliKehormat(clause.constitutionValues[8]?.definitionName);
      setAhliSeumurHidup(clause.constitutionValues[9]?.definitionName);
      setAhliRemaja(clause.constitutionValues[10]?.definitionName);
      setIsEdit(clause.edit);
      setAsal(
        asalData.find(
          (item: any) => item.clauseNo === clause.clauseNo
        ) || ""
      );
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<Kewarganegaraan>>/gi,
    `<b>${kewarganegaraan || "<<Kewarganegaraan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<umur minima>>/gi,
    `<b>${umur || "<<umur minima>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kawasan>>/gi,
    `<b>${kawasan || "<<kawasan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<keturunan>>/gi,
    `<b>${keturunan || "<<keturunan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jantina>>/gi,
    `<b>${jantina || "<<jantina>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<agama>>/gi,
    `<b>${agama || "<<agama>>"}</b>`
  );
  if (kemahiran) {
    clauseContent = clauseContent.replaceAll(
      /<<kriteria lain>>/gi,
      `<b>${kemahiran || "<<kriteria lain>>"}</b>\n`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*vii\.\s*Kriteria Keahlian Yang Lain:\s*<<kriteria lain>>\s*/gim,
      "\n        "
    );
  }
  clauseContent = alignMembershipClause(clauseContent);

  clauseContent = ahliBersekutu
    ? clauseContent.replaceAll(
        /\s*<<ahli bersekutu>>/gi,
        `${
          ahliBersekutu
            ? `<div style="width:100%;padding-left:30px;"><b>${ahliBersekutu}</b></div>`
            : `<b><<ahli bersekutu>></b>`
        }`
      )
    : clauseContent.replaceAll(
        /<<ahli bersekutu>>/gi,
        `${
          ahliBersekutu
            ? `<div style="width:100%;padding-left:30px;"><b>${ahliBersekutu}</b></div>`
            : `<b><<ahli bersekutu>></b>`
        }`
      );

  clauseContent = ahliKehormat
    ? clauseContent.replaceAll(
        /\s*<<ahli kehormat>>/gi,
        `${
          ahliKehormat
            ? `<div style="width:100%;padding-left:30px;"><b>${ahliKehormat}</b></div>`
            : `<b><<ahli kehormat>></b>`
        }`
      )
    : clauseContent.replaceAll(
        /<<ahli kehormat>>/gi,
        `${
          ahliKehormat
            ? `<div style="width:100%;padding-left:30px;"><b>${ahliKehormat}</b></div>`
            : `<b><<ahli kehormat>></b>`
        }`
      );

  clauseContent = ahliSeumurHidup
    ? clauseContent.replaceAll(
        /\s*<<ahli seumur hidup>>/gi,
        `${
          ahliSeumurHidup
            ? `<div style="width:100%;padding-left:30px;"><b>${ahliSeumurHidup}</b></div>`
            : `<b><<ahli seumur hidup>></b>`
        }`
      )
    : clauseContent.replaceAll(
        /<<ahli seumur hidup>>/gi,
        `${
          ahliSeumurHidup
            ? `<div style="width:100%;padding-left:30px;"><b>${ahliSeumurHidup}</b></div>`
            : `<b><<ahli seumur hidup>></b>`
        }`
      );

  clauseContent = ahliRemaja
    ? clauseContent.replaceAll(
        /\s*<<ahli remaja>>\s*/gi,
        `${
          ahliRemaja
            ? `<div style="width:100%;padding-left:30px;padding-bottom:10px"><b>${ahliRemaja}</b></div>`
            : `<b><<ahli remaja>></b>`
        }`
      )
    : clauseContent.replaceAll(
        /<<ahli remaja>>/gi,
        `${
          ahliRemaja
            ? `<div style="width:100%;padding-left:30px;"><b>${ahliRemaja}</b></div>`
            : `<b><<ahli remaja>></b>`
        }`
      );

  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  return (
    <>
      <AsalContent
        asalData={asal?.description || ""}
        clauseId={clauseId}
        name={asal?.clauseName}
      />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          Ahli Biasa
        </Typography>

        <Grid container spacing={2} sx={{ mt: 2 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("citizenship")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <Grid item xs={12}>
              <RadioGroup
                row
                value={kewarganegaraan}
                onChange={(e) => setKewarganegaraan(e.target.value)}
              >
                <FormControlLabel
                  disabled={isViewMode}
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("citizen")}
                  control={<Radio />}
                  label={t("citizen")}
                />
                <FormControlLabel
                  disabled={isViewMode}
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("nonCitizen")}
                  control={<Radio />}
                  label={t("nonCitizen")}
                />
                <FormControlLabel
                  disabled={isViewMode}
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("open")}
                  control={<Radio />}
                  label={t("open")}
                />
              </RadioGroup>
            </Grid>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("minimumAge")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              value={umur}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setUmur(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    umur: "",
                  }));
                } else {
                  setUmur("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    umur: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.umur}
              helperText={formErrors.umur}
              type="number"
              InputProps={{
                endAdornment: (
                  <Typography sx={{ ...labelStyle, mt: 1 }}>
                    {t("years")}
                  </Typography>
                ),
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item />
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("residentialArea")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              value={kawasan}
              onChange={(e) => {
                setKawasan(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  kawasan: "",
                }));
              }}
              error={!!formErrors.kawasan}
              helperText={formErrors.kawasan}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("descent")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required error={!!formErrors.keturunan}>
              <Select
                disabled={isViewMode || raceListIsLoading}
                size="small"
                value={keturunan}
                displayEmpty
                onChange={(e) => {
                  setKeturunan(capitalizeWords(e.target.value.toLowerCase()));
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    keturunan: "",
                  }));
                }}
                renderValue={(selected) => {
                  if (selected === "") {
                    return t("selectPlaceholder");
                  }

                  return selected;
                }}
              >
                {raceList.map((option: any) => (
                  <MenuItem
                    key={option.id}
                    value={capitalizeWords(option.name.toLowerCase())}
                  >
                    {capitalizeWords(option.name.toLowerCase())}
                  </MenuItem>
                ))}
              </Select>
              {formErrors.keturunan && (
                <FormHelperText>{formErrors.keturunan}</FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("gender")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required error={!!formErrors.jantina}>
              <Select
                disabled={isViewMode}
                size="small"
                value={jantina}
                displayEmpty
                onChange={(e) => {
                  setJantina(capitalizeWords(e.target.value.toLowerCase()));
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jantina: "",
                  }));
                }}
                renderValue={(selected) => {
                  if (selected === "") {
                    return t("selectPlaceholder");
                  }

                  return selected;
                }}
              >
                {jantinaData.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
              {formErrors.jantina && (
                <FormHelperText>{formErrors.jantina}</FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("religion")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required error={!!formErrors.agama}>
              <Select
                disabled={isViewMode || religionListIsLoading}
                size="small"
                value={agama}
                displayEmpty
                onChange={(e) => {
                  setAgama(capitalizeWords(e.target.value.toLowerCase()));
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    agama: "",
                  }));
                }}
                renderValue={(selected) => {
                  if (selected === "") {
                    return t("selectPlaceholder");
                  }

                  return selected;
                }}
              >
                {religionList.map((option: any) => (
                  <MenuItem
                    key={option.id}
                    value={capitalizeWords(option.name.toLowerCase())}
                  >
                    {capitalizeWords(option.name.toLowerCase())}
                  </MenuItem>
                ))}
              </Select>
              {formErrors.agama && (
                <FormHelperText>{formErrors.agama}</FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("otherSkillsCriteria")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={kemahiran}
              onChange={(e) => setKemahiran(e.target.value)}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("associateMember")}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <RadioGroup
              row
              value={ahliBersekutu}
              onChange={(e) => setAhliBersekutu(e.target.value)}
            >
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  disabled={isViewMode}
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("associateMemberFirstOption")}
                  control={<Radio />}
                  label={t("associateMemberFirstOption")}
                />
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  disabled={isViewMode}
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("none")}
                  control={<Radio />}
                  label={t("none")}
                />
              </Grid>
            </RadioGroup>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("honoraryMember")}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <RadioGroup
              row
              value={ahliKehormat}
              onChange={(e) => setAhliKehormat(e.target.value)}
            >
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  disabled={isViewMode}
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("honoraryMemberFirstOption")}
                  control={<Radio />}
                  label={t("honoraryMemberFirstOption")}
                />
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  disabled={isViewMode}
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("none")}
                  control={<Radio />}
                  label={t("none")}
                />
              </Grid>
            </RadioGroup>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("lifetimeMember")}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <RadioGroup
              row
              value={ahliSeumurHidup}
              onChange={(e) => setAhliSeumurHidup(e.target.value)}
            >
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  disabled={isViewMode}
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("lifetimeMemberFirstOption")}
                  control={<Radio />}
                  label={t("lifetimeMemberFirstOption")}
                />
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  disabled={isViewMode}
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("none")}
                  control={<Radio />}
                  label={t("none")}
                />
              </Grid>
            </RadioGroup>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("youthMember")}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <RadioGroup
              row
              value={ahliRemaja}
              onChange={(e) => setAhliRemaja(e.target.value)}
            >
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  disabled={isViewMode}
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("youthMemberFirstOption")}
                  control={<Radio />}
                  label={t("youthMemberFirstOption")}
                />
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  disabled={isViewMode}
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("none")}
                  control={<Radio />}
                  label={t("none")}
                />
              </Grid>
            </RadioGroup>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              const errors = validateForm();
              if (Object.keys(errors).length > 0) {
                setFormErrors(errors);
                return;
              }

              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: kewarganegaraan,
                    titleName: "Kewarganegaraan",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: umur,
                    titleName: "Umur Minimum",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: kawasan,
                    titleName: "Kawasan / Negeri Tempat Tinggal Ahli",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: keturunan,
                    titleName: "Keturunan",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jantina,
                    titleName: "Jantina",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: agama,
                    titleName: "Agama",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: kemahiran,
                    titleName: "Kriteria Kemahiran Lain",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: ahliBersekutu,
                    titleName: "Ahli Bersekutu",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: ahliKehormat,
                    titleName: "Ahli Kehormatan",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: ahliSeumurHidup,
                    titleName: "Ahli Seumur Hidup",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: ahliRemaja,
                    titleName: "Ahli Remaja",
                  },
                ],
                clause: "clause4",
                clauseCount: 4,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentEmpatCawangan;
