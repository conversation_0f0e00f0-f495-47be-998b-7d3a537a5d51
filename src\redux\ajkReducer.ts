import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "./store";

// Define a type for the slice state
export interface AJKState {
  no?: number;
  designationCode: string;
  name: string;
  email: string;
  employerCity: string;
}

interface State {
  ajkState: AJKState[];
  ajkPaparanData: {};
  allAjkList: [];
  allBranchAjkList: [];
}

// Define the initial state using that type
const initialState: State = {
  ajkState: [],
  ajkPaparanData: {},
  allAjkList: [],
  allBranchAjkList: []
};

export const ajkSlice = createSlice({
  name: "ajk",
  initialState,
  reducers: {
    addAjk: (state, action: PayloadAction<AJKState>) => {
      const nextNo = state.ajkState.length + 1;

      const newAjk = {
        ...action.payload,
        no: nextNo,
      };
      state.ajkState.push(newAjk);
    },

    removeAjk: (state, action: PayloadAction<string | number>) => {
      state.ajkState = state.ajkState.filter(
        (ajk) => ajk.no !== action.payload
      );
    },
    setAJK: (state, action: PayloadAction<AJKState[]>) => {
      state.ajkState = action.payload?.map((item, i) => {
        return {
          ...item,
          no: i + 1,
        };
      });
    },
    setAllAjkList: (state, action) => {
      state.allAjkList = action.payload
    },
    setAjkPaparanData: (state, action) => {
      state.ajkPaparanData = action.payload
    },
    setAllBranchAjkList: (state, action) => {
      state.allBranchAjkList = action.payload
    }, 
  },
});

export const mapAjk = (timbalanPengerusi = 0, naibPengerusi = 0,
  penolongSetiausaha = 0, penolongBendahari = 0, ajk = 0, pengerusi = 1,
  setiausahaAgung = 1, bendahariAgung = 1) => {
  let j = 1;
  const arr: AJKState[] = [];
  for (let i = 0; i < pengerusi; i++) {
    const a: AJKState = {
      no: j,
      designationCode: "pengerusi".toUpperCase(),
      name: "",
      email: "",
      employerCity: ""
    }
    j++;
  }
  for (let i = 0; i < setiausahaAgung; i++) {
    const a: AJKState = {
      no: j,
      designationCode: "setiausahaAgung".toUpperCase(),
      name: "",
      email: "",
      employerCity: ""
    }
    j++;
  }
  for (let i = 0; i < bendahariAgung; i++) {
    const a: AJKState = {
      no: j,
      designationCode: "bendahariAgung".toUpperCase(),
      name: "",
      email: "",
      employerCity: ""
    }
    j++;
  }
  for (let i = 0; i < timbalanPengerusi; i++) {
    const a: AJKState = {
      no: j,
      designationCode: "timbalanPengerusi".toUpperCase(),
      name: "",
      email: "",
      employerCity: ""
    }
    j++;
  }
  for (let i = 0; i < naibPengerusi; i++) {
    const a: AJKState = {
      no: j,
      designationCode: "naibPengerusi".toUpperCase(),
      name: "",
      email: "",
      employerCity: ""
    }
    j++;
  }
  for (let i = 0; i < penolongSetiausaha; i++) {
    const a: AJKState = {
      no: j,
      designationCode: "penolongSetiausaha".toUpperCase(),
      name: "",
      email: "",
      employerCity: ""
    }
    j++;
  }
  for (let i = 0; i < penolongBendahari; i++) {
    const a: AJKState = {
      no: j,
      designationCode: "penolongBendahari".toUpperCase(),
      name: "",
      email: "",
      employerCity: ""
    }
    j++;
  }
  for (let i = 0; i < ajk; i++) {
    const a: AJKState = {
      no: j,
      designationCode: "ajk".toUpperCase(),
      name: "",
      email: "",
      employerCity: ""
    }
    j++;
  }
}

export const { addAjk, removeAjk, setAJK, setAjkPaparanData, setAllAjkList, setAllBranchAjkList } = ajkSlice.actions;

export const allAjkListData = (state: RootState) => state.ajk.allAjkList;
export const selectAJK = (state: RootState) => state.ajk.ajkState;
export const AJKpaparan = (state: RootState) => state.ajk.ajkPaparanData;

export const allBranchAjkListData = (state: RootState) => state.ajk.allBranchAjkList;

export default ajkSlice.reducer;
