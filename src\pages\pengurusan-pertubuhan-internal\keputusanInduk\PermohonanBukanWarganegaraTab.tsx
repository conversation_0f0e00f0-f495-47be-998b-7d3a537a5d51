import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  capitalizeWords,
  formatDate,
  getLocalStorage,
} from "../../../helpers/utils";
import { NEW_PermissionNames, pageAccessEnum } from "../../../helpers/enums";

import {
  Box,
  Grid,
  Typography,
  IconButton,
  TextField,
  Select,
  MenuItem,
  debounce,
  FormHelperText,
  FormControl,
} from "@mui/material";
import DataTable, { IColumn } from "../../../components/datatable";

import { EditIcon } from "../../../components/icons";
import { CrudFilter } from "@refinedev/core";
import { useCallback, useEffect, useState } from "react";
import useQuery from "@/helpers/hooks/useQuery";
import AuthHelper from "@/helpers/authHelper";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
  "& span": {
    color: "red",
  },
};

type Props = {
  number?: number;
};

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "30px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const PermohonanBukanWarganegaraTab = ({ number }: Props) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState({
    organizationName: "",
    subOrganizationCategory: "",
    organizationCategory: "",
  });
  const hasReadPermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_INDUK.children.PERMOHONAN_BUKAN_WARGANEGARA.label,
    pageAccessEnum.Read
  );

  const request = debounce((value) => {
    fetchPendingResult({
      filters: [
        {
          field: "pageSize",
          value: pageSize,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: page,
          operator: "eq",
        },
        {
          field: "isQuery",
          operator: "eq",
          value: "0",
        },
        {
          field: "categoryCode",
          value: formData.organizationCategory || "",
          operator: "eq",
        },
        {
          field: "subCategoryCode",
          value: formData.subOrganizationCategory || "",
          operator: "eq",
        },
        {
          field: "societyName",
          value: value,
          operator: "eq",
        },
      ],
    });
  }, 800);

  const debouceRequest = useCallback((value: any) => request(value), []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      organizationName: value,
    }));
    debouceRequest(value);
    setFormErrors((prev) => ({ ...prev, organizationName: "" }));
  };

  const categories = getLocalStorage("category_list", []);
  const mainCategories = categories.filter((cat: any) => cat.level === 1);
  const subCategories = categories.filter((cat: any) => cat.level === 2);
  const subCategoriesOptions = subCategories
    ?.filter((items: any) => {
      return items.pid === parseInt(formData.organizationCategory);
    })
    .map((category: any) => ({
      value: category.id.toString(),
      label: category.categoryNameEn,
    }));
  const columns: IColumn[] = [
    {
      field: "societyNo",
      headerName: "No. PPM/NSID",
      flex: 1,
      align: "center",
      renderCell({ row }) {
        return row?.societyNo ?? row?.societyApplicationNo ?? "-";
      },
    },
    {
      field: "name",
      headerName: t("ajkName"),
      flex: 1,
      align: "center",
    },
    {
      field: "roName",
      headerName: "RO",
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.roName ? row?.roName : "-";
      },
    },
    {
      field: "transferDate",
      headerName: t("tarikhAlir"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.transferDate
          ? formatDate(row.transferDate, "DD-MM-YYYY", {
              parseFormat: "DD/MM/YYYY",
            })
          : "-";
      },
    },
    {
      field: "stateName",
      headerName: t("state"),
      flex: 1,
      align: "center",
    },
    {
      field: "createdDate",
      headerName: t("tarikhPermohonan"),
      flex: 1,
      align: "center",
      renderCell({ row }) {
        return formatDate(row?.createdDate, "DD-MM-YYYY", {
          parseFormat: "DD/MM/YYYY",
        });
      },
    },
    {
      field: "actions",
      headerName: t("action"),
      align: "center",
      renderCell: ({ row }: any) => (
        <IconButton
          disabled={!hasReadPermission}
          onClick={() =>
            navigate(
              `/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/permohonan-bukan-warganegara/${row.id}`
            )
          }
        >
          <EditIcon
            sx={{
              color: hasReadPermission
                ? "var(--primary-color)"
                : "var(--text-grey-disabled)",
              width: "1rem",
              height: "1rem",
            }}
          />
        </IconButton>
      ),
    },
  ];

  const {
    data: searchResult,
    isLoading: isSearchLoading,
    refetch: fetchPendingResult,
  } = useQuery({
    url: `society/roDecision/getAllPending/society/nonCitizen`,
    autoFetch: false,
  });

  let dataRo = [
    // {
    //   id: 1,
    //   societyName: "test",
    //   applicationType: "test",
    //   ro: "test",
    //   tarikhAlir: "test",
    //   stateName: "test",
    //   tarikhPermohonan: "test",
    //   societyNo: "test",
    // },
  ];
  if (!isSearchLoading) {
    dataRo = searchResult?.data?.data?.data || [];
  }

  const handleChangePage = (newPage: number) => {
    const filters: CrudFilter[] = [
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: newPage, operator: "eq" },
      { field: "isQuery", value: "0", operator: "eq" },
      {
        field: "categoryCode",
        value: formData.organizationCategory || "",
        operator: "eq",
      },
      {
        field: "subCategoryCode",
        value: formData.subOrganizationCategory || "",
        operator: "eq",
      },
      {
        field: "societyName",
        value: formData.organizationName || "",
        operator: "eq",
      },
    ];
    setPage(newPage);
    fetchPendingResult({ filters });
  };

  useEffect(() => {
    fetchPendingResult({
      filters: [
        {
          field: "pageSize",
          value: pageSize,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: page,
          operator: "eq",
        },
        {
          field: "isQuery",
          operator: "eq",
          value: "0",
        },
        {
          field: "categoryCode",
          value: formData.organizationCategory || "",
          operator: "eq",
        },
        {
          field: "subCategoryCode",
          value: formData.subOrganizationCategory || "",
          operator: "eq",
        },
        {
          field: "societyName",
          value: formData.organizationName || "",
          operator: "eq",
        },
      ],
    });
  }, [
    formData.organizationCategory,
    formData.subOrganizationCategory,
    pageSize,
  ]);

  const totalList = searchResult?.data?.data?.total ?? 0;

  return (
    <Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("noncitizenRegList")}
          </Typography>
          <Grid container spacing={2}>
            {/* organization category */}
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("organizationCategory")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FormControl
                fullWidth
                required
                error={!!formErrors.organizationCategory}
              >
                <Select
                  size="small"
                  value={formData.organizationCategory}
                  displayEmpty
                  onChange={(e) => {
                    setFormData((prevState) => ({
                      ...prevState,
                      organizationCategory: e.target.value,
                    }));

                    // Reset sub-category when main category changes
                    setFormData((prevState) => ({
                      ...prevState,
                      subOrganizationCategory: "",
                    }));

                    setFormErrors((prev) => ({
                      ...prev,
                      organizationCategory: "",
                    }));
                  }}
                  renderValue={(selected) =>
                    selected
                      ? mainCategories.find(
                          (cat: any) => cat.id === parseInt(selected)
                        )?.categoryNameEn
                      : t("selectPlaceholder")
                  }
                >
                  <MenuItem value="" disabled>
                    {t("selectPlaceholder")}
                  </MenuItem>
                  {mainCategories.map((category: any) => (
                    <MenuItem key={category.id} value={category.id.toString()}>
                      {category.categoryNameEn}
                    </MenuItem>
                  ))}
                </Select>
                {formErrors.organizationCategory && (
                  <FormHelperText>
                    {formErrors.organizationCategory}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            {/* sub organization category */}
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("organizationSubCategory2")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FormControl
                fullWidth
                required
                error={!!formErrors.subOrganizationCategory}
              >
                <Select
                  size="small"
                  value={formData.subOrganizationCategory}
                  displayEmpty
                  sx={{ 
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#DADADA !important",
                    },
                    backgroundColor:
                      subCategoriesOptions.length === 0 ? "#66666626" : "#FFF",
                  }}
                  onChange={(e) => {
                    setFormData((prevState) => ({
                      ...prevState,
                      subOrganizationCategory: e.target.value,
                    }));
                    setFormErrors((prev) => ({
                      ...prev,
                      subOrganizationCategory: "",
                    }));
                  }}
                  renderValue={(selected) => {
                    if (!selected) {
                      return (
                        <span style={{ color: "#aaa" }}>
                          {t("pleaseSelect")}
                        </span>
                      );
                    }
                    const selectedOption = subCategoriesOptions.find(
                      (item: any) => item.value === selected
                    );
                    return selectedOption ? selectedOption.label : selected;
                  }}
                  disabled={!formData.organizationCategory}
                >
                  <MenuItem value="" disabled>
                    {t("selectPlaceholder")}
                  </MenuItem>
                  {subCategoriesOptions.map((item: any) => (
                    <MenuItem key={item.value} value={item.value}>
                      {item.label}
                    </MenuItem>
                  ))}
                </Select>
                {formErrors.subOrganizationCategory && (
                  <FormHelperText>
                    {formErrors.subOrganizationCategory}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            {/* organization name */}
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("organizationName")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                fullWidth
                required
                name="organizationName"
                value={formData.organizationName}
                onChange={handleInputChange}
                error={!!formErrors.organizationName}
                helperText={formErrors.organizationName}
              />
            </Grid>
          </Grid>
        </Box>
      </Box>
      {/* ============= */}
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            textAlign: "center",
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",
            py: 2,
            mb: 2,
          }}
        >
          <Typography variant="h5" gutterBottom>
            {totalList}
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
            {t("permohonanMenungguKeputusan")}
          </Typography>
        </Box>

        <Box
          sx={{
            width: "100%",
            border: "0.5px solid #DADADA",
            borderRadius: "10px",
            padding: "22px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="37px"
          >
            {capitalizeWords(t("noncitizenRegList"))}
          </Typography>

          <DataTable
            columns={columns}
            rows={dataRo}
            page={page}
            rowsPerPage={pageSize}
            totalCount={totalList}
            isLoading={isSearchLoading}
            onPageChange={handleChangePage}
            onPageSizeChange={(sizePerPage) => {
              setPage(1);
              setPageSize(sizePerPage);
            }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default PermohonanBukanWarganegaraTab;
