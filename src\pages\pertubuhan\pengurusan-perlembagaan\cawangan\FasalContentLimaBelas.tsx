import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import AsalContent from "@/components/asalFasalContent";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
interface FasalContentLimaBelasCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: [];
  name: string;
}

export const FasalContentLimaBelasCawangan: React.FC<
  FasalContentLimaBelasCawanganProps
> = ({ activeStep, setActiveStep, clause, asalData, name }) => {
  const [asal, setAsal] = useState<any>(null);
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [lainlain, setLainLain] = useState("");
  const [dataId, setDataId] = useState<number | null>(null);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const { id, clauseId } = useParams();
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName } = responseData?.data?.data;
        setNamaPertubuhan(societyName);
      },
    },
  });

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!pemilihanAjk) {
      errors.pemilihanAjk = t("fieldRequired");
    }

    return errors;
  };

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause = JSON.parse(clause15);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause.societyName);
      if((clause.constitutionValues[0]?.definitionName && clause.constitutionValues[0]?.definitionName !== t("annual") && clause.constitutionValues[0]?.definitionName !== t("biennial"))){
        setPemilihanAjk(t("lainLain"));
        setLainLain(clause.constitutionValues[0]?.definitionName)
      }else{
        setPemilihanAjk(clause.constitutionValues[0]?.definitionName);
      }
      setIsEdit(clause.edit);
      setAsal(
        asalData.find((item: any) => item.clauseNo === clause.clauseNo
        ) || ""
      );
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);
  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${lainlain ? lainlain : pemilihanAjk || "<<jenis mesyuarat agung>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  return (
    <>
      <AsalContent
        asalData={asal?.description || ""}
        clauseId={clauseId}
        name={name}
      />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("generalMeeting")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("jenisMesyuaratAgung")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required error={!!formErrors.pemilihanAjk}>
              <Select
                size="small"
                value={pemilihanAjk}
                displayEmpty
                onChange={(e) => {
                  setPemilihanAjk(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    pemilihanAjk: "",
                  }));
                  if ((e.target.value as string) == t("annual")) {
                    setLainLain("")
                  } else if ((e.target.value as string) == t("biennial")) {
                    setLainLain("")
                  }
                }}
              >
                <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
                <MenuItem value={t("lainLain")}>{t("lainLain")}</MenuItem>
              </Select>
              {formErrors.pemilihanAjk && (
                <FormHelperText>{formErrors.pemilihanAjk}</FormHelperText>
              )}
            </FormControl>
          </Grid>
        </Grid>


        {pemilihanAjk === t("lainLain") ?
          <Grid container spacing={2} sx={{ mt: 0 }}>
              <Grid item xs={12} md={4}></Grid>
              <Grid item xs={12} md={8}>
                <TextField
                  type="text"
                  size="small"
                  placeholder="Yearly, Biannually, Tri-tahunan"
                  fullWidth
                  required
                  value={lainlain}
                  onChange={(e) => {
                    setLainLain(e.target.value as string);
                  }}
                />
              </Grid>
            </Grid>: null
          }
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}
      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              const errors = validateForm();
              if (Object.keys(errors).length > 0) {
                setFormErrors(errors);
                return;
              }

              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: lainlain ? lainlain : pemilihanAjk ,
                    titleName: "Jenis Mesyuarat Agung",
                  },
                ],
                clause: "clause15",
                clauseCount: 15,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentLimaBelasCawangan;
