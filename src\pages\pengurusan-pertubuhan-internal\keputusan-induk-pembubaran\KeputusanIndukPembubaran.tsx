import React, { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useGetIdentity } from "@refinedev/core";
import { TFunction } from "i18next";
import { FieldValues, useForm } from "react-hook-form";
import { useParams } from "react-router-dom";
import useMutation from "../../../helpers/hooks/useMutation";
import { useKeputusanIndukPembubaranContext } from "./KeputusanIndukPembubaranProvider";
import { filterEmptyValuesOnObject } from "../../../helpers/utils";
import {
  DecisionOptionsCode,
  ApplicationStatusEnum,
  DecisionRoOptionsCode,
  pageAccessEnum,
  NEW_PermissionNames,
} from "../../../helpers/enums";

import { Box, Grid, Theme, Typography, useMediaQuery } from "@mui/material";
import MaklumatAmSection from "./views/MaklumatAmSection";
import MaklumatMesyuaratSection from "./views/MaklumatMesyuaratSection";
import MaklumatKewanganSection from "./views/MaklumatKewanganSection";
import MaklumatAsetSection from "./views/MaklumatAsetSection";
import MaklumatPembubaranPertubuhanSection from "./views/MaklumatPembubaranPertubuhanSection";
import DokumenLainSection from "./views/DokumenLainSection";
import SuratPembubaranSection from "./views/SuratPembubaranSection";
import AccordionComp from "../View/Accordion";
import TextFieldController from "../../../components/input/TextFieldController";
import SelectFieldController from "../../../components/input/select/SelectFieldController";
import CustomSkeleton from "../../../components/custom-skeleton";
import {
  DialogConfirmation,
  FormFieldRow,
  Label,
  DisabledTextField,
} from "@/components";
import { ButtonOutline, ButtonPrimary } from "../../../components/button";

import { IUser } from "@/types";
import AuthHelper from "@/helpers/authHelper";

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

const labelStyle = {
  fontSize: "16px",
  color: "#666666",
  fontWeight: "400 !important",
};

type ReadStatusType = {
  [key: number]: boolean;
};

function KeputusanIndukPembubaranComp() {
  const { id, branchId } = useParams();
  const { t, i18n } = useTranslation();
  const { data: user } = useGetIdentity<IUser>();
  const navigate = useNavigate();
  const {
    liquidationDetailData,
    societyDetailData,
    isLoadingLiquidationDetail,
    roList,
  } = useKeputusanIndukPembubaranContext();

  const { ro, roDecisionCode, roDecisionNote } = liquidationDetailData || {};
  const isUserRo = Number(user?.id) === Number(ro);

  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const isMyLanguage = i18n.language === "my";
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const isPerlembagaanFaedahBersama =
    societyDetailData?.constitutionType === "Perlembagaan Faedah Bersama";

  //

  const roListOptions =
    roList.map((item: any) => ({
      value: Number(item.id),
      label: item.name,
    })) || [];

  const { fetch: updateRO, isLoading: isUpdatingRO } = useMutation({
    url: "society/roDecision/updateRo",
    method: "patch",
  });

  const onSubmitRoAction = (data: FieldValues) => {
    updateRO(data);
  };

  const methodsRoAction = useForm<FieldValues>({
    defaultValues: {
      liquidationId: id,
      roId: "",
      noteRo: "",
      roApprovalType: branchId ? "BRANCH_LIQUIDATION" : "SOCIETY_LIQUIDATION",
    },
  });

  const {
    handleSubmit: handleSubmitRoAction,
    control: controlRoAction,
    setValue: setValueRoAction,
    getValues: getValuesRoAction,
    watch: watchRoAction,
  } = methodsRoAction;

  useEffect(() => {
    if (liquidationDetailData) {
      setValueRoAction("roId", Number(liquidationDetailData.ro) || "");
      setValueRoAction("noteRo", liquidationDetailData.noteRo ?? "");
    }
  }, [liquidationDetailData]);

  const { fetch: createApproval, isLoading } = useMutation({
    url: "society/roDecision/updateApprovalStatus",
    method: "patch",
    onSuccess: (res) => {
      const responseCode = res?.data?.code;
      if (responseCode === 200) {
        setIsSuccess(true);
        setTimeout(() => {
          navigate("../keputusan-induk?tab=pembubaran", { replace: true });
        }, 2000);
      }
    },
  });

  const { control, handleSubmit, getValues, watch, setValue } =
    useForm<FieldValues>({
      defaultValues: {
        liquidationId: id,
        applicationStatusCode: "",
        societyId: "",
        societyNo: "",
        note: "",
        roApprovalType: branchId ? "BRANCH_LIQUIDATION" : "SOCIETY_LIQUIDATION",
      },
    });

  const applicationStatusCode = watch("applicationStatusCode");
  const requiredNote = isUserRo && applicationStatusCode === 19; // decision tolak for RO

  const getDecisionOptions = (
    t: TFunction<"translation", undefined>,
    isUserRo: boolean
  ) => {
    const options = DecisionOptionsCode(t).filter(
      (decision) => decision.value !== 36
    ); // remove kuiri from options
    const roOptions = DecisionRoOptionsCode(t);

    if (isUserRo) {
      return roOptions;
    }

    return options;
  };

  const decisionOptions = useMemo(
    () => getDecisionOptions(t, isUserRo),
    [isUserRo]
  );

  const decisionLabel = useMemo(
    () =>
      decisionOptions.find(
        (item) => item.value === Number(applicationStatusCode)
      )?.label || "",
    [applicationStatusCode, decisionOptions]
  );

  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);

  const [readStatus, setReadStatus] = useState<ReadStatusType>({});

  const sectionItems = [
    {
      subTitle: t("generalInformation"),
      component: <MaklumatAmSection />,
    },
    ...(liquidationDetailData?.liquidationDocumentType === 1
      ? [
          {
            subTitle: t("meetingInformation"),
            component: <MaklumatMesyuaratSection />,
          },
        ]
      : [
          {
            subTitle: isMyLanguage ? "Dokumen Lain" : "Other Document",
            component: <DokumenLainSection />,
          },
        ]),
    ...(isPerlembagaanFaedahBersama
      ? [
          {
            subTitle: "Surat Pembubaran",
            component: <SuratPembubaranSection />,
          },
        ]
      : []),
    {
      subTitle: t("financialInformation"),
      component: <MaklumatKewanganSection />,
    },
    {
      subTitle: t("maklumatAsset"),
      component: <MaklumatAsetSection />,
    },

    {
      subTitle: t("informationOnDissolutionOrganizations"),
      component: <MaklumatPembubaranPertubuhanSection />,
    },
  ];

  const handleChangeCurrentExpandSection =
    (item: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);

      if (isExpanded) {
        setReadStatus((prevState) => {
          const updatedStatus = sectionItems.reduce((acc, _, i) => {
            if (i + 1 <= item) {
              acc[i + 1] = true;
            } else {
              acc[i + 1] = !!prevState[i + 1] || false;
            }
            return acc;
          }, {} as Record<number, boolean>);
          return updatedStatus;
        });
      }
    };

  const onSubmit = () => setIsDialogOpen(true);
  const handleDialogClose = () => setIsDialogOpen(false);
  const handleFormSubmit = () => {
    const payload: Record<string, any> = {
      ...getValues(),
      branchId: branchId ?? undefined,
    };

    const { note, applicationStatusCode } = payload;

    switch (applicationStatusCode) {
      case 4:
        payload.rejectReason = note;
        delete payload.note;
        break;
      default:
        break;
    }

    const filterPayload = filterEmptyValuesOnObject(payload);

    createApproval(filterPayload);
  };

  const handleGoBack = () => {
    const url = branchId
      ? "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pembubaran"
      : "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk?tab=pembubaran";

    navigate(url);
  };

  useEffect(() => {
    if (!liquidationDetailData) return;

    const { reviews, societyId, societyNo } = liquidationDetailData;

    const lastReview =
      reviews
        ?.filter(
          (review: any) =>
            review.isPPP &&
            (review.decision === "Lulus" || review.decision === "Tolak")
        )
        ?.at(-1) ?? null;

    setValue("societyId", societyId);
    setValue("societyNo", societyNo);

    if (lastReview) {
      const { applicationStatusCode, note } = lastReview;

      setValue("applicationStatusCode", applicationStatusCode);
      setValue("note", note);
      setIsFormDisabled(true);
    } else {
      setIsFormDisabled;
    }
  }, [liquidationDetailData]);

  if (isLoadingLiquidationDetail || !liquidationDetailData)
    return <CustomSkeleton />;

  const hasUpdatePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_INDUK.children.PEMBUBARAN.label,
    pageAccessEnum.Update
  );

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            <Typography
              fontWeight="500 !important"
              fontSize="14px"
              lineHeight="21px"
            >
              {isLoadingLiquidationDetail ? (
                "Loading..."
              ) : (
                <>
                  {societyDetailData?.societyName} <br />
                  {societyDetailData?.societyNo}
                </>
              )}
            </Typography>
          </Box>
        </Box>

        <Box sx={{ mt: 4 }}>
          {sectionItems.map((item, index) => {
            return (
              <AccordionComp
                key={index}
                subTitle={item.subTitle}
                currentIndex={index + 1}
                currentExpand={currentExpandSection}
                readStatus={readStatus}
                onChangeFunc={handleChangeCurrentExpandSection}
              >
                {item.component}
              </AccordionComp>
            );
          })}
        </Box>

        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <form
            style={{ marginBottom: "40px" }}
            onSubmit={handleSubmitRoAction(onSubmitRoAction)}
          >
            <Box
              sx={{
                p: 3,
                mb: 3,
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography variant="h6" component="h2" sx={subTitleStyle}>
                {t("ROAction")}
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("responsibleRO")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <SelectFieldController
                    name="roId"
                    control={controlRoAction}
                    options={roListOptions}
                    disabled={!hasUpdatePermission || isUserRo}
                    required
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("remarks")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={controlRoAction}
                    disabled={!hasUpdatePermission || isUserRo}
                    name="noteRo"
                    multiline
                    sx={{
                      minHeight: "126px",
                    }}
                    sxInput={{
                      minHeight: "126px",
                    }}
                  />
                </Grid>
              </Grid>
            </Box>

            {!isUserRo && (
              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrimary
                  type="submit"
                  disabled={!hasUpdatePermission || isUpdatingRO}
                >
                  {t("update")}
                </ButtonPrimary>
              </Grid>
            )}
          </form>

          {roDecisionCode && (
            <Box
              sx={{
                p: 3,
                mb: 3,
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography variant="h6" component="h2" sx={subTitleStyle}>
                Syor
              </Typography>

              <FormFieldRow
                label={<Label text="Syor Ro" />}
                value={
                  <DisabledTextField
                    value={`Pembubaran ${ApplicationStatusEnum[roDecisionCode]}`}
                  />
                }
              />

              <FormFieldRow
                align="flex-start"
                label={<Label text="Catatan Ro" />}
                value={
                  <DisabledTextField
                    multiline
                    row={3}
                    value={roDecisionNote ?? "-"}
                  />
                }
              />
            </Box>
          )}

          <form onSubmit={handleSubmit(onSubmit)}>
            <Box
              sx={{
                pl: 2,
                p: 3,
                mt: 1,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
              }}
            >
              <Box
                sx={{
                  mb: 3,
                }}
              >
                <Typography variant="h6" component="h2" sx={subTitleStyle}>
                  {t("keputusan")}
                </Typography>
              </Box>
              <Grid container>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("statusPermohonan")}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                  <SelectFieldController
                    control={control}
                    name="applicationStatusCode"
                    options={decisionOptions}
                    disabled={!hasUpdatePermission || isFormDisabled}
                    sx={{
                      background: isFormDisabled
                        ? "rgba(218, 218, 218, 0.5)"
                        : "",
                    }}
                    required
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("remarks")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={control}
                    name="note"
                    multiline
                    disabled={!hasUpdatePermission || isFormDisabled}
                    sx={{
                      minHeight: "92px",
                      background: isFormDisabled
                        ? "rgba(218, 218, 218, 0.5)"
                        : "",
                    }}
                    sxInput={{
                      minHeight: "92px",
                    }}
                    required={requiredNote}
                  />
                </Grid>
              </Grid>
            </Box>
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                marginTop: "34px",
                gap: "10px",
              }}
            >
              <ButtonOutline onClick={handleGoBack}>{t("back")}</ButtonOutline>

              <ButtonPrimary
                type="submit"
                disabled={!hasUpdatePermission || isFormDisabled}
              >
                {t("update")}
              </ButtonPrimary>
            </Box>
          </form>
        </Box>
      </Box>

      <DialogConfirmation
        open={isDialogOpen}
        onClose={handleDialogClose}
        onAction={handleFormSubmit}
        isMutating={isLoading}
        onConfirmationText={t("sureToSubmit")}
        onSuccessText={t("applicationSuccessSubmited")}
        isSuccess={isSuccess}
        decisionLabel={decisionLabel}
      />
    </>
  );
}

export default KeputusanIndukPembubaranComp;
