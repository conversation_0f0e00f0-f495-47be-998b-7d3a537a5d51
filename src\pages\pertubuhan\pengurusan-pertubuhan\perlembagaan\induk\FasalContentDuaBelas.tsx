import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { useSelector } from "react-redux";
import { API_URL } from "../../../../../api";
import { FasalContentProps } from "../Fasal";
import { RegExNumbers } from "../../../../../helpers/enums";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";

interface FasalContentDuaBelasProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentDuaBelas: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [kekerapanPelaksanaan, setKekerapanPelaksanaan] = useState(
    t("setahun")
  );
  const [tempohPelaksanaan, setTempohPelaksanaan] = useState("");
  const [notisPanggilanMesyuarat, setNotisPanggilanMesyuarat] = useState("");
  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  //const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [bilangan, setBilangan] = useState("");
  const [tempoh, setTempoh] = useState("");
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!pemilihanAjk) {
      errors.pemilihanAjk = t("fieldRequired");
    }

    // if (!kekerapanPelaksanaan) {
    //   errors.kekerapanPelaksanaan = t("fieldRequired");
    // }

    if (!notisPanggilanMesyuarat) {
      errors.notisPanggilanMesyuarat = t("fieldRequired");
    }

    if (!tempohPelaksanaan) {
      errors.tempohPelaksanaan = t("fieldRequired");
    }

    if (!bilangan) {
      errors.bilangan = t("fieldRequired");
    }

    return errors;
  };

  /*const clauseContent = `
              1. Pengelolaan Pertubuhan ini terserah kepada Mesyuarat Agung ahli-ahli. Sekurang-kurangnya satu perdua (1/2) daripada jumlah ahli yang berhak mengundi atau dua (2) kali jumlah bilangan Ahli Jawatankuasa, mengikut mana yang kurang, hendaklah hadir dalam Mesyuarat Agung bagi mengesahkan perjalanan mesyuarat dan mencukupkan kuorum untuk mesyuarat.

              2. Jika kuorum tidak cukup selepas setengah jam daripada waktu yang telah ditetapkan untuk mesyuarat, maka mesyuarat itu hendaklah ditangguhkan kepada suatu tarikh (tidak lebih daripada 30 hari) yang ditetapkan oleh Jawatankuasa dan jika kuorum tidak cukup selepas setengah jam daripada waktu yang telah ditetapkan untuk mesyuarat yang telah ditangguhkan itu, maka ahli-ahli yang hadir berkuasa menjalankan mesyuarat tetapi tidak berkuasa meminda Perlembagaan Pertubuhan.

              3. Mesyuarat Agung ${
                pemilihanAjk != "" ? pemilihanAjk : "<<Jenis Mesyuarat Agung>>"
              } bagi Pertubuhan hendaklah diadakan sebelum ${
    tempohPelaksanaan != ""
      ? tempohPelaksanaan
      : "<<Tempoh Pelaksanaan Mesyuarat Agung Baru>>"
  } setiap ${
    kekerapanPelaksanaan != ""
      ? kekerapanPelaksanaan
      : "<<Kekerapan Pelaksanaan Mesyuarat Agung Baru>>"
  } daripada tarikh Mesyuarat Agung terakhir. Tarikh, masa dan tempat ditetapkan oleh Mesyuarat Jawatankuasa.

              4. Tugas-tugas Mesyuarat Agung ${
                pemilihanAjk != "" ? pemilihanAjk : "<<Jenis Mesyuarat Agung>>"
              } adalah:
                 a. mengesahkan minit Mesyuarat Agung tahun lalu;
                 b. mempertimbang laporan Jawatankuasa berkenaan aktiviti Pertubuhan tahun lalu;
                 c. mempertimbang laporan Bendahari dan penyata kewangan tahun lalu yang telah diaudit;
                 d. memilih Ahli Jawatankuasa dan melantik Juruaudit (jika berkenaan); dan
                 e. menguruskan perkara-perkara lain yang dibentangkan dalam mesyuarat.

              5. Setiausaha hendaklah menghantar notis panggilan mesyuarat kepada tiap-tiap ahli sekurang-kurangnya ${
                notisPanggilanMesyuarat != ""
                  ? notisPanggilanMesyuarat
                  : "<<Notis Panggilan Mesyuarat>>"
              } hari sebelum Mesyuarat Agung ${
    pemilihanAjk != "" ? pemilihanAjk : "<<Jenis Mesyuarat Agung>>"
  } diadakan, agenda mesyuarat, salinan minit mesyuarat, laporan aktiviti tahunan, penyata kewangan Pertubuhan bagi tahun lalu yang telah diaudit serta cadangan dan pencalonan untuk pemilihan pegawai-pegawai. Setiap salinan dokumen tersebut hendaklah disimpan di alamat tempat urusan Pertubuhan untuk makluman ahli.

              6. Mesyuarat Agung Khas bagi Pertubuhan boleh diadakan:
                 a. apabila difikirkan mustahak oleh Jawatankuasa; atau
                 b. atas permintaan bertulis kepada Setiausaha oleh tidak kurang daripada satu perlima (1/5) dari jumlah ahli yang berhak mengundi dengan menerangkan tujuan mengadakannya.

              7. Mesyuarat Agung Khas atas permintaan ahli-ahli hendaklah diadakan dalam tempoh 30 hari dari tarikh penerimaan permintaan mesyuarat tersebut.

              8. Pengumuman dan agenda untuk Mesyuarat Agung Khas itu hendaklah diedarkan oleh Setiausaha kepada semua ahli-ahli sekurang-kurangnya 14 hari sebelum tarikh yang telah ditetapkan untuk mesyuarat.

              9. Fasal 12(1) dan 12(2) dalam Perlembagaan ini berkenaan kuorum dan penangguhan Mesyuarat Agung ${
                pemilihanAjk != "" ? pemilihanAjk : "<<Jenis Mesyuarat Agung>>"
              } adalah terpakai untuk Mesyuarat Agung Khas, dengan syarat jika kuorum tidak mencukupi selepas setengah jam dari waktu yang telah ditetapkan bagi Mesyuarat Agung Khas atas permintaan ahli-ahli, maka mesyuarat tersebut hendaklah dibatalkan dan sekurang-kurangnya enam (6) bulan dari tarikh ini, Mesyuarat Agung Khas atas permintaan ahli-ahli dengan tujuan yang sama tidak boleh diadakan.

              10. Setiausaha hendaklah menghantar kepada tiap-tiap ahli satu salinan minit Mesyuarat Agung ${
                pemilihanAjk != "" ? pemilihanAjk : "<<Jenis Mesyuarat Agung>>"
              } atau Mesyuarat Agung Khas dalam tempoh tiga (3) bulan setelah selesai mesyuarat tersebut.

              11. Mesyuarat Agung Pertama hendaklah diadakan dengan segera dan tidak melebihi setahun daripada tarikh penubuhan Pertubuhan selepas cukup jumlah ahli yang dirasakan sesuai oleh Jawatankuasa Pertubuhan atau apabila telah melebihi dua (2) kali jumlah bilangan Ahli Jawatankuasa.
`;*/

  //const clause12 = localStorage.getItem("clause12");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      setDataId(clause.id);
      if (clause.clauseContentId) {
        setClauseContentId(clause.clauseContentId);
      }
      if(clause.constitutionValues.length > 0){ 
      const fieldMappings: Record<string, (value: string) => void> = { 
        'Jenis Mesyuarat Agung': (value: string) => {
          setPemilihanAjk(value)
          if (value && value == t("annual")) {
            setBilangan("12");
            setTempohPelaksanaan(t("month"));
          } else if(value && value == t("biennial")){
            setBilangan("24");
            setTempohPelaksanaan(t("month"));
          }},  
        "Notis panggilan mesyuarat": setNotisPanggilanMesyuarat
      };
      if(clause.constitutionValues){
        clause.constitutionValues.forEach((item:any) => {
          const setter = fieldMappings[item.titleName];
          if (setter && item.definitionName) {
            setter(item.definitionName);
          }
        });
      }
      setIsEdit(clause.edit);
    }
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id } = useParams();

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<kekerapan pelaksanaan mesyuarat agung baru>>/gi,
    `<b>${
      kekerapanPelaksanaan || "<<kekerapan pelaksanaan mesyuarat agung baru>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${pemilihanAjk || "<<jenis mesyuarat agung>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bilangan tempoh pelaksanaan mesyuarat agung baru>>/gi,
    `<b>${
      bilangan || "<<bilangan tempoh pelaksanaan mesyuarat agung baru>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh pelaksanaan mesyuarat agung baru>>/gi,
    `<b>${
      tempohPelaksanaan || "<<tempoh pelaksanaan mesyuarat agung baru>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<notis panggilan mesyuarat>>/gi,
    `<b>${notisPanggilanMesyuarat || "<<notis panggilan mesyuarat>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("generalMeeting")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("jenisMesyuaratAgung")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required error={!!formErrors.pemilihanAjk}>
              <Select
                size="small"
                value={pemilihanAjk}
                displayEmpty
                onChange={(e) => {
                  setPemilihanAjk(e.target.value as string);
                  if ((e.target.value as string) == t("annual")) {
                    setBilangan("12");
                    setTempohPelaksanaan(t("month"));
                  }
                  if ((e.target.value as string) == t("biennial")) {
                    setBilangan("24");
                    setTempohPelaksanaan(t("month"));
                  }
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    pemilihanAjk: "",
                  }));
                }}
              >
                <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
              </Select>
              {formErrors.pemilihanAjk && (
                <FormHelperText>{formErrors.pemilihanAjk}</FormHelperText>
              )}
            </FormControl>
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("notisPanggilanMesyuarat")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.notisPanggilanMesyuarat}
            >
              <Select
                size="small"
                value={notisPanggilanMesyuarat}
                displayEmpty
                onChange={(e) => {
                  setNotisPanggilanMesyuarat(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    notisPanggilanMesyuarat: "",
                  }));
                }}
              >
                <MenuItem value={"7"}>7</MenuItem>
                <MenuItem value={"14"}>14</MenuItem>
                <MenuItem value={"30"}>30</MenuItem>
              </Select>
              {formErrors.notisPanggilanMesyuarat && (
                <FormHelperText>
                  {formErrors.notisPanggilanMesyuarat}
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <Select disabled size="small" value={t("day")} displayEmpty>
              <MenuItem value={t("day")}>{t("day")}</MenuItem>
            </Select>
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("tempohPelaksanaan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              disabled
              size="small"
              fullWidth
              required
              value={bilangan}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setBilangan(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    setBilangan: "",
                  }));
                } else {
                  setBilangan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    setBilangan: "Invalid Value",
                  }));
                }
              }}
              type="number"
              error={!!formErrors.bilangan}
              helperText={formErrors.bilangan}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.tempohPelaksanaan}
            >
              <Select
                disabled
                size="small"
                value={tempohPelaksanaan}
                displayEmpty
                onChange={(e) => {
                  setTempohPelaksanaan(e.target.value as string);
                  setFormErrors((prev) => ({
                    ...prev,
                    tempohPelaksanaan: "",
                  }));
                }}
              >
                <MenuItem value={t("month")}>{t("month")}</MenuItem>
                <MenuItem value={t("year")}>{t("year")}</MenuItem>
              </Select>
              {formErrors.tempohPelaksanaan && (
                <FormHelperText>{formErrors.tempohPelaksanaan}</FormHelperText>
              )}
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            const errors = validateForm();
            if (Object.keys(errors).length > 0) {
              setFormErrors(errors);
              return;
            }

            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: pemilihanAjk,
                  titleName: "Jenis Mesyuarat Agung",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bilangan,
                  titleName: "bilangan tempoh pelaksanaan mesyuarat agung baru",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tempohPelaksanaan,
                  titleName:
                    "Tempoh pelaksanaan mesyuarat agung baru daripada tarikh terakhir Mesyuarat Agung",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: notisPanggilanMesyuarat,
                  titleName: "Notis panggilan mesyuarat",
                },
              ],
              clause: "clause12",
              clauseCount: 12,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentDuaBelas;
