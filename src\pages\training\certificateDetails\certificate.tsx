import React from "react";
import {Typography} from "@mui/material";
import TrainingBreadcrumb from "@/pages/training/breadcrumb";
import Box from "@mui/material/Box";
import ERosesLogoWithName from "@/components/eroses-log-with-name";
import TrainingFragment from "@/pages/training/trainingFragment";
import {TrainingRequiredIcon} from "@/components/icons/trainingRequired";
import {PaticipantsIcon} from "@/components/icons/participants";
import {DurationIcon} from "@/components/icons/duration";
import {TrainingEnums} from "@/helpers";
import {ButtonPrimary} from "@/components";
import {useTranslation} from "react-i18next";
import CertificateFragment from "@/pages/training/certificateDetails/certificateFragment";
import {useLocation} from "react-router-dom";
import {useCustom} from "@refinedev/core";
import {API_URL} from "@/api";


const Certificate: React.FC = () => {

  const location = useLocation();
  const {t, i18n} = useTranslation();

  const enrollId = location.state?.enrollId;
  const courseId = location.state?.courseId;



  /*const baseArray = {
    required: true,
    requiredIcon: <TrainingRequiredIcon/>,
    image: '../latihanSample/images4.png',
    type: "Kepimpinan",
    title: "Kursus Induksi Jabatan Pendaftaran Pertubuhan Malaysia (JPPM)",
    level: "Tahap Mudah",
    levelColor: "#CE4444B2",
    points: 0,
    participants: 0,
    participantsIcon: <PaticipantsIcon/>,
    duration: "1.5 Jam pembelajaran",
    durationIcon: <DurationIcon/>,
    progress: 0
  }*/

  const {data: trainingData, isLoading: isTrainingLoading} = useCustom({
    url: `${API_URL}/society/training/courses/${courseId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const training = trainingData?.data?.data || {};
  console.log("training", training)

  const {data: trainingEnrollmentData, isLoading: isEnrolledTrainingLoading} = useCustom({
    url: `${API_URL}/society/training/enrollments/${enrollId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const trainingEnrollment = trainingEnrollmentData?.data?.data || {};
  console.log("trainingEnrollment", trainingEnrollment)

  const {data: trainingCertificateData, isLoading: isCertificateTrainingLoading} = useCustom({
    url: `${API_URL}/society/training/certificates/${trainingEnrollment.certificateId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: trainingEnrollment?.certificateId != null && trainingEnrollment?.certificateId != "",
      retry: false,
      cacheTime: 0,
    },
  });

  return (
    <>
      <TrainingBreadcrumb isAdmin={false}/>
      <Box sx={{
        display: "flex",
        flexDirectionL: "row",
        gap:1
      }}>
        <CertificateFragment enrollmentId={enrollId} />
        <Box sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          px: 2,
          py: 2,
          mb: 1,
        }}>
          <Typography
            sx={{
              color: "#666666",
              pt: 3,
              fontWeight: "700",
              fontSize: 14,
            }}
          >
            Penerima Sijil
          </Typography>
          <Box sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "flex-start",
            mt:2,
          }}>
            <Box sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              border: "1px solid #D9D9D9",
              margin: 0,
              width: "100px",
            }}>
              <Typography
                sx={{
                  color: "#0CA6A6",
                  fontWeight: "400",
                  fontSize: 14,
                  textAlign: "center"
                }}
              >
                129 pts
              </Typography>
            </Box>
            <Box sx={{
              px:2,
              margin: 0,
              width: "50%",
            }}>
              <Typography
                sx={{
                  color: "#666666",
                  fontWeight: "400",
                  fontSize: 14,
                }}
              >
                Nur Amirah Syahira Binti Ali
              </Typography>
            </Box>
          </Box>
          <Typography
            sx={{
              mt:5,
              color: "#666666",
              fontWeight: "700",
              fontSize: 14,
            }}
          >
            Info Kursus
          </Typography>
          <TrainingFragment item={training} type={TrainingEnums.All} width={"100%"}/>
          <Box sx={{display: "flex", mt: 5, justifyContent: "flex-start"}}>
            <ButtonPrimary
              variant="outlined"
              sx={{
                borderColor: "#666666",
                bgcolor: "#fff",
                "&:hover": {bgcolor: "#fff", borderColor: "#666666",},
                color: "#666666",
                fontWeight: "400",
              }}
              //onClick={() => navigate("/latihan/mula")}
            >
              {t("download")}
            </ButtonPrimary>
          </Box>
        </Box>
      </Box>
    </>
  );
}

export default Certificate;
