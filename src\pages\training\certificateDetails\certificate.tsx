import React, {useEffect, useState} from "react";
import {Typography} from "@mui/material";
import TrainingBreadcrumb from "@/pages/training/breadcrumb";
import Box from "@mui/material/Box";
import TrainingFragment from "@/pages/training/trainingFragment";
import {TrainingEnums} from "@/helpers";
import {ButtonPrimary} from "@/components";
import {useTranslation} from "react-i18next";
import CertificateInfo from "@/pages/training/certificateDetails/certificateInfo";
import {useLocation} from "react-router-dom";
import {API_URL} from "@/api";
import {useCustom} from "@refinedev/core";



const Certificate: React.FC = () => {

  const location = useLocation();
  const {t, i18n} = useTranslation();
  const [name, setName] = useState<string>("");

  const enrollmentData = location.state?.enrollmentData;
  //const courseId = location.state?.courseId;

  const {
    data: trainingCertificateData,
    isLoading: isCertificateTrainingLoading,
  } = useCustom({
    url: `${API_URL}/society/training/certificates/${enrollmentData.certificateId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled:
        enrollmentData?.certificateId != null &&
        enrollmentData?.certificateId != "",
      retry: false,
      cacheTime: 0,
    },
  });

  const trainingCertificate = trainingCertificateData?.data?.data || {};
  console.log("trainingCertificate", trainingCertificate);

  useEffect(() => {
    if (trainingCertificate) {
      //if (handleCallback) handleCallback(trainingCertificate.user?.name ?? "")
      setName((trainingCertificate.user?.name ?? ""))
    }
  }, [trainingCertificateData])

  const handleDownload = () => {
    const filePath = `${trainingCertificate.certificatePath}`;
    console.log("handleDownload",filePath);
    window.open(filePath, "_blank");
  };

  return (
    <>
      <TrainingBreadcrumb isAdmin={false}/>
      <Box sx={{
        display: "flex",
        flexDirectionL: "row",
        gap:1,
      }}>
        <CertificateInfo enrollmentData={enrollmentData} trainingCertificate={trainingCertificate} />
        <Box sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          px: 2,
          py: 2,
          mb: 1,
          width: "20%"
        }}>
          <Typography
            sx={{
              color: "#666666",
              pt: 3,
              fontWeight: "700",
              fontSize: 14,
            }}
          >
            {t("certificateRecipient")}
          </Typography>
          <Box sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "flex-start",
            mt:2,
          }}>
            <Box sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              border: "1px solid #D9D9D9",
              margin: 0,
              width: "100px",
            }}>
              <Typography
                sx={{
                  color: "#0CA6A6",
                  fontWeight: "400",
                  fontSize: 14,
                  textAlign: "center"
                }}
              >
                {`${enrollmentData.totalPoints} pts`}
              </Typography>
            </Box>
            <Box sx={{
              px:2,
              margin: 0,
              width: "50%",
            }}>
              <Typography
                sx={{
                  color: "#666666",
                  fontWeight: "400",
                  fontSize: 14,
                }}
              >
                {name}
              </Typography>
            </Box>
          </Box>
          <Typography
            sx={{
              mt:5,
              color: "#666666",
              fontWeight: "700",
              fontSize: 14,
            }}
          >
            {t("courseInformation")}
          </Typography>
          <TrainingFragment item={enrollmentData} type={TrainingEnums.All} width={"100%"}/>
          <Box sx={{display: "flex", mt: 5, justifyContent: "flex-start"}}>
            <ButtonPrimary
              variant="outlined"
              sx={{
                borderColor: "#666666",
                bgcolor: "#fff",
                "&:hover": {bgcolor: "#fff", borderColor: "#666666",},
                color: "#666666",
                fontWeight: "400",
              }}
              onClick={handleDownload}
            >
              {t("download")}
            </ButtonPrimary>
          </Box>
        </Box>
      </Box>
    </>
  );
}

export default Certificate;
