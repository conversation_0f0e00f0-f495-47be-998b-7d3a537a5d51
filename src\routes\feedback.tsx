import { Route, Outlet } from "react-router-dom";
// import Feedback from "../pages/Feedback";
// import FeedbackBaru from "../pages/Feedback/cadanganBaru";
import FeedbackSemak from "../pages/Feedback/cadanganSemak";
import { FeedbackPublicAduanCreate001 } from "@/pages/Feedback/public/AduanCreate001";
import { FeedbackPublicAduanCreate002 } from "@/pages/Feedback/public/AduanCreate002";
import { FeedbackPublicAduanLayout } from "@/pages/Feedback/public/AduanLayout";
import { FeedbackPublicAduanSuccess } from "@/pages/Feedback/public/AduanSuccess";
import { FeedbackPublicCadanganCreate } from "@/pages/Feedback/public/CadanganCreate";
import { FeedbackPublicLayout } from "@/pages/Feedback/public/Layout";
import { FeedbackPublicMain } from "@/pages/Feedback/public/Main";
import { FeedbackPublicAduanView } from "@/pages/Feedback/public/AduanView";
import { FeedbackPublicCadanganCheck } from "@/pages/Feedback/public/CadanganCheck";
import { registerRoutes } from "../helpers/routeDetector";
import { RouteGuard } from "../components/RouteGuard";

// Layout component to wrap all feedback routes with protection
const FeedbackGuardLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <FeedbackPublicLayout />
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  '/feedback': 'shared',
  '/feedback/cadanganBaru': 'shared',
  '/feedback/cadanganSemak': 'shared',
  '/feedback/aduan': 'shared',
  '/feedback/aduan/create/:id': 'shared',
  '/feedback/aduan/create/success': 'shared',
  '/feedback/aduan/view/:id': 'shared',
  // Add your route registrations here
});

export const FeedbackRoute = {
  resources: [
    {
      name: "feedback",
      // list: "/feedback",
      // create: "/feedback/create",
    },
  ],
  routes: (
    <>
      <Route path="/feedback" element={<FeedbackGuardLayout />}>
      {/* <Route path="/feedback" element={<FeedbackPublicLayout />}> */}
        <Route index element={<FeedbackPublicMain />} />
        <Route path="cadanganBaru" element={<FeedbackPublicCadanganCreate />} />
        <Route path="cadanganSemak" element={<FeedbackPublicCadanganCheck />} />
        {/* <Route path="cadanganSemak" element={<FeedbackSemak />} /> */}
        <Route path="aduan" element={<FeedbackPublicAduanLayout />}>
          <Route index element={<FeedbackPublicAduanCreate001 />} />
          <Route path="create/:id" element={<FeedbackPublicAduanCreate002 />} />
          <Route path="create/success" element={<FeedbackPublicAduanSuccess />} />
          <Route path="view/:id" element={<FeedbackPublicAduanView />} />
        </Route>
        {/* <Route index element={<Feedback />} />
        <Route path="cadanganBaru" element={<FeedbackBaru />} />
        <Route path="cadanganSemak" element={<FeedbackSemak />} /> */}
      </Route>
    </>
  ),
};
