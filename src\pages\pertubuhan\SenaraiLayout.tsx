import React, { PropsWithChildren, useEffect, useState } from "react";
import {
  NavLink,
  NavLinkRenderProps,
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Grid, IconButton, Typography, useTheme } from "@mui/material";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../api";
import { RootState } from "@/redux/store";

import SenaraiContext from "./SenaraiContext";
import Sidebar from "../../components/layout/Sidebar";
import MenuItem from "../../components/layout/MenuItem";
import {
  ApplicationStatus,
  HideOrDisplayFlex,
  ROApprovalType,
} from "../../helpers/enums";
import { NavigateBefore } from "@mui/icons-material";
import { StepperRegisterCawangan } from "./pengurusan-cawangan/RegisterCawangan/components/Stepper";
import AjkNavigationTabs from "./AjkNavigationTabs";
import { StepperPegawai } from "./rayuan/pegawai/Stepper";
import { StepperPerlembagaan } from "./pengurusan-perlembagaan/Stepper";
import { StepperPembaharuanPelantikan } from "./pengurusan-cawangan/RegisterCawangan/components/StepperPembaharuanPelantikan";
import { CawanganStepper } from "./pengurusan-cawangan/cawangan-stepper";
import { StepperExpired } from "./pengurusan-cawangan/paper-expired/components/Stepper";
import { StepperPaparExpiredCawangan } from "./pengurusan-cawangan/paper-expired/components/StepperPaperExpired";
import { AppDispatch } from "@/redux/store";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { fetchSocietyByIdData } from "@/redux/APIcalls/societyByIdThunks";
import CawanganAjkNavigationTabs from "./pengurusan-cawangan/component/CawanganAJKKeahlian/AjkNavigationTabs";
import { useQuery } from "@/helpers";
import { setSocietyDataRedux } from "@/redux/societyDataReducer";
import { StepperCawanganPindaan } from "./pengurusan-cawangan/component/CawanganPindaan/Stepper";
import CheckIcon from "@mui/icons-material/Check";
import { HebahanIcon } from "@/components/icons/hebahan";

export const SenaraiLayout: React.FC<PropsWithChildren> = ({ children }) => {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const { id } = useParams();
  const theme = useTheme();
  const [activeSection, setActiveSection] = useState("maklumat");
  const [breadcrumbText, setBreadcrumbText] = useState(
    t("organizationInformation")
  );

  const liquidationState = useSelector((state: RootState) => state.liquidation);

  const [openDropDown, setOpenDropdown] = useState<number>();
  const [urlMenu, setUrlMenu] = useState<string>();
  const [activeSubItem, setActiveSubItem] = useState<string | null>(null);
  const [activeRayuanTab, setActiveRayuanTab] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pembubaranStep, setPembubaranStep] = useState(0);
  const [societyData, setSocietyData] = useState<any>({});
  const [societyMigrateStatus, setSocietyMigrateStatus] = useState(null);

  const [isPembubaranSuccess, setIsPembubaranSuccess] = useState(false);

  const primary = theme.palette.primary.main;
  const isPembubaranPage =
    location.pathname.includes("senarai/pembubaran/create") ||
    /\/senarai\/pembubaran\/\d+(\/feedback)?$/.test(location.pathname);

  const isCawanganPembubaranPage =
    location.pathname.includes("senarai/cawangan/view/pembubaran/create") ||
    /\/senarai\/cawangan\/view\/pembubaran\/\d+\/feedback$/.test(
      location.pathname
    );

  const [penyataTahunanStep, setPenyataTahunanStep] = useState(0);

  const pembubaranStepList = [
    {
      id: 1,
      label: t("maklumatMesyuarat"),
    },
    {
      id: 2,
      label: t("financialInformation"),
    },
    {
      id: 3,
      label: t("maklumatAsset"),
    },
  ];

  const { data: societyResponseData, refetch: fetchSociety } = useQuery({
    url: location.pathname.includes("senarai/cawangan")
      ? `society/${id}/basic`
      : `society/${id}`,
    onSuccess: (data) => {
      const response = data?.data?.data;
      setSocietyData(response);
      setSocietyMigrateStatus(response.migrateStat);
      //
      dispatch(setSocietyDataRedux(response));
      // dispatch(setSocietyDataRedux(response));
    },
    autoFetch: false,
  });

  // const societyDetail = societyData || {};
  // console.log(societyDetail)
  // console.log(societyDataById)

  //console.log(societyDetail);
  const branchAmendRedux = useSelector(
    (state: { branchAmendData: any }) => state.branchAmendData.data
  );

  const branchRedux = useSelector((state: any) => state.branchData.data);

  const [searchParams] = useSearchParams();
  // const { id: branchParamId } = useParams<{ id: string }>(); //newer
  const branchParamId = searchParams.get("id");
  /*const branchId = searchParams.get("id")
    ? Number(searchParams.get("id"))
    : branchParamId;*/

  const branchId =
    location.pathname.includes("senarai/cawangan/view/mesyuarat") &&
    branchAmendRedux?.branchId
      ? branchAmendRedux?.branchId
      : location.state?.branchId ?? 0;

  const branchNo =
    location.pathname.includes("senarai/cawangan/view/mesyuarat") &&
    branchAmendRedux?.branchId
      ? branchAmendRedux?.branchNo
      : location.state?.branchNo ?? 0;

  const [branchData, setBranchData] = useState<any>();
  const { data: branchDataById, isLoading: isLoadingBranch } = useCustom<any>({
    url: `${API_URL}/society/branch/getById/${
      branchId ? branchId : branchParamId
    }`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled:
        (branchId !== null &&
          branchId !== 0 &&
          (location.pathname.includes("senarai/cawangan") ||
            location.pathname.includes("/cawangan/lanjut-masa"))) ||
        (branchParamId !== null &&
          branchParamId !== "0" &&
          location.pathname.includes("/papar")),
      retry: false,
      cacheTime: 0,
      onSuccess: (responseData) => {
        if (responseData?.data?.data) {
          setBranchData(responseData?.data?.data);
        } else {
          setBranchData(null);
        }
      },
    },
  });

  const excludedStatus = [
    ApplicationStatus.MENUNGGU_KEPUTUSAN,
    ApplicationStatus.TOLAK,
    ApplicationStatus.LUPUT,
  ];

  let hideMenu = excludedStatus.includes(
    societyResponseData?.data?.data?.applicationStatusCode
  )
    ? "none"
    : "inherited";
  if (branchDataById?.data?.data) {
    hideMenu = excludedStatus.includes(
      branchDataById.data?.data?.applicationStatusCode
    )
      ? "none"
      : "inherited";
  }

  const sections = [
    {
      id: "maklumat",
      label: t("organizationInformation"),
      path: `maklumat`,
      icon: "/maklumat.png",
      subPath: ["senarai/maklumat"],
    },
    {
      id: "perlembagaan",
      label: t("constitution"),
      path: `perlembagaan`,
      icon: "/perlembagaan.png",
      display: HideOrDisplayFlex,
      subPath: ["perlembagaan/maklumat"],
      children: [
        {
          id: "maklumat-perlembagaan",
          label: (
            <Typography sx={{ fontSize: "14px", fontWeight: "400 !important" }}>
              {t("pengurusanPerlembagaan")}
            </Typography>
          ),
          path: `perlembagaan/maklumat`,
          icon: "",
          display: HideOrDisplayFlex,
          subPath: ["perlembagaan/maklumat"],
        },
        {
          id: "senarai-pindaan",
          label: t("migrasiPerlembagaan"),
          path: `perlembagaan/migrasi`,
          icon: "",
          subPath: ["perlembagaan/migrasi"],
          //display: HideOrDisplayFlex,
          display: societyMigrateStatus == 1 ? hideMenu : "none",
        },
      ],
    },
    {
      id: "meeting",
      label: t("meetingManagement"),
      path: `mesyuarat`,
      subPath: ["createMeeting"],
      icon: "/aliranTugas.png",
      // children: [
      //   {
      //     id: "meeting",
      //     label: t("meeting"),
      //     path: `mesyuarat`,
      //     icon: "/aliranTugas.png",
      //     display: HideOrDisplayFlex,
      //   },
      //   {
      //     id: "tugasan",
      //     label: t("taskFlow"),
      //     path: `mesyuarat/tugasan`,
      //     icon: "/aliranTugas.png",
      //     display: HideOrDisplayFlex,
      //   },
      // ],
    },
    {
      id: "pembubaran",
      label: t("liquidation"),
      path: `pembubaran`,
      subPath: ["create", "feedback"],
      icon: "/pembubaran1.png",
      //display: HideOrDisplayFlex,
      display: hideMenu,
    },
    {
      id: "ajk",
      label: t("pengurusanAjk"),
      path: `ajk/jawatankuasa`,
      subPath: [
        "jawatankuasa",
        "ahli",
        "pemegang-amanah",
        "pegawai",
        "juruaudit",
        "aliran-tugas",
      ],
      icon: "/rayuan.png",
      display: HideOrDisplayFlex,
      // children: [
      //   {
      //     id: "rayuan",
      //     label: t("positionOfAuthority"),
      //     path: "rayuan",
      //     icon: "/jawatankuasa.png",
      //     display: "flex",
      //   },
      //   {
      //     id: "ahli",
      //     label: t("Ahli"),
      //     path: "rayuan/ahli",
      //     icon: "/ahli.png",
      //     display: "flex",
      //   },
      //   {
      //     id: "pemegang-amanah",
      //     label: t("Pemegang Amanah"),
      //     path: "pemegang-amanah",
      //     icon: "/pemegang-amanah.png",
      //     display: "flex",
      //   },
      //   {
      //     id: "pegawai",
      //     label: t("Pegawai"),
      //     path: "pemegang-amanah",
      //     icon: "/pegawai.png",
      //     display: "flex",
      //   },
      //   {
      //     id: "juruaudit",
      //     label: t("Juruaudit"),
      //     path: "juru-audit",
      //     icon: "/juru-audit.png",
      //     display: "flex",
      //   },
      //   {
      //     id: "aliran-tugas",
      //     label: t("Aliran tugas"),
      //     path: "aliran-tugas",
      //     icon: "/aliran-tugas.png",
      //     display: "flex",
      //   },
      // ],
    },
    {
      id: "penyataTahunan",
      label: t("annualStatement"),
      path: `penyataTahunan`,
      subPath: [
        "penyata-tahunan-agung",
        "penyata-tahunan-info",
        "penyata-tahunan-ajk",
        "penyata-tahunan-juruaudit",
        "penyata-tahunan-pendapatan",
        "penyata-tahunan-laporan-aktiviti",
        "penyata-tahunan-sumbangan",
        "penyata-tahunan-bank",
        "penyata-tahunan-display",
        "penyata-tahunan-pengakuan",
      ],
      icon: "/penyata.png",
      //display: HideOrDisplayFlex,
      display: hideMenu,
    },
    // {
    //   id: "cawangan",
    //   label: t("pengurusanCawangan"),
    //   path: `cawangan`,
    //   icon: "/cawangan.png",
    // },
    {
      id: "dokumen",
      label: t("document"),
      path: `dokumen`,
      icon: "/dokumen.png",
      display: HideOrDisplayFlex,
    },
    {
      id: "hebahan",
      label: t("hebahan"),
      path: "/hebahan/",
      //action: () => handleComingSoon?.(true),
      icon: (
        <HebahanIcon
          sx={{
            color: "#fff",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      //permissions: [],
    },
  ];
  const branchListStatuses = [
    {
      translation: "tempohPengisianMaklumatCawanganDibuka",
      color: primary,
    },
    {
      translation: "dalamProsesPermohonanLanjutanMasa",
      color: "#FFD100",
    },
    {
      translation: "permohonanCawanganLuput",
      color: "#FF0000",
    },
    {
      translation: "cawanganMigrasi",
      color: "#848484",
    },
  ];

  const sectionsCawangan = [
    {
      id: "maklumat",
      label: t("organizationInformation"),
      path: `cawangan/view`,
      // path: `cawangan/view/maklumat`,
      icon: "/maklumat.png",
      subPath: ["senarai/cawangan/view"],
    },
    {
      id: "perlembagaan",
      label: t("constitution"),
      path: `cawangan/view/perlembagaan/maklumat`,
      icon: "/perlembagaan.png",
      subPath: ["perlembagaan/maklumat"],
    },
    {
      id: "meeting",
      label: t("meetingManagement"),
      path: `cawangan/view/mesyuarat`,
      icon: "/aliranTugas.png",
    },
    {
      id: "pembubaran",
      label: t("liquidation"),
      path: `cawangan/view/pembubaran`,
      subPath: ["create", "feedback"],
      icon: "/pembubaran1.png",
      display: hideMenu,
    },
    {
      id: "ajk",
      label: t("pengurusanAjk"),
      path: `cawangan/view/ajk/jawatankuasa`,
      subPath: [
        "jawatankuasa",
        "ahli",
        "pemegang-amanah",
        "pegawai",
        "juruaudit",
        "aliran-tugas",
      ],
      icon: "/rayuan.png",
    },
    {
      id: "penyataTahunan",
      label: t("annualStatement"),
      path: `cawangan/view/penyataTahunan`,
      subPath: [
        "cawangan-penyata-tahunan-agung",
        "cawangan-penyata-tahunan-info",
        "cawangan-penyata-tahunan-ajk",
        "cawangan-penyata-tahunan-juruaudit",
        "cawangan-penyata-tahunan-pendapatan",
        "cawangan-penyata-tahunan-laporan-aktiviti",
        "cawangan-penyata-tahunan-sumbangan",
        "cawangan-penyata-tahunan-bank",
        "cawangan-penyata-tahunan-display",
        "cawangan-penyata-tahunan-pengakuan",
      ],
      icon: "/penyata.png",
      display: hideMenu,
    },
    {
      id: "dokumen",
      label: t("document"),
      path: `cawangan/view/dokumen`,
      icon: "/dokumen.png",
    },
  ];

  if (/\/senarai\/pembubaran\/\d+\/feedback$/.test(location.pathname)) {
    pembubaranStepList.push({
      id: 4,
      label: t("maklumbalasJawatankuasa"),
    });
  }

  const penyataTahunanStepList = [
    {
      id: 1,
      label: t("meeting"),
      path: "penyataTahunan/penyata-tahunan-agung",
      branchpath: "penyataTahunan/cawangan-penyata-tahunan-agung",
    },
    {
      id: 2,
      label: t("generalInformation"),
      path: "penyataTahunan/penyata-tahunan-info",
      branchpath: "penyataTahunan/cawangan-penyata-tahunan-info",
    },
    {
      id: 3,
      label: t("ajkInformation"),
      path: "penyataTahunan/penyata-tahunan-ajk",
      branchpath: "penyataTahunan/cawangan-penyata-tahunan-ajk",
    },
    {
      id: 4,
      label: t("auditorInfo"),
      path: "penyataTahunan/penyata-tahunan-juruaudit",
      branchpath: "penyataTahunan/cawangan-penyata-tahunan-juruaudit",
    },
    {
      id: 5,
      label: t("penyataPendapatanPerbelanjaan"),
      path: "penyataTahunan/penyata-tahunan-pendapatan",
      branchpath: "penyataTahunan/cawangan-penyata-tahunan-pendapatan",
    },
    {
      id: 6,
      label: t("activityReport"),
      path: "penyataTahunan/penyata-tahunan-laporan-aktiviti",
      branchpath: "penyataTahunan/cawangan-penyata-tahunan-laporan-aktiviti",
    },
    {
      id: 7,
      label: t("contributionsFromAbroad"),
      path: "penyataTahunan/penyata-tahunan-sumbangan",
      branchpath: "penyataTahunan/cawangan-penyata-tahunan-sumbangan",
    },
    {
      id: 8,
      label: t("paparan"),
      path: "penyataTahunan/penyata-tahunan-display",
      branchpath: "penyataTahunan/cawangan-penyata-tahunan-display",
    },
    {
      id: 9,
      label: t("pengakuan"),
      path: "penyataTahunan/penyata-tahunan-pengakuan",
      branchpath: "penyataTahunan/cawangan-penyata-tahunan-pengakuan",
    },
  ];

  const [isPenyataTahunanSuccess, setIsPenyataTahunanSuccess] = useState(false);

  const handleNextPenyataTahunan = () =>
    setPenyataTahunanStep((prevStep) => prevStep + 1);
  const handleBackPenyataTahunan = () =>
    setPenyataTahunanStep((prevStep) => prevStep - 1);
  const handleResetPenyataTahunan = () => {
    setPenyataTahunanStep(0);
    setIsPenyataTahunanSuccess(false);
  };
  const setPenyataTahunanSuccess = (value: boolean) =>
    setIsPenyataTahunanSuccess(value);

  const dispatch: AppDispatch = useDispatch();
  const {
    data: societyDataById,
    loading: loadingSociety,
    error: errorSociety,
  } = useSelector((state: any) => state.societyByIdData);

  useEffect(() => {
    fetchSociety();
  }, [id]);

  useEffect(() => {
    return () => {
      if (location.pathname.includes("senarai/cawangan")) return;

      setBranchData(null);
    };
  }, [location.pathname]);

  const handleNextPembubaran = () =>
    setPembubaranStep((prevStep) => prevStep + 1);
  const handleBackPembubaran = () =>
    setPembubaranStep((prevStep) => prevStep - 1);
  const handleResetPembubaran = () => {
    setPembubaranStep(0);
    setIsPembubaranSuccess(false);
  };
  const setPembubaranSuccess = (value: boolean) =>
    setIsPembubaranSuccess(value);

  useEffect(() => {
    const pathSegments = location.pathname.split("/").filter(Boolean);
    const currentPath = pathSegments.pop();

    const relevantSections = location.pathname.includes("senarai/cawangan")
      ? sectionsCawangan
      : sections;

    const isCawangan = location.pathname.includes("senarai/cawangan");

    const activeSection = relevantSections.findIndex((section) =>
      section.subPath?.some((sub) => pathSegments.includes(sub))
    );

    if (activeSection !== -1) {
      setOpenDropdown(activeSection);
      setUrlMenu(relevantSections[activeSection].path);
      setBreadcrumbText(t(relevantSections[activeSection].id));
      setActiveSubItem(null); // Reset active sub-item
    } else {
      const hasPerlembagaan = pathSegments.includes("perlembagaan");
      if (!hasPerlembagaan) setUrlMenu(currentPath);
    }

    //for Langkah penyata tahunan correct step visualize on UI
    const matchedStep = penyataTahunanStepList.find((item) =>
      location.pathname.includes(isCawangan ? item.branchpath : item.path)
    );

    if (matchedStep) {
      setPenyataTahunanStep(matchedStep.id);
    }
  }, [location.pathname]);

  const handleSectionClick = (
    sectionIndex: number,
    sectionsNow: any = sections
  ) => {
    if (loadingSociety) return;

    const section = sectionsNow[sectionIndex];

    if (section.children?.length) {
      setOpenDropdown(sectionIndex);
      setUrlMenu(section.path);
      setActiveSubItem(null); // Reset active sub-item
    } else {
      navigate(section.path, {
        state: {
          disabled: hideMenu === "none",
          branchId: branchId,
          branchNo: branchNo,
          societyId: id,
        },
      });
      setUrlMenu(section.path);
      setOpenDropdown(undefined);
      setActiveSubItem(null); // Reset active sub-item
    }
    setActiveRayuanTab(0);
    setActiveSection(section.id);
    setBreadcrumbText(t(section.id));
    handleResetPembubaran();
  };

  const handleSubItemClick = (sectionIndex: number, subItemPath: string) => {
    navigate(subItemPath, {
      state: {
        disabled: hideMenu === "none",
        branchId: branchId,
      },
    });
    setUrlMenu(sections[sectionIndex].path);
    setActiveSubItem(subItemPath); // Set active sub-item
  };

  const handleClick = (item: any) => {
    setPenyataTahunanStep(item.id);
    navigate(
      location.pathname.includes("view/penyataTahunan")
        ? "cawangan/view/" + item.branchpath
        : item.path
    );
  };

  const { mutate: queryData } = useCustomMutation();
  const [kuiri, setKuiri] = useState<string | null>(null);
  useEffect(() => {
    if (branchData?.applicationStatusCode == ApplicationStatus.KUIRI) {
      queryData(
        {
          method: "post",
          url: `${API_URL}/society/roQuery/getQuery`,
          values: {
            roApprovalType: ROApprovalType.BRANCH_REGISTRATION.code,
            branchId: branchParamId,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
        },
        {
          onSuccess(data) {
            if (data?.data?.data[0]?.note) {
              setKuiri(data?.data?.data[0]?.note);
            } else {
              setKuiri(null);
            }
          },
        }
      );
    } else {
      setKuiri(null);
    }
  }, [branchData]);

  return (
    <Box
      sx={{
        "& .MuiTypography-root": {
          fontFamily: "'Poppins', 'Roboto', 'Helvetica', 'Arial', sans-serif",
          fontWeight: "700",
        },
        transition: "max-height 0.3s ease",
      }}
    >
      {/* <BreadcrumbComponent breadcrumbs={breadcrumbs} /> */}
      <Box
        onClick={
          location.pathname.includes("ahli") &&
          location.pathname.includes("serta-pertubuhan")
            ? () => navigate(-1)
            : location.pathname.includes("senarai/cawangan/register") ||
              location.pathname.includes("senarai/cawangan/pembayaran")
            ? () =>
                navigate(
                  -1
                  // `/pertubuhan/society/${id}/senarai/cawangan`
                )
            : () =>
                navigate(
                  -1
                  // "/pertubuhan"
                )
        }
        sx={{
          display: "flex",
          alignItems: "center",
          mb: 3,
          gap: 2,
          cursor: "pointer",
        }}
      >
        <IconButton size="small" sx={{ color: "#666666", p: 0 }}>
          <NavigateBefore />
        </IconButton>

        <Typography
          sx={{ color: "#666666", fontSize: 18, fontWeight: "400 !important" }}
        >
          {location.pathname.includes("ahli") &&
          location.pathname.includes("serta-pertubuhan")
            ? t("sertaPertubuhan")
            : location.pathname.includes("/cawangan")
            ? t("cawangan")
            : t("Pertubuhan")}
        </Typography>
      </Box>

      {/* <Box sx={{ display: "flex" }}> */}
      <Grid container spacing={{ xs: 1, sm: 2 }}>
        {location.pathname.includes("senarai/cawangan/register") ||
        location.pathname.includes("senarai/cawangan/pembayaran") ? (
          // ||
          // (location.pathname.includes("cawangan") &&
          //   location.pathname.includes("papar"))
          <Grid item xs={2}>
            <StepperRegisterCawangan
              activeStep={
                location.pathname.includes("senarai/cawangan/register")
                  ? 0
                  : location.pathname.includes("senarai/cawangan/pembayaran")
                  ? 1
                  : 0
              }
            />
          </Grid>
        ) : location.pathname.includes("senarai/cawangan/expired") ? (
          <Grid item xs={2}>
            <StepperExpired
              activeStep={
                location.pathname.includes("expired/maklumat")
                  ? 0
                  : location.pathname.includes("expired/minit")
                  ? 1
                  : 2
              }
            />
          </Grid>
        ) : (location.pathname.includes("papar") ||
            location.pathname.includes("lanjut-masa")) &&
          branchData?.applicationStatusCode == ApplicationStatus.KUIRI &&
          kuiri ? (
          <Grid
            item
            xs={2}
            sx={{
              display: "grid",
              gap: 3,
              mt: 2,
              padding: 3,
              backgroundColor: "white",
              borderRadius: "15px",
              height: "fit-content",
            }}
          >
            <Typography className="title">{t("kuiri")}</Typography>
            <Box
              sx={{
                padding: 1.5,
                backgroundColor: "var(--border-grey)",
                borderRadius: "12px",
              }}
            >
              <Typography className="label-dashboard">{kuiri}</Typography>
            </Box>
          </Grid>
        ) : location.pathname.includes("/branch-Info") ? (
          <Grid item xs={2}>
            <StepperCawanganPindaan
              activeStep={
                location.pathname.includes("senarai/cawangan/branch-Info/form")
                  ? 1
                  : location.pathname.includes(
                      "senarai/cawangan/branch-Info/amend"
                    )
                  ? 2
                  : location.pathname.includes(
                      "senarai/cawangan/branch-Info/bayaran"
                    )
                  ? 3
                  : location.pathname.includes("senarai/cawangan/branch-Info")
                  ? 0
                  : 0
              }
            />
          </Grid>
        ) : (
          // <Box
          //   sx={{
          //     display: "grid",
          //     justifyContent: "center",
          //     background: "green",
          //     width: "100%",
          //   }}
          // >
          <Grid item xs={2}>
            {!location.pathname.includes(`senarai/cawangan`) && (
              <>
                <Sidebar sx={{ minWidth: "100%" }}>
                  {sections.map((section, idx) => {
                    return (
                      <Box
                        key={section.id}
                        sx={{
                          display: "grid",
                          justifyContent: "center",
                          alignItems: "center",
                          fontSize: "14px",
                        }}
                      >
                        <MenuItem
                          onClick={() => handleSectionClick(idx)}
                          display={section.display}
                          active={
                            urlMenu?.includes(section.path) ||
                            section.subPath?.includes(urlMenu ?? "")
                          }
                          icon={<></>}
                          sx={{
                            textAlign: "center",
                            cursor: loadingSociety ? "wait" : "pointer",
                            fontSize: "14px",
                            color:
                              urlMenu?.includes(section.path) ||
                              section.subPath?.includes(urlMenu ?? "")
                                ? "#fff"
                                : "var(--text-grey)",
                          }}
                        >
                          {section.label}
                        </MenuItem>

                        {openDropDown === idx && section.children && (
                          <Box
                            sx={{
                              maxHeight: openDropDown === idx ? "500px" : "0",
                              opacity: openDropDown === idx ? 1 : 0,
                              overflow: "hidden",
                              transition:
                                "max-height 0.3s ease, opacity 0.3s ease",
                              display: "grid",
                              justifyContent: "flex-start",
                              alignItems: "center",
                              backgroundColor: "#F7F7F7",
                              p: "0rem 0.5rem 1rem 0.5rem",
                              borderRadius: 2,
                              mt: 1,
                            }}
                          >
                            {section.children.map((subItem: any) => (
                              <MenuItem
                                key={subItem.id}
                                activeBg={"var(--primary-color)80"}
                                display={subItem?.display}
                                onClick={() =>
                                  handleSubItemClick(idx, subItem.path)
                                }
                                active={activeSubItem === "subItem.path"}
                                icon={
                                  // <img
                                  //   src={subItem.icon}
                                  //   alt={subItem.label}
                                  //   style={{
                                  //     width: getIconSize(subItem?.icon || ""),
                                  //     height: getIconSize(subItem?.icon || ""),
                                  //     objectFit: "contain",
                                  //     filter: `brightness(0) saturate(100%) invert(${
                                  //       subItem.path === urlMenu ? 0 : 0
                                  //     }) grayscale(${
                                  //       subItem.path === urlMenu ? 0 : 1
                                  //     }) brightness(${
                                  //       subItem.path === urlMenu ? 1 : 0.5
                                  //     })`,
                                  //   }}
                                  // />
                                  <></>
                                }
                                sx={{ textAlign: "center" }}
                              >
                                {subItem.label}
                              </MenuItem>
                            ))}
                          </Box>
                        )}
                      </Box>
                    );
                  })}
                </Sidebar>
              </>
            )}

            {/* PEMBUBARAN STEP */}
            {isPembubaranPage && (
              <Box
                sx={{
                  position: "sticky",
                  top: "30px",
                }}
              >
                <Box
                  sx={{
                    width: "100%",
                    marginTop: "10px",
                    padding: "19px 10px",
                    borderRadius: "15px",
                    background: "#FFF",
                    boxShadow: " 0px 12px 12px 0px #EAE8E8CC",
                  }}
                >
                  <Typography
                    fontSize="12px"
                    fontWeight="400 !important"
                    color="var(--primary-color)"
                    marginBottom={2}
                  >
                    {t("dissolutionSteps")}
                  </Typography>

                  {pembubaranStepList.map((item) => (
                    <Box
                      key={item.label}
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 1,
                        marginBottom: "15px",
                        "&:nth-last-of-type(1)": {
                          marginBottom: 0,
                        },
                      }}
                    >
                      <Box
                        sx={{
                          backgroundColor:
                            pembubaranStep + 1 > item.id || isPembubaranSuccess
                              ? "var(--primary-color)"
                              : "#FFF",
                          flexShrink: 0,
                          padding: 0,
                          margin: 0,
                          width: "12px",
                          height: "12px",
                          border:
                            pembubaranStep + 1 >= item.id || isPembubaranSuccess
                              ? "1px solid var(--primary-color)"
                              : "1px solid #DADADA",
                          borderRadius: "3px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        {(pembubaranStep + 1 >= item.id ||
                          isPembubaranSuccess) && (
                          <CheckIcon
                            sx={{
                              fontSize: "10px",
                              color: "#FFF",
                            }}
                          />
                        )}
                      </Box>
                      <Typography
                        fontSize="12px"
                        color={
                          pembubaranStep + 1 >= item.id || isPembubaranSuccess
                            ? "var(--primary-color)"
                            : "#DADADA"
                        }
                        fontWeight={
                          pembubaranStep + 1 > item.id || isPembubaranSuccess
                            ? "500 !important"
                            : "400 !important"
                        }
                      >
                        {item.label}
                      </Typography>
                    </Box>
                  ))}
                </Box>

                {(liquidationState.roQuery || liquidationState.roApproval) && (
                  <Box
                    sx={{
                      mt: 1,
                      background: "#fff",
                      boxShadow: "0px 12px 12px 0px #EAE8E866",
                      borderRadius: "15px",
                      padding: "20px 12px 1px 12px",
                      display: {
                        xs: "none",
                        lg: "block",
                      },
                    }}
                  >
                    <Typography
                      fontWeight="400 !important"
                      fontSize="12px"
                      color="var(--primary-color)"
                      mb="15px"
                    >
                      {t("remarks")}
                    </Typography>
                    <Typography
                      fontWeight="400 !important"
                      fontSize="12px"
                      mb="35px"
                    >
                      {liquidationState.roQuery?.note ??
                        liquidationState.roApproval?.note ??
                        "-"}
                    </Typography>
                  </Box>
                )}
              </Box>
            )}

            {(location.pathname.includes(
              "senarai/rayuan/pegawai/create-awam"
            ) ||
              location.pathname.includes(
                "senarai/rayuan/pegawai/create-harta"
              )) && (
              <StepperPegawai
                jenisPegawai={
                  location.pathname.includes(
                    "senarai/rayuan/pegawai/create-awam"
                  ) && i18n?.language === "en"
                    ? "Public"
                    : location.pathname.includes(
                        "senarai/rayuan/pegawai/create-harta"
                      ) && i18n?.language === "en"
                    ? "Property"
                    : location.pathname.includes(
                        "senarai/rayuan/pegawai/create-awam"
                      ) && i18n?.language === "my"
                    ? "Awam"
                    : "Harta"
                }
                activeStep={
                  location.pathname.includes(
                    "senarai/rayuan/pegawai/create-awam/bayaran"
                  ) ||
                  location.pathname.includes(
                    "senarai/rayuan/pegawai/create-harta/bayaran"
                  )
                    ? 1
                    : location.pathname.includes(
                        "senarai/rayuan/pegawai/create-awam"
                      ) ||
                      location.pathname.includes(
                        "senarai/rayuan/pegawai/create-harta"
                      )
                    ? 0
                    : 0
                }
              />
            )}

            {(location.pathname.includes(
              "senarai/perlembagaan/maklumat/pindaan-perlembagaan"
            ) ||
              location.pathname.includes(
                "senarai/perlembagaan/migrasi/pindaan-perlembagaan"
              )) && (
              <StepperPerlembagaan
                activeStep={
                  location.pathname.includes(
                    "senarai/perlembagaan/maklumat/pindaan-perlembagaan/bayaran"
                  ) ||
                  location.pathname.includes(
                    "senarai/perlembagaan/migrasi/pindaan-perlembagaan/bayaran"
                  )
                    ? 1
                    : location.pathname.includes(
                        "senarai/perlembagaan/maklumat/pindaan-perlembagaan"
                      ) ||
                      location.pathname.includes(
                        "senarai/perlembagaan/migrasi/pindaan-perlembagaan"
                      )
                    ? 0
                    : 0
                }
              />
            )}

            {location.pathname.includes("senarai/cawangan") &&
              !location.pathname.includes("view") &&
              !location.pathname.includes("maklumat-am") &&
              !location.pathname.includes("minute-mesyuarat") &&
              !location.pathname.includes("ahlijawatankuasa") &&
              !location.pathname.includes("dokumen-sokongan") &&
              !location.pathname.includes("pembaharuan/pelantikan") &&
              !location.pathname.includes("pembaharuan/cetak") &&
              !location.pathname.includes("/create-ajk") &&
              !location.pathname.includes("/create-ajk-bukan-wn") &&
              !location.pathname.includes("/view-ajk-bukan-wn") &&
              !location.pathname.includes("/papar") &&
              !location.pathname.includes("/branch-Info") && (
                <Box
                  sx={{
                    p: 3,
                    borderRadius: "20px",
                    backgroundColor: "white",
                    maxHeight: {
                      lg: "700px",
                      md: "800px",
                      xs: "800px",
                    },
                    width: "100%",
                    minWidth: {
                      lg: "25%",
                    },
                    maxWidth: {
                      md: "100%",
                      lg: "300px",
                    },
                  }}
                >
                  {branchListStatuses.map((item, index) => (
                    <Box
                      key={`branch-list-status-${index}`}
                      sx={{
                        display: "flex",
                        flexDirection: { xs: "column", xl: "row" },
                        alignItems: "center",
                        gap: { xs: 1, lg: 2, xl: 4 },
                        mb: 2,
                      }}
                    >
                      <Box
                        sx={{
                          width: 20,
                          height: 20,
                          flexShrink: 0,
                          borderRadius: "4px",
                          border: "1px solid rgba(0,0,0,0.1)",
                          backgroundColor: item.color,
                        }}
                      />
                      <Typography
                        sx={{
                          fontSize: { xs: 12, lg: 14 },
                          color: "#666666",
                          fontWeight: "400 !important",
                          maxWidth: "200px",
                        }}
                      >
                        {t(item.translation)}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              )}

            {location.pathname.includes(
              "senarai/cawangan/pembaharuan/pelantikan"
            ) && <StepperPembaharuanPelantikan />}
            {location.pathname.includes(
              "senarai/cawangan/pembaharuan/expire"
            ) && (
              <StepperPaparExpiredCawangan
                activeStep={
                  location.pathname.includes(
                    "senarai/cawangan/pembaharuan/expire"
                  )
                    ? 1
                    : currentPage === 2
                    ? 1
                    : 0
                }
                handleClick={(step) => {
                  if (step === 0) {
                    setCurrentPage(1);
                  } else if (step === 1) {
                    setCurrentPage(2);
                  }
                }}
              />
            )}

            {(location.pathname.includes("senarai/cawangan/maklumat-am") ||
              location.pathname.includes("senarai/cawangan/minute-mesyuarat") ||
              location.pathname.includes("senarai/cawangan/ahlijawatankuasa") ||
              location.pathname.includes("senarai/cawangan/create-ajk") ||
              location.pathname.includes(
                "senarai/cawangan/create-ajk-bukan-wn"
              ) ||
              location.pathname.includes(
                "senarai/cawangan/view-ajk-bukan-wn"
              ) ||
              location.pathname.includes(
                "senarai/cawangan/dokumen-sokongan"
              )) && (
              <>
                <CawanganStepper kuiri={kuiri} activeStep={null} />
              </>
            )}

            {location.pathname.includes("senarai/cawangan/view") &&
              !location.pathname.includes("senarai/cawangan/view-ajk") && (
                <>
                  <Sidebar sx={{ minWidth: "100%" }}>
                    {sectionsCawangan.map((section, idx) => (
                      <Box
                        key={section.id}
                        sx={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          fontSize: "14px",
                        }}
                      >
                        <NavLink
                          end={idx === 0}
                          style={{
                            width: "100%",
                            textDecoration: "none",
                          }}
                          state={{
                            branchId,
                            branchNo,
                          }}
                          to={section.path}
                        >
                          {({ isActive }: NavLinkRenderProps) => (
                            <MenuItem
                              display={section.display}
                              onClick={() =>
                                handleSectionClick(idx, sectionsCawangan)
                              }
                              active={isActive}
                              icon={null}
                              sx={{
                                textAlign: "center",
                                cursor: loadingSociety
                                  ? "not-allowed"
                                  : "pointer",
                                fontSize: "14px",
                                textDecoration: "none !important",
                              }}
                            >
                              {section.label}
                            </MenuItem>
                          )}
                        </NavLink>
                      </Box>
                    ))}
                  </Sidebar>
                </>
              )}

            {isCawanganPembubaranPage && (
              <Box
                sx={{
                  position: "sticky",
                  top: "30px",
                }}
              >
                <Box
                  sx={{
                    width: "100%",
                    marginTop: "10px",
                    padding: "19px 10px",
                    borderRadius: "15px",
                    background: "#FFF",
                    boxShadow: " 0px 12px 12px 0px #EAE8E8CC",
                  }}
                >
                  <Typography
                    fontSize="12px"
                    fontWeight="400 !important"
                    color="var(--primary-color)"
                    marginBottom={2}
                  >
                    {t("dissolutionSteps")}
                  </Typography>

                  {pembubaranStepList.map((item) => (
                    <Box
                      key={item.label}
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 1,
                        marginBottom: "15px",
                        "&:nth-last-of-type(1)": {
                          marginBottom: 0,
                        },
                      }}
                    >
                      <Box
                        sx={{
                          backgroundColor:
                            pembubaranStep + 1 > item.id || isPembubaranSuccess
                              ? "var(--primary-color)"
                              : "#FFF",
                          flexShrink: 0,
                          padding: 0,
                          margin: 0,
                          width: "12px",
                          height: "12px",
                          border:
                            pembubaranStep + 1 >= item.id || isPembubaranSuccess
                              ? "1px solid var(--primary-color)"
                              : "1px solid #DADADA",
                          borderRadius: "3px",
                          position: "relative",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        {(pembubaranStep + 1 >= item.id ||
                          isPembubaranSuccess) && (
                          <CheckIcon
                            sx={{
                              fontSize: "10px",
                              color: "#FFF",
                            }}
                          />
                        )}
                      </Box>
                      <Typography
                        fontSize="12px"
                        color={
                          pembubaranStep + 1 >= item.id || isPembubaranSuccess
                            ? "var(--primary-color)"
                            : "#DADADA"
                        }
                        fontWeight={
                          pembubaranStep + 1 > item.id || isPembubaranSuccess
                            ? "500 !important"
                            : "400 !important"
                        }
                      >
                        {item.label}
                      </Typography>
                    </Box>
                  ))}
                </Box>

                {(liquidationState.roQuery || liquidationState.roApproval) && (
                  <Box
                    sx={{
                      mt: 1,
                      background: "#fff",
                      boxShadow: "0px 12px 12px 0px #EAE8E866",
                      borderRadius: "15px",
                      padding: "20px 12px 1px 12px",
                      display: {
                        xs: "none",
                        lg: "block",
                      },
                    }}
                  >
                    <Typography
                      fontWeight="400 !important"
                      fontSize="12px"
                      color="var(--primary-color)"
                      mb="15px"
                    >
                      {t("remarks")}
                    </Typography>
                    <Typography
                      fontWeight="400 !important"
                      fontSize="12px"
                      mb="35px"
                    >
                      {liquidationState.roQuery?.note ??
                        liquidationState.roApproval?.note ??
                        "-"}
                    </Typography>
                  </Box>
                )}
              </Box>
            )}

            {/* PENYATA TAHUNAN STEP */}
            {((location.pathname.includes("senarai/penyataTahunan") &&
              location.pathname.split("senarai/penyataTahunan")[1]?.length >
                0) ||
              (location.pathname.includes("view/penyataTahunan") &&
                location.pathname.split("view/penyataTahunan")[1]?.length >
                  0)) && (
              <Box
                sx={{
                  position: "sticky",
                  top: "30px",
                  width: "100%",
                  marginTop: "10px",
                  padding: "19px 10px",
                  borderRadius: "15px",
                  background: "#FFF",
                  boxShadow: " 0px 12px 12px 0px #EAE8E8CC",
                }}
              >
                <Typography
                  fontSize="12px"
                  fontWeight="400 !important"
                  color="var(--primary-color)"
                  marginBottom={2}
                >
                  {t("langkahPenyataTahunan")}
                </Typography>

                {penyataTahunanStepList.map((item) => (
                  <Box
                    key={item.label}
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 1,
                      marginBottom: "15px",
                      "&:nth-last-of-type(1)": {
                        marginBottom: 0,
                      },
                      cursor: "pointer", // Adding cursor to indicate it's clickable
                    }}
                    onClick={() => handleClick(item)} // Your click handler
                  >
                    <Box
                      sx={{
                        backgroundColor:
                          penyataTahunanStep > item.id ||
                          isPenyataTahunanSuccess
                            ? "var(--primary-color)"
                            : "#FFF",
                        flexShrink: 0,
                        padding: 0,
                        margin: 0,
                        width: "12px",
                        height: "12px",
                        border:
                          penyataTahunanStep >= item.id ||
                          isPenyataTahunanSuccess
                            ? "1px solid var(--primary-color)"
                            : "1px solid #DADADA",
                        borderRadius: "3px",
                        position: "relative",
                      }}
                    >
                      {(penyataTahunanStep >= item.id ||
                        isPenyataTahunanSuccess) && (
                        <CheckIcon
                          sx={{
                            fontSize: "10px",
                            color: "#FFF",
                            position: "absolute",
                            top: "50%",
                            left: "50%",
                            transform: "translate(-50%, -50%)",
                          }}
                        />
                      )}
                    </Box>
                    <Typography
                      fontSize="12px"
                      color={
                        penyataTahunanStep >= item.id || isPenyataTahunanSuccess
                          ? "var(--primary-color)"
                          : "#DADADA"
                      }
                      fontWeight={
                        penyataTahunanStep > item.id || isPenyataTahunanSuccess
                          ? "500 !important"
                          : "400 !important"
                      }
                    >
                      {item.label}
                    </Typography>
                  </Box>
                ))}
              </Box>
            )}
          </Grid>
        )}

        {/* <Box sx={{ pl: 3, flexGrow: 1 }}> */}
        {!location.pathname.includes("papar") ? (
          <Grid item xs={10}>
            <Box
              sx={{
                backgroundColor: "white",
                padding: "18px 16px",
                marginBottom: 2,
                borderRadius: "14px",
              }}
            >
              <Box
                sx={{
                  backgroundColor: "var(--primary-color)",
                  padding: "27px 41px",
                  borderRadius: "14px",
                  position: "relative",
                }}
              >
                {(location.pathname.includes("cawangan/branch-Info") ||
                  location.pathname.includes("cawangan/view/mesyuarat")) &&
                branchAmendRedux?.id ? (
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: "bold",
                      color: "white",
                      fontSize: "18px",
                    }}
                  >
                    <>
                      {branchAmendRedux?.currentBranchName}
                      <br />
                      {branchAmendRedux?.branchNo
                        ? branchAmendRedux?.branchNo
                        : branchAmendRedux?.branchApplicationNo
                        ? branchAmendRedux?.branchApplicationNo
                        : "-"}
                    </>
                  </Typography>
                ) : !location.pathname.includes("senarai/cawangan") ||
                  !branchData ? (
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: "bold",
                      color: "white",
                      fontSize: "18px",
                    }}
                  >
                    {loadingSociety ? (
                      "Loading..."
                    ) : (
                      <>
                        {societyData?.societyName}
                        <br />
                        {societyData?.societyNo
                          ? societyData?.societyNo
                          : societyData?.applicationNo}
                      </>
                    )}
                  </Typography>
                ) : location.pathname.endsWith("senarai/cawangan") ? (
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: "bold",
                      color: "white",
                      fontSize: "18px",
                    }}
                  >
                    {loadingSociety ? (
                      "Loading..."
                    ) : (
                      <>
                        {societyData?.societyName}
                        <br />
                        {societyData?.societyNo
                          ? societyData?.societyNo
                          : societyData?.applicationNo}
                      </>
                    )}
                  </Typography>
                ) : (
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: "bold",
                      color: "white",
                      fontSize: "18px",
                    }}
                  >
                    {isLoadingBranch ? (
                      "Loading..."
                    ) : (
                      <>
                        {branchData?.name}
                        <br />
                        {branchData?.branchNo ??
                          branchData?.branchApplicationNo ??
                          "-"}
                      </>
                    )}
                  </Typography>
                )}
                <Box
                  sx={{
                    width: "94px",
                    position: "absolute",
                    right: "37px",
                    bottom: "-20px",
                  }}
                >
                  <img src="/ornament.svg" alt="ornament" width="100%" />
                </Box>
              </Box>
            </Box>
            {location.pathname.includes("senarai/ajk") && <AjkNavigationTabs />}
            {location.pathname.includes("senarai/cawangan/view/ajk") && (
              <CawanganAjkNavigationTabs />
            )}
            <SenaraiContext.Provider
              value={{
                societyDetail: societyData,
                isLoadingSocietyDetail: loadingSociety,
                isPembubaranSuccess: isPembubaranSuccess,
                pembubaranStep: pembubaranStep,
                handleNextPembubaran: handleNextPembubaran,
                handleBackPembubaran: handleBackPembubaran,
                handleResetPembubaran: handleResetPembubaran,
                setPembubaranSuccess: setPembubaranSuccess,

                penyataTahunanStep: penyataTahunanStep,
                handleNextPenyataTahunan: handleNextPenyataTahunan,
                handleBackPenyataTahunan: handleBackPenyataTahunan,
                handleResetPenyataTahunan: handleResetPenyataTahunan,
                setPenyataTahunanSuccess: setPenyataTahunanSuccess,
              }}
            >
              {children}
            </SenaraiContext.Provider>
            {/* </Box> */}
          </Grid>
        ) : (
          <Grid item xs={12}>
            <Box
              sx={{
                backgroundColor: "white",
                padding: "18px 16px",
                marginBottom: 2,
                borderRadius: "14px",
              }}
            >
              <Box
                sx={{
                  backgroundColor: "var(--primary-color)",
                  padding: "27px 41px",
                  borderRadius: "14px",
                  position: "relative",
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontWeight: "bold",
                    color: "white",
                    fontSize: "18px",
                  }}
                >
                  {isLoadingBranch ? (
                    "Loading..."
                  ) : (
                    <>
                      {branchData?.name}
                      <br />
                      {branchData?.branchNo ??
                        branchData?.branchApplicationNo ??
                        "-"}
                    </>
                  )}
                </Typography>
                <Box
                  sx={{
                    width: "94px",
                    position: "absolute",
                    right: "37px",
                    bottom: "-20px",
                  }}
                >
                  <img src="/ornament.svg" alt="ornament" width="100%" />
                </Box>
              </Box>
            </Box>
            <SenaraiContext.Provider
              value={{
                societyDetail: societyData,
                isLoadingSocietyDetail: loadingSociety,
                isPembubaranSuccess: isPembubaranSuccess,
                pembubaranStep: pembubaranStep,
                handleNextPembubaran: handleNextPembubaran,
                handleBackPembubaran: handleBackPembubaran,
                handleResetPembubaran: handleResetPembubaran,
                setPembubaranSuccess: setPembubaranSuccess,

                penyataTahunanStep: penyataTahunanStep,
                handleNextPenyataTahunan: handleNextPenyataTahunan,
                handleBackPenyataTahunan: handleBackPenyataTahunan,
                handleResetPenyataTahunan: handleResetPenyataTahunan,
                setPenyataTahunanSuccess: setPenyataTahunanSuccess,
              }}
            >
              {children}
            </SenaraiContext.Provider>
          </Grid>
        )}
      </Grid>
      {/* </Box> */}
    </Box>
  );
};

export default SenaraiLayout;
