import { useMemo, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useDispatch } from "react-redux";
import { handlePrev } from "@/redux/secretaryBranchReformReducer";
import { useFormContext } from "react-hook-form";
import { useSecretaryBranchReformContext } from "../Provider";
import { MeetingTypeOption, useQuery, formatDate } from "@/helpers";

import {
  Box,
  Typography,
  Checkbox,
  SelectChangeEvent,
  Link,
} from "@mui/material";
import {
  PageButton,
  NavButton,
  FormFieldRow,
  Label,
  TextFieldController,
  SelectFieldController,
  DatePickerController,
  ToggleButtonController,
} from "@/components";

import { IMeetingList, IApiResponse } from "@/types";
import dayjs from "dayjs";

const MeetingForm = () => {
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const { id: societyId, branchId } = useParams();
  const { setValue, control, watch } = useFormContext();
  const [availableDateList, setAvailableDateList] = useState<string[]>([]);
  const { isViewOnly, isAgreed, handleIsAgreed } =
    useSecretaryBranchReformContext();

  const isSuccess = useSelector(
    (state: RootState) => state.secretaryBranchReform.isSuccess
  );

  const isMyLanguage = i18n.language === "my";

  const selectedReason = watch("reasonOfChange");
  const meetingDate = watch("meetingDate");
  const meetingType = watch("meetingType");

  const reasonOptions = [
    {
      value: "Lantikan baru",
      label: "Lantikan baru",
    },
    {
      value: "Setiausaha lama telah letak jawatan/ meninggal dunia/ dipecat",
      label: "Setiausaha lama telah letak jawatan/ meninggal dunia/ dipecat",
    },
    {
      value: "Lain-lain",
      label: "Lain-lain",
    },
  ];

  const {
    data: meetingListRest,
    refetch: fetchMeetingList,
    isLoading: isFetchingMeetingList,
  } = useQuery<IApiResponse<IMeetingList[]>>({
    url: `society/${societyId}/branches/${branchId}/meetings`,
    autoFetch: false,
    filters: [
      {
        field: "meetingType",
        operator: "eq",
        value: meetingType,
      },
      {
        field: "meetingDate",
        operator: "eq",
        value: formatDate(meetingDate, "YYYY-MM-DD"),
      },
    ],
  });

  const meetingList = meetingListRest?.data?.data ?? [];

  const meetingListOptions = useMemo(
    () =>
      meetingList.map((meeting) => {
        const matchedMeeting = MeetingTypeOption.find(
          (option) => option.value === Number(meeting.meetingType)
        );
        return {
          label: matchedMeeting ? matchedMeeting.label : "Unknown Type Meeting",
          value: meeting.id,
        };
      }),
    [meetingList]
  );

  const getSelectedMeeting = (id: number): IMeetingList | null => {
    if (!meetingList.length) return null;

    const selectedMeeting = meetingList.find((meeting) => meeting.id === id);

    return selectedMeeting ?? null;
  };

  const disabledState = isViewOnly || isSuccess;
  // const requiredState = !disabledState;

  const handleMeetingChange = (e: SelectChangeEvent<any>) => {
    const id = e.target.value;
    const selectedMeeting = getSelectedMeeting(id);

    if (selectedMeeting) {
      setValue("memberCount", selectedMeeting.memberCount);
      setValue("ajkCount", selectedMeeting.ajkCount);
    }
  };

  useEffect(() => {
    if (meetingDate && meetingType) {
      setValue("meetingId", "");
      fetchMeetingList();
    }
  }, [meetingDate, meetingType]);

  const { data: branchMeetingData, isLoading: branchMeetingDataIsLoading } =
    useQuery({
      url: `society/meeting/findByBranchId/${branchId}`,
      onSuccess: (data) => {
        const meetings = data?.data?.data || [];
        const availableList = meetings.map((item: any) => {
          return item.meetingDate;
        });
        setAvailableDateList(availableList);
      },
    });

  return (
    <>
      <Box
        sx={{
          py: 1,
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: "12px",
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "500" }}>
              {t("peringatan")} :
            </span>
            <br />
            {isMyLanguage
              ? " Sila isi maklumat mesyuarat pembaharuan setiausaha cawangan jika Jenis Mesyuarat tiada dalam pilihan atau klik"
              : "Please fill in the branch secretary renewal meeting information if the Meeting Type is not in the options or click"}{" "}
            <span style={{ color: "var(--primary-color)", cursor: "pointer" }}>
              <Link href={`/pertubuhan/society/${societyId}/senarai/mesyuarat`}>
                {isMyLanguage ? "disini" : "here"}
              </Link>
            </span>{" "}
            {isMyLanguage
              ? "bagi kemasukan maklumat mesyuarat terlibat."
              : "for entering information about the meeting involved."}
          </Typography>
        </Box>
      </Box>

      <Box
        sx={{
          backgroundColor: "white",
          py: 1,
          borderRadius: "15px",
        }}
      >
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {isMyLanguage
              ? "Maklumat Mesyuarat Pembaharuan Setiausaha Cawangan"
              : "Branch Secretary Renewal Meeting Information"}
          </Typography>

          <FormFieldRow
            label={<Label text={t("meetingType")} required />}
            value={
              <SelectFieldController
                name="meetingType"
                control={control}
                options={MeetingTypeOption.filter((item: any) => item.id !== 1)}
                placeholder={t("selectPlaceholder")}
                disabled={disabledState}
                onChange={handleMeetingChange}
                required
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("meetingDate")} required />}
            value={
              <DatePickerController
                name="meetingDate"
                shouldDisableDate={(date) => {
                  if (availableDateList.length === 0) {
                    return false;
                  }
                  const newList = availableDateList.map((d: any) =>
                    dayjs(d, "YYYY-MM-DD")
                  );
                  return !newList.some((allowedDate) =>
                    allowedDate.isSame(date, "day")
                  );
                }}
                control={control}
                format="YYYY-MM-DD"
                formatValue="YYYY-MM-DD"
                disabled={!meetingType || branchMeetingDataIsLoading}
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("meetingList")} required />}
            value={
              <SelectFieldController
                name="meetingId"
                control={control}
                options={meetingListOptions}
                placeholder={
                  isFetchingMeetingList ? t("loading") : t("selectPlaceholder")
                }
                disabled={
                  disabledState ||
                  !meetingType ||
                  !meetingDate ||
                  isFetchingMeetingList
                }
                onChange={handleMeetingChange}
                required
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("bilanganAhliYangHadir")} required />}
            value={
              <TextFieldController
                control={control}
                name="memberCount"
                placeholder={`${t("example")} : 10 ${t("orang")}`}
                disabled
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("bilanganPemegangJawatan")} required />}
            value={
              <TextFieldController
                control={control}
                name="ajkCount"
                placeholder={`${t("example")} : 10 ${t("orang")}`}
                disabled
              />
            }
          />
        </Box>
      </Box>

      <Box
        sx={{
          backgroundColor: "white",
          py: 1,
          borderRadius: "15px",
        }}
      >
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {isMyLanguage
              ? "Tujuan Pembaharuan Setiausaha Cawangan Baru"
              : "Purpose of The New Branch Secretary Reform"}
          </Typography>

          <ToggleButtonController
            name="reasonOfChange"
            control={control}
            options={reasonOptions}
            sx={{
              gap: 3,
            }}
            sxButton={{
              width: "15px",
              height: "15px",
            }}
            sxLabel={{
              fontSize: "14px",
            }}
            disabled={disabledState}
            required
          />
          {selectedReason === "Lain-lain" && (
            <TextFieldController
              control={control}
              name="otherReason"
              multiline
              rows={4}
              sx={{ mt: 1, marginLeft: "30px", maxWidth: "478px" }}
              required={selectedReason === "Lain-lain"}
            />
          )}
        </Box>
      </Box>

      <Box
        sx={{
          backgroundColor: "white",
          p: 2,
          mt: 1,
          borderRadius: "15px",
          border: "1px solid #D9D9D9",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Checkbox
            checked={isAgreed}
            disabled={isSuccess}
            onChange={(e) => handleIsAgreed(e.target.checked)}
          />
          <Typography
            sx={{
              fontSize: "12px",
              color: "#666666",
              fontWeight: "400 !important",
            }}
          >
            {t("checkSetiausahaBaruCawangan")}
          </Typography>
        </Box>
      </Box>

      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          alignItems: "center",
          gap: 1,
          mt: 3,
          mb: 3,
        }}
      >
        <NavButton onClick={() => dispatch(handlePrev())} disabled={isSuccess}>
          {t("back")}
        </NavButton>

        <PageButton disabled={isSuccess} onClick={() => dispatch(handlePrev())}>
          1
        </PageButton>
        <PageButton active>2</PageButton>
      </Box>
    </>
  );
};

export default MeetingForm;
