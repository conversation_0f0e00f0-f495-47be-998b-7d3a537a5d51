import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useDispatch } from "react-redux";
import { handlePrev } from "@/redux/secretaryBranchReformReducer";
import { useFormContext } from "react-hook-form";
import { useSecretaryBranchReformContext } from "../Provider";
import { MeetingTypeOption, useQuery, formatDate } from "@/helpers";

import {
  Box,
  Typography,
  Checkbox,
  Link,
  CircularProgress,
} from "@mui/material";
import {
  PageButton,
  NavButton,
  FormFieldRow,
  Label,
  TextFieldController,
  SelectFieldController,
  DatePickerController,
  ToggleButtonController,
  CustomPickersDay,
} from "@/components";

import { IMeetingList, IApiResponse } from "@/types";
import dayjs from "dayjs";
import { Meeting } from "@/pages/pertubuhan/pernyata-tahunan/interface";
import { useCustom } from "@refinedev/core";
import { API_URL } from "@/api";
import MessageDialog from "@/components/dialog/message";

const MeetingForm = () => {
  const dispatch = useDispatch();

  const navigate = useNavigate();

  const [availableDateList, setAvailableDateList] = useState<string[]>([]);
  const [savedMeetingDate, setSavedMeetingDate] = useState("");
  const [filteredMeetings, setFilteredMeeting] = useState<Meeting[]>();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const { t, i18n } = useTranslation();
  const { id: societyId, branchId } = useParams();
  const { setValue, control, watch, getValues, reset } = useFormContext();
  const { isViewOnly, isAgreed, handleIsAgreed } =
    useSecretaryBranchReformContext();

  const isSuccess = useSelector(
    (state: RootState) => state.secretaryBranchReform.isSuccess
  );

  const isMyLanguage = i18n.language === "my";

  const meetingDateExist = getValues("meetingDate");
  const selectedReason = watch("reasonOfChange");
  const meetingDate = watch("meetingDate");
  const meetingType = watch("meetingType");

  const reasonOptions = [
    {
      value: "Lantikan baru",
      label: "Lantikan baru",
    },
    {
      value: "Setiausaha lama telah letak jawatan/ meninggal dunia/ dipecat",
      label: "Setiausaha lama telah letak jawatan/ meninggal dunia/ dipecat",
    },
    {
      value: "Lain-lain",
      label: "Lain-lain",
    },
  ];

  const {
    data: meetingListRest,
    refetch: fetchMeetingList,
    isLoading: isFetchingMeetingList,
  } = useCustom({
    url: `${API_URL}/society/${societyId}/branches/${branchId}/meetings`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      filters: [
        {
          field: "meetingType",
          value: meetingType,
          operator: "eq",
        },
        {
          field: "meetingDate",
          value: meetingDate,
          operator: "eq",
        },
      ],
    },
    queryOptions: {
      enabled: !!meetingType,
      retry: false,
      cacheTime: 0,
      onSuccess(data) {
        const responseData = data?.data?.data?.[0];
        setValue("memberCount", responseData.memberCount);
        setValue("ajkCount", responseData.ajkCount);
      },
    },
  });

  const meetingList = meetingListRest?.data?.data ?? [];

  const getSelectedMeeting = (id: string | number): IMeetingList | null => {
    if (!meetingList.length) return null;
    const selectedMeeting = meetingList.find(
      (meeting: any) => meeting.id === id
    );

    return selectedMeeting ?? null;
  };

  const disabledState = isViewOnly || isSuccess;
  // const requiredState = !disabledState;

  const handleMeetingChange = (id: any) => {
    const selectedMeeting = getSelectedMeeting(id);
    if (selectedMeeting) {
      setValue("memberCount", selectedMeeting.memberCount);
      setValue("ajkCount", selectedMeeting.ajkCount);
    }
  };

  const { data: branchMeetingData, isLoading: branchMeetingDataIsLoading } =
    useQuery({
      url: `society/meeting/findByBranchId/${branchId}`,
      onSuccess: (data) => {
        const meetings = data?.data?.data || [];
        setMeetings(meetings);
        const availableList = meetings
          .filter((item: any) => Number(item.meetingType) !== 1)
          .map((item: any) => item.meetingDate);
        setAvailableDateList(availableList);
      },
    });

  function setMeetingList(meetingList: Meeting[]) {
    if (branchMeetingDataIsLoading) {
      return;
    }
    setFilteredMeeting(meetingList);
    if (meetingList && meetingList.length > 0) {
      setIsDialogOpen(false);
    } else {
      setIsDialogOpen(true);
    }
  }

  function resetChanges() {
    setFilteredMeeting([]);
    setSavedMeetingDate("");
    setValue("meetingId", "");
    setValue("memberCount", "");
    setValue("ajkCount", "");
    setValue("meetingType", "");
  }

  function goMeetingPage() {
    navigate(`../../../mesyuarat`);
  }

  useEffect(() => {
    if (meetingDate) {
      const filteredMeeting = meetings.filter(
        (meeting) =>
          meeting.meetingDate.toString() ===
          dayjs(meetingDate).format("YYYY-MM-DD")
      );
      setMeetingList(filteredMeeting);
    }
  }, [meetingDate, meetings]);

  return (
    <>
      <Box
        sx={{
          py: 1,
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: "12px",
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "500" }}>
              {t("peringatan")} :
            </span>
            <br />
            {isMyLanguage
              ? " Sila isi maklumat mesyuarat pembaharuan setiausaha cawangan jika Jenis Mesyuarat tiada dalam pilihan atau klik"
              : "Please fill in the branch secretary renewal meeting information if the Meeting Type is not in the options or click"}{" "}
            <span style={{ color: "var(--primary-color)", cursor: "pointer" }}>
              <Link href={`/pertubuhan/society/${societyId}/senarai/mesyuarat`}>
                {isMyLanguage ? "disini" : "here"}
              </Link>
            </span>{" "}
            {isMyLanguage
              ? "bagi kemasukan maklumat mesyuarat terlibat."
              : "for entering information about the meeting involved."}
          </Typography>
        </Box>
      </Box>

      <Box
        sx={{
          backgroundColor: "white",
          py: 1,
          borderRadius: "15px",
        }}
      >
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {isMyLanguage
              ? "Maklumat Mesyuarat Pembaharuan Setiausaha Cawangan"
              : "Branch Secretary Renewal Meeting Information"}
          </Typography>

          <FormFieldRow
            label={<Label text={t("meetingDate")} required />}
            value={
              branchMeetingDataIsLoading ? (
                <Box display="flex" justifyContent="center" alignItems="center">
                  <CircularProgress size={20} />
                </Box>
              ) : (
                <DatePickerController
                  name="meetingDate"
                  slots={{
                    day: (dayProps) => (
                      <CustomPickersDay
                        {...dayProps}
                        availableDate={availableDateList}
                      />
                    ),
                  }}
                  control={control}
                  onChange={(date) => {
                    resetChanges();
                    setSavedMeetingDate(date ?? "");
                    setValue("meetingDate", date);

                    const filteredMeeting = meetings.filter(
                      (meeting) =>
                        meeting.meetingDate.toString() ===
                        dayjs(date).format("YYYY-MM-DD")
                    );
                    const finilzedMeetings = filteredMeeting.filter(
                      (meeting) => {
                        return Number(meeting.meetingType) !== 1;
                      }
                    );
                    setMeetingList(finilzedMeetings);
                  }}
                  disabled={isViewOnly}
                />
              )
            }
          />

          {/* <Controller
            name="meetingDate"
            control={control}
            render={({ field }) => {
              return (
                <Input
                  required
                  label={t("meetingDate")}
                  type="date"
                  availableDate={availableDateList}
                  disabled={disabledState}
                  value={meetingDate ? meetingDate : savedMeetingDate}
                  onChange={(e) => {
                    resetChanges();
                    setSavedMeetingDate(e.target.value);
                    setValue("meetingDate", e.target.value);

                    const filteredMeeting = meetings.filter(
                      (meeting) =>
                        meeting.meetingDate.toString() ===
                        dayjs(e.target.value).format("YYYY-MM-DD")
                    );
                    const finilzedMeetings = filteredMeeting.filter(
                      (meeting) => {
                        return Number(meeting.meetingType) !== 1;
                      }
                    );
                    setMeetingList(finilzedMeetings);
                  }}
                />
              );
            }}
          /> */}

          {filteredMeetings && filteredMeetings.length > 0 ? (
            <>
              <FormFieldRow
                label={<Label text={t("meetingList")} required />}
                value={
                  <SelectFieldController
                    control={control}
                    name="meetingId"
                    onChange={(e) => {
                      const selectedMeeting = meetings.find(
                        (meeting) => meeting.id === e.target.value
                      );
                      setValue("meetingType", selectedMeeting?.meetingType);
                      handleMeetingChange(selectedMeeting?.id);
                    }}
                    options={filteredMeetings.map((meeting) => ({
                      value: meeting.id,
                      label: `${
                        MeetingTypeOption.filter(
                          (item: any) => item.id !== 1
                        ).find(
                          (option) =>
                            Number(option.value) === Number(meeting.meetingType)
                        )?.label ?? "-"
                      } (${dayjs(meeting.meetingDate).format("DD-MM-YYYY")})`,
                    }))}
                    required
                    disabled={disabledState}
                  />
                }
              />
            </>
          ) : null}

          {meetingType && !isFetchingMeetingList ? (
            <>
              <FormFieldRow
                label={<Label text={t("bilanganAhliYangHadir")} required />}
                value={
                  <TextFieldController
                    control={control}
                    name="memberCount"
                    placeholder={`${t("example")} : 10 ${t("orang")}`}
                    disabled
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("bilanganPemegangJawatan")} required />}
                value={
                  <TextFieldController
                    control={control}
                    name="ajkCount"
                    placeholder={`${t("example")} : 10 ${t("orang")}`}
                    disabled
                  />
                }
              />
            </>
          ) : (
            ""
          )}
        </Box>
      </Box>

      <Box
        sx={{
          backgroundColor: "white",
          py: 1,
          borderRadius: "15px",
        }}
      >
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {isMyLanguage
              ? "Tujuan Pembaharuan Setiausaha Cawangan Baru"
              : "Purpose of The New Branch Secretary Reform"}
          </Typography>

          <ToggleButtonController
            name="reasonOfChange"
            control={control}
            options={reasonOptions}
            sx={{
              gap: 3,
            }}
            sxButton={{
              width: "15px",
              height: "15px",
            }}
            sxLabel={{
              fontSize: "14px",
            }}
            disabled={disabledState}
            required
          />
          {selectedReason === "Lain-lain" && (
            <TextFieldController
              control={control}
              name="otherReason"
              multiline
              rows={4}
              sx={{ mt: 1, marginLeft: "30px", maxWidth: "478px" }}
              required={selectedReason === "Lain-lain"}
            />
          )}
        </Box>
      </Box>

      <Box
        sx={{
          backgroundColor: "white",
          p: 2,
          mt: 1,
          borderRadius: "15px",
          border: "1px solid #D9D9D9",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Checkbox
            checked={isAgreed}
            disabled={isSuccess}
            onChange={(e) => handleIsAgreed(e.target.checked)}
          />
          <Typography
            sx={{
              fontSize: "12px",
              color: "#666666",
              fontWeight: "400 !important",
            }}
          >
            {t("checkSetiausahaBaruCawangan")}
          </Typography>
        </Box>
      </Box>

      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          alignItems: "center",
          gap: 1,
          mt: 3,
          mb: 3,
        }}
      >
        <NavButton onClick={() => dispatch(handlePrev())} disabled={isSuccess}>
          {t("back")}
        </NavButton>

        <PageButton disabled={isSuccess} onClick={() => dispatch(handlePrev())}>
          1
        </PageButton>
        <PageButton active>2</PageButton>
      </Box>

      <MessageDialog
        open={isDialogOpen}
        onClickFunction={() => goMeetingPage()}
        buttonText={t("Viewlist")}
        onClose={() => setIsDialogOpen(false)}
        message={t("createMeetingReminder")}
      />
    </>
  );
};

export default MeetingForm;
