import { Route, Outlet } from "react-router-dom";
import ChatbotPage from "../pages/chatbot/ChatbotPage";
import SpeechInputPage from "../pages/chatbot/SpeechInputPage";
import ChatbotFullPage from "../pages/chatbot/ChatbotFullPage";
import { registerRoutes } from "../helpers/routeDetector";
import { RouteGuard } from "../components/RouteGuard";

// Layout component to wrap all chatbot routes with protection
const ChatbotLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Outlet />
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example: '/chatbot': 'shared',
  // Add your route registrations here
});

export const chatbotRoute = {
  resources: [
    {
      name: "chatbot",
      list: "/chatbot",
    },
    {
      name: "speech-input",
      list: "/speech-input",
    },
    {
      name: "chatbot-full",
      list: "/chatbot-full",
    },
  ],
  routes: (
    <>
      <Route path="/chatbot" element={<ChatbotLayout />}>
        <Route index element={<ChatbotPage />} />
      </Route>
      {/* <Route path="/speech-input">
        <Route index element={<SpeechInputPage />} />
      </Route>
      <Route path="/chatbot-full">
        <Route index element={<ChatbotFullPage />} />
      </Route> */}
    </>
  ),
};
