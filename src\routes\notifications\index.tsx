import { Route, Outlet } from "react-router-dom";
import { MainNotificationPage } from "@/pages/dashboard/notification/MainNotificationPage";
import { registerRoutes } from "../../helpers/routeDetector";
import { RouteGuard } from "../../components/RouteGuard";

// Layout component to wrap all notification routes with protection
const NotificationLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === "development"}
  >
    <Outlet />
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example: '/notifikasi': 'shared',
  // Add your route registrations here
  "/notifikasi": "shared",
});

export const notifikasi = {
  resources: [
    {
      name: "notifikasi",
      list: "/notifikasi",
    },
  ],
  routes: (
    <Route path="/notifikasi" element={<NotificationLayout />}>
      <Route index element={<MainNotificationPage />} />
    </Route>
  ),
};
