export interface IMeetingDetail {
  createdBy: string | number;
  createdDate: string;
  modifiedBy: string | number;
  modifiedDate: string;
  id: string | number;
  amendmentId: any;
  societyId: string | number;
  societyNo: string;
  branchId: string;
  branchNo: string;
  statementId: any;
  meetingType: string;
  meetingPurpose: string;
  meetingPlace: string;
  meetingMethod: string;
  platformType: string;
  meetingDate: string;
  meetingTime: string;
  meetingTimeTo: string;
  meetingTimeDurationMinutes: string;
  meetingAddress: string;
  state: any;
  district: any;
  city: any;
  postcode: any;
  totalAttendees: number;
  openingRemarks: any;
  meetingContent: string;
  mattersDiscussed: any;
  otherMatters: any;
  closing: any;
  providedBy: any;
  confirmBy: any;
  meetingMinute: string;
  status: string;
  meetingMemberAttendances: MeetingMemberAttendance[];
  GISInformation: string;
}
export interface MeetingMemberAttendance {
  createdBy: string | number;
  createdDate: string;
  modifiedBy: string | number;
  modifiedDate: string;
  id: string | number;
  meetingId: string | number;
  societyId: string | number;
  societyNo?: string;
  branchId: string;
  branchNo: string;
  meetingDate: number[];
  icNo?: string;
  present: number;
  name: string;
  position: string;
  status: number;
}

export interface IMeetingOptions {
  id: string | number;
  pid: string | number;
  nameEn: string;
  nameBm: string;
  desciption: string;
  status: number;
}

export type IMeetingList = Pick<
  IMeetingDetail,
  "id" | "meetingType" | "meetingDate"
> & {
  memberCount: number;
  ajkCount: number;
};

export interface IMeetingInfo {
  id: string | number
  createdDate: string
  createdBy: string | number
  modifiedDate: string
  modifiedBy: string | number
  pid: string | number
  nameEn: string
  nameBm: string
  desciption: string
  status: number
}

