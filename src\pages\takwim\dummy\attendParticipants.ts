export interface AttendParticipant {
  id: string | number;
  name: string;
  avatar?: string;
  status: 'CONFIRMED' | 'PENDING' | 'CANCELLED';
  dateJoined: string;
  email: string;
  phone?: string;
}

export const attendParticipants: AttendParticipant[] = [
  {
    id: 1,
    name: "<PERSON>",
    avatar: "/avatars/user1.jpg",
    status: "CONFIRMED",
    dateJoined: "2024-01-15T08:30:00Z",
    email: "<EMAIL>",
    phone: "0123456789"
  },
  {
    id: 2,
    name: "<PERSON><PERSON> <PERSON><PERSON>",
    avatar: "/avatars/user2.jpg",
    status: "CONFIRMED",
    dateJoined: "2024-01-15T09:15:00Z",
    email: "<EMAIL>",
    phone: "0123456790"
  },
  {
    id: 3,
    name: "<PERSON>",
    status: "PENDING",
    dateJoined: "2024-01-15T10:00:00Z",
    email: "<EMAIL>"
  },
  {
    id: 4,
    name: "<PERSON>",
    avatar: "/avatars/user4.jpg",
    status: "CONFIRMED",
    dateJoined: "2024-01-15T10:30:00Z",
    email: "<EMAIL>",
    phone: "0123456792"
  },
  {
    id: 5,
    name: "Sarah Abdullah",
    status: "CANCELLED",
    dateJoined: "2024-01-15T11:00:00Z",
    email: "sarah.ab<PERSON><PERSON>@email.com",
    phone: "0123456793"
  }
];
