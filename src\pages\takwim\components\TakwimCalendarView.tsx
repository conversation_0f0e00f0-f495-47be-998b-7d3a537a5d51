import React, { useState, useMemo } from "react";
import { Box, Typography, Paper, Button } from "@mui/material";
import { useTranslation } from "react-i18next";
import dayjs from "../../../helpers/dayjs";
import { Dayjs } from "dayjs";
import { Calendar } from "../../../components/calendar/Main";
import { DayCalendarSkeleton } from "@mui/x-date-pickers";
import { PickersDay, PickersDayProps } from "@mui/x-date-pickers";
import { useNavigate } from "react-router-dom";
import "../takwim-landing.css";
import { useTakwim } from "../../../contexts/takwimProvider";
import { IEvent } from "@/types/event";
import { Address } from "cluster";
import { API_URL } from "@/api";
import { useCustom } from "@refinedev/core";
import TakwimSelect from "@/components/input/TakwimSelect";
import { SelectChangeEvent } from "@mui/material/Select";

interface CalendarViewProps {
  events?: IEvent[];
}

export const TakwimCalendarView: React.FC<CalendarViewProps> = ({
  events: propEvents = [],
}) => {
  const { t } = useTranslation();
  const [loading] = useState(false);
  const [viewType, setViewType] = useState("month");
  const [selectedFilterState, setSelectedFilterState] = useState<number | null>(
    null
  );
  const navigate = useNavigate();
  const { selectedMonthYear } = useTakwim();

  const { data: addressList, isLoading: isAddressLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
      },
    },
  });
  const addressData: Address[] = addressList?.data?.data || [];
  const stateOptions = addressData
    .filter((item: any) => item.pid === 152)
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));

  // Use either provided events or dummy events
  const events = propEvents;

  // Filter events based on selected state
  const filteredEvents = useMemo(() => {
    if (!selectedFilterState) return propEvents;

    return propEvents?.filter((event) => {
      // Check if event's state includes the selected state
      const eventStates = Array.isArray(event.state)
        ? event.state
        : [event.state];
      const hasSelectedState = eventStates.some(
        (state) => Number(state) === selectedFilterState
      );

      // Also check stateAddress if available
      const hasSelectedStateAddress =
        event.stateAddress === selectedFilterState;

      return hasSelectedState || hasSelectedStateAddress;
    });
  }, [propEvents, selectedFilterState]);

  // Handle state change
  const handleStateChange = (e: SelectChangeEvent<string>) => {
    const value = e.target.value;
    setSelectedFilterState(value ? Number(value) : null);
  };

  // Generate array of months for the current year
  const currentYear = dayjs().year();
  const monthsArray = Array.from({ length: 12 }, (_, i) =>
    dayjs().year(currentYear).month(i).startOf("month").format("YYYY-MM-DD")
  );

  const handleViewChange = (_event: React.SyntheticEvent, newValue: string) => {
    setViewType(newValue);
  };

  const handleEventClick = (eventId: string) => {
    navigate(`activity/${eventId}`);
  };

  const renderDay = (
    day: Dayjs,
    selectedDays: Array<Dayjs | null>,
    pickersDayProps: PickersDayProps<Dayjs>
  ) => {
    const eventsForDay =
      filteredEvents?.filter((event) =>
        dayjs(event.eventStartDate).isSame(day, "day")
      ) || [];

    // Check if the day is in the current month
    const isCurrentMonth = day.isSame(selectedMonthYear, "month");

    return (
      <Box
        sx={{
          width: "100%",
          height: "auto",
          aspectRatio: "1/1",
          position: "relative",
          "&:hover": {
            borderBottom: "2px solid #0CA6A6",
          },
        }}
      >
        <PickersDay {...pickersDayProps} />

        {/* Custom overlay for events */}
        <Box
          sx={{
            border: "1px solid #f0f0f0",
            minHeight: "100%",
            width: "100%",
            display: "flex",
            flexDirection: "column",
            position: "absolute",
            top: 0,
            left: 0,
            padding: "4px",
          }}
        >
          <Box
            sx={{
              display: "grid",
              mt: "auto",
              mb: "auto",
              width: "100%",
            }}
          >
            <Typography
              sx={{
                fontSize: "1rem",
                color: day.isSame(dayjs(), "day")
                  ? "#0CA6A6"
                  : !isCurrentMonth
                  ? "#00000040" // Light gray for dates outside current month
                  : "inherit",
                // fontWeight: day.isSame(dayjs(), "day") ? 800 : 400,
                textAlign: "center",
              }}
            >
              {day.date()}
            </Typography>

            {/* Events list */}
            {eventsForDay.length > 0 && (
              <Box
                sx={{
                  width: "100%",
                  display: "flex",
                  flexDirection: "column",
                  gap: 0.5,
                  px: 0.5,
                  alignItems: "center",
                }}
              >
                {eventsForDay.slice(0, 2).map((event) => (
                  <Button
                    key={event.id}
                    onClick={() => handleEventClick(event.eventNo)}
                    sx={{
                      fontSize: "0.65rem",
                      backgroundColor:
                        event.visibility == "PUBLIC" ? "#FF9B9B" : "#0CA6A6",
                      width: "120px", // required to limit width
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      textTransform: "none",

                      color: "#fff",
                      borderRadius: "4px",
                      padding: "1px 4px",
                      textAlign: "center",
                      minHeight: "20px",
                      maxHeight: "20px",
                      maxWidth: "100%",
                      "&:hover": {
                        backgroundColor:
                          event.visibility == "PUBLIC" ? "#ff8080" : "#3ab0b0",
                      },
                    }}
                    title={event.eventName}
                  >
                    <span
                      style={{
                        display: "block",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        width: "100%",
                      }}
                    >
                      {event.eventName}
                    </span>
                  </Button>
                ))}

                {eventsForDay.length > 2 && (
                  <Button
                    className="btn-calendar"
                    onClick={() => navigate("/takwim/activity")}
                    sx={{
                      border: "1.5px solid #dadada99",
                      fontSize: "0.65rem",
                      color: "#0CA6A6",
                      textAlign: "center",
                      width: "fit-content",
                      cursor: "pointer",
                      minWidth: "100%",
                      padding: "1px 4px",
                      "&:hover": {
                        backgroundColor: "rgba(65, 195, 195, 0.08)",
                      },
                    }}
                  >
                    Selanjutnya
                  </Button>
                )}
              </Box>
            )}
          </Box>
        </Box>
      </Box>
    );
  };

  return (
    <Paper
      elevation={1}
      sx={{
        border: "1px solid #dadada99",
        borderRadius: "1.25rem",
        padding: "1.5rem",
        boxShadow: "0 0.75rem 0.75rem 0 #EAE8E866",
      }}
    >
      <Typography
        variant="body1"
        sx={{
          color: "primary.main",
          mb: 4,
          textAlign: "start",
          maxWidth: "800px",
          // mx: "auto",
        }}
      >
        {t("takwim")}
      </Typography>
      <Box sx={{ display: "flex", gap: 2, mb: 3 }}>
        <Box>
          <TakwimSelect
            label=""
            value={selectedFilterState || ""}
            onChange={handleStateChange}
            options={stateOptions}
            placeholder={t("state")}
            sx={{
              width: "400px",
            }}
          />
        </Box>
        <Box
          sx={{
            display: "flex",
            mb: 3,
            border: "1px solid #e0e0e0",
            padding: "3px",
            borderRadius: "8px",
            width: "fit-content",
            marginLeft: "auto",
          }}
        >
          <Button
            onClick={() =>
              handleViewChange({} as React.SyntheticEvent, "month")
            }
            sx={{
              textTransform: "none",
              fontWeight: 400,
              color: viewType === "month" ? "#fff" : "#666",
              bgcolor: viewType === "month" ? "#0CA6A6" : "transparent",
              borderRadius: viewType === "month" ? "5px" : "0",
              "&:hover": {
                bgcolor: viewType === "month" ? "#0CA6A6" : "#f5f5f5",
              },
              px: 3,
              py: 1,
              minWidth: "100px",
            }}
          >
            {t("month")}
          </Button>
          <Button
            onClick={() => handleViewChange({} as React.SyntheticEvent, "year")}
            sx={{
              textTransform: "none",
              fontWeight: 400,
              color: viewType === "year" ? "#fff" : "#666",
              bgcolor: viewType === "year" ? "#0CA6A6" : "transparent",
              borderRadius: viewType === "year" ? "5px" : "0",
              "&:hover": {
                bgcolor: viewType === "year" ? "#0CA6A6" : "#f5f5f5",
              },
              px: 3,
              py: 1,
              minWidth: "100px",
            }}
          >
            {t("year")}
          </Button>
        </Box>
      </Box>

      {viewType === "year" && (
        <Box className="takwim-calendar-container">
          {monthsArray.map((refDate, index) => (
            <Box key={`${refDate}-${index}`} className="takwim-month-calendar">
              <Calendar
                className="takwim-calendar-custom"
                loading={loading}
                views={["day"]}
                renderLoading={() => <DayCalendarSkeleton />}
                referenceDate={dayjs(refDate)}
                slotProps={{
                  previousIconButton: {
                    hidden: true,
                  },
                  nextIconButton: {
                    hidden: true,
                  },
                  day: {
                    sx: {
                      fontSize: ".75rem",
                    },
                  },
                }}
                sx={{
                  width: "100%",
                  "& .MuiPickersCalendarHeader-label": {
                    fontSize: "0.9rem",
                    fontWeight: "bold",
                  },
                }}
              />
            </Box>
          ))}
        </Box>
      )}

      {viewType === "month" && (
        <Box>
          <Calendar
            className="takwim-calendar-custom"
            loading={loading}
            value={selectedMonthYear}
            views={["day"]}
            showDaysOutsideCurrentMonth
            displayWeekNumber={false}
            fixedWeekNumber={5}
            dayOfWeekFormatter={(day) => {
              const dayNames = [
                "Ahad",
                "Isnin",
                "Selasa",
                "Rabu",
                "Khamis",
                "Jumaat",
                "Sabtu",
              ];
              const dayNumber = (day as Dayjs).day();
              return dayNames[dayNumber];
            }}
            slots={{
              day: (props) =>
                renderDay(
                  dayjs(props.day as Date),
                  [
                    props.selected
                      ? dayjs(props.selected as unknown as Date)
                      : null,
                  ],
                  props as PickersDayProps<Dayjs>
                ),
            }}
            slotProps={{
              calendarHeader: {
                sx: { justifyContent: "space-around" },
              },
              previousIconButton: {
                hidden: true,
              },
              nextIconButton: {
                hidden: true,
              },
            }}
            sx={{
              margin: 0,
              width: "100%",
              borderRadius: "0.5rem",
              maxHeight: "none !important",
              "&.MuiDateCalendar-root": {
                height: "auto",
              },
              "& .MuiPickersCalendarHeader-root": {
                paddingLeft: "12px",
                paddingRight: "12px",
              },
              "& .MuiDayCalendar-header": {
                justifyContent: "space-around",
                "& > span": {
                  color: "#666",
                  fontSize: "0.9rem", // Increased from 0.8rem
                  fontWeight: 500,
                  width: "14.28%", // Changed from fixed 45px
                  textAlign: "center",
                },
              },
              "& .MuiDayCalendar-weekContainer": {
                margin: "0",
                minHeight: "120px",
              },
              "& .MuiDayCalendar-monthContainer": {
                height: "auto",
                minHeight: "600px",
              },
              "& .MuiPickersDay-today": {
                border: "none",
                "&::before": {
                  content: '""',
                  position: "absolute",
                  bottom: "2px",
                  left: "50%",
                  transform: "translateX(-50%)",
                  width: "20px",
                  height: "2px",
                  backgroundColor: "#0CA6A6",
                },
              },
              "& .MuiPickersSlideTransition-root": {
                height: "auto",
                minHeight: "110vh",
              },
              "& .MuiButtonBase-root.MuiPickersDay-root": {
                color: "transparent",
                display: "none !important",
                "& .MuiTouchRipple-root": {
                  display: "none",
                },
              },
            }}
          />
        </Box>
      )}
    </Paper>
  );
};
