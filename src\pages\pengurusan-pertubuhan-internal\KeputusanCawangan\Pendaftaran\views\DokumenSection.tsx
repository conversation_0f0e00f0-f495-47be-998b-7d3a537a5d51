import { useParams } from "react-router-dom";
import { DocumentUploadType } from "@/helpers";
import { useKeputusanCawanganPendaftaranContext } from "../KeputusanCawanganPendaftaranProvider";
import FileUploader from "@/components/input/fileUpload";

const DokumenSection = () => {
  const { id } = useParams();
  const { branchDataById } = useKeputusanCawanganPendaftaranContext();
  return (
    id && (
      <FileUploader
        type={DocumentUploadType.BRANCH}
        societyId={branchDataById?.societyId}
        branchId={id}
        disabled={true}
        validTypes={[
          "text/plain",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/msword",
          "application/pdf",
        ]}
      />
    )
  );
};

export default DokumenSection;
