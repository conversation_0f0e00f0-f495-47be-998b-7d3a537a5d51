import { BranchListResponseBodyGet } from "@/types";
import { createAction, createSlice } from "@reduxjs/toolkit";

export interface BranchDataState<
  Data extends BranchListResponseBodyGet = BranchListResponseBodyGet
> {
  data: Data | null;
}

// Define the initial state using that type
const initialState: BranchDataState = {
  data: null,
};
const name = "branchData";

export const setBranchDataRedux = createAction<BranchDataState["data"] | { id: string | number }>(`${name}/setBranchDataRedux`)

export const branchDataSlice = createSlice({
  name: "branchData",
  initialState,
  reducers: {},
  selectors: {
    getBranchDataRedux: (state) => state.data,
  },
  extraReducers: builder =>
    builder
      .addCase(setBranchDataRedux, (state, action) => {
        state.data = action.payload as BranchDataState["data"];
      })
});

export const { getBranchDataRedux } = branchDataSlice.selectors;

export default branchDataSlice.reducer;
