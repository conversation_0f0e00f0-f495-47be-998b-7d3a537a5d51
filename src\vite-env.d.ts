/// <reference types="vite/client" />
/// <reference types="vite-plugin-svgr/client" />

interface ImportMetaEnv {
  readonly VITE_BACKEND_URL: string
  readonly VITE_APP_ENV: 'development' | 'staging' | 'production'
  readonly VITE_AWS_API_KEY: string
  readonly VITE_AWS_API_KEY_REGION: string
  readonly VITE_AWS_PLACE_INDEX: string
  readonly VITE_AWS_MAP_NAME: string
  readonly VITE_GA_MEASUREMENT_ID: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
