import { Outlet } from "react-router-dom";
import { Box } from "@mui/material";
import { useTranslation } from "react-i18next";
import CustomTabContainer from "../../../components/customTab";
import { NEW_PermissionNames } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";

const MaklumatPertubuhan = () => {
  const { t } = useTranslation();

  const tab = [
    {
      name: t("Induk"),
      navigate_screen: "induk",
      permissionNames: [
        NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
          .INDUK.label,
      ],
    },
    {
      name: t("Cawangan"),
      navigate_screen: "cawangan",
      permissionNames: [
        NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
          .CAWANGAN.label,
      ],
    },
  ];

  const enrichedTabs = tab.map((item) => ({
    ...item,
    hasPermission: AuthHelper.hasAuthority(item.permissionNames),
  }));

  return (
    <Box>
      <CustomTabContainer
        tabs={enrichedTabs.map((tab) => ({
          name: tab.name,
          navigate_screen: tab.navigate_screen,
          disabled: !tab.hasPermission,
        }))}
      />
      <Outlet />
    </Box>
  );
};

export default MaklumatPertubuhan;
