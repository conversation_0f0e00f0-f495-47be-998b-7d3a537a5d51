import { Box, CircularProgress, Grid, Stack, Typography } from "@mui/material";
import React from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import { useSenaraiContext } from "../../SenaraiContext";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import useQuery from "../../../../helpers/hooks/useQuery";
import { useSelector } from "react-redux";
import { downloadFile } from "@/helpers";

const index = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const {
    handleNextPenyataTahunan: handleNext,
    handleBackPenyataTahunan: handleBack,
  } = useSenaraiContext();

  const handleBackActions = () => {
    handleBack();
    navigate(-1);
  };

  const handleNextActions = () => {
    handleNext();
    navigate(`../penyata-tahunan-pengakuan`, {
      state: {
        societyId: societyId,
        statementId: statementId,
        year: year,
      },
    });
  };
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  //@ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);
  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;

  const {
    data: DownloadStatement,
    isLoading: isDownloadtatementLoading,
    refetch: fetchDownloadStatement,
  } = useQuery({
    url: `society/statement/exportPdfV2`,
    filters: [
      { field: "societyId", value: societyId, operator: "eq" },
      { field: "statementId", value: statementId, operator: "eq" },
    ],
    autoFetch: false,
    onSuccess: (data) => {
      const file = data?.data as unknown as string;
      downloadFile({
        data: file,
        name: `ANNUAL STATEMENT ${year}`,
      });
    },
  });

  const handleCetak = () => {
    fetchDownloadStatement();
  };

  return (
    <Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "bold" }}>
              {t("peringatan")} :
            </span>{" "}
            <span style={{ fontSize: 12 }}>{t("assetsLiabilitiesInfo")}</span>
          </Typography>
        </Box>
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("muatTurunPenyataTahunan")}
          </Typography>

          <Grid container spacing={10} alignItems="flex-start" sx={{ mb: 1 }}>
            <Grid item sm={10}>
              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: "400 !important",
                }}
              >
                <span>{t("annualStatement")}</span>
              </Typography>
            </Grid>
            <Grid item sm={2}>
              <Stack
                direction="row"
                spacing={2}
                sx={{ pl: 1 }}
                justifyContent="flex-end"
              >
                <ButtonPrimary onClick={() => handleCetak()}>
                  {isDownloadtatementLoading ? (
                    <CircularProgress
                      size={24}
                      sx={{ display: "block", color: "#fff" }}
                    />
                  ) : (
                    t("cetak")
                  )}{" "}
                </ButtonPrimary>
              </Stack>
            </Grid>
          </Grid>
        </Box>
      </Box>
      <Box
        mt={3}
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          justifyContent: "flex-end",
          display: "flex",
        }}
      >
        <Stack
          direction="row"
          spacing={2}
          sx={{ pl: 1 }}
          justifyContent="flex-end"
        >
          <ButtonOutline onClick={handleBackActions}>{t("back")}</ButtonOutline>
          <ButtonPrimary onClick={handleNextActions}>{t("next")}</ButtonPrimary>
        </Stack>
      </Box>
    </Box>
  );
};

export default index;
