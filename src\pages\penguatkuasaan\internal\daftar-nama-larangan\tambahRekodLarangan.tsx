import { <PERSON>, Button, Switch, Typography } from "@mui/material";
import { LaranganBox } from "./component/LaranganBorder";
import { LaranganPaper } from "./component/LaranganPaper";
import TakwimSelect from "@/components/input/TakwimSelect";
import { useEffect, useState } from "react";
import TakwimInput from "@/components/input/TakwimInput";
import TakwimTextField from "@/components/input/TakwimTextField";
import { ButtonPrimary } from "@/components";
import DialogActionFlow from "@/components/dialog/confirm/DialogActionFlow";
import { NamaLarangan } from "@/types/larangan/namaLarangan";
import { TabNamaTerlarang } from "./tabNamaLarangan";
import { TabLogoTerlarang } from "./tabLogoTerlarang";
import AuthHelper from "@/helpers/authHelper";
import { pageAccessEnum, PermissionNames } from "@/helpers";

export const TambahRekodLarangan = () => {
  const id = getUrlParam("id");
  const tab = getUrlParam("tab");
  const mode = getUrlParam("mode");
  const [tambahRekodTab, setTambahRekodTab] = useState<number | undefined>(0);
  const [isDisabled, setIsDisabled] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);

  const readAccess = checkPermissionAndUserGroup(pageAccessEnum.Read);
  const createAccess = checkPermissionAndUserGroup(pageAccessEnum.Create);
  const deleteAccess = checkPermissionAndUserGroup(pageAccessEnum.Delete);
  const updateAccess = checkPermissionAndUserGroup(pageAccessEnum.Update);

  function checkPermissionAndUserGroup(accessType: number) {
    const hasPermission: boolean = AuthHelper.hasPageAccess(
      PermissionNames.PENGURUSAN_LARANGAN?.label || "PENG-LAR",
      accessType
    );
    if (localStorage.getItem("portal") === "2") {
      return hasPermission && true;
    } else {
      return hasPermission && false;
    }
  }
  function getUrlParam(param: string): string | null {
    return new URLSearchParams(window.location.search).get(param);
  }

  const tabOptions = [
    { label: "nama", value: 0 },
    { label: "logo", value: 1 },
  ];

  useEffect(() => {
    if (tab != null || tab != undefined) {
      const tabNumber = tabOptions.find((item) => item.label === tab)?.value;
      setTambahRekodTab(tabNumber);
    }

    if (mode != null || mode != undefined) {
      if (mode === "edit") {
        setTambahRekodTab(1);
        setIsEditMode(true);
      }
    }

    if (id == null || id == undefined) {
      console.log(id, "ID");
      if (!createAccess) {
        setIsEditMode(false);
        setIsDisabled(true);
      }
    }

    checkAccess(mode);
  }, [tab, mode, id]);

  const checkAccess = (mode: string | null) => {
    if (mode != null) {
      if (mode == "view" || !updateAccess) {
        setIsDisabled(true);
      }
      if (mode == "edit" && updateAccess) {
        setIsDisabled(false);
        setIsEditMode(true);
      }
    } else {
      if (!createAccess) {
        setIsDisabled(true);
      }
    }
  };

  const renderTabContent = () => {
    switch (tambahRekodTab) {
      case 0:
        return (
          <TabNamaTerlarang
            readAccess={readAccess}
            createAccess={createAccess}
            updateAccess={updateAccess}
            deleteAccess={deleteAccess}
            mode={mode}
            id={Number(id)}
            isEditMode={isEditMode}
            isDisabled={isDisabled}
          />
        );
      case 1:
        return (
          <TabLogoTerlarang
            mode={mode}
            id={Number(id)}
            readAccess={readAccess}
            createAccess={createAccess}
            updateAccess={updateAccess}
            deleteAccess={deleteAccess}
            isEditMode={isEditMode}
            isDisabled={isDisabled}
          />
        );
      default:
        return (
          <TabNamaTerlarang
            readAccess={readAccess}
            createAccess={createAccess}
            updateAccess={updateAccess}
            deleteAccess={deleteAccess}
            mode={mode}
            id={Number(id)}
            isEditMode={isEditMode}
            isDisabled={isDisabled}
          />
        );
    }
  };

  // const handleSave = async () => {
  //   try{
  //     const namaLarangan: Partial<NamaLarangan> = {
  //       activeRemarks
  //     }
  //   }
  // };
  return (
    <>
      <Box display={"grid"} gap={2}>
        <LaranganPaper
          sx={{
            padding: "5px",
          }}
        >
          <>
            <Box
              sx={{
                width: "100%",
                display: "flex",
                // gap: 1,
                // p: 0.5,
                borderRadius: "10px",
              }}
            >
              <Button
                className="larangan-tab-btn"
                fullWidth
                variant={tambahRekodTab == 0 ? "contained" : "text"}
                onClick={() => setTambahRekodTab(0)}
              >
                Nama Terlarang
              </Button>
              <Button
                className="larangan-tab-btn"
                fullWidth
                variant={tambahRekodTab == 1 ? "contained" : "text"}
                onClick={() => setTambahRekodTab(1)}
              >
                Logo Terlarang
              </Button>
            </Box>
          </>
        </LaranganPaper>
        {renderTabContent()}
      </Box>
    </>
  );
};
