import { useState, useEffect } from "react";
import {
  Box,
  Paper,
  Button,
  Grid,
  Typography,
  SelectChangeEvent,
  TextField,
  IconButton,
  MenuItem,
  Select,
  FormControlLabel,
  Checkbox,
  CircularProgress,
  LinearProgress,
  Tooltip,
} from "@mui/material";
import { useGetIdentity } from "@refinedev/core";
import { IIdentity } from "../../components/header/header-sidebar-authenticated";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker, TimePicker } from "@mui/x-date-pickers";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import CloseIcon from "@mui/icons-material/Close";
import ErrorIcon from "@mui/icons-material/Error";
import { TakwimInput } from "@/components/input/TakwimInput";
import { TakwimTextField } from "@/components/input/TakwimTextField";
import { TakwimSelect } from "@/components/input/TakwimSelect";
import { TakwimPaper } from "@/components/paper";
import { t } from "i18next";
import dayjs from "@/helpers/dayjs";
import CiptaMaklumBalasContent from "./components/CiptaMaklumBalasContent";
import { useParams, useNavigate } from "react-router-dom";
import {
  ApiResponse,
  BannerUploadResponse,
  EventFormData,
  IEvent,
  PrivateEventSocitie,
} from "@/types/event";
import { eventService } from "@/services/eventService";
import { useTakwim } from "@/contexts/takwimProvider";
import {
  IEventFeedback,
  IEventFeedbackQuestion,
} from "@/types/eventFeedbackQuestion";
import { LoadingDialog } from "@/components/dialog/loading";
import { useNotification } from "@refinedev/core";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import { fetchPositionListData } from "@/redux/APIcalls/positionListThunks";
import { IUser } from "@/types";
import {
  OrganisationPositions,
  OrganizationLevelOption,
  PermissionNames,
  pageAccessEnum,
} from "@/helpers/enums";
import { API_URL } from "@/api";
import { useCustom } from "@refinedev/core";
import CreateNewFolderIcon from "@mui/icons-material/CreateNewFolder";
import InsertInvitationIcon from "@mui/icons-material/InsertInvitation";
import { TakwimMultiSelect } from "@/components/input/TakwimMultiSelect";
import { TakwimMultiInput } from "@/components/input/TakwimMultiInput";
import AuthHelper from "@/helpers/authHelper";
import DialogActionFlow from "@/components/dialog/confirm/DialogActionFlow";
import { useUploadPresignedUrl, DocumentUploadType } from "@/helpers";
import BackButton from "@/components/BackButton";
import { ButtonPrimary } from "@/components";
import TakwimPrimaryButton from "./components/TakwimPrimaryButton";
import TakwimOutlineButton from "./components/TakwimOtlineButton";

// Add interface for society type
interface Society {
  id: string;
  societyName: string;
  societyNo: string;
}
interface Address {
  code: string;
  id: number;
  level: number;
  migrate: number;
  name: string;
  pid: number;
  shortCode: string;
  status: number;
}
interface OrganisationCategory {
  categoryNameBm: string;
  categoryNameEn: string;
  id: number;
  pid: number;
  level: number;
}

interface FormatedOrganisationCategory {
  categoryNameBm: string;
  categoryNameEn: string;
  id: number;
  pid: number;
  level: number;
}

const CreateEventPage = () => {
  const { open: openNotification } = useNotification();
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [privacyType, setPrivacyType] = useState<string>("");
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [clickedSimpanDraf, setClickedSimpanDraf] = useState(false);
  const {
    eventFeedBack,
    eventFormData,
    setEventFormData,
    setEventFeedBack,
    setIsEventEnded,
    isEventEnded,
  } = useTakwim();

  // Add this state at the top of your component
  const [privatePage, setPrivatePage] = useState(1);
  const [selectedSociety, setSelectedSociety] = useState<Society[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [societies, setSocieties] = useState<Society[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [organizationCategory, setOrganizationCategory] = useState<
    OrganisationCategory[]
  >([]);
  const [formatedCategory, setFormatedCategory] = useState<any[]>([]);
  const [cityInputFocused, setCityInputFocused] = useState(false);
  const [bannerImage, setBannerImage] = useState<FormData | null>(null);
  const [bannerPreview, setBannerPreview] = useState<any>(null);
  const [bannerDeleted, setBannerDeleted] = useState(false);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [selectedBannerFile, setSelectedBannerFile] = useState<File | null>(
    null
  );
  const [bannerUrlS3, setBannerUrlS3] = useState<string | null>("");
  const [uploadProgress, setUploadProgress] = useState(0);
  // FOR CREATE EVENT AND EDIT EVENT
  const { eventNo } = useParams();
  const navigate = useNavigate();
  const isEditMode = Boolean(eventNo);
  const { data: user } = useGetIdentity<IUser>();
  const [loading, setLoading] = useState(false);
  const invalidDate = "Invalid Date";
  // Add these filter states
  const [filters, setFilters] = useState({
    permohonan: "",
    pertubuhan: "",
    jawatan: "",
    kategori: "",
  });

  // Add this state at the component level
  const [jawatanFields, setJawatanFields] = useState<number[]>([0]); // Initialize with 0 or any default value

  // Add these functions to handle add/remove
  const handleAddJawatan = () => {
    setJawatanFields((prev) => [...prev, 0]);
  };
  function checkPermissionAndUserGroup(accessType: number) {
    const hasPermission: boolean = AuthHelper.hasPageAccess(
      PermissionNames.TAKWIM?.label || "TAKWIM-AKT",
      accessType
    );

    // 2 is internal user
    if (localStorage.getItem("portal") === "2") {
      return hasPermission && true;
    } else {
      return hasPermission && false;
    }
  }

  const hasEditEventPermission = checkPermissionAndUserGroup(
    pageAccessEnum.Update
  );
  const hasCreateEventPermission = checkPermissionAndUserGroup(
    pageAccessEnum.Create
  );
  const hasDeleteEventPermission = checkPermissionAndUserGroup(
    pageAccessEnum.Delete
  );

  useEffect(() => {
    // Check if user has any of the required permissions
    const hasRequiredPermissions =
      hasEditEventPermission ||
      hasCreateEventPermission ||
      hasDeleteEventPermission;

    // Redirect to /takwim if user doesn't have any required permissions
    if (!hasRequiredPermissions) {
      navigate("/takwim");
    }
  }, [
    hasEditEventPermission,
    hasCreateEventPermission,
    hasDeleteEventPermission,
    navigate,
    openNotification,
  ]);

  const handleRemoveJawatan = (indexToRemove: number) => {
    if (jawatanFields.length > 0) {
      setJawatanFields((prev) =>
        prev.filter((_, index) => index !== indexToRemove)
      );
    }
  };

  const handleJawatanChange = (index: number, value: string) => {
    const numericValue = parseInt(value, 10);
    setJawatanFields((prev) =>
      prev.map((field, i) => (i === index ? numericValue : field))
    );
  };

  const { data: addressList, isLoading: isAddressLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData: Address[] = addressList?.data?.data || [];

  const fetchSocietiesByIds = async (
    societyIds: PrivateEventSocitie[] | null
  ) => {
    try {
      // Create an array of promises for each society fetch
      if (societyIds === null) return;
      const societyPromises = societyIds.map(async (soc) => {
        const response = await fetch(`${API_URL}/society/${soc.societyId}`, {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        });
        const result = await response.json();
        return result.data;
      });

      // Wait for all promises to resolve
      const societies = await Promise.all(societyPromises);

      // Transform the data into the required format
      return societies;
      // .filter(society => society) // Remove any null values
      // .map(society => ({
      //   id: society.id.toString(),
      //   societyName: society.societyName,
      //   societyNo: society.societyNo
      // }));
    } catch (error) {
      console.error("Error fetching societies:", error);
      return [];
    }
  };

  const { data: categoryData, isLoading: isCategoryLoading } = useCustom({
    url: `${API_URL}/society/admin/category/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess(data) {
        const cat = data?.data?.data || [];
        // Filter categories with pid == 1 only
        const filteredCategories = cat.filter(
          (category: OrganisationCategory) => category.pid === 1
        );
        // Transform the filtered category data into the format expected by TakwimSelect
        const formattedCategories = filteredCategories.map(
          (category: OrganisationCategory) => ({
            value: category.id,
            label: category.categoryNameBm || category.categoryNameEn,
          })
        );
        setFormatedCategory(formattedCategories);
        // setOrganizationCategory(formattedCategories);
      },
    },
  });

  // Get state options (Negeri)
  const stateOptions = addressData
    .filter((item: any) => item.pid === 152)
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));

  // Get district options based on selected state
  const districtOptions = addressData
    .filter((item: any) => item.pid === Number(eventFormData?.state))
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));

  // Get city options based on selected district
  const cityOptions = addressData
    .filter((item: any) => item.pid === Number(eventFormData?.district))
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));

  useEffect(() => {
    if (isEditMode && eventNo) {
      fetchEventData(eventNo);
    }
  }, [eventNo]);

  const fetchEventData = async (eventNo: string) => {
    setLoading(true);
    try {
      const { data: eventData }: ApiResponse<IEvent> =
        await eventService.getEventByEventNo(eventNo);
      fetchEventFeedbackQuestion(eventNo);
      if (eventData?.bannerUrl) {
        setBannerPreview(eventData.bannerUrl);
      }

      //handle redirect if user access event that already ended
      if (eventData?.eventEndDate) {
        const today = dayjs();
        const eventEndDate = dayjs(eventData?.eventEndDate, "YYYY-MM-DD");
        const eventEndDateTime = dayjs(
          `${eventData?.eventEndDate} ${eventData?.endTime}`,
          "YYYY-MM-DD HH:mm:ss"
        );

        if (eventEndDate.isBefore(today, "day")) {
          // End date is before today: event ended
          navigate(`/takwim/activity/${eventNo}`);
        } else if (eventEndDate.isSame(today, "day")) {
          // End date is today: check endTime
          if (eventEndDateTime.isBefore(today)) {
            navigate(`/takwim/activity/${eventNo}`);
          }
        }
      }
      const selected: Society[] | any = await fetchSocietiesByIds(
        eventData.privateEventSocieties
      );
      setSelectedSociety(selected);
      const positionArray = eventData.position
        ? String(eventData.position)
            .split(",")
            .map((pos) => parseInt(pos.trim()))
            .filter((pos) => !isNaN(pos))
        : [];
      setJawatanFields(positionArray);

      // Transform IEvent data to EventFormData
      setEventFormData({
        // Add missing required properties
        posterPreview: null,
        dateError: null,

        // Existing properties
        eventNo: eventData.eventNo || "",
        eventName: eventData.eventName,
        eventAdminId: eventData.eventAdminId || 0,
        description: eventData.description,
        collaboratorName: eventData.collaboratorName || "",

        // Dates
        startTime: eventData.startTime || "",
        endTime: eventData.endTime || "",
        eventStartDate: eventData.eventStartDate
          ? dayjs(eventData.eventStartDate).toDate()
          : null,
        eventEndDate: eventData.eventEndDate
          ? dayjs(eventData.eventEndDate).toDate()
          : null,
        regStartDate: eventData.regStartDate || "",
        regEndDate: eventData.regEndDate || "",

        // Location details
        address1: eventData.address1 || "",
        address2: eventData.address2 || "",
        state: eventData.state || [],
        postcode: eventData.postcode || "",
        city: eventData.city || "",
        district: eventData.district || [],
        venue: eventData.venue || "",
        mapUrl: eventData.mapUrl || "",

        // Organizers and participants
        maxParticipants: eventData.maxParticipants || null,
        hasMax: Boolean(eventData.maxParticipants),
        organiserId: eventData.organiserId || null,
        organisationLevel: eventData.organisationLevel || "",
        organisationCategory: eventData.organisationCategory || null,

        // Status and visibility
        status: eventData.status || "",
        visibility: eventData.visibility || "PUBLIC",
        published: eventData.published || false,
        feedbackName: eventData.feedbackName || "",
        societiesId: eventData.societiesId || null,
        position: positionArray || [],
        bannerUrl: eventData.bannerUrl || "",
        picContactNo: eventData.picContactNo || "",
        stateAddress: eventData.stateAddress || null,
        districtAddress: eventData.districtAddress || null,
        cityAddress: eventData.cityAddress || "",
        postcodeAddress: eventData.postcodeAddress || "",
      });
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to fetch event data"
      );
    } finally {
      setLoading(false);
    }
  };

  const fetchEventFeedbackQuestion = async (eventNo: string | undefined) => {
    try {
      // setIsLoading(true);
      const feedbackQuestions: ApiResponse<IEventFeedbackQuestion[]> =
        await eventService.getEventFeedbackQuestionByEventNo(eventNo);
      if (feedbackQuestions.code == 200) {
        const selectedFeedback: number[] = [];
        feedbackQuestions.data.forEach((item: IEventFeedbackQuestion) => {
          selectedFeedback.push(item.feedbackQuestionId);
        });
        setEventFeedBack(selectedFeedback);

        // setEventFeedBack(feedbackQuestions.data);
      }
    } catch (error) {
      console.log(
        error instanceof Error
          ? error.message
          : "Failed to fetch feedback questions"
      );
    } finally {
      // setIsLoading(false);
    }
  };
  const handleBannerImageChange = async (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const banner = e.target.files?.[0];
    if (!banner) return;

    // Size validation
    if (banner.size > 25 * 1024 * 1024) {
      // 5MB limit
      openNotification?.({
        type: "error",
        message: "Error",
        description: "File size should not exceed 25MB",
      });
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (event) => {
      setBannerPreview(event.target?.result as string);
    };
    reader.readAsDataURL(banner);

    // Store the file for upload
    setSelectedBannerFile(banner);
    if (banner) {
      try {
        setIsSaving(true);
        await uploadBanner({
          params: {
            type: DocumentUploadType.TAKWIM_BANNER,
            name: banner.name,
            societyId: null,
            societyNo: null,
            branchId: null,
            branchNo: null,
            meetingId: null,
            societyCommitteeId: null,
            icNo: "",
          },
          file: banner,
        });
      } catch (error) {
        console.error("Error uploading banner:", error);
        openNotification?.({
          type: "error",
          message: "Error",
          description: "Failed to upload banner image",
        });
        setIsSaving(false);
        return;
      } finally {
        setIsSaving(false);
      }
    }
  };

  const { upload: uploadBanner } = useUploadPresignedUrl({
    onSuccessUpload: (data) => {
      // Handle successful upload
      if (data.data.data.url) {
        setBannerUrlS3(data.data.data.url);
      }
    },
  });

  const validateFormBeforeConfirm = () => {
    let hasErrors = false;
    setClickedSimpanDraf(true);

    if (!eventFormData?.eventName) {
      // setEventFormData((prev) => ({
      //   ...prev,
      //   dateError: t("Tarikh acara diperlukan"),
      // }));
      hasErrors = true;
    } else {
      setEventFormData((prev) => ({
        ...prev,
        dateError: null,
      }));
    }

    if (hasErrors) {
      openNotification?.({
        message: t("Sila isi sekurang-kurangnya Nama Acara."),
        type: "error",
      });
      setOpenConfirmDialog(false);
      return;
    } else {
      setOpenConfirmDialog(true);
    }
  };

  const handleConfirm = () => {};
  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormSubmitted(true);

    // let bannerUrl = bannerUrlS3;

    // Inside your handleSave function

    // Validate event start date

    if (isEditMode) {
      if (bannerDeleted) {
        setBannerUrlS3(null);
      } else {
        setBannerUrlS3(eventFormData?.bannerUrl);
      }
    }

    setIsSaving(true);
    setError(null);
    //EVEN END DATE IS INVALID CHECKD THIS TOMORROW
    try {
      // Check if eventEndDate is valid
      const isEndDateValid =
        eventFormData?.eventEndDate &&
        dayjs(eventFormData.eventEndDate).isValid();

      // Use eventStartDate if eventEndDate is invalid or not set
      // const endDate = isEndDateValid
      //   ? eventFormData.eventEndDate
      //   : eventFormData?.eventStartDate;

      // Create event data without the banner image
      const eventData: Partial<IEvent> = {
        eventName: eventFormData?.eventName || "",
        description: eventFormData?.description,
        visibility: eventFormData?.visibility,
        maxParticipants: eventFormData?.maxParticipants || 0,
        collaboratorName: eventFormData?.collaboratorName,
        venue: eventFormData?.venue,
        state: eventFormData?.state || [],
        postcode: eventFormData?.postcode,
        city: eventFormData?.city || [],
        district: eventFormData?.district || [],
        address1: eventFormData?.address1,
        address2: eventFormData?.address2,
        eventStartDate: eventFormData?.eventStartDate
          ? dayjs(eventFormData.eventStartDate).format("YYYY-MM-DD")
          : null,
        eventEndDate: eventFormData?.eventEndDate
          ? dayjs(eventFormData.eventEndDate).format("YYYY-MM-DD")
          : null,
        published: false, // Default to unpublished
        hasMax: Boolean(eventFormData?.maxParticipants),
        status: "",
        feedbackName: eventFormData?.feedbackName,
        startTime: eventFormData?.startTime,
        endTime: eventFormData?.endTime,
        mapUrl: eventFormData?.mapUrl || "",
        societiesId: selectedSociety?.map((society) => parseInt(society.id)),
        organisationLevel: eventFormData?.organisationLevel,
        organisationCategory: eventFormData?.organisationCategory,
        position: jawatanFields,
        bannerUrl: eventFormData?.bannerUrl || bannerUrlS3,
        picContactNo: eventFormData?.picContactNo,
        stateAddress: eventFormData?.stateAddress,
        districtAddress: eventFormData?.districtAddress,
        cityAddress: eventFormData?.cityAddress,
        postcodeAddress: eventFormData?.postcodeAddress,
      };

      // console.log("Event Data to Save:", eventData);
      // return;

      if (isEditMode && eventNo) {
        const editRes: ApiResponse<IEvent> = await eventService.updateEvent(
          eventNo,
          eventData
        );

        openNotification?.({
          message: t("Acara anda berjaya dikemaskini."),
          type: "success",
        });
        if (editRes.code === 201 || editRes.code === 202) {
          const resFeedBack: ApiResponse<IEventFeedback[]> =
            await eventService.createEventFeedback(eventNo, eventFeedBack);
          if (resFeedBack.code == 201) {
            openNotification?.({
              message: t("Maklum balas berjaya dikemas kini."),
              type: "success",
            });
          }
          navigate(`/takwim/activity/${eventNo}`);
        } else {
          throw new Error(editRes.msg || "Failed to update event");
        }
      } else {
        const res: ApiResponse<IEvent> = await eventService.createEvent(
          eventData
        );
        if (res.code === 201 && res.data) {
          const createdEvent = res.data;

          if (eventFeedBack.length > 0) {
            const resFeedBack: ApiResponse<IEventFeedback[]> =
              await eventService.createEventFeedback(
                createdEvent.eventNo,
                eventFeedBack
              );
            if (resFeedBack.code !== 201) {
              throw new Error(resFeedBack.msg || "Failed to create feedback");
            }
          }

          openNotification?.({
            message: t("Acara anda berjaya dicipta."),
            type: "success",
          });
          navigate(`/takwim/activity/${createdEvent.eventNo}`);
        } else {
          throw new Error(res.msg || "Failed to create event");
        }
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to save event";
      setError(errorMessage);
      openNotification?.({
        message: t(errorMessage),
        type: "error",
        description: "Error",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle form field changes

  const handleDateChange =
    (field: "eventStartDate" | "eventEndDate") =>
    (value: dayjs.Dayjs | null) => {
      setEventFormData((prev) => {
        if (!prev) return prev;
        return {
          ...prev,
          [field]: value?.toString() || null,
        };
      });
    };

  const handleTabClick = (tabIndex: number) => {
    setActiveTab(tabIndex);
  };

  const privacyOptions = [
    { value: "PUBLIC", label: t("Umum") },
    { value: "PRIVATE", label: t("Dalaman") },
  ];

  const handlePrivacyChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    setPrivacyType(value);
    setEventFormData((prev) => ({
      ...prev,
      visibility: value as "PRIVATE" | "PUBLIC",
    }));
  };

  function handleNext(): void {
    handleTabClick(1);
  }
  const renderEventForm = () => {
    return (
      <Box
        component="form"
        sx={{ mt: 3, display: "flex", flexDirection: "column", gap: "10px" }}
      >
        {/* DEFAULT SECTION START */}
        <TakwimPaper>
          <Typography variant="subtitle2" color="#4DB6AC" sx={{ mb: 2 }}>
            Nama acara
          </Typography>
          <Box sx={{ display: "grid", gap: 2 }}>
            <TakwimInput
              label="Nama acara"
              value={eventFormData.eventName}
              onChange={(e) => {
                setEventFormData((prev) => ({
                  ...prev,
                  eventName: e.target.value,
                }));
                setClickedSimpanDraf(false); // or any other logic
              }}
              placeholder="Nama acara"
              required
              error={!eventFormData.eventName && clickedSimpanDraf}
              helperText={
                !eventFormData.eventName && clickedSimpanDraf
                  ? "Sila lengkapkan maklumat ini"
                  : ""
              }
              InputProps={{
                endAdornment:
                  !eventFormData.eventName && clickedSimpanDraf ? (
                    <Tooltip title="Sila lengkapkan maklumat ini">
                      <ErrorIcon color="error" />
                    </Tooltip>
                  ) : null,
              }}
            />
            <Box>
              <TakwimTextField
                label="Deskripsi acara"
                value={eventFormData?.description}
                // onChange={handleInputChange("description")}
                onChange={(e) =>
                  setEventFormData((prev) => {
                    if (!prev) return prev;
                    return {
                      ...prev,
                      description: e.target.value,
                    };
                  })
                }
                placeholder="Deskripsi acara"
                rows={4}
                multiline
                required
              />
            </Box>
            <TakwimInput
              value={eventFormData?.picContactNo || ""}
              onChange={(e) =>
                setEventFormData((prev) => ({
                  ...prev,
                  picContactNo: e.target.value.replace(/\D/g, ""), // Only digits allowed
                }))
              }
              label="No Telefon Pegawai Bertanggungjawab"
              required
              inputProps={{ inputMode: "numeric", pattern: "[0-9]*" }}
            />
          </Box>
        </TakwimPaper>
        <TakwimPaper>
          {/* Privacy Section */}
          <Typography variant="subtitle2" color="#4DB6AC" sx={{ mb: 2 }}>
            Privasi acara
          </Typography>
          <Box sx={{ mb: 4 }}>
            <TakwimSelect
              label="Privasi acara"
              value={eventFormData?.visibility || "PUBLIC"} // Use logical OR operator
              onChange={handlePrivacyChange}
              options={privacyOptions}
              placeholder="Sila pilih"
              required
            />
          </Box>
        </TakwimPaper>
        <TakwimPaper>
          <Box sx={{ mb: 4 }}>
            {/* Privacy Section */}
            <Typography variant="subtitle2" color="#4DB6AC" sx={{ mb: 2 }}>
              {t("organizationCategory")}
            </Typography>
            <Box sx={{ display: "grid", gap: 2 }}>
              {eventFormData.visibility == "PRIVATE" && (
                <Box>
                  <TakwimSelect
                    label={t("organizationCategory")}
                    value={eventFormData?.organisationCategory ?? 0}
                    onChange={(e) => {
                      const value = e.target.value;
                      setEventFormData((prev) => ({
                        ...prev,
                        organisationCategory: value !== 0 ? value : null,
                      }));
                    }}
                    options={[
                      { value: 0, label: t("Tiada") }, // Add "None/Tiada" option
                      ...formatedCategory,
                    ]}
                    placeholder="Sila pilih"
                    displayValue={(value) => {
                      if (value === 0) return t("Tiada");
                      const option = formatedCategory.filter(
                        (opt) => opt.value == Number(value)
                      );
                      return option ? option[0]?.label : value.toString();
                    }}
                  />
                </Box>
              )}

              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={3}>
                  <Typography
                    variant="body1"
                    sx={{
                      color: "#666666",
                      fontSize: "14px",
                    }}
                  >
                    Jumlah maksima peserta
                    {eventFormData?.visibility == "PRIVATE" && (
                      <span style={{ color: "red" }}>*</span>
                    )}
                  </Typography>
                </Grid>
                <Grid item>
                  <TextField
                    size="small"
                    type="number"
                    value={eventFormData?.maxParticipants}
                    onChange={(e) =>
                      setEventFormData((prev) => {
                        if (!prev) return prev;
                        const value =
                          e.target.value === "" ? null : Number(e.target.value);
                        return {
                          ...prev,
                          maxParticipants: value,
                          hasMax: Boolean(value),
                        };
                      })
                    }
                    placeholder="0"
                    inputProps={{ min: 0 }}
                    sx={{
                      width: "100px",
                      "& .MuiOutlinedInput-root": {
                        backgroundColor: "white",
                        "& fieldset": {
                          borderColor: "#DADADA",
                        },
                        "&:hover fieldset": {
                          borderColor: "#DADADA",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#DADADA",
                        },
                      },
                      "& .MuiInputBase-input": {
                        fontSize: "14px",
                        padding: "8.5px 14px",
                      },
                    }}
                  />
                </Grid>
                <Grid item>
                  <Typography sx={{ fontSize: "14px", color: "#666666" }}>
                    {t("people")}
                  </Typography>
                </Grid>
              </Grid>
              <Box>
                <TakwimInput
                  label={t("Kerjasama Badan")}
                  value={eventFormData?.collaboratorName}
                  onChange={(e) =>
                    setEventFormData((prev) => {
                      if (!prev) return prev;
                      return {
                        ...prev,
                        collaboratorName: e.target.value,
                      };
                    })
                  }
                  placeholder={t("Kerjasama Badan")}
                />
              </Box>
            </Box>
          </Box>
        </TakwimPaper>

        {/* Event Details Section */}
        {/* MASA DAN TARIKH */}
        <TakwimPaper>
          <Box sx={{ mb: 4 }}>
            <Typography variant="subtitle2" color="#4DB6AC" sx={{ mb: 2 }}>
              {t("Masa, tarikh acara")}
            </Typography>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={3}>
                <Typography sx={{ fontSize: "14px", color: "#666666" }}>
                  {t("Tarikh acara")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={9}>
                <Box display="flex" alignItems="center" gap={2}>
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                      sx={{ width: "100%" }}
                      value={
                        eventFormData?.eventStartDate
                          ? dayjs(eventFormData.eventStartDate)
                          : null
                      }
                      onChange={(newValue) =>
                        setEventFormData((prev) => {
                          if (!prev) return prev;
                          return {
                            ...prev,
                            eventStartDate:
                              (newValue as dayjs.Dayjs)?.format("YYYY-MM-DD") ||
                              null,
                          };
                        })
                      }
                      slotProps={{
                        textField: {
                          size: "small",
                          placeholder: "DD/MM/YYYY",
                          error: !!eventFormData?.dateError,
                          helperText: eventFormData?.dateError,
                          inputProps: {
                            placeholder: "DD-MM-YYYY",
                            required: true,
                          },
                        },
                      }}
                      format="DD-MM-YYYY"
                      views={["year", "month", "day"]}
                      disablePast={true}
                      maxDate={
                        eventFormData?.eventEndDate
                          ? dayjs(eventFormData.eventEndDate)
                          : undefined
                      }
                    />
                  </LocalizationProvider>
                  <Box sx={{ mx: 1 }}>—</Box>
                  {/* HERE */}
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                      sx={{ width: "100%" }}
                      value={
                        eventFormData?.eventEndDate
                          ? dayjs(eventFormData.eventEndDate)
                          : null
                      }
                      onChange={(newValue) =>
                        setEventFormData((prev) => {
                          if (!prev) return prev;
                          return {
                            ...prev,
                            eventEndDate:
                              (newValue as dayjs.Dayjs)?.format("YYYY-MM-DD") ||
                              null,
                          };
                        })
                      }
                      slotProps={{
                        textField: {
                          size: "small",
                          placeholder: "DD/MM/YYYY",
                          error: !!eventFormData?.dateError,
                          helperText: eventFormData?.dateError,
                          inputProps: {
                            placeholder: "DD-MM-YYYY",
                          },
                        },
                      }}
                      format="DD-MM-YYYY"
                      views={["year", "month", "day"]}
                      minDate={
                        eventFormData?.eventStartDate
                          ? dayjs(eventFormData.eventStartDate)
                          : undefined
                      }
                    />
                  </LocalizationProvider>
                </Box>
              </Grid>

              <Grid item xs={12} sm={3}>
                <Typography sx={{ fontSize: "14px", color: "#666666" }}>
                  {t("Masa")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={9}>
                <Box display="flex" alignItems="center" gap={2}>
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <TimePicker
                      sx={{ width: "100%" }}
                      ampm
                      value={
                        eventFormData?.startTime
                          ? dayjs(`2024-01-01 ${eventFormData.startTime}`)
                          : null
                      }
                      slotProps={{
                        textField: {
                          size: "small",
                          placeholder: "09:00 AM",
                        },
                      }}
                      onChange={(newValue) =>
                        setEventFormData((prev) => {
                          if (!prev) return prev;
                          return {
                            ...prev,
                            startTime:
                              (newValue as dayjs.Dayjs)?.format("HH:mm:ss") ||
                              "",
                          };
                        })
                      }
                    />
                  </LocalizationProvider>
                  <Box sx={{ mx: 1 }}>—</Box>
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <TimePicker
                      sx={{ width: "100%" }}
                      ampm
                      value={
                        eventFormData?.endTime
                          ? dayjs(`2024-01-01 ${eventFormData.endTime}`)
                          : null
                      }
                      slotProps={{
                        textField: {
                          size: "small",
                          placeholder: "09:00 AM",
                        },
                      }}
                      onChange={(newValue) =>
                        setEventFormData((prev) => {
                          if (!prev) return prev;
                          return {
                            ...prev,
                            endTime:
                              (newValue as dayjs.Dayjs)?.format("HH:mm:ss") ||
                              "",
                          };
                        })
                      }
                    />
                  </LocalizationProvider>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </TakwimPaper>
        {/* LOCATION EVENT DATA */}
        <TakwimPaper>
          <Box sx={{ mb: 4 }}>
            <Typography variant="subtitle2" color="#4DB6AC" sx={{ mb: 2 }}>
              {t("Alamat Tempat Acara")}
            </Typography>
          </Box>
          <Box sx={{ mb: 2 }}>
            <TakwimInput
              label="Nama tempat acara"
              value={eventFormData?.venue}
              onChange={(e) =>
                setEventFormData((prev) => {
                  if (!prev) return prev;
                  return {
                    ...prev,
                    venue: e.target.value,
                  };
                })
              }
              placeholder="Nama tempat acara"
              required
            />
          </Box>
          <Box sx={{ mb: 2 }}>
            <TakwimInput
              label="Alamat tempat acara"
              value={eventFormData?.address1}
              onChange={(e) =>
                setEventFormData((prev) => {
                  if (!prev) return prev;
                  return {
                    ...prev,
                    address1: e.target.value,
                  };
                })
              }
              placeholder="Alamat tempat acara"
              required
            />
          </Box>
          <Box sx={{ mb: 2 }}>
            <TakwimSelect
              required
              label="Negeri"
              value={eventFormData?.stateAddress || ""}
              onChange={(e) =>
                setEventFormData((prev) => ({
                  ...prev,
                  stateAddress: e.target.value,
                }))
              }
              options={stateOptions}
              placeholder="Sila pilih"
            />
          </Box>
          <Box sx={{ mb: 2 }}>
            <TakwimSelect
              label="Daerah"
              required
              value={eventFormData?.districtAddress || ""}
              onChange={(e) =>
                setEventFormData((prev) => ({
                  ...prev,
                  districtAddress: e.target.value,
                }))
              }
              options={addressData
                .filter(
                  (item: any) =>
                    item.pid === Number(eventFormData?.stateAddress)
                )
                .map((item: any) => ({
                  value: item.id,
                  label: item.name,
                }))}
              placeholder="Sila pilih"
              disabled={!eventFormData?.stateAddress}
            />
          </Box>
          <Box sx={{ mb: 2 }}>
            <TakwimInput
              label="Poskod"
              value={eventFormData?.postcodeAddress}
              onChange={(e) =>
                setEventFormData((prev) => {
                  if (!prev) return prev;
                  return {
                    ...prev,
                    postcodeAddress: e.target.value,
                  };
                })
              }
              placeholder="Poskod"
              required
            />
          </Box>
          <Box sx={{ mb: 2 }}>
            <TakwimInput
              label="Link Google Map/Waze"
              value={eventFormData?.mapUrl}
              onChange={(e) =>
                setEventFormData((prev) => {
                  if (!prev) return prev;
                  return {
                    ...prev,
                    mapUrl: e.target.value,
                  };
                })
              }
              placeholder="Link Google Map/Waze"
            />
          </Box>
        </TakwimPaper>

        {/* Poster Banner Section */}
        <TakwimPaper>
          <Box sx={{ mb: 4 }}>
            <Typography variant="subtitle2" color="#4DB6AC" sx={{ mb: 2 }}>
              Poster Banner
            </Typography>
            <Box
              sx={{
                border: "1px dashed #ccc",
                borderRadius: 1,
                p: 3,
                textAlign: "center",
                cursor: "pointer",
                position: "relative",
                minHeight: "200px",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
              }}
              onClick={() => document.getElementById("posterUpload")?.click()}
            >
              <input
                type="file"
                id="posterUpload"
                hidden
                accept="image/*"
                onChange={handleBannerImageChange}
              />
              {bannerPreview ? (
                <Box
                  sx={{
                    width: "100%",
                    height: "100%",
                    position: "relative",
                  }}
                >
                  <img
                    src={bannerPreview}
                    alt="Poster preview"
                    style={{
                      maxWidth: "100%",
                      maxHeight: "200px",
                      objectFit: "contain",
                    }}
                  />
                  <IconButton
                    sx={{
                      position: "absolute",
                      top: -10,
                      right: -10,
                      bgcolor: "white",
                      "&:hover": { bgcolor: "#f5f5f5" },
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      setBannerPreview("");
                      setBannerDeleted(true);
                    }}
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                </Box>
              ) : (
                <>
                  <CloudUploadIcon
                    sx={{ fontSize: 40, color: "#4DB6AC", mb: 1 }}
                  />
                  <Typography variant="body2" color="textSecondary">
                    Muat naik
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    SVG, PNG, JPG or GIF (max. 800x400px)
                  </Typography>
                </>
              )}
            </Box>
          </Box>
        </TakwimPaper>
        {/* DEFAULT SECTION END */}

        {/* Additional sections for PUBLIC events */}
        {/* {privacyType === "PUBLIC" && (
          <>

          </>
        )} */}
        {/* PUBLIC SECTION END */}

        {/* PRIVATE SECTION START */}
        {eventFormData?.visibility === "PRIVATE" && (
          <>
            {/* Private Content */}
            {renderPrivateContent()}
          </>
        )}
        {/* PRIVATE SECTION END */}

        {/* Pagination */}
        {eventFormData?.visibility === "PRIVATE" && (
          <>
            <Box
              // elevation={1}
              sx={{
                display: "flex",
                justifyContent: "end",
                gap: 1,
                p: 1,
                mb: 2,
              }}
            >
              {[1, 2].map((page) => (
                <Button
                  key={page}
                  variant={privatePage === page ? "contained" : "outlined"}
                  onClick={() => setPrivatePage(page)}
                  sx={{
                    minWidth: 40,
                    bgcolor: privatePage === page ? "#4DB6AC" : "transparent",
                    color: privatePage === page ? "white" : "#4DB6AC",
                    "&:hover": {
                      bgcolor: privatePage === page ? "#3d8f86" : "transparent",
                    },
                  }}
                >
                  {page}
                </Button>
              ))}
            </Box>
          </>
        )}
      </Box>
    );
  };

  const renderPrivateContent = () => {
    if (privatePage === 1) {
      return (
        <>
          <TakwimPaper>
            <Typography variant="subtitle2" color="#4DB6AC" sx={{ mb: 2 }}>
              Butiran acara dalaman (lokaliti)
            </Typography>
            <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
              <TakwimMultiSelect
                label="Negeri"
                value={
                  eventFormData?.state
                    ? Array.isArray(eventFormData.state)
                      ? eventFormData.state
                      : [eventFormData.state]
                    : []
                }
                onChange={(values) =>
                  setEventFormData((prev) => {
                    if (!prev) return prev;

                    // Get previously selected states
                    const prevStates = Array.isArray(prev.state)
                      ? prev.state
                      : [prev.state].filter(Boolean);
                    // Get newly selected states
                    const newStates = values.map((value) => Number(value));

                    // Find removed states
                    const removedStates = prevStates.filter(
                      (state) => !newStates.includes(Number(state))
                    );

                    // Keep only districts that don't belong to removed states
                    const updatedDistricts = Array.isArray(prev.district)
                      ? prev.district
                          .filter((districtId) => {
                            // Find the district's parent state
                            const districtItem = addressData.find(
                              (item) => item.id === Number(districtId)
                            );
                            // Keep if not found or if its parent state wasn't removed
                            return (
                              !districtItem ||
                              !removedStates.includes(districtItem.pid)
                            );
                          })
                          .map((district) => Number(district)) // Convert all to numbers
                      : [];

                    return {
                      ...prev,
                      state: newStates,
                      district: updatedDistricts,
                      // city: [], // Still reset city as it depends on districts
                    };
                  })
                }
                options={stateOptions}
                placeholder="Pilih negeri"
              />

              <TakwimMultiSelect
                label="Daerah"
                value={
                  eventFormData?.district
                    ? Array.isArray(eventFormData.district)
                      ? eventFormData.district
                      : [eventFormData.district]
                    : []
                }
                onChange={(values) =>
                  setEventFormData((prev) => {
                    if (!prev) return prev;
                    return {
                      ...prev,
                      district: values.map((value) => Number(value)),
                      // city: [], // Reset city when district changes
                    };
                  })
                }
                options={addressData
                  .filter(
                    (item: Address) =>
                      Array.isArray(eventFormData?.state) &&
                      eventFormData.state.includes(item.pid)
                  )
                  .map((item: any) => ({
                    value: item.id,
                    label: item.name,
                  }))}
                placeholder="Pilih daerah"
                disabled={!eventFormData?.state?.length}
              />

              {/* <TakwimSelect
                label="Bandar"
                value={Number(eventFormData?.city) || ""}
                onChange={(e) =>
                  setEventFormData((prev) => ({
                    ...prev,
                    city: e.target.value,
                  }))
                }
                options={cityOptions}
                placeholder="Sila pilih"
                disabled={!eventFormData?.district}
              /> */}
              <TakwimMultiInput
                label={t("bandar")}
                value={eventFormData.city}
                onChange={(e) =>
                  setEventFormData((prev) => ({
                    ...prev,
                    city: e,
                  }))
                }
                onFocus={() => setCityInputFocused(true)}
                onBlur={() => setCityInputFocused(false)}
                placeholder={t("Masukkan bandar, pisahkan dengan koma")}
                helperText={
                  cityInputFocused ? t(`Pisahkan bandar dengan koma " , "`) : ""
                }
              />
            </Box>
          </TakwimPaper>

          <TakwimPaper>
            <Typography variant="subtitle2" color="#4DB6AC" sx={{ mb: 2 }}>
              Butiran acara dalaman (Taraf pertubuhan)
            </Typography>
            <TakwimSelect
              label="Taraf pertubuhan"
              value={eventFormData?.organisationLevel || ""}
              onChange={(e) =>
                setEventFormData((prev) => ({
                  ...prev,
                  organisationLevel: e.target.value,
                }))
              }
              options={OrganizationLevelOption}
              placeholder="Sila pilih"
            />
          </TakwimPaper>

          <TakwimPaper>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
              }}
            >
              <Typography variant="subtitle2" color="#4DB6AC">
                Butiran acara dalaman (Penyertaan jawatan)
              </Typography>
              <Button
                onClick={handleAddJawatan}
                startIcon={<AddIcon />}
                sx={{
                  color: "#4DB6AC",
                  "&:hover": {
                    backgroundColor: "rgba(77, 182, 172, 0.08)",
                  },
                }}
              >
                Tambah Jawatan
              </Button>
            </Box>
            <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
              {jawatanFields.map((value, index) => (
                <Box
                  key={index}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                  }}
                >
                  <TakwimSelect
                    label={`Penyertaan jawatan ${index + 1}`}
                    value={
                      t(
                        OrganisationPositions.filter(
                          (position) => position.value === value
                        )[0]?.label
                      ) || ""
                    } // Convert to string for the input
                    onChange={(e) => handleJawatanChange(index, e.target.value)}
                    placeholder="Pilih Jawatan"
                    options={OrganisationPositions.filter(
                      (position) =>
                        // Show option if it's not selected in other fields
                        !jawatanFields.includes(position.value) ||
                        // Or if it's the current field's value
                        value === position.value
                    ).map((position) => ({
                      value: position.value,
                      label: t(position.label),
                    }))}
                  />
                  <Box
                    sx={{
                      width: "10px",
                      height: "10px",
                      borderRadius: "50%",
                      backgroundColor: "#FF0000",
                    }}
                  />
                  {jawatanFields.length > 0 && (
                    <IconButton
                      onClick={() => handleRemoveJawatan(index)}
                      sx={{
                        color: "#FF0000",
                        "&:hover": {
                          backgroundColor: "rgba(255, 0, 0, 0.08)",
                        },
                      }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  )}
                </Box>
              ))}
            </Box>
          </TakwimPaper>
        </>
        // ______
      );
    }

    // Page 2 content (your existing private sections)
    return (
      <>
        <TakwimPaper>
          <Typography variant="subtitle2" color="#4DB6AC" sx={{ mb: 3 }}>
            Penyertaan pertubuhan acara dalaman
          </Typography>
          {/* Search Bar */}
          <TextField
            placeholder="Pilih pertubuhan"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{
              width: "100%",
              alignItems: "center",
              // border: "1px solid red",
              "& .MuiOutlinedInput-root": {
                borderRadius: "10px",
                width: "500px",
                "& fieldset": {
                  borderColor: "#DADADA",
                  backgroundColor: "#8484844D",
                },
                // '&:hover fieldset': {
                //   borderColor: '#DADADA',
                // },
                // '&.Mui-focused fieldset': {
                //   borderColor: '#4DB6AC',
                // },
              },
            }}
          />

          {/* Organizations List */}
          <Box sx={{ mb: 3, width: "70%", marginX: "auto", marginY: "auto" }}>
            {!isLoading && societies.length > 0 && (
              <Grid
                container
                sx={{ borderBottom: 1, borderColor: "divider", py: 1 }}
              >
                <Grid item xs={2}>
                  {/* <Typography>Pertubuhan</Typography> */}
                </Grid>
                <Grid
                  item
                  xs={5}
                  sx={{ display: "grid", justifyContent: "center" }}
                >
                  <Typography>Pertubuhan</Typography>
                </Grid>
                <Grid
                  item
                  xs={5}
                  sx={{ display: "grid", justifyContent: "center" }}
                >
                  <Typography>No. pertubuhan</Typography>
                </Grid>
              </Grid>
            )}

            {/* Loading State */}
            {isLoading && (
              <Box sx={{ py: 2, textAlign: "center" }}>
                <CircularProgress size={24} sx={{ color: "#4DB6AC" }} />
              </Box>
            )}

            {/* Organization Items */}
            {!isLoading &&
              societies.map((society) => (
                <Grid
                  container
                  key={society.id}
                  sx={{ py: 1, borderBottom: 1, borderColor: "divider" }}
                >
                  <Grid item xs={2}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={selectedSociety?.includes(society)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedSociety((prev) =>
                                Array.isArray(prev)
                                  ? [...prev, society]
                                  : [society]
                              );
                            } else {
                              setSelectedSociety((prev) =>
                                Array.isArray(prev)
                                  ? prev.filter((p) => p.id !== society.id)
                                  : []
                              );
                            }
                          }}
                        />
                      }
                      label=""
                    />
                  </Grid>
                  <Grid
                    item
                    xs={5}
                    sx={{ display: "grid", justifyContent: "center" }}
                  >
                    <Typography>{society.societyName}</Typography>
                  </Grid>
                  <Grid
                    item
                    xs={5}
                    sx={{ display: "grid", justifyContent: "center" }}
                  >
                    <Typography>{society.societyNo}</Typography>
                  </Grid>
                </Grid>
              ))}

            {/* No Results Message */}
            {!isLoading && societies.length === 0 && (
              <Box
                sx={{
                  padding: "80px 0",
                  textAlign: "center",
                  height: 250,
                }}
              >
                <CreateNewFolderIcon sx={{ fontSize: 50, color: "#DADADA" }} />
                <Typography color="#DADADA">
                  Tiada pertubuhan dijumpai
                </Typography>
              </Box>
            )}
          </Box>
        </TakwimPaper>
        <TakwimPaper>
          {/* Selected Organizations Count */}
          <Box
            sx={{
              bgcolor: "#4DB6AC",
              color: "white",
              p: 2,
              borderRadius: 1,
              textAlign: "center",
              mb: 2,
            }}
          >
            <Typography variant="h4">{selectedSociety?.length}</Typography>
            <Typography>Persatuan dijemput ke acara dalaman</Typography>
          </Box>

          {/* Selected Organizations List */}
          {selectedSociety?.length < 1 && (
            <Box
              sx={{
                padding: "80px 0",
                textAlign: "center",
                height: 250,
              }}
            >
              <InsertInvitationIcon sx={{ fontSize: 50, color: "#DADADA" }} />
              <Typography color="#DADADA">
                Tiada persatuan dijemput ke acara ini
              </Typography>
            </Box>
          )}
          {selectedSociety?.length > 0 && (
            <Box>
              <Typography color="primary" sx={{ mb: 2 }}>
                senarai persatuan yang dijemput
              </Typography>

              <Grid
                container
                sx={{ borderBottom: 1, borderColor: "divider", py: 1 }}
              >
                <Grid
                  item
                  xs={6}
                  sx={{ display: "grid", justifyContent: "center" }}
                >
                  <Typography>Pertubuhan </Typography>
                </Grid>
                <Grid
                  item
                  xs={6}
                  sx={{ display: "grid", justifyContent: "center" }}
                >
                  <Typography>No. pertubuhan</Typography>
                </Grid>
              </Grid>

              {/* // .filter(society => selectedSociety.includes(society)) */}
              {selectedSociety?.map((society) => (
                <Grid
                  container
                  key={society.id}
                  sx={{ py: 1, borderBottom: 1, borderColor: "divider" }}
                >
                  <Grid
                    item
                    xs={6}
                    sx={{ display: "grid", justifyContent: "center" }}
                  >
                    <Typography>{society.societyName}</Typography>
                  </Grid>
                  <Grid
                    item
                    xs={6}
                    sx={{ display: "flex", justifyContent: "center" }}
                  >
                    <Typography>{society.societyNo}</Typography>
                    <IconButton
                      onClick={() =>
                        setSelectedSociety((prev) =>
                          prev.filter((p) => p.id !== society.id)
                        )
                      }
                      sx={{
                        color: "#FF0000",
                        // ml: 1,
                        // p: 0.5,
                        "&.MuiButtonBase-root.MuiIconButton-root": {
                          padding: "0",
                          marginLeft: "10px",
                          // borderRadius: '4px',
                        },
                        "&:hover": {
                          backgroundColor: "rgba(255, 0, 0, 0.08)",
                        },
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Grid>
                </Grid>
              ))}
            </Box>
          )}
        </TakwimPaper>
      </>
    );
  };

  // Add search function
  const searchSocieties = async (query: string) => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        searchQuery: query,
        size: "10",
        page: "1",
      }).toString();

      const response = await fetch(`${API_URL}/society/search?${params}`, {
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();
      setSocieties(data?.data?.data || []);
    } catch (error) {
      console.error("Error searching societies:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Add debounce effect for search
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (searchTerm) {
        searchSocieties(searchTerm);
      } else {
        setSocieties([]);
      }
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [searchTerm]);

  useEffect(() => {
    // Reset form data when component mounts and not in edit mode
    if (!isEditMode) {
      resetEventFormData();
    }

    // Cleanup function to reset form data when component unmounts
    return () => {
      resetEventFormData();
    };
  }, [isEditMode]);

  const resetEventFormData = () => {
    setEventFormData({
      posterPreview: null,
      dateError: null,
      eventNo: "",
      eventName: "",
      eventAdminId: 0,
      description: "",
      collaboratorName: "",
      startTime: "",
      endTime: "",
      eventStartDate: null,
      eventEndDate: null,
      regStartDate: "",
      regEndDate: "",
      address1: "",
      address2: null,
      state: [],
      postcode: "",
      venue: "",
      mapUrl: "",
      maxParticipants: null,
      hasMax: false,
      organiserId: null,
      status: null,
      visibility: "PUBLIC",
      published: false,
      feedbackName: "",
      societiesId: null,
      position: [],
      city: [],
      district: [],
      organisationLevel: "",
      bannerUrl: "",
      organisationCategory: null,
      picContactNo: null,
      stateAddress: null,
      districtAddress: null,
      cityAddress: "",
      postcodeAddress: "",
    });
    setSelectedSociety([]);
    setBannerPreview(null);
    setBannerImage(null);
    setBannerDeleted(false);
    setJawatanFields([0]);
    setEventFeedBack([]);
    setActiveTab(0);
  };

  return (
    <>
      <BackButton label="Takwim" to="" />

      <Paper
        elevation={1}
        sx={{
          boxShadow: "0 0.75rem 0.75rem 0 #EAE8E866",
          borderRadius: "10px",
          padding: "5px",
          top: "6rem",
          mb: 2,
        }}
      >
        <Box
          sx={{
            width: "100%",
            display: "flex",
            gap: 2,
            p: 0.5,
            borderRadius: "10px",
          }}
        >
          <>
            <Button
              className="tab-btn"
              fullWidth
              variant={activeTab === 0 ? "contained" : "text"}
              onClick={() => handleTabClick(0)}
              sx={{
                textTransform: "none",
              }}
            >
              Cipta Acara
            </Button>
            {eventFormData?.hasMax && (
              <Button
                className="tab-btn"
                fullWidth
                variant={activeTab === 1 ? "contained" : "text"}
                onClick={() => handleTabClick(1)}
                sx={{
                  textTransform: "none",
                }}
              >
                Maklum balas
              </Button>
            )}
          </>
        </Box>
      </Paper>

      {loading ? (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "300px",
          }}
        >
          <CircularProgress sx={{ color: "#4DB6AC" }} />
        </Box>
      ) : (
        <Box sx={{ width: "100%" }}>
          <Paper
            elevation={1}
            sx={{
              boxShadow: "0 0.75rem 0.75rem 0 #EAE8E866",
              borderRadius: "1.25rem",
              padding: "1.5rem",
            }}
          >
            <Box>
              {activeTab === 0 && renderEventForm()}
              {activeTab === 1 && <CiptaMaklumBalasContent />}

              {/* Submit Button - Now outside the conditional rendering */}
              <Box
                sx={{
                  display: "flex",
                  gap: 2,
                  justifyContent: "flex-end",
                  mt: 2,
                }}
              >
                <TakwimOutlineButton
                  variant="outlined"
                  onClick={() => validateFormBeforeConfirm()}
                >
                  {t("Simpan Draf")}
                </TakwimOutlineButton>
                {activeTab === 0 && eventFormData?.hasMax && (
                  <TakwimPrimaryButton onClick={(e) => handleNext()}>
                    {t("Seterusnya")}
                  </TakwimPrimaryButton>
                )}
                {/* {activeTab === 1 && (
                  <Button
                    variant="contained"
                    sx={{
                      bgcolor: "#4DB6AC",
                      "&:hover": { bgcolor: "#3d8f86" },
                      textTransform: "none",
                    }}
                  >
                    Cipta acara
                  </Button>
                )} */}
              </Box>
            </Box>
          </Paper>
        </Box>
      )}
      <LoadingDialog
        open={isSaving}
        message={isEditMode ? "Mengemaskini..." : "Menyimpan..."}
        blur
      />
      <DialogActionFlow
        open={openConfirmDialog}
        onClose={() => setOpenConfirmDialog(false)}
        onConfirm={async () => {
          await handleSave(new Event("submit") as unknown as React.FormEvent);
          setOpenConfirmDialog(false);
        }}
        confirmationText={t("Adakah anda pasti untuk menyimpan draf ini?")}
        // successMessage={t("Draf berjaya disimpan")}
        // errorMessage={t("Ralat semasa menyimpan draf")}
      />
    </>
  );
};

export default CreateEventPage;
