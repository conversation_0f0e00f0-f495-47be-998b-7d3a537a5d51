import React, { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { OrganizationStepper } from "../organization-stepper";
import { useTheme, useMediaQuery, Fade, Grid } from "@mui/material";
import { useCustom } from "@refinedev/core";
import Input from "../../../../components/input/Input";
import { useNavigate, useSearchParams } from "react-router-dom";
import { API_URL } from "../../../../api";
import InfoQACard from "../InfoQACard";
import { useSelector } from "react-redux";
import usePaymentService from "../../../../helpers/hooks/usePaymentService";
import { selectCalculatedPayment } from "../../../../redux/paymentReducer";

export const Butiran = () => {
  const { t } = useTranslation();
  const [activeStep] = useState(5);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogAlertSuccessSaveOpen, setDialogAlertSuccessSaveOpen] =
    useState(false);
  const [selectedBank, setSelectedBank] = useState("");
  const [email, setEmail] = useState("");
  const [emailError, setEmailError] = useState("");
  const [searchParams] = useSearchParams();
  const [paymentStatus, setPaymentStatus] = useState<'initial' | 'pending' | 'cancelled'>('initial');

  const navigate = useNavigate();

  // Payment service hook and Redux selectors
  const { processPayment } = usePaymentService();
  const calculatedPayment = useSelector(selectCalculatedPayment);
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const { data: bankList, isLoading: isLoadingBanks } = useCustom({
    url: `${API_URL}/payment/getBankList`,
    method: "get",
    config: {
      headers: {
        "portal": localStorage.getItem('portal'),
        "authorization": `Bearer ${localStorage.getItem('refine-auth')}`
      },
    },
  });

  const bankOptions = bankList?.data?.data?.bankTypeList?.map((bank: any) => ({
    value: bank.bankCode,
    label: t("language") === "en" ? bank.bankName : bank.bankName,
  })) || [];

  // Add this to help debug
  useEffect(() => {
  }, [selectedBank, bankOptions]);

  const handleBankChange = (event: any) => {
    const value = event?.target?.value ?? event;
    setSelectedBank(value);
  };

  // Tambahkan log untuk melihat nilai yang dipilih
  useEffect(() => {
  }, [selectedBank, bankOptions]);

  // Add email validation function
  const validateEmail = (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError(t("emailRequired"));
      return false;
    }
    if (!regex.test(email)) {
      setEmailError(t("invalidEmail"));
      return false;
    }
    setEmailError("");
    return true;
  };

  const handleEmailChange = (event: any) => {
    const value = event?.target?.value;
    setEmail(value);
    validateEmail(value);
  };

  // Check if form is valid
  const isFormValid = selectedBank && email && !emailError;



  // Add function to handle payment cancellation
  const handleCancelPayment = async () => {
    try {
      const response = await fetch(`${API_URL}/payment/cancelPayment`, {
        method: 'POST',
        headers: {
          "portal": localStorage.getItem('portal') || '',
          "authorization": `Bearer ${localStorage.getItem('refine-auth')}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          societyId: societyDataRedux?.id,
        })
      });

      const data = await response.json();
      if (data.status === "SUCCESS") {
        setPaymentStatus('initial');
      }
    } catch (error) {
      console.error("Cancel payment error:", error);
    }
  };



  // Modified handlePayment function to use processPayment API from paymentService
  const handlePayment = async () => {
    if (!isFormValid) return;

    // Check if we have calculated payment data from Redux
    if (!calculatedPayment) {
      console.error('No calculated payment data available');
      // You might want to show an error message to the user
      return;
    }

    try {
      const processPaymentRequest = {
        societyId: societyDataRedux?.id,
        amount: calculatedPayment.totalAmount,
        email: email,
        bankCode: selectedBank,
        signature: calculatedPayment.signature
      };

      console.log('Processing payment with payload:', processPaymentRequest);

      const response = await processPayment(processPaymentRequest);

      if (response?.data) {
        const { paymentInfo } = response.data;

        if (!paymentInfo) {
          throw new Error('Payment info is missing from response');
        }

        // Create form for submission to payment gateway
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'https://epayment.eroses.gov.my/eps/process';

        // Map response data to required form fields
        const formData = {
          exchange_id: paymentInfo.exchange_id,
          trans_id: paymentInfo.trans_id,
          payment_mode: paymentInfo.payment_mode?.toUpperCase(),
          BANK_CODE: paymentInfo.bank_CODE,
          amaun: paymentInfo.amaun?.toFixed(2),
          namaPemohon: paymentInfo.namaPemohon,
          noKp: paymentInfo.noKp,
          alamat: paymentInfo.alamat,
          email: paymentInfo.email,
          namaPertubuhan: paymentInfo.namaPertubuhan,
          noPertubuhan: paymentInfo.noPertubuhan || '',
          jenisBayaran: paymentInfo.jenisBayaran,
          kod_doc: paymentInfo.kod_doc,
          kod_status_permohonan: paymentInfo.kod_status_permohonan,
          kodOsolAmanah: paymentInfo.kodOsolAmanah,
          kod_negeri: paymentInfo.kod_negeri,
          jabatan: paymentInfo.jabatan,
          ptj: paymentInfo.ptj,
          noRujukan: paymentInfo.noRujukan,
          tarikhPermohonan: paymentInfo.tarikhPermohonan,
          environment: paymentInfo.environment,
          urlhost: paymentInfo.urlhost,
          url_request: paymentInfo.url_request,
          no_resit_ros: paymentInfo.no_resit_ros || '',
        };

        // Create hidden inputs for each field
        Object.entries(formData).forEach(([key, value]) => {
          const input = document.createElement('input');
          input.type = 'hidden';
          input.name = key;
          input.value = value?.toString() || '';
          form.appendChild(input);
        });

        // Append form to body and submit
        document.body.appendChild(form);
        form.submit();

        // Update payment status
        setPaymentStatus('pending');
      }
    } catch (error) {
      console.error("Payment error:", error);
      // You might want to show an error message to the user
      // setError('Failed to process payment. Please try again.');
    }
  };

  // Add useEffect to check URL parameters for payment status
  useEffect(() => {
    const paymentResult = searchParams.get('payment_status');
    if (paymentResult === 'pending') {
      setPaymentStatus('pending');
    }
  }, [searchParams]);

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box sx={{ width: "55vw" }}>
        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3
            }}
          >
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: 600,
                    paddingLeft: 2
                  }}
                >
                  {t("payment")} - {t("butiran")}
                </Typography>
              </Box>

              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: 400,
                  paddingLeft: 2
                }}
              >
                {t("infoButiran")}
              </Typography>

              <Grid container spacing={2} pl={4} pt={2} mb={3} mt={3}>
                <Grid
                  item
                  xs={12}
                  sm={4}
                  sx={{ "&.MuiGrid-root": { p: 0 }, mb: 1 }}
                >
                  <Typography
                    sx={{ fontWeight: "500 !important", fontSize: "14px" }}
                  >
                    {t("perbankan")}
                  </Typography>
                </Grid>
                <Grid
                  item
                  xs={12}
                  sm={8}
                  sx={{
                    "&.MuiGrid-root": { py: 0, pr: 0, pl: 1 },
                    mb: 1,
                    display: "flex",
                    gap: 8,
                  }}
                >
                  <img src="/fpx.png" width="100" alt="" />
                </Grid>

                <Input
                  disabled
                  label={t("butiran")}
                  value="Pendaftaran Pertubuhan (Pembayaran Online)"
                />
                <Input
                  disabled
                  label={t("modPembayaran")}
                  value="FPX (Individu)"
                />
                <Input
                  label={t("pilihanBank")}
                  type="select"
                  options={bankOptions}
                  placeholder={
                    isLoadingBanks ? t("loading") : t("selectPlaceholder")
                  }
                  disabled={isLoadingBanks}
                  value={selectedBank}
                  onChange={handleBankChange}
                />
                <Input
                  label={t("email")}
                  value={email}
                  onChange={handleEmailChange}
                  error={!!emailError}
                  helperText={emailError}
                />
              </Grid>

              <Typography
                variant="body2"
                sx={{
                  color: "#402DFF",
                  fontSize: 14,
                  paddingLeft: 2
                }}
              >
                {t("noteButiran")}
              </Typography>
            </Box>

            <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2, marginTop: 15 }}>
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              {paymentStatus === "pending" ? (
                <ButtonPrimary
                  onClick={handleCancelPayment}
                  sx={{
                    backgroundColor: "#f44336",
                    "&:hover": {
                      backgroundColor: "#d32f2f"
                    },
                    borderRadius: 1,
                    textTransform: "none"
                  }}
                >
                  {t("cancel")}
                </ButtonPrimary>
              ) : (
                <ButtonPrimary
                  disabled={!isFormValid}
                  onClick={handlePayment}
                  sx={{
                    backgroundColor: "#00A7A7",
                    "&:hover": {
                      backgroundColor: "#008F8F"
                    },
                    borderRadius: 1,
                    textTransform: "none"
                  }}
                >
                  {t("Continue")}
                </ButtonPrimary>
              )}
            </Box>
          </Box>
        </Fade>

        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
              backgroundColor: "#fff",
              color: "#000",
              minWidth: fullScreen ? "100%" : "1000px",
              maxWidth: "100%",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogTitle sx={{ pb: 2.5 }}>
            <Box
              sx={{
                backgroundColor: "#e0f2f1",
                px: 2.5,
                py: 0.5,
                borderRadius: 2.5,
              }}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{ fontWeight: "bold", fontSize: 16, color: "black" }}
              >
                {t("submitApplication")}
              </Typography>
              <IconButton onClick={handleCloseDialog} size="small">
                <CloseIcon sx={{ color: "black" }} />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ py: 4 }}>
            <DialogContentText
              id="alert-dialog-description"
              sx={{ color: "black", fontSize: 14, ml: 2 }}
            >
              {t("confirmSubmitApplication")}
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ py: 2, px: 3 }}>
            <ButtonOutline onClick={handleCloseDialog} sx={{ fontSize: 14 }}>
              {t("no")}
            </ButtonOutline>
            <ButtonPrimary color="primary" autoFocus sx={{ fontSize: 14 }}>
              {t("yes")}
            </ButtonPrimary>
          </DialogActions>
        </Dialog>

        <Dialog
          open={dialogAlertSuccessSaveOpen}
          onClose={() => setDialogAlertSuccessSaveOpen(false)}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogContent sx={{ p: 4 }}>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <img src="/success.gif" alt="success" width={200} height={200} />
            </Box>
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontSize: 28, textAlign: "center" }}
            >
              Permohonan berjaya dihantar.
            </Typography>

            <Box
              sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
            >
              <ButtonPrimary
                sx={{ backgroundColor: "#51CA47", borderRadius: "18px" }}
                onClick={() => setDialogAlertSuccessSaveOpen(false)}
              >
                {t("Continue")}
              </ButtonPrimary>
            </Box>
          </DialogContent>
        </Dialog>
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <OrganizationStepper activeStep={activeStep} />

        <InfoQACard />
      </Box>
    </Box>
  );
};

export default Butiran;
