import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  InputAdornment,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { API_URL } from "../../../../../api";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import { RegExNumbers } from "../../../../../helpers/enums";
import { capitalizeWords, useQuery } from "@/helpers";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
interface FasalContentEmpatCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentEmpatCawangan: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const {
    data: religion,
    isLoading: religionListIsLoading,
    refetch: fetchReligionList,
  } = useQuery({
    url: "society/lookup/religion/allActive",
    onSuccess: (data) => {
      const response = data?.data?.data;
      //agama
      setReligionList(response);
    },
  });

  const {
    data: race,
    isLoading: raceListIsLoading,
    refetch: fetchRaceList,
  } = useQuery({
    url: "society/lookup/race/allActive",
    onSuccess: (data) => {
      const response = data?.data?.data;
      //bangsa
      setRaceList(response);
    },
  });

  const jantinaData = [
    { value: "LELAKI", label: "LELAKI" },
    { value: "PEREMPUAN", label: "PEREMPUAN" },
    { value: "LELAKI DAN PEREMPUAN", label: "LELAKI DAN PEREMPUAN" },
  ];

  const [religionList, setReligionList] = useState([]);
  const [raceList, setRaceList] = useState([]);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [kewarganegaraan, setKewarganegaraan] = useState("");
  const [umur, setUmur] = useState("");
  const [kawasan, setKawasan] = useState("");
  const [keturunan, setKeturunan] = useState("");
  const [jantina, setJantina] = useState("");
  const [agama, setAgama] = useState("");
  const [kemahiran, setKemahiran] = useState("");
  const [ahliBersekutu, setAhliBersekutu] = useState("");
  const [ahliKehormat, setAhliKehormat] = useState("");
  const [ahliSeumurHidup, setAhliSeumurHidup] = useState("");
  const [ahliRemaja, setAhliRemaja] = useState("");

  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  //const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!umur) {
      errors.umur = t("fieldRequired");
    }

    if (!kawasan) {
      errors.kawasan = t("fieldRequired");
    }

    if (!keturunan) {
      errors.keturunan = t("fieldRequired");
    }

    if (!jantina) {
      errors.jantina = t("fieldRequired");
    }

    if (!agama) {
      errors.agama = t("fieldRequired");
    }

    return errors;
  };

  /*const clauseContent = `
1. Kriteria keahlian Pertubuhan adalah seperti berikut:

A. Ahli Biasa
  i. Kewarganegaraan : ${kewarganegaraan || "<<Kewarganegaraan>>"}
  ii. Umur Minima : ${umur || "<<Umur Minima>>"} tahun
  iii. Kawasan/Negeri Tempat Tinggal Ahli: ${kawasan || "<<Kawasan>>"}
  iv. Keturunan/Bangsa: ${keturunan || "<<Keturunan>>"}
  v. Jantina : ${jantina || "<<Jantina>>"}
  vi. Agama : ${agama || "<<Agama>>"}
  vii. Kriteria Keahlian Yang Lain: ${kemahiran || "<<Kriteria Lain>>"}

B. Ahli Bersekutu : ${ahliBersekutu || "<<Ahli Bersekutu>>"}

C. Ahli Kehormat : ${ahliKehormat || "<<Ahli Kehormat>>"}

D. Ahli Seumur Hidup : ${ahliSeumurHidup || "<<Ahli Seumur Hidup>>"}

E. Ahli Remaja : ${ahliRemaja || "<<Ahli Remaja>>"}

2. Tiap-tiap permohonan menjadi ahli hendaklah direkod dan dicadangkan serta disokong oleh ahli dan dihantar kepada Setiausaha yang dikehendaki mengemukakan permohonan itu dengan segera kepada Mesyuarat Jawatankuasa untuk dipertimbangkan. Jawatankuasa boleh mengikut budi bicaranya menolak sebarang permohonan.

3. Tiap-tiap pemohon yang permohonannya telah diluluskan seperti yang tersebut di atas, hendaklah diterima menjadi ahli Pertubuhan setelah membayar yuran yang ditetapkan dan berhaklah ia menjadi ahli.
`;*/

  //const clause4 = localStorage.getItem("clause4");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause4Data = JSON.parse(clause4);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause4Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause4Data.societyName);
      if (clause.constitutionValues.length > 0) {
        const fieldMappings: Record<string, (value: string) => void> = {
          Kewarganegaraan: setKewarganegaraan,
          "Umur Minimum": setUmur,
          "Kawasan / Negeri Tempat Tinggal Ahli": setKawasan,
          Keturunan: setKeturunan,
          Jantina: setJantina,
          Agama: setAgama,
          "Kriteria Kemahiran Lain": setKemahiran,
          "Ahli Bersekutu": setAhliBersekutu,
          "Ahli Kehormatan": setAhliKehormat,
          "Ahli Seumur Hidup": setAhliSeumurHidup,
          "Ahli Remaja": setAhliRemaja,
        };

        Object.values(fieldMappings).forEach((setter) => setter(""));

        if (clause.constitutionValues) {
          clause.constitutionValues.forEach((item: any) => {
            const setter = fieldMappings[item.titleName];
            if (setter && item.definitionName) {
              setter(item.definitionName);
            }
          });
        }
        setIsEdit(clause.edit);
      }
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  const { id } = useParams();

  const alignMembershipClause = (content: string): string => {
    const tokens = {
      "<<ahli bersekutu>>": "b. Ahli Bersekutu",
      "<<ahli kehormat>>": "c. Ahli Kehormat",
      "<<ahli seumur hidup>>": "d. Ahli Seumur Hidup",
      "<<ahli remaja>>": "e. Ahli Remaja",
    };

    let lines = content.split("\n");

    const newLines: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const trimmed = lines[i].trim().toLowerCase();

      const tokenEntry = Object.entries(tokens).find(([token]) =>
        trimmed.includes(token.toLowerCase())
      );

      if (tokenEntry) {
        const [token, label] = tokenEntry;

        const prevLine = newLines[newLines.length - 1]?.trim().toLowerCase();
        if (prevLine !== label.toLowerCase()) {
          newLines.push(`    ${label}`);
        }

        newLines.push(`       ${token}`);
      } else {
        const isLabelLine = Object.values(tokens).some(
          (label) => trimmed === label.toLowerCase()
        );
        const nextLine = lines[i + 1]?.trim().toLowerCase() ?? "";
        const isNextToken = Object.keys(tokens).some((token) =>
          nextLine.includes(token.toLowerCase())
        );

        if (isLabelLine && isNextToken) {
          continue;
        }

        newLines.push(lines[i]);
      }
    }

    return newLines.join("\n");
  };

  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<Kewarganegaraan>>/gi,
    `<b>${kewarganegaraan || "<<Kewarganegaraan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<umur minima>>/gi,
    `<b>${umur || "<<umur minima>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kawasan>>/gi,
    `<b>${kawasan || "<<kawasan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<keturunan>>/gi,
    `<b>${keturunan || "<<keturunan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jantina>>/gi,
    `<b>${jantina || "<<jantina>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<agama>>/gi,
    `<b>${agama || "<<agama>>"}</b>`
  );
  if (kemahiran) {
    clauseContent = clauseContent.replaceAll(
      /<<kriteria lain>>/gi,
      `<b>${kemahiran || "<<kriteria lain>>"}</b>\n`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*vii\.\s*Kriteria Keahlian Yang Lain: <<kriteria lain>>*[\r\n]?/gim,
      "\n"
    );
  }
  clauseContent = clauseContent.replaceAll(
    /<<ahli bersekutu>>/gi,
    `<b>${ahliBersekutu || "<<ahli bersekutu>>"}</b>`
  );

  clauseContent = alignMembershipClause(clauseContent);

  clauseContent = ahliBersekutu
    ? clauseContent.replaceAll(
        /\s*<<ahli bersekutu>>/gi,
        `${
          ahliBersekutu
            ? `<div style="width:100%;padding-left:30px;"><b>${ahliBersekutu}</b></div>`
            : `<b><<ahli bersekutu>></b>`
        }`
      )
    : clauseContent.replaceAll(
        /<<ahli bersekutu>>/gi,
        `${
          ahliBersekutu
            ? `<div style="width:100%;padding-left:30px;"><b>${ahliBersekutu}</b></div>`
            : `<b><<ahli bersekutu>></b>`
        }`
      );

  clauseContent = ahliKehormat
    ? clauseContent.replaceAll(
        /\s*<<ahli kehormat>>/gi,
        `${
          ahliKehormat
            ? `<div style="width:100%;padding-left:30px;"><b>${ahliKehormat}</b></div>`
            : `<b><<ahli kehormat>></b>`
        }`
      )
    : clauseContent.replaceAll(
        /<<ahli kehormat>>/gi,
        `${
          ahliKehormat
            ? `<div style="width:100%;padding-left:30px;"><b>${ahliKehormat}</b></div>`
            : `<b><<ahli kehormat>></b>`
        }`
      );

  clauseContent = ahliSeumurHidup
    ? clauseContent.replaceAll(
        /\s*<<ahli seumur hidup>>/gi,
        `${
          ahliSeumurHidup
            ? `<div style="width:100%;padding-left:30px;"><b>${ahliSeumurHidup}</b></div>`
            : `<b><<ahli seumur hidup>></b>`
        }`
      )
    : clauseContent.replaceAll(
        /<<ahli seumur hidup>>/gi,
        `${
          ahliSeumurHidup
            ? `<div style="width:100%;padding-left:30px;"><b>${ahliSeumurHidup}</b></div>`
            : `<b><<ahli seumur hidup>></b>`
        }`
      );

  clauseContent = ahliRemaja
    ? clauseContent.replaceAll(
        /\s*<<ahli remaja>>\s*/gi,
        `${
          ahliRemaja
            ? `<div style="width:100%;padding-left:30px;padding-bottom:10px"><b>${ahliRemaja}</b></div>`
            : `<b><<ahli remaja>></b>`
        }`
      )
    : clauseContent.replaceAll(
        /<<ahli remaja>>/gi,
        `${
          ahliRemaja
            ? `<div style="width:100%;padding-left:30px;"><b>${ahliRemaja}</b></div>`
            : `<b><<ahli remaja>></b>`
        }`
      );

  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");
  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          Ahli Biasa
        </Typography>

        <Grid container spacing={2} sx={{ mt: 2 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("citizenship")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <Grid item xs={12}>
              <RadioGroup
                row
                value={kewarganegaraan}
                onChange={(e) => setKewarganegaraan(e.target.value)}
              >
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("citizen")}
                  control={<Radio />}
                  label={t("citizen")}
                />
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("nonCitizen")}
                  control={<Radio />}
                  label={t("nonCitizen")}
                />
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("open")}
                  control={<Radio />}
                  label={t("open")}
                />
              </RadioGroup>
            </Grid>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("minimumAge")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              size="small"
              fullWidth
              required
              value={umur}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setUmur(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    umur: "",
                  }));
                } else {
                  setUmur("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    umur: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.umur}
              helperText={formErrors.umur}
              type="number"
              InputProps={{
                endAdornment: (
                  <Typography sx={{ ...labelStyle, mt: 1 }}>
                    {t("years")}
                  </Typography>
                ),
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item />
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("residentialArea")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              value={kawasan}
              onChange={(e) => {
                setKawasan(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  kawasan: "",
                }));
              }}
              error={!!formErrors.kawasan}
              helperText={formErrors.kawasan}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("descent")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required error={!!formErrors.keturunan}>
              <Select
                disabled={raceListIsLoading}
                size="small"
                value={keturunan}
                displayEmpty
                onChange={(e) => {
                  setKeturunan(capitalizeWords(e.target.value.toLowerCase()));
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    keturunan: "",
                  }));
                }}
                renderValue={(selected) => {
                  if (selected === "") {
                    return t("selectPlaceholder");
                  }

                  return selected;
                }}
              >
                {raceList.map((option: any) => (
                  <MenuItem
                    key={option.id}
                    value={capitalizeWords(option.name.toLowerCase())}
                  >
                    {capitalizeWords(option.name.toLowerCase())}
                  </MenuItem>
                ))}
              </Select>
              {formErrors.keturunan && (
                <FormHelperText>{formErrors.keturunan}</FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("gender")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required error={!!formErrors.jantina}>
              <Select
                size="small"
                value={jantina}
                displayEmpty
                onChange={(e) => {
                  setJantina(capitalizeWords(e.target.value.toLowerCase()));
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jantina: "",
                  }));
                }}
                renderValue={(selected) => {
                  if (selected === "") {
                    return t("selectPlaceholder");
                  }

                  return selected;
                }}
              >
                {jantinaData.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
              {formErrors.jantina && (
                <FormHelperText>{formErrors.jantina}</FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("religion")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required error={!!formErrors.agama}>
              <Select
                disabled={religionListIsLoading}
                size="small"
                value={agama}
                displayEmpty
                onChange={(e) => {
                  setAgama(capitalizeWords(e.target.value.toLowerCase()));
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    agama: "",
                  }));
                }}
                renderValue={(selected) => {
                  if (selected === "") {
                    return t("selectPlaceholder");
                  }

                  return selected;
                }}
              >
                {religionList.map((option: any) => (
                  <MenuItem
                    key={option.id}
                    value={capitalizeWords(option.name.toLowerCase())}
                  >
                    {capitalizeWords(option.name.toLowerCase())}
                  </MenuItem>
                ))}
              </Select>
              {formErrors.agama && (
                <FormHelperText>{formErrors.agama}</FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("otherSkillsCriteria")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              value={kemahiran}
              onChange={(e) => setKemahiran(e.target.value)}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("associateMember")}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <RadioGroup
              row
              value={ahliBersekutu}
              onChange={(e) => setAhliBersekutu(e.target.value)}
            >
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("associateMemberFirstOption")}
                  control={<Radio />}
                  label={t("associateMemberFirstOption")}
                />
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("none")}
                  control={<Radio />}
                  label={t("none")}
                />
              </Grid>
            </RadioGroup>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("honoraryMember")}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <RadioGroup
              row
              value={ahliKehormat}
              onChange={(e) => setAhliKehormat(e.target.value)}
            >
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("honoraryMemberFirstOption")}
                  control={<Radio />}
                  label={t("honoraryMemberFirstOption")}
                />
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("none")}
                  control={<Radio />}
                  label={t("none")}
                />
              </Grid>
            </RadioGroup>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("lifetimeMember")}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <RadioGroup
              row
              value={ahliSeumurHidup}
              onChange={(e) => setAhliSeumurHidup(e.target.value)}
            >
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("lifetimeMemberFirstOption")}
                  control={<Radio />}
                  label={t("lifetimeMemberFirstOption")}
                />
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("none")}
                  control={<Radio />}
                  label={t("none")}
                />
              </Grid>
            </RadioGroup>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("youthMember")}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <RadioGroup
              row
              value={ahliRemaja}
              onChange={(e) => setAhliRemaja(e.target.value)}
            >
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("youthMemberFirstOption")}
                  control={<Radio />}
                  label={t("youthMemberFirstOption")}
                />
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("none")}
                  control={<Radio />}
                  label={t("none")}
                />
              </Grid>
            </RadioGroup>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            const errors = validateForm();
            if (Object.keys(errors).length > 0) {
              setFormErrors(errors);
              return;
            }

            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kewarganegaraan,
                  titleName: "Kewarganegaraan",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: umur,
                  titleName: "Umur Minimum",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kawasan,
                  titleName: "Kawasan / Negeri Tempat Tinggal Ahli",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: keturunan,
                  titleName: "Keturunan",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jantina,
                  titleName: "Jantina",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: agama,
                  titleName: "Agama",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kemahiran,
                  titleName: "Kriteria Kemahiran Lain",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: ahliBersekutu,
                  titleName: "Ahli Bersekutu",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: ahliKehormat,
                  titleName: "Ahli Kehormatan",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: ahliSeumurHidup,
                  titleName: "Ahli Seumur Hidup",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: ahliRemaja,
                  titleName: "Ahli Remaja",
                },
              ],
              clause: "clause4",
              clauseCount: 4,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentEmpatCawangan;
