import { Formik } from "formik";
import { useParams } from "react-router-dom";

import { FileUploader } from "@/components";
import { FormMeetingCreateBySocietyIdInner } from "@/components/form/meeting/create/BySocietyIdInner";
import { useFormManagementMeetingCreateBySocietyIdInitialValue } from "@/controllers";
import { DocumentUploadType, fromBase64, useQuery } from "@/helpers";
import { usePertubuhanContext } from "../PertubuhanProvider";
import { Box } from "@mui/material";
import { useState } from "react";

export const PertubuhanMesyuaratById = () => {
  const { getInitialValue } =
    useFormManagementMeetingCreateBySocietyIdInitialValue();
  const { meetingId: meetingIdEncoded } = useParams();
  const meetingId = fromBase64(meetingIdEncoded || "");

  const { societyDetailData: societyData } = usePertubuhanContext();
  const [currentMeetingData, setCurrentMeetingData] = useState<any>(null);

  useQuery<{ data: any }>({
    url: `society/meeting/${meetingId}`,
    onSuccess: (response) => {
      const currentMeetingData = response.data.data ?? null;
      setCurrentMeetingData(currentMeetingData);
    },
  });

  return (
    <Box sx={{ mt: 2 }}>
      <Formik
        initialValues={getInitialValue({ societyData, currentMeetingData })}
        onSubmit={() => {}}
        enableReinitialize
      >
        <FormMeetingCreateBySocietyIdInner
          readOnly={true}
          includeMesyuaratPenubuhan={true}
          attachmentComponent={
            <FileUploader
              societyId={societyData.id}
              type={DocumentUploadType.MEETING}
              meetingId={meetingId!}
              validTypes={[]}
              disabled={true}
              onLoadComplete={(items) => {
                const selectedItem = items?.[0] ?? null;
                if (selectedItem) {
                  setCurrentMeetingData((prev: any) => ({
                    ...prev,
                    meetingMinute: selectedItem.name,
                    meetingMinuteURL: selectedItem.url,
                  }));
                }
              }}
            />
          }
        />
      </Formik>
    </Box>
  );
};
