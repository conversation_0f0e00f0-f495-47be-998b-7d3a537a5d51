import { Formik } from "formik";
import { useState } from "react";
import { useParams } from "react-router-dom";

import { FileUploader } from "@/components";
import { FormMeetingCreateBySocietyIdInner } from "@/components/form/meeting/create/BySocietyIdInner";
import { useFormManagementMeetingCreateBySocietyIdInitialValue } from "@/controllers";
import { DocumentUploadType, useQuery } from "@/helpers";
import { usePertubuhanContext } from "../PertubuhanProvider";
import { Box } from "@mui/material";

export const PertubuhanMesyuaratById = () => {
  const { getInitialValue } =
    useFormManagementMeetingCreateBySocietyIdInitialValue();
  const { id, meetingId: meetingIdEncoded } = useParams();
  const meetingId = atob(meetingIdEncoded || "");

  const { societyDetailData: societyData } = usePertubuhanContext();
  const { data: meetingResponse } = useQuery<{ data: any }>({
    url: `society/meeting/${meetingId}`,
  });

  const [currentMeetingData, setCurrentMeetingData] = useState(
    () => meetingResponse?.data?.data ?? null
  );

  return (
    <Box sx={{ mt: 2 }}>
      <Formik
        initialValues={getInitialValue({ societyData, currentMeetingData })}
        onSubmit={() => {}}
        enableReinitialize
      >
        <FormMeetingCreateBySocietyIdInner
          readOnly={true}
          includeMesyuaratPenubuhan={true}
          attachmentComponent={
            <FileUploader
              societyId={societyData.id}
              type={DocumentUploadType.MEETING}
              meetingId={parseInt(meetingId!)}
              validTypes={[]}
              disabled={true}
              onLoadComplete={(items) => {
                const selectedItem = items?.[0] ?? null;
                if (selectedItem) {
                  setCurrentMeetingData((prev: any) => ({
                    ...prev,
                    meetingMinute: selectedItem.name,
                    meetingMinuteURL: selectedItem.url,
                  }));
                }
              }}
            />
          }
        />
      </Formik>
    </Box>
  );
};
