import { Fragment, useState, useEffect, useCallback } from "react";
import { FieldValues } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  Button,
  Divider,
  SvgIcon,
  useTheme,
  debounce,
  CircularProgress,
} from "@mui/material";
import FilterListIcon from "@mui/icons-material/FilterList";
import { ButtonPrimary } from "../../../components/button";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../api";
import {
  NewSocietyBranchStatus,
  BranchStatusList,
  ApplicationStatusList,
} from "../../../helpers/enums";
import TrashIcon from "@/assets/svg/icon-trash.svg?react";
import { TimerIcon } from "../../../components/icons/timerIcon";
import { capitalizeWords, formatArrayDate } from "@/helpers";
import { useDispatch } from "react-redux";
import { setExtensionItems } from "@/redux/extensionTimeDataReducer";
import { useSelector } from "react-redux";
import { BranchListResponseBodyGet } from "@/types";
import { DialogConfirmation, IColumn } from "@/components";
import { EyeIcon, SearchIcon } from "@/components/icons";
import { useForm } from "@refinedev/react-hook-form";
import useQuery from "@/helpers/hooks/useQuery";
import { DataTable } from "@/components";
import { resetBranchAmendInfo } from "@/redux/branchAmendReducer";
import { setBranchDataRedux } from "@/redux/branchDataReducer";
import { fetchSocietyByIdData } from "@/redux/APIcalls/societyByIdThunks";
import { AppDispatch } from "@/redux/store";

const ListCawangan = <
  BranchData extends BranchListResponseBodyGet = BranchListResponseBodyGet
>() => {
  const { t } = useTranslation();
  const theme = useTheme();
  const primaryColor = theme.palette.primary.main;
  const navigate = useNavigate();
  const { id: societyId } = useParams();
  const [selectedBranchToDelete, setSelectedBranchToDelete] =
    useState<BranchData | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isSecretary, setIsSecretary] = useState(false);
  const [branchList, setBranchList] = useState<any>([]);
  const dispatch: AppDispatch = useDispatch();

  const { mutateAsync: mutateDeleteBranch, isLoading: isLoadingDelete } =
    useCustomMutation();

  const { watch, setValue } = useForm<FieldValues>({
    defaultValues: {
      name: "",
      sortBy: null,
      sortDir: null,
      page: 1,
      rowsPerPage: 5,
    },
  });

  // old society/branch/getBranchesByParam
  const {
    data,
    isLoading,
    refetch: fetchBranchList,
  } = useQuery({
    url: "society/branch/getUserBranches",
    filters: [
      { field: "name", operator: "eq", value: watch("name") },
      { field: "societyId", operator: "eq", value: societyId },
      { field: "sortBy", operator: "eq", value: watch("sortBy") },
      { field: "sortDir", operator: "eq", value: watch("sortDir") },
      { field: "pageSize", operator: "eq", value: watch("rowsPerPage") },
      { field: "pageNo", operator: "eq", value: watch("page") },
    ],
    autoFetch: true,
    onSuccess(data) {
      dispatch(resetBranchAmendInfo());
      if (societyId) {
        dispatch(
          fetchSocietyByIdData({
            id: societyId,
          })
        );
      }
    },
  });

  const handleCloseDialog = () => {
    setSelectedBranchToDelete(null);
    setShowDeleteDialog(false);
  };
  const confirmDeleteBranch = (data: any) => {
    setSelectedBranchToDelete(data);
    setShowDeleteDialog(true);
  };
  const deleteBranch = async () => {
    try {
      if (selectedBranchToDelete) {
        await mutateDeleteBranch({
          url: `${API_URL}/society/branch/update`,
          method: "put",
          values: {
            id: selectedBranchToDelete.id,
            status: "-001",
            applicationStatusCode: -1,
          },
          config: {
            headers: {
              "Content-Type": "application/json",
              portal: localStorage.getItem("portal") || "",
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => ({
            message: data?.data?.msg,
            type: "success",
          }),
          errorNotification: (data) => ({
            message: data?.response?.data?.msg,
            type: "error",
          }),
        });
      }
    } finally {
      handleCloseDialog();
      await fetchBranchList();
    }
  };

  const EditIcon = (props: any) => (
    <SvgIcon {...props}>
      <path
        d="M17,20H1c-0.6,0-1-0.4-1-1V3c0-0.6,0.4-1,1-1h9v2H2v14h14v-8h2v9C18,19.6,17.6,20,17,20z"
        fill="#147C7C"
      />
      <path
        d="M9.3,10.7c-0.4-0.4-0.4-1,0-1.4l9-9c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-9,9C10.3,11.1,9.7,11.1,9.3,10.7z"
        fill="#147C7C"
      />
    </SvgIcon>
  );

  const {
    data: societyDataById,
    loading: loadingSociety,
    error: errorSociety,
  } = useSelector((state: any) => state.societyByIdData);

  const goCreateExtensionTime = (data: any) => {
    const { id, branchApplicationNo } = data;
    dispatch(
      setExtensionItems({
        societyId: societyId,
        societyNo: societyDataById?.societyNo,
        branchId: id,
        branchNo: branchApplicationNo,
      })
    );
    navigate("/pertubuhan/paparan-pertubuhan/cawangan/lanjut-masa/create");
  };

  const handleSearchFilter = useCallback(
    debounce(
      ({
        target: { value },
      }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setValue("page", 1);
        setValue("name", value?.trim() || undefined);
      },
      1000
    ),
    []
  );

  const columns: IColumn<BranchData>[] = [
    {
      field: "colorCode",
      // headerName: t("namaCawangan"),
      flex: 1,
      align: "left",
      headerAlign: "center",
      renderCell: (params) => {
        const row = params?.row;
        const status = row?.applicationStatusCode;
        // const currentDate = new Date();
        // const ninetyDaysAgo = new Date();
        // ninetyDaysAgo.setDate(currentDate.getDate() - 90);

        if (row?.migrate) {
          return (
            <Box
              sx={{
                width: 15,
                height: 15,
                borderRadius: 1,
                bgcolor: "var(--indicator-grey)",
              }}
            />
          );
        } else if (status === 8) {
          return (
            <Box
              sx={{
                width: 15,
                height: 15,
                borderRadius: 1,
                bgcolor: "var(--error)",
              }}
            />
          );
        } else if (
          status === 9 &&
          !row?.isExtended &&
          !row?.isOnGoingExtensionTime
        ) {
          return (
            <Box
              sx={{
                width: 15,
                height: 15,
                borderRadius: 1,
                bgcolor: "var(--primary-color)",
              }}
            />
          );
        } else if (status === 9 && row?.isOnGoingExtensionTime) {
          return (
            <Box
              sx={{
                width: 15,
                height: 15,
                borderRadius: 1,
                bgcolor: "var(--indicator-yellow)",
              }}
            />
          );
        } else if (row?.migrateStat) {
          return (
            <Box
              sx={{
                width: 15,
                height: 15,
                borderRadius: 1,
                bgcolor: "var(--indicator-grey)",
              }}
            />
          );
        } else {
          return;
        }
      },
    },
    {
      field: "name",
      headerName: t("namaCawangan"),
      flex: 1,
      align: "left",
      headerAlign: "left",
    },
    {
      field: "applicationStatusCode",
      headerName: t("applicationStatus"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const status = ApplicationStatusList.find(
          (item) => item.id === params?.row?.applicationStatusCode
        );
        return capitalizeWords(t(status?.value ?? "-"));
      },
      cellClassName: "custom-cell",
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (params: any) => {
        const isActive = params?.row?.status === NewSocietyBranchStatus.AKTIF_1;
        const branchStatus = BranchStatusList.find(
          (item) => item.value === params?.row?.status
        );
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            {branchStatus?.label && (
              <Typography
                className="status-pertubuhan-text"
                sx={{
                  backgroundColor: "#fff",
                  border: `2px solid ${
                    isActive ? "var(--success)" : "var(--error)"
                  }`,
                }}
              >
                {t(`${branchStatus?.label}`)}
              </Typography>
            )}
          </Box>
        );
      },
    },
    {
      field: "submissionDate",
      headerName: t("tarikhPemohonan"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: ({ row }) => {
        return (
          <Typography sx={{ fontSize: 13, fontWeight: "400 !important" }}>
            {formatArrayDate(row.submissionDate)}
          </Typography>
        );
      },
    },
    {
      field: "actions",
      headerName: t("action"),
      flex: 1,
      align: "right",
      headerAlign: "center",
      renderCell: (params) => {
        const row = params?.row;
        const branchStatus = row?.branchStatusCode;
        const createdDate = new Date(row.createdDate);
        const currentDate = new Date();
        const ninetyDaysAgo = new Date();
        ninetyDaysAgo.setDate(currentDate.getDate() - 90);
        if (branchStatus && branchStatus === 3) {
          return (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                columnGap: "0.5rem",
              }}
            >
              <IconButton
                color="primary"
                sx={{
                  minWidth: "3rem",
                  minHeight: "3rem",
                }}
                onClick={() => {
                  //temp
                  dispatch(setBranchDataRedux(params?.row));
                  navigate(
                    `/pertubuhan/society/${societyId}/senarai/cawangan/view`,
                    {
                      state: {
                        branchId: params?.row?.id,
                        branchNo: params?.row?.branchNo,
                      },
                    }
                  );
                }}
              >
                <EyeIcon color={primaryColor} />
              </IconButton>
            </Box>
          );
        }
        switch (row?.applicationStatusCode) {
          case 1:
          case 5:
          case 6:
          case 44:
            return (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  columnGap: "0.5rem",
                }}
              >
                <IconButton
                  style={{
                    minWidth: "2rem",
                    minHeight: "2rem",
                  }}
                  color="primary"
                  onClick={() => {
                    navigate(
                      `/pertubuhan/society/${societyId}/senarai/cawangan/register?id=${params?.row?.id}`
                    );
                  }}
                >
                  <EditIcon
                    sx={{
                      fontSize: "2rem",
                      width: "1rem",
                      height: "1rem",
                    }}
                  />
                </IconButton>

                <IconButton
                  style={{
                    minHeight: "2rem",
                    minWidth: "2rem",
                  }}
                  color="error"
                  onClick={() => confirmDeleteBranch(row)}
                >
                  <TrashIcon />
                </IconButton>
              </Box>
            );
          case 9:
            return (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  columnGap: "0.5rem",
                }}
              >
                {createdDate < ninetyDaysAgo ? (
                  <IconButton
                    color="primary"
                    sx={{
                      minWidth: "2rem",
                      minHeight: "2rem",
                    }}
                    onClick={() => {
                      navigate(
                        `/pertubuhan/society/${societyId}/senarai/cawangan/expired/maklumat?id=${params?.row?.id}`
                      );
                    }}
                  >
                    <EyeIcon
                      sx={{
                        fontSize: "2rem",
                        width: "1rem",
                        height: "1rem",
                      }}
                      color={primaryColor}
                    />
                  </IconButton>
                ) : (
                  <>
                    <IconButton
                      color="primary"
                      sx={{
                        minWidth: "2rem",
                        minHeight: "2rem",
                      }}
                      onClick={() => {
                        navigate(
                          `../cawangan/maklumat-am?id=${params?.row?.id}`
                        );
                      }}
                    >
                      <EditIcon
                        sx={{
                          mt: "2px",
                          fontSize: "2rem",
                          width: "1rem",
                          height: "1rem",
                        }}
                      />
                    </IconButton>

                    {row?.isExtended !== 1 && (
                      <IconButton
                        color="primary"
                        sx={{
                          minWidth: "2rem",
                          minHeight: "2rem",
                        }}
                        onClick={() => goCreateExtensionTime(params?.row)}
                      >
                        <TimerIcon
                          color={`${primaryColor} !important`}
                          sx={{
                            fontSize: "1rem",
                            width: "1rem",
                            height: "1rem",
                          }}
                        />
                      </IconButton>
                    )}
                  </>
                )}
              </Box>
            );
          case 8:
          case 3:
          case 4:
            return (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  columnGap: "0.5rem",
                }}
              >
                <IconButton
                  color="primary"
                  sx={{
                    minWidth: "2rem",
                    minHeight: "2rem",
                  }}
                  onClick={() => {
                    //temp
                    dispatch(setBranchDataRedux(params?.row));
                    navigate(
                      `/pertubuhan/society/${societyId}/senarai/cawangan/view`,
                      {
                        state: {
                          branchId: params?.row?.id,
                          branchNo: params?.row?.branchNo,
                        },
                      }
                    );
                  }}
                >
                  <EyeIcon
                    sx={{
                      fontSize: "2rem",
                      width: "1rem",
                      height: "1rem",
                    }}
                    color={primaryColor}
                  />
                </IconButton>
              </Box>
            );
          case 2:
            return (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  columnGap: "0.5rem",
                }}
              >
                <IconButton
                  color="primary"
                  sx={{
                    minHeight: "2rem",
                    minWidth: "2rem",
                  }}
                  onClick={() => {
                    //temp
                    dispatch(setBranchDataRedux(params?.row));
                    navigate(
                      `/pertubuhan/society/${societyId}/senarai/cawangan/view`,
                      {
                        state: {
                          branchId: params?.row?.id,
                          branchNo: params?.row?.branchNo,
                        },
                      }
                    );
                  }}
                >
                  <EyeIcon
                    sx={{
                      fontSize: "2rem",
                      width: "1rem",
                      height: "1rem",
                    }}
                    color={primaryColor}
                  />
                </IconButton>
              </Box>
            );
          case 36:
            return (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  columnGap: "0.5rem",
                }}
              >
                <IconButton
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignContent: "center",
                    aspectRatio: "1/1",
                  }}
                  onClick={() => {
                    navigate(`../cawangan/maklumat-am?id=${params?.row?.id}`, {
                      state: {
                        branchId: params?.row?.id,
                        branchNo: params?.row?.branchNo,
                      },
                    });
                  }}
                >
                  <EditIcon
                    sx={{
                      mt: "2px",
                      fontSize: "2rem",
                      width: "1rem",
                      height: "1rem",
                    }}
                  />
                </IconButton>
                <IconButton
                  color="primary"
                  sx={{
                    minWidth: "2rem",
                    minHeight: "2rem",
                  }}
                  onClick={() => {
                    navigate(
                      `/pertubuhan/society/${societyId}/senarai/cawangan/${params?.row?.id}/papar`,
                      {
                        state: {
                          branchId: params?.row?.id,
                          branchNo: params?.row?.branchNo,
                        },
                      }
                    );
                  }}
                >
                  <EyeIcon
                    sx={{
                      fontSize: "2rem",
                      width: "1rem",
                      height: "1rem",
                    }}
                    color={primaryColor}
                  />
                </IconButton>
              </Box>
            );
          default:
            return (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  columnGap: "0.5rem",
                }}
              >
                <IconButton
                  color="primary"
                  sx={{
                    minWidth: "3rem",
                    minHeight: "3rem",
                  }}
                  onClick={() => {
                    //temp
                    dispatch(setBranchDataRedux(params?.row));
                    navigate(
                      `/pertubuhan/society/${societyId}/senarai/cawangan/view`,
                      {
                        state: {
                          branchId: params?.row?.id,
                          branchNo: params?.row?.branchNo,
                        },
                      }
                    );
                  }}
                >
                  <EyeIcon color={primaryColor} />
                </IconButton>
              </Box>
            );
        }
      },
    },
  ];

  const sortAttributes = [
    { label: t("permohonan"), attribute: "applicationStatusCode" },
    { label: t("cawangan"), attribute: "name" },
  ];

  const handleSortClick = (attribute: string) => {
    if (watch("sortBy") === attribute) {
      setValue("sortDir", watch("sortDir") === "asc" ? "desc" : "asc");
    } else {
      setValue("sortBy", attribute);
      setValue("sortDir", "asc");
    }
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "36px",
    borderRadius: "16px",
    fontSize: "16px",
    fontWeight: "500 !important",
  };

  useEffect(() => {
    fetchBranchList();
  }, [
    watch("name"),
    watch("sortByApplicationStatus"),
    watch("sortByName"),
    watch("rowsPerPage"),
    watch("page"),
  ]);

  useEffect(() => {
    setBranchList(data?.data?.data?.data ?? []);
  }, [data]);

  const totalRecords = data?.data?.data?.total ?? 0;

  const { isLoading: checkisManageAuthorizedIsLoading } = useQuery({
    url: `society/isManageAuthorized`,
    filters: [{ field: "societyId", operator: "eq", value: societyId }],
    onSuccess: (data) => {
      setIsSecretary(data?.data?.data);
    },
  });

  return (
    <>
      {showDeleteDialog && selectedBranchToDelete && (
        <DialogConfirmation
          isMutating={isLoadingDelete}
          open={showDeleteDialog}
          onClose={handleCloseDialog}
          onAction={async () => await deleteBranch()}
          onConfirmationText={t("confirmDeleteBranch", {
            name: selectedBranchToDelete.name,
          })}
        />
      )}
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "bold" }}>
              {t("peringatan")} :
            </span>{" "}
            {t("peringatanHubungiJPPM")}
          </Typography>
        </Box>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", sm: "row" },
            justifyContent: "space-between",
            alignItems: { xs: "stretch", sm: "center" },
            gap: 2,
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: { xs: "column", sm: "row" },
              alignItems: { xs: "stretch", sm: "center", md: "center" },
              flex: 1,
              gap: 2,
              justifyContent: "center",
            }}
          >
            <TextField
              placeholder={t("namaCawangan")}
              variant="outlined"
              name="name"
              onChange={handleSearchFilter}
              fullWidth
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon
                      sx={{
                        color: "var(--text-grey-disabled)",
                        marginLeft: "8px",
                      }}
                    />
                  </InputAdornment>
                ),
              }}
              sx={{
                display: "block",
                boxSizing: "border-box",
                maxWidth: 570,
                marginInline: "auto",
                height: "40px",
                background: "var(--border-grey)",
                opacity: 0.5,
                border: "1px solid var(--text-grey)",
                borderRadius: "10px",
                "& .MuiOutlinedInput-root": {
                  height: "40px",
                  "& fieldset": {
                    border: "none",
                  },
                },
              }}
            />
          </Box>
        </Box>
        <Box
          sx={{
            maxWidth: "fit-content",
            minHeight: 40,
            display: "flex",
            marginInline: "auto",
            alignItems: "center",
            boxShadow: "0px 12px 12px rgba(234, 232, 232, 0.4)",
            borderRadius: "10px",
            padding: "4px",
            gap: "5px",
            mb: 2,
            mt: 2,
          }}
        >
          <Button
            variant="text"
            startIcon={<FilterListIcon sx={{ color: "#6B7280" }} />}
            sx={{
              height: "36px",
              color: "#6B7280",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#F9FAFB",
              },
            }}
          >
            {t("filterBy")}
          </Button>

          {sortAttributes.map((item, index) => (
            <Fragment key={`filter-${index}`}>
              <Divider
                orientation="vertical"
                flexItem
                sx={{ backgroundColor: "#E5E7EB" }}
              />
              <Button
                variant="text"
                onClick={() => handleSortClick(item.attribute)}
                endIcon={
                  watch("sortBy") === item.attribute && (
                    <SvgIcon sx={{ color: "#6B7280" }}>
                      <svg
                        style={{
                          fontSize: 12,
                          transform:
                            watch("sortDir") === "desc"
                              ? "rotate(180deg)"
                              : "none",
                        }}
                        width="9"
                        height="7"
                        viewBox="0 0 9 7"
                        fill="none"
                      >
                        <path
                          d="M1 1L4.5 6L8 1"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </SvgIcon>
                  )
                }
                sx={{
                  height: "36px",
                  color: "#6B7280",
                  textTransform: "none",
                  width: "190px",
                  justifyContent: "space-between",
                  "&:hover": {
                    backgroundColor: "#F9FAFB",
                  },
                }}
              >
                {item.label}
              </Button>
            </Fragment>
          ))}
        </Box>

        <Box mt={3} p={2}>
          <DataTable
            columns={columns as any}
            rows={branchList}
            page={watch("page")}
            rowsPerPage={watch("rowsPerPage")}
            totalCount={totalRecords}
            onPageChange={(newPage) => setValue("page", newPage)}
            onPageSizeChange={(newPageSize) =>
              setValue("rowsPerPage", newPageSize)
            }
            isLoading={isLoading}
          />
        </Box>
      </Box>

      {checkisManageAuthorizedIsLoading ? (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "300px",
          }}
        >
          <CircularProgress />
        </Box>
      ) : isSecretary ? (
        <>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography className="title" sx={sectionStyle}>
                {t("daftarCawanganBaru")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  className="title"
                  sx={{ color: "#666666", fontSize: 14 }}
                >
                  {t("daftarCawanganBaru")}
                </Typography>

                <ButtonPrimary
                  sx={{ fontWeight: 400 }}
                  onClick={() => {
                    navigate(
                      `/pertubuhan/society/${societyId}/senarai/cawangan/register`
                    );
                  }}
                >
                  {t("registerButton")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography className="title" sx={sectionStyle}>
                {t("daftarSetiausahaCawanganBaru")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  className="title"
                  sx={{ color: "#666666", fontSize: 14 }}
                >
                  {t("daftarSetiausahaCawanganBaru")}
                </Typography>

                <ButtonPrimary
                  sx={{ fontWeight: 400 }}
                  onClick={() => {
                    navigate(
                      `/pertubuhan/society/${societyId}/senarai/cawangan/pembaharuan`
                    );
                  }}
                >
                  {t("registerButton")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography className="title" sx={sectionStyle}>
                {t("pindaanNameAlamatBranch")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  className="title"
                  sx={{ color: "#666666", fontSize: 14 }}
                >
                  {t("pindaanNameAlamatBranch")}
                </Typography>

                <ButtonPrimary
                  sx={{ fontWeight: 400 }}
                  onClick={() => {
                    dispatch(resetBranchAmendInfo());
                    navigate(
                      `/pertubuhan/society/${societyId}/senarai/cawangan/branch-Info`
                    );
                  }}
                >
                  {t("pinda")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>
        </>
      ) : null}
    </>
  );
};

export default ListCawangan;
