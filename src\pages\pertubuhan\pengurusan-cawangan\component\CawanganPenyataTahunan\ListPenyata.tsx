import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  Typography,
  IconButton,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Grid,
  FormControl,
  Select,
  Stack,
  MenuItem,
} from "@mui/material";
import Searchbar from "@/components/bar/Searchbar";
import { ButtonPrimary } from "@/components/button";
import useQuery from "@/helpers/hooks/useQuery";
import { ApplicationStatusEnum, COMMITTEE_TASK_TYPE } from "@/helpers/enums";
import { EditIcon, EyeIcon } from "@/components/icons";
import { TrashIcon } from "@/components/icons";
import ConfirmationDialog from "@/components/dialog/confirm";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import {
  setIsViewStatement,
  setStatementDataRedux,
} from "@/redux/statementDataReducer";
import { useDispatch, useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { usejawatankuasaContext } from "../CawanganAJKKeahlian/jawatankuasa/jawatankuasaProvider";
import { ALiranTugas } from "../../AliranTugas";
import dayjs from "dayjs";
import { DataTable, IColumn } from "@/components";
import { CrudFilter } from "@refinedev/core";
import { debounce } from "lodash";
import { useBranchContext } from "@/pages/pertubuhan/BranchProvider";

const ListPenyata: React.FC = () => {
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const renderFormField = (
    label: string,
    component: React.ReactNode,
    required = false
  ) => (
    <Grid container spacing={2} alignItems="center" sx={{ mb: 1 }}>
      <Grid item xs={12} sm={3}>
        <Typography
          variant="body1"
          sx={{ color: "#666666", fontWeight: "400 !important", fontSize: 14 }}
        >
          {label}
          {required && <span style={{ color: "red" }}>*</span>}
        </Typography>
      </Grid>
      <Grid item xs={12} sm={9}>
        {component}
      </Grid>
    </Grid>
  );
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const { id: societyId } = useParams();
  const { t } = useTranslation();
  const [isValid, setIsValid] = useState(true); // Track validation state
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const currentYear = new Date().getFullYear();
  const establishedYear = branchDataRedux?.submissionDate?.split("-")[0];
  const [submittedYearsList, setSubmittedYearsList] = useState<number[]>([]);
  let years: any = [];
  if (establishedYear) {
    const todayDate = new Date().toISOString().slice(0, 10);
    const est = parseInt(establishedYear);
    const endYear = currentYear - 1;
    years = Array.from({ length: endYear - est + 1 }, (_, i) => endYear - i);
    const systemYear = new Date().getFullYear();
    if (systemYear > currentYear) {
      years.unshift(currentYear);
    }
    //filter submitted year and add color indicator
    years = years
      .filter((year: any) => !submittedYearsList.includes(year))
      .map((year: any) => {
        const deadline = dayjs(`${Number(year) + 1}-03-01`);
        const color = dayjs(todayDate).isAfter(deadline)
          ? "#FF0000"
          : "#1fde15";
        return { label: year, value: year, color };
      });
  }

  const [yearSelected, setYearSelected] = useState<number>(0);

  const navigate = useNavigate();
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const [statementId, setStatementId] = useState<number>(0);

  type Penyata = {
    id: number;
    statementId: number;
    societyId: number;
    statementYear: number;
    applicationStatusCode: number;
    submissionDate?: string;
  };

  const [ListPenyata, SetListPenyata] = useState<Penyata[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>("");

  const { fetchAjkList } = usejawatankuasaContext();

  const {
    isBlackListed,
    isAuthorized,
    isAliranModuleAccess,
    branchId,
    fetchAliranTugasAccess,
  } = useBranchContext();

  useEffect(() => {
    fetchAjkList();
    fetchAliranTugasAccess(COMMITTEE_TASK_TYPE.PENYATAAN_TAHUNAN);
  }, []);

  const isAccessible = !isBlackListed && (isAuthorized || isAliranModuleAccess);

  const { mutate: addPenyata } = useCustomMutation();

  const handleAktiviti = () => {
    if (yearSelected == 0) {
      setIsValid(false);
      return;
    }
    addPenyata(
      {
        url: `${API_URL}/society/statement/general/create`,
        method: "post",
        values: {
          branchId: branchId,
          societyId: parseInt(societyId as string),
          statementYear: yearSelected,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: (data) => {
          const statementId = data?.data?.data?.statementId || null;
          setStatementId(statementId);
          dispatch(
            setStatementDataRedux({
              statementId: statementId,
              statementYear: yearSelected,
              societyId: parseInt(societyId as string),
            })
          );
          refetch();
        },
      }
    );
  };

  const handleConfirmDeletePenyata = (statementId: number) => {
    setStatementId(statementId);
    setOpenConfirm(true);
  };

  const { mutate: deletePenyata, isLoading: isLoading } = useCustomMutation();

  const handleDeletePenyata = () => {
    deletePenyata(
      {
        url: `${API_URL}/society/statement/${statementId}/delete`,
        method: "put",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          refetch();
          setOpenConfirm(false);
        },
      }
    );
  };
  const dispatch = useDispatch();

  const handleEditPenyata = (data: Penyata) => {
    dispatch(setStatementDataRedux(data));
    navigate("cawangan-penyata-tahunan-agung");
  };

  const { refetch } = useQuery({
    url: "society/statement/general/list",
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchId },
    ],
    onSuccess: (data) => {
      const statementData = data?.data?.data?.data || [];
      if (statementData) {
        SetListPenyata(statementData);
        const submittedYear = statementData
          .filter((i: any) => i.applicationStatusCode === "17")
          .map((i: any) => i.statementYear);
        setSubmittedYearsList(submittedYear);
      }
    },
  });

  const {
    data: statementListData,
    isLoading: statementListIsLoading,
    refetch: refetchStatementList,
  } = useQuery({
    url: "society/statement/general/list",
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchId },
    ],
    autoFetch: false,
    onSuccess: (data) => {
      const statementData = data?.data?.data?.data || [];
      const total = data?.data?.data?.total;
      setTotal(total);
      if (statementData) {
        SetListPenyata(statementData);
        if (statementData.length > 0) {
          //Get submittedYear list
          const submittedYear = statementData
            //temp status for test
            .filter((i: any) => i.applicationStatusCode === "17")
            .map((i: any) => i.statementYear);
          setSubmittedYearsList(submittedYear);
        }
      }
    },
  });

  useEffect(() => {
    refetchStatementList();
  }, []);

  useEffect(() => {
    if (!societyId) {
      return;
    }
  }, []);

  const handleSearch = debounce((query: any) => {
    const filters: CrudFilter[] = [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchId },
      { field: "searchQuery", operator: "eq", value: query },
    ];
    refetchStatementList({ filters });
  }, 300);

  const handleChangePage = (newPage: number) => {
    const filters: CrudFilter[] = [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchId },
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: newPage, operator: "eq" },
    ];
    setPage(newPage);
    refetchStatementList({ filters });
  };

  const handlePageSizeChange = (newPageSize: number) => {
    const filters: CrudFilter[] = [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchId },
      { field: "pageSize", value: newPageSize, operator: "eq" },
      { field: "pageNo", value: page, operator: "eq" },
    ];
    setPageSize(newPageSize);
    refetchStatementList({ filters });
  };

  const getStatusColor = (
    status: number,
    statementYear: number,
    submissionDate?: string
  ): string => {
    // 1: "BELUM DIHANTAR",
    // 17: "SELESAI",
    // E_ROS-2781
    const todayDate = new Date().toISOString().slice(0, 10);
    const deadlineSelesai = dayjs(`${Number(statementYear) + 2}-03-01`);
    const deadlineBelum = dayjs(`${Number(statementYear) + 1}-03-01`);
    if (Number(status) === 17) {
      if (!submissionDate) return "000";
      return dayjs(submissionDate).isBefore(deadlineSelesai.add(1, "day"))
        ? "000"
        : "#FF0000";
    }
    if (Number(status) === 1) {
      return dayjs(todayDate).isAfter(deadlineBelum) ? "#FF0000" : "#1fde15";
    }
    return "000";
  };

  ///
  const columns: IColumn[] = [
    {
      field: `${t("tahunPenyata")}`,
      headerName: `${t("tahunPenyata")}`,
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        let checkStatusColor = getStatusColor(
          row.applicationStatusCode,
          row.statementYear,
          row.submissionDate
        );
        const isAccessable = isAccessible;
        return (
          <Box
            {...(isAccessable
              ? {
                  sx: {
                    color: checkStatusColor,
                  },
                }
              : {})}
          >{`Penyata tahunan tahun ${row.statementYear}`}</Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: `${t("statusPermohonan")}`,
      headerName: `${t("statusPermohonan")}`,
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return <Box>{t(ApplicationStatusEnum[row.applicationStatusCode])}</Box>;
      },
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      headerName: t("action"),
      align: "center",
      renderCell: ({ row }: any) => {
        const status = row.applicationStatusCode;
        const isSelesai = status === "17";
        return (
          <>
            {isAccessible ? (
              <>
                {isSelesai ? (
                  <IconButton
                    onClick={() => {
                      dispatch(setIsViewStatement(true));
                      handleEditPenyata(row);
                    }}
                  >
                    <EyeIcon sx={{ color: "var(--primary-color)" }} />
                  </IconButton>
                ) : (
                  <>
                    <IconButton
                      onClick={() => {
                        dispatch(setIsViewStatement(false));
                        handleEditPenyata(row);
                      }}
                    >
                      <EditIcon sx={{ color: "var(--primary-color)" }} />
                    </IconButton>
                    <IconButton
                      onClick={() =>
                        handleConfirmDeletePenyata(row.statementId)
                      }
                    >
                      <TrashIcon sx={{ color: "red" }} />
                    </IconButton>
                  </>
                )}
              </>
            ) : (
              <IconButton
                onClick={() => {
                  dispatch(setIsViewStatement(true));
                  handleEditPenyata(row);
                }}
              >
                <EyeIcon sx={{ color: "var(--primary-color)" }} />
              </IconButton>
            )}
          </>
        );
      },
      cellClassName: "custom-cell",
    },
  ];

  return (
    <>
      <Box
        mt={5}
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid container spacing={2}>
          <Searchbar
            xs={9}
            text={t("annualStatement")}
            title={false}
            filter={false}
            onSearchChange={(query) => handleSearch(query)} // Handle search input
          />
        </Grid>

        <DataTable
          columns={columns}
          rows={ListPenyata}
          isLoading={statementListIsLoading}
          page={page}
          rowsPerPage={pageSize}
          totalCount={total}
          onPageChange={handleChangePage}
          onPageSizeChange={handlePageSizeChange}
        />
      </Box>
      {isAccessible ? (
        <>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                background: "white",
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
                mb: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("tambahPenyataTahunan")}
              </Typography>
              {years
                ? renderFormField(
                    t("tambahPenyataTahunan"),
                    <FormControl fullWidth required>
                      <Select
                        error={!isValid}
                        size="small"
                        displayEmpty
                        required
                        value={yearSelected}
                        onChange={(e) =>
                          setYearSelected(e.target.value as number)
                        }
                        renderValue={(selected) => {
                          return !selected
                            ? t("pleaseSelect")
                            : (selected as number);
                        }}
                      >
                        <MenuItem value="0" disabled>
                          {t("pleaseSelect")}
                        </MenuItem>
                        {years.map((data: any) => (
                          <MenuItem
                            sx={{ color: data.color }}
                            key={data.label}
                            value={data.value}
                          >
                            {data.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>,
                    true
                  )
                : null}
              <Stack
                direction="row"
                spacing={2}
                mt={2}
                sx={{ pl: 1, width: "100%" }}
                justifyContent="flex-end"
              >
                <ButtonPrimary onClick={handleAktiviti}>
                  {t("add")}
                </ButtonPrimary>
              </Stack>
            </Box>
          </Box>
          {isAuthorized ? (
            <ALiranTugas
              branchId={branchId}
              module={COMMITTEE_TASK_TYPE.PENYATAAN_TAHUNAN}
            />
          ) : (
            <></>
          )}
        </>
      ) : (
        <></>
      )}

      <ConfirmationDialog
        status={1}
        open={openConfirm}
        onClose={() => setOpenConfirm(false)}
        title={t("confirmDelete")}
        message={`${t("deleteConfirmationMessage")}`}
        onConfirm={handleDeletePenyata}
        onCancel={() => setOpenConfirm(false)}
      />
    </>
  );
};

export default ListPenyata;
