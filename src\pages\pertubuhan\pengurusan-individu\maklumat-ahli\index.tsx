import {
  Box,
  Divider,
  Grid,
  IconButton,
  InputBase,
  Menu,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import TuneIcon from "@mui/icons-material/Tune";
import AddIcon from "@mui/icons-material/Add";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DeleteIcon from "@/assets/svg/icon-trash.svg?react";
import EditIcon from "@mui/icons-material/Edit";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { ButtonPrimary } from "../../../../components/button";
import { HideOrDisplayInherit } from "../../../../helpers/enums";

const MaklumatAhli: React.FC = () => {
  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openMenuId, setOpenMenuId] = useState<string | number | null>(null);
  const [ahliList, setAhliList] = useState([
    {
      id: 1,
      namaAhli: "Abdul rashid",
      emel: "<EMAIL>",
      negeri: "Selangor",
    },
    {
      id: 2,
      namaAhli: "Hajah",
      emel: "<EMAIL>",
      negeri: "Selangor",
    },
    {
      id: 3,
      namaAhli: "Ali",
      emel: "<EMAIL>",
      negeri: "Selangor",
    },
    {
      id: 4,
      namaAhli: "Aminah",
      emel: "<EMAIL>",
      negeri: "Selangor",
    },
    {
      id: 5,
      namaAhli: "Sara",
      emel: "<EMAIL>",
      negeri: "Selangor",
    },
    {
      id: 6,
      namaAhli: "Farid",
      emel: "<EMAIL>",
      negeri: "Selangor",
    },
    {
      id: 7,
      namaAhli: "Sharifah",
      emel: "<EMAIL>",
      negeri: "Selangor",
    },
  ]);
  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const handleMenuOpen = (
    event: React.MouseEvent<HTMLElement>,
    id: string | number
  ) => {
    setAnchorEl(event.currentTarget);
    setOpenMenuId(id);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setOpenMenuId(null);
  };

  const handleUpdateDocument = () => {
    // Implement view document logic
    handleMenuClose();
  };

  const handleViewDocument = () => {
    // Implement view document logic
    handleMenuClose();
  };

  const handleDeleteDocument = () => {
    // Implement delete document logic
    handleMenuClose();
  };

  const handleDaftarAhli = () => {
    navigate("/pertubuhan/pengurusan-individu/maklumat-ahli/create-ahli");
  };

  return (
    <Box sx={{ p: { xs: 2, sm: 3 }, mt: 3, ml: 7, mr: 7 }}>
      <Grid container spacing={2}>
        <Grid item xs={12} lg={8}>
          <Box alignItems={"center"} gap={3} display={"flex"}>
            <span style={{ textWrap: "nowrap" }}>{t("searchInfo")}</span>
            <Paper
              component="form"
              sx={{
                p: "2px 4px",
                display: "flex",
                alignItems: "center",
                width: "100%",
              }}
            >
              <IconButton type="button" sx={{ p: "10px" }} aria-label="search">
                <SearchIcon />
              </IconButton>
              <InputBase
                sx={{ ml: 1, flex: 1 }}
                placeholder={t("enterMemberName")}
              />

              <IconButton sx={{ p: "10px" }} aria-label="menu">
                <TuneIcon />
              </IconButton>
            </Paper>
          </Box>
        </Grid>
        <Grid item xs={12} lg={4}>
          <Box display={"flex"} justifyContent={"flex-end"}>
            <ButtonPrimary onClick={handleDaftarAhli}>
              <AddIcon />
              {t("memberRegister")}
            </ButtonPrimary>
          </Box>
        </Grid>
      </Grid>

      <Box
        sx={{
          backgroundColor: "#e0f2f1",
          px: 2,
          py: 1,
          borderRadius: 2.5,
          mt: 5,
        }}
      >
        <Typography
          variant="h6"
          component="h2"
          sx={{ fontWeight: "bold", fontSize: 14 }}
        >
          {t("memberList")}
        </Typography>
      </Box>

      <Typography
        variant="body2"
        sx={{ textAlign: "right", mb: 1, color: "blue", mt: 3 }}
      >
        {t("officerCount2", { count: ahliList.length })}
      </Typography>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: "none",
          border: "1px solid #e0e0e0",
          backgroundColor: "white",
          borderRadius: 2.5 * 1.5,
          p: 1,
          mb: 3,
        }}
      >
        <Table>
          <TableHead>
            <TableRow>
              <TableCell
                sx={{
                  fontWeight: "bold",
                  borderBottom: "1px solid #e0e0e0",
                  color: "black",
                  p: 1,
                }}
              >
                {t("memberName")}
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: "bold",
                  borderBottom: "1px solid #e0e0e0",
                  color: "black",
                  p: 1,
                }}
              >
                {t("email")}
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: "bold",
                  borderBottom: "1px solid #e0e0e0",
                  color: "black",
                  p: 1,
                }}
              >
                {t("state")}
              </TableCell>
              <TableCell
                align="right"
                sx={{
                  fontWeight: "bold",
                  borderBottom: "1px solid #e0e0e0",
                  color: "black",
                  p: 1,
                }}
              >
                {t("activity")}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {ahliList.map((row, index) => (
              <TableRow key={row.id}>
                <TableCell
                  sx={{
                    color: "black",
                    borderBottom: "1px solid #e0e0e0",
                    p: 1,
                  }}
                >
                  {row.namaAhli}
                </TableCell>
                <TableCell
                  sx={{
                    color: "black",
                    borderBottom: "1px solid #e0e0e0",
                    p: 1,
                  }}
                >
                  {row.emel}
                </TableCell>
                <TableCell
                  sx={{
                    color: "black",
                    borderBottom: "1px solid #e0e0e0",
                    p: 1,
                  }}
                >
                  {row.negeri}
                </TableCell>
                <TableCell
                  align="right"
                  sx={{ borderBottom: "1px solid #e0e0e0", p: 1 }}
                >
                  <IconButton
                    onClick={(event) => handleMenuOpen(event, row.id)}
                  >
                    <MoreVertIcon sx={{ color: "black" }} />
                  </IconButton>
                  <Menu
                    anchorEl={anchorEl}
                    open={openMenuId === row.id}
                    onClose={handleMenuClose}
                    slotProps={{
                      paper: {
                        elevation: 0,
                        sx: {
                          backgroundColor: "white",
                          color: "black",
                          boxShadow: "0 4px 4px rgba(0, 0, 0, 0.25)",
                          "& .MuiMenuItem-root": {
                            color: "black",
                            "& .MuiSvgIcon-root": {
                              color: "black",
                            },
                          },
                          "& .MuiDivider-root": {
                            my: 0.5,
                          },
                        },
                      },
                    }}
                    transformOrigin={{ horizontal: "right", vertical: "top" }}
                    anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
                  >
                    <MenuItem
                      onClick={handleUpdateDocument}
                      sx={{ display: HideOrDisplayInherit }}
                    >
                      <EditIcon sx={{ mr: 2 }} />
                      {t("update")}
                    </MenuItem>
                    <MenuItem onClick={handleViewDocument}>
                      <VisibilityIcon sx={{ mr: 2 }} />
                      {t("viewMemberInfo")}
                    </MenuItem>
                    <Divider
                      variant="middle"
                      component="li"
                      sx={{ borderColor: "#e0e0e0" }}
                    />
                    <MenuItem onClick={handleDeleteDocument}>
                      <DeleteIcon style={{ marginRight: "0.5rem" }} />
                      {t("deleteInfo")}
                    </MenuItem>
                  </Menu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default MaklumatAhli;
