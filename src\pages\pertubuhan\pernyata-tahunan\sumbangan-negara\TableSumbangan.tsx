import TableContainer from "@mui/material/TableContainer";
import Table from "@mui/material/Table";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import Paper from "@mui/material/Paper";
import TableBody from "@mui/material/TableBody";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import { EditIcon } from "../../../../components/icons";
import { TrashIcon } from "../../../../components/icons";
import { Sumbangan } from "../interface";
import { getLocalStorage, toDollarFormat } from "../../../../helpers/utils";

export interface TableSumbanganProps {
  items: Sumbangan[];
  headerCountry: string;
  handleEdit: (id: number, contributionCode: string) => void;
  handleDelete: (id: number) => void;
  isDisable: boolean;
}

export const TableSumbangan: React.FC<TableSumbanganProps> = ({
  items,
  headerCountry,
  handleEdit,
  handleDelete,
  isDisable,
}) => {

  const { t } = useTranslation();

  const addressList = getLocalStorage("address_list", null); 

  function getCountryLabelById(id: string): string | undefined {
    const country = addressList
      .filter((item: any) => item.pid === 0)
      .map((item: any) => ({
        label: item.name,
        value: item.id.toString(),
      }))
      .find((item: any) => item.value === id);
    return country?.label; // Returns undefined if not found
  }

  return (
    <TableContainer component={Paper} sx={{ boxShadow: "none" }}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell
              sx={{
                fontWeight: "bold",
                borderBottom: "1px solid #e0e0e0",
                color: "black",
                p: 1,
              }}
            >
              {t("contributionProvider")}
            </TableCell>
            <TableCell
              sx={{
                fontWeight: "bold",
                borderBottom: "1px solid #e0e0e0",
                color: "black",
                p: 1,
              }}
            >
              {headerCountry}
            </TableCell>
            <TableCell
              sx={{
                fontWeight: "bold",
                borderBottom: "1px solid #e0e0e0",
                color: "black",
                p: 1,
              }}
            >
              {t("value")}
            </TableCell>
            <TableCell
              align="right"
              sx={{
                fontWeight: "bold",
                borderBottom: "1px solid #e0e0e0",
                color: "black",
                p: 1,
              }}
            ></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {items.map((row, index) => (
            <TableRow key={row.id}>
              <TableCell
                sx={{ color: "black", borderBottom: "1px solid #e0e0e0", p: 1 }}
              >
                {row.contribution}
              </TableCell>
              <TableCell
                sx={{ color: "black", borderBottom: "1px solid #e0e0e0", p: 1 }}
              >
                {getCountryLabelById(row.countryOrigin)}
              </TableCell>
              <TableCell
                sx={{ color: "black", borderBottom: "1px solid #e0e0e0", p: 1 }}
              >
                RM {toDollarFormat(row.value)}
              </TableCell>
              <TableCell
                align="right"
                sx={{ borderBottom: "1px solid #e0e0e0", p: 1 }}
              >
                {isDisable ? null : (
                  <>
                    <IconButton
                      onClick={() => handleEdit(row.id, row.contributionCode)}
                    >
                      <EditIcon sx={{ color: "var(--primary-color)" }} />
                    </IconButton>
                    <IconButton onClick={() => handleDelete(row.id)}>
                      <TrashIcon sx={{ color: "red" }} />
                    </IconButton>
                  </>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default TableSumbangan;
