import { Route, Outlet, Navigate } from "react-router-dom";
import Profile from "../pages/profile";
import Password from "../pages/profile/password";
import { registerRoutes } from "../helpers/routeDetector";
import { RouteGuard } from "../components/RouteGuard";
import AuthHelper from "@/helpers/authHelper";
import { NEW_PermissionNames } from "@/helpers";

// Layout component to wrap all profile routes with protection
const ProfileLayout = () => {
  if (
    !AuthHelper.hasAuthority([NEW_PermissionNames.PROFIL.label]) &&
    localStorage.getItem("portal") === "2"
  ) {
    return <Navigate to="/forbidden" replace />;
  }
  return (
    <RouteGuard
      autoUpdatePortal={true}
      showDebugInfo={process.env.NODE_ENV === "development"}
    >
      <Outlet />
    </RouteGuard>
  );
};

// Register profile routes with their portal types
registerRoutes({
  "/profile": "shared",
  "/profile/password": "shared",
});

export const profile = {
  routes: (
    <Route path="/profile" element={<ProfileLayout />}>
      <Route index element={<Profile />} />
      <Route path="password" element={<Password />} />
    </Route>
  ),
};
