import { useState, use<PERSON><PERSON>back, ChangeEvent, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { CrudFilter } from "@refinedev/core";
import { useTranslation } from "react-i18next";
import { useForm, FieldValues } from "react-hook-form";
import { useQuery, useMutation, debounce } from "@/helpers";

import {
  Box,
  Typography,
  TextField,
  MenuItem,
  Select,
  SelectChangeEvent,
  Button,
  Divider,
} from "@mui/material";
import { Search } from "@mui/icons-material";
import { ButtonPrimary } from "@/components/button";
import DataTable, { IColumn } from "@/components/datatable";
import DialogConfirmation from "@/components/dialog/confirm/dialog-confirmation";

import StatusChip from "@/components/status-chip";
import ActionButtons from "./view/ActionButtons";

import { FilterIcon } from "@/components/icons";
import { useSelector } from "react-redux";
import { getUserPermission } from "@/redux/userReducer";
import { GridFilterListIcon } from "@mui/x-data-grid";

const filterOptions = [
  { value: 2, label: "Menunggu Keputusan" },
  { value: 1, label: "Belum dihantar" },
  { value: 3, label: "Lulus" },
  { value: 36, label: "Kuiri" },
  { value: 4, label: "Tolak" },
  // { value: 23, label: "Menunggu Maklumbalas" },
];

export const PembaharuanSetiausaha: React.FC = () => {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const isMyLanguage = i18n.language === "my";
  const [selectedSecretaryId, setSelectedSecretaryId] = useState<number | null>(
    null
  );
  const [isSuccessDeletingSecretary, setIsSuccessDeletedSecretary] =
    useState(false);

  const columns: IColumn[] = [
    { field: "societyName", headerName: t("namaPertubuhan"), flex: 3 },
    {
      field: "applicationStatusCode",
      headerName: t("statusPermohonan"),
      flex: 2,
      align: "center",
      renderCell: ({ row }: any) => (
        <StatusChip
          statusCode={row.applicationStatusCode}
          useStatusColor={false}
        />
      ),
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => (
        <ActionButtons
          id={row.id}
          onDelete={() => setSelectedSecretaryId(row.id)}
          statusCode={Number(row.applicationStatusCode)}
        />
      ),
    },
  ];

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      pageNo: 1,
      pageSize: 10,
      societyName: "",
      applicationStatusCode: "",
    },
  });

  const pageNo = watch("pageNo");
  const pageSize = watch("pageSize");
  const societyName = watch("societyName");
  const applicationStatusCode = watch("applicationStatusCode");

  const filters = useMemo(() => {
    const filterList: CrudFilter[] = [
      {
        field: "pageNo",
        operator: "eq",
        value: pageNo,
      },
      {
        field: "pageSize",
        operator: "eq",
        value: pageSize,
      },
    ];

    if (societyName) {
      filterList.push({
        field: "societyName",
        operator: "eq",
        value: societyName,
      });
    }

    if (applicationStatusCode) {
      filterList.push({
        field: "applicationStatusCode",
        operator: "eq",
        value: applicationStatusCode,
      });
    }

    return filterList;
  }, [societyName, applicationStatusCode, pageNo, pageSize]);

  const {
    data: secretaryPrincipalRes,
    isLoading: isLoadingSecretaryList,
    refetch: fetchSecretary,
  } = useQuery({
    url: "society/secretary/principal/get-all",
    filters,
  });

  const { fetch: deleteSecretary, isLoading: isDeletingSecretary } =
    useMutation({
      url: `society/secretary/principal/${selectedSecretaryId ?? undefined}`,
      method: "delete",
      onSuccess: () => {
        setIsSuccessDeletedSecretary(true);
        fetchSecretary();
      },
    });

  const secretaryPrincipalList = secretaryPrincipalRes?.data?.data?.data ?? [];
  const totalList = secretaryPrincipalRes?.data?.data?.total ?? 0;

  const handleChangePage = (newPage: number) => setValue("pageNo", newPage);

  const handleChangePageSize = (newPageSize: number) =>
    setValue("pageSize", newPageSize);

  const handleSearchSocietyName = useCallback(
    debounce((e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const value = e.target.value;

      setValue("societyName", value);
    }, 500),
    []
  );

  const handleFilterChange = (event: SelectChangeEvent) => {
    setValue("applicationStatusCode", event.target.value as string);
  };

  const handleCloseDialog = () => {
    setSelectedSecretaryId(null);
    setIsSuccessDeletedSecretary(false);
  };

  const handleDeleteSecretary = () => deleteSecretary();

  return (
    <>
      <Box sx={{ maxWidth: "80%" }}>
        <Box
          sx={{
            backgroundColor: "white",
            borderRadius: "16px",
            p: { xs: 3, sm: 4 },
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.05)",
          }}
        >
          <Box
            sx={{
              display: "flex",
              gap: 2,
              mb: 5,
              mx: { xs: 2, md: "auto" },
              maxWidth: "600px",
              width: "100%",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <Box
              sx={{
                display: "flex",
                flex: 1,
                backgroundColor: "#F8F8F8",
                borderRadius: "8px",
                alignItems: "center",
                px: 1,
                border: "1px solid #EAEAEA",
                width: "100%",
              }}
            >
              <Search sx={{ color: "#666666", mr: 1 }} />
              <TextField
                placeholder="Nama pertubuhan"
                variant="standard"
                fullWidth
                InputProps={{
                  disableUnderline: true,
                }}
                sx={{
                  "& input": {
                    p: "8px 0",
                  },
                }}
                onChange={handleSearchSocietyName}
              />
            </Box>

            <Box
              sx={{
                maxWidth: "fit-content",
                minHeight: 40,
                display: "flex",
                marginInline: "auto",
                alignItems: "center",
                boxShadow: "0px 12px 12px rgba(234, 232, 232, 0.4)",
                borderRadius: "10px",
                padding: "4px",
                gap: "5px",
                mb: 2,
                mt: 2,
              }}
            >
              <Button
                variant="text"
                startIcon={
                  <GridFilterListIcon
                    sx={{ fontSize: "16px !important", color: "#6B7280" }}
                  />
                }
                disabled
                sx={{
                  // flex: 1,
                  color: "#6B7280",
                  textTransform: "none",
                  textWrap: "nowrap",
                  justifyContent: "center",
                  alignItems: "center",
                  display: "flex",
                  width: "150px",
                  "&:disabled": { color: "#6B7280" },
                }}
              >
                {t("filterBy")}
              </Button>
              <Divider
                orientation="vertical"
                sx={{ backgroundColor: "#E5E7EB", height: 30 }}
              />
              <Select
                value={applicationStatusCode}
                onChange={handleFilterChange}
                displayEmpty
                variant="standard"
                sx={{
                  flex: 1,
                  ml: 1,
                  minWidth: 120,
                  "&:before": { borderBottom: "none" },
                  "&:after": { borderBottom: "none" },
                  "&:hover:not(.Mui-disabled):before": { borderBottom: "none" },
                  "& .MuiSelect-select:focus": {
                    backgroundColor: "transparent",
                  },
                  "& .MuiTouchRipple-root": {
                    display: "none",
                  },
                  "& .MuiSelect-select": {
                    p: "0",
                    color: "#666666",
                    fontSize: "14px",
                  },
                }}
              >
                <MenuItem value="" disabled>
                  Tapis mengikut
                </MenuItem>
                {filterOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </Box>
          </Box>

          <Box
            sx={{
              maxWidth: "475px",
              mx: "auto",
            }}
          >
            <DataTable
              columns={columns}
              rows={secretaryPrincipalList}
              page={pageNo}
              rowsPerPage={pageSize}
              totalCount={totalList}
              onPageChange={handleChangePage}
              onPageSizeChange={handleChangePageSize}
              pagination={true}
              isLoading={isLoadingSecretaryList}
            />
          </Box>
        </Box>

        <Box
          sx={{
            backgroundColor: "white",
            borderRadius: "16px",
            p: { xs: 3, sm: 4 },
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.05)",
            marginTop: 2,
          }}
        >
          <Box
            sx={{
              border: "1px solid #DADADA",
              borderRadius: "8px",
              p: { xs: 2, sm: 3 },
              backgroundColor: "transparent",
            }}
          >
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontSize: "16px",
                fontWeight: "bold",
                mb: 2,
              }}
            >
              {t("pembaharuanSetiausaha")}
            </Typography>

            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography
                sx={{
                  color: "#666666",
                  fontSize: "14px",
                }}
              >
                {t("additionSecretaryRenewal")}
              </Typography>

              <ButtonPrimary
                onClick={() => navigate("tambah")}
                sx={{
                  backgroundColor: "var(--primary-color)",
                  color: "white",
                  "&:hover": { backgroundColor: "#19ADAD" },
                  textTransform: "none",
                  fontWeight: "bold",
                }}
              >
                {t("mohon")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Box>
      </Box>

      <DialogConfirmation
        open={!!selectedSecretaryId}
        onConfirmationText={t("confirmDeleteApplication")}
        onSuccessText={
          isMyLanguage
            ? "Setiausaha berjaya dipadam"
            : "The secretary was successfully deleted."
        }
        isSuccess={isSuccessDeletingSecretary}
        isMutating={isDeletingSecretary}
        onClose={handleCloseDialog}
        onAction={handleDeleteSecretary}
      />
    </>
  );
};

export default PembaharuanSetiausaha;
