import { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Checkbox from "@mui/material/Checkbox";
import type { SelectChangeEvent } from "@mui/material/Select";
import InputLabel from "@mui/material/InputLabel";
import { useTranslation } from "react-i18next";
import { ButtonPrimary } from "../../../../components/button";
import { Select, Option } from "../../../../components/input";
import { Fade, Grid } from "@mui/material";
import { useNavigate, useSearchParams } from "react-router-dom";
import { API_URL } from "../../../../api";
import { DialogConfirmation } from "@/components";
import { useMutation } from "@/helpers";
import usePaymentService from "@/helpers/hooks/usePaymentService";
import { useDispatch, useSelector } from "react-redux";
import {
  setCalculatedPayment,
  selectCalculatedPayment,
} from "@/redux/paymentReducer";
import { PaymentItemCode } from "@/helpers/enums";

interface PaymentPermissionData {
  payment: string;
  alert: string;
}
interface PaymentPermission {
  data: PaymentPermissionData;
  msg: string | null;
}

export const ListBayaran = () => {
  const [paymentMethod, setPaymentMethod] = useState("");
  const { t } = useTranslation();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [onlinePayment, setOnlinePayment] = useState<
    PaymentPermission | undefined
  >({
    data: {
      payment: "",
      alert: "",
    },
    msg: null,
  });

  const [searchParams] = useSearchParams();
  const appealId = searchParams.get("appealId");
  const societyId = searchParams.get("societyId");

  const navigate = useNavigate();
  const [isChecked, setIsChecked] = useState(false);

  // Payment service and Redux
  const {
    calculatePayment,
    processPayment,
    loading: paymentLoading,
  } = usePaymentService();
  const dispatch = useDispatch();
  const calculatedPayment = useSelector(selectCalculatedPayment);

  const {
    fetchAsync: updateAppealApplication,
    isLoading: isLoadingUpdateAppealApplication,
  } = useMutation({
    url: "society/appeal/appealApplication",
    msgSuccess: "Permohonan berjaya dihantar.",
    method: "put",
  });

  const handleChange = (event: SelectChangeEvent) => {
    setPaymentMethod(event.target.value as string);
  };

  const updateAppealApplicationByPaymentMethod = async (
    paymentMethod: string
  ) => {
    const isKaunterPayment = paymentMethod === "kaunter";
    await updateAppealApplication({
      id: appealId,
      applicationStatusCode: isKaunterPayment ? 5 : 6,
    });
    navigate(
      `${
        isKaunterPayment ? "kaunter" : "online"
      }?appealId=${appealId}&societyId=${societyId}`
    );
  };
  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const handleKaunterPayment = async (calculatedPaymentData?: any) => {
    try {
      const paymentData = calculatedPaymentData;

      if (!paymentData) {
        throw new Error("Payment calculation not available");
      }

      // Process payment using calculated data
      const getUserDetails = localStorage.getItem("user-details");
      const email = getUserDetails ? JSON.parse(getUserDetails).email : "";

      const processPaymentRequest = {
        societyId: societyId,
        appealId: appealId,
        amount: paymentData.totalAmount,
        email: email,
        signature: paymentData.signature,
      };

      await processPayment(processPaymentRequest);
      await updateAppealApplicationByPaymentMethod("kaunter");
    } catch (error) {
      console.error("Error in kaunter payment:", error);
      throw error;
    }
  };

  const handleOnlinePayment = async () => {
    try {
      // Use the calculated payment from Redux (already calculated in action handler)
      if (!calculatedPayment) {
        throw new Error("Payment calculation not available");
      }

      await updateAppealApplicationByPaymentMethod("online");
    } catch (error) {
      console.error("Error in online payment:", error);
      throw error;
    }
  };

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsChecked(event.target.checked);
  };

  const handleSubmit = () => {
    handleOpenDialog();
  };

  const fetchOnlinePaymentPermission = async () => {
    try {
      const response = await fetch(
        `${API_URL}/society/admin/integration/payment/status`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log("paymentD", data);
      setOnlinePayment(data);
    } catch (error) {
      console.error("Failed to fetch data:", error);
      return null;
    }
  };

  useEffect(() => {
    fetchOnlinePaymentPermission();
  }, []);

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box sx={{ width: "55vw" }}>
        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3,
            }}
          >
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: "500 !important",
                  }}
                >
                  {t("payment")}
                </Typography>
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color: "#666666",
                  fontFamily: "Poppins",
                  fontSize: 14,
                  fontWeight: "400 !important",
                  lineHeight: "21px",
                  textAlign: "left",
                  textUnderlinePosition: "from-font",
                  textDecorationSkipInk: "none",
                  mb: 2,
                }}
              >
                {t("agreementText")}
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "flex-start",
                  gap: 1,
                }}
              >
                <Checkbox
                  id="akuan-setuju-terima"
                  checked={isChecked}
                  onChange={handleCheckboxChange}
                  sx={{
                    color: "#00A7A7",
                    "&.Mui-checked": {
                      color: "#00A7A7",
                    },
                    padding: "0",
                  }}
                />
                <InputLabel
                  htmlFor="akuan-setuju-terima"
                  required
                  sx={{
                    color: "#333333",
                    fontSize: 14,
                    fontWeight: 400,
                    lineHeight: 1.4,
                    "& .MuiFormLabel-asterisk": {
                      color: "red",
                    },
                  }}
                >
                  {t("agreementAcceptance")}
                </InputLabel>
              </Box>
            </Box>

            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 3,
              }}
            >
              <Box sx={{ mb: 2 }}>
                <Box sx={{ mb: 1 }}>
                  <Typography
                    variant="h6"
                    component="h2"
                    sx={{
                      color: "#00A7A7",
                      fontSize: 16,
                      fontWeight: "500 !important",
                    }}
                  >
                    {t("paymentMethod")}
                  </Typography>
                </Box>
                <Grid
                  container
                  rowSpacing={1}
                  columnSpacing={{ xs: 1, sm: 2, md: 3 }}
                >
                  <Grid item xs={4}>
                    {/* <Typography>1</Typography> */}
                  </Grid>
                  <Grid item xs={8}>
                    <Typography
                      sx={{
                        fontFamily: "Poppins, sans-serif",
                        fontSize: "12px",
                        fontWeight: 500,
                        lineHeight: "14px",
                        color: "#FF0000",
                      }}
                    >
                      {onlinePayment?.data?.alert
                        ? onlinePayment?.data?.alert
                        : null}
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <InputLabel
                      htmlFor="cara-pembayaran"
                      required
                      sx={{
                        color: "#333333",
                        fontSize: 14,
                        fontWeight: 400,
                        minWidth: "150px",
                        "& .MuiFormLabel-asterisk": {
                          color: "red",
                        },
                      }}
                    >
                      {t("paymentMethod")}
                    </InputLabel>
                  </Grid>
                  <Grid item xs={8}>
                    <Select
                      value={paymentMethod}
                      onChange={handleChange}
                      id="cara-pembayaran"
                      t={t}
                      sx={{
                        width: "100%",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#E5E5E5",
                          borderRadius: 1,
                        },
                      }}
                    >
                      <Option value="kaunter">{t("pembayaranKaunter")}</Option>
                      <Option
                        disabled={
                          onlinePayment?.data?.payment === "ENABLED"
                            ? false
                            : true
                        }
                        value="online"
                      >
                        {t("pembayaranOnline")}
                      </Option>
                    </Select>
                  </Grid>
                </Grid>
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color: "#666666",
                  fontSize: 12,
                  marginTop: 10,
                  textAlign: "center",
                }}
              >
                {t("paymentNote")}
              </Typography>
            </Box>

            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              <ButtonPrimary
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  borderRadius: 1,
                  textTransform: "none",
                }}
                disabled={!isChecked || !paymentMethod}
                onClick={handleSubmit}
              >
                {t("hantar")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>

        <DialogConfirmation
          open={dialogOpen}
          onClose={handleCloseDialog}
          onConfirmationText={t("confirmSubmitApplication")}
          onSuccessText="Permohonan berjaya dihantar."
          isMutating={isLoadingUpdateAppealApplication || paymentLoading}
          onAction={async () => {
            try {
              // Calculate payment first before calling payment methods
              const itemCode =
                paymentMethod === "kaunter"
                  ? PaymentItemCode.RAYUAN_KAUNTER
                  : PaymentItemCode.RAYUAN_ONLINE;

              const calculateRequest = {
                items: [
                  {
                    itemCode: itemCode,
                    quantity: 1,
                  },
                ],
              };

              const calculationResult = await calculatePayment(
                calculateRequest
              );

              if (!calculationResult.data) {
                throw new Error("Failed to calculate payment");
              }

              // Store calculated payment in Redux
              dispatch(setCalculatedPayment(calculationResult.data));

              // Then call the appropriate payment method
              if (paymentMethod === "kaunter") {
                handleKaunterPayment(calculationResult.data);
              } else {
                handleOnlinePayment();
              }
            } finally {
              handleCloseDialog();
            }
          }}
        />
      </Box>
    </Box>
  );
};

export default ListBayaran;
