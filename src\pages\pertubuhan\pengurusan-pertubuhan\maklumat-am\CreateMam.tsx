import React, { ChangeEvent, useEffect, useState } from "react";
import {
  Box,
  TextField,
  Typography,
  Grid,
  Select,
  MenuItem,
  FormControl,
  useMediaQuery,
  Theme,
  Fade,
  FormHelperText,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { OrganizationStepper } from "../organization-stepper";
import { useCustom, useCreate } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { removeFromStorage } from "../perlembagaan/removeFasal";
import { LoadingOverlay } from "../../../../components/loading";
import { ApplicationStatus, MALAYSIA } from "../../../../helpers/enums";
import InfoQACard from "../InfoQACard";
import CustomPopover from "../../../../components/popover";
import { Switch } from "../../../../components/switch";
import { useDispatch, useSelector } from "react-redux";
import { setSocietyDataRedux } from "../../../../redux/societyDataReducer";
import { setAddressDataRedux } from "../../../../redux/addressDataReducer";
import AWSLocationMap from "@/components/geocoder/geocoder";
import {
  LocationClient,
  SearchPlaceIndexForTextCommand,
} from "@aws-sdk/client-location";
import { withAPIKey } from "@aws/amazon-location-utilities-auth-helper";
import { capitalizeWords, useQuery } from "@/helpers";
import { useCheckAndUpdateRegistration } from "@/helpers/hooks/useCheckAndupdateRegister";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const sectionStyleCustom = {
  color: "var(--primary-color)",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
  mt: 1,
};

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

export const CreateMam: React.FC = () => {
  const { t } = useTranslation();
  const [businessState, setBusinessState] = useState("");
  const [businessDistrict, setBusinessDistrict] = useState("");

  const [mailingState, setMailingState] = useState("");
  const [mailingDistrict, setMailingDistrict] = useState("");
  const [applicationStatusCode, setApplicationStatusCode] = useState(0);
  const [client, setClient] = useState<LocationClient | null>(null);
  const PLACE_INDEX_NAME = import.meta.env.VITE_AWS_PLACE_INDEX;
  const AWS_REGION = import.meta.env.VITE_AWS_API_KEY_REGION;
  const API_KEY = import.meta.env.VITE_AWS_API_KEY;

  const [isCreated, setIsCreated] = useState(false);

  const [sameAddress, setSameAddress] = useState(false);
  const [isNameExist, setIsNameExist] = useState("");

  const [activeStep, setActiveStep] = useState(0);
  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  // Add new state for coordinates
  const [organizationCoords, setOrganizationCoords] = useState<
    [number, number]
  >([101.707021, 2.745564]);
  const [businessCoords, setBusinessCoords] = useState<[number, number]>([
    101.707021, 2.745564,
  ]);

  const phoneRegex = /^\+60\d{8,10}$/;
  const [oldName, setOldName] = useState("");
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState({
    societyName: "",
    stateCode: "",
    districtCode: "",
    city: "",
    postcode: "",
    address: "",
    mailingStateCode: "",
    mailingDistrictCode: "",
    mailingCity: "",
    mailingPostcode: "",
    mailingAddress: "",
    phoneNumber: "",
    email: "",
    faxNumber: "",
    addressLongitude: 101.707021,
    addressLatitude: 2.745564,
    mailingAddressLongitude: 101.707021,
    mailingAddressLatitude: 2.745564,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
    setFormErrors((prev) => ({ ...prev, [name]: "" }));
    if (name === "address") {
      clearTimeout((window as any).addressTimeout);
      (window as any).addressTimeout = setTimeout(() => {
        searchLocation(value, "address");
      }, 500);
    } else if (name === "mailingAddress") {
      clearTimeout((window as any).addressTimeoutMailing);
      (window as any).addressTimeoutMailing = setTimeout(() => {
        searchLocation(value, "mailingAddress");
      }, 500);
    }
  };

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    // Organization name validation
    if (!formData.societyName) {
      errors.societyName = t("requiredValidation");
    } else {
      const namePattern = /^(pertubuhan|persatuan|kelab)\s/i;
      if (!namePattern.test(formData.societyName)) {
        errors.societyName =
          "Nama pertubuhan hendaklah bermula dengan Pertubuhan/ persatuan/ kelab";
      }
    }

    if (!formData.stateCode) errors.stateCode = t("requiredValidation");
    if (!formData.districtCode) errors.districtCode = t("requiredValidation");
    // if (!formData.city) errors.city = t("requiredValidation");
    if (!formData.postcode) errors.postcode = t("requiredValidation");
    if (!formData.address) errors.address = t("requiredValidation");
    if (!formData.mailingStateCode)
      errors.mailingStateCode = t("requiredValidation");
    if (!formData.mailingDistrictCode)
      errors.mailingDistrictCode = t("requiredValidation");
    // if (!formData.mailingCity) errors.mailingCity = t("requiredValidation");
    if (!formData.mailingPostcode)
      errors.mailingPostcode = t("requiredValidation");
    if (!formData.phoneNumber) errors.phoneNumber = t("requiredValidation");
    if (!formData.email) errors.email = t("requiredValidation");

    // Phone number validation
    if (!formData.phoneNumber) {
      errors.phoneNumber = t("requiredValidation");
    } else {
      if (!phoneRegex.test(formData.phoneNumber)) {
        errors.phoneNumber = t("invalidPhoneNumber");
      }
    }

    // Email validation
    if (!formData.email) {
      errors.email = t("requiredValidation");
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        errors.email = t("invalidEmail");
      }
    }

    // Add postcode validation
    if (!formData.postcode) {
      errors.postcode = t("requiredValidation");
    } else if (!/^\d{5}$/.test(formData.postcode)) {
      errors.postcode = t("postcodeValidation");
    }

    if (!formData.mailingPostcode) {
      errors.mailingPostcode = t("requiredValidation");
    } else if (!/^\d{5}$/.test(formData.mailingPostcode)) {
      errors.mailingPostcode = t("postcodeValidation");
    }

    return errors;
  };

  const { data, isLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData = data?.data?.data || [];

  const { mutate: createSociety, isLoading: isCreating } = useCreate();

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");
  const societyId = atob(encodedId || "");
  //const [queryText, setQueryText] = useState("");

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);
  //console.log("societyDataRedux",societyDataRedux);
  const dispatch = useDispatch();

  useEffect(() => {
    if (addressData.length > 0) {
      dispatch(setAddressDataRedux(addressData));
    }
  }, [addressData.length]);

  const handleSave = () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    const data = {
      ...formData,
      createdBy: "user1",
    };

    if (encodedId) {
      // If encodedId exists, use update endpoint
      const decodedId = atob(encodedId);
      createSociety(
        {
          resource: `society/${decodedId}/edit`,
          values: data,
          meta: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
            method: "put",
          },
          successNotification: () => ({
            message: t("messageMaklumatAmSuccess"),
            type: "success",
          }),
          errorNotification: () => ({
            message: t("messageMaklumatAmError"),
            type: "error",
          }),
        },
        {
          onSuccess(data, variables, context) {
            // const temp = {
            //   ...data?.data?.data,
            //   queryText: societyDataRedux.queryText,
            // };
            // dispatch(setSocietyDataRedux(temp));
            fetchSocietyData();
            navigate("../maklumat-am?id=" + encodedId);
          },
        }
      );
    } else {
      // If no encodedId, use create endpoint
      createSociety(
        {
          resource: "society/register",
          values: data,
          meta: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            if (data?.data?.status == "SUCCESS") {
              localStorage.removeItem("societyIdEncoded");
              localStorage.setItem(
                "societyIdEncoded",
                btoa(data?.data?.data?.id)
              );

              setIsCreated(true);
              return {
                message: t("messageMaklumatAmSuccess"),
                type: "success",
              };
            } else {
              return {
                message: data?.data?.msg,
                type: "error",
              };
            }
          },
          errorNotification: () => ({
            message: t("messageMaklumatAmError"),
            type: "error",
          }),
        },
        {
          onSuccess(data, variables, context) {
            if (data?.data?.status == "SUCCESS") {
              localStorage.removeItem("meetingCreateRequest");
              localStorage.removeItem("committeeCreateRequests");
              localStorage.removeItem("nonCitizenCommitteeCreateRequests");
              localStorage.removeItem("documentCreateRequest");
              localStorage.removeItem("organizationGoals");
              // const temp = {
              //   ...data?.data?.data,
              //   queryText: societyDataRedux.queryText,
              // };
              // dispatch(setSocietyDataRedux(temp));
              fetchSocietyData();
              removeFromStorage();
              navigate("../maklumat-am?id=" + btoa(data?.data?.data?.id));
            }
          },
        }
      );
    }
  };

  const { data: societyRefetchedData, refetch: fetchSocietyData } = useCustom({
    url: `${API_URL}/society/${encodedId && atob(encodedId)}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !!encodedId,
      retry: false,
      cacheTime: 0,
      onSuccess: (responseData) => {
        dispatch(setSocietyDataRedux(responseData?.data?.data));
      },
    },
  });

  const [isLoadingData, setIsLoadingData] = useState(false);

  useEffect(() => {
    if (societyDataRedux && encodedId) {
      localStorage.setItem("societyIdEncoded", encodedId);
      setOldName(societyDataRedux.societyName);
      setBusinessState(societyDataRedux.stateCode);
      setFormData((prevState) => ({
        ...prevState,
        stateCode: societyDataRedux.stateCode,
      }));
      setBusinessDistrict(societyDataRedux.districtCode);

      // Set mailing address data
      setMailingState(societyDataRedux.mailingStateCode);
      setMailingDistrict(societyDataRedux.mailingDistrictCode);
      setApplicationStatusCode(societyDataRedux.applicationStatusCode);
      if (
        societyDataRedux?.addressLatitude &&
        societyDataRedux?.addressLongitude
      ) {
        setOrganizationCoords([
          societyDataRedux?.addressLongitude,
          societyDataRedux?.addressLatitude,
        ]);
      }
      if (
        societyDataRedux?.mailingAddressLatitude &&
        societyDataRedux?.mailingAddressLongitude
      ) {
        setBusinessCoords([
          societyDataRedux?.mailingAddressLongitude,
          societyDataRedux?.mailingAddressLatitude,
        ]);
      }

      setFormData({
        societyName: societyDataRedux.societyName,
        stateCode: societyDataRedux.stateCode,
        districtCode: societyDataRedux.districtCode,
        city: societyDataRedux.city,
        postcode: societyDataRedux.postcode,
        address: societyDataRedux.address,
        mailingAddress: societyDataRedux.mailingAddress,
        mailingStateCode: societyDataRedux.mailingStateCode,
        mailingDistrictCode: societyDataRedux.mailingDistrictCode,
        mailingCity: societyDataRedux.mailingCity,
        mailingPostcode: societyDataRedux.mailingPostcode,
        phoneNumber: societyDataRedux.phoneNumber,
        email: societyDataRedux.email,
        faxNumber: societyDataRedux.faxNumber,
        addressLatitude: societyDataRedux?.addressLatitude,
        addressLongitude: societyDataRedux?.addressLongitude,
        mailingAddressLatitude: societyDataRedux?.mailingAddressLatitude,
        mailingAddressLongitude: societyDataRedux?.mailingAddressLongitude,
      });

      const isAddressMatching =
        societyDataRedux.stateCode === societyDataRedux.mailingStateCode &&
        societyDataRedux.districtCode ===
          societyDataRedux.mailingDistrictCode &&
        societyDataRedux.city === societyDataRedux.mailingCity &&
        societyDataRedux.postcode === societyDataRedux.mailingPostcode &&
        societyDataRedux.address === societyDataRedux.mailingAddress;

      setSameAddress(isAddressMatching);

      setIsCreated(true);
      setIsLoadingData(false);
    }
  }, [societyDataRedux]);

  const handleSwitchOnChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { checked } = event.target;

    setSameAddress(checked);

    if (checked) {
      setFormData((prevState) => ({
        ...prevState,
        mailingStateCode: prevState.stateCode,
        mailingDistrictCode: prevState.districtCode,
        mailingCity: prevState.city,
        mailingPostcode: prevState.postcode,
        mailingAddress: prevState.address,
        mailingAddressLatitude: prevState.addressLatitude,
        mailingAddressLongitude: prevState.addressLongitude,
      }));

      setBusinessCoords([organizationCoords[0], organizationCoords[1]]);

      setMailingState(businessState);
      setMailingDistrict(businessDistrict);
    }
  };

  // Add marker icon fix
  // Add new function to handle form reset
  const handleReset = () => {
    // Reset form data to initial state
    setFormData({
      societyName: "",
      stateCode: "",
      districtCode: "",
      city: "",
      postcode: "",
      address: "",
      mailingStateCode: "",
      mailingDistrictCode: "",
      mailingCity: "",
      mailingPostcode: "",
      phoneNumber: "",
      email: "",
      mailingAddress: "",
      faxNumber: "",
      addressLongitude: 101.707021,
      addressLatitude: 2.745564,
      mailingAddressLongitude: 101.707021,
      mailingAddressLatitude: 2.745564,
    });

    // Reset all other state variables
    setBusinessState("");
    setBusinessDistrict("");
    setMailingState("");
    setMailingDistrict("");
    setSameAddress(false);
    setFormErrors({});

    // Reset coordinates to default values
    setOrganizationCoords([101.707021, 2.745564]);
    setBusinessCoords([101.707021, 2.745564]);
  };

  const handleLocationSelected = (location: {
    fullAddress: string;
    state: string;
    stateId: string | number;
    district: string;
    districtId: string | number;
    city: string;
    postcode: string;
    geometry: number[];
  }) => {
    setBusinessState(`${location.stateId}`);
    setFormData((prev) => ({
      ...prev,
      stateCode: `${location.stateId}`,
      districtCode: `${location.districtId}`,
      address: location.fullAddress,
      city: location.city,
      postcode: location.postcode,
      addressLongitude: location.geometry[0],
      addressLatitude: location.geometry[1],
    }));
  };

  const handleMailingLocationSelected = (location: {
    fullAddress: string;
    state: string;
    stateId: string | number;
    district: string;
    districtId: string | number;
    city: string;
    postcode: string;
    geometry: number[];
  }) => {
    setMailingState(`${location.stateId}`);
    setFormData((prev) => ({
      ...prev,
      mailingStateCode: `${location.stateId}`,
      mailingDistrictCode: `${location.districtId}`,
      mailingAddress: location.fullAddress,
      mailingCity: location.city,
      mailingPostcode: location.postcode,
      mailingAddressLongitude: location.geometry[0],
      mailingAddressLatitude: location.geometry[1],
    }));
  };

  // ✅ AWS Location Client Initialization
  useEffect(() => {
    const initializeClient = async () => {
      try {
        const authHelper = await withAPIKey(API_KEY, AWS_REGION);
        const locationClient = new LocationClient(authHelper.getClientConfig());
        setClient(locationClient);
      } catch (error) {
        console.error("❌ Failed to initialize AWS Location Client:", error);
      }
    };
    initializeClient();
  }, []);

  const searchLocation = async (
    query: string,
    type: "address" | "mailingAddress"
  ) => {
    if (!query) return;
    if (!client) {
      console.warn("❌ AWS Location Client has not been initialized");
      return;
    }
    try {
      const command = new SearchPlaceIndexForTextCommand({
        IndexName: PLACE_INDEX_NAME,
        Text: query,
        MaxResults: 1,
        FilterCountries: ["MYS"],
      });
      const response = await client.send(command);
      console.log("place", response);
      if (!response?.Results || response.Results.length === 0) {
        console.warn("⚠️ No locations found");
        return;
      }
      const firstResult = response.Results[0];
      console.log("firstResult", firstResult);

      if (firstResult?.Place?.Geometry?.Point) {
        const [longitude, latitude] = firstResult.Place.Geometry.Point;
        console.log("📍 Coordinates:", " lng: ", longitude, " Lat: ", latitude);

        const city = firstResult.Place.Municipality || "";
        const postcode = firstResult.Place.PostalCode || "";
        const stateName = firstResult.Place.Region || "";
        const districtName = firstResult.Place.SubRegion || "";
        setOrganizationCoords([longitude, latitude]);
        if (type === "address") {
          setOrganizationCoords([longitude, latitude]);
          setFormData((prev) => ({
            ...prev,
            city: city || prev.city,
            postcode: postcode || prev.postcode,
            addressLongitude: longitude,
            addressLatitude: latitude,
          }));
        } else if (type === "mailingAddress") {
          setBusinessCoords([longitude, latitude]);
          setFormData((prev) => ({
            ...prev,
            mailingCity: city || prev.mailingCity,
            mailingPostcode: postcode || prev.mailingPostcode,
            mailingAddressLongitude: longitude,
            mailingAddressLatitude: latitude,
          }));
        }
      }
    } catch (error) {
      console.error("Error searching location:", error);
    }
  };

  const [debouncedSocietyName, setDebouncedSocietyName] = useState("");
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSocietyName(formData?.societyName);
    }, 500); // Adjust 500ms delay as needed

    return () => clearTimeout(timer);
  }, [formData?.societyName]);

  useEffect(() => {
    if (debouncedSocietyName !== oldName && debouncedSocietyName?.length > 5) {
      refetch();
    }
  }, [debouncedSocietyName]);

  const {
    data: societyNameExist,
    isLoading: isLoadingSocietyNameExist,
    refetch,
  } = useQuery({
    autoFetch: false,
    url: `society/checkSocietyNameExists`,
    filters: [
      {
        field: "societyName",
        operator: "eq",
        value: debouncedSocietyName,
      },
    ],
    onSuccess: (data) => {
      const response = data?.data;

      const errors: { [key: string]: string } = {};

      if (response?.data === true) {
        errors.societyName = t(response?.msg);
        setIsNameExist(t(response?.msg));
      } else {
        setIsNameExist("");
      }

      if (!formData.societyName) {
        // Organization name validation
      } else {
        const namePattern = /^(pertubuhan|persatuan|kelab)\s/i;
        if (!namePattern.test(formData.societyName)) {
          errors.societyName =
            "Nama pertubuhan hendaklah bermula dengan Pertubuhan/ persatuan/ kelab";
        }
      }
      setFormErrors(errors);
    },
  });

  const { data: checkAndUpdateRegistrationData, fetch: fetchCheck } =
    useCheckAndUpdateRegistration({
      id: societyId ?? null,
      pageNo: 1,
      enabled: false,
      onSuccessNotification: (data) => {
        const responseData = data?.data?.data;
        //  const message = data?.data?.msg
        //  if(!responseData){
        //   return {
        //     message: message,
        //     type: "error",
        //   };
        //  }
      },
      onSuccess: (data) => {
        const responseData = data?.data?.data;
        if (responseData) {
          navigate(`../mesyuarat-penubuhan?id=${encodedId}`);
        }
      },
    });

  const handleGoNext = () => {
    fetchCheck();
  };

  const disableNameinput =
    (applicationStatusCode == ApplicationStatus.BELUM_DIHANTAR ||
      params.get("disabled") == "true") &&
    applicationStatusCode !== 36;

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box sx={{ width: "55vw" }}>
        <LoadingOverlay isLoading={isLoadingData} />
        <Fade in={true} timeout={500}>
          <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px" }}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mb: 2,
              }}
            >
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4} sx={{ mt: 2 }}>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <Typography sx={labelStyle}>
                      {t("organizationName")}
                    </Typography>
                    <CustomPopover
                      customStyles={{
                        maxWidth: "210px",
                        backgroundColor: "white",
                        mt: 1,
                      }}
                      content={
                        <Typography
                          sx={{
                            color: "#FF0000",
                            fontSize: "12px",
                          }}
                        >
                          {t("organizationNameHelper")}
                        </Typography>
                      }
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    required
                    name="societyName"
                    value={formData.societyName}
                    onChange={handleInputChange}
                    error={!!formErrors.societyName}
                    helperText={formErrors?.societyName}
                    placeholder="Contoh: Pertubuhan Perak, Kelab Perak"
                    disabled={disableNameinput}
                  />
                </Grid>
              </Grid>
            </Box>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mb: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("registeredAddressAndPlaceOfBusinessOfTheOrganization")}
              </Typography>
              <Grid container spacing={2} sx={{ mb: 6 }}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("petaLokasi")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <div
                    style={{
                      height: "150px",
                      width: "100%",
                      borderRadius: "8px",
                      overflow: "hidden",
                      position: "relative",
                    }}
                  >
                    <AWSLocationMap
                      longitude={organizationCoords[0]}
                      latitude={organizationCoords[1]}
                      onLocationSelected={handleLocationSelected}
                    />
                  </div>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("alamatTempatUrusan")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    required
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    error={!!formErrors.address}
                    helperText={formErrors.address}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("state")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <FormControl
                    fullWidth
                    required
                    error={!!formErrors.stateCode}
                  >
                    <Select
                      value={formData.stateCode}
                      displayEmpty
                      required
                      disabled={isLoading}
                      onChange={(e) => {
                        setBusinessState(e.target.value);
                        setFormData((prevState) => ({
                          ...prevState,
                          stateCode: e.target.value,
                        }));
                        setFormErrors((prev) => ({ ...prev, stateCode: "" }));
                      }}
                    >
                      <MenuItem value="" disabled>
                        {isLoading ? "Loading..." : t("pleaseSelect")}
                      </MenuItem>
                      {!isLoading &&
                        addressData
                          .filter((item: any) => item.pid === MALAYSIA)
                          .map((item: any) => (
                            <MenuItem key={item.id} value={`${item.id}`}>
                              {capitalizeWords(item.name, null, true)}
                            </MenuItem>
                          ))}
                    </Select>
                    {formErrors.stateCode && (
                      <FormHelperText>{formErrors.stateCode}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("district")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <FormControl
                    fullWidth
                    required
                    error={!!formErrors.districtCode}
                  >
                    <Select
                      value={formData.districtCode}
                      displayEmpty
                      required
                      onChange={(e) => {
                        setBusinessDistrict(e.target.value);
                        setFormData((prevState) => ({
                          ...prevState,
                          districtCode: e.target.value,
                        }));
                      }}
                      disabled={isLoading || !businessState}
                    >
                      <MenuItem value="" disabled>
                        {isLoading ? "Loading..." : t("selectPlaceholder")}
                      </MenuItem>
                      {!isLoading &&
                        addressData
                          .filter((item: any) => item.pid == businessState)
                          .map((item: any) => (
                            <MenuItem key={item.id} value={`${item.id}`}>
                              {capitalizeWords(item.name, null, true)}
                            </MenuItem>
                          ))}
                    </Select>
                    {formErrors.districtCode && (
                      <FormHelperText>{formErrors.districtCode}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("city")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    name="city"
                    value={formData.city}
                    error={!!formErrors.city}
                    helperText={formErrors.city}
                    onChange={handleInputChange}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("postcode")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    required
                    name="postcode"
                    value={formData.postcode}
                    error={!!formErrors.postcode}
                    helperText={formErrors.postcode || t("postcodeHelper")}
                    onChange={(e: any) => {
                      if (/^\d{0,5}$/.test(e.target.value)) {
                        handleInputChange(e);
                      }
                    }}
                    inputProps={{ pattern: "\\d{5}" }}
                  />
                </Grid>
              </Grid>
            </Box>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mb: 2,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  mb: 2,
                }}
              >
                <Typography variant="subtitle1" sx={sectionStyleCustom}>
                  {t("mailingAddress")}
                </Typography>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  {" "}
                  {/* Add spacing */}
                  <Typography
                    variant="body2"
                    sx={{ fontSize: "12px", color: "#66666680" }}
                  >
                    {t("sameAsAbove")}
                  </Typography>
                  <Switch
                    checked={sameAddress}
                    onChange={handleSwitchOnChange}
                  />
                </Box>
              </Box>
              {!sameAddress && (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("petaLokasi")} <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <div
                      style={{
                        height: "150px",
                        width: "100%",
                        borderRadius: "8px",
                      }}
                    >
                      <AWSLocationMap
                        longitude={businessCoords[0]}
                        latitude={businessCoords[1]}
                        // zoom={20}
                        onLocationSelected={handleMailingLocationSelected}
                      />
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("businessAddress")}{" "}
                      <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <TextField
                      fullWidth
                      required
                      disabled={sameAddress}
                      name="mailingAddress"
                      value={formData.mailingAddress}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("state")} <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <FormControl
                      fullWidth
                      required
                      error={!!formErrors.mailingStateCode}
                    >
                      <Select
                        value={mailingState}
                        displayEmpty
                        required
                        disabled={isLoading || sameAddress}
                        onChange={(e) => {
                          setMailingState(e.target.value);
                          setFormData((prevState) => ({
                            ...prevState,
                            mailingStateCode: e.target.value,
                          }));
                        }}
                      >
                        <MenuItem value="" disabled>
                          {isLoading ? "Loading..." : t("pleaseSelect")}
                        </MenuItem>
                        {!isLoading &&
                          addressData
                            .filter((item: any) => item.pid === MALAYSIA)
                            .map((item: any) => (
                              <MenuItem key={item.id} value={`${item.id}`}>
                                {capitalizeWords(item.name, null, true)}
                              </MenuItem>
                            ))}
                      </Select>
                      {formErrors.mailingStateCode && (
                        <FormHelperText>
                          {formErrors.mailingStateCode}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("district")} <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <FormControl
                      fullWidth
                      required
                      error={!!formErrors.mailingDistrictCode}
                    >
                      <Select
                        value={mailingDistrict}
                        displayEmpty
                        required
                        onChange={(e) => {
                          setMailingDistrict(e.target.value);
                          setFormData((prevState) => ({
                            ...prevState,
                            mailingDistrictCode: e.target.value,
                          }));
                        }}
                        disabled={isLoading || !mailingState || sameAddress}
                      >
                        <MenuItem value="" disabled>
                          {isLoading ? "Loading..." : t("selectPlaceholder")}
                        </MenuItem>
                        {!isLoading &&
                          addressData
                            .filter((item: any) => item.pid == mailingState)
                            .map((item: any) => (
                              <MenuItem key={item.id} value={`${item.id}`}>
                                {capitalizeWords(item.name, null, true)}
                              </MenuItem>
                            ))}
                      </Select>
                      {formErrors.mailingDistrictCode && (
                        <FormHelperText>
                          {formErrors.mailingDistrictCode}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>{t("city")}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <TextField
                      fullWidth
                      name="mailingCity"
                      value={formData.mailingCity}
                      error={!!formErrors.mailingCity}
                      helperText={formErrors.mailingCity}
                      onChange={handleInputChange}
                      disabled={sameAddress}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("postcode")} <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <TextField
                      fullWidth
                      required
                      name="mailingPostcode"
                      value={formData.mailingPostcode}
                      error={!!formErrors.mailingPostcode}
                      helperText={
                        formErrors.mailingPostcode || t("postcodeHelper")
                      }
                      onChange={(e: any) => {
                        if (/^\d{0,5}$/.test(e.target.value)) {
                          handleInputChange(e);
                        }
                      }}
                      inputProps={{ pattern: "\\d{5}" }}
                      disabled={sameAddress}
                    />
                  </Grid>
                </Grid>
              )}
            </Box>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mb: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("organizationContactInfo")}
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("mobileNumber")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    required
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    error={!!formErrors.phoneNumber}
                    helperText={
                      formErrors.phoneNumber || t("phoneNumberHelper")
                    }
                    inputProps={{
                      inputMode: "numeric",
                    }}
                    onChange={(e) => {
                      const input = e.target as HTMLInputElement;
                      let raw = input.value.replace(/[^\d]/g, "");

                      // Allow empty input
                      if (!raw) {
                        setFormData((prev) => ({
                          ...prev,
                          phoneNumber: "",
                        }));
                        setFormErrors((prev) => ({
                          ...prev,
                          phoneNumber: "",
                        }));
                        return;
                      }

                      // Remove leading 60 if present
                      if (raw.startsWith("60")) {
                        raw = raw.slice(2);
                      }

                      const limitedDigits = raw.slice(0, 10);
                      const formatted = "+60" + limitedDigits;

                      let error = "";
                      if (limitedDigits.length < 8) {
                        error = t("phoneDigitLimitWarning");
                      }

                      setFormData((prev) => ({
                        ...prev,
                        phoneNumber: formatted,
                      }));

                      setFormErrors((prev) => ({
                        ...prev,
                        phoneNumber: error,
                      }));
                    }}
                    onKeyDown={(e) => {
                      const input = e.target as HTMLInputElement;
                      const pos = input.selectionStart ?? 0;
                      const hasValue = input.value.length > 0;

                      // restrictions
                      if (hasValue) {
                        if (
                          (e.key.length === 1 && pos < 3) || // typing characters in +60
                          (e.key === "Backspace" && pos <= 3) || // backspacing into +60
                          (e.key === "Delete" && pos < 3) // deleting inside +60
                        ) {
                          e.preventDefault();
                        }
                      }
                    }}
                    onClick={(e) => {
                      const input = e.target as HTMLInputElement;
                      if (
                        input.value &&
                        input.selectionStart !== null &&
                        input.selectionStart < 3
                      ) {
                        // Move cursor to after +60 if user clicks inside prefix
                        setTimeout(() => {
                          input.setSelectionRange(3, 3);
                        }, 0);
                      }
                    }}
                    onFocus={(e) => {
                      const input = e.target as HTMLInputElement;
                      if (
                        input.value &&
                        input.selectionStart !== null &&
                        input.selectionStart < 3
                      ) {
                        // move cursor to after +60 on focus
                        setTimeout(() => {
                          input.setSelectionRange(3, 3);
                        }, 0);
                      }
                    }}
                    placeholder="+60"
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("businessOfficePhoneNumber")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    required
                    name="faxNumber"
                    value={formData.faxNumber}
                    error={!!formErrors.faxNumber}
                    onChange={(e) => {
                      // Only allow numbers and limit to 10 digits
                      const value = e.target.value
                        .replace(/[^\d]/g, "")
                        .slice(0, 10);
                      setFormData((prevState) => ({
                        ...prevState,
                        faxNumber: value,
                      }));
                    }}
                    type="tel"
                    inputProps={{
                      maxLength: 10,
                      pattern: "[0-9]*",
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("email")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    required
                    name="email"
                    value={formData.email}
                    error={!!formErrors.email}
                    helperText={formErrors.email || t("emailHelper")}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                  />
                </Grid>

                <Grid
                  item
                  xs={12}
                  sx={{
                    mt: 2,
                    display: "flex",
                    flexDirection: isMobile ? "column" : "row",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <ButtonOutline
                    sx={{
                      bgcolor: "white",
                      "&:hover": { bgcolor: "white" },
                      width: isMobile ? "100%" : "auto",
                    }}
                    onClick={handleReset}
                  >
                    {t("semula")}
                  </ButtonOutline>
                  <ButtonPrimary
                    variant="contained"
                    sx={{
                      width: isMobile ? "100%" : "auto",
                    }}
                    onClick={handleSave}
                    disabled={isCreating || isNameExist?.length > 0}
                  >
                    {t("update")}
                  </ButtonPrimary>
                </Grid>
              </Grid>
            </Box>
            <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 3 }}>
              <ButtonPrimary
                variant="contained"
                sx={{ width: isMobile ? "100%" : "auto" }}
                disabled={!isCreated}
                onClick={() => handleGoNext()}
              >
                {t("next")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <OrganizationStepper
          isMaklumatCreated={isCreated}
          activeStep={activeStep}
          hidePayment={
            societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI
          }
          //isFasalFinished={}
        />
        {societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI ? (
          <Box
            sx={{
              padding: 3,
              backgroundColor: "white",
              borderRadius: "15px",
              maxHeight: "60vh",
              maxWidth: "18vw",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("kuiri")}
            </Typography>
            <Box
              sx={{
                padding: 3,
                backgroundColor: "#DADADA",
                borderRadius: "15px",
                maxHeight: "30vh",
                maxWidth: "18vw",
                overflowY: "scroll",
              }}
            >
              <Typography
                sx={{
                  mb: 8,
                  fontSize: "12px",
                  color: "#666666",
                  fontWeight: "500 !important",
                  wordBreak: "break-word",
                }}
              >
                {societyDataRedux.queryText}
              </Typography>
            </Box>
          </Box>
        ) : null}
        <InfoQACard />
      </Box>
    </Box>
  );
};

export default CreateMam;
