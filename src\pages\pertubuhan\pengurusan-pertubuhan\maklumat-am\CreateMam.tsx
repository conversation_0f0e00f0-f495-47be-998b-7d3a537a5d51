import React, { useEffect, useState } from "react";
import { Box, Typography } from "@mui/material";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { OrganizationStepper } from "../organization-stepper";
import { BaseRecord } from "@refinedev/core";
import { removeFromStorage } from "../perlembagaan/removeFasal";
import { LoadingOverlay } from "../../../../components/loading";
import { ApplicationStatus } from "../../../../helpers/enums";
import InfoQACard from "../InfoQACard";
import { useDispatch, useSelector } from "react-redux";
import { setSocietyDataRedux } from "../../../../redux/societyDataReducer";
import { setAddressDataRedux } from "../../../../redux/addressDataReducer";
import {
  useMutation,
  useQuery,
  useYupRequiredInputValidation,
} from "@/helpers";
import { isValidPhoneNumber } from "libphonenumber-js/max";
import {
  FormGeneralInformationSocietyRegistrationInner,
  GeneralInformationSocietyRegistrationRequestBodyCreateOrUpdateForm,
} from "@/components/form/general-information/SocietyRegistrationInner";
import { FormikHelpers } from "formik";
import { IAddressNode } from "@/types";
import { object, string } from "yup";
import { Formik } from "@/contexts";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

export const CreateMam: React.FC = <
  ReqBody extends GeneralInformationSocietyRegistrationRequestBodyCreateOrUpdateForm = GeneralInformationSocietyRegistrationRequestBodyCreateOrUpdateForm,
  UpdateReqBody extends Omit<
    ReqBody,
    "createdDate" | "modifiedDate" | "registeredDate"
  > = Omit<ReqBody, "createdDate" | "modifiedDate" | "registeredDate">
>() => {
  const { t } = useTranslation();
  const strictRequired = useYupRequiredInputValidation();

  const [activeStep] = useState(0);
  const navigate = useNavigate();

  const validationSchema = object<ReqBody>({
    societyName: string()
      .required()
      .test(strictRequired)
      .test({
        name: "societyNameDoesntConflictWithOthers",
        message:
          "Nama pertubuhan hendaklah bermula dengan Pertubuhan/ persatuan/ kelab",
        test: (val) =>
          typeof val === "string" &&
          /^(pertubuhan|persatuan|kelab)\s/i.test(val),
      }),
    phoneNumber: string()
      .required()
      .test(strictRequired)
      .test({
        name: "validMYPhoneNumber",
        message: () => t("invalidPhoneNumberWithoutCountryCode"),
        test: (val) => typeof val === "string" &&
          isValidPhoneNumber(`+60${val}`, {
            defaultCountry: "MY",
          })
      }),
    address: string().required().test(strictRequired),
    stateCode: string().required().test(strictRequired),
    districtCode: string().required().test(strictRequired),
    postcode: string()
      .required()
      .test(strictRequired)
      .min(5, t("postcodeValidation"))
      .max(5, t("postcodeValidation")),
    mailingAddress: string().required().test(strictRequired),
    mailingStateCode: string().required().test(strictRequired),
    mailingDistrictCode: string().required().test(strictRequired),
    mailingPostcode: string()
      .required()
      .test(strictRequired)
      .min(5, t("postcodeValidation"))
      .max(5, t("postcodeValidation")),
    email: string().email().required().test(strictRequired),
  }).required();
  const { data: addressResponse } = useQuery<{ data: IAddressNode[] }>({
    url: `society/admin/address/list`,
  });
  const addressData = addressResponse?.data?.data ?? [];

  const [params] = useSearchParams();
  const encodedId = params.get("id");
  const societyId = encodedId ? atob(encodedId) : null;

  const societyDataRedux = useSelector(
    (state: any) => state.societyData.data
  ) as ReqBody;
  const dispatch = useDispatch();
  const { fetchAsync: updateSocietyRegistration } = useMutation<
    BaseRecord,
    UpdateReqBody
  >({
    url: `society/${societyId!}/edit`,
    method: "put",
    msgSuccess: "messageMaklumatAmSuccess",
    onSuccess: async () => {
      await fetchSocietyData();
      navigate(`../maklumat-am?id=${encodedId!}`);
    },
  });
  const { fetchAsync: registerSociety } = useMutation<
    { data: { id: string }; status: string },
    ReqBody
  >({
    url: "society/register",
    onSuccessNotification: (response) => {
      if (response?.data?.status == "SUCCESS") {
        localStorage.removeItem("societyIdEncoded");
        localStorage.setItem(
          "societyIdEncoded",
          btoa(response?.data?.data?.id)
        );

        return {
          message: t("messageMaklumatAmSuccess"),
          type: "success",
        };
      } else {
        return {
          message: response?.data?.msg,
          type: "error",
        };
      }
    },
    onErrorNotification: () => ({
      message: t("messageMaklumatAmError"),
      type: "error",
    }),
    onSuccess: async (response) => {
      if (response?.data?.status == "SUCCESS") {
        localStorage.removeItem("meetingCreateRequest");
        localStorage.removeItem("committeeCreateRequests");
        localStorage.removeItem("nonCitizenCommitteeCreateRequests");
        localStorage.removeItem("documentCreateRequest");
        localStorage.removeItem("organizationGoals");
        await fetchSocietyData();
        removeFromStorage();
        navigate(`../maklumat-am?id=${btoa(response?.data?.data?.id)}`);
      }
    },
  });

  useEffect(() => {
    if (addressData.length > 0) {
      dispatch(setAddressDataRedux(addressData));
    }
  }, [addressData.length]);

  const { refetch: fetchSocietyData, isLoading: isLoadingSocietyData } =
    useQuery({
      url: `society/${societyId!}`,
      autoFetch: false,
      onSuccess: (response) => {
        dispatch(setSocietyDataRedux(response?.data?.data));
      },
    });

  const initialValue: ReqBody =
    societyId !== null
      ? ({
          ...societyDataRedux,
          phoneNumber: societyDataRedux?.phoneNumber?.startsWith("+60")
            ? societyDataRedux?.phoneNumber?.slice(3)
            : societyDataRedux?.phoneNumber ?? "",
          sameAddress:
            societyDataRedux?.address === societyDataRedux?.mailingAddress &&
            societyDataRedux?.mailingAddressLatitude ===
              societyDataRedux?.addressLatitude &&
            societyDataRedux?.mailingAddressLongitude ===
              societyDataRedux?.addressLongitude &&
            societyDataRedux?.mailingCity === societyDataRedux?.city &&
            societyDataRedux?.mailingStateCode ===
              societyDataRedux?.stateCode &&
            societyDataRedux?.mailingDistrictCode ===
              societyDataRedux?.districtCode &&
            societyDataRedux?.mailingPostcode === societyDataRedux?.postcode,
        } as ReqBody)
      : ({
          id: null,
          societyName: "",
          stateCode: "",
          districtCode: "",
          city: "",
          postcode: "",
          address: "",
          mailingStateCode: "",
          mailingDistrictCode: "",
          mailingCity: "",
          mailingPostcode: "",
          mailingAddress: "",
          phoneNumber: "",
          email: "",
          faxNumber: "",
          addressLongitude: 101.707021,
          addressLatitude: 2.745564,
          mailingAddressLongitude: 101.707021,
          mailingAddressLatitude: 2.745564,
          createdBy: "user1",
          sameAddress: false,
        } as unknown as ReqBody);
  const isCreated = initialValue.id !== null;
  const handleSubmit = async (
    initialValue: ReqBody,
    { setSubmitting }: FormikHelpers<ReqBody>
  ) => {
    const value = {
      ...initialValue,
      phoneNumber: initialValue.phoneNumber.startsWith("+60")
        ? initialValue.phoneNumber
        : `+60${initialValue.phoneNumber}`,
    } as ReqBody;
    setSubmitting(true);
    try {
      if (societyId) {
        const { createdDate, modifiedDate, registeredDate, ...updateValue } =
          value;
        await updateSocietyRegistration(updateValue as UpdateReqBody);
      } else {
        await registerSociety(value);
      }
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    if (encodedId) {
      fetchSocietyData();
    }
  }, []);

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box sx={{ width: "55vw" }}>
        <LoadingOverlay isLoading={isLoadingSocietyData} />
        <Formik<ReqBody>
          initialValues={initialValue}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          <FormGeneralInformationSocietyRegistrationInner
            initialValue={initialValue}
          />
        </Formik>
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <OrganizationStepper
          isMaklumatCreated={isCreated}
          activeStep={activeStep}
          hidePayment={
            societyDataRedux?.applicationStatusCode == ApplicationStatus.KUIRI
          }
        />
        {societyDataRedux?.applicationStatusCode == ApplicationStatus.KUIRI ? (
          <Box
            sx={{
              padding: 3,
              backgroundColor: "white",
              borderRadius: "15px",
              maxHeight: "60vh",
              maxWidth: "18vw",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("kuiri")}
            </Typography>
            <Box
              sx={{
                padding: 3,
                backgroundColor: "#DADADA",
                borderRadius: "15px",
                maxHeight: "30vh",
                maxWidth: "18vw",
                overflowY: "scroll",
              }}
            >
              <Typography
                sx={{
                  mb: 8,
                  fontSize: "12px",
                  color: "#666666",
                  fontWeight: "500 !important",
                  wordBreak: "break-word",
                }}
              >
                {societyDataRedux?.queryText}
              </Typography>
            </Box>
          </Box>
        ) : null}
        <InfoQACard />
      </Box>
    </Box>
  );
};

export default CreateMam;
