import {
  Box,
  Chip,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  FormHelperText,
  Grid,
  IconButton,
  MenuItem,
  Radio,
  RadioGroup,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useCallback, useEffect, useRef, useState } from "react";
import Input from "../../../../../components/input/Input";
import { RootState } from "../../../../../redux/store";
import { useSelector } from "react-redux";
import { LoadingOverlay } from "../../../../../components/loading";
import { useNavigate, useSearchParams } from "react-router-dom";
import { API_URL } from "../../../../../api";
import { useCustomMutation } from "@refinedev/core";
import dayjs from "@/helpers/dayjs";
import {
  useFormatDateLocalization,
  pageAccessEnum,
  useQuery,
  aliranAccessList,
} from "@/helpers";
import { DialogConfirmation, Label, Switch } from "@/components";
import { EditIcon } from "@/components/icons";
import { Close } from "@mui/icons-material";
export interface PermissionList {
  id: string | number;
  pid: string | number;
  level: number;
  name: string;
  code?: string | undefined;
  description: string;
  oldRoleId: string | number | null;
  userRole: null;
}

export type PermissionListsResponseBodyGet<
  BaseType extends PermissionList = PermissionList
> = BaseType & {
  children: BaseType[];
};

interface FormValues {
  role: string | null;
  permissions: Permission[] | any;
  oldPermissions?: any;
  description: string | null;
  status: number | null;
  modifiedDate: number[] | null;
  createdByName: string | null;
}

interface Permission {
  id: string | number;
  pid: string | number;
  level: number;
  name: string;
  code: string;
  description: string;
  oldRoleId: number | null;
  userRole: string | null;
  isPermissionLevel: boolean;
  children: Permission[];
}

type EditTarget = { name: string; code: string } | null;

function CreateRoleJPM<
  // PageAccessType extends PageAccessByUserRoleResponseBodyGet = PageAccessByUserRoleResponseBodyGet,
  PermissionListType extends PermissionListsResponseBodyGet = PermissionListsResponseBodyGet
>() {
  const { t } = useTranslation();
  const initialFormValues: FormValues = {
    role: null,
    permissions: [],
    oldPermissions: null,
    description: null,
    status: null,
    modifiedDate: null,
    createdByName: null,
  };
  const [formValues, setFormValues] = useState<FormValues>(initialFormValues);
  const [updateFormValues, setUpdateFormValues] = useState<FormValues | null>(
    null
  );
  const { formatDate } = useFormatDateLocalization();
  const [backupSavedRoles, setBackupSavedRoles] = useState<Permission[]>([]);
  const [errors, setErrors] = useState<{ [key in keyof FormValues]?: string }>(
    {}
  );

  const {
    mutate: updatePermissionForRole,
    isLoading: isUpdatingPermissionForRole,
  } = useCustomMutation();

  const EditRoles = async () => {
    const values = {
      id: roleId ?? null,
      role: formValues?.role?.trim(),
      status: Number(formValues.status),
      description: formValues.description,
    };
    const isUpdate = values?.id !== null;
    await updatePermissionForRole(
      {
        url: roleId
          ? `${API_URL}/user/userRole/updateUserRole`
          : `${API_URL}/user/userRole/addUserRole`,
        method: roleId ? "put" : "post",
        values,
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status === "ERROR") {
            return {
              message:
                data?.data?.msg === "Ralat. Kategori Pengguna ini telah wujud."
                  ? t("roleNameAlreadyInUse")
                  : t(data?.data?.msg),
              type: "error",
            };
          } else {
            setErrors({});

            navigate(-1);
            setActionSuccess(true);
            const message = t(
              `jppmUserCategory${isUpdate ? "Updated" : "Created"}Successfully`,
              {
                name: values.role?.trim(),
              }
            );
            return {
              message,
              type: "success",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const currDate = new Date();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const roleToEdit = searchParams.get("role");
  const roleId = searchParams.get("id");
  const [savedRoles, setSavedRoles] = useState<Permission[]>([]);
  const getUserDetails = localStorage.getItem("user-details");
  const userName = getUserDetails ? JSON.parse(getUserDetails).name : "";
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);

  const [actionSuccess, setActionSuccess] = useState(false);
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const permissionsList = useSelector<RootState, PermissionListType[]>(
    (state) => state.permissionsListData.data
  );
  const isLoading = useSelector(
    (state: RootState) => state.permissionsListData.loading
  );

  const [selectedCodes, setSelectedCodes] = useState<any[]>([]);
  const [selectedPerm, setSelectedPerm] = useState<EditTarget>(null);
  const [open, setOpen] = useState(false);

  const pageAccessOptions = Object.entries(pageAccessEnum)
    .filter(([key]) => key !== "All")
    .map(([key, value]) => ({
      label: t(key),
      value,
    }));

  const aliranAccessListOptions = Object.entries(aliranAccessList).map(
    ([key, value]) => ({
      label: t(key),
      value,
    })
  );

  const {
    data: currentPermissions,
    isLoading: isLoadingCurrentPermissions,
    refetch: fetchCurrPermissions,
  } = useQuery({
    url: `user/userRole/getByRole`,
    filters: [
      {
        field: "role",
        operator: "eq",
        value: roleToEdit,
      },
    ],
    autoFetch: roleToEdit ? true : false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      const returnedData = data?.data?.data;
      setFormValues((prev) => ({
        ...prev,
        ...returnedData,
      }));
    },
  });

  const {
    data: newRolePermissions,
    isLoading: isLoadingNewRolePermissions,
    refetch: fetchNewRolePermissions,
  } = useQuery({
    url: `user/pageManagement/getPageCodesByRole`,
    filters: [
      {
        field: "role",
        operator: "eq",
        value: roleToEdit,
      },
    ],
    autoFetch: roleToEdit ? true : false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      const codes = data?.data?.data;
      setSelectedCodes(codes);
    },
  });

  const handleToggle = (code?: string) => {
    const hasMatch = selectedCodes?.find((c) => c === code);
    if (hasMatch) {
      updatePermissionForRole(
        {
          url: `${API_URL}/user/pageManagement/updateRolePermissions`,
          method: "put",
          values: {
            //  gainAccess: code,
            revokeAccess: code,
            role: formValues?.role,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            if (data?.data?.status === "SUCCESS") {
              // navigate(-1);
              return {
                message: data?.data?.msg,
                type: "success",
              };
            } else {
              return {
                message: data?.data?.msg,
                type: "error",
              };
            }
          },
          errorNotification: (data) => {
            return {
              message: data?.response?.data?.msg,
              type: "error",
            };
          },
        },
        {
          onError(error, variables, context) {
            console.log(error);
          },
          onSuccess: () => {
            fetchNewRolePermissions();
          },
        }
      );
    } else {
      updatePermissionForRole(
        {
          url: `${API_URL}/user/pageManagement/updateRolePermissions`,
          method: "put",
          values: {
            gainAccess: code,
            role: formValues?.role,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            if (data?.data?.status === "SUCCESS") {
              // navigate(-1);
              return {
                message: data?.data?.msg,
                type: "success",
              };
            } else {
              return {
                message: data?.data?.msg,
                type: "error",
              };
            }
          },
          errorNotification: (data) => {
            return {
              message: data?.response?.data?.msg,
              type: "error",
            };
          },
        },
        {
          onError(error, variables, context) {
            console.log(error);
          },
          onSuccess: () => {
            fetchNewRolePermissions();
          },
        }
      );
    }
  };

  const handlePermissionToggle = (
    baseCode: string,
    value: number,
    checked: boolean
  ) => {
    const exists = selectedCodes.some((c) => c === `${baseCode}:${value}`);

    if (exists) {
      updatePermissionForRole(
        {
          url: `${API_URL}/user/pageManagement/updateRolePermissions`,
          method: "put",
          values: {
            revokeAccess: `${baseCode}:${value}`,
            role: formValues?.role,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            if (data?.data?.status === "SUCCESS") {
              // navigate(-1);
              return {
                message: data?.data?.msg,
                type: "success",
              };
            } else {
              return {
                message: data?.data?.msg,
                type: "error",
              };
            }
          },
          errorNotification: (data) => {
            return {
              message: data?.response?.data?.msg,
              type: "error",
            };
          },
        },
        {
          onError(error, variables, context) {
            console.log(error);
          },
          onSuccess: () => {
            fetchNewRolePermissions();
          },
        }
      );
    } else {
      updatePermissionForRole(
        {
          url: `${API_URL}/user/pageManagement/updateRolePermissions`,
          method: "put",
          values: {
            gainAccess: `${baseCode}:${value}`,
            role: formValues?.role,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            if (data?.data?.status === "SUCCESS") {
              // navigate(-1);
              return {
                message: data?.data?.msg,
                type: "success",
              };
            } else {
              return {
                message: data?.data?.msg,
                type: "error",
              };
            }
          },
          errorNotification: (data) => {
            return {
              message: data?.response?.data?.msg,
              type: "error",
            };
          },
        },
        {
          onError(error, variables, context) {
            console.log(error);
          },
          onSuccess: () => {
            fetchNewRolePermissions();
          },
        }
      );
    }
  };

  const renderPermission = (perm: Permission, level = 0) => {
    const isChecked = selectedCodes.some((c) => c === perm?.code);

    return (
      <Box key={perm.id} sx={{ ml: level, mb: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 1,
          }}
        >
          <Typography
            style={
              level === 0
                ? { color: "var(--primary-color)" }
                : { color: "var(--text-grey)" }
            }
          >
            {perm.name}
          </Typography>

          {perm.isPermissionLevel && perm.children.length === 0 ? (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                width: 20,
              }}
            >
              <IconButton
                onClick={() =>
                  handleEditClick({ name: perm.name, code: perm.code })
                }
              >
                <EditIcon color="var(--primary-color)" />
              </IconButton>
            </Box>
          ) : (
            <Switch
              checked={isChecked}
              onChange={() => handleToggle(perm.code)}
            />
          )}
        </Box>

        {perm.children?.length > 0 &&
          perm.children.map((child) => renderPermission(child, level + 1))}
      </Box>
    );
  };

  const handleEditClick = (perm: { name: string; code: string }) => {
    setSelectedPerm(perm);
    setOpen(true);
  };

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setErrors({});
    setFormValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  }, []);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    const newErrors: { [key in keyof FormValues]?: string } = {};

    if (!formValues.role) {
      newErrors.role = t("fieldRequired");
    }
    if (!formValues.description) {
      newErrors.description = t("fieldRequired");
    }
    if (!formValues.status) {
      newErrors.status = t("fieldRequired");
    }

    if (Object.keys(newErrors).length === 0) {
      setOpenConfirmDialog(true);
      setErrors({});
    }
    setErrors(newErrors);
  };

  const handleConfirm = () => {
    EditRoles();
  };

  const resetForm = () => {
    if (roleToEdit) {
      setFormValues({
        ...updateFormValues!,
        permissions: backupSavedRoles,
      });
      setSavedRoles(backupSavedRoles);
    } else {
      setFormValues(initialFormValues);
      setSavedRoles([]);
    }
  };

  useEffect(() => {
    if (savedRoles.length > 0) {
      setFormValues((prev) => ({
        ...prev,
        permissions: savedRoles,
      }));
    }
  }, [savedRoles]);

  const statusRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (errors.status && statusRef.current) {
      statusRef.current.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  }, [errors.status]);

  return (
    <Box
      sx={{ display: "grid", gap: 2 }}
      component="form"
      onSubmit={handleSubmit}
    >
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography className={"title"}>
            {t("addCategoryPenggunaJPPM")}
          </Typography>
          <Box
            sx={{
              mt: 3,
            }}
          >
            <Input
              required
              disabled={!!roleId}
              name="role"
              label={t("kategoriPengguna")}
              value={formValues?.role ? formValues.role : ""}
              onChange={handleChange}
              error={!!errors.role}
              helperText={errors.role}
            />
            <Input
              required
              name="description"
              label={t("descriptionCategory")}
              value={formValues?.description ? formValues.description : ""}
              onChange={handleChange}
              error={!!errors.description}
              helperText={errors.description}
            />
            <Grid container spacing={2} sx={{ pt: 2, pb: 2 }}>
              <Grid
                item
                xs={12}
                sm={4}
                sx={{ display: "flex", alignItems: "center" }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    color: "#666666",
                    fontWeight: "400 !important",
                    fontSize: "14px",
                  }}
                >
                  {t("status")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8} ref={statusRef}>
                <RadioGroup
                  value={formValues.status}
                  onChange={handleChange}
                  name="status"
                  row
                >
                  <FormControlLabel
                    value={1}
                    className="label"
                    control={<Radio />}
                    label={t("active")}
                  />
                  <FormControlLabel
                    value={0}
                    className="label"
                    control={<Radio />}
                    label={t("inactive")}
                  />
                </RadioGroup>
                {errors.status ? (
                  <FormHelperText error>{errors.status}</FormHelperText>
                ) : null}
              </Grid>
            </Grid>
            <Input
              value={
                formValues?.createdByName
                  ? formValues?.createdByName
                  : roleId
                  ? ""
                  : userName
              }
              name="createdByName"
              label={t("recordedBy")}
              onChange={handleChange}
              disabled
              error={!!errors.createdByName}
              helperText={errors.createdByName}
            />
            <Input
              value={
                formValues?.modifiedDate
                  ? formatDate(
                      dayjs(
                        formValues.modifiedDate as unknown as string,
                        "YYYY-MM-DD hh:mm:ss"
                      ),
                      t("dateFormatWithPrefix", {
                        prefix: t("datePrefixOn"),
                        dateFormat: "dddd, MMM D YYYY, h:mm:ss A",
                      })
                    )
                  : roleId
                  ? ""
                  : formatDate(
                      currDate,
                      t("dateFormatWithPrefix", {
                        prefix: t("datePrefixOn"),
                        dateFormat: "dddd, MMM D YYYY, h:mm:ss A",
                      })
                    )
              }
              name="modifiedDate"
              type="text"
              label={t("lastUpdate")}
              onChange={handleChange}
              disabled
              error={!!errors.modifiedDate}
              helperText={errors.modifiedDate}
            />
          </Box>
        </Box>
        <Grid
          item
          xs={12}
          sx={{
            mt: 2,
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            justifyContent: "flex-end",
            gap: 1,
          }}
        >
          <ButtonOutline variant="outlined" onClick={resetForm}>
            {t("previous")}
          </ButtonOutline>
          <ButtonPrimary
            type="submit"
            variant="contained"
            sx={{
              width: isMobile ? "100%" : "auto",
            }}
            disabled={
              isUpdatingPermissionForRole ||
              isLoading ||
              permissionsList?.length === 0
                ? true
                : false
            }
          >
            {isUpdatingPermissionForRole ? (
              <CircularProgress size={15} />
            ) : (
              t("hantar")
            )}
          </ButtonPrimary>
        </Grid>
      </Box>
      {roleId && (
        <Box
          sx={{
            p: 2,
            backgroundColor: "white",
            borderRadius: "14px",
          }}
        >
          <Box
            sx={{
              borderRadius: "14px",
              display: "grid",
              gap: 1,
              mb: 2,
            }}
          >
            {isLoading || permissionsList?.length === 0 ? (
              <LoadingOverlay />
            ) : (
              permissionsList?.map((item: any, index: number) => (
                <Box
                  sx={{
                    display: "grid",
                    py: 1,
                    px: 2,
                    borderRadius: "10px",
                  }}
                  key={index}
                >
                  {index === 0 && (
                    <>
                      <Typography sx={{ mb: 6 }} className={"title"}>
                        {t("ketetapanKebenaranAkses")}
                      </Typography>
                    </>
                  )}

                  {renderPermission(item)}
                </Box>
              ))
            )}
          </Box>
        </Box>
      )}

      <DialogConfirmation
        isMutating={isUpdatingPermissionForRole}
        open={openConfirmDialog}
        onClose={() => setOpenConfirmDialog(false)}
        onAction={handleConfirm}
        onConfirmationText={
          roleId ? t("roleConfirmation") : t("roleConfirmationCreate")
        }
        isSuccess={actionSuccess}
      />

      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        PaperProps={{
          style: {
            borderRadius: "15px",
            backgroundColor: "#fff",
            color: "#000",
            minWidth: "50%",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <Box sx={{ m: 2, border: "1px solid #D9D9D9", borderRadius: "12px" }}>
          <DialogTitle sx={{ pb: 2.5 }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography className="title">
                {t("listViewSettings")}: {selectedPerm?.name}
              </Typography>
              <IconButton onClick={() => setOpen(false)} size="small">
                <Close sx={{ color: "black" }} />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent>
            {/* <Typography>Code: {selectedPerm?.code}</Typography> */}

            <Box sx={{ display: "grid", gap: 2 }}>
              <Box>
                <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      flex: "0 0 34%",
                      mb: 1,
                    }}
                  >
                    <Label text={t("state")} required />
                  </Box>

                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      flex: "0 0 66%",
                      mb: 1,
                      gap: 2.8,
                    }}
                  >
                    <Box
                      key={"Negari Pengguna"}
                      sx={{ display: "flex", alignItems: "center", gap: 1 }}
                    >
                      <input
                        type="checkbox"
                        name={`NegariPengguna`}
                        className="checkboxStyled"
                        // checked={isChecked}
                        disabled
                        // onChange={() => {
                        //   handleToggle(selectedPerm?.code);
                        // }}
                      />
                      <Label text={"Negari Pengguna"} />
                    </Box>
                    <Box
                      key={"Semua Negeri"}
                      sx={{ display: "flex", alignItems: "center", gap: 1 }}
                    >
                      <input
                        type="checkbox"
                        name={`Semua Negeri`}
                        className="checkboxStyled"
                        // checked={isChecked}
                        disabled
                        // onChange={() => {
                        //   handleToggle(selectedPerm?.code);
                        // }}
                      />
                      <Label text={"Semua Negeri"} />
                    </Box>
                    <Box
                      key={"Custom"}
                      sx={{ display: "flex", alignItems: "center", gap: 1 }}
                    >
                      <input
                        type="checkbox"
                        name={`Custom`}
                        className="checkboxStyled"
                        // checked={isChecked}
                        disabled
                        // onChange={() => {
                        //   handleToggle(selectedPerm?.code);
                        // }}
                      />
                      <Label text={"Custom"} />
                    </Box>
                  </Box>
                </Box>
              </Box>
              <Box>
                <Input disabled multiline isLabel={false} rows={3} />
              </Box>
              <Box>
                <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      flex: "0 0 34%",
                      mb: 1,
                    }}
                  >
                    <Label text={t("category")} required />
                  </Box>

                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      flex: "0 0 66%",
                      mb: 1,
                      gap: 2.8,
                    }}
                  >
                    <Box
                      key={"Semua Kategori"}
                      sx={{ display: "flex", alignItems: "center", gap: 1 }}
                    >
                      <input
                        type="checkbox"
                        name={`Semua Kategori`}
                        className="checkboxStyled"
                        // checked={isChecked}
                        disabled
                        // onChange={() => {
                        //   handleToggle(selectedPerm?.code);
                        // }}
                      />
                      <Label text={"Semua Kategori"} />
                    </Box>
                    <Box
                      key={"Custom"}
                      sx={{ display: "flex", alignItems: "center", gap: 1 }}
                    >
                      <input
                        type="checkbox"
                        name={`Custom`}
                        className="checkboxStyled"
                        // checked={isChecked}
                        disabled
                        // onChange={() => {
                        //   handleToggle(selectedPerm?.code);
                        // }}
                      />
                      <Label text={"Custom"} />
                    </Box>
                  </Box>
                </Box>
              </Box>
              <Box>
                <Input disabled multiline label={t("category")} rows={3} />
              </Box>
              <Box>
                <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      flex: "0 0 34%",
                      mb: 1,
                    }}
                  >
                    <Label text={t("flow")} required />
                  </Box>

                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      flex: "0 0 66%",
                      mb: 1,
                      gap: 2.8,
                    }}
                  >
                    {aliranAccessListOptions.map((opt) => {
                      const existing = selectedCodes.find((c) =>
                        c.startsWith(selectedPerm?.code ?? "")
                      );

                      const targetCode = existing
                        ? [
                            selectedPerm?.code,
                            opt.value,
                            ...existing.split(":").slice(2),
                          ].join(":")
                        : null;

                      const isChecked = existing
                        ? selectedCodes.includes(targetCode!)
                        : opt.value === 0; // default to "0" if no existing code

                      return (
                        <Box
                          key={opt.value + "aliran"}
                          sx={{ display: "flex", alignItems: "center", gap: 1 }}
                        >
                          <input
                            type="checkbox"
                            name={`${selectedPerm?.code}-${opt.label} aliran`}
                            className="checkboxStyled"
                            checked={isChecked}
                            disabled
                            onChange={() => {
                              handleToggle(selectedPerm?.code);
                            }}
                          />
                          <Label text={opt.label} />
                        </Box>
                      );
                    })}
                  </Box>
                </Box>
              </Box>

              <Box>
                <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      flex: "0 0 34%",
                      mb: 1,
                    }}
                  >
                    <Label text={"CRUD"} required />
                  </Box>

                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      flex: "0 0 66%",
                      mb: 1,
                      gap: 3,
                    }}
                  >
                    {pageAccessOptions.map((opt) => {
                      const existing = selectedCodes.find((c) =>
                        c.startsWith(selectedPerm?.code ?? "")
                      );

                      const isAll = existing?.split(":")[1] === "15";

                      const isChecked = existing
                        ? selectedCodes.includes(
                            [
                              selectedPerm?.code,
                              opt.value,
                              ...existing.split(":").slice(2),
                            ].join(":")
                          )
                        : false;

                      return (
                        <Box
                          key={opt.value}
                          sx={{ display: "flex", alignItems: "center", gap: 1 }}
                        >
                          <input
                            type="checkbox"
                            name={`${selectedPerm?.code}-${opt.label}`}
                            className="checkboxStyled"
                            checked={isChecked || isAll}
                            // disabled={isAll}
                            onChange={(e) =>
                              handlePermissionToggle(
                                selectedPerm?.code ?? "-",
                                opt.value,
                                e.target.checked
                              )
                            }
                          />
                          <Label text={opt.label} />
                        </Box>
                      );
                    })}
                  </Box>
                </Box>
              </Box>
            </Box>
          </DialogContent>
        </Box>
      </Dialog>
    </Box>
  );
}

export default CreateRoleJPM;
