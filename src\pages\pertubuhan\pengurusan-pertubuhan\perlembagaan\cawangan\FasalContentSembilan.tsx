import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { handleSaveValue } from "../helper/handleSaveValue";
import { handleSaveContent } from "../helper/handleSaveContent";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../../api";
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import { months, RegExNumbers, RegExText } from "../../../../../helpers/enums";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import { formatAndValidateNumber } from "@/helpers";
import CustomPopover from "@/components/popover";
interface FasalContentSembilanCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentSembilanCawangan: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [jumlahWangTangan, setJumlahWangTangan] = useState("");
  const [jumlahWangTanganKata, setJumlahWangTanganKata] = useState("");
  const [kuasaPerbelanjaan, setKuasaPerbelanjaan] = useState("");
  const [kuasaPerbelanjaanKata, setKuasaPerbelanjaanKata] = useState("");
  const [kuasaPerbelanjaanJawatankuasa, setKuasaPerbelanjaanJawatankuasa] =
    useState("");
  const [
    kuasaPerbelanjaanJawatankuasaKata,
    setKuasaPerbelanjaanJawatankuasaKata,
  ] = useState("");
  const [perbelanjaanDibenarkan, setPerbelanjaanDibenarkan] = useState("");
  const [perbelanjaanDibenarkanKata, setPerbelanjaanDibenarkanKata] =
    useState("");
  const [tempohDibenarkan, setTempohDibenarkan] = useState("");
  const [tahunKewanganBermula, setTahunKewanganBermula] = useState("1 Januari");

  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  //const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);

  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");

  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!jumlahWangTangan) {
      errors.jumlahWangTangan = t("fieldRequired");
    }

    if (!jumlahWangTanganKata) {
      errors.jumlahWangTanganKata = t("fieldRequired");
    }

    if (!kuasaPerbelanjaan) {
      errors.kuasaPerbelanjaan = t("fieldRequired");
    }

    if (!kuasaPerbelanjaanKata) {
      errors.kuasaPerbelanjaanKata = t("fieldRequired");
    }

    if (!kuasaPerbelanjaanJawatankuasa) {
      errors.kuasaPerbelanjaanJawatankuasa = t("fieldRequired");
    }

    if (!kuasaPerbelanjaanJawatankuasaKata) {
      errors.kuasaPerbelanjaanJawatankuasaKata = t("fieldRequired");
    }

    if (!perbelanjaanDibenarkan) {
      errors.perbelanjaanDibenarkan = t("fieldRequired");
    }

    if (!perbelanjaanDibenarkanKata) {
      errors.perbelanjaanDibenarkanKata = t("fieldRequired");
    }

    if (!tempohDibenarkan) {
      errors.tempohDibenarkan = t("fieldRequired");
    }

    if (!tahunKewanganBermula) {
      errors.tahunKewanganBermula = t("fieldRequired");
    }

    return errors;
  };

  /*const clauseContent = `
1. Tertakluk kepada peruntukan-peruntukan dalam Perlembagaan ini, wang Pertubuhan ini boleh digunakan untuk perkara-perkara yang berfaedah bagi menjalankan tujuan-tujuan Pertubuhan, termasuklah belanja pentadbiran, bayaran gaji, biayaan dan perbelanjaan Ahli Jawatankuasa, Juruaudit dan kakitangan yang bergaji serta upah Juruaudit luar dan bertauliah.

2. Bendahari dibenarkan menyimpan wang tunai dalam tangan tidak lebih daripada RM ${
    jumlahWangTangan || "<<Jumlah Wang Yang Dibenarkan Dalam Tangan>>"
  } (Ringgit Malaysia ${
    jumlahWangTanganKata || "<<Jumlah Wang Dibenarkan-tulis Dalam Perkataan>>"
  } Sahaja) pada satu-satu masa. Wang yang lebih daripada jumlah itu hendaklah dimasukkan ke akaun bank yang diluluskan oleh Jawatankuasa dalam tempoh ${
    tempohDibenarkan ||
    "<<Tempoh Yang Dibenarkan Untuk Masukkan Wang ke Dalam Bank>>"
  } hari. Akaun bank tersebut hendaklah dibuka atas nama Pertubuhan.

3. Segala cek atau penyata pengeluaran wang daripada akaun Pertubuhan hendaklah ditandatangani oleh Pengerusi, Setiausaha dan Bendahari. Walau bagaimanapun, Jawatankuasa melalui mesyuarat berhak melantik sesiapa di antara mereka sebagai pengganti untuk menandatangani cek atau pengeluaran wang Pertubuhan semasa ketiadaan mana-mana penandatangan tersebut.

4. Perbelanjaan yang lebih daripada RM ${
    kuasaPerbelanjaanJawatankuasa || "<<Kuasa Perbelanjaan Jawatankuasa>>"
  } (Ringgit Malaysia ${
    kuasaPerbelanjaanJawatankuasaKata ||
    "<<Kuasa Perbelanjaan Jawatankuasa Tulis Dalam Perkataan>>"
  } Sahaja) pada satu-satu masa tidak boleh dilakukan tanpa kelulusan Mesyuarat Jawatankuasa. Perbelanjaan yang lebih daripada RM ${
    kuasaPerbelanjaan || "<<Kuasa Perbelanjaan Mesyuarat Agung>>"
  } (Ringgit Malaysia ${
    kuasaPerbelanjaanKata ||
    "<<Kuasa Perbelanjaan Mesyuarat Agung Tulis Dalam Perkataan>>"
  } Sahaja) bagi satu-satu masa tidak boleh dilakukan tanpa kelulusan Mesyuarat Agung. Perbelanjaan yang kurang daripada RM ${
    perbelanjaanDibenarkan || "<<Perbelanjaan yang Dibenarkan>>"
  } (Ringgit Malaysia ${
    perbelanjaanDibenarkanKata ||
    "<<Perbelanjaan yang Dibenarkan Tulis Dalam Perkataan>>"
  } Sahaja) boleh diluluskan oleh Pengerusi, Setiausaha dan Bendahari.

5. Tahun kewangan Pertubuhan ini hendaklah bagi tempoh 12 bulan iaitu bermula dari ${
    tahunKewanganBermula || "<<Tahun Kewangan Bermula>>"
  }. Setiausaha adalah bertanggungjawab untuk mengemukakan dokumen dalam masa 60 hari selepas Mesyuarat Agung kepada Pendaftar Pertubuhan apabila disahkan dan/atau berakhirnya setiap tahun kalendar sekiranya tiada Mesyuarat Agung.

6. Jawatankuasa yang melebihi tempoh pelantikan jawatannya tidak mempunyai kuasa untuk membuat sebarang pengeluaran wang Pertubuhan.
`;*/

  //const clause9 = localStorage.getItem("clause9");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause = JSON.parse(clause9);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause.societyName);
      if(clause.constitutionValues.length > 0){ 
      const fieldMappings: Record<string, (value: string) => void> = {
        "Jumlah Wang Tangan yang Dibenarkan Dalam Tangan": setJumlahWangTangan,
        "Jumlah Wang Tangan yang Dibenarkan Dalam Tangan (Tulis Dalam Perkataan)": setJumlahWangTanganKata,
        "Kuasa Perbelanjaan Mesyuarat Agung": setKuasaPerbelanjaan,
        "Kuasa Perbelanjaan Mesyuarat Agung (Tulis Dalam Perkataan)": setKuasaPerbelanjaanKata,
        "Kuasa Perbelanjaan Jawatankuasa": setKuasaPerbelanjaanJawatankuasa,
        "Kuasa Perbelanjaan Jawatankuasa (Tulis Dalam Perkataan)": setKuasaPerbelanjaanJawatankuasaKata,
        "Perbelanjaan yang Dibenarkan": setPerbelanjaanDibenarkan,
        "Perbelanjaan yang Dibenarkan (Tulis Dalam Perkataan)": setPerbelanjaanDibenarkanKata,
        "Tempoh yang Dibenarkan Untuk Memasukkan Wang Lebihan ke Dalam Bank": setTempohDibenarkan,
        "Tahun Kewangan Bermula (Tahun Kewangan Bermula Dari Tahun Kewangan Yang Dibenarkan)": (value: string) => {
          if (value) {
            setTahunKewanganBermula(value);
          } else {
            setTahunKewanganBermula("1 Januari");
          }},
     
      };

      Object.values(fieldMappings).forEach(setter => setter(''));
      
      if(clause.constitutionValues){
        clause.constitutionValues.forEach((item:any) => {
          const setter = fieldMappings[item.titleName];
          if (setter && item.definitionName) {
            setter(item.definitionName);
          }
        });
      }
 
      setIsEdit(clause.edit);
    }
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const inputStyle = {
    height:"100%",
    width:"100%",
    display:"flex",
    justifyContent:"center",
    alignItems:"center",
    fontSize: "14px",
    color: "#666666",
    fontWeight:"400!important"
  }

  const { id } = useParams();

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<jumlah wang tangan yang dibenarkan dalam tangan>>/gi,
    `<b>${
      jumlahWangTangan || "<<jumlah wang tangan yang dibenarkan dalam tangan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jumlah wang dibenarkan-tulis dalam perkataan>>/gi,
    `<b>${
      jumlahWangTanganKata || "<<jumlah wang dibenarkan-tulis dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<perbelanjaan yg dibenarkan>>/gi,
    `<b>${perbelanjaanDibenarkan || "<<perbelanjaan yg dibenarkan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<perbelanjaan yg dibenarkan-tulis dalam perkataan>>/gi,
    `<b>${
      perbelanjaanDibenarkanKata ||
      "<<perbelanjaan yg dibenarkan-tulis dalam perkataan>>"
    }</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${tempohPelucutanWaktu || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan mesyuarat agung>>/gi,
    `<b>${kuasaPerbelanjaan || "<<kuasa perbelanjaan mesyuarat agung>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahPengerusi || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan mesyuarat agung-tulis dalam perkataan>>/gi,
    `<b>${
      kuasaPerbelanjaanKata ||
      "<<kuasa perbelanjaan mesyuarat agung-tulis dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan jawatankuasa>>/gi,
    `<b>${
      kuasaPerbelanjaanJawatankuasa || "<<kuasa perbelanjaan jawatankuasa>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan jawatankuasa-tulis dalam perkataan>>/gi,
    `<b>${
      kuasaPerbelanjaanJawatankuasaKata ||
      "<<kuasa perbelanjaan jawatankuasa-tulis dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh yang dibenarkan utk masukkan wang ke dalam bank>>/gi,
    `<b>${
      tempohDibenarkan ||
      "<<tempoh yang dibenarkan utk masukkan wang ke dalam bank>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tahun kewangan bermula>>/gi,
    `<b>${tahunKewanganBermula || "<<tahun kewangan bermula>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("permulaanTahunKewangan")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography sx={labelStyle}>
                {t("tahunKewanganBermula")}{" "}
                <Typography sx={{ display: "inline", color: "red" }}>
                  *
                </Typography>
              </Typography>
              <CustomPopover
                customStyles={{ maxWidth: "250px" }}
                content={
                  <Typography sx={{ color: "#666666" }}>
                    {t("contohJan")}
                  </Typography>
                }
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required>
              <Select
                required
                size="small"
                value={tahunKewanganBermula}
                disabled
                displayEmpty
                onChange={(e) => {
                  setTahunKewanganBermula(e.target.value);
                }}
              >
                {months.map((i) => {
                  return (
                    <MenuItem value={i.value} selected>
                      {i.label}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("financialMangement")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("hadMaksimumWangDalamTangan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="RM 0.00"
              value={jumlahWangTangan}
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setJumlahWangTangan(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahWangTangan: "",
                  }));
                } else {
                  setJumlahWangTangan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahWangTangan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.jumlahWangTangan}
              helperText={formErrors.jumlahWangTangan}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh "
              value={jumlahWangTanganKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setJumlahWangTanganKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahWangTanganKata: "",
                  }));
                } else {
                  setJumlahWangTanganKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahWangTanganKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.jumlahWangTanganKata}
              helperText={formErrors.jumlahWangTanganKata}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          Tempoh Masukkan Wang Lebih Ke Bank
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("tempohDibenarkan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              value={tempohDibenarkan}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setTempohDibenarkan(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    tempohDibenarkan: "",
                  }));
                } else {
                  setTempohDibenarkan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    tempohDibenarkan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.tempohDibenarkan}
              helperText={formErrors.tempohDibenarkan}
              type="number"
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("kuasaPerbelanjaanCawangan")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("kuasaPerbelanjaan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
            <Grid item xs={8} sx={{display:"flex" }} gap={1}>  
              <Grid item xs={12} md={2}>
                <Box sx={inputStyle}>
                  {t("morethan")}
                </Box>
              </Grid>
            <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="RM 0.00"
              value={kuasaPerbelanjaan}
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setKuasaPerbelanjaan(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaan: "",
                  }));
                } else {
                  setKuasaPerbelanjaan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaan}
              helperText={formErrors.kuasaPerbelanjaan}
            />
          </Grid>
        </Grid> 
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh "
              value={kuasaPerbelanjaanKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setKuasaPerbelanjaanKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanKata: "",
                  }));
                } else {
                  setKuasaPerbelanjaanKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaanKata}
              helperText={formErrors.kuasaPerbelanjaanKata}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("kuasaPerbelanjaanJawatankuasa")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("kuasaPerbelanjaanJawatankuasa")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid> 
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={kuasaPerbelanjaanJawatankuasa}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setKuasaPerbelanjaanJawatankuasa(formattedValue);
                  setPerbelanjaanDibenarkan(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasa: "",
                  }));
                } else {
                  setKuasaPerbelanjaanJawatankuasa("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasa: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaanJawatankuasa}
              helperText={formErrors.kuasaPerbelanjaanJawatankuasa}
            />
          </Grid> 

          <Grid item xs={12} md={2}>
            <Typography sx={inputStyle}>
              sehingga 
            </Typography>
          </Grid>

          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={kuasaPerbelanjaan} 
              disabled
              placeholder="RM 0.00" 
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              placeholder="contoh : dua puluh "
              multiline
              rows={3}
              required
              value={kuasaPerbelanjaanJawatankuasaKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setKuasaPerbelanjaanJawatankuasaKata(e.target.value);
                  setPerbelanjaanDibenarkanKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasaKata: "",
                  }));
                } else {
                  setKuasaPerbelanjaanJawatankuasaKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasaKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaanJawatankuasaKata}
              helperText={formErrors.kuasaPerbelanjaanJawatankuasaKata}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <Typography sx={inputStyle}>
              sehingga 
            </Typography>
          </Grid>

          <Grid item xs={12} md={2}>
              <TextField
              size="small"
              disabled
              multiline
              rows={3}
              fullWidth
              placeholder="contoh : dua puluh "
              required
              value={kuasaPerbelanjaanKata} 
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("perbelanjaanDibenarkan")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("perbelanjaanDibenarkan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
         <Grid item xs={8} sx={{display:"flex"}} gap={1}>  
          <Grid item xs={12} md={2}>
            <Box sx={inputStyle}>
              {t("lessthan")}
            </Box>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled
              required
              placeholder="RM 0.00"
              value={perbelanjaanDibenarkan}
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setPerbelanjaanDibenarkan(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    perbelanjaanDibenarkan: "",
                  }));
                } else {
                  setPerbelanjaanDibenarkan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    perbelanjaanDibenarkan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.perbelanjaanDibenarkan}
              helperText={formErrors.perbelanjaanDibenarkan}
            />
          </Grid>
         </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled
              placeholder="contoh : dua puluh "
              required
              value={perbelanjaanDibenarkanKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setPerbelanjaanDibenarkanKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    perbelanjaanDibenarkanKata: "",
                  }));
                } else {
                  setPerbelanjaanDibenarkanKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    perbelanjaanDibenarkanKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.perbelanjaanDibenarkanKata}
              helperText={formErrors.perbelanjaanDibenarkanKata}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            const errors = validateForm();
            if (Object.keys(errors).length > 0) {
              setFormErrors(errors);
              return;
            }

            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jumlahWangTangan,
                  titleName: "Jumlah Wang Tangan yang Dibenarkan Dalam Tangan",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jumlahWangTanganKata,
                  titleName:
                    "Jumlah Wang Tangan yang Dibenarkan Dalam Tangan (Tulis Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kuasaPerbelanjaan,
                  titleName: "Kuasa Perbelanjaan Mesyuarat Agung",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kuasaPerbelanjaanKata,
                  titleName:
                    "Kuasa Perbelanjaan Mesyuarat Agung (Tulis Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kuasaPerbelanjaanJawatankuasa,
                  titleName: "Kuasa Perbelanjaan Jawatankuasa",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kuasaPerbelanjaanJawatankuasaKata,
                  titleName:
                    "Kuasa Perbelanjaan Jawatankuasa (Tulis Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: perbelanjaanDibenarkan,
                  titleName: "Perbelanjaan yang Dibenarkan",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: perbelanjaanDibenarkanKata,
                  titleName:
                    "Perbelanjaan yang Dibenarkan (Tulis Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tempohDibenarkan,
                  titleName:
                    "Tempoh yang Dibenarkan Untuk Memasukkan Wang Lebihan ke Dalam Bank",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tahunKewanganBermula,
                  titleName:
                    "Tahun Kewangan Bermula (Tahun Kewangan Bermula Dari Tahun Kewangan Yang Dibenarkan)",
                },
              ],
              clause: "clause9",
              clauseCount: 9,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentSembilanCawangan;
