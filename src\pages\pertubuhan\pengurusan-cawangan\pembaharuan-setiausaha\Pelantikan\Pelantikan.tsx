import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useNotification } from "@refinedev/core";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/redux/store";
import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  setIsSecretaryReformSuccess,
  handleNext,
  reset,
} from "@/redux/secretaryBranchReformReducer";
import {
  useMutation,
  useQuery,
  omitKeysFromObject,
  filterEmptyValuesOnObject,
  MeetingTypeOption,
} from "@/helpers";
import { useSecretaryBranchReformContext } from "../Provider";

import { Box, Fade } from "@mui/material";
import {
  ButtonOutline,
  ButtonPrimary,
  CustomSkeleton,
  DialogConfirmation,
} from "@/components";
import MeetingForm from "../view/MeetingForm";
import SecretaryForm from "../view/SecretaryForm";

import { IApiResponse, ISecretaryBranch, ISocietyBranchDetail } from "@/types";

const formComponents: Record<number, React.ReactNode> = {
  1: <SecretaryForm />,
  2: <MeetingForm />,
};

export const PelantikanComp: React.FC = () => {
  const { open } = useNotification();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { branchId } = useParams();
  const { setValue, getValues, trigger, reset: resetForm } = useFormContext();
  const { isAgreed, handleSetMeetingList } = useSecretaryBranchReformContext();

  const formStep = useSelector(
    (state: RootState) => state.secretaryBranchReform.step
  );
  const isSuccess = useSelector(
    (state: RootState) => state.secretaryBranchReform.isSuccess
  );

  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const { isLoading: isLoadingBranchData } = useQuery<
    IApiResponse<ISocietyBranchDetail>
  >({
    url: `society/branch/getById/${branchId}`,
    autoFetch: !!branchId,
    onSuccess: (data) => {
      const branchData = data?.data?.data;

      if (branchData) {
        setValue("branchId", branchData.id);
        setValue("branchNo", branchData.branchNo);
      }
    },
  });

  const { isLoading: isLoadingSecretaryBranch } = useQuery<
    IApiResponse<ISecretaryBranch>
  >({
    url: `society/branch/${branchId}/active-secretary`,
    onSuccess: (data) => {
      const secretaryBranch = data?.data?.data ?? null;

      if (secretaryBranch) {
        setValue("oldSecretaryId", secretaryBranch.id);
        setValue("branchId", secretaryBranch.branchId);
        setValue("branchNo", secretaryBranch.branchNo);
        setValue("oldSecretaryName", secretaryBranch.committeeName);
        setValue(
          "oldSecretaryIdentificationNumber",
          secretaryBranch.committeeIcNo
        );
      }
    },
  });

  const { fetch: createSecretaryBranch, isLoading: isCreatingSecretaryBranch } =
    useMutation<IApiResponse<{ id: string | number }>>({
      url: "society/branch/committee/secretary",
      method: "post",
      onSuccess: (data) => {
        const successCode = [200, 201];
        const responseCode = data?.data?.code;
        const id = data?.data?.data?.id;

        if (!successCode.includes(responseCode))
          return open?.({
            type: "error",
            message: t("error"),
            description: "Failed to create secretary branch",
          });

        if (id) setTimeout(() => navigate("../.."), 1000);

        dispatch(setIsSecretaryReformSuccess(true));
      },
    });

  const renderForm = () => formComponents[formStep] || null;

  const handleNextStep = async () => {
    const isFormValid = await trigger();

    if (isFormValid) dispatch(handleNext());
  };

  const handleCreateSecretary = () => {
    const formValues = getValues();

    const {
      workTelNoCode,
      officePhoneNumber,
      homeTelNoCode,
      homePhoneNumber,
      hpNoCode,
      phoneNumber,
      meetingType,
      reasonOfChange,
      otherReason,
    } = formValues;

    const keysToSkip = [
      "workTelNoCode",
      "hpNoCode",
      "homeTelNoCode",
      "oldSecretaryName",
      "oldSecretaryIdentificationNumber",
      "memberCount",
      "ajkCount",
      "otherReason",
      "identificationNo",
    ];

    const filteredSecretaryValues = omitKeysFromObject(formValues, keysToSkip);

    const formattedMeetingType =
      typeof meetingType === "string"
        ? MeetingTypeOption.find((meeting) => meeting.label === meetingType)
            ?.value ?? meetingType
        : meetingType;

    const payload = filterEmptyValuesOnObject({
      ...filteredSecretaryValues,
      officePhoneNumber: `${workTelNoCode} ${officePhoneNumber}`,
      homePhoneNumber: `${homeTelNoCode} ${homePhoneNumber}`,
      phoneNumber: `${hpNoCode} ${phoneNumber}`,
      meetingType: formattedMeetingType,
      reasonOfChange:
        reasonOfChange === "Lain-lain" ? otherReason : reasonOfChange,
    });

    createSecretaryBranch(payload);
  };

  const handleSubmit = async () => {
    const isFormValid = await trigger();

    if (isFormValid) setIsDialogOpen(true);
  };

  useEffect(() => {
    return () => {
      dispatch(reset());
    };
  }, []);

  return (
    <>
      <Box
        sx={{
          backgroundColor: "white",
          p: 3,
          borderRadius: "15px",
        }}
      >
        {isLoadingSecretaryBranch || isLoadingBranchData ? (
          <CustomSkeleton />
        ) : (
          <Fade in={true} timeout={500} key={formStep}>
            <Box>{renderForm()}</Box>
          </Fade>
        )}

        {formStep === 1 ? (
          <Box
            sx={{ display: "flex", justifyContent: "flex-end", mt: 5, gap: 1 }}
          >
            <ButtonOutline
              onClick={resetForm}
              sx={{
                width: "100px",
                minWidth: "unset",
                textTransform: "none",
                fontWeight: 400,
                fontSize: "8px",
                borderColor: "#DADADA",
              }}
            >
              {t("reset")}
            </ButtonOutline>
            <ButtonPrimary
              onClick={handleNextStep}
              sx={{
                display: "flex",
                alignItems: "center",
                gap: "5px",
                backgroundColor: "var(--primary-color)",
                width: "100px",
                minWidth: "unset",
                height: "32px",
                color: "white",
                "&:hover": { backgroundColor: "#19ADAD" },
                textTransform: "none",
                fontWeight: 400,
                fontSize: "8px",
              }}
            >
              {t("next")}
            </ButtonPrimary>
          </Box>
        ) : (
          <Box
            sx={{ display: "flex", justifyContent: "flex-end", mt: 5, gap: 1 }}
          >
            <ButtonPrimary
              onClick={() => handleSubmit()}
              disabled={!isAgreed}
              sx={{
                textTransform: "none",
                fontSize: "14px",
                color: "#FFFFFF",
              }}
            >
              {t("hantar")}
            </ButtonPrimary>
          </Box>
        )}
      </Box>

      <DialogConfirmation
        open={isDialogOpen}
        isSuccess={isSuccess}
        isMutating={isCreatingSecretaryBranch}
        onClose={() => setIsDialogOpen(false)}
        onAction={handleCreateSecretary}
      />
    </>
  );
};

export default PelantikanComp;
