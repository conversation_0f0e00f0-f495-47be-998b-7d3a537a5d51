import { useEffect, FormEvent } from "react";
import { t } from "i18next";
import { useNavigate } from "react-router-dom";
import { CrudFilter } from "@refinedev/core";
import { FieldValues, useForm } from "react-hook-form";
import { NEW_PermissionNames, pageAccessEnum, useQuery } from "@/helpers";

import {
  Box,
  IconButton,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import {
  ButtonOutline,
  ButtonPrimary,
  IColumn,
  DataTable,
  FormFieldRow,
  Label,
  TextFieldController,
} from "@/components";
import ButtonPrevious from "@/components/button/ButtonPrevious";

import { EditIcon } from "@/components/icons";

import { IApiPaginatedResponse } from "@/types";
import { ILookupDetail } from "@/types";
import AuthHelper from "@/helpers/authHelper";

const List = () => {
  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const hasCreatePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.SELENGGARA.children.PENYELENGGARAAN_UMUM.children
      .JABATAN_INSOLVENSI.label,
    pageAccessEnum.Create
  );
  const { control, setValue, watch, getValues } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      nameQuery: "",
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");

  const {
    data: insolvencyDepartmentListResponse,
    refetch: fetchInsolvencyDepartment,
    isLoading: isLoadingInsolvencyDepartmentList,
  } = useQuery<IApiPaginatedResponse<ILookupDetail[]>>({
    url: "society/lookup/insolvencyDepartment/all",
    autoFetch: false,
  });

  const insolvencyDepartmentList =
    insolvencyDepartmentListResponse?.data.data?.data ?? [];
  const totalInsolvencyDepartmentList =
    insolvencyDepartmentListResponse?.data?.data?.total ?? 0;

  const getBaseFilters = (pageSize: number, pageNo: number): CrudFilter[] => [
    { field: "pageSize", value: pageSize, operator: "eq" },
    { field: "pageNo", value: pageNo, operator: "eq" },
  ];

  const buildFilters = (
    pageSize: number,
    pageNo: number,
    options: {
      nameQuery?: string;
    }
  ): CrudFilter[] => {
    const filters = getBaseFilters(pageSize, pageNo);

    if (options.nameQuery) {
      filters.push({
        field: "nameQuery",
        value: options.nameQuery,
        operator: "eq",
      });
    }

    return filters;
  };

  const columns: IColumn<any>[] = [
    { field: "code", headerName: t("kodInsolvensi"), flex: 1, align: "center" },
    {
      field: "name",
      headerName: t("namaJabatanInsolvensi"),
      flex: 1,
      align: "center",
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => {
        return (
          <Typography
            sx={{
              backgroundColor: row.status ? "#00B69B66" : "#FF000066",
              color: "#FFFFFF",
              padding: "4px 10px",
              borderRadius: "16px",
              fontSize: "10px",
              fontWeight: 500,
              width: "fit-content",
              marginInline: "auto",
            }}
          >
            {row.status ? "active" : "inactive"}
          </Typography>
        );
      },
    },
    {
      field: "status",
      headerName: t("action"),
      align: "center",
      renderCell: ({ row }) => {
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <IconButton
              onClick={() => {
                navigate(`kemaskini/${row.id}`);
              }}
            >
              <EditIcon color="var(--primary-color)" />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  const applyFilters = (pageSize: number, page: number) => {
    const formValues = getValues();
    const filters = buildFilters(pageSize, page, {
      nameQuery: formValues.nameQuery,
    });

    fetchInsolvencyDepartment({ filters });
  };

  const handleSearchNameQuery = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    applyFilters(pageSize, 1);
  };

  const handleReset = () => {
    setValue("nameQuery", "");
    const filters = getBaseFilters(pageSize, 1);
    fetchInsolvencyDepartment({ filters });
  };

  const handleChangePage = (newPage: number) => {
    setValue("page", newPage);
    applyFilters(pageSize, newPage);
  };

  const handleChangePageSize = (newPageSize: number) => {
    setValue("pageSize", newPageSize);
    applyFilters(newPageSize, 1);
  };

  useEffect(() => {
    applyFilters(pageSize, 1);
  }, []);

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography sx={{ mb: 2 }} className="title">
            {t("search")}
          </Typography>

          <Box
            component="form"
            onSubmit={handleSearchNameQuery}
            sx={{ display: "grid" }}
          >
            <FormFieldRow
              label={<Label text={t("namaJabatanInsolvensi")} />}
              value={<TextFieldController control={control} name="nameQuery" />}
            />

            <Box
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonPrevious
                variant="outlined"
                sx={{
                  bgcolor: "white",
                  "&:hover": { bgcolor: "white" },
                  width: isMobile ? "100%" : "auto",
                }}
                onClick={handleReset}
              >
                {t("previous")}
              </ButtonPrevious>
              <ButtonPrimary
                type="submit"
                variant="contained"
                sx={{
                  width: isMobile ? "100%" : "auto",
                }}
              >
                {t("search")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Box>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography className="title">{t("keputusan")}</Typography>

          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
            }}
          >
            <ButtonOutline
              onClick={() => {
                navigate("tambah");
              }}
              disabled={!hasCreatePermission}
            >
              {t("tambahJabatanInsolvensi")}
            </ButtonOutline>
          </Box>

          <Box
            sx={{
              borderRadius: "14px",
              mt: 3,
            }}
          >
            <DataTable
              columns={columns}
              rows={insolvencyDepartmentList}
              page={page}
              rowsPerPage={pageSize}
              totalCount={totalInsolvencyDepartmentList}
              isLoading={isLoadingInsolvencyDepartmentList}
              onPageChange={handleChangePage}
              onPageSizeChange={handleChangePageSize}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default List;
