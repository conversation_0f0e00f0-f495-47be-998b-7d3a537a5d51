import React, { useEffect, useRef, useState } from "react";
import {
  Box,
  TextField,
  Typography,
  Grid,
  Select,
  MenuItem,
  FormControl,
  useMediaQuery,
  Theme,
  Fade,
  FormHelperText,
  CircularProgress,
} from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "@/components";
import AmendmentAddress from "./view/amendmentAddress";
import AmendmentName from "./view/amendmentName";
import AmendmentNameAndAddress from "./view/amendmentNameAndAddress";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import { useQuery } from "@/helpers";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { setRegisterDateTime } from "@/redux/branchAmendReducer";

function SelectPindaan() {
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };
  const { t } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const dispatch = useDispatch();
  const isView = useSelector(
    (state: { branchAmendData: any }) => state.branchAmendData.isView
  );
  // const { branchId, amendId } = useParams();
  const branchAmendRedux = useSelector(
    (state: { branchAmendData: any }) => state.branchAmendData.data
  );

  const amendId = branchAmendRedux?.id;

  const navigate = useNavigate();
  const [amendmentType, setAmendmentType] = useState("");
  const [currentData, setCurrentData] = useState<any>(null);
  const [disableNext, setDisableNext] = useState(true);
  const [display, setDisplay] = useState(false);
  const AMENDMENTTYPE_LIST = [
    { value: "pinda_nama_alamat", label: t("pinda_nama_alamat") },
    { value: "pinda_nama", label: t("pinda_nama") },
    { value: "pinda_alamat", label: t("pinda_alamat") },
  ];

  const {
    data: exportData,
    isLoading: isLoadingExportConstitutions,
    refetch: exportFetch,
  } = useQuery({
    url: `society/external/branchAmendment/${amendId}`,
    onSuccess: (data) => {
      const response = data?.data?.data;
      setCurrentData(response);
      setAmendmentType(response?.amendmentType);
    },
  });

  const { mutate: updateData, isLoading: isLoadingUpdateData } =
    useCustomMutation();
  const { mutate: selectAmendType, isLoading: isLoadingSelectAmendType } =
    useCustomMutation();
  const reset = () => {
    setAmendmentType("");
  };

  const BELUM_HANTAR =
    (currentData?.applicationStatusCode &&
      (Number(currentData?.applicationStatusCode) === 1 ||
        Number(currentData?.applicationStatusCode) === 5 ||
        Number(currentData?.applicationStatusCode) === 6) &&
      currentData?.amendmentType) ||
    isView;

  const MENUNGGU_BAYARAN =
    Number(currentData?.applicationStatusCode) === 5 ||
    Number(currentData?.applicationStatusCode) === 6 ||
    Number(currentData?.applicationStatusCode) === 44;

  const updateType = () => {
    selectAmendType(
      {
        url: `${API_URL}/society/external/branchAmendment/update`,
        method: "patch",
        values: {
          id: amendId,
          amendmentType: "PINDAAN_NAMA_DAN_ALAMAT_CAWANGAN",
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: t("error"),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          const infoId = data?.data?.data?.id;
          setDisplay(true);
        },
      }
    );
  };

  const onUpdate = (data: any) => {
    const req = {
      id: amendId,
      ...data,
    };

    updateData(
      {
        url: `${API_URL}/society/external/branchAmendment/update`,
        method: "patch",
        values: req,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          const dateTime = data?.data?.data?.createdDate;
          dispatch(setRegisterDateTime(dateTime));
          const statusCode = data?.data?.code;
          if (statusCode === 400) {
            return {
              message: data?.data?.msg,
              type: "error",
            };
          }
          setDisableNext(false);
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: t("messageKeputusanPermohonanError"),
            type: "error",
          };
        },
      }
      // {
      //   onSuccess(data, variables, context) {
      //     setDisableNext(false);
      //   },
      // }
    );
  };

  const renderAmendmentContent = () => {
    let content;
    switch (amendmentType) {
      case "pinda_nama_alamat":
        content = (
          <AmendmentNameAndAddress
            onUpdate={(data) => onUpdate(data)}
            disableNext={disableNext}
            currentData={currentData}
            isView={isView}
            isWaitingPayment={MENUNGGU_BAYARAN}
          />
        );
        break;
      case "pinda_nama":
        content = (
          <AmendmentName
            onUpdate={(data) => onUpdate(data)}
            disableNext={disableNext}
            currentData={currentData}
            isView={isView}
            isWaitingPayment={MENUNGGU_BAYARAN}
          />
        );
        break;
      case "pinda_alamat":
        content = (
          <AmendmentAddress
            onUpdate={(data) => onUpdate(data)}
            disableNext={disableNext}
            currentData={currentData}
            isView={isView}
            isWaitingPayment={MENUNGGU_BAYARAN}
          />
        );
        break;
      default:
    }
    return content;
  };

  if (isLoadingExportConstitutions) {
    return (
      <Box sx={{ display: "flex", gap: 2, alignItems: "flex-start" }}>
        <Box
          sx={{
            width: "55vw",
            backgroundColor: "white",
            p: 3,
            borderRadius: "15px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "50vw",
          }}
        >
          <CircularProgress />
        </Box>
      </Box>
    );
  }

  return (
    <Box>
      <Box
        sx={{
          backgroundColor: "white",
          p: 3,
          borderRadius: "15px",
        }}
      >
        <Box
          sx={{
            p: { xs: 2, sm: 3 },
            width: "100%",
            backgroundColor: "none",
            border: "1px solid #EAEAEA",
            borderRadius: "12px",
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("selectAmendBranch")}
          </Typography>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={3} sx={{ display: "flex" }}>
              <Typography sx={labelStyle}>
                {t("amendmentType")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} md={9}>
              <FormControl fullWidth>
                <Select
                  size="small"
                  disabled={isView}
                  value={amendmentType}
                  displayEmpty
                  required
                  onChange={(e) => {
                    setAmendmentType(e.target.value);
                    setDisableNext(true);
                  }}
                >
                  {AMENDMENTTYPE_LIST?.map((items: any, index) => {
                    return (
                      <MenuItem key={index} value={items.value}>
                        {items.label}
                      </MenuItem>
                    );
                  })}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
          <Grid
            container
            spacing={2}
            sx={{
              mt: 1,
              display: isView ? "none" : "block",
            }}
          >
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline
                sx={{
                  width: isMobile ? "100%" : "auto",
                }}
                onClick={() => reset()}
              >
                {t("semula")}
              </ButtonOutline>
              <ButtonPrimary
                variant="contained"
                sx={{ width: isMobile ? "100%" : "auto" }}
                onClick={updateType}
                disabled={!amendmentType || isLoadingSelectAmendType}
              >
                {t("update")}
              </ButtonPrimary>
            </Grid>
          </Grid>
        </Box>
      </Box>
      {BELUM_HANTAR || display ? renderAmendmentContent() : null}
    </Box>
  );
}

export default SelectPindaan;
