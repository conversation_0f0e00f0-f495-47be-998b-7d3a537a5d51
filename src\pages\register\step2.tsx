import { useEffect, useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { Box, Typography, TextField, useTheme, Button } from "@mui/material";
import { useTranslation } from "react-i18next";
import { ChevronLeftRounded, DoneRounded } from "@mui/icons-material";
import { API_URL } from "../../api";
import { ValidationTimeTwoMin } from "../../helpers/enums";
import { useCustomMutation, useRegister } from "@refinedev/core";
import { ButtonPrimary, ButtonText } from "@/components/button";
import { redirect, useNavigate } from "react-router-dom";

const maskPhoneNumber = (phoneNumber: string) => {
  if (!phoneNumber) return "";
  // Keep first 2 digits and last 2 digits visible, mask the rest
  return (
    phoneNumber.slice(0, 3) +
    "*".repeat(phoneNumber.length - 3) +
    phoneNumber.slice(-2)
  );
};

export const Step2 = ({
  IC,
  name,
  email,
  phoneNumber,
  idType,
  citizenshipTitle,
  isMyDigitalIdNewUser,
  isUsingMyDigitalId,
  onBack,
  onNext,
  onPhoneOTPSuccess,
}: {
  IC?: string;
  name?: string;
  email?: string;
  phoneNumber: string;
  idType?: number;
  citizenshipTitle?: number | undefined;
  isMyDigitalIdNewUser?: boolean | null | undefined;
  isUsingMyDigitalId?: boolean | null | undefined;
  onBack: () => void;
  onNext: () => void;
  onPhoneOTPSuccess: () => void;
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  const [isVerified, setIsVerified] = useState(false);
  const [isVerifyOtpRequested, setVerifyOtpRequested] = useState(false);
  const [timeLeft, setTimeLeft] = useState(ValidationTimeTwoMin);
  const [isOTPSuccess, setIsOTPSuccess] = useState(true);
  const [otpError, setOTPError] = useState("");
  const navigate = useNavigate();

  useEffect(() => {
    // exit early when we reach 0
    if (timeLeft <= 0) {
      return;
    }

    // save intervalId to clear the interval when the
    // component re-renders
    const intervalId = setInterval(() => {
      setTimeLeft((prevTimeLeft) => prevTimeLeft - 1);
    }, 1000);

    // clear interval on re-render to avoid memory leaks
    return () => clearInterval(intervalId);
    // add timeLeft as a dependency to re-rerun the effect
    // when we update it
  }, [timeLeft]);

  const formatTimeLeft = (seconds: number) => {
    const minutes = String(Math.floor(seconds / 60)).padStart(2, "0");
    const secs = String(seconds % 60).padStart(2, "0");
    return `${minutes}:${secs}`;
  };

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm({
    defaultValues: {
      verificationCode: "",
    },
  });

  const verificationCode = watch("verificationCode");
  const isVerificationCodeComplete = verificationCode.length === 6;

  const onSubmit = () => {
    // Perform verification
    // If successful:
    if (verificationCode.length === 6) {
      verifyPhoneOtp();
    }
  };

  const requestPhoneOtp = async () => {
    if (!timeLeft) {
      const response = await fetch(
        `${API_URL}/user/otp/confirmPhone?phone=${phoneNumber}`,
        {
          method: "get",
          headers: {
            portal: localStorage.getItem("portal") || "",
            //"authorization": `Bearer ${localStorage.getItem('refine-auth')}`,
            "Content-Type": "application/json",
          },
        }
      );
      const data = await response.json();
      if (data.status === "SUCCESS") {
        setTimeLeft(ValidationTimeTwoMin);
      }
    }
  };

  const { mutate: verifyOtpRequest, isLoading } = useCustomMutation();
  const { mutate: register } = useRegister();

  const verifyPhoneOtp = async () => {
    setVerifyOtpRequested(true);
    const values = {
      otp: verificationCode,
      moduleType: "CONFIRM_PHONE",
      identifier: phoneNumber,
    };
    verifyOtpRequest(
      {
        url: `${API_URL}/user/otp/verifyOtp`,
        method: "post",
        values: values,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            //authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (!data?.data?.data?.otpVerified && data?.data?.data?.message) {
            setOTPError(data?.data?.data?.message);
            return {
              message: t(data?.data?.data?.message),
              type: "error",
            };
          }

          if (data?.data?.data?.otpVerified) {
            setIsVerified(true);
            setIsOTPSuccess(true);
            onPhoneOTPSuccess();
            // Wait a moment before moving to the next step

            if (isUsingMyDigitalId) {
              const finalData = {
                identificationNo: IC,
                name: name,
                contact: phoneNumber,
                email: email,
                identificationType: idType,
                citizenshipTitle: citizenshipTitle,
                digitalIdUser: true,
              };
              register(finalData, {
                onSuccess: (response) => {
                  setTimeout(() => {
                    if (response.success) {
                      setIsVerified(true);
                      localStorage.removeItem("registrationData"); // Clear temporary data
                      localStorage.removeItem("myDigitalIdNewUser");
                      localStorage.removeItem("myDigitalIdNewUserName");
                      localStorage.removeItem("myDigitalIdNewUserID");
                      localStorage.removeItem("isUsingMyDigitalId");
                    } else {
                      return {
                        message: t(data?.data?.data?.message),
                        type: "error",
                      };
                    }
                    navigate("/login");
                  }, 3000);
                },
                onError: (error) => {
                  console.error("Registration failed:", error);
                },
              });
            } else {
              setTimeout(() => {
                onNext();
              }, 3000);
            }

            //TODO PUT TRANSLATED MESSAGE HERE
            return {
              message: t("phoneVerificationSuccessful"),
              type: "success",
            };
          } else {
            setVerifyOtpRequested(false);
            setIsOTPSuccess(false);
            //TODO PUT TRANSLATED MESSAGE HERE
            return {
              message: t("phoneVerificationFailed"),
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          //TODO PUT TRANSLATED MESSAGE HERE
          setVerifyOtpRequested(false);
          return {
            message: t("phoneVerificationFailed"),
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          setVerifyOtpRequested(false);
          console.log(error);
        },
      }
    );
  };

  return (
    <>
      {/* Verification content */}

      {isVerified ? (
        <Box
          sx={{
            display: "grid",
            justifyItems: "center",
          }}
        >
          <Typography className="title-login">
            {t("phoneNoVerification")}
          </Typography>
          <Box
            sx={{
              display: "grid",
              gap: 2,
              mt: { sm: 2, md: 3, lg: 6, xl: 12 },
              justifyItems: "center",
            }}
          >
            <Box
              sx={{
                width: 42,
                height: 42,
                bgcolor: "var(--primary-color)",
                borderRadius: "50%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <DoneRounded sx={{ color: "#fff", fontSize: 25 }} />
            </Box>
            <Typography
              sx={{
                fontFamily: "Poppins, sans-serif",
                color: "#55556D",
                textAlign: "center",
              }}
            >
              {t("YourPhoneHasBeenVerified")}
            </Typography>
            {isUsingMyDigitalId ? null : (
              <>
                <ButtonPrimary
                  onClick={onNext}
                  fullWidth
                  className="btn-login"
                  sx={{ mt: { sm: 2, md: 3, lg: 6, xl: 12 } }}
                >
                  {t("Continue")}
                </ButtonPrimary>
                <ButtonText
                  onClick={onBack}
                  className="label"
                  sx={{ textDecoration: "underline", color: "#666666B2" }}
                >
                  <ChevronLeftRounded /> {t("back")}
                </ButtonText>
              </>
            )}
          </Box>
        </Box>
      ) : (
        <Box
          sx={{
            display: "grid",
            pl: { xs: 2, sm: 6, md: 6 },
            pr: { xs: 2, sm: 6, md: 6 },
            pt: 4,
          }}
          component="form"
          noValidate
          onSubmit={handleSubmit(onSubmit)}
        >
          <Typography
            sx={{
              fontFamily: "Poppins, sans-serif",
              fontSize: "25px",
              fontWeight: 500,
              lineHeight: "24px",
              color: "#55556D",
            }}
          >
            {t("phoneNoVerification")}
          </Typography>
          <Typography
            className="label"
            sx={{
              fontSize: "14px !important",
              color: "#55556D",
              mt: 2,
              mb: 5,
            }}
          >
            {t("VerificationCodeSentTo")}
            <br />
            {maskPhoneNumber(phoneNumber)}
          </Typography>
          <Controller
            name="verificationCode"
            control={control}
            rules={{
              required: t("VerificationCodeIsRequired"),
              validate: (value) =>
                value.length === 6 || t("VerificationCodeMustBe6Digits"),
            }}
            render={({ field }) => (
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  gap: { xs: 0.5, sm: 1, md: 1.5 },
                  overflow: "hidden",
                }}
              >
                {[0, 1, 2, 3, 4, 5].map((index) => (
                  <TextField
                    key={index}
                    {...field}
                    value={field.value[index] || ""}
                    inputProps={{
                      inputMode: "numeric",
                      pattern: "[0-9]*",
                    }}
                    error={!isOTPSuccess}
                    onChange={(e) => {
                      console.log("change");
                      setVerifyOtpRequested(false);
                      const value = e.target.value.replace(/[^0-9]/g, "");
                      const newValue = field.value.split("");
                      newValue[index] = value;
                      field.onChange(newValue.join(""));
                      if (value && index < 5) {
                        const nextInput =
                          e.target.parentElement?.parentElement?.nextElementSibling?.querySelector(
                            "input"
                          );
                        if (nextInput) nextInput.focus();
                      }
                    }}
                    sx={{
                      width: { xs: "35px", sm: "40px", md: "50px" },
                      height: { xs: "45px", sm: "50px", md: "60px" },
                      color: "#666666",
                      "& .MuiOutlinedInput-root": {
                        height: "100%",
                        "& fieldset": {
                          borderColor: "#DADADA",
                          borderRadius: "10px",
                        },
                      },
                      "& .MuiInputBase-input": {
                        textAlign: "center",
                        fontSize: { xs: "18px", sm: "20px", md: "24px" },
                        p: 0,
                        height: "100%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      },
                    }}
                  />
                ))}
              </Box>
            )}
          />

          {(!isOTPSuccess || otpError) && (
            <Typography
              className="error-text-login"
              sx={{
                mt: 12,
              }}
            >
              {otpError ? t(otpError) : t("emailOTPFailed")}
            </Typography>
          )}
          <ButtonPrimary
            type="submit"
            fullWidth
            disabled={
              !isVerificationCodeComplete ||
              isVerifyOtpRequested ||
              timeLeft <= 0
            }
            className="btn-login"
            sx={{
              mt: isOTPSuccess ? 12 : 0,
            }}
          >
            {t("Continue")}
          </ButtonPrimary>
          {timeLeft > 0 ? (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                mt: 2,
                mb: 4,
              }}
            >
              <Typography
                sx={{
                  fontFamily: "Poppins",
                  fontSize: "14px",
                  fontWeight: 600,
                  lineHeight: "17.5px",
                  textAlign: "left",
                  color: "#000000B2",
                  opacity: timeLeft === 0 ? 0.7 : 0.4,
                  mr: 1,
                }}
              >
                {t("ResendCodeIn")}
              </Typography>
              <Typography
                sx={{
                  fontFamily: "Arial, sans-serif", // Changed to a regular font
                  fontSize: "15px",
                  fontWeight: 400,
                  lineHeight: "18.75px",
                  textAlign: "left",
                  color: "#000000B2",
                  opacity: 0.7,
                }}
              >
                <span style={{ fontFamily: "Arial, sans-serif" }}>
                  {formatTimeLeft(timeLeft)}
                </span>
              </Typography>
            </Box>
          ) : (
            <Button
              variant="outlined"
              sx={{
                mb: 2,
                mt: 2,
                textTransform: "capitalize",
                borderRadius: "7px",
              }}
              type="button"
              size="large"
              fullWidth
              onClick={requestPhoneOtp}
            >
              {t("ResendCodeIn")}
            </Button>
          )}

          <ButtonText
            onClick={onBack}
            className="label"
            sx={{ textDecoration: "underline", color: "#666666B2", mr: 3 }}
            disableRipple
          >
            <ChevronLeftRounded /> {t("back")}
          </ButtonText>
        </Box>
      )}
    </>
  );
};
