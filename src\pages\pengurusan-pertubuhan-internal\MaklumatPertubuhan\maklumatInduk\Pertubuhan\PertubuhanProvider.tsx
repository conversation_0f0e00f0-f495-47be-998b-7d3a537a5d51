import React, {
  createContext,
  useContext,
  PropsWithChildren,
  useState,
} from "react";
import { useLocation } from "react-router-dom";
import useQuery from "../../../../../helpers/hooks/useQuery";

interface PertubuhanContextProps {
  societyDetailData: any;
  addressData: any;
  meetingDetail: any;
  committeeList: any;
  setCommittee: any;
  committee: any;
  document: any;
  // listAjk: any;
}

const PertubuhanContext = createContext<PertubuhanContextProps | undefined>(
  undefined
);

// eslint-disable-next-line react-refresh/only-export-components
export const usePertubuhanContext = (): PertubuhanContextProps => {
  const context = useContext(PertubuhanContext);

  if (!context) {
    throw new Error(
      "usePertubuhanContext must be used within a usePertubuhanContext"
    );
  }
  return context;
};

const PertubuhanProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const location = useLocation();
  const societyId = location.state?.societyId;

  const { data: societyDetailResponse, refetch: fetchSociety } = useQuery({
    url: `society/${societyId}`,
  });

  const { data: addressList } = useQuery({
    url: `society/admin/address/list`,
  });

  const { data: meetingDetailData } = useQuery({
    url: `society/meeting/findBySocietyId/${societyId}`,
  });

  const { data: committeeListData } = useQuery({
    url: `society/committee/getAll`,
    filters: [{ field: "societyId", operator: "eq", value: societyId }],
  });

  // const { data: listAjkData } = useQuery({
  //   url: `society/committee/listAjk`,
  //   filters: [
  //     { field: "societyId", operator: "eq", value: societyId },
  //     { field: "status", operator: "eq", value: "001" },
  //   ],
  // });

  const { data: documentData } = useQuery({
    url: `society/document/documentByParam`,
    filters: [{ field: "societyId", operator: "eq", value: societyId }],
  });

  const [committee, setCommittee] = useState(null);

  const addressData = addressList?.data?.data || [];
  const societyDetailData = societyDetailResponse?.data?.data ?? null;
  const meetingDetail =
    meetingDetailData?.data?.data?.length > 0
      ? meetingDetailData?.data?.data[0]
      : null;
  const committeeList = committeeListData?.data?.data ?? [];
  const document = documentData?.data?.data || [];
  // const listAjk = listAjkData?.data?.data || [];

  return (
    <PertubuhanContext.Provider
      value={{
        societyDetailData,
        addressData,
        meetingDetail,
        committeeList,
        setCommittee,
        committee,
        document,
        // listAjk,
      }}
    >
      {children}
    </PertubuhanContext.Provider>
  );
};

export default PertubuhanProvider;
