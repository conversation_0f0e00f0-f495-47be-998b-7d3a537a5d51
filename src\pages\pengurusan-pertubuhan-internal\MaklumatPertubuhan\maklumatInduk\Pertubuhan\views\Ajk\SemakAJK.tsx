import {
  Box,
  CircularProgress,
  Grid,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { t } from "i18next";
import {
  ButtonOutline,
  ButtonPrimary,
} from "../../../../../../../components/button";
import {
  CitizenshipStatus,
  designation,
  formatDate,
  ListGender,
  OrganisationPositions,
  useQuery,
} from "@/helpers";
import Input from "@/components/input/Input";

const SemakAJK = () => {
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
    "& span": {
      color: "red",
    },
  };
  const { id } = useParams();
  const decodedId = atob(id || "");
  const location = useLocation();
  const societyDetailData = location.state?.societyDetailData;

  const [pertubuhan, setPert<PERSON>uhan] = useState([]);
  const { data: listAjkData, isLoading } = useQuery({
    url: `society/committee/listAjk`,
    filters: [
      { field: "societyId", operator: "eq", value: decodedId },
      {
        field: "status",
        operator: "eq",
        value: societyDetailData?.applicationStatusCode !== 3 ? "008" : "001",
      },
    ],
    onSuccess: (data) => {
      setPertubuhan(data?.data?.data?.data || []);
    },
  });

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  console.log("pertubuhan", pertubuhan);
  const rowHeight = "80px";
  const getGenderLabel = (val: string) => {
    if (!val) return "-";
    const genderItem = ListGender?.find((item) => item.value === val) ?? null;
    return genderItem?.label ? t(genderItem.label) : "-";
  };

  const getCitizenshipStatus = (val: string) => {
    if (!val) return "-";
    const citizenshipItem =
      CitizenshipStatus?.find((item) => item.value === parseInt(val)) ?? null;
    return citizenshipItem?.label ? t(citizenshipItem.label) : "-";
  };

  const downloadFile = (filePath: string) => {
    window.open(filePath, "_blank");
  };

  const {
    data: exportAjkGeneral,
    isLoading: isLoadingExportAjkGeneral,
    refetch: exportAjkGeneralFetch,
  } = useQuery({
    url: `society/committee/exportAjkGeneral`,
    autoFetch: false,
    onSuccess: (data) => {
      const link = data?.data?.data;
      downloadFile(link);
    },
  });

  const exportUmum = () => {
    exportAjkGeneralFetch({
      filters: [
        {
          field: "societyId",
          value: decodedId,
          operator: "eq",
        },
      ],
    });
  };

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            <Typography
              fontWeight="500 !important"
              fontSize="14px"
              lineHeight="21px"
            >
              {societyDetailData?.societyName ?? "-"} <br />
              {societyDetailData?.societyNo
                ? societyDetailData?.societyNo
                : societyDetailData?.applicationNo ?? "-"}
            </Typography>
          </Box>
        </Box>
      </Box>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("viewAjk")}
            </Typography>
            <Input
              label={t("organizationName")}
              value={societyDetailData.societyName ?? "-"}
              disabled
            />
            <Input
              label={t("organizationNumber")}
              value={
                societyDetailData.societyNo
                  ? societyDetailData.societyNo
                  : societyDetailData.applicationNo ?? "-"
              }
              disabled
            />
          </Box>
        </Box>
      </Box>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("viewAjk")}
            </Typography>
            <Box
              sx={{
                pl: 2,
                p: 3,
                mt: 1,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
              }}
            >
              <Box sx={{ display: "flex", width: "100%", overflow: "scroll" }}>
                {/* Labels */}
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    textAlign: "center",
                    borderRight: "1px solid #DADADA",
                    flexShrink: 0,
                  }}
                >
                  {[
                    { label: t("jawatan"), key: "designationCode" },
                    { label: t("namaPenuh"), key: "fullName" },
                    { label: t("gender"), key: "gender" },
                    { label: t("idNumberPlaceholder"), key: "idNumber" },
                    { label: t("citizen"), key: "citizen" },
                    { label: t("dateAndBirthPlace"), key: "dobAndPob" },
                    { label: t("phone"), key: "phone" },
                    { label: t("pekerjaan"), key: "occupation" },
                    {
                      label: t("residentialAddress"),
                      key: "residentialAddress",
                    },
                    { label: t("employerAddress"), key: "employerAddress" },
                  ].map((row) => (
                    <Box
                      key={row.key}
                      sx={{
                        ...labelStyle,
                        fontWeight: "500!important",
                        fontSize: "14px",
                        color: "#666666",
                        minHeight: rowHeight,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        padding: "0 20px",
                      }}
                    >
                      {row.label}
                    </Box>
                  ))}
                </Box>

                {/* Data Columns */}
                <Box sx={{ display: "flex" }}>
                  {pertubuhan?.length > 0 ? (
                    pertubuhan.map((item: any, colIndex: number) => {
                      const {
                        id,
                        name,
                        gender,
                        identificationNo,
                        nationalityStatus,
                        dateOfBirth,
                        phoneNumber,
                        jobCode,
                        residentialAddress,
                        designationCode,
                        employerAddress,
                      } = item;

                      const rowData = [
                        designationCode
                          ? t(
                              OrganisationPositions.find(
                                (item) => item.value === Number(designationCode)
                              )?.label ?? "-"
                            )
                          : "-",
                        name,
                        getGenderLabel(gender),
                        identificationNo,
                        getCitizenshipStatus(nationalityStatus),
                        formatDate(dateOfBirth),
                        phoneNumber,
                        jobCode,
                        residentialAddress,
                        employerAddress ?? "-",
                      ];

                      return (
                        <Box
                          key={id}
                          sx={{
                            display: "flex",
                            flexDirection: "column",
                            textAlign: "center",
                            borderRight:
                              colIndex === pertubuhan.length - 1
                                ? "none"
                                : "1px solid #DADADA",
                            flex: 1,
                            minWidth: 0,
                          }}
                        >
                          {rowData.map((value, index) => (
                            <Box
                              key={index}
                              sx={{
                                ...labelStyle,
                                padding: "0 30px",
                                minHeight: rowHeight,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                whiteSpace: index >= 8 ? "pre-wrap" : "nowrap",
                                wordBreak: "break-word",
                                color:
                                  index === 0
                                    ? "var(--primary-color)"
                                    : "inherit",
                                width: "100%",
                                maxWidth: "100%",
                              }}
                            >
                              {value || ""}
                            </Box>
                          ))}
                        </Box>
                      );
                    })
                  ) : isLoading ? (
                    <CircularProgress size={15} sx={{ m: 2 }} />
                  ) : null}
                </Box>
              </Box>
            </Box>
          </Box>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonOutline onClick={() => navigate(-1)}>
              {t("back")}
            </ButtonOutline>
            <ButtonPrimary type="submit" onClick={() => exportUmum()}>
              {t("cetak")}
            </ButtonPrimary>
          </Grid>
        </Box>
      </Box>
    </>
  );
};

export default SemakAJK;
