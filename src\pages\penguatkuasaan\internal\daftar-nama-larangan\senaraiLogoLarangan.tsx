import {
  Box,
  Chip,
  FormControl,
  IconButton,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import { LaranganBox } from "./component/LaranganBorder";
import { LaranganPaper } from "./component/LaranganPaper";
import { SearchIcon } from "@/components/icons";
import { FilterList, KeyboardArrowDown } from "@mui/icons-material";
import { useState } from "react";
import { Edit as EditIcon, Delete as DeleteIcon } from "@mui/icons-material";
import { DataTable } from "@/components";
import { pageAccessEnum, PermissionNames } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";
import { useNavigate } from "react-router-dom";
import VisibilityIcon from "@mui/icons-material/Visibility";

const sampleData = [
  {
    id: 1,
    logo: "https://doc-dev.ros.gov.my/takwim-banner/20200721_091637.jpg_90f9e44b-7d61-48f9-981d-380e39a05714?versionId=h20firY30aCxOa2c2G2pmwH3Aa5EKAcT",
    tarikhTerbit: "12-4-2025",
    masaTerbit: "3:40 petang",
    catatan: "diharamkan kerana mereka bukan tuhan di antartika",
    status: "aktif",
  },
  {
    id: 2,
    logo: "https://doc-dev.ros.gov.my/takwim-banner/20200721_091637.jpg_90f9e44b-7d61-48f9-981d-380e39a05714?versionId=h20firY30aCxOa2c2G2pmwH3Aa5EKAcT",
    tarikhTerbit: "12-4-2025",
    masaTerbit: "3:40 petang",
    catatan: "diharamkan kerana mereka bukan tuhan di antartika",
    status: "tidak_aktif",
  },
  // ... more data
];

export const SenaraiLogoLarangan = () => {
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState<"edit" | "delete">("edit");
  const [customDialogMessage, setCustomDialogMessage] = useState<string | null>(
    null
  );
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [data, setData] = useState(sampleData);
  const navigate = useNavigate();

  const theme = useTheme();
  const primary = theme.palette.primary.main;

  function checkPermissionAndUserGroup(accessType: number) {
    const hasPermission: boolean = AuthHelper.hasPageAccess(
      PermissionNames.PENGURUSAN_LARANGAN?.label || "PENG-LAR",
      accessType
    );
    if (localStorage.getItem("portal") === "2") {
      return hasPermission && true;
    } else {
      return hasPermission && false;
    }
  }

  const readAccess = checkPermissionAndUserGroup(pageAccessEnum.Read);
  const createAccess = checkPermissionAndUserGroup(pageAccessEnum.Create);
  const deleteAccess = checkPermissionAndUserGroup(pageAccessEnum.Delete);
  const updateAccess = checkPermissionAndUserGroup(pageAccessEnum.Update);
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setRowsPerPage(newPageSize);
  };

  const columns = [
    {
      field: "",
      headerName: "No.",
      flex: 0.5,
      align: "center" as const,
      renderCell: ({ rowIndex }: { rowIndex: number }) => {
        return <Typography>{rowIndex + 1}</Typography>;
      },
    },
    {
      field: "logo",
      headerName: "Logo",
      flex: 2,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        return (
          // <Image src={row.logo} alt="Logo" width={100} height={100} />
          <img src={row.logo} alt="" width={100} height={100} />
        );
      },
    },
    {
      field: "tarikhTerbit",
      headerName: "Tarikh Terbit",
      flex: 1,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        return (
          <Typography sx={{ fontSize: "14px" }}>{row.tarikhTerbit}</Typography>
        );
      },
    },
    {
      field: "masaTerbit",
      headerName: "Masa Terbit",
      flex: 1,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        return (
          <Typography sx={{ fontSize: "14px" }}>{row.masaTerbit}</Typography>
        );
      },
    },
    {
      field: "catatan",
      headerName: "Catatan",
      flex: 3,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        return (
          <Typography sx={{ fontSize: "14px", textAlign: "center" }}>
            {row.catatan}
          </Typography>
        );
      },
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        const isActive = row.status === "aktif";
        return (
          <Chip
            label={isActive ? "Aktif" : "Tidak Aktif"}
            sx={{
              backgroundColor: isActive ? "#E8F5E8" : "#FFE8E8",
              color: isActive ? "#2E7D32" : "#D32F2F",
              border: `1px solid ${isActive ? "#4CAF50" : "#F44336"}`,
              borderRadius: "16px",
              fontSize: "12px",
              fontWeight: 500,
              minWidth: "80px",
              height: "28px",
            }}
          />
        );
      },
    },
    {
      field: "tindakan",
      headerName: "Tindakan",
      flex: 1,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        return (
          <Box sx={{ display: "flex", gap: 1, justifyContent: "center" }}>
            {updateAccess && (
              <IconButton
                size="small"
                onClick={() =>
                  navigate(
                    `/penguatkuasaan/daftar_nama_larangan/tambah_rekod?id=${row.id}&tab=logo&mode=edit`
                  )
                }
                sx={{
                  color: "var(--primary-color)",
                  "&:hover": { backgroundColor: "rgba(12, 166, 166, 0.1)" },
                }}
              >
                <EditIcon sx={{ fontSize: "18px" }} />
              </IconButton>
            )}
            {deleteAccess && (
              <IconButton
                size="small"
                onClick={() => {
                  setOpenDialog(true);
                  setCustomDialogMessage(
                    "Adakah anda pasti untuk memadamkan nama larangan ini?"
                  );
                  setDialogType("delete");
                }}
                sx={{
                  color: "#F44336",
                  "&:hover": { backgroundColor: "rgba(244, 67, 54, 0.1)" },
                }}
              >
                <DeleteIcon sx={{ fontSize: "18px" }} />
              </IconButton>
            )}

            <IconButton
              size="small"
              onClick={() =>
                navigate(
                  `/penguatkuasaan/daftar_nama_larangan/tambah_rekod?id=${row.id}&tab=logo&mode=view`
                )
              }
              sx={{
                color: "var(--primary-color)",
                "&:hover": { backgroundColor: "rgba(12, 166, 166, 0.1)" },
              }}
            >
              <VisibilityIcon sx={{ fontSize: "18px" }} />
            </IconButton>
          </Box>
        );
      },
    },
  ];
  return (
    <LaranganPaper>
      <LaranganBox>
        <Typography
          color={primary}
          sx={{
            fontSize: 14,
            fontWeight: "medium",
            marginBottom: "1.5rem",
          }}
        >
          Logo Terlarang
        </Typography>
        <Box
          sx={{
            mr: "auto",
            ml: "auto",
            width: "70%",
            display: "flex",
            flexDirection: "column",
            // gap: 1,
          }}
        >
          <Box sx={{ mb: 2, width: "100%" }}>
            <TextField
              placeholder="Nama Acara"
              variant="outlined"
              fullWidth
              size="small"
              // value={searchTerm}
              // onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon sx={{ color: "#9CA3AF" }} />
                  </InputAdornment>
                ),
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  borderRadius: "10px",
                  "& fieldset": {
                    borderColor: "#E5E7EB",
                  },
                  "&:hover fieldset": {
                    borderColor: "#E5E7EB",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "#E5E7EB",
                  },
                },
              }}
            />
          </Box>
          <Box
            sx={{
              // border: "1px solid #E5E7EB",
              borderRadius: "30px",
              // padding: "30px 20px 20px 20px",
              // boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
            }}
          >
            <Box
              sx={{
                display: "flex",
                gap: 2,
                justifyContent: "center",
                ml: "auto",
                mr: "auto",
                width: "70%",
                borderRadius: "20px",
                boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
                p: "3px",
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  color: "#6B7280",
                  borderRight: "1px solid #E5E7EB",
                  pr: 2,
                  height: "40px",
                }}
              >
                <FilterList sx={{ fontSize: 20, mr: 1 }} />
                <Typography variant="body2">Tapis Berdasarkan</Typography>
              </Box>
              <FormControl
                size="small"
                sx={{
                  minWidth: 120,
                  borderRight: "1px solid #E5E7EB",
                  height: "40px",
                  // paddingRight: 2,
                  // marginRight: 2
                }}
              >
                <Select
                  displayEmpty
                  placeholder="Status Paparan"
                  // value={selectedActiveOption || ""}
                  // onChange={
                  //   (e) => setSelectedActiveOption(e.target.value)
                  //   // handleActiveOptionChange(e.target.value);
                  // }
                  IconComponent={(props) => (
                    <KeyboardArrowDown {...props} sx={{ marginLeft: 1 }} />
                  )}
                  sx={{
                    border: "none",
                    "& .MuiOutlinedInput-notchedOutline": {
                      border: "none",
                    },
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      border: "none",
                    },
                    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      border: "none",
                    },
                    "& .MuiSelect-select": {
                      paddingRight: "32px", // Make room for the dropdown icon
                      fontWeight: 400, // Normal font weight instead of bold
                      fontSize: "14px",
                      color: "#6B7280",
                    },
                  }}
                  // renderValue={(selected) => {
                  //   if (!selected) return "Status Paparan";
                  //   const option = activeFilterOption.find(
                  //     (opt) => opt.value == selected
                  //   );
                  //   return option ? option.status : "Status Paparan";
                  // }}
                >
                  <MenuItem defaultValue={-1} value={-1}>
                    <em>Status Paparan</em>
                  </MenuItem>
                  {/* {activeFilterOption.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.status}
                          </MenuItem>
                        ))} */}
                </Select>
              </FormControl>
            </Box>
          </Box>
        </Box>
      </LaranganBox>
      <LaranganBox>
        <DataTable
          columns={columns}
          rows={data}
          page={page}
          rowsPerPage={rowsPerPage}
          totalCount={data.length}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          pagination={true}
          paginationType="custom"
          numbered={false}
          sx={{
            width: "100%",
            "& .MuiTableCell-head": {
              // backgroundColor: "#f5f5f5",
              fontWeight: 600,
              fontSize: "14px",
              color: "#666666",
            },
            "& .MuiTableCell-body": {
              fontSize: "14px",
              color: "#666666",
            },
          }}
        />
      </LaranganBox>
    </LaranganPaper>
  );
};
