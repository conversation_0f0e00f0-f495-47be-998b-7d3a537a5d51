import {
  Box,
  Chip,
  FormControl,
  IconButton,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import { LaranganBox } from "./component/LaranganBorder";
import { LaranganPaper } from "./component/LaranganPaper";
import { SearchIcon } from "@/components/icons";
import { FilterList, KeyboardArrowDown } from "@mui/icons-material";
import { useEffect, useState } from "react";
import { Edit as EditIcon, Delete as DeleteIcon } from "@mui/icons-material";
import { DataTable } from "@/components";
import { pageAccessEnum, PermissionNames } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";
import { useNavigate } from "react-router-dom";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { LogoLarangan } from "@/types/larangan/logoLarangan";
import { laranganService } from "@/services/laranganService";
import { useNotification } from "@refinedev/core";
import { t } from "i18next";
import dayjs from "dayjs";
import "./larangan.css";
import DialogActionFlow from "@/components/dialog/confirm/DialogActionFlow";

const sampleData = [
  {
    id: 1,
    logo: "https://doc-dev.ros.gov.my/takwim-banner/20200721_091637.jpg_90f9e44b-7d61-48f9-981d-380e39a05714?versionId=h20firY30aCxOa2c2G2pmwH3Aa5EKAcT",
    tarikhTerbit: "12-4-2025",
    masaTerbit: "3:40 petang",
    catatan: "diharamkan kerana mereka bukan tuhan di antartika",
    status: "aktif",
  },
  {
    id: 2,
    logo: "https://doc-dev.ros.gov.my/takwim-banner/20200721_091637.jpg_90f9e44b-7d61-48f9-981d-380e39a05714?versionId=h20firY30aCxOa2c2G2pmwH3Aa5EKAcT",
    tarikhTerbit: "12-4-2025",
    masaTerbit: "3:40 petang",
    catatan: "diharamkan kerana mereka bukan tuhan di antartika",
    status: "tidak_aktif",
  },
  // ... more data
];

export const SenaraiLogoLarangan = () => {
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState<"edit" | "delete">("edit");
  const [customDialogMessage, setCustomDialogMessage] = useState<string | null>(
    null
  );
  const [isLoadingSenarai, setIsLoadingSenarai] = useState(false);
  const [totalPage, setTotalPage] = useState(0);
  const [senaraiLogoLaranganData, setSenaraiLogoLaranganData] = useState<
    LogoLarangan[]
  >([]);
  const [status, setStatus] = useState<boolean | null>(null);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [data, setData] = useState(sampleData);
  const [searchText, setSearchText] = useState("");
  const [selectedActiveOption, setSelectedActiveOption] = useState("");
  const navigate = useNavigate();
  const { open: openNotification } = useNotification();

  const theme = useTheme();
  const primary = theme.palette.primary.main;

  function checkPermissionAndUserGroup(accessType: number) {
    const hasPermission: boolean = AuthHelper.hasPageAccess(
      PermissionNames.PENGURUSAN_LARANGAN?.label || "PENG-LAR",
      accessType
    );
    if (localStorage.getItem("portal") === "2") {
      return hasPermission && true;
    } else {
      return hasPermission && false;
    }
  }

  useEffect(() => {
    searchLogoByCatatan("");
  }, [page, rowsPerPage, status]);

  const readAccess = checkPermissionAndUserGroup(pageAccessEnum.Read);
  const createAccess = checkPermissionAndUserGroup(pageAccessEnum.Create);
  const deleteAccess = checkPermissionAndUserGroup(pageAccessEnum.Delete);
  const updateAccess = checkPermissionAndUserGroup(pageAccessEnum.Update);
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setRowsPerPage(newPageSize);
  };

  const handleSearch = async (keyword: string) => {
    searchLogoByCatatan(keyword);
  };

  const searchLogoByCatatan = async (catatan: string) => {
    try {
      setIsLoadingSenarai(true);
      const response = await laranganService.searchSenaraiLogo(
        catatan,
        page,
        rowsPerPage,
        status
      );
      setSenaraiLogoLaranganData(response?.data?.data);
      console.log(response.data, "senarai logo larangan");

      setTotalPage(response?.data?.total);
    } catch (error) {
      openNotification?.({
        type: "error",
        message: "Error",
        description: t("Failed to fetch senarai logo larangan"),
      });
      console.error(error);
    } finally {
      setIsLoadingSenarai(false);
    }
  };

  const deleteLogoLarangan = async (id: number) => {
    try {
      const response = await laranganService.deleteLaranganLogo(id);
      if (response.code === 200) {
        openNotification?.({
          type: "success",
          message: "Success",
          description: t("Successfully deleted logo larangan"),
        });
        searchLogoByCatatan("");
      } else {
        openNotification?.({
          type: "error",
          message: "Error",
          description: t("Failed to delete logo larangan"),
        });
      }
    }
    catch (error) {
      openNotification?.({
        type: "error",
        message: "Error",
        description: t("Failed to delete logo larangan"),
      });
    }
  };

  const statusSwicth = (status: string) => {
    setPage(1);
    switch (status) {
      case "all":
        setStatus(null);
        break;
      case "active":
        setStatus(true);
        break;
      case "inactive":
        setStatus(false);
        break;
      default:
        setStatus(null);
        break;
    }
  };

  const handleConfirm = async () => {
    setOpenDialog(false);
  };

  const activeFilterOption = [
    {
      value: "all",
      status: "Semua",
    },
    {
      value: "active",
      status: "Aktif",
    },
    {
      value: "inactive",
      status: "Tidak Aktif",
    },
  ];
  const columns = [
    // {
    //   field: "",
    //   headerName: "No.",
    //   flex: 0.5,
    //   align: "center" as const,
    //   renderCell: ({ rowIndex }: { rowIndex: number }) => {
    //     return <Typography>{rowIndex + 1}</Typography>;
    //   },
    // },
    {
      field: "logo",
      headerName: "Logo",
      flex: 2,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        return (
          // <Image src={row.logo} alt="Logo" width={100} height={100} />
          <img src={row.logoUrl} alt="" className="list-logo" />
        );
      },
    },
    {
      field: "tarikhTerbit",
      headerName: "Tarikh Terbit",
      flex: 1,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        const dateOnlyMY = dayjs(row.createdDate)
          .tz("Asia/Kuala_Lumpur")
          .format("DD-M-YYYY");
        return <Typography sx={{ fontSize: "14px" }}>{dateOnlyMY}</Typography>;
      },
    },
    {
      field: "masaTerbit",
      headerName: "Masa Terbit",
      flex: 1,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        const date = new Date(row.createdDate);
        let hour = date.getHours();
        const minute = date.getMinutes().toString().padStart(2, "0");

        // Determine period of day
        let period;
        if (hour >= 5 && hour < 12) {
          period = "pagi";
        } else if (hour >= 12 && hour < 13) {
          period = "tengah hari";
        } else if (hour >= 13 && hour < 18) {
          period = "petang";
        } else {
          period = "malam";
        }
        hour = hour % 12 || 12;
        const timeMalay = `${hour
          .toString()
          .padStart(2, "0")}:${minute} ${period}`;
        return <Typography sx={{ fontSize: "14px" }}>{timeMalay}</Typography>;
      },
    },
    {
      field: "catatan",
      headerName: "Catatan",
      flex: 3,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        return (
          <Typography sx={{ fontSize: "14px", textAlign: "center" }}>
            {row.remarks}
          </Typography>
        );
      },
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        const isActive = row.active === true;
        return (
          <Chip
            label={isActive ? "Aktif" : "Tidak Aktif"}
            sx={{
              backgroundColor: "inherit",
              border: `2px solid ${isActive ? "#00B69B" : "#F44336"}`,
              height: "28px",
              width: "92px",
              fontSize: "10px",
            }}
          />
        );
      },
    },
    {
      field: "tindakan",
      headerName: "Tindakan",
      flex: 1,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        return (
          <Box sx={{ display: "flex", gap: 1, justifyContent: "center" }}>
            {updateAccess && (
              <IconButton
                size="small"
                onClick={() =>
                  navigate(
                    `/penguatkuasaan/daftar_nama_larangan/tambah_rekod?id=${row.id}&tab=logo&mode=edit`
                  )
                }
                sx={{
                  color: "var(--primary-color)",
                  "&:hover": { backgroundColor: "rgba(12, 166, 166, 0.1)" },
                }}
              >
                <EditIcon sx={{ fontSize: "18px" }} />
              </IconButton>
            )}
            {deleteAccess && (
              <IconButton
                size="small"
                onClick={() => {
                  setOpenDialog(true);
                  setCustomDialogMessage(
                    "Adakah anda pasti untuk memadamkan nama larangan ini?"
                  );
                  setDialogType("delete");
                }}
                sx={{
                  color: "#F44336",
                  "&:hover": { backgroundColor: "rgba(244, 67, 54, 0.1)" },
                }}
              >
                <DeleteIcon sx={{ fontSize: "18px" }} />
              </IconButton>
            )}

            <IconButton
              size="small"
              onClick={() =>
                navigate(
                  `/penguatkuasaan/daftar_nama_larangan/tambah_rekod?id=${row.id}&tab=logo&mode=view`
                )
              }
              sx={{
                color: "var(--primary-color)",
                "&:hover": { backgroundColor: "rgba(12, 166, 166, 0.1)" },
              }}
            >
              <VisibilityIcon sx={{ fontSize: "18px" }} />
            </IconButton>
          </Box>
        );
      },
    },
  ];
  return (
    <>
      <LaranganPaper>
        <LaranganBox>
          <Typography
            color={primary}
            sx={{
              fontSize: 14,
              fontWeight: "medium",
              marginBottom: "1.5rem",
            }}
          >
            Logo Terlarang
          </Typography>
          <Box
            sx={{
              mr: "auto",
              ml: "auto",
              width: "70%",
              display: "flex",
              flexDirection: "column",
              // gap: 1,
            }}
          >
            <Box sx={{ mb: 2, width: "100%" }}>
              <TextField
                placeholder="Nama Acara"
                variant="outlined"
                fullWidth
                size="small"
                value={searchText}
                onChange={(e) => {
                  setSearchText(e.target.value);
                  handleSearch(e.target.value);
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon sx={{ color: "#9CA3AF" }} />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "10px",
                    "& fieldset": {
                      borderColor: "#E5E7EB",
                    },
                    "&:hover fieldset": {
                      borderColor: "#E5E7EB",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "#E5E7EB",
                    },
                  },
                }}
              />
            </Box>
            <Box
              sx={{
                // border: "1px solid #E5E7EB",
                borderRadius: "30px",
                // padding: "30px 20px 20px 20px",
                // boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  gap: 2,
                  justifyContent: "center",
                  ml: "auto",
                  mr: "auto",
                  width: "70%",
                  borderRadius: "20px",
                  boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
                  p: "3px",
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    color: "#6B7280",
                    borderRight: "1px solid #E5E7EB",
                    pr: 2,
                    height: "40px",
                  }}
                >
                  <FilterList sx={{ fontSize: 20, mr: 1 }} />
                  <Typography variant="body2">Tapis Berdasarkan</Typography>
                </Box>
                <FormControl
                  size="small"
                  sx={{
                    minWidth: 120,
                    borderRight: "1px solid #E5E7EB",
                    height: "40px",
                    // paddingRight: 2,
                    // marginRight: 2
                  }}
                >
                  <Select
                    displayEmpty
                    placeholder="Status Paparan"
                    value={selectedActiveOption || ""}
                    onChange={(e) => {
                      setSelectedActiveOption(e.target.value);
                      statusSwicth(e.target.value);
                      // handleActiveOptionChange(e.target.value);
                    }}
                    IconComponent={(props) => (
                      <KeyboardArrowDown {...props} sx={{ marginLeft: 1 }} />
                    )}
                    sx={{
                      border: "none",
                      "& .MuiOutlinedInput-notchedOutline": {
                        border: "none",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        border: "none",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        border: "none",
                      },
                      "& .MuiSelect-select": {
                        paddingRight: "32px", // Make room for the dropdown icon
                        fontWeight: 400, // Normal font weight instead of bold
                        fontSize: "14px",
                        color: "#6B7280",
                      },
                    }}
                    renderValue={(selected) => {
                      if (!selected) return "Status Paparan";
                      const option = activeFilterOption.find(
                        (opt) => opt.value == selected
                      );
                      return option ? option.status : "Status Paparan";
                    }}
                  >
                    {/* <MenuItem defaultValue={-1} value={-1}>
                    <em>Status Paparan</em>
                  </MenuItem> */}
                    {activeFilterOption.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.status}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Box>
          </Box>
        </LaranganBox>
        <LaranganBox>
          <DataTable
            columns={columns}
            rows={senaraiLogoLaranganData}
            page={page}
            rowsPerPage={rowsPerPage}
            totalCount={data.length}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            pagination={true}
            paginationType="custom"
            numbered={true}
            isLoading={isLoadingSenarai}
            sx={{
              width: "100%",
              "& .MuiTableCell-head": {
                // backgroundColor: "#f5f5f5",
                fontWeight: 600,
                fontSize: "14px",
                color: "#666666",
              },
              "& .MuiTableCell-body": {
                fontSize: "14px",
                color: "#666666",
              },
            }}
          />
        </LaranganBox>
      </LaranganPaper>

      <DialogActionFlow
        open={openDialog}
        onClose={() => {
          setOpenDialog(false);
          setCustomDialogMessage(null);
        }}
        onConfirm={customDialogMessage ? async () => {} : handleConfirm}
        hideOnError={false}
        confirmationText={
          customDialogMessage
            ? customDialogMessage
            : "Adakah anda pasti untuk memadamkan nama larangan ini?"
        }
        successMessage={
          customDialogMessage
            ? customDialogMessage
            : "Nama larangan berjaya dipadam"
        }
      />
    </>
  );
};
