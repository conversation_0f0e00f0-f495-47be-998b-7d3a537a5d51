import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { getLocalStorage } from "../../../helpers/utils";
import {
  MALAYSIA,
  NEW_PermissionNames,
  pageAccessEnum,
} from "../../../helpers/enums";

import {
  Box,
  Grid,
  Typography,
  IconButton,
  TextField,
  Select,
  MenuItem,
} from "@mui/material";
import DataTable, { IColumn } from "../../../components/datatable";

import { EditIcon, EyeIcon } from "../../../components/icons";
import AuthHelper from "@/helpers/authHelper";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
  "& span": {
    color: "red",
  },
};

const rows = [
  {
    id: 1,
    namePemohon: "<PERSON><PERSON><PERSON> bin<PERSON>",
    pertubuhan: "Kelab Cakna Sosial Warga Lenggong",
    organizationNumber: "PPM-001-10-05061960",
    tarikhAlir: "17/4/2024",
    tarikhBayar: "17/6/2024",
    applicationType: "Permohonan Setiausaha BWN",
    ro: "Rozita Che wan",
    negeri: "Perak",
  },
  {
    id: 2,
    namePemohon: "Rozita binti Mara",
    pertubuhan: "Kelab Cakna Sosial Warga Lenggong",
    organizationNumber: "PPM-001-10-05061960",
    tarikhAlir: "17/4/2024",
    tarikhBayar: "17/6/2024",
    applicationType: "Permohonan Setiausaha BWN",
    ro: "Rozita Che wan",
    negeri: "Perak",
  },
];

const PembaharuanSetiausahaMigrasiTab: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const currentLanguage = i18n.language;
  const addressList = getLocalStorage("address_list", null);
  const malaysiaList =
    addressList.filter((address: any) => address.pid === MALAYSIA) ?? [];

  const getStateName = (id: string) => {
    return malaysiaList.find((state: any) => state.id === id)?.name || "-";
  };

  const hasUpdatePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_INDUK.children.PEMBAHARUAN_SETIAUSAHA_MIGRASI.label,
    pageAccessEnum.Read
  );

  const columns: IColumn[] = [
    {
      field: "organizationNumber",
      headerName: "No. PPM/NSID",
      flex: 1,
    },
    { field: "namePemohon", headerName: t("namePemohon"), flex: 1 },
    { field: "pertubuhan", headerName: t("pertubuhan"), flex: 1 },
    { field: "tarikhAlir", headerName: t("tarikhAlir"), flex: 1 },
    { field: "tarikhBayar", headerName: t("tarikhBayar"), flex: 1 },
    { field: "ro", headerName: "RO", flex: 1 },
    {
      field: "stateCode",
      headerName: "Negeri",
      flex: 1,
      renderCell: ({ row }: any) => getStateName(row.stateCode),
    },
    {
      field: "actions",
      headerName: t("action"),
      renderCell: ({ row }: any) => (
        <IconButton
          onClick={() =>
            navigate(
              `/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/pembaharuan-setiausaha-migrasi/${row.id}`
            )
          }
          sx={{ padding: 0, display: "block", marginInline: "auto" }}
          disabled={!hasUpdatePermission}
        >
          <EditIcon
            sx={{
              color: hasUpdatePermission
                ? "var(--primary-color)"
                : "var(--text-grey-disabled)",
              width: "1rem",
              height: "1rem",
            }}
          />
        </IconButton>
      ),
    },
  ];

  const categories = getLocalStorage("category_list", []);
  const mainCategories = categories.filter((cat: any) => cat.level === 1) ?? [];
  const subCategories = categories.filter((cat: any) => cat.level === 2) ?? [];

  const mainCategoriesOptions = mainCategories?.map((category: any) => ({
    value: category.id.toString(),
    label: category.categoryNameEn,
  }));
  const subCategoriesOptions = subCategories?.map((category: any) => ({
    value: category.id.toString(),
    label: category.categoryNameEn,
  }));

  const handleChangePage = (newPage: number) => {};

  return (
    <>
      <Box
        sx={{
          padding: "22px 16px",
          background: "#FFF",
          borderRadius: "15px",
          boxShadow: "0px 12px 12px 0px #EAE8E866",
          marginBottom: 1,
        }}
      >
        <Box
          sx={{
            width: "100%",
            border: "0.5px solid #DADADA",
            borderRadius: "10px",
            padding: "22px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {currentLanguage === "my"
              ? "Senarai  pembaharuan setiausaha (migrasi)"
              : "Secretary (migration) renewal list"}
          </Typography>

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>
                  {t("organization_category")}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <Select
                fullWidth
                displayEmpty
                defaultValue=""
                MenuProps={{
                  PaperProps: {
                    sx: {
                      "& .MuiMenuItem-root": {
                        fontSize: "14px",
                      },
                    },
                  },
                }}
                sx={{ height: "37px", fontSize: "14px" }}
              >
                {mainCategoriesOptions.map((item: any) => (
                  <MenuItem key={item.value} value={item.value}>
                    {item.label}
                  </MenuItem>
                ))}
              </Select>
            </Grid>
          </Grid>

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>
                  {t("organizationSubCategory2")}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <Select
                fullWidth
                displayEmpty
                defaultValue=""
                MenuProps={{
                  PaperProps: {
                    sx: {
                      "& .MuiMenuItem-root": {
                        fontSize: "14px",
                      },
                    },
                  },
                }}
                sx={{ height: "37px", fontSize: "14px" }}
              >
                {subCategoriesOptions.map((item: any) => (
                  <MenuItem key={item.value} value={item.value}>
                    {item.label}
                  </MenuItem>
                ))}
              </Select>
            </Grid>
          </Grid>

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t("namaPertubuhan")}</Typography>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <TextField
                fullWidth
                sx={{
                  backgroundColor: "#FFF",
                  "& .MuiInputBase-input": { fontSize: "14px" },
                }}
                size="small"
              />
            </Grid>
          </Grid>
        </Box>
      </Box>

      <Box
        sx={{
          padding: "22px 16px",
          background: "#FFF",
          borderRadius: "15px",
          boxShadow: "0px 12px 12px 0px #EAE8E866",
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: "13px",
            padding: "15px 0",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            marginBottom: 1,
            backgroundColor: "var(--primary-color)",
          }}
        >
          <Typography
            fontWeight="500 !important"
            fontSize="36px"
            color="#FFF"
            textAlign="center"
            lineHeight="30px"
            sx={{
              "& span": {
                fontSize: "20px",
              },
            }}
          >
            123 <br />
            <span>
              {currentLanguage === "my"
                ? "Permohonan pembaharuan setiausaha migrasi"
                : "Migration secretary renewal application"}
            </span>
          </Typography>
        </Box>
        <Box
          sx={{
            width: "100%",
            border: "0.5px solid #DADADA",
            borderRadius: "10px",
            padding: "22px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="37px"
          >
            {currentLanguage === "my"
              ? "Senarai pembaharuan setiausaha"
              : "Secretary renewal list"}
          </Typography>

          <DataTable
            columns={columns}
            rows={rows}
            page={1}
            rowsPerPage={10}
            totalCount={rows.length}
            onPageChange={handleChangePage}
          />
        </Box>
      </Box>
    </>
  );
};

export default PembaharuanSetiausahaMigrasiTab;
