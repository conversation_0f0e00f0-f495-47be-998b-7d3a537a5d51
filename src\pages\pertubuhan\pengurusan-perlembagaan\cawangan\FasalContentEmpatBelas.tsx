import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengu<PERSON>an-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import { ConstitutionType, RegExNumbers } from "@/helpers";
import AsalContent from "@/components/asalFasalContent";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
interface FasalContentEmpatBelasCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: [];
  name: string;
}

export const FasalContentEmpatBelasCawangan: React.FC<
  FasalContentEmpatBelasCawanganProps
> = ({ activeStep, setActiveStep, clause, asalData, name }) => {
  const [asal, setAsal] = useState<any>(null);
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [minimumJumlahAhliPertubuhan, setMinimumJumlahAhliPertubuhan] =
    useState("");
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);
  const { id, clauseId } = useParams();
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const [isRequiredConstitution, setIsRequiredConstitution] = useState(false);

  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName } = responseData?.data?.data;
        setNamaPertubuhan(societyName);
      },
    },
  });

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause14Data = JSON.parse(clause14);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause14Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause14Data.societyName);
      setMinimumJumlahAhliPertubuhan(
        clause.constitutionValues[0]?.definitionName
      );
      setIsEdit(clause.edit);
      setAsal(
        asalData.find((item: any) => item.clauseNo === clause.clauseNo
        ) || ""
      );
    }
  }, [clause]);

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};
    const number = Number(minimumJumlahAhliPertubuhan);
    const minValue = isRequiredConstitution ? 7 : 0;

    if (!minimumJumlahAhliPertubuhan) {
      errors.minimumJumlahAhliPertubuhan = t("fieldRequired");
    } else if (isRequiredConstitution) {
      if (number < minValue) {
        errors.minimumJumlahAhliPertubuhan = t("validation.minValue", {
          value: minValue,
        });
      }
    }

    return errors;
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<minimum jumlah ahli pertubuhan >>/gi,
    `<b>${
      minimumJumlahAhliPertubuhan || "<<minimum jumlah ahli pertubuhan >>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
      if (
        societyDataRedux?.constitutionType ===
          ConstitutionType.CawanganNGO[1] ||
        societyDataRedux?.constitutionType === ConstitutionType.CawanganAgama[1]
      ) {
        setIsRequiredConstitution(true);
      } else {
        setIsRequiredConstitution(false);
      }
    }
  }, [societyDataRedux]);

  return (
    <>
      <AsalContent
        asalData={asal?.description || ""}
        clauseId={clauseId}
        name={asal?.clauseName}
      />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          background: "#fff",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clause")} {clauseId}
        </Typography>

        <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
          <Typography sx={labelStyle}>
            {t("minimumJumlahAhliPertubuhan")}
          </Typography>
          <TextField
            size="small"
            fullWidth
            required
            disabled={isViewMode}
            value={minimumJumlahAhliPertubuhan}
            onChange={(e) => {
              const value = e.target.value;
              const number = Number(value);
              const minValue = isRequiredConstitution ? 7 : 0;

              if (value === "" || !RegExNumbers.test(value)) {
                setMinimumJumlahAhliPertubuhan(value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  minimumJumlahAhliPertubuhan: t("fieldRequired"),
                }));
                return;
              }

              setMinimumJumlahAhliPertubuhan(value);

              if (number < minValue) {
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  minimumJumlahAhliPertubuhan: t("validation.minValue", {
                    value: minValue,
                  }),
                }));
              } else {
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  minimumJumlahAhliPertubuhan: "",
                }));
              }
            }}
            error={!!formErrors.minimumJumlahAhliPertubuhan}
            helperText={formErrors.minimumJumlahAhliPertubuhan}
            type="number"
            inputProps={{ min: isRequiredConstitution ? 7 : 0 }}
          />
        </Box>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>
      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              const errors = validateForm();
              if (Object.keys(errors).length > 0) {
                setFormErrors(errors);
                return;
              }
              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: minimumJumlahAhliPertubuhan,
                    titleName: "Minimum Jumlah Ahli Pertubuhan",
                  },
                ],
                clause: "clause14",
                clauseCount: 14,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentEmpatBelasCawangan;
