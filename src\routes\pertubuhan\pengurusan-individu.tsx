import { Route, Outlet, Navigate } from "react-router-dom";
import { RouteGuard } from "../../components/RouteGuard";
import { registerRoutes } from "../../helpers/routeDetector";
import Layout from "../../pages/pertubuhan/pengurusan-individu/Layout";
import MaklumatAjk from "../../pages/pertubuhan/pengurusan-individu/maklumat-ajk";
import MaklumatAhli from "../../pages/pertubuhan/pengurusan-individu/maklumat-ahli";
import MaklumatPemegangAmanah from "../../pages/pertubuhan/pengurusan-individu/maklumat-pemegang-amanah";
import MaklumatPegawaiAwam from "../../pages/pertubuhan/pengurusan-individu/maklumat-pegawai-awam";
import MaklumatPegawaiHarta from "../../pages/pertubuhan/pengurusan-individu/maklumat-pegawai-harta";
import MaklumatJuruAudit from "../../pages/pertubuhan/pengurusan-individu/maklumat-juru-audit";
import CreateAjk from "../../pages/pertubuhan/pengurusan-individu/maklumat-ajk/CreateAjk";
import CreateAjkBukanWn from "../../pages/pertubuhan/pengurusan-individu/maklumat-ajk/CreateAjkBukanWn";
import CreateAhli from "../../pages/pertubuhan/pengurusan-individu/maklumat-ahli/CreateAhli";
import CreatePemegangAmanah from "../../pages/pertubuhan/pengurusan-individu/maklumat-pemegang-amanah/CreatePemegangAmanah";
import CreatePegawaiAwam from "../../pages/pertubuhan/pengurusan-individu/maklumat-pegawai-awam/CreatePegawaiAwam";
import CreatePegawaiHarta from "../../pages/pertubuhan/pengurusan-individu/maklumat-pegawai-harta/CreatePegawaiHarta";
import CreateJuruAudit from "../../pages/pertubuhan/pengurusan-individu/maklumat-juru-audit/CreateJuruAudit";

// Layout component to wrap all pengurusan individu routes with protection
const PengurusanIndividuLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Layout>
      <Outlet />
    </Layout>
  </RouteGuard>
);

// Register routes with their portal types
registerRoutes({
  // Add your route registrations here
});

export const pengurusanIndividu = {
  routes: (
    <Route path="pengurusan-individu" element={<PengurusanIndividuLayout />}>
      <Route index element={<Navigate to="maklumat-ajk" />} />

      <Route path="maklumat-ajk">
        <Route index element={<MaklumatAjk />} />
        <Route path="create-ajk" element={<CreateAjk />} />
        <Route path="create-ajk-bukanwn" element={<CreateAjkBukanWn />} />
      </Route>

      <Route path="maklumat-ahli">
        <Route index element={<MaklumatAhli />} />
        <Route path="create-ahli" element={<CreateAhli />} />
      </Route>

      <Route path="maklumat-pemegang-amanah">
        <Route index element={<MaklumatPemegangAmanah />} />
        <Route
          path="create-pemegang-amanah"
          element={<CreatePemegangAmanah />}
        />
      </Route>

      <Route path="maklumat-pegawai-awam">
        <Route index element={<MaklumatPegawaiAwam />} />
        <Route path="create-pegawai-awam" element={<CreatePegawaiAwam />} />
      </Route>

      <Route path="maklumat-pegawai-harta">
        <Route index element={<MaklumatPegawaiHarta />} />
        <Route path="create-pegawai-harta" element={<CreatePegawaiHarta />} />
      </Route>

      <Route path="maklumat-juru-audit">
        <Route index element={<MaklumatJuruAudit />} />
        <Route path="create-juru-audit" element={<CreateJuruAudit />} />
      </Route>
    </Route>
  ),
};
