import { useParams } from "react-router-dom";
import { FieldValues, useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";
import {
  useMutation,
  useQuery,
  omitKeysFromObject,
  NEW_PermissionNames,
  pageAccessEnum,
} from "@/helpers";

import {
  Box,
  Theme,
  Typography,
  useMediaQuery,
  CircularProgress,
} from "@mui/material";
import {
  ButtonPrimary,
  FormFieldRow,
  TextFieldController,
  Label,
  CustomSkeleton,
} from "@/components";
import ButtonPrevious from "@/components/button/ButtonPrevious";
import Input from "@/components/input/Input";

import { IApiResponse } from "@/types";
import { ILookupList } from "@/types";
import AuthHelper from "@/helpers/authHelper";

const Kemaskini = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const hasUpdatePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.SELENGGARA.children.PENYELENGGARAAN_UMUM.children
      .PEKERJAAN.label,
    pageAccessEnum.Update
  );
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const { control, setValue, watch, handleSubmit } = useForm<FieldValues>({
    defaultValues: {
      name: "",
      code: "",
      description: "",
      status: 1,
      createdDate: "",
      modifiedDate: "",
    },
  });

  const { fetch: updateOccupation, isLoading: isUpdatingOccupation } =
    useMutation<IApiResponse<ILookupList>>({
      url: `society/lookup/occupation/${id}/edit`,
      method: "put",
      onSuccess: (res) => {
        const resCode = res.data.code;

        if (resCode === 200) navigate("..");
      },
    });

  const { isLoading: isLoadingOccupationDetail } = useQuery<
    IApiResponse<ILookupList>
  >({
    url: `society/lookup/occupation/${id}`,
    onSuccess: (res) => {
      const resCode = res?.data?.code ?? null;
      const detail = res?.data?.data ?? null;

      if (resCode && resCode === 200) {
        setValue("name", detail?.name);
        setValue("code", detail?.code);
        setValue("description", detail?.description);
        setValue("status", detail?.status ? 1 : 0);
        setValue("createdDate", detail?.createdDate);
        setValue("modifiedDate", detail?.modifiedDate);
      }
    },
  });

  const onSubmit = (data: FieldValues) => {
    const keysToSkip = ["createdDate", "modifiedDate"];
    const payload = omitKeysFromObject(data, keysToSkip);

    updateOccupation(payload);
  };

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography sx={{ mb: 2 }} className="title">
            {t("penambahanPekerjaan")}
          </Typography>

          {isLoadingOccupationDetail ? (
            <CustomSkeleton height={50} />
          ) : (
            <Box
              component="form"
              onSubmit={handleSubmit(onSubmit)}
              sx={{ display: "grid" }}
            >
              <FormFieldRow
                label={<Label text={t("kodPekerjaan")} />}
                value={
                  <TextFieldController
                    disabled={!hasUpdatePermission}
                    control={control}
                    name="code"
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("namaPekerjaan")} />}
                value={
                  <TextFieldController
                    disabled={!hasUpdatePermission}
                    control={control}
                    name="name"
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("keteranganPekerjaan")} />}
                value={
                  <TextFieldController
                    disabled={!hasUpdatePermission}
                    control={control}
                    name="description"
                  />
                }
              />

              <Input
                type="radio"
                label={t("status")}
                value={watch("status")}
                disabled={!hasUpdatePermission}
                options={[
                  { value: 1, label: t("active") },
                  { value: 0, label: t("inactive") },
                ]}
                onChange={(e) => {
                  const value = e.target.value;
                  setValue("status", Number(value));
                }}
              />

              <FormFieldRow
                label={<Label text={t("wujudDaftar")} />}
                value={
                  <TextFieldController
                    control={control}
                    name="createdDate"
                    disabled
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("kemaskiniTerakhir")} />}
                value={
                  <TextFieldController
                    control={control}
                    name="modifiedDate"
                    disabled
                  />
                }
              />

              <Box
                sx={{
                  mt: 2,
                  display: "flex",
                  flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrevious
                  variant="outlined"
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    width: isMobile ? "100%" : "auto",
                  }}
                  onClick={() => navigate(-1)}
                >
                  {t("back")}
                </ButtonPrevious>
                {hasUpdatePermission ? (
                  <ButtonPrimary
                    type="submit"
                    disabled={isUpdatingOccupation}
                    variant="contained"
                    sx={{
                      width: isMobile ? "100%" : "auto",
                      display: "flex",
                      alignItems: "center",
                      gap: "5px",
                    }}
                  >
                    {isUpdatingOccupation && <CircularProgress size={15} />}
                    {t("kemaskini")}
                  </ButtonPrimary>
                ) : null}
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default Kemaskini;
