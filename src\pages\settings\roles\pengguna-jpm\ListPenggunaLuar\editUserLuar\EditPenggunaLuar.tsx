import {
  Box,
  FormControlLabel,
  FormHelperText,
  Grid,
  Radio,
  RadioGroup,
  styled,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import {
  ButtonOutline,
  ButtonPrimary,
} from "../../../../../../components/button";
import { useEffect, useState } from "react";
import {
  IdTypes,
  ListGelaran,
  MALAYSIA,
} from "../../../../../../helpers/enums";
import Input from "../../../../../../components/input/Input";
import { API_URL } from "../../../../../../api";
import { useNavigate, useSearchParams } from "react-router-dom";
import { getLocalStorage } from "../../../../../../helpers/utils";
import { useCustomMutation } from "@refinedev/core";

interface FormValues {
  userId?: number | null;
  titleCode?: string | null;
  name?: string | null;
  email?: string | null;
  identificationType?: string | null;
  identificationNo?: string | null;
  citizenship?: boolean | null;
  address?: string | null;
  stateCode?: number | null;
  districtCode?: number | null;
  city?: string | null;
  postcode?: string | null;
  homePhone?: string | null;
  mobilePhone?: string | null;
}

interface ListItem {
  value: any;
  label: any;
}

interface UserData {
  userId?: number | null;
  titleCode?: string | null;
  name?: string | null;
  email?: string | null;
  identificationType?: string | null;
  identificationNo?: string | null;
  citizenship?: boolean | null;
  address?: string | null;
  stateCode?: number | null;
  districtCode?: number | null;
  city?: string | null;
  postcode?: string | null;
  homePhone?: string | null;
  mobilePhone?: string | null;
}

function EditPenggunaLuar() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const userId = searchParams.get("id");
  const viewType = searchParams.get("type");

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const [formValues, setFormValues] = useState<FormValues>({
    titleCode: null,
    name: null,
    email: null,
    identificationType: null,
    identificationNo: null,
    citizenship: null,
    address: null,
    stateCode: null,
    districtCode: null,
    city: null,
    postcode: null,
    homePhone: null,
    mobilePhone: null,
  });
  const [errors, setErrors] = useState<{ [key in keyof FormValues]?: string }>(
    {}
  );

  const addressList = getLocalStorage("address_list", null);

  const [districtList, setDistrictList] = useState<ListItem[]>([]);
  const StateList = addressList
    .filter((item: any) => item.pid === MALAYSIA)
    .map((item: any) => ({ value: item.id, label: item.name }));

  const { mutate: editExternalUser, isLoading: isLoadingRegister } =
    useCustomMutation();

  useEffect(() => {
    if (formValues.stateCode) {
      setDistrictList(
        addressList
          .filter((item: any) => item.pid === formValues.stateCode)
          .map((item: any) => ({ value: item.id, label: item.name }))
      );
    }
  }, [formValues?.stateCode]);

  const [userData, setUserData] = useState<UserData>();
  const [loading, setLoading] = useState(true);
  // /society/admin/branch/list

  const CustomRadio = styled(Radio)(({ theme }) => ({
    "& .MuiSvgIcon-root": {
      borderRadius: "2px",
      width: 16,
      height: 16,
    },
    // Styles for unchecked stateCode
    "& .MuiSvgIcon-root:first-of-type": {
      border: "2px solid #979797",
      background: "white",
    },
    // Styles for checked stateCode
    "& .MuiSvgIcon-root:last-child": {
      border: "2px solid #1976d2",
      background: "#1976d2",
      "&:before": {
        display: "block",
        width: 16,
        height: 16,
        backgroundImage: "radial-gradient(#fff,#fff 28%,transparent 32%)",
        content: '""',
      },
    },
  }));

  const CheckboxIcon = () => (
    <svg width="16" height="16" viewBox="0 0 16 16">
      <rect
        width="16"
        height="16"
        fill="none"
        stroke="#979797"
        strokeWidth="1"
        rx="3"
        ry="3"
      />

      <rect x="2" y="2" width="12" height="12" fill="#979797" />
    </svg>
  );

  const CheckedIcon = () => (
    <svg width="16" height="16" viewBox="0 0 16 16">
      <rect
        width="16"
        height="16"
        fill="none"
        stroke="#979797"
        strokeWidth="1"
        rx="3"
        ry="3"
      />
      <rect x="2" y="2" width="12" height="12" fill="currentColor" />
    </svg>
  );

  useEffect(() => {
    if (userId) {
      const fetchData = async () => {
        try {
          const response = await fetch(
            `${API_URL}/user/admin/external/${userId}`,
            {
              method: "GET",
              headers: {
                portal: localStorage.getItem("portal") || "",
                Authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
                "Content-Type": "application/json",
              },
            }
          );
          if (!response.ok) {
            throw new Error("Failed to fetch data");
          }
          const result = await response.json();
          const data = result?.data;
          setFormValues({
            titleCode: data?.titleCode,
            name: data?.name,
            email: data?.email,
            identificationType: data?.identificationType,
            identificationNo: data?.identificationNo,
            citizenship: data?.citizenship,
            address: data?.address,
            stateCode: Number(data?.stateCode),
            districtCode: Number(data?.districtCode),
            city: data?.city,
            postcode: data?.postcode,
            homePhone: data?.homePhone,
            mobilePhone: data?.mobilePhone,
          });
          setUserData(data);
        } catch (error) {
          console.error("Error fetching data:", error);
        } finally {
          setLoading(false);
        }
      };
      fetchData();
    }
  }, [userId]);

  const EditUser: () => void = () => {
    editExternalUser(
      {
        url: `${API_URL}/user/admin/updateExternalUser`,
        method: "put",
        values: {
          id: userId,
          titleCode: formValues.titleCode,
          name: formValues.name,
          email: formValues.email,
          address: formValues.address,
          state: formValues.stateCode,
          district: formValues.districtCode,
          city: formValues.city,
          postcode: formValues.postcode,
          homePhone: formValues.homePhone,
          mobilePhone: formValues.mobilePhone,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          setErrors({});
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newErrors: { [key in keyof FormValues]?: string } = {};

    if (!formValues.name) {
      newErrors.name = t("requiredField");
    }
    if (!formValues.email) {
      newErrors.email = t("requiredField");
    }

    if (!formValues.address) {
      newErrors.address = t("requiredField");
    }
    if (!formValues.stateCode) {
      newErrors.stateCode = t("requiredField");
    }
    if (!formValues.districtCode) {
      newErrors.districtCode = t("requiredField");
    }
    if (!formValues.city) {
      newErrors.city = t("requiredField");
    }
    if (!formValues.postcode) {
      newErrors.postcode = t("requiredField");
    }

    if (Object.keys(newErrors).length === 0) {
      EditUser();
    } else {
      setErrors(newErrors);
    }
  };

  const handleChange = (e: any) => {
    const { name, value } = e.target;

    if (name === "postcode") {
      if (!/^\d*$/.test(value)) {
        return;
      }

      setFormValues({
        ...formValues,
        [name!]: value,
      });

      if (value.length > 0 && value.length < 5) {
        setErrors({
          ...errors,
          [name!]: "Postcode must be exactly 5 digits",
        });
      } else {
        setErrors({
          ...errors,
          [name!]: "",
        });
      }
      return;
    }

    setFormValues({
      ...formValues,
      [name!]: value as string,
    });
  };

  const clearForm = (e: any) => {
    const currentValues = formValues;

    setFormValues({
      identificationType: currentValues?.identificationType,
      identificationNo: currentValues?.identificationNo,
      citizenship: currentValues?.citizenship,
    });
  };

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          component="form"
          onSubmit={handleSubmit}
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Box sx={{ mb: 6 }}>
            <Typography sx={{ mb: 4 }} className={"title"}>
              {t("externalUserupdate")}
            </Typography>

            <Input
              value={formValues.titleCode ? formValues.titleCode : ""}
              name="titleCode"
              disabled={viewType ? true : false}
              onChange={handleChange}
              label={t("gelaran")}
              options={ListGelaran.map((item) => ({
                ...item,
                label: t(item.label),
              }))}
              type="select"
              error={!!errors.titleCode}
              helperText={errors.titleCode}
            />

            <Input
              value={formValues.name ? formValues.name : ""}
              name="name"
              disabled={viewType ? true : false}
              onChange={handleChange}
              required
              label={t("fullName")}
              error={!!errors.name}
              helperText={errors.name}
            />

            <Input
              value={formValues.email ? formValues.email : ""}
              name="email"
              disabled={viewType ? true : false}
              onChange={handleChange}
              required
              label={t("email")}
              type="email"
              error={!!errors.email}
              helperText={errors.email}
            />
            <Grid container spacing={2} sx={{ pt: 2, pb: 2 }}>
              <Grid
                item
                xs={12}
                sm={4}
                sx={{ display: "flex", alignItems: "center" }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    color: "#666666",
                    fontWeight: "400 !important",
                    fontSize: "14px",
                  }}
                >
                  {t("citizenship")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <RadioGroup
                  value={
                    formValues?.identificationType?.toString() === "1"
                      ? "1"
                      : "2"
                  }
                  onChange={handleChange}
                  name="citizenship"
                  row
                >
                  <FormControlLabel
                    value="1"
                    className="label"
                    control={<Radio />}
                    disabled
                    label={t("citizen")}
                  />
                  <FormControlLabel
                    value="2"
                    className="label"
                    control={<Radio />}
                    disabled
                    label={t("nonCitizen")}
                  />
                </RadioGroup>
                {errors.citizenship && (
                  <FormHelperText error>{errors.citizenship}</FormHelperText>
                )}
              </Grid>
            </Grid>

            <Input
              value={
                formValues.identificationType
                  ? formValues.identificationType.toString()
                  : ""
              }
              name="identificationType"
              disabled
              onChange={handleChange}
              required
              label={t("idType")}
              options={IdTypes.map((item) => ({
                ...item,
                label: t(item.label),
              }))}
              type="select"
              error={!!errors.identificationType}
              helperText={errors.identificationType}
            />

            <Input
              value={
                formValues.identificationNo ? formValues.identificationNo : ""
              }
              name="identificationNo"
              disabled
              onChange={handleChange}
              required
              label={t("idNumberPlaceholder")}
              error={!!errors.identificationNo}
              helperText={errors.identificationNo}
            />
          </Box>
          {/* SECTION 2 */}
          <Box sx={{ mb: 6 }}>
            <Input
              value={formValues.address ? formValues.address : ""}
              name="address"
              disabled={viewType ? true : false}
              onChange={handleChange}
              required
              label={t("address")}
              error={!!errors.address}
              helperText={errors.address}
              multiline
              rows={3}
            />
            <Input
              value={formValues.stateCode ? Number(formValues.stateCode) : ""}
              name="stateCode"
              disabled={viewType ? true : false}
              onChange={handleChange}
              required
              options={StateList}
              type="select"
              label={t("negeri")}
              error={!!errors.stateCode}
              helperText={errors.stateCode}
            />
            <Input
              value={
                formValues.districtCode ? Number(formValues.districtCode) : ""
              }
              name="districtCode"
              onChange={handleChange}
              required
              label={t("daerah")}
              type="select"
              disabled={!formValues?.stateCode || viewType ? true : false}
              options={districtList ? districtList : []}
              error={!!errors.districtCode}
              helperText={errors.districtCode}
            />
            <Input
              value={formValues.city ? formValues.city : ""}
              name="city"
              disabled={viewType ? true : false}
              onChange={handleChange}
              required
              label={t("city")}
              error={!!errors.city}
              helperText={errors.city}
            />
            <Input
              value={formValues.postcode ? formValues.postcode : ""}
              name="postcode"
              disabled={viewType ? true : false}
              onChange={(e) => {
                if (/^\d{0,5}$/.test(e.target.value)) {
                  handleChange(e);
                }
              }}
              required
              label={t("postcode")}
              error={!!errors.postcode}
              helperText={errors.postcode}
            />
          </Box>

          {/* SECTION 3 */}
          <Box sx={{ mb: 6 }}>
            <Input
              value={formValues.homePhone ? formValues.homePhone : ""}
              name="homePhone"
              disabled={viewType ? true : false}
              onChange={handleChange}
              label={t("telephoneNoHome")}
              error={!!errors.homePhone}
              helperText={errors.homePhone}
            />
            <Input
              value={formValues.mobilePhone ? formValues.mobilePhone : ""}
              name="mobilePhone"
              disabled={viewType ? true : false}
              onChange={handleChange}
              label={t("telephoneNoMobile")}
              error={!!errors.mobilePhone}
              helperText={errors.mobilePhone}
            />
          </Box>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            {viewType ? (
              <>
                <ButtonOutline onClick={() => navigate(-1)}>
                  {t("back")}
                </ButtonOutline>
              </>
            ) : (
              <>
                <ButtonOutline onClick={() => clearForm(-1)}>
                  {t("previous")}
                </ButtonOutline>
                <ButtonPrimary type="submit">{t("update")}</ButtonPrimary>
              </>
            )}
          </Grid>
        </Box>
      </Box>
    </Box>
  );
}

export default EditPenggunaLuar;
