import React, {useEffect, useState} from "react";
import {DurationIcon} from "@/components/icons/duration";
import {Box, Typography} from "@mui/material";

interface CountdownProps{
  initialTime: number,
  handleFinish: () => void;
  handleTimeChange: (val: number) => void;
  attemptId: string;
}

const Countdown: React.FC<CountdownProps> = ({initialTime, handleFinish, handleTimeChange, attemptId}) => {

  const [timeLeft, setTimeLeft] = useState(initialTime);

  useEffect(() => {
    const intervalId = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 0) {
          clearInterval(intervalId);
          handleFinish();
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => {
      clearInterval(intervalId);
    }
  }, [attemptId]);

  useEffect(() => {
    handleTimeChange(timeLeft);
  },[timeLeft])

  const hours = Math.floor(timeLeft / 3600);
  const minutes = Math.floor((timeLeft % 3600) / 60);
  const seconds = timeLeft % 60;

  return (
    <>
      <Box sx={{display: "flex", justifyContent: "space-between", gap: 1}}>
        <DurationIcon />
        <Typography
          sx={{
            color: "#0CA6A6",
            //pt: 3,
            //lineHeight: 18,
            fontWeight: "500",
            fontSize: 12,
          }}
        >
          {`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`}
        </Typography>
      </Box>
    </>
  );
}

export default Countdown;
