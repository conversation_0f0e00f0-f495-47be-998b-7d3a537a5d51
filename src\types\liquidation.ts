import { IFeedback, IReview } from "./approval";

export interface ILiquidationFeedbackPaging {
  liquidationId: number | null;
  transferDate: string | null;
  submissionDate: string | null;
  decisionDate: string | null;
  applicationStatusCode: number | null;
  secretaryName: string | null;
}

export interface ILiquidationApprovalPaging {
  liquidationId: string;
  societyName: string;
  societyNo: string;
  ro: string;
  transferDate: string;
  submissionDate: string;
  stateCode: string;
  applicationStatusCode: number;
}

export interface IAsset {
  id: number;
  assetType: string;
  assetValue: number;
  donation: number;
  liability: number;
  balance: number;
  other: number;
  otherReason?: string;
}

export interface ILiquidationApproval {
  id: number
  societyId: number
  societyNo: string
  branchId: number
  branchNo: string
  liquidationDocumentType: number
  meetingId: number
  meetingDate: string
  committeeVoteCount: number
  committeeAttendCount: number
  committeeAgreeCount: number
  totalFixedAssets: any
  cashInHand: any
  cashInBank: any
  totalAsset: any
  shortTermLiability: any
  longTermLiability: any
  totalLiability: any
  totalFixedAssetsFinance: any
  cashInHandFinance: any
  cashInBankFinance: any
  totalAssetFinance: any
  shortTermLiabilityFinance: any
  longTermLiabilityFinance: any
  totalLiabilityFinance: any
  applicantName: any
  submissionDate: string
  decisionDate: any
  applicationStatusCode: number
  ro: string | number;
  branchLiquidation: number
  notePpp: string
  transferDate: any
  noteRo: string
  status: any
  createdBy: number
  createdDate: string
  modifiedBy: number
  modifiedDate: string
  secretaryName: string
  secretaryContactNo: string
  roName: string
  assets: IAsset[]
  feedbacks: IFeedback[]
  userRo: boolean;
  reviews: IReview[];
  roDecisionCode?: number;
  roDecisionNote?: string;
}

export interface ILiquidationFeedback {
  id: number;
  meetingId: number;
  societyName: string;
  societyId: number;
  societyNo: string;
  committeeVoteCount: number;
  committeeAttendCount: number;
  committeeAgreeCount: number;
  applicationStatusCode: number;
  committeeName: string;
  committeeDesignationCode: string;
  committeeIdentificationNo: string;
  committeeNationalityStatus: string;
  assets: IAsset[];
  liquidationDocumentType: number;
}
