import { IFeedback, IReview } from "./approval";

export interface ILiquidationFeedbackPaging {
  liquidationId: string | number | null;
  transferDate: string | null;
  submissionDate: string | null;
  decisionDate: string | null;
  applicationStatusCode: number | null;
  secretaryName: string | null;
}

export interface ILiquidationApprovalPaging {
  liquidationId: string;
  societyName: string;
  societyNo: string;
  ro: string;
  transferDate: string;
  submissionDate: string;
  stateCode: string;
  applicationStatusCode: number;
}

export interface IAsset {
  id: string | number;
  assetType: string;
  assetValue: number;
  donation: number;
  liability: number;
  balance: number;
  other: number;
  otherReason?: string;
}

export interface ILiquidationApproval {
  id: string | number
  societyId: string | number
  societyNo: string
  branchId: string | number
  branchNo: string | number
  liquidationDocumentType: number
  meetingId: string | number
  meetingDate: string
  committeeVoteCount: number
  committeeAttendCount: number
  committeeAgreeCount: number
  totalFixedAssets: any
  cashInHand: any
  cashInBank: any
  totalAsset: any
  shortTermLiability: any
  longTermLiability: any
  totalLiability: any
  totalFixedAssetsFinance: any
  cashInHandFinance: any
  cashInBankFinance: any
  totalAssetFinance: any
  shortTermLiabilityFinance: any
  longTermLiabilityFinance: any
  totalLiabilityFinance: any
  applicantName: any
  submissionDate: string
  decisionDate: any
  applicationStatusCode: number
  ro: string | number;
  branchLiquidation: number
  notePpp: string
  transferDate: any
  noteRo: string
  status: any
  createdBy: string | number
  createdDate: string
  modifiedBy: string | number
  modifiedDate: string
  secretaryName: string
  secretaryContactNo: string
  roName: string
  assets: IAsset[]
  feedbacks: IFeedback[]
  userRo: boolean;
  reviews: IReview[];
  roDecisionCode?: number;
  roDecisionNote?: string;
  statementId: string | number;
}

export interface ILiquidationFeedback {
  id: string | number;
  meetingId: string | number;
  societyName: string;
  societyId: string | number;
  societyNo: string;
  committeeVoteCount: number;
  committeeAttendCount: number;
  committeeAgreeCount: number;
  applicationStatusCode: number;
  committeeName: string;
  committeeDesignationCode: string;
  committeeIdentificationNo: string;
  committeeNationalityStatus: string;
  assets: IAsset[];
  liquidationDocumentType: number;
}
