import React, {useState} from "react";
import {Typography} from "@mui/material";
import Box from "@mui/material/Box";
import CustomDataGrid from "@/components/datagrid";
import {GridColDef} from "@mui/x-data-grid";
import {useNavigate} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {API_URL} from "@/api";
import {formatDate} from "@/helpers";


const CertificateModule: React.FC = () => {
  const {t, i18n} = useTranslation();
  const navigate = useNavigate();

  const [refetchData, setRefetchData] = useState(false);

  const columns: GridColDef[] = [
    {
      field: "title",
      headerName: t("titleFeedback"),
      flex: 1,
      renderCell: ({row}) => {
        return  <Typography
          sx={{
            color: "#666666",
            fontWeight: "400",
            fontSize: 14,
            cursor:"pointer"
          }}
          onClick={() => navigate("sijil/detail", {state:{enrollId:row?.trainingEnrollmentId, courseId:row?.trainingId}})}
        >
          {row?.trainingTitle}
        </Typography>;
      },
    },
    {
      field: "dateFinished",
      headerName: "Tarikh Kursus Disiapkan",
      flex: 1,
      renderCell: ({row}) => {
        const formattedDate = formatDate(row?.issueDate)
        return formattedDate;
      },
    },
    {
      field: "type",
      headerName: "Jenis Aktiviti",
      flex: 1,
      renderCell: ({row}) => {
        return row?.type;
      },
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      renderCell: ({row}) => {
        return row?.status;
      },
    },
    {
      field: "action",
      headerName: t("action"),
      flex: 1,
      renderCell: ({row}) => {
        return  <Typography
          sx={{
            color: "#666666",
            fontWeight: "400",
            fontSize: 14,
            cursor:"pointer"
          }}
          onClick={() => navigate("sijil", {state:{enrollId:row?.trainingEnrollmentId, courseId:row?.trainingId}})}
        >
          Lihat Sijil
        </Typography>;
      },
    },
  ]

  return (
    <>
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //flex: 5,
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              pt: 3,
              fontWeight: "500",
              fontSize: 14,
            }}
          >
            Carian Latihan
          </Typography>
          <Box>
            <CustomDataGrid
              url={`${API_URL}/society/training/certificates`}
              columns={columns}
              noResultMessage={t("noData")}
              isFiltered
              type={1}
              setRefetchData={setRefetchData}
              refetchData={refetchData}
            />
          </Box>
        </Box>
      </Box>
    </>
  );
}

export default CertificateModule;
