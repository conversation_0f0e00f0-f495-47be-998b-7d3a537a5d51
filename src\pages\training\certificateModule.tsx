import React, {useEffect, useState} from "react";
import {Grid, Typography} from "@mui/material";
import Box from "@mui/material/Box";
import CustomDataGrid from "@/components/datagrid";
import {GridColDef} from "@mui/x-data-grid";
import {useNavigate} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {API_URL} from "@/api";
import {formatDate, TrainingEnums} from "@/helpers";
import {useCustom} from "@refinedev/core";
import TrainingFragment from "@/pages/training/trainingFragment";
import CertificateFragment from "@/pages/training/certificateDetails/certificateFragment";


export interface CertificateModuleProps {
  enrolledTraining: any[]
}

const CertificateModule: React.FC<CertificateModuleProps> = ({enrolledTraining}) => {
  const {t, i18n} = useTranslation();
  const navigate = useNavigate();

  const columns: GridColDef[] = [
    {
      field: "title",
      headerName: t("titleFeedback"),
      flex: 1,
      renderCell: ({row}) => {
        return  <Typography
          sx={{
            color: "#666666",
            fontWeight: "400",
            fontSize: 14,
            cursor:"pointer"
          }}
          onClick={() => navigate("sijil/detail", {state:{enrollId:row?.trainingEnrollmentId, courseId:row?.trainingId}})}
        >
          {row?.trainingTitle}
        </Typography>;
      },
    },
    {
      field: "dateFinished",
      headerName: "Tarikh Kursus Disiapkan",
      flex: 1,
      renderCell: ({row}) => {
        const formattedDate = formatDate(row?.issueDate)
        return formattedDate;
      },
    },
    {
      field: "type",
      headerName: "Jenis Aktiviti",
      flex: 1,
      renderCell: ({row}) => {
        return row?.type;
      },
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      renderCell: ({row}) => {
        return row?.status;
      },
    },
    {
      field: "action",
      headerName: t("action"),
      flex: 1,
      renderCell: ({row}) => {
        return  <Typography
          sx={{
            color: "#666666",
            fontWeight: "400",
            fontSize: 14,
            cursor:"pointer"
          }}
          onClick={() => navigate("sijil", {state:{enrollId:row?.trainingEnrollmentId, courseId:row?.trainingId}})}
        >
          Lihat Sijil
        </Typography>;
      },
    },
  ]

  const { data: certificateData, isLoading: isCertificateLoading } = useCustom({
    url: `${API_URL}/society/training/certificates`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const certificates = certificateData?.data?.data || [];
  //console.log("certificateData", certificates)
  const filteredEnrollment = enrolledTraining.filter((t) => (t.completionStatus === "COMPLETED" || t.completionStatus === "FAILED"));

  return (
    <>
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#0CA6A6",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box sx={{justifyItems:"center"}}>
          <Typography
            sx={{
              color: "#FFFFFF",
              pt: 1,
              fontWeight: "500",
              fontSize: 36,
            }}
          >
            {`${certificates.length}`}
          </Typography>
          <Typography
            sx={{
              color: "#FFFFFF",
              //pt: 3,
              fontWeight: "500",
              fontSize: 20,
            }}
          >
            {`${t("totalCertification")}`}
          </Typography>
        </Box>
      </Box>
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //flex: 5,
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
            pb: 5
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              pt: 3,
              fontWeight: "500",
              fontSize: 14,
            }}
          >
            {t("myTraining")}
          </Typography>
          <Box>
            <Grid container spacing={2}>
              {filteredEnrollment.map((item: any, index: number) => (
                <Grid key={index} item xs={3}>
                  <CertificateFragment item={item} />
                </Grid>
              ))}
            </Grid>
            {/*<CustomDataGrid
              url={`${API_URL}/society/training/certificates`}
              columns={columns}
              noResultMessage={t("noData")}
              isFiltered
              type={1}
              setRefetchData={setRefetchData}
              refetchData={refetchData}
            />*/}

          </Box>
        </Box>
      </Box>
    </>
  );
}

export default CertificateModule;
