import { Route, Outlet } from "react-router-dom";
import InternalLaporanIndex from "@/pages/laporan";
import { registerRoutes } from "../../helpers/routeDetector";
import { RouteGuard } from "../../components/RouteGuard";

// Layout component to wrap all laporan routes with protection
const LaporanLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Outlet />
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example: '/laporan': 'internal',
  // Example: '/laporan/:category': 'internal',
  // Add your route registrations here
});

export const laporan = {
  routes: (
    <>
      <Route path="/laporan" element={<LaporanLayout />}>
        <Route index element={<InternalLaporanIndex />} />
        <Route path=":category" element={<InternalLaporanIndex />} />
      </Route>
    </>
  )
};
