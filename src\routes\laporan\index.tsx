import { Route, Outlet, Navigate } from "react-router-dom";
import InternalLaporanIndex from "@/pages/laporan";
import { registerRoutes } from "../../helpers/routeDetector";
import { RouteGuard } from "../../components/RouteGuard";
import { NEW_PermissionNames } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";
import DashboardStatistikContent from "@/pages/laporan/dashboard-statistik";
import Statistik from "@/pages/laporan/statistik";
import Laporan from "@/pages/laporan/laporan";
import AnalisisContent from "@/pages/laporan/analisis";
import { TabGuard } from "@/layouts/tabGuard";

// Layout component to wrap all laporan routes with protection
const LaporanLayout = () => {
  if (
    !AuthHelper.hasAuthority([NEW_PermissionNames.LAPORAN.label]) &&
    localStorage.getItem("portal") === "2"
  ) {
    return <Navigate to="/forbidden" replace />;
  }
  return (
    <RouteGuard
      autoUpdatePortal={true}
      showDebugInfo={process.env.NODE_ENV === "development"}
    >
      <Outlet />
    </RouteGuard>
  );
};

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example: '/laporan': 'internal',
  // Example: '/laporan/:category': 'internal',
  // Add your route registrations here
});

export const laporan = {
  routes: (
    <>
      <Route path="/laporan" element={<LaporanLayout />}>
        <Route index element={<InternalLaporanIndex />} />
        <Route element={<InternalLaporanIndex />}>
          {/* <Route path=":category" element={<InternalLaporanIndex />}> */}
          <Route
            path="dashboard-statistik"
            element={
              <TabGuard
                permissionList={[
                  NEW_PermissionNames.LAPORAN.children.DASHBOARD_STATISTIK
                    .label,
                ]}
              >
                <DashboardStatistikContent />
              </TabGuard>
            }
          />
          <Route
            path="statistik"
            element={
              <TabGuard
                permissionList={[
                  NEW_PermissionNames.LAPORAN.children.STATISTIK.label,
                ]}
              >
                <Statistik />
              </TabGuard>
            }
          />
          <Route
            path="laporan"
            element={
              <TabGuard
                permissionList={[
                  NEW_PermissionNames.LAPORAN.children.LAPORAN_DETAIL.label,
                ]}
              >
                <Laporan />
              </TabGuard>
            }
          />
          <Route
            path="analisis"
            element={
              <TabGuard
                permissionList={[
                  NEW_PermissionNames.LAPORAN.children.ANALISIS.label,
                ]}
              >
                <AnalisisContent />
              </TabGuard>
            }
          />
        </Route>
      </Route>
      {/* </Route> */}
    </>
  ),
};
