import React from "react";
import { formatDate } from "@/helpers";

import {
  Box,
  Typography,
  Divider,
  Paper,
  Container,
  Grid,
} from "@mui/material";

import { ISecretaryBranchDetail } from "@/types";

interface PrintPageProps {
  secretaryBranchDetail: ISecretaryBranchDetail | null;
}

const PrintPage = React.forwardRef<HTMLDivElement, PrintPageProps>(
  ({ secretaryBranchDetail }, ref) => {

    if (!secretaryBranchDetail) return null

    const data = [
      {
        label: "Nama Pemohon",
        value: secretaryBranchDetail?.applicantName ?? "-",
      },
      {
        label: "No. Kad <PERSON> Pemohon",
        value: secretaryBranchDetail?.applicantIdNo ?? "-",
      },
    ];

    const dataCawangan = [
      {
        label: "<PERSON><PERSON> (Cawangan)",
        value: secretaryBranchDetail?.societyName ?? "-",
      },
      {
        label: "Nama Setiausaha Cawangan",
        value: secretaryBranchDetail?.secretaryName ?? "-",
      },
      {
        label: "No Pengenalan Setiausaha Cawangan",
        value: secretaryBranchDetail?.secretaryIdNo ?? "-",
      },
      {
        label: "Nombor Rujukan Cawangan",
        value: secretaryBranchDetail?.branchNo ?? "-",
      },
      {
        label: "Wujud Daftar",
        value: formatDate(secretaryBranchDetail?.applicationDateTime),
      },
    ];

    return (
      <Paper
        ref={ref as React.RefObject<HTMLDivElement>}
        sx={{ padding: 4, width: "80%", margin: "0 auto" }}
      >
        <Box sx={{ display: "flex" }}>
          <Box sx={{ flexShrink: "none" }}>
            <img src="/logo.png" alt="logo" width={100} />
          </Box>

          <Box mb={2}>
            <Typography fontWeight="300 !important" fontSize="14px">
              JABATAN PENDAFTARAN PERTUBUHAN MALAYSIA
            </Typography>
            <Typography fontWeight="300 !important" fontSize="14px">
              KEMENTERIAN DALAM NEGERI
            </Typography>
            <Typography fontWeight="300 !important" fontSize="14px">
              Aras 8&9, Blok Setia Perkasa 7,
            </Typography>
            <Typography fontWeight="300 !important" fontSize="14px">
              Kompleks Setia Perkasa,Pusat Pentadbiran Kerajaan Persekutuan,
            </Typography>
            <Typography fontWeight="300 !important" fontSize="14px">
              62546, Putrajaya
            </Typography>
            <Typography fontWeight="300 !important" fontSize="14px">
              Tel: 603-8890 5776 / 603-8890 5778 Faks: 03-8889 3706 / E-mail:
            </Typography>
            <Typography fontWeight="300 !important" fontSize="14px">
              <EMAIL>
            </Typography>
          </Box>
        </Box>

        <Typography
          align="center"
          gutterBottom
          fontWeight="500 !important"
          fontSize="16px"
        >
          SLIP PENGESAHAN PEMBAHARUAN SETIAUSAHA CAWANGAN
        </Typography>
        <Typography
          align="center"
          mb={4}
          sx={{ fontStyle: "italic" }}
          fontWeight="300 !important"
          fontSize="14px"
        >
          *dikepilkan di fail perlembagaan
        </Typography>

        <Container maxWidth="md">
          <Grid container>
            {data.map((item, index) => (
              <Grid container key={index} item spacing={1}>
                <Grid item xs={5}>
                  <Typography fontWeight="300 !important" fontSize="14px">
                    {item.label}
                  </Typography>
                </Grid>
                <Grid item xs={7}>
                  <Typography fontWeight="300 !important" fontSize="14px">
                    : {item.value}
                  </Typography>
                </Grid>
              </Grid>
            ))}
          </Grid>

          <Divider sx={{ my: 2, borderStyle: "dashed" }} />

          <Grid container>
            {dataCawangan.map((item, index) => (
              <Grid container key={index} item spacing={1}>
                <Grid item xs={5}>
                  <Typography fontWeight="300 !important" fontSize="14px">
                    {item.label}
                  </Typography>
                </Grid>
                <Grid item xs={7}>
                  <Typography fontWeight="300 !important" fontSize="14px">
                    : {item.value}
                  </Typography>
                </Grid>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Paper>
    );
  }
);

export default PrintPage;
