import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import { RegExNumbers } from "@/helpers";
import AsalContent from "@/components/asalFasalContent";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";

interface FasalContentDuaBelasCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: [];
  name: string;
}

export const FasalContentDuaBelasCawangan: React.FC<
  FasalContentDuaBelasCawanganProps
> = ({ activeStep, setActiveStep, clause, asalData, name }) => {
  const [asal, setAsal] = useState<any>(null);
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [lainlain, setLainLain] = useState("");
  const [kekerapanPelaksanaan, setKekerapanPelaksanaan] = useState(
    t("setahun")
  );
    const [bilanganTempohPelaksanaan, setBilanganTempohPelaksanaan] = useState("1");
    const [tempohPelaksanaan, setTempohPelaksanaan] = useState(""); 
  const [notisPanggilanMesyuarat, setNotisPanggilanMesyuarat] = useState("7");
  const [tempohPelantikanWakilCawangan, setTempohPelantikanWakilCawangan] =
    useState("");
  const [tempohPelantikanWakilAhli, setTempohPelantikanWakilAhli] =
    useState("");
  const [bilanganWakilCawanganTerpilih, setBilanganWakilCawanganTerpilih] =
    useState("");
  const [bilanganWakilCawanganPertama, setBilanganWakilCawanganPertama] =
    useState("");
  const [
    bilanganWakilCawanganAhliPertama,
    setBilanganWakilCawanganAhliPertama,
  ] = useState("");
  const [
    bilanganWakilCawanganAhliSeterusnya,
    setBilanganWakilCawanganAhliSeterusnya,
  ] = useState("");
  const [bilanganWakilAhliTerpilih, setBilanganWakilAhliTerpilih] =
    useState("");
  const [bilanganWakilAhliPertama, setBilanganWakilAhliPertama] = useState("");
  const [bilanganWakilAhliSeterusnya, setBilanganWakilAhliSeterusnya] =
    useState("");
  const [maksimumBilanganWakilCawangan, setMaksimumBilanganWakilCawangan] =
    useState("");
  const [maksimumBilanganWakilAhli, setMaksimumBilanganWakilAhli] =
    useState("");

  const [dataId, setDataId] = useState<number | null>(null);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const { id, clauseId } = useParams();
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName } = responseData?.data?.data;
        setNamaPertubuhan(societyName);
      },
    },
  });

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!pemilihanAjk) {
      errors.pemilihanAjk = t("fieldRequired");
    }

    if (!kekerapanPelaksanaan) {
      errors.kekerapanPelaksanaan = t("fieldRequired");
    }

    if (!notisPanggilanMesyuarat) {
      errors.notisPanggilanMesyuarat = t("fieldRequired");
    }

    if (!tempohPelaksanaan) {
      errors.tempohPelaksanaan = t("fieldRequired");
    }

    return errors;
  };

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause = JSON.parse(clause12);
      setDataId(clause.id);
      //setNamaPertubuhan(clause.societyName);
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent);
        setClauseContentId(clause.clauseContentId);
      }

      const fieldMappings: Record<string, (value: string) => void> = {
        'Jenis Mesyuarat Agung': (value: string) => {
          if (value && value !== t("annual") && value !== t("biennial")) {
            setPemilihanAjk(t("lainLain"));
            setLainLain(value);
          } else {
            setPemilihanAjk(value);
          }}, 
        "Kekerapan pelaksanaan mesyuarat agung baru": (value: string) => {
          if (value) {
            setKekerapanPelaksanaan(value);
          } else {
            setKekerapanPelaksanaan(t("tahun"));
          }},   
        "Notis panggilan mesyuarat": (value: string) => {
          if (value) {
            setNotisPanggilanMesyuarat(value);
          } else {
            setNotisPanggilanMesyuarat("7");
          }},   
        "Bilangan tempoh pelaksanaan mesyuarat agung baru": (value: string) => {
          if (value) {
            setBilanganTempohPelaksanaan(value);
          } else {
            setBilanganTempohPelaksanaan("1");
          }},  
        "Tempoh pelaksanaan mesyuarat agung baru daripada tarikh terakhir Mesyuarat Agung":(value: string) => {
          if (value) {
            setTempohPelaksanaan(value);
          } else {
            setTempohPelaksanaan(t("day"));
          }},    
        "Tempoh pelantikan wakil cawangan": (value: string) => {
          if (value) {
            setTempohPelantikanWakilCawangan(value);
          } else {
            setTempohPelantikanWakilCawangan("1");
          }},    
        "Tempoh pelantikan wakil ahli": (value: string) => {
          if (value) {
            setTempohPelantikanWakilAhli(value);
          } else {
            setTempohPelantikanWakilAhli("1");
          }},   
        "Bilangan wakil cawangan terpilih": (value: string) => {
          if (value) {
            setBilanganWakilCawanganTerpilih(value);
          } else {
            setBilanganWakilCawanganTerpilih("1");
          }},   
        "Bilangan wakil cawangan ahli pertama":(value: string) => {
          if (value) {
            setBilanganWakilCawanganAhliPertama(value);
          } else {
            setBilanganWakilCawanganAhliPertama("7");
          }},     
        "Bilangan wakil cawangan ahli seterusnya": (value: string) => {
          if (value) {
            setBilanganWakilCawanganAhliSeterusnya(value);
          } else {
            setBilanganWakilCawanganAhliSeterusnya("7");
          }},    
        "Bilangan wakil ahli terpilih": (value: string) => {
          if (value) {
            setBilanganWakilAhliTerpilih(value);
          } else {
            setBilanganWakilAhliTerpilih("1");
          }},    
        "Bilangan wakil ahli pertama":(value: string) => {
          if (value) {
            setBilanganWakilAhliPertama(value);
          } else {
            setBilanganWakilAhliPertama("7");
          }},    
        "Bilangan wakil ahli seterusnya":(value: string) => {
          if (value) {
            setBilanganWakilAhliSeterusnya(value);
          } else {
            setBilanganWakilAhliSeterusnya("7");
          }},    
        "Maksimum bilangan wakil cawangan":(value: string) => {
          if (value) {
            setMaksimumBilanganWakilCawangan(value);
          } else {
            setMaksimumBilanganWakilCawangan("2");
          }},   
        "Maksimum bilangan wakil ahli":(value: string) => {
          if (value) {
            setMaksimumBilanganWakilAhli(value);
          } else {
            setMaksimumBilanganWakilAhli("2");
          }},    
      };

      Object.values(fieldMappings).forEach(setter => setter(''));
      
      if(clause.constitutionValues){
        clause.constitutionValues.forEach((item:any) => {
          const setter = fieldMappings[item.titleName];
          if (setter && item.definitionName) {
            setter(item.definitionName);
          }
        });
      }
  
      setIsEdit(clause.edit);
      setAsal(
        asalData.find((item: any) => item.clauseNo === clause.clauseNo
        ) || ""
      );
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<kekerapan pelaksanaan mesyuarat agung baru>>/gi,
    `<b>${
      kekerapanPelaksanaan || "<<kekerapan pelaksanaan mesyuarat agung baru>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b> ${lainlain ? lainlain : pemilihanAjk || "<<jenis mesyuarat agung>>"} </b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bilangan tempoh pelaksanaan mesyuarat agung baru>>/gi,
    `<b>${
      bilanganTempohPelaksanaan || "<<bilangan tempoh pelaksanaan mesyuarat agung baru>>"
    }</b>`
  ); 
  clauseContent = clauseContent.replaceAll(
    /<<tempoh pelaksanaan mesyuarat agung baru>>/gi,
    `<b>${
      tempohPelaksanaan || "<<tempoh pelaksanaan mesyuarat agung baru>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh pelantikan wakil cawangan>>/gi,
    `<b>${
      tempohPelantikanWakilCawangan || "<<tempoh pelantikan wakil cawangan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<notis panggilan mesyuarat>>/gi,
    `<b>${notisPanggilanMesyuarat || "<<notis panggilan mesyuarat>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh pelantikan wakil ahli>>/gi,
    `<b>${tempohPelantikanWakilAhli || "<<tempoh pelantikan wakil ahli>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahPengerusi || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<bilangan wakil cawangan terpilih>>/gi,
    `<b>${
      bilanganWakilCawanganTerpilih || "<<bilangan wakil cawangan terpilih>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bilangan wakil ahli terpilih>>/gi,
    `<b>${bilanganWakilAhliTerpilih || "<<bilangan wakil ahli terpilih>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<bilangan wakil cawangan pertama>>/gi, `<b>${bilanganWakilCawanganPertama || '<<bilangan wakil cawangan pertama>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<bilangan wakil cawangan ahli pertama>>/gi,
    `<b>${
      bilanganWakilCawanganAhliPertama ||
      "<<bilangan wakil cawangan ahli pertama>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bilangan wakil ahli pertama>>/gi,
    `<b>${bilanganWakilAhliPertama || "<<bilangan wakil ahli pertama>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bilangan wakil ahli seterusnya>>/gi,
    `<b>${
      bilanganWakilAhliSeterusnya || "<<bilangan wakil ahli seterusnya>>"
    }</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahBendahari || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<bilangan wakil cawangan ahli seterusnya>>/gi,
    `<b>${
      bilanganWakilCawanganAhliSeterusnya ||
      "<<bilangan wakil cawangan ahli seterusnya>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<maksimum bilangan wakil cawangan>>/gi,
    `<b>${
      maksimumBilanganWakilCawangan || "<<maksimum bilangan wakil cawangan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<maksimum bilangan wakil ahli>>/gi,
    `<b>${maksimumBilanganWakilAhli || "<<maksimum bilangan wakil ahli>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahSetiaUsaha || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");
  return (
    <>
      <AsalContent
        asalData={asal?.description || ""}
        clauseId={clauseId}
        name={name}
      />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("generalMeeting")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("jenisMesyuaratAgung")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required error={!!formErrors.pemilihanAjk}>
              <Select
                size="small"
                value={pemilihanAjk}
                displayEmpty
                disabled={isViewMode}
                onChange={(e) => {
                  setPemilihanAjk(e.target.value as string);
                  if (e.target.value === t("annual")) {
                    setLainLain("")
                    setKekerapanPelaksanaan(t("setahun"));
                  } else if (e.target.value === t("biennial")) {
                    setLainLain("")
                    setKekerapanPelaksanaan(t("duaTahun"));
                  }
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    pemilihanAjk: "",
                  }));
                }}
              >
                <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
                <MenuItem value={t("lainLain")}>{t("lainLain")}</MenuItem>
              </Select>
              {formErrors.pemilihanAjk && (
                <FormHelperText>{formErrors.pemilihanAjk}</FormHelperText>
              )}
            </FormControl>
          </Grid>

          {pemilihanAjk === t("lainLain") ?
            <>
              <Grid item xs={12} md={4}></Grid>
              <Grid item xs={12} md={8}>
                <TextField
                  type="text"
                  size="small"
                  placeholder="Yearly, Biannually, Tri-tahunan"
                  fullWidth
                  required
                  value={lainlain}
                  onChange={(e) => {
                    setLainLain(e.target.value as string);
                  }}
                />
              </Grid>
            </> : null
          }

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("kekerapanPelaksanaan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              disabled={isViewMode}
              value={kekerapanPelaksanaan}
              onChange={(e) => {
                setKekerapanPelaksanaan(e.target.value as string);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  kekerapanPelaksanaan: "",
                }));
              }}
              error={!!formErrors.kekerapanPelaksanaan}
              helperText={formErrors.kekerapanPelaksanaan}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("notisPanggilanMesyuarat")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.notisPanggilanMesyuarat}
            >
              <Select
                size="small"
                value={notisPanggilanMesyuarat}
                displayEmpty
                disabled={isViewMode}
                onChange={(e) => {
                  setNotisPanggilanMesyuarat(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    notisPanggilanMesyuarat: "",
                  }));
                }}
              >
                <MenuItem value={"7"}>7</MenuItem>
                <MenuItem value={"14"}>14</MenuItem>
                <MenuItem value={"30"}>30</MenuItem>
              </Select>
              {formErrors.notisPanggilanMesyuarat && (
                <FormHelperText>
                  {formErrors.notisPanggilanMesyuarat}
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <Select disabled size="small" value={t("day")} displayEmpty>
              <MenuItem value={t("day")}>{t("day")}</MenuItem>
            </Select>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("tempohPelaksanaan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}> 
            <TextField
              size="small"
              fullWidth
              required
              disabled={isViewMode}
              value={bilanganTempohPelaksanaan}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setBilanganTempohPelaksanaan(e.target.value); 
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganTempohPelaksanaan: "",
                  }));
                } else {
                  setBilanganTempohPelaksanaan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganTempohPelaksanaan: "Invalid Value", 
                  }));
                }
              }}
              type="number"
              error={!!formErrors.bilanganTempohPelaksanaan}
              helperText={formErrors.bilanganTempohPelaksanaan}
            />
          </Grid>
          {/*  */}
            <Grid item xs={12} md={4}>
              <Select   
                onChange={(e) => { 
                  setTempohPelaksanaan(e.target.value as string); 
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      pemilihanAjk: "",
                    })); 
                  }} 
                  size="small" value={tempohPelaksanaan} displayEmpty>
                <MenuItem value={t("day")}>{t("day")}</MenuItem>
                <MenuItem value={t("minggu")}>{t("minggu")}</MenuItem>
                <MenuItem value={t("bulan")}>{t("bulan")}</MenuItem>
              </Select>
            </Grid>
            {/*  */}

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("tempohPelantikanWakilCawangan")}
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={tempohPelantikanWakilCawangan}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setTempohPelantikanWakilCawangan(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    tempohPelantikanWakilCawangan: "",
                  }));
                } else {
                  setTempohPelantikanWakilCawangan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    tempohPelantikanWakilCawangan: "Invalid Value",
                  }));
                }
              }}
              type="number"
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Select disabled size="small" value={t("year")} displayEmpty>
              <MenuItem value={t("year")}>{t("year")}</MenuItem>
            </Select>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("tempohPelantikanWakilAhli")}
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={tempohPelantikanWakilAhli}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setTempohPelantikanWakilAhli(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    tempohPelantikanWakilAhli: "",
                  }));
                } else {
                  setTempohPelantikanWakilAhli("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    tempohPelantikanWakilAhli: "Invalid Value",
                  }));
                }
              }}
              type="number"
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Select disabled size="small" value={t("year")} displayEmpty>
              <MenuItem value={t("year")}>{t("year")}</MenuItem>
            </Select>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("bilanganWakilCawangan")}
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bilanganWakilCawanganTerpilih}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setBilanganWakilCawanganTerpilih(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganWakilCawanganTerpilih: "",
                  }));
                } else {
                  setBilanganWakilCawanganTerpilih("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganWakilCawanganTerpilih: "Invalid Value",
                  }));
                }
              }}
              type="number"
            />
          </Grid>

          {/*<Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("bilanganWakilCawanganPertama")}
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              value={bilanganWakilCawanganPertama}
              onChange={(e) => {
                if(RegExNumbers.test(e.target.value)) {
                  setBilanganWakilCawanganPertama(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganWakilCawanganPertama: "",
                  }));
                }
                else {
                  setBilanganWakilCawanganPertama("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganWakilCawanganPertama: "Invalid Value",
                  }));
                }
              }}
              type="number"
            />
          </Grid>*/}

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("bilanganWakilCawanganAhli")}
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bilanganWakilCawanganAhliPertama}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setBilanganWakilCawanganAhliPertama(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganWakilCawanganAhliPertama: "",
                  }));
                } else {
                  setBilanganWakilCawanganAhliPertama("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganWakilCawanganAhliPertama: "Invalid Value",
                  }));
                }
              }}
              type="number"
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("bilanganWakilCawanganAhliSeterusnya")}
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bilanganWakilCawanganAhliSeterusnya}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setBilanganWakilCawanganAhliSeterusnya(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganWakilCawanganAhliSeterusnya: "",
                  }));
                } else {
                  setBilanganWakilCawanganAhliSeterusnya("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganWakilCawanganAhliSeterusnya: "Invalid Value",
                  }));
                }
              }}
              type="number"
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("bilanganWakilAhli")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bilanganWakilAhliTerpilih}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setBilanganWakilAhliTerpilih(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganWakilAhliTerpilih: "",
                  }));
                } else {
                  setBilanganWakilAhliTerpilih("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganWakilAhliTerpilih: "Invalid Value",
                  }));
                }
              }}
              type="number"
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("bilanganWakilAhliPertama")}
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bilanganWakilAhliPertama}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setBilanganWakilAhliPertama(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganWakilAhliPertama: "",
                  }));
                } else {
                  setBilanganWakilAhliPertama("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganWakilAhliPertama: "Invalid Value",
                  }));
                }
              }}
              type="number"
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("bilanganWakilAhliSeterusnya")}
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bilanganWakilAhliSeterusnya}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setBilanganWakilAhliSeterusnya(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganWakilAhliSeterusnya: "",
                  }));
                } else {
                  setBilanganWakilAhliSeterusnya("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilanganWakilAhliSeterusnya: "Invalid Value",
                  }));
                }
              }}
              type="number"
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("maksimumWakilCawangan")}
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={maksimumBilanganWakilCawangan}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setMaksimumBilanganWakilCawangan(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    maksimumBilanganWakilCawangan: "",
                  }));
                } else {
                  setMaksimumBilanganWakilCawangan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    maksimumBilanganWakilCawangan: "Invalid Value",
                  }));
                }
              }}
              type="number"
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("maksimumWakilAhli")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              value={maksimumBilanganWakilAhli}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setMaksimumBilanganWakilAhli(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    maksimumBilanganWakilAhli: "",
                  }));
                } else {
                  setMaksimumBilanganWakilAhli("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    maksimumBilanganWakilAhli: "Invalid Value",
                  }));
                }
              }}
              type="number"
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              const errors = validateForm();
              if (Object.keys(errors).length > 0) {
                setFormErrors(errors);
                return;
              }

              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: lainlain ? lainlain : pemilihanAjk,
                    titleName: "Jenis Mesyuarat Agung",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bilanganTempohPelaksanaan,
                    titleName:
                      "Bilangan tempoh pelaksanaan mesyuarat agung baru", 
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tempohPelaksanaan,
                    titleName:
                      "Tempoh pelaksanaan mesyuarat agung baru daripada tarikh terakhir Mesyuarat Agung",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: kekerapanPelaksanaan,
                    titleName: "Kekerapan pelaksanaan mesyuarat agung baru",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: notisPanggilanMesyuarat,
                    titleName: "Notis panggilan mesyuarat",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tempohPelantikanWakilCawangan,
                    titleName: "Tempoh pelantikan wakil cawangan",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tempohPelantikanWakilAhli,
                    titleName: "Tempoh pelantikan wakil ahli",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bilanganWakilCawanganTerpilih,
                    titleName: "Bilangan wakil cawangan terpilih",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bilanganWakilCawanganAhliPertama,
                    titleName: "Bilangan wakil cawangan ahli pertama",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bilanganWakilCawanganAhliSeterusnya,
                    titleName: "Bilangan wakil cawangan ahli seterusnya",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bilanganWakilAhliTerpilih,
                    titleName: "Bilangan wakil ahli terpilih",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bilanganWakilAhliPertama,
                    titleName: "Bilangan wakil ahli pertama",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bilanganWakilAhliSeterusnya,
                    titleName: "Bilangan wakil ahli seterusnya",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: maksimumBilanganWakilCawangan,
                    titleName: "Maksimum bilangan wakil cawangan",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: maksimumBilanganWakilAhli,
                    titleName: "Maksimum bilangan wakil ahli",
                  },
                  /*{
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bilanganWakilCawanganPertama,
                  titleName: "Bilangan wakil cawangan pertama",
                },*/
                ],
                clause: "clause12",
                clauseCount: 12,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentDuaBelasCawangan;
