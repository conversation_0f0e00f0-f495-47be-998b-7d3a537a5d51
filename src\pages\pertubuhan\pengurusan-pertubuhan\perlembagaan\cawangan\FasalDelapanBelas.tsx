import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { SingleFileInput } from "../../../../../components/input";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { API_URL } from "../../../../../api";
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import { months, RegExNumbers, RegExText } from "../../../../../helpers/enums";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import { formatAndValidateNumber } from "@/helpers";
import CustomPopover from "@/components/popover";
interface FasalContentDelapanBelasCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

const inputStyle = {
  height: "100%",
  width: "100%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400!important",
};

export const FasalContentDelapanBelasCawangan: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [jumlahWangTangan, setJumlahWangTangan] = useState("");
  const [jumlahWangTanganKata, setJumlahWangTanganKata] = useState("");
  const [kuasaPerbelanjaan, setKuasaPerbelanjaan] = useState("");
  const [kuasaPerbelanjaanKata, setKuasaPerbelanjaanKata] = useState("");
  const [kuasaPerbelanjaanJawatankuasa, setKuasaPerbelanjaanJawatankuasa] =
    useState("");
  const [
    kuasaPerbelanjaanJawatankuasaKata,
    setKuasaPerbelanjaanJawatankuasaKata,
  ] = useState("");
  const [perbelanjaanDibenarkan, setPerbelanjaanDibenarkan] = useState("");
  const [perbelanjaanDibenarkanKata, setPerbelanjaanDibenarkanKata] =
    useState("");
  const [tempohDibenarkan, setTempohDibenarkan] = useState("");
  const [tahunKewanganBermula, setTahunKewanganBermula] = useState("");

  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string | number>("");
  //const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!jumlahWangTangan) {
      errors.jumlahWangTangan = t("fieldRequired");
    }

    if (!jumlahWangTanganKata) {
      errors.jumlahWangTanganKata = t("fieldRequired");
    }

    if (!kuasaPerbelanjaan) {
      errors.kuasaPerbelanjaan = t("fieldRequired");
    }

    if (!kuasaPerbelanjaanKata) {
      errors.kuasaPerbelanjaanKata = t("fieldRequired");
    }

    if (!kuasaPerbelanjaanJawatankuasa) {
      errors.kuasaPerbelanjaanJawatankuasa = t("fieldRequired");
    }

    if (!kuasaPerbelanjaanJawatankuasaKata) {
      errors.kuasaPerbelanjaanJawatankuasaKata = t("fieldRequired");
    }

    if (!perbelanjaanDibenarkan) {
      errors.perbelanjaanDibenarkan = t("fieldRequired");
    }

    if (!perbelanjaanDibenarkanKata) {
      errors.perbelanjaanDibenarkanKata = t("fieldRequired");
    }

    if (!tempohDibenarkan) {
      errors.tempohDibenarkan = t("fieldRequired");
    }

    // if (!tahunKewanganBermula) {
    //   errors.tahunKewanganBermula = t("fieldRequired");
    // }

    return errors;
  };

  /*const clauseContent = `
1. Semua wang yang diterima oleh Ibu Pejabat dan setiap cawangan Pertubuhan akan menjadi aset bersama Pertubuhan.

2. Mesyuarat Agung ${
    pemilihanAjk || "<<jenis mesyuarat agung>>"
  } akan menentukan dari semasa ke semasa kadar simpanan tunai yang akan disimpan di cawangan sebagai kumpulan wang cawangan dan akan menetapkan jenis-jenis perbelanjaan yang dibenarkan menggunakan kumpulan wang cawangan.

3. Tiap-tiap Bendahari bagi Cawangan yang tidak mempunyai akaun bank yang diluluskan hendaklah menyampaikan kepada Bendahari Agung sebelum 15 hari bulan tiap-tiap bulan yuran-yuran yang diterima oleh cawangan setelah ditolak kadar simpanan tunai kumpulan wang cawangan.

4. Bendahari Cawangan dibenarkan menyimpan wang tunai dalam tangan tidak lebih daripada RM ${
    jumlahWangTangan || "<<jumlah wang tangan yang dibenarkan dalam tangan>>"
  } (Ringgit Malaysia ${
    jumlahWangTanganKata ||
    "<<jumlah wang tangan dibenarkan-tulis dalam perkataan>>"
  } sahaja) pada satu-satu masa. Wang yang lebih daripada jumlah itu mestilah dimasukkan ke akaun bank yang diluluskan oleh Jawatankuasa Cawangan dalam tempoh ${
    tempohDibenarkan ||
    "<<tempoh yang dibenarkan untuk memasukkan wang lebihan ke dalam bank>>"
  } hari. Akaun bank tersebut hendaklah dibuka atas nama cawangan.

5. Segala cek atau penyata pengeluaran wang daripada akaun cawangan hendaklah ditandatangani bersama oleh Pengerusi Cawangan, Setiausaha Cawangan, dan Bendahari Cawangan. Walau bagaimanapun, Jawatankuasa Cawangan melalui mesyuarat berhak melantik sesiapa di antara mereka sebagai pengganti untuk menandatangani cek atau pengeluaran wang cawangan semasa ketiadaan mana-mana penandatangan tersebut.

6. Perbelanjaan yang lebih daripada RM ${
    kuasaPerbelanjaanJawatankuasa ||
    "<<kuasa perbelanjaan jawatankuasa cawangan>>"
  } (Ringgit Malaysia ${
    kuasaPerbelanjaanJawatankuasaKata ||
    "<<kuasa perbelanjaan jawatankuasa cawangan-tulis dalam perkataan>>"
  } Sahaja) pada satu-satu masa tidak boleh dilakukan tanpa kelulusan Mesyuarat Jawatankuasa Cawangan dan perbelanjaan yang lebih daripada RM ${
    kuasaPerbelanjaan || "<<kuasa perbelanjaan mesyuarat agung cawangan>>"
  } (Ringgit Malaysia ${
    kuasaPerbelanjaanKata ||
    "<<kuasa perbelanjaan mesyuarat agung cawangan-tulis dalam perkataan>>"
  } Sahaja) bagi satu-satu masa tidak boleh dilakukan tanpa mendapat kebenaran Mesyuarat Agung Cawangan terlebih dahulu. Perbelanjaan yang kurang RM ${
    perbelanjaanDibenarkan || "<<perbelanjaan yg dibenarkan>>"
  } (Ringgit Malaysia ${
    perbelanjaanDibenarkanKata ||
    "<<perbelanjaan yg dibenarkan-tulis dalam perkataan>>"
  } Sahaja) boleh diluluskan bersama oleh Pengerusi Cawangan, Setiausaha Cawangan, dan Bendahari Cawangan.

7. Tahun kewangan Pertubuhan ini hendaklah bagi tempoh 12 bulan iaitu bermula dari ${
    tahunKewanganBermula || "<<tahun kewangan bermula>>"
  }.

8. Penyata kewangan bagi tempoh 12 bulan hendaklah disediakan oleh Bendahari Cawangan dan diperiksa oleh Juruaudit yang dilantik di bawah Fasal 11 Perlembagaan ini dengan segera setelah tamat tahun kewangan. Penyata kewangan yang telah diaudit itu hendaklah diedar untuk makluman ahli-ahli dan dikemukakan untuk diluluskan oleh Mesyuarat Agung ${
    pemilihanAjk || "<<jenis mesyuarat agung>>"
  } Cawangan yang berikut. Setiap salinan dokumen tersebut hendaklah disimpan di alamat tempat urusan cawangan untuk makluman ahli.

9. Semua wang, buku, dan harta-harta Pertubuhan yang dipegang oleh mana-mana Cawangan yang telah dibubarkan hendaklah dihantar dengan segera oleh Setiausaha Cawangan kepada Setiausaha Agung, bersama-sama dengan penyata kewangan sebagaimana yang dinyatakan pada Fasal 14(6) dan 14(7).
`;*/

  //const clause18 = localStorage.getItem("clause18");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause = JSON.parse(clause18);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }

      const fieldMappings: Record<string, (value: string) => void> = {
        "Tahun Kewangan Bermula (Tahun Kewangan Bermula Dari Tahun Kewangan Yang Dibenarkan)":
          setTahunKewanganBermula,
        "Jenis Mesyuarat Agung": setPemilihanAjk,
        "Jumlah Wang Tangan yang Dibenarkan Dalam Tangan": setJumlahWangTangan,
        "Jumlah Wang Tangan yang Dibenarkan Dalam Tangan (Tulis Dalam Perkataan)":
          setJumlahWangTanganKata,
        "Kuasa Perbelanjaan Mesyuarat Agung": setKuasaPerbelanjaan,
        "Kuasa Perbelanjaan Mesyuarat Agung (Tulis Dalam Perkataan)":
          setKuasaPerbelanjaanKata,
        "Kuasa Perbelanjaan Jawatankuasa": setKuasaPerbelanjaanJawatankuasa,
        "Kuasa Perbelanjaan Jawatankuasa (Tulis Dalam Perkataan)":
          setKuasaPerbelanjaanJawatankuasaKata,
        "Perbelanjaan yang Dibenarkan": setPerbelanjaanDibenarkan,
        "Perbelanjaan yang Dibenarkan (Tulis Dalam Perkataan)":
          setPerbelanjaanDibenarkanKata,
        "Tempoh yang Dibenarkan Untuk Memasukkan Wang Lebihan ke Dalam Bank":
          setTempohDibenarkan,
      };
      Object.values(fieldMappings).forEach((setter) => setter(""));

      if (clause.constitutionValues) {
        clause.constitutionValues.forEach((item: any) => {
          const setter = fieldMappings[item.titleName];
          if (setter && item.definitionName) {
            setter(item.definitionName);
          }
        });
      }

      // setPemilihanAjk(clause.constitutionValues[0]?.definitionName);
      // setTahunKewanganBermula(clause.constitutionValues[1]?.definitionName);

      // setJumlahWangTangan(clause.constitutionValues[2]?.definitionName);

      // setJumlahWangTanganKata(clause.constitutionValues[3]?.definitionName);
      // setKuasaPerbelanjaan(clause.constitutionValues[4]?.definitionName);
      // setKuasaPerbelanjaanKata(clause.constitutionValues[5]?.definitionName);
      // setKuasaPerbelanjaanJawatankuasa(
      //   clause.constitutionValues[6]?.definitionName
      // );
      // setKuasaPerbelanjaanJawatankuasaKata(
      //   clause.constitutionValues[7]?.definitionName
      // );
      // setPerbelanjaanDibenarkan(clause.constitutionValues[8]?.definitionName);
      // setPerbelanjaanDibenarkanKata(
      //   clause.constitutionValues[9]?.definitionName
      // );
      // setTempohDibenarkan(clause.constitutionValues[10]?.definitionName);

      setIsEdit(clause.edit);
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id } = useParams();

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<jumlah wang tangan yang dibenarkan dalam tangan>>/gi,
    `<b>${
      jumlahWangTangan || "<<jumlah wang tangan yang dibenarkan dalam tangan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jumlah wang tangan dibenarkan-tulis dalam perkataan>>/gi,
    `<b>${
      jumlahWangTanganKata || "<<jumlah wang dibenarkan-tulis dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<perbelanjaan yg dibenarkan>>/gi,
    `<b>${perbelanjaanDibenarkan || "<<perbelanjaan yg dibenarkan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<perbelanjaan yg dibenarkan-tulis dalam perkataan>>/gi,
    `<b>${
      perbelanjaanDibenarkanKata ||
      "<<perbelanjaan yg dibenarkan-tulis dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${pemilihanAjk || "<<jenis mesyuarat agung>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan mesyuarat agung cawangan>>/gi,
    `<b>${
      kuasaPerbelanjaan || "<<kuasa perbelanjaan mesyuarat agung cawangan>>"
    }</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahPengerusi || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan mesyuarat agung cawangan-tulis dalam perkataan>>/gi,
    `<b>${
      kuasaPerbelanjaanKata ||
      "<<kuasa perbelanjaan mesyuarat agung cawangan-tulis dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan jawatankuasa cawangan>>/gi,
    `<b>${
      kuasaPerbelanjaanJawatankuasa ||
      "<<kuasa perbelanjaan jawatankuasa cawangan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan jawatankuasa cawangan-tulis dalam perkataan>>/gi,
    `<b>${
      kuasaPerbelanjaanJawatankuasaKata ||
      "<<kuasa perbelanjaan jawatankuasa cawangan-tulis dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh yang dibenarkan untuk memasukkan wang lebihan ke dalam bank>>/gi,
    `<b>${
      tempohDibenarkan ||
      "<<tempoh yang dibenarkan untuk memasukkan wang lebihan ke dalam bank>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tahun kewangan bermula>>/gi,
    `<b>${tahunKewanganBermula || "<<tahun kewangan bermula>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("jenisMesyuaratAgung")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("jenisMesyuaratAgung")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required>
              <Select
                // disabled
                size="small"
                value={pemilihanAjk}
                displayEmpty
                onChange={(e) => setPemilihanAjk(e.target.value as string)}
              >
                <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("permulaanTahunKewangan")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography sx={labelStyle}>
                {t("tahunKewanganBermula")}{" "}
                <Typography sx={{ display: "inline", color: "red" }}>
                  *
                </Typography>
              </Typography>
              <CustomPopover
                customStyles={{ maxWidth: "250px" }}
                content={
                  <Typography sx={{ color: "#666666" }}>
                    {t("contohJan")}
                  </Typography>
                }
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required>
              <TextField
                size="small"
                fullWidth
                required
                disabled
                value={tahunKewanganBermula}
              />
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("financialMangement")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("jumlahWangTangan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={jumlahWangTangan}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setJumlahWangTangan(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahWangTangan: "",
                  }));
                } else {
                  setJumlahWangTangan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahWangTangan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.jumlahWangTangan}
              helperText={formErrors.jumlahWangTangan}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh "
              value={jumlahWangTanganKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setJumlahWangTanganKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahWangTanganKata: "",
                  }));
                } else {
                  setJumlahWangTanganKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahWangTanganKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.jumlahWangTanganKata}
              helperText={formErrors.jumlahWangTanganKata}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          Tempoh Masukkan Wang Lebih Ke Bank
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("tempohDibenarkan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              value={tempohDibenarkan}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setTempohDibenarkan(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    tempohDibenarkan: "",
                  }));
                } else {
                  setTempohDibenarkan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    tempohDibenarkan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.tempohDibenarkan}
              helperText={formErrors.tempohDibenarkan}
              type="number"
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("kuasaPerbelanjaanCaw")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("kuasaPerbelanjaanCaw")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={8} sx={{ display: "flex" }} gap={1}>
            <Grid item xs={12} md={2}>
              <Box sx={inputStyle}>{t("morethan")}</Box>
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                size="small"
                fullWidth
                required
                value={kuasaPerbelanjaan}
                placeholder="RM 0.00"
                onChange={(e) => {
                  const formattedValue = formatAndValidateNumber(
                    e.target.value
                  );
                  if (formattedValue !== null) {
                    setKuasaPerbelanjaan(formattedValue);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      kuasaPerbelanjaan: "",
                    }));
                  } else {
                    setKuasaPerbelanjaan("");
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      kuasaPerbelanjaan: "Invalid Value",
                    }));
                  }
                }}
                error={!!formErrors.kuasaPerbelanjaan}
                helperText={formErrors.kuasaPerbelanjaan}
              />
            </Grid>
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh "
              value={kuasaPerbelanjaanKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setKuasaPerbelanjaanKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanKata: "",
                  }));
                } else {
                  setKuasaPerbelanjaanKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaanKata}
              helperText={formErrors.kuasaPerbelanjaanKata}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("kuasaPerbelanjaanCawangan2")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("kuasaPerbelanjaanCawangan2")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={kuasaPerbelanjaanJawatankuasa}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setKuasaPerbelanjaanJawatankuasa(formattedValue);
                  setPerbelanjaanDibenarkan(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasa: "",
                  }));
                } else {
                  setKuasaPerbelanjaanJawatankuasa("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasa: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaanJawatankuasa}
              helperText={formErrors.kuasaPerbelanjaanJawatankuasa}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <Typography sx={inputStyle}>sehingga</Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={kuasaPerbelanjaan}
              disabled
              placeholder="RM 0.00"
            />
          </Grid>
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              placeholder="contoh : dua puluh "
              required
              multiline
              rows={3}
              value={kuasaPerbelanjaanJawatankuasaKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setKuasaPerbelanjaanJawatankuasaKata(e.target.value);
                  setPerbelanjaanDibenarkanKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasaKata: "",
                  }));
                } else {
                  setKuasaPerbelanjaanJawatankuasaKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasaKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaanJawatankuasaKata}
              helperText={formErrors.kuasaPerbelanjaanJawatankuasaKata}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <Typography sx={inputStyle}>sehingga</Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              disabled
              multiline
              rows={3}
              fullWidth
              placeholder="contoh : dua puluh "
              required
              value={kuasaPerbelanjaanKata}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("perbelanjaanDibenarkanCawangan")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("perbelanjaanDibenarkan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={8} sx={{ display: "flex" }} gap={1}>
            <Grid item xs={12} md={2}>
              <Box sx={inputStyle}>{t("lessthan")}</Box>
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                size="small"
                fullWidth
                required
                disabled
                value={perbelanjaanDibenarkan}
                placeholder="RM 0.00"
                onChange={(e) => {
                  const formattedValue = formatAndValidateNumber(
                    e.target.value
                  );
                  if (formattedValue !== null) {
                    setPerbelanjaanDibenarkan(formattedValue);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      perbelanjaanDibenarkan: "",
                    }));
                  } else {
                    setPerbelanjaanDibenarkan("");
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      perbelanjaanDibenarkan: "Invalid Value",
                    }));
                  }
                }}
                error={!!formErrors.perbelanjaanDibenarkan}
                helperText={formErrors.perbelanjaanDibenarkan}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>

          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled
              placeholder="contoh : dua puluh "
              required
              value={perbelanjaanDibenarkanKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setPerbelanjaanDibenarkanKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    perbelanjaanDibenarkanKata: "",
                  }));
                } else {
                  setPerbelanjaanDibenarkanKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    perbelanjaanDibenarkanKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.perbelanjaanDibenarkanKata}
              helperText={formErrors.perbelanjaanDibenarkanKata}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            const errors = validateForm();
            console.log(errors);
            if (Object.keys(errors).length > 0) {
              setFormErrors(errors);
              return;
            }

            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: pemilihanAjk,
                  titleName: "Jenis Mesyuarat Agung",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jumlahWangTangan,
                  titleName: "Jumlah Wang Tangan yang Dibenarkan Dalam Tangan",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jumlahWangTanganKata,
                  titleName:
                    "Jumlah Wang Tangan yang Dibenarkan Dalam Tangan (Tulis Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kuasaPerbelanjaan,
                  titleName: "Kuasa Perbelanjaan Mesyuarat Agung",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kuasaPerbelanjaanKata,
                  titleName:
                    "Kuasa Perbelanjaan Mesyuarat Agung (Tulis Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kuasaPerbelanjaanJawatankuasa,
                  titleName: "Kuasa Perbelanjaan Jawatankuasa",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kuasaPerbelanjaanJawatankuasaKata,
                  titleName:
                    "Kuasa Perbelanjaan Jawatankuasa (Tulis Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: perbelanjaanDibenarkan,
                  titleName: "Perbelanjaan yang Dibenarkan",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: perbelanjaanDibenarkanKata,
                  titleName:
                    "Perbelanjaan yang Dibenarkan (Tulis Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tempohDibenarkan,
                  titleName:
                    "Tempoh yang Dibenarkan Untuk Memasukkan Wang Lebihan ke Dalam Bank",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tahunKewanganBermula,
                  titleName:
                    "Tahun Kewangan Bermula (Tahun Kewangan Bermula Dari Tahun Kewangan Yang Dibenarkan)",
                },
              ],
              clause: "clause18",
              clauseCount: 18,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentDelapanBelasCawangan;
