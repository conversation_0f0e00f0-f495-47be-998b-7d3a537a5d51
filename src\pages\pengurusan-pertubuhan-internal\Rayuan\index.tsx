import { Box, CircularProgress, Grid, Typography } from "@mui/material";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import WrapContent from "../View/WrapContent";
import MainSet from "./sections/MainSet";
import { useQuery } from "@/helpers";

interface RayuanBoxesProps {
  data: {
    name: string;
    number: number;
  };
  isActive: boolean;
  onClick: () => void;
}
const primary = "var(--primary-color)";

const RayuanBoxes: React.FC<RayuanBoxesProps> = React.memo(
  ({ data, isActive, onClick }) => {
    return (
      <Box
        onClick={onClick}
        sx={{
          "&:hover": {
            backgroundColor: isActive ? primary : "#f5f5f5",
            transform: "translateY(-2px)",
          },
          padding: "0.75rem",
          paddingTop: "0.5rem !important",
          borderRadius: "0.5rem",
          border: `1px solid ${primary}`,
          backgroundColor: isActive ? primary : "white",
          position: "relative",
          display: "grid",
          gap: 2,
          flexDirection: "column",
          justifyContent: "space-between",
          alignItems: "flex-start",
          height: "100%",
          minHeight: "80px",
          paddingBottom: 0,
          cursor: "pointer",
          transition: "all 0.2s ease-in-out",
          ...(isActive && {
            boxShadow: "4px 6px 12px 0 rgba(102, 102, 102, 0.3)",
          }),
        }}
      >
        <Box
          sx={{
            color: isActive ? "#fff" : "var(--primary-color)",
            fontWeight: 400,
            fontSize: "12px",
          }}
        >
          {data.name}
        </Box>
        <Typography
          sx={{
            alignSelf: "flex-end",
            color: isActive ? "#fff" : "var(--primary-color)",
            fontWeight: 500,
          }}
        >
          {data.number}
        </Typography>
      </Box>
    );
  }
);

export default function Rayuan() {
  const { t } = useTranslation();
  const [count, setCount] = useState<any>();

  const {
    data,
    refetch: fetchCount,
    isLoading,
  } = useQuery({
    url: "society/appeal/countByType",
    autoFetch: true,
    onSuccess: (data) => {
      setCount(data?.data?.data);
    },
  });

  const tab = count?.map((item: any, index: number) => ({
    id: index,
    name: item.typeName,
    number: item.count,
  }));

  const [activeTab, setActiveTab] = useState(0);

  const renderTab = () => {
    if (!count || count.length === 0) return null;

    return (
      <MainSet
        key={activeTab}
        title={count?.[activeTab]?.typeName}
        total={count?.[activeTab]?.count}
        idSebab={count?.[activeTab]?.idSebab}
        moduleType={count?.[activeTab]?.moduleType}
      />
    );
  };

  return (
    <>
      {isLoading ? (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            mt: 3,
          }}
        >
          <CircularProgress />
        </Box>
      ) : (
        <Box>
          <WrapContent title={t("decisionOfAppealApplication")}>
            <Grid container spacing={2}>
              {tab?.map((data: any, index: number) => (
                <Grid item xs={12} sm={6} md={4} lg={3} xl={2} key={index}>
                  <Box sx={{ height: "100%" }}>
                    <RayuanBoxes
                      data={data}
                      isActive={data.id === activeTab}
                      onClick={() => setActiveTab(data.id)}
                    />
                  </Box>
                </Grid>
              ))}
            </Grid>
          </WrapContent>
          {renderTab()}
        </Box>
      )}
    </>
  );
}
