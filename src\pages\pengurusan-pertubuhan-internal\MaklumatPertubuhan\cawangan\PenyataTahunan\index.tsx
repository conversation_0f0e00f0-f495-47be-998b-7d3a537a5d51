import { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";

import {
  Box,
  Button,
  CircularProgress,
  FormControl,
  Grid,
  MenuItem,
  Select,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { downloadFile, getLocalStorage } from "../../../../../helpers/utils";
import useMutation from "../../../../../helpers/hooks/useMutation";
import DisabledTextField from "../../../../../components/input/DisabledTextField";
import { DokumenIcon } from "../../../../../components/icons";
import { DialogConfirmation } from "@/components";
import { NEW_PermissionNames, pageAccessEnum, useQuery } from "@/helpers";
import { useCustom } from "@refinedev/core";
import { API_URL } from "@/api";
import dayjs from "dayjs";
import AuthHelper from "@/helpers/authHelper";

const labelStyle = {
  fontSize: "16px",
  color: "#666666",
  fontWeight: "400 !important",
};
interface YearOption {
  label: string;
  value: string;
}

function generateYearOptions(
  startDate: string,
  options?: {
    includeStartYear?: boolean;
    yearsAfterCurrent?: number;
    sort?: "asc" | "desc";
  }
): YearOption[] {
  const currentYear = new Date().getFullYear();
  const startYear = dayjs(startDate, "DD-MM-YYYY").year();

  const {
    includeStartYear = true,
    yearsAfterCurrent = 0,
    sort = "asc",
  } = options || {};

  const yearOptions: YearOption[] = [];
  const endYear = currentYear + yearsAfterCurrent;

  const firstYear = includeStartYear ? startYear : startYear + 1;
  const lastYear = endYear;
  // -1 year for current year
  for (let year = firstYear; year <= lastYear; year++) {
    yearOptions.push({
      label: year.toString(),
      value: year.toString(),
    });
  }

  if (sort === "desc") {
    yearOptions.sort((a, b) => parseInt(b.value) - parseInt(a.value));
  }

  return yearOptions;
}

function PenyataTahunanBranchDetail() {
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const hasUpdatePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
      .CAWANGAN.children.PENYATA_TAHUNAN_CAWANGAN.label,
    pageAccessEnum.Update
  );
  const { id } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [societyName, setSocietyName] = useState("");
  const [societyNo, setSocietyNo] = useState("");
  const [societyId, setSocietyId] = useState("");
  const [branchName, setBranchName] = useState("");
  const [branchNo, setBranchNo] = useState("");
  const [city, setCity] = useState("");
  const [districtCode, setDistrictCode] = useState("");
  const [stateCode, setStateCode] = useState("");
  const [address, setAddress] = useState("");
  const [categoryLabel, setCategoryLabel] = useState("");
  const [subCategoryLabel, setSubCategoryLabel] = useState("");
  const [postcode, setPostcode] = useState("");
  const [email, setEmail] = useState("");
  const categories = getLocalStorage("category_list", []);
  const { state } = useLocation();
  const [currentBranchId, setCurrentBranchId] = useState("");
  const recordData = state.data;
  const isView = state.isView;
  const statementId = recordData?.statementId;
  const [yearSelected, setYearSelected] = useState("");
  const addressList = getLocalStorage("address_list", []);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [approvedDate, setApprovedDate] = useState("");
  const [isSuccess, setIsSuccess] = useState(false);
  const [registeredDate, setRegisteredDate] = useState("");

  const { fetch: changeYear, isLoading } = useMutation({
    url: "society/statement/internal/changeStatementYear",
    onSuccess: () => {
      setIsSuccess(true);
      navigate(-1);
    },
  });

  const onSubmit = () => setIsDialogOpen(true);
  const handleDialogClose = () => setIsDialogOpen(false);
  const handleFormSubmit = () => {
    const value = {
      societyId: societyId,
      branchId: currentBranchId,
      statementId: statementId,
      statementYear: yearSelected,
    };
    changeYear(value);
    setIsDialogOpen(false);
  };
  const {
    data: branchDetailResponse,
    isLoading: getBranchDetailIsLoading,
    refetch: fetchBranch,
  } = useQuery({
    url: `society/branch/getBranchesByParam`,
    filters: [
      { field: "branchId", operator: "eq", value: recordData?.branchId },
    ],
    onSuccess: (responseData) => { 
      const {
        societyName,
        societyNo,
        subCategoryCode,
        categoryCodeJppm,
        city,
        districtCode,
        stateCode,
        address,
        postcode,
        email,
        id,
        paymentDate,
      } = responseData?.data?.data?.data?.[0];
      const Categorylabel = categoryCodeJppm
        ? categories.find((cat: any) => cat.id === parseInt(categoryCodeJppm))
            ?.categoryNameBm
        : "-";

      const subCategoryLabel = subCategoryCode
        ? categories.find((cat: any) => cat.id === parseInt(subCategoryCode))
            ?.categoryNameBm
        : "-";
      const formattedDate = dayjs(paymentDate).format("DD-MM-YYYY");

      setCategoryLabel(Categorylabel);
      setSubCategoryLabel(subCategoryLabel);
      setSocietyName(societyName);
      setSocietyNo(societyNo);
      setAddress(address);
      setCity(city);
      setDistrictCode(districtCode);
      setStateCode(stateCode);
      setPostcode(postcode);
      setEmail(email);
      setSocietyId(id);
      setRegisteredDate(formattedDate);
    },
  });

  const { data, isLoading: societyDataIsLoading } = useCustom({
    url: `${API_URL}/society/${societyId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !!societyId,
      onSuccess: (responseData) => {
        const { subCategoryCode, categoryCodeJppm, email, approvedDate } =
          responseData?.data?.data;
        const Categorylabel = categoryCodeJppm
          ? categories.find((cat: any) => cat.id === parseInt(categoryCodeJppm))
              ?.categoryNameBm
          : "-";

        const subCategoryLabel = subCategoryCode
          ? categories.find((cat: any) => cat.id === parseInt(subCategoryCode))
              ?.categoryNameBm
          : "-";
        setCategoryLabel(Categorylabel);
        setSubCategoryLabel(subCategoryLabel);
        setEmail(email);
        setApprovedDate(approvedDate);
      },
    },
  });

  const getStateName = (stateCode: any) => {
    const stateName = addressList.filter(
      (i: any) => Number(i.id) === Number(stateCode)
    );
    return stateName[0]?.name;
  };

  const getDistrict = (val: null | string | number = null) => {
    const address = addressList
      .filter((items: any) => items.id === Number(val))
      .map((item: any) => ({ value: item.id, label: item.name }));

    return address?.[0]?.label || "-";
  };

  const {
    data: DownloadStatement,
    isLoading: isDownloadtatementLoading,
    refetch: fetchDownloadStatement,
  } = useQuery({
    url: `society/statement/exportPdfV2`,
    filters: [
      { field: "societyId", value: recordData?.societyId, operator: "eq" },
      { field: "branchId", value: recordData?.branchId, operator: "eq" },
      { field: "statementId", value: statementId, operator: "eq" },
    ],
    autoFetch: false,
    onSuccess: (data) => {
      const file = data?.data as unknown as string;
      downloadFile({
        data: file,
        name: `ANNUAL STATEMENT ${recordData?.statementYear}`,
      });
    },
  });

  const handleCetak = () => {
    fetchDownloadStatement();
  };

  const yearOptionsList = generateYearOptions(registeredDate, {
    includeStartYear: false,
  });

  if (getBranchDetailIsLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "300px",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            <Typography
              fontWeight="500 !important"
              fontSize="14px"
              lineHeight="21px"
            >
              {branchName}
              <br />
              {branchNo}
            </Typography>
          </Box>
        </Box>
      </Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            backgroundColor: "white",
            padding: "18px 16px",
            borderRadius: "14px",
            marginBottom: 1,
          }}
        >
          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Typography
              fontSize="16px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("organizationInformation")}
            </Typography>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("organizationCategory")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={categoryLabel} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("organizationSubCategory2")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={subCategoryLabel} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("alamatTempatUrusan")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={address} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("negeri")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={getStateName(stateCode)} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("daerah")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={getDistrict(districtCode)} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("bandar")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={city} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("poskod")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={postcode} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("emel")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={email} />
              </Grid>
            </Grid>
          </Box>

          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Typography
              fontSize="16px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("maklumatTahunPenyata")}
            </Typography>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("tahunPenyataSemasa")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={recordData?.statementYear} />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("annualStatementRecord")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <Button
                  variant="outlined"
                  disabled={societyDataIsLoading}
                  startIcon={<DokumenIcon />}
                  onClick={handleCetak}
                  fullWidth
                  sx={{
                    textTransform: "none", // Prevents text from being uppercase
                    borderRadius: "8px", // Optional: Rounded corners
                    fontWeight: 500, // Optional: Custom font weight
                  }}
                >
                  {t("paparPeyataTahunan")}
                </Button>
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="start">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("tahunPenyata")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <FormControl fullWidth>
                  <Select
                    size="small"
                    sx={{
                      backgroundColor: isView ? "#E8E9E8" : undefined,
                    }}
                    disabled={!hasUpdatePermission || !registeredDate || isView}
                    value={yearSelected}
                    onChange={(e) => setYearSelected(e.target.value)}
                    displayEmpty
                  >
                    {yearOptionsList &&
                      yearOptionsList.map((i) => {
                        return <MenuItem value={i.value}>{i.value}</MenuItem>;
                      })}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonOutline onClick={() => navigate(-1)}>
              {t("back")}
            </ButtonOutline>
            <ButtonPrimary
              disabled={!hasUpdatePermission || !yearSelected}
              onClick={() => onSubmit()}
            >
              {t("update")}
            </ButtonPrimary>
          </Grid>
        </Box>
      </Box>

      <DialogConfirmation
        open={isDialogOpen}
        onClose={handleDialogClose}
        onAction={handleFormSubmit}
        isMutating={isLoading}
        onConfirmationText={t("permohonanConfirmation")}
        onSuccessText={t("applicationSuccessSubmited")}
        isSuccess={isSuccess}
      />
    </>
  );
}

export default PenyataTahunanBranchDetail;
