import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { handleSaveValue } from "../helper/handleSaveValue";
import { handleSaveContent } from "../helper/handleSaveContent";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import { RegExNumbers, RegExText } from "../../../../../helpers/enums";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import { formatAndValidateNumber } from "@/helpers";
import CustomPopover from "@/components/popover";

interface FasalContentSembilanFaedahProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

const renderTitle = (title: string) => {
  const { t } = useTranslation();
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  return (
    <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
      <Typography sx={labelStyle}>
        {title}{" "}
        <Typography sx={{ display: "inline", color: "red" }}>*</Typography>
      </Typography>
      <CustomPopover
        customStyles={{ maxWidth: "250px" }}
        content={
          <Typography sx={{ color: "#666666" }}>{t("max2000")}</Typography>
        }
      />
    </Box>
  );
};

export const FasalContentSembilanFaedah: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");

  const [bantuanKelahiranAnakPertama, setBantuanKelahiranAnakPertama] =
    useState("");
  const [bantuanKelahiranAnakPertamaKata, setBantuanKelahiranAnakPertamaKata] =
    useState("");
  const [bantuanKelahiranAnakKedua, setBantuanKelahiranAnakKedua] =
    useState("");
  const [bantuanKelahiranAnakKeduaKata, setBantuanKelahiranAnakKeduaKata] =
    useState("");
  const [bantuanKelahiranAnakKetiga, setBantuanKelahiranAnakKetiga] =
    useState("");
  const [bantuanKelahiranAnakKetigaKata, setBantuanKelahiranAnakKetigaKata] =
    useState("");
  const [bantuanKelahiranAnakSeterusnya, setBantuanKelahiranAnakSeterusnya] =
    useState("");
  const [
    bantuanKelahiranAnakSeterusnyaKata,
    setBantuanKelahiranAnakSeterusnyaKata,
  ] = useState("");
  const [bantuanKematianAhliSendiri, setBantuanKematianAhliSendiri] =
    useState("");
  const [bantuanKematianAhliSendiriKata, setBantuanKematianAhliSendiriKata] =
    useState("");
  const [bantuanKematianSuamiIsteriAhli, setBantuanKematianSuamiIsteriAhli] =
    useState("");
  const [
    bantuanKematianSuamiIsteriAhliKata,
    setBantuanKematianSuamiIsteriAhliKata,
  ] = useState("");
  const [bantuanKematianAnakAhli, setBantuanKematianAnakAhli] = useState("");
  const [bantuanKematianAnakAhliKata, setBantuanKematianAnakAhliKata] =
    useState("");
  const [bantuanKematianIbuBapaAhli, setBantuanKematianIbuBapaAhli] =
    useState("");
  const [bantuanKematianIbuBapaAhliKata, setBantuanKematianIbuBapaAhliKata] =
    useState("");
  const [bantuanKematianNenekDatukAhli, setBantuanKematianNenekDatukAhli] =
    useState("");
  const [
    bantuanKematianNenekDatukAhliKata,
    setBantuanKematianNenekDatukAhliKata,
  ] = useState("");
  const [bantuanPengebumian, setBantuanPengebumian] = useState("");
  const [bantuanPengebumianKata, setBantuanPengebumianKata] = useState("");
  const [bantuanNafkahAhliYangKeuzuran, setBantuanNafkahAhliYangKeuzuran] =
    useState("");
  const [
    bantuanNafkahAhliYangKeuzuranKata,
    setBantuanNafkahAhliYangKeuzuranKata,
  ] = useState("");
  const [
    bantuanNafkahAhliYangMenjadiBalu,
    setBantuanNafkahAhliYangMenjadiBalu,
  ] = useState("");
  const [
    bantuanNafkahAhliYangMenjadiBaluKata,
    setBantuanNafkahAhliYangMenjadiBaluKata,
  ] = useState("");
  const [
    bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun,
    setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun,
  ] = useState("");
  const [
    bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata,
    setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata,
  ] = useState("");

  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const limitAmount = 2000;
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  //const clause9 = localStorage.getItem("clause9");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause = JSON.parse(clause9);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause3Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      if(clause.constitutionValues.length > 0){ 
      const fieldMappings: Record<string, (value: string) => void> = {
        // Kelahiran
        "Bantuan Kelahiran Anak Pertama": setBantuanKelahiranAnakPertama,
        "Bantuan Kelahiran Anak Pertama (Dalam Perkataan)": setBantuanKelahiranAnakPertamaKata,
        "Bantuan Kelahiran Anak Kedua": setBantuanKelahiranAnakKedua,
        "Bantuan Kelahiran Anak Kedua (Dalam Perkataan)": setBantuanKelahiranAnakKeduaKata,
        "Bantuan Kelahiran Anak Ketiga": setBantuanKelahiranAnakKetiga,
        "Bantuan Kelahiran Anak Ketiga (Dalam Perkataan)": setBantuanKelahiranAnakKetigaKata,
        "Bantuan Kelahiran Anak Seterusnya": setBantuanKelahiranAnakSeterusnya,
        "Bantuan Kelahiran Anak Seterusnya (Dalam Perkataan)": setBantuanKelahiranAnakSeterusnyaKata,
      
        // Kematian
        "Bantuan Kematian Ahli Sendiri": setBantuanKematianAhliSendiri,
        "Bantuan Kematian Ahli Sendiri (Dalam Perkataan)": setBantuanKematianAhliSendiriKata,
        "Bantuan Kematian Suami/Isteri Ahli": setBantuanKematianSuamiIsteriAhli,
        "Bantuan Kematian Suami/Isteri Ahli (Dalam Perkataan)": setBantuanKematianSuamiIsteriAhliKata,
        "Bantuan Kematian Anak Ahli": setBantuanKematianAnakAhli,
        "Bantuan Kematian Anak Ahli (Dalam Perkataan)": setBantuanKematianAnakAhliKata,
        "Bantuan Kematian Ibu/Bapa Ahli": setBantuanKematianIbuBapaAhli,
        "Bantuan Kematian Ibu/Bapa Ahli (Dalam Perkataan)": setBantuanKematianIbuBapaAhliKata,
        "Bantuan Kematian Nenek/Datuk Ahli": setBantuanKematianNenekDatukAhli,
        "Bantuan Kematian Nenek/Datuk Ahli (Dalam Perkataan)": setBantuanKematianNenekDatukAhliKata,
        "Bantuan Pengebumian": setBantuanPengebumian,
        "Bantuan Pengebumian (Dalam Perkataan)": setBantuanPengebumianKata,
      
        // Nafkah
        "Bantuan Nafkah Ahli Yang Keuzuran": setBantuanNafkahAhliYangKeuzuran,
        "Bantuan Nafkah Ahli Yang Keuzuran (Dalam Perkataan)": setBantuanNafkahAhliYangKeuzuranKata,
        "Bantuan Nafkah Ahli Yang Menjadi Balu": setBantuanNafkahAhliYangMenjadiBalu,
        "Bantuan Nafkah Ahli Yang Menjadi Balu (Dalam Perkataan)": setBantuanNafkahAhliYangMenjadiBaluKata,
        "Bantuan Nafkah Ahli Balu Yang Memiliki Anak Dibawah Umur 12 Tahun": setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun,
        "Bantuan Nafkah Ahli Balu Yang Memiliki Anak Dibawah Umur 12 Tahun (Dalam Perkataan)": setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata
      };

      if(clause.constitutionValues){
        clause.constitutionValues.forEach((item:any) => {
          const setter = fieldMappings[item.titleName];
          if (setter && item.definitionName) {
            setter(item.definitionName);
          }
        });
      } 
      setIsEdit(clause.edit);
    }
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id } = useParams();

  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak pertama>>/gi,
    `<b>${
      bantuanKelahiranAnakPertama || "<<bantuan kelahiran anak pertama>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak pertama-dalam perkataan>>/gi,
    `<b>${
      bantuanKelahiranAnakPertamaKata ||
      "<<bantuan kelahiran anak pertama-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak kedua>>/gi,
    `<b>${bantuanKelahiranAnakKedua || "<<bantuan kelahiran anak kedua>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak kedua-dalam perkataan>>/gi,
    `<b>${
      bantuanKelahiranAnakKeduaKata ||
      "<<bantuan kelahiran anak kedua-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak ketiga>>/gi,
    `<b>${
      bantuanKelahiranAnakKetiga || "<<bantuan kelahiran anak ketiga>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak ketiga-dalam perkataan>>/gi,
    `<b>${
      bantuanKelahiranAnakKetigaKata ||
      "<<bantuan kelahiran anak ketiga-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak seterusnya>>/gi,
    `<b>${
      bantuanKelahiranAnakSeterusnya || "<<bantuan kelahiran anak seterusnya>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak seterusnya-dalam perkataan>>/gi,
    `<b>${
      bantuanKelahiranAnakSeterusnyaKata ||
      "<<bantuan kelahiran anak seterusnya-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian ahli sendiri>>/gi,
    `<b>${
      bantuanKematianAhliSendiri || "<<bantuan kematian ahli sendiri>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian ahli sendiri-dalam perkataan>>/gi,
    `<b>${
      bantuanKematianAhliSendiriKata ||
      "<<bantuan kematian ahli sendiri-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian suami\/isteri ahli>>/gi,
    `<b>${
      bantuanKematianSuamiIsteriAhli || "<<bantuan kematian suami/isteri ahli>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian suami\/isteri ahli-dalam perkataan>>/gi,
    `<b>${
      bantuanKematianSuamiIsteriAhliKata ||
      "<<bantuan kematian suami/isteri ahli-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian anak ahli>>/gi,
    `<b>${bantuanKematianAnakAhli || "<<bantuan kematian anak ahli>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian anak ahli-dalam perkataan>>/gi,
    `<b>${
      bantuanKematianAnakAhliKata ||
      "<<bantuan kematian anak ahli-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian ibu\/bapa ahli>>/gi,
    `<b>${
      bantuanKematianIbuBapaAhli || "<<bantuan kematian ibu/bapa ahli>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian ibu\/bapa ahli-dalam perkataan>>/gi,
    `<b>${
      bantuanKematianIbuBapaAhliKata ||
      "<<bantuan kematian ibu/bapa ahli-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian nenek\/datuk ahli>>/gi,
    `<b>${
      bantuanKematianNenekDatukAhli || "<<bantuan kematian nenek/datuk ahli>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian nenek\/datuk ahli-dalam perkataan>>/gi,
    `<b>${
      bantuanKematianNenekDatukAhliKata ||
      "<<bantuan kematian nenek/datuk ahli-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan pengebumian>>/gi,
    `<b>${bantuanPengebumian || "<<bantuan pengebumian>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan pengebumian-dalam perkataan>>/gi,
    `<b>${
      bantuanPengebumianKata || "<<bantuan pengebumian-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan\/nafkah ahli yang menjadi balu>>/gi,
    `<b>${
      bantuanNafkahAhliYangMenjadiBalu ||
      "<<bantuan/nafkah ahli yang menjadi balu>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan\/nafkah ahli yang menjadi balu-dalam perkataan>>/gi,
    `<b>${
      bantuanNafkahAhliYangMenjadiBaluKata ||
      "<<bantuan nafkah ahli yang menjadi balu-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan\/nafkah ahli yang keuzuran>>/gi,
    `<b>${
      bantuanNafkahAhliYangKeuzuran || "<<bantuan nafkah ahli yang keuzuran>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan\/nafkah ahli yang keuzuran-dalam perkataan>>/gi,
    `<b>${
      bantuanNafkahAhliYangKeuzuranKata ||
      "<<bantuan/nafkah ahli yang keuzuran-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan\/nafkah ahli balu yang mempunyai anak di bawah umur 12 tahun>>/gi,
    `<b>${
      bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun ||
      "<<bantuan/nafkah ahli balu yang mempunyai anak di bawah umur 12 tahun>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan\/nafkah ahli balu yang mempunyai anak di bawah umur 12 tahun-dalam perkataan>>/gi,
    `<b>${
      bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata ||
      "<<bantuan/nafkah ahli balu yang mempunyai anak di bawah umur 12 tahun-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  const parser = new DOMParser();

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKelahiranAnakPertama")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKelahiranAnakPertama"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanKelahiranAnakPertama}
              error={!!formErrors.bantuanKelahiranAnakPertama}
              helperText={formErrors.bantuanKelahiranAnakPertama}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKelahiranAnakPertama(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakPertama:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakPertama: "",
                    }));
                  }
                } else {
                  setBantuanKelahiranAnakPertama("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanKelahiranAnakPertamaKata}
              placeholder="contoh : dua puluh."
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKelahiranAnakPertamaKata(e.target.value);
                } else {
                  setBantuanKelahiranAnakPertamaKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKelahiranAnakKedua")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKelahiranAnakKedua"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanKelahiranAnakKedua}
              error={!!formErrors.bantuanKelahiranAnakKedua}
              helperText={formErrors.bantuanKelahiranAnakKedua}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakKedua:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakKedua: "",
                    }));
                  }
                  setBantuanKelahiranAnakKedua(formattedValue);
                } else {
                  setBantuanKelahiranAnakKedua("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanKelahiranAnakKeduaKata}
              placeholder="contoh : dua puluh."
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKelahiranAnakKeduaKata(e.target.value);
                } else {
                  setBantuanKelahiranAnakKeduaKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKelahiranAnakKetiga")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKelahiranAnakKetiga"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanKelahiranAnakKetiga}
              error={!!formErrors.bantuanKelahiranAnakKetiga}
              helperText={formErrors.bantuanKelahiranAnakKetiga}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKelahiranAnakKetiga(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakKetiga:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakKetiga: "",
                    }));
                  }
                } else {
                  setBantuanKelahiranAnakKetiga("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanKelahiranAnakKetigaKata}
              placeholder="contoh : dua puluh."
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKelahiranAnakKetigaKata(e.target.value);
                } else {
                  setBantuanKelahiranAnakKetigaKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKelahiranAnakSeterusnya")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKelahiranAnakSeterusnya"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanKelahiranAnakSeterusnya}
              error={!!formErrors.bantuanKelahiranAnakSeterusnya}
              helperText={formErrors.bantuanKelahiranAnakSeterusnya}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKelahiranAnakSeterusnya(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakSeterusnya:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakSeterusnya: "",
                    }));
                  }
                } else {
                  setBantuanKelahiranAnakSeterusnya("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanKelahiranAnakSeterusnyaKata}
              placeholder="contoh : dua puluh."
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKelahiranAnakSeterusnyaKata(e.target.value);
                } else {
                  setBantuanKelahiranAnakSeterusnyaKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKematianAhliSendiri")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKematianAhliSendiri"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanKematianAhliSendiri}
              error={!!formErrors.bantuanKematianAhliSendiri}
              helperText={formErrors.bantuanKematianAhliSendiri}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKematianAhliSendiri(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianAhliSendiri:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianAhliSendiri: "",
                    }));
                  }
                } else {
                  setBantuanKematianAhliSendiri("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh."
              value={bantuanKematianAhliSendiriKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKematianAhliSendiriKata(e.target.value);
                } else {
                  setBantuanKematianAhliSendiriKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKematianPasanganAhli")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKematianPasanganAhli"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanKematianSuamiIsteriAhli}
              error={!!formErrors.bantuanKematianSuamiIsteriAhli}
              helperText={formErrors.bantuanKematianSuamiIsteriAhli}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKematianSuamiIsteriAhli(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianSuamiIsteriAhli:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianSuamiIsteriAhli: "",
                    }));
                  }
                } else {
                  setBantuanKematianSuamiIsteriAhli("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh."
              value={bantuanKematianSuamiIsteriAhliKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKematianSuamiIsteriAhliKata(e.target.value);
                } else {
                  setBantuanKematianSuamiIsteriAhliKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKematianAnakAhli")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKematianAnakAhli"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanKematianAnakAhli}
              error={!!formErrors.bantuanKematianAnakAhli}
              helperText={formErrors.bantuanKematianAnakAhli}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKematianAnakAhli(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianAnakAhli:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianAnakAhli: "",
                    }));
                  }
                } else {
                  setBantuanKematianAnakAhli("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh."
              value={bantuanKematianAnakAhliKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKematianAnakAhliKata(e.target.value);
                } else {
                  setBantuanKematianAnakAhliKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKematianIbuBapaAhli")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKematianIbuBapaAhli"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanKematianIbuBapaAhli}
              error={!!formErrors.bantuanKematianIbuBapaAhli}
              helperText={formErrors.bantuanKematianIbuBapaAhli}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKematianIbuBapaAhli(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianIbuBapaAhli:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianIbuBapaAhli: "",
                    }));
                  }
                } else {
                  setBantuanKematianIbuBapaAhli("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh."
              value={bantuanKematianIbuBapaAhliKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKematianIbuBapaAhliKata(e.target.value);
                } else {
                  setBantuanKematianIbuBapaAhliKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKematianNenekDatukAhli")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKematianNenekDatukAhli"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanKematianNenekDatukAhli}
              error={!!formErrors.bantuanKematianNenekDatukAhli}
              helperText={formErrors.bantuanKematianNenekDatukAhli}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKematianNenekDatukAhli(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianNenekDatukAhli:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianNenekDatukAhli: "",
                    }));
                  }
                } else {
                  setBantuanKematianNenekDatukAhli("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh."
              value={bantuanKematianNenekDatukAhliKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKematianNenekDatukAhliKata(e.target.value);
                } else {
                  setBantuanKematianNenekDatukAhliKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanPengebumian")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanPengebumian"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanPengebumian}
              error={!!formErrors.bantuanPengebumian}
              helperText={formErrors.bantuanPengebumian}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanPengebumian(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanPengebumian: "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanPengebumian: "",
                    }));
                  }
                } else {
                  setBantuanPengebumian("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh."
              value={bantuanPengebumianKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanPengebumianKata(e.target.value);
                } else {
                  setBantuanPengebumianKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanNafkahAhliKeuzuran")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanNafkahAhliKeuzuran"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanNafkahAhliYangKeuzuran}
              error={!!formErrors.bantuanNafkahAhliYangKeuzuran}
              helperText={formErrors.bantuanNafkahAhliYangKeuzuran}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanNafkahAhliYangKeuzuran(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanNafkahAhliYangKeuzuran:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanNafkahAhliYangKeuzuran: "",
                    }));
                  }
                } else {
                  setBantuanNafkahAhliYangKeuzuran("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh."
              value={bantuanNafkahAhliYangKeuzuranKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanNafkahAhliYangKeuzuranKata(e.target.value);
                } else {
                  setBantuanNafkahAhliYangKeuzuranKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanNafkahAhliBalu")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanNafkahAhliBalu"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanNafkahAhliYangMenjadiBalu}
              error={!!formErrors.bantuanNafkahAhliYangMenjadiBalu}
              helperText={formErrors.bantuanNafkahAhliYangMenjadiBalu}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanNafkahAhliYangMenjadiBalu(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanNafkahAhliYangMenjadiBalu:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanNafkahAhliYangMenjadiBalu: "",
                    }));
                  }
                } else {
                  setBantuanNafkahAhliYangMenjadiBalu("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh."
              value={bantuanNafkahAhliYangMenjadiBaluKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanNafkahAhliYangMenjadiBaluKata(e.target.value);
                } else {
                  setBantuanNafkahAhliYangMenjadiBaluKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanNafkahAhliBaluAnak")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanNafkahAhliBaluAnak"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun}
              error={
                !!formErrors.bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun
              }
              helperText={
                formErrors.bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun
              }
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun(
                    formattedValue
                  );
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun:
                        "",
                    }));
                  }
                } else {
                  setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun(
                    ""
                  );
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              value={
                bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata
              }
              placeholder="contoh : dua puluh."
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata(
                    e.target.value
                  );
                } else {
                  setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata(
                    ""
                  );
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKelahiranAnakPertama,
                  titleName: "Bantuan Kelahiran Anak Pertama",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKelahiranAnakPertamaKata,
                  titleName: "Bantuan Kelahiran Anak Pertama (Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKelahiranAnakKedua,
                  titleName: "Bantuan Kelahiran Anak Kedua",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKelahiranAnakKeduaKata,
                  titleName: "Bantuan Kelahiran Anak Kedua (Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKelahiranAnakKetiga,
                  titleName: "Bantuan Kelahiran Anak Ketiga",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKelahiranAnakKetigaKata,
                  titleName: "Bantuan Kelahiran Anak Ketiga (Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKelahiranAnakSeterusnya,
                  titleName: "Bantuan Kelahiran Anak Seterusnya",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKelahiranAnakSeterusnyaKata,
                  titleName:
                    "Bantuan Kelahiran Anak Seterusnya (Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKematianAhliSendiri,
                  titleName: "Bantuan Kematian Ahli Sendiri",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKematianAhliSendiriKata,
                  titleName: "Bantuan Kematian Ahli Sendiri (Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKematianSuamiIsteriAhli,
                  titleName: "Bantuan Kematian Suami/Isteri Ahli",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKematianSuamiIsteriAhliKata,
                  titleName:
                    "Bantuan Kematian Suami/Isteri Ahli (Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKematianAnakAhli,
                  titleName: "Bantuan Kematian Anak Ahli",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKematianAnakAhliKata,
                  titleName: "Bantuan Kematian Anak Ahli (Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKematianIbuBapaAhli,
                  titleName: "Bantuan Kematian Ibu/Bapa Ahli",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKematianIbuBapaAhliKata,
                  titleName: "Bantuan Kematian Ibu/Bapa Ahli (Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKematianNenekDatukAhli,
                  titleName: "Bantuan Kematian Nenek/Datuk Ahli",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanKematianNenekDatukAhliKata,
                  titleName:
                    "Bantuan Kematian Nenek/Datuk Ahli (Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanPengebumian,
                  titleName: "Bantuan Pengebumian",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanPengebumianKata,
                  titleName: "Bantuan Pengebumian (Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanNafkahAhliYangKeuzuran,
                  titleName: "Bantuan Nafkah Ahli Yang Keuzuran",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanNafkahAhliYangKeuzuranKata,
                  titleName:
                    "Bantuan Nafkah Ahli Yang Keuzuran (Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanNafkahAhliYangMenjadiBalu,
                  titleName: "Bantuan Nafkah Ahli Yang Menjadi Balu",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bantuanNafkahAhliYangMenjadiBaluKata,
                  titleName:
                    "Bantuan Nafkah Ahli Yang Menjadi Balu (Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName:
                    bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun,
                  titleName:
                    "Bantuan Nafkah Ahli Balu Yang Memiliki Anak Dibawah Umur 12 Tahun",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName:
                    bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata,
                  titleName:
                    "Bantuan Nafkah Ahli Balu Yang Memiliki Anak Dibawah Umur 12 Tahun (Dalam Perkataan)",
                },
              ],
              clause: "clause9",
              clauseCount: 9,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentSembilanFaedah;
