export interface ISubmittedStatement {
  societyId: string | number;
  branchId: string | number;
  year: number;
  statementId: string | number;
  submitted: boolean;
}

export interface IStatementFinancial {
  id: string | number;
  financialDeclaration: boolean;
  statementId: string | number;
  societyId: string | number;
  societyNo: string;
  branchId: string | number;
  branchNo: any;
  feeIncome: any;
  feeIncomeMember: any;
  fundIncome: any;
  donationIncome: any;
  othersIncome: any;
  foodRevenue: any;
  bookRevenue: any;
  serviceRevenue: any;
  otherRevenue: any;
  rentalInvestment: any;
  dividendInvestment: any;
  interestInvestment: any;
  propertyInvestment: any;
  otherInvestment: any;
  govGrant: any;
  privateGrant: any;
  individualGrant: any;
  otherGrant: any;
  otherIncome: any;
  totalIncome: number;
  sumbExpense: any;
  donationExpense: any;
  taxExpense: any;
  otherExpenses: any;
  generalWelfare: any;
  deathWelfare: any;
  giftWelfare: any;
  scholarshipWelfare: any;
  zakatWelfare: any;
  donationWelfare: any;
  organizedActivity: any;
  promoActivity: any;
  banquetActivity: any;
  tourActivity: any;
  rentalCost: any;
  utilityCost: any;
  supplyCost: any;
  otherCost: any;
  otherExpense: any;
  totalExpense: number;
  buildingAsset: any;
  investmentAsset: any;
  intangibleAsset: any;
  assetOnHand: any;
  bankAsset: any;
  accountAsset: any;
  inventoryAsset: any;
  investAsset: any;
  depositAsset: any;
  taxAsset: any;
  totalAsset: number;
  creditorLiability: any;
  taxLiability: any;
  loanLiability: any;
  debtLiability: any;
  deferredTaxLiability: any;
  borrowLiability: any;
  totalLiability: number;
  applicationStatusCode: string;
  createdBy: string;
  createdDate: number[];
  modifiedBy: string;
  modifiedDate: number[];
  cardCost: any;
  investmentActivity: any;
  feeActivity: any;
  otherActivity: any;
  salaryCost: any;
  bonusCost: any;
  kwspCost: any;
  insuranceCost: any;
  maintenanceCost: any;
  renovationCost: any;
  transportationCost: any;
  photocopyCost: any;
  bankChargeCost: any;
  assetNo: any;
  landAsset: any;
  vehicleAsset: any;
  machineAsset: any;
  furnitureAsset: any;
  officeAsset: any;
  uniformCost: any;
  expenseNo: any;
}
