import { Box, Grid, Theme, Typography, useMediaQuery } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useContext, useEffect, useState } from "react";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { SearchContext } from "../../../../../contexts/searchProvider";
import {
  ListUserPengurusanStatus,
  ListUserStatus,
  NEW_PermissionNames,
  pageAccessEnum,
} from "../../../../../helpers/enums";
import Input from "../../../../../components/input/Input";
import { API_URL } from "../../../../../api";
import { useCustom } from "@refinedev/core";
import { capitalizeWords, useQuery } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";

interface FormValues {
  userId?: string | number | null;
  jppmBranchId?: string | number | null;
  status?: string | null;
  name?: string | null;
  userRole?: string | null;
}

interface list {
  value: any;
  label: any;
}

function SearchPengguna() {
  const { t } = useTranslation();
  const {
    setPage,
    setPageSize,
    setPagePending,
    setPageSizePending,
    page,
    pageSize,
    pageInactive,
    setPageInactive,
    pageSizeInactive,
    setPageSizeInactive,
    setSearchResult,
    pagePending,
    pageSizePending,
    setSearchPendingResult,
    searchInactiveResult,
    setSearchInactiveResult,
  } = useContext(SearchContext);

  const hasRoPermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.SELENGGARA.children.PENGURUSAN_PENGGUNA.children
      .SENARAI_PENGGUNA_JPPM_MENUNGGU_KEPUTUSAN.label,
    pageAccessEnum.Read
  );

  function FetchUsers(reset: boolean) {
    fetch(`${API_URL}/user/admin/getUsers`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        portal: localStorage.getItem("portal") || "",
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      body: reset
        ? JSON.stringify({
            jppmBranchId: null,
            status: null,
            name: null,
            userRole: null,
            pageNo: page,
            pageSize: pageSize,
          })
        : JSON.stringify({ ...formValues, pageNo: page, pageSize: pageSize }),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Erorr when fetching users");
        }
        return response.json();
      })
      .then((data) => {
        setSearchResult(data);
      })
      .catch((error) => {
        console.error("Error:", error);
      });
  }

  function FetchPendingUsers(reset: boolean) {
    fetch(`${API_URL}/user/admin/getUsers`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        portal: localStorage.getItem("portal") || "",
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      body: reset
        ? JSON.stringify({
            jppmBranchId: null,
            status: "2",
            name: null,
            userRole: null,
            pageNo: pagePending,
            pageSize: pageSizePending,
          })
        : JSON.stringify({
            userId: formValues.userId,
            jppmBranchId: formValues.jppmBranchId,
            name: formValues.name,
            userRole: formValues.userRole,
            status: "2",
            pageNo: pagePending,
            pageSize: pageSizePending,
          }),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Erorr when fetching users");
        }
        return response.json();
      })
      .then((data) => {
        setSearchPendingResult(data);
      })
      .catch((error) => {
        console.error("Error:", error);
      });
  }

  function FetchInactiveUsers(reset: boolean) {
    fetch(`${API_URL}/user/admin/getUsers`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        portal: localStorage.getItem("portal") || "",
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      body: reset
        ? JSON.stringify({
            jppmBranchId: null,
            status: "-1",
            name: null,
            userRole: null,
            pageNo: pageInactive,
            pageSize: pageSizeInactive,
          })
        : JSON.stringify({
            userId: formValues.userId,
            jppmBranchId: formValues.jppmBranchId,
            name: formValues.name,
            userRole: formValues.userRole,
            status: "-1",
            pageNo: pageInactive,
            pageSize: pageSizeInactive,
          }),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Erorr when fetching inactive users");
        }
        return response.json();
      })
      .then((data) => {
        setSearchInactiveResult(data);
      })
      .catch((error) => {
        console.error("Error:", error);
      });
  }

  useEffect(() => {
    if (hasRoPermission) {
      FetchPendingUsers(false);
    }
  }, [pagePending, pageSizePending, hasRoPermission]);

  useEffect(() => {
    FetchUsers(false);
  }, [page, pageSize]);

  useEffect(() => {
    FetchInactiveUsers(false);
  }, [pageInactive, pageSizeInactive]);

  const [FilteredPositionList, setFilteredPositionList] = useState<list[]>([]);

  const { data: listUserRoles, isLoading: isLoadingUserRoles } = useCustom({
    url: `${API_URL}/user/userRole/getAllUserRolesList`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      filters: [
        { field: "size", value: 1000, operator: "eq" },
        { field: "page", value: 1, operator: "eq" },
      ],
    },
  });
  useEffect(() => {
    if (listUserRoles?.data?.data?.data) {
      const transformedList = listUserRoles.data.data.data.map((item: any) => ({
        value: item.role,
        label: item.role,
      }));
      setFilteredPositionList(transformedList);
    }
  }, [listUserRoles]);

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const [formValues, setFormValues] = useState<FormValues>({
    userId: null,
    jppmBranchId: null,
    status: null,
    name: null,
    userRole: null,
  });

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setFormValues({
      ...formValues,
      [name!]: value as string,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    setPageSize(5);
    setPagePending(1);
    setPageSizePending(5);
    setPageInactive(1);
    setPageSizeInactive(5);

    const newErrors: { [key in keyof FormValues]?: string } = {};

    if (Object.keys(newErrors).length === 0) {
      const updatedFormValues = Object.fromEntries(
        Object.entries(formValues).map(([key, value]) => [
          key,
          typeof value === "string" && value.trim() === "" ? null : value,
        ])
      );

      setFormValues(updatedFormValues);

      FetchUsers(false);
      if (hasRoPermission) {
        FetchPendingUsers(false);
      }
      FetchInactiveUsers(false);
    }
  };

  const handleClearSearch = () => {
    setFormValues({
      jppmBranchId: null,
      status: null,
      name: null,
      userRole: null,
    });

    FetchUsers(true);
    if (hasRoPermission) {
      FetchPendingUsers(true);
    }
    FetchInactiveUsers(true);
  };

  const [translatedList, setTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);

  useEffect(() => {
    const newList = ListUserPengurusanStatus.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setTranslatedList(newList);
  }, [t]);

  const { data: branchListsResponse, isLoading: isLoadingBranch } = useQuery<{
    data: any;
  }>({
    url: `society/admin/branch/list`,
  });
  const branchLists = branchListsResponse?.data?.data ?? [];
  const branchListOptions = branchLists.map((branch: any) => ({
    value: branch.id,
    label: capitalizeWords(branch.description, null, true),
  }));

  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Typography className={"title"} sx={{ mb: 4 }}>
        {t("maklumatPengguna")}
      </Typography>

      {listUserRoles && (
        <Input
          value={formValues?.userRole ? formValues?.userRole : ""}
          name="userRole"
          onChange={handleChange}
          label={t("kategoriPengguna")}
          options={FilteredPositionList}
          type="select"
          placeholder={t("pleaseSelectCategory")}
        />
      )}

      <Input
        value={formValues.jppmBranchId ? formValues?.jppmBranchId : ""}
        name="jppmBranchId"
        onChange={handleChange}
        label={t("cawanganJPPM")}
        options={branchListOptions}
        type="select"
        placeholder={t("pleaseSelectState")}
      />

      <Input
        value={formValues.status ? formValues?.status : ""}
        name="status"
        onChange={handleChange}
        label={t("status")}
        options={translatedList}
        type="select"
        placeholder={t("pleaseSelectStatus")}
      />

      <Input
        value={formValues.name ? formValues?.name : ""}
        name="name"
        onChange={handleChange}
        label={t("carianNama")}
        placeholder={t("pleaseEnterName")}
      />
      <Grid container mt={3} spacing={2}>
        <Grid
          item
          xs={12}
          sx={{
            mt: 2,
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            justifyContent: "flex-end",
            gap: 1,
          }}
        >
          <ButtonOutline onClick={handleClearSearch}>
            {t("previous")}
          </ButtonOutline>
          <ButtonPrimary type="submit">{t("cari")}</ButtonPrimary>
        </Grid>
      </Grid>
    </Box>
  );
}

export default SearchPengguna;
