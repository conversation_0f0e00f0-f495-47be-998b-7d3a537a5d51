import { useEffect, useState } from "react";
import { useForm, FieldValues } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { globalStyles, useQuery, useQueryFilterForm } from "@/helpers";

import { Box, Typography, IconButton, Button } from "@mui/material";
import { DataTable, IColumn } from "@/components";
import FilterBar from "@/components/filter";
import { SearchGrey } from "@/components/input/SearchGrey";
import { ButtonSmallWithIcon } from "@/components";

import { IApiPaginatedResponse, ISocietyDetail } from "@/types";

import { EditIcon, AddIcon } from "@/components/icons";
import ArrowRightAltIcon from "@mui/icons-material/ArrowRightAlt";

const NotisTab: React.FC = () => {
  const { t, i18n } = useTranslation();
  const classes = globalStyles();
  const navigate = useNavigate();

  const isMyLanguage = i18n.language === "my";

  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string>
  >({
    noticeType: "noticeType",
    status: "status",
  });

  const formMethods = useForm<FieldValues>({
    defaultValues: {
      pageNo: 1,
      pageSize: 5,
      searchQuery: "",
      type: "",
      status: "",
    },
  });
  const { buildFilters } = useQueryFilterForm({
    formMethods,
  });
  const { watch, setValue } = formMethods;

  const pageNo = watch("pageNo");
  const pageSize = watch("pageSize");

  const filterOptions = {
    noticeType: [],
    status: [],
  };

  const columns: IColumn<ISocietyDetail>[] = [
    {
      field: "",
      headerName: "No",
      flex: 1,
      align: "center",
      renderCell: ({ rowIndex }) => {
        const number = pageNo * pageSize + rowIndex + 1 - pageSize;
        return <>{number}</>;
      },
    },
    {
      field: "",
      headerName: "No. PPM",
      flex: 1,
      align: "center",
    },
    {
      field: "",
      headerName: "Jenis Notis",
      flex: 1,
      align: "center",
    },
    {
      field: "",
      headerName: "Tarikh Cipta",
      flex: 1,
      align: "center",
    },
    {
      field: "",
      headerName: "Masa Cipta",
      flex: 1,
      align: "center",
    },
    {
      field: "",
      headerName: t("status"),
      flex: 1,
      align: "center",
      renderCell: () => {
        return (
          <Typography
            className={classes.statusBadge}
            sx={{
              border: "1px solid var(--primary-color)",
            }}
          >
            {isMyLanguage ? "Aktif" : "Active"}
          </Typography>
        );
      },
    },
    {
      field: "",
      headerName: t("action"),
      align: "center",
      renderCell: ({ row }) => {
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <IconButton
              onClick={() => {
                navigate(`cipta-pembatalan-induk/${row.id}`);
              }}
            >
              <EditIcon color="#1DC1C1" />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  const {
    data: societyListResponse,
    refetch: fetchActiveSociety,
    isLoading: isLoadingSocietyList,
  } = useQuery<IApiPaginatedResponse<ISocietyDetail>>({
    url: "society/cancellation/getAllActiveSociety",
    autoFetch: false,
  });

  const societyList = societyListResponse?.data.data?.data ?? [];
  const totalSocietyList = societyListResponse?.data?.data?.total ?? 0;

  const onFilterChange = (filter: string, value: number | string) => {
    setValue("page", 1);
    switch (filter) {
      case "noticeType":
        setValue("noticeType", value);
        break;
      case "status":
        setValue("status", value);
        break;
    }
  };

  const handleSelectedFiltersChange = (
    updatedFilters: Record<string, string>
  ) => setSelectedFilters(updatedFilters);

  const applyFilters = (pageSize: number, page: number) => {
    const filters = buildFilters(pageSize, page);

    fetchActiveSociety({ filters });
  };

  const handleChangePage = (newPage: number) => {
    setValue("pageNo", newPage);
    applyFilters(pageSize, newPage);
  };

  const handleChangePageSize = (newPageSize: number) => {
    setValue("pageSize", newPageSize);
    setValue("pageNo", 1);
    applyFilters(newPageSize, 1);
  };

  useEffect(() => {
    applyFilters(pageSize, 1);
  }, []);

  return (
    <>
      <Box className={classes.section} mb={2}>
        <Box className={classes.sectionBox} mb={2}>
          <Typography className="title" mb={4}>
            {isMyLanguage ? "Senarai Rekod Notis" : "Senarai Rekod Notis"}
          </Typography>

          <SearchGrey
            placeholder={t("carian")}
            onSearchKeyDown={(val) => onFilterChange("searchQuery", val)}
          />

          <FilterBar
            filterOptions={filterOptions}
            onFilterChange={onFilterChange}
            selectedFilters={selectedFilters}
            onSelectedFiltersChange={handleSelectedFiltersChange}
          />
        </Box>
      </Box>

      <Box className={classes.section} mb={2}>
        <Box className={classes.sectionBox} mb={2}>
          <Box
            sx={{
              marginLeft: "auto",
              width: "fit-content",
              marginBottom: 4,
            }}
          >
            <ButtonSmallWithIcon
              icon={<AddIcon color="#ffffff" />}
              onClick={() => navigate("./create")}
            >
              Tambah Notis
            </ButtonSmallWithIcon>
          </Box>

          <DataTable
            columns={columns}
            rows={[]}
            page={pageNo}
            rowsPerPage={pageSize}
            pagination={pageSize > 5}
            totalCount={totalSocietyList}
            isLoading={isLoadingSocietyList}
            onPageChange={handleChangePage}
            onPageSizeChange={handleChangePageSize}
          />

          {!(pageSize > 5) && (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                marginTop: "0.5rem",
              }}
            >
              <Button
                onClick={() => handleChangePageSize(10)}
                className={classes.btnOutline}
                sx={{
                  mt: 3,
                  marginInline: "auto",
                }}
              >
                {t("seeFullView")}
                <ArrowRightAltIcon sx={{ color: "#666666B2" }} />
              </Button>
            </div>
          )}
        </Box>
      </Box>
    </>
  );
};

export default NotisTab;
