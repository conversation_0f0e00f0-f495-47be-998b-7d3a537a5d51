import { createAction, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ModelGeranSeranaiPermohonanPersatuan } from "../models";
import { Dayjs } from "dayjs";

export interface GrantTemplateFieldCreateRequest {
  id: string;
  fieldName: string;
  fieldType: string;
  isRequired: boolean;
  sequenceOrder: number;
  sectionId: string;
  sectionName: string;
  pageNumber: number;
  options?: string[];
  table?: string[][];
}
interface FieldOption {
  key: string;
  value: string;
  row: number;
  col: number;
}

export interface Section {
  id: string;
  name: string;
  pageNumber: number;
  fields: GrantTemplateFieldCreateRequest[];
}

export interface GeranState<
  SelectedOrganizationType extends ModelGeranSeranaiPermohonanPersatuan = ModelGeranSeranaiPermohonanPersatuan
> {
  selectedOrganization: null | SelectedOrganizationType;
  sections: Section[];
  title: string;
  prePublishDate: string | Date | Dayjs | null;
  endDate: string | Date | Dayjs | null;
  statusReport?: string | null;
  categories?: string[];
  subCategories?: string[];
  societyRegistrationDate: string | Date | Dayjs | null;
  categoryPairs?: [number, number][];
  currentGrant?: { id: number | null };
  lastPickedComponentLabel: string | null;
}

const initialState: GeranState = {
  selectedOrganization: null,
  sections: [],
  lastPickedComponentLabel: null,
  title: "",
  prePublishDate: null,
  endDate: null,
  categories: [],
  subCategories: [],
  categoryPairs: [],
  societyRegistrationDate: null,
  statusReport: "",
  currentGrant: { id: null },
};

const name = "geran";

export const setSelectedOrganizationRedux = createAction<
  GeranState["selectedOrganization"]
>(`${name}/setSelectedOrganizationRedux`);

export const geranSlice = createSlice({
  name,
  initialState,
  reducers: {
    resetGrantTemplate: () => initialState,
    setFieldField: (
      state,
      action: PayloadAction<{
        sectionId: string;
        sectionName?: string;
        fieldId: string;
        fieldName?: string;
        fieldType?: string;
        isRequired?: boolean;
        sequenceOrder?: number;
        pageNumber?: number;
        options?: string[];
      }>
    ) => {
      const {
        sectionId,
        sectionName,
        fieldId,
        fieldName,
        fieldType,
        isRequired,
        sequenceOrder,
        pageNumber,
        options,
      } = action.payload;

      const section = state.sections.find((s) => s.id === sectionId);
      if (!section) return;

      const fieldIndex = section.fields.findIndex((f) => f.id === fieldId);

      if (fieldIndex !== -1) {
        const field = section.fields[fieldIndex];
        if (fieldName !== undefined) field.fieldName = fieldName;
        if (fieldType !== undefined) field.fieldType = fieldType;
        if (sectionName !== undefined) field.sectionName = sectionName;
        if (isRequired !== undefined) field.isRequired = isRequired;
        if (sequenceOrder !== undefined) field.sequenceOrder = sequenceOrder;
        if (pageNumber !== undefined) field.pageNumber = pageNumber;
        if (options !== undefined) field.options = options;
      } else {
        section.fields.push({
          id: fieldId,
          sectionId,
          sectionName: sectionName || "",
          fieldName: fieldName || "",
          fieldType: fieldType || "TEXT",
          isRequired: isRequired ?? false,
          sequenceOrder: sequenceOrder ?? section.fields.length + 1,
          pageNumber: pageNumber ?? 1,
          options: options && options.length > 0 ? options : ["", ""],
          table: Array.from({ length: 1 }, () =>
            Array.from({ length: 1 }, () => "")
          ),
        });
      }
    },
    updateTableField: (
      state,
      action: PayloadAction<{
        sectionId: string;
        fieldId: string;
        table: string[][];
      }>
    ) => {
      const { sectionId, fieldId, table } = action.payload;
      const section = state.sections.find((s) => s.id === sectionId);
      if (!section) {
        return;
      }
      const field = section.fields.find((f) => f.id === fieldId);
      if (!field) {
        return;
      }

      field.table = table.map((row) => [...row]);
    },
    setSyaratGeran: (state, action: PayloadAction<Partial<GeranState>>) => {
      return { ...state, ...action.payload };
    },
    updateFieldPageNumber(
      state,
      action: PayloadAction<{
        sectionId: string;
        fieldId: string;
        pageNumber: number;
      }>
    ) {
      const section = state.sections.find(
        (s) => s.id === action.payload.sectionId
      );
      if (section) {
        const field = section.fields.find(
          (f) => f.id === action.payload.fieldId
        );
        if (field) {
          field.pageNumber = action.payload.pageNumber;
        }
      }
    },

    setLastPickedComponentLabel: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.lastPickedComponentLabel = action.payload;
    },

    addSection: (
      state,
      action: PayloadAction<{ id: string; name: string; pageNumber: number }>
    ) => {
      state.sections.push({
        id: action.payload.id,
        name: action.payload.name,
        pageNumber: action.payload.pageNumber,
        fields: [],
      });
    },

    addFieldToSection: (
      state,
      action: PayloadAction<{
        sectionId: string;
        field: GrantTemplateFieldCreateRequest;
      }>
    ) => {
      const section = state.sections.find(
        (s) => s.id === action.payload.sectionId
      );
      if (section) {
        const newField = {
          ...action.payload.field,
          sequenceOrder: section.fields.length + 1,
          options:
            action.payload.field.options &&
            action.payload.field.options.length > 0
              ? action.payload.field.options
              : ["", ""],
        };
        section.fields.push(newField);
      }
    },

    updateFieldInSection: (
      state,
      action: PayloadAction<{
        sectionId: string;
        field: GrantTemplateFieldCreateRequest;
      }>
    ) => {
      const section = state.sections.find(
        (s) => s.id === action.payload.sectionId
      );
      if (section) {
        const idx = section.fields.findIndex(
          (f) => f.id === action.payload.field.id
        );
        if (idx !== -1) {
          section.fields[idx] = action.payload.field;
        }
      }
    },

    updateSectionsOrder: (state, action) => {
      const newOrder: string[] = action.payload;
      state.sections = newOrder.map(
        (id) => state.sections.find((s) => s.id === id)!
      );
    },

    removeFieldFromSection: (
      state,
      action: PayloadAction<{ sectionId: string; fieldId: string }>
    ) => {
      const section = state.sections.find(
        (s) => s.id === action.payload.sectionId
      );
      if (section) {
        section.fields = section.fields.filter(
          (f) => f.id !== action.payload.fieldId
        );
        section.fields = section.fields.map((f, index) => ({
          ...f,
          sequenceOrder: index + 1,
        }));
      }
    },

    removeSection: (state, action: PayloadAction<string>) => {
      const sectionId = action.payload;
      state.sections = state.sections.filter((s) => s.id !== sectionId);
    },
    setCategories: (state, action: PayloadAction<string[]>) => {
      state.categories = action.payload;
    },
    setSubCategories: (state, action: PayloadAction<string[]>) => {
      state.subCategories = action.payload;
    },
    setCategoryPairs: (state, action: PayloadAction<[number, number][]>) => {
      state.categoryPairs = action.payload;
    },
    setSocietyRegistrationDate(state, action: PayloadAction<string | null>) {
      state.societyRegistrationDate = action.payload;
    },
    setStatusReport(state, action: PayloadAction<string | null>) {
      state.statusReport = action.payload;
    },
    setTitle(state, action: PayloadAction<string>) {
      state.title = action.payload;
    },
  },
  extraReducers: (builder) =>
    builder.addCase(setSelectedOrganizationRedux, (state, action) => {
      state.selectedOrganization = action.payload;
    }),
  selectors: {
    getSelectedOrganization: (state) => state.selectedOrganization,
    getSections: (state) => state.sections,
    getFieldsBySection: (state, sectionId: string) =>
      state.sections.find((s) => s.id === sectionId)?.fields ?? [],
    getLastPickedComponentLabel: (state) => state.lastPickedComponentLabel,
  },
});

export const {
  addSection,
  addFieldToSection,
  updateFieldInSection,
  removeFieldFromSection,
  removeSection,
  updateSectionsOrder,
  setLastPickedComponentLabel,
  setFieldField,
  updateFieldPageNumber,
  resetGrantTemplate,
  updateTableField,
  setSyaratGeran,
  setCategories,
  setCategoryPairs,
  setSocietyRegistrationDate,
  setStatusReport,
  setSubCategories,
  setTitle,
} = geranSlice.actions;
export const {
  getSelectedOrganization,
  getSections,
  getFieldsBySection,
  getLastPickedComponentLabel,
} = geranSlice.selectors;

export default geranSlice.reducer;
