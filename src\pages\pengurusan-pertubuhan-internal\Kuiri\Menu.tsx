import { Box, Skeleton, Typography } from "@mui/material";
import React from "react";

interface KeputusanBoxesProps {
  data: {
    name: string;
    number: number;
  };
  isActive: boolean;
  /**
   * @default false
   */
  isLoading?: boolean;
  disabled?: boolean;
  onClick: () => void;
}
const primary = "var(--primary-color)";

const Menu: React.FC<KeputusanBoxesProps> = React.memo(
  ({ data, isActive, isLoading = false, onClick, disabled }) => {
    return (
      <Box
        onClick={!disabled ? onClick : undefined}
        sx={{
          ...(!disabled && {
            "&:hover": {
              backgroundColor: isActive ? primary : "#f5f5f5",
              transform: "translateY(-2px)",
            },
          }),
          padding: "0.75rem",
          paddingTop: "0.5rem !important",
          borderRadius: "0.5rem",
          border: `1px solid ${!disabled ? primary : "var(--border-grey)"}`,
          backgroundColor: isActive
            ? primary
            : !disabled
            ? "white"
            : "var(--border-grey)",
          position: "relative",
          display: "grid",
          gap: 2,
          flexDirection: "column",
          justifyContent: "space-between",
          alignItems: "flex-start",
          height: "100%",
          minHeight: "80px",
          paddingBottom: 0,
          cursor: !disabled ? "pointer" : "default",
          transition: "all 0.2s ease-in-out",
          ...(isActive && {
            boxShadow: "4px 6px 12px 0 rgba(102, 102, 102, 0.3)",
          }),
          ...(isLoading
            ? {
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                paddingBottom: 0,
              }
            : {}),
        }}
      >
        <Box
          sx={{
            color: disabled
              ? "var(--text-grey-disabled)"
              : isActive
              ? "#fff"
              : "var(--primary-color)",
            fontWeight: 400,
            fontSize: "14px",
          }}
        >
          {data.name}
        </Box>
        <div
          style={{
            display: "flex",
            width: "100%",
            justifyContent: "flex-end",
            alignItems: "flex-end",
          }}
        >
          {isLoading ? (
            <Skeleton
              sx={{
                backgroundColor: isActive ? "#fff" : "var(--primary-color)",
              }}
              width="10%"
            />
          ) : (
            <Typography
              sx={{
                position: "absolute",
                bottom: 0,
                right: "10px",
                color: disabled
                  ? "var(--text-grey-disabled)"
                  : isActive
                  ? "#fff"
                  : "var(--primary-color)",
                fontWeight: 500,
              }}
            >
              {data.number}
            </Typography>
          )}
        </div>
      </Box>
    );
  }
);

export default Menu;
