import React, { ChangeEvent, useEffect, useState } from "react";
import {
  Box,
  Typography,
  Grid,
  useMediaQuery,
  Theme,
  Fade,
} from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  AWSLocationSearchMap,
  ButtonOutline,
  ButtonPrimary,
  DisabledTextField,
  SelectFieldController,
  TextFieldController,
} from "@/components";
import { API_URL } from "@/api";
import { useCustom } from "@refinedev/core";
import { debounce, MALAYSIA } from "@/helpers";
import { useSelector } from "react-redux";
import { FieldValues, useForm, Resolver } from "react-hook-form";
import AWSLocationMap from "@/components/geocoder/geocoder";
import {
  LocationClient,
  SearchPlaceIndexForTextCommand,
} from "@aws-sdk/client-location";
import { withAPIKey } from "@aws/amazon-location-utilities-auth-helper";
import { number, object, string } from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import CustomPopover from "@/components/popover";
import { Switch } from "@/components/switch";

const AmendmentNameAndAddress = ({
  onUpdate,
  disableNext,
  currentData,
  isView,
  isWaitingPayment,
}: {
  onUpdate: (data: any) => void;
  disableNext: boolean;
  currentData: any;
  isView: boolean;
  isWaitingPayment: boolean;
}) => {
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const { t, i18n } = useTranslation();

  const isMyLanguage = i18n.language === "my";
  const validPrefixBranchName = /^(Cawangan|Bahagian|Negeri|Daerah)\b/i;

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const branchAmendRedux = useSelector(
    (state: { branchAmendData: any }) => state.branchAmendData.data
  );

  const { id } = useParams();
  const navigate = useNavigate();
  const [addressData, setAddressData] = useState([]);
  const [bName, setBName] = useState("");
  const [pass, setPass] = useState(true);
  const [sameAddress, setSameAddress] = useState(false);

  const handleSwitchOnChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { checked } = event.target;

    setSameAddress(checked);
  };

  const resolver = yupResolver(
    object().shape({
      branchName: string()
        .label(t("namaCawangan"))
        .test({
          name: "valid_prefix",
          message: isMyLanguage
            ? "Nama cawangan mesti bermula dengan 'Cawangan', 'Bahagian', 'Negeri' atau 'Daerah'.'"
            : "Branch name must begin with 'Branch', 'Division', 'State' or 'District'.'",
          test: function (value) {
            if (!value) return true;
            return validPrefixBranchName.test(value.trim());
          },
        })
        .test({
          name: "branch_name_exist",
          message: t("branchNameAlreadyExist"),
          test: async function (name) {
            const trimmedName = name?.trim().toLowerCase();

            // Skip API call if name is too short
            if (!trimmedName || trimmedName.length <= 6) {
              return true;
            }

            // Skip API call if name hasn't changed
            if (bName === trimmedName) {
              return pass;
            }

            try {
              const response = await fetch(
                `${API_URL}/society/branch/checkBranchNameExist?branchName=${trimmedName}&societyId=${currentData?.societyId}`,
                {
                  method: "GET",
                  headers: {
                    portal: localStorage.getItem("portal") || "",
                    Authorization: `Bearer ${localStorage.getItem(
                      "refine-auth"
                    )}`,
                  },
                }
              );

              if (response.ok) {
                const data = await response.json();
                console.log("data?.data", data?.data);
                // If data exists, the name is taken (return error)
                if (!data?.data) {
                  setBName(trimmedName);
                  setPass(false);
                  return this.createError({
                    message: t("branchNameAlreadyExist"),
                  });
                }
                // If data doesn't exist, the name is available

                setBName(trimmedName);
                setPass(true);
                return true;
              }

              // If response not OK, consider invalid

              setBName(trimmedName);
              setPass(false);
              return false;
            } catch (e) {
              setBName(trimmedName);
              setPass(false);
              return this.createError({ message: t("error") });
            }
          },
        })
        .required(),
      address: string().label(t("businessAddressLabel")).required(),
      stateCode: number()
        .label(t("state"))
        .required()
        .typeError("Sila pilih Negeri."),
      districtCode: number()
        .label(t("district"))
        .required()
        .typeError("Sila pilih Daerah."),

      postcode: string()
        .label(t("postcode"))
        .test({
          name: "postcode_validation",
          test: (val: string | undefined, context) => {
            if (typeof val === "string") {
              if (!val.split("").every((num) => !Number.isNaN(parseInt(num)))) {
                return context.createError({
                  message: t("validation.mustBeNumber"),
                });
              }
              return val.length === 5;
            }
            return true;
          },
          message: t("postcodeValidation"),
        })
        .required(),

      mailingAddress: string().when("$sameAddress", {
        is: false,
        then: (schema) => schema.required().label(t("businessAddressLabel")),
        otherwise: (schema) => schema.strip(),
      }),

      mailingStateId: number().when("$sameAddress", {
        is: false,
        then: (schema) =>
          schema.required().label(t("state")).typeError("Sila pilih Negeri."),
        otherwise: (schema) => schema.strip(),
      }),

      mailingDistrictId: number().when("$sameAddress", {
        is: false,
        then: (schema) =>
          schema
            .required()
            .label(t("district"))
            .typeError("Sila pilih Daerah."),
        otherwise: (schema) => schema.strip(),
      }),

      mailingPostcode: string().when("$sameAddress", {
        is: false,
        then: (schema) =>
          schema
            .required()
            .test({
              name: "postcode_validation",
              test: (val: string | undefined, context) => {
                if (typeof val === "string") {
                  if (
                    !val.split("").every((num) => !Number.isNaN(parseInt(num)))
                  ) {
                    return context.createError({
                      message: t("validation.mustBeNumber"),
                    });
                  }
                  return val.length === 5;
                }
                return true;
              },
              message: t("postcodeValidation"),
            })
            .label(t("postcode")),
        otherwise: (schema) => schema.strip(),
      }),
    })
  ) as unknown as Resolver<FieldValues>;

  const {
    control,
    handleSubmit,
    getValues,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<FieldValues>({
    mode: "onChange",
    defaultValues: {
      branchName: currentData?.branchName || "",
      address: currentData?.address,
      stateCode: currentData?.stateCode || "",
      districtCode: currentData?.districtCode || "",
      city: currentData?.city,
      postcode: currentData?.postcode,
      mailingAddress: currentData?.mailingAddress,
      mailingStateId: currentData?.mailingStateId || "",
      mailingDistrictId: currentData?.mailingDistrictId || "",
      mailingCity: currentData?.mailingCity,
      mailingPostcode: currentData?.mailingPostcode,
      // sameAddress: currentData?.sameAddress ?? true,
    },
    context: { sameAddress },
    resolver,
  });

  const cityOptions = addressData
    .filter((item: any) => item.pid === MALAYSIA)
    .map((item: any) => ({
      value: String(item.id),
      label: item.name,
    }));
  const districtOptions = addressData
    .filter((item: any) => item.pid === Number(watch("stateCode")))
    .map((item: any) => ({
      value: String(item.id),
      label: item.name,
    }));
  const districtOptions2 = addressData
    .filter((item: any) => item.pid === Number(watch("mailingStateId")))
    .map((item: any) => ({
      value: String(item.id),
      label: item.name,
    }));

  const resetAll = () => {
    setValue("branchName", "");
    setValue("address", "");
    setValue("stateCode", "");
    setValue("districtCode", "");
    setValue("city", "");
    setValue("postcode", "");
    setValue("mailingAddress", "");
    setValue("mailingStateId", "");
    setValue("mailingDistrictId", "");
    setValue("mailingCity", "");
    setValue("mailingPostcode", "");
  };

  const updateHandler = (data: FieldValues) => {
    if (sameAddress) {
      setValue("mailingAddress", getValues("address"));
      setValue("mailingStateId", getValues("stateCode"));
      setValue("mailingDistrictId", getValues("districtCode"));
      setValue("mailingCity", getValues("city"));
      setValue("mailingPostcode", getValues("postcode"));
      data.mailingAddress = getValues("address");
      data.mailingStateId = getValues("stateCode");
      data.mailingDistrictId = getValues("districtCode");
      data.mailingCity = getValues("city");
      data.mailingPostcode = getValues("postcode");
    }

    onUpdate({
      amendmentType: "pinda_nama_alamat",
      ...data,
    });
  };

  const goNext = () => {
    navigate(`/pertubuhan/society/${id}/senarai/cawangan/branch-Info/bayaran`);
  };

  const goBack = () => {
    navigate(-1);
  };

  const { data: addressList } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess(data) {
        const responseData = data?.data?.data;
        setAddressData(responseData);
      },
    },
  });

  const [organizationCoords, setOrganizationCoords] = useState<
    [number, number]
  >([101.707021, 2.745564]);

  const [organizationCoords2, setOrganizationCoords2] = useState<
    [number, number]
  >([101.707021, 2.745564]);

  // aws location
  const PLACE_INDEX_NAME = import.meta.env.VITE_AWS_PLACE_INDEX;
  const AWS_REGION = import.meta.env.VITE_AWS_API_KEY_REGION;
  const API_KEY = import.meta.env.VITE_AWS_API_KEY;
  const [client, setClient] = useState<LocationClient | null>(null);
  const [locations, setLocations] = useState<string[]>([]);

  // ✅ AWS Location Client Initialization
  useEffect(() => {
    const initializeClient = async () => {
      try {
        const authHelper = await withAPIKey(API_KEY, AWS_REGION);
        const locationClient = new LocationClient(authHelper.getClientConfig());
        setClient(locationClient);
      } catch (error) {
        console.error("❌ Failed to initialize AWS Location Client:", error);
      }
    };
    initializeClient();
  }, []);

  const addressValue = watch("address");
  const addressMailingValue = watch("mailingAddress");

  useEffect(() => {
    // Only search if the address is not empty and has more than 3 characters
    if (addressValue && addressValue.length > 3) {
      // Debounce the search to avoid too many calls
      const delayDebounceFn = setTimeout(() => {
        searchLocation(addressValue, "address");
      }, 500); // 500ms delay

      return () => clearTimeout(delayDebounceFn);
    }
  }, [addressValue, client]); // re-run if address or client changes

  useEffect(() => {
    if (addressMailingValue && addressMailingValue.length > 3) {
      // Debounce the search to avoid too many calls
      const delayDebounceFn = setTimeout(() => {
        searchLocation(addressMailingValue, "mailingAddress");
      }, 500); // 500ms delay

      return () => clearTimeout(delayDebounceFn);
    }

    searchLocation(addressMailingValue, "mailingAddress");
  }, [addressMailingValue, client]); // re-run if address or client changes

  const searchLocation = async (
    query: string,
    type: "address" | "mailingAddress"
  ) => {
    if (!query) return;
    if (!client) {
      console.warn("❌ AWS Location Client has not been initialized");
      return;
    }
    try {
      const command = new SearchPlaceIndexForTextCommand({
        IndexName: PLACE_INDEX_NAME,
        Text: query,
        MaxResults: 1,
        FilterCountries: ["MYS"],
      });
      const response = await client.send(command);
      console.log("place", response);
      if (!response?.Results || response.Results.length === 0) {
        console.warn("⚠️ No locations found");
        setLocations([]);
        return;
      }
      const firstResult = response.Results[0];
      console.log("firstResult", firstResult);

      if (firstResult?.Place?.Geometry?.Point) {
        const [longitude, latitude] = firstResult.Place.Geometry.Point;
        console.log("📍 Coordinates:", longitude, latitude);

        const city = firstResult.Place.Municipality || "";
        const postcode = firstResult.Place.PostalCode || "";
        const stateName = firstResult.Place.Region || "";
        const districtName = firstResult.Place.SubRegion || "";

        console.log("🏙️ City:", city);
        console.log("📮 Postcode:", postcode);
        console.log("🗺️ State name:", stateName);
        console.log("🗺️ District name:", districtName);

        if (type === "address") {
          setOrganizationCoords([longitude, latitude]);
          setValue("city", city || watch("city"));
          setValue("postcode", postcode || watch("postcode"));
        }
        if (type === "mailingAddress") {
          setOrganizationCoords2([longitude, latitude]);
          setValue("mailingCity", city || watch("mailingCity"));
          setValue("mailingPostcode", postcode || watch("mailingPostcode"));
        }
      }

      setLocations(
        response.Results.map(
          (place) => place.Place?.Label ?? "Unknown Location"
        )
      );
    } catch (error) {
      console.error("Error searching location:", error);
    }
  };

  const handleLocationSelected = (location: {
    fullAddress: string;
    state: string;
    district: string;
    city: string;
    postcode: string;
  }) => {
    setValue("address", location.fullAddress);
    setValue("city", location.city);
    setValue("postcode", location.postcode);
  };

  const handleLocationSelected2 = (location: {
    fullAddress: string;
    state: string;
    district: string;
    city: string;
    postcode: string;
  }) => {
    setValue("mailingAddress", location.fullAddress);
    setValue("mailingCity", location.city);
    setValue("mailingPostcode", location.postcode);
  };

  return (
    <Fade in={true} timeout={500}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mt: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("maklumatAsalCawangan")}
          </Typography>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={3} sx={{ display: "flex" }}>
              <Typography sx={labelStyle}>{t("namaAsal")}</Typography>
            </Grid>
            <Grid item xs={12} md={9}>
              <DisabledTextField
                value={branchAmendRedux?.currentBranchName ?? "-"}
              />
            </Grid>

            <Grid item xs={12} md={3} sx={{ display: "flex" }}>
              <Typography sx={labelStyle}>{t("alamatAsal")}</Typography>
            </Grid>
            <Grid item xs={12} md={9}>
              <DisabledTextField
                value={branchAmendRedux?.currentBranchAddress ?? "-"}
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("maklumatPindaNamaDanAlamatCawangan")}
          </Typography>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={3} sx={{ display: "flex" }}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>
                  {t("newBranchName")} <span style={{ color: "red" }}>*</span>
                </Typography>
                <CustomPopover
                  customStyles={{
                    maxWidth: "210px",
                    backgroundColor: "white",
                    mt: 1,
                  }}
                  content={
                    <Typography
                      sx={{
                        color: "#FF0000",
                        fontSize: "12px",
                      }}
                    >
                      {t("branchNameHelper1")}
                    </Typography>
                  }
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={9}>
              <TextFieldController
                control={control}
                name="branchName"
                placeholder="Contoh: Cawangan Perak, Bahagian Perak"
                fullWidth
                required
                disabled={isView}
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("petaLokasi")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <div
                style={{
                  height: "150px",
                  width: "100%",
                  borderRadius: "8px",
                }}
              >
                <AWSLocationMap
                  longitude={organizationCoords[0]}
                  latitude={organizationCoords[1]}
                  // zoom={20}
                  onLocationSelected={handleLocationSelected}
                />
              </div>
            </Grid>

            <Grid item xs={12} md={3} sx={{ display: "flex" }}>
              <Typography sx={labelStyle}>
                {t("newBranchBusinessAddress")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} md={9}>
              <TextFieldController
                disabled={isView}
                control={control}
                name="address"
                fullWidth
                required
              />
            </Grid>

            <Grid item xs={12} md={3} sx={{ display: "flex" }}>
              <Typography sx={labelStyle}>
                {t("negeri")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} md={9}>
              <SelectFieldController
                disabled={isView}
                name="stateCode"
                control={control}
                options={cityOptions}
                placeholder={t("selectPlaceholder")}
                onChange={() => setValue("districtCode", "")}
                required
              />
            </Grid>
            <Grid item xs={12} md={3} sx={{ display: "flex" }}>
              <Typography sx={labelStyle}>
                {t("daerah")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} md={9}>
              <SelectFieldController
                name="districtCode"
                control={control}
                options={districtOptions}
                placeholder={t("selectPlaceholder")}
                disabled={!watch("stateCode") || isView}
                required
              />
            </Grid>

            <Grid item xs={12} md={3} sx={{ display: "flex" }}>
              <Typography sx={labelStyle}>{t("bandar")}</Typography>
            </Grid>
            <Grid item xs={12} md={9}>
              <TextFieldController
                control={control}
                name="city"
                disabled={isView}
                fullWidth
              />
            </Grid>

            <Grid item xs={12} md={3} sx={{ display: "flex" }}>
              <Typography sx={labelStyle}>
                {t("poskod")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} md={9}>
              <TextFieldController
                disabled={isView}
                name="postcode"
                control={control}
                fullWidth
                required
                type="number"
                rules={{
                  validate: (value: string) => {
                    if (!/^\d{5}$/.test(value)) {
                      return t("postcodeValidation");
                    }
                    return true;
                  },
                }}
                isPostcode
              />
            </Grid>
          </Grid>
          <Grid
            container
            spacing={2}
            sx={{ display: isView ? "none" : "block" }}
          >
            <Grid
              item
              xs={12}
              sx={{
                mt: 4,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline
                sx={{
                  width: isMobile ? "100%" : "auto",
                }}
                onClick={() => resetAll()}
              >
                {t("semula")}
              </ButtonOutline>

              <ButtonPrimary
                variant="contained"
                sx={{ width: isMobile ? "100%" : "auto" }}
                onClick={handleSubmit(updateHandler)}
              >
                {t("update")}
              </ButtonPrimary>
            </Grid>
          </Grid>
        </Box>
        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              mb: 2,
            }}
          >
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontSize: "14px",
                fontWeight: "500 !important",
              }}
            >
              {t("BranchCorrespondenceAddress")}
            </Typography>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Typography
                variant="body2"
                sx={{ fontSize: "12px", color: "#66666680" }}
              >
                {t("sameAsAbove")}
              </Typography>
              <Switch checked={sameAddress} onChange={handleSwitchOnChange} />
            </Box>
          </Box>
          {!sameAddress && (
            <Grid container spacing={2} sx={{ mt: 0 }}>
              <Grid item xs={12} sm={3}>
                <Typography sx={labelStyle}>
                  {t("petaLokasi")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={9}>
                <div
                  style={{
                    height: "150px",
                    width: "100%",
                    borderRadius: "8px",
                  }}
                >
                  <AWSLocationMap
                    longitude={organizationCoords2[0]}
                    latitude={organizationCoords2[1]}
                    // zoom={20}
                    onLocationSelected={handleLocationSelected2}
                  />
                </div>
              </Grid>

              <Grid item xs={12} md={3} sx={{ display: "flex" }}>
                <Typography sx={labelStyle}>
                  {t("newBranchCorrespondenceAddress")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} md={9}>
                <TextFieldController
                  control={control}
                  name="mailingAddress"
                  disabled={sameAddress || isView}
                  fullWidth
                  required
                />
              </Grid>

              <Grid item xs={12} md={3} sx={{ display: "flex" }}>
                <Typography sx={labelStyle}>
                  {t("negeri")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} md={9}>
                <SelectFieldController
                  name="mailingStateId"
                  disabled={sameAddress || isView}
                  control={control}
                  options={cityOptions}
                  placeholder={t("selectPlaceholder")}
                  onChange={() => setValue("mailingDistrictId", "")}
                  required
                />
              </Grid>

              <Grid item xs={12} md={3} sx={{ display: "flex" }}>
                <Typography sx={labelStyle}>
                  {t("daerah")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} md={9}>
                <SelectFieldController
                  name="mailingDistrictId"
                  control={control}
                  options={districtOptions2}
                  placeholder={t("selectPlaceholder")}
                  disabled={!watch("mailingStateId") || sameAddress || isView}
                  required
                />
              </Grid>

              <Grid item xs={12} md={3} sx={{ display: "flex" }}>
                <Typography sx={labelStyle}>{t("bandar")}</Typography>
              </Grid>
              <Grid item xs={12} md={9}>
                <TextFieldController
                  control={control}
                  name="mailingCity"
                  disabled={sameAddress || isView}
                  fullWidth
                />
              </Grid>

              <Grid item xs={12} md={3} sx={{ display: "flex" }}>
                <Typography sx={labelStyle}>
                  {t("poskod")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} md={9}>
                <TextFieldController
                  name="mailingPostcode"
                  control={control}
                  fullWidth
                  required
                  disabled={sameAddress || isView}
                  type="number"
                  rules={{
                    validate: (value: string) => {
                      if (!/^\d{5}$/.test(value)) {
                        return t("postcodeValidation");
                      }
                      return true;
                    },
                  }}
                  isPostcode
                />
              </Grid>
            </Grid>
          )}
        </Box>
        <Grid container spacing={2}>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              pr: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            {isView && !isWaitingPayment ? (
              <ButtonPrimary
                variant="contained"
                sx={{ width: isMobile ? "100%" : "auto" }}
                onClick={() => goBack()}
              >
                {t("back")}
              </ButtonPrimary>
            ) : (
              <ButtonPrimary
                variant="contained"
                sx={{ width: isMobile ? "100%" : "auto" }}
                disabled={disableNext && !isWaitingPayment}
                onClick={() => goNext()}
              >
                {t("seterusnya")}
              </ButtonPrimary>
            )}
          </Grid>
        </Grid>
      </Box>
    </Fade>
  );
};

export default AmendmentNameAndAddress;
