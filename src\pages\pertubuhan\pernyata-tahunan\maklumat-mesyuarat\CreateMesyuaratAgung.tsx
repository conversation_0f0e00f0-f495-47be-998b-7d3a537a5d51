import React, { useState } from "react";
import { Box, CircularProgress, Typography } from "@mui/material";
import { Trans, useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { useSenaraiContext } from "../../SenaraiContext";
import KehadiranAJK from "./KehadiranAJK";
import MaklumatMesyuaratPenyataTahunan from "./MaklumatMesyuaratPenyataTahunan";
import AlamatTempatMesyuarat from "./AlamatTempatMesyuarat";
import MaklumatMesyuarat from "./MaklumatMesyuarat";
import MinitMesyuarat from "./MinitMesyuarat";
import useQuery from "../../../../helpers/hooks/useQuery";
import { Meeting } from "../interface";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import { ApplicationStatus } from "@/helpers";
import MessageDialog from "@/components/dialog/message";

export const CreateMesyuaratAgung: React.FC = () => {
  const { id } = useParams(); // Access the dynamic `id` from the URL (e.g., "406")
  //@ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);

  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);
  //@ts-ignore
  const isviewStatement = useSelector(
    //@ts-ignore
    (state) => state?.statementData?.isViewStatement
  );
  const [selectedDate, setSelectedDate] = useState("");
  const [meetingId, setMeetingId] = useState<string | number | null>("");
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [meeting, setMeeting] = useState<Meeting | null>();

  const isDisabled = (!isManager && !isAliranTugasAccess) || isviewStatement;
  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;
  const [statementComplete, setStatementComplete] = useState(false);
  // =====
  const [availableDateList, setAvailableDateList] = useState<string[]>([]);
  const {
    handleNextPenyataTahunan: handleNext,
    handleBackPenyataTahunan: handleBack,
  } = useSenaraiContext();

  const { t } = useTranslation();
  const navigate = useNavigate();

  const [businessCoords, setBusinessCoords] = useState<[number, number]>([
    2.745564, 101.707021,
  ]);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { mutate: saveGeneralInfo } = useCustomMutation();

  const { isLoading: societyInfoIsLoading } = useQuery({
    url: `society/statement/societyInfo/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "year", operator: "eq", value: year },
    ],
    onSuccess: (data) => {
      if (
        Number(data?.data?.data?.applicationStatusCode) ===
        ApplicationStatus.SELESAI
      ) {
        setStatementComplete(true);
      }
      const generalInfo = data?.data?.data || [];
      setSelectedDate(generalInfo?.meetingDate);
      setMeetingId(generalInfo?.meetingId);
      fetchMeetingList();
    },
  });

  const handleBackActions = () => {
    handleBack();
    navigate(-1);
  };

  const handleNextActions = () => {
    if (statementComplete) {
      handleNext();
      navigate(`../penyata-tahunan-info`, {
        state: {
          societyId: societyId,
          statementId: statementId,
          year: year,
        },
      });
      // navigate(`../ajk`);
    } else {
      saveGeneralInfo(
        {
          url: `${API_URL}/society/statement/societyInfo/update`,
          method: "put",
          values: {
            meetingDate: selectedDate,
            societyId: societyId,
            statementId: statementId,
            meetingType: meeting?.meetingType,
            meetingId: meeting?.id,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
        },
        {
          onSuccess: () => {
            handleNext();
            navigate(`../penyata-tahunan-info`, {
              state: {
                societyId: societyId,
                statementId: statementId,
                year: year,
              },
            });
          },
        }
      );
    }
  };

  const { isLoading: isMeetinglistLoading, refetch: fetchMeetingList } =
    useQuery({
      url: `society/statement/general/meeting/get`,
      filters: [{ field: "societyId", operator: "eq", value: societyId }],
      autoFetch: false,
      onSuccess: (data) => {
        const meetings = data?.data?.data || [];
        setMeetings(meetings);
        const availableList = meetings
          .filter((item: any) => Number(item.meetingType) !== 1)
          .map((item: any) => item.meetingDate);
        setAvailableDateList(availableList);
      },
    });

  const handleSetMeeting = (meeting: Meeting | null) => {
    if (!meeting) {
      setMeeting(null);
      setMeetingId(null);
      return;
    } else {
      setMeetingId(meeting?.id);
      setMeeting(meeting);
    }
  };
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [filteredMeetings, setFilteredMeeting] = useState<Meeting[]>([]);

  const handleSetFilteredMeeting = (meetings: Meeting[]) => {
    const finilzedMeetings = meetings.filter((meeting) => {
      return Number(meeting.meetingType) !== 1;
    });
    setFilteredMeeting(finilzedMeetings);
    if (meetings && meetings.length > 0) {
      setIsDialogOpen(false);
    } else {
      setIsDialogOpen(true);
    }
  };

  //

  const [savedMeetingDate, setSavedMeetingDate] = useState("");

  function goMeetingPage() {
    navigate(`../../mesyuarat`);
  }

  function resetChanges() {
    setFilteredMeeting([]);
    setMeeting(undefined);
  }

  const handleNavigate = (url: string) => {
    navigate(url);
  };

  const apiIsLoading = societyInfoIsLoading || isMeetinglistLoading;

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        {/* peringatan */}
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "bold" }}>
              {t("peringatan")} :
            </span>{" "}
            <br />
            <span style={{ fontSize: 12 }}>
              <Trans
                i18nKey="penyataTahunan_peringatan_point"
                components={{
                  1: (
                    <span
                      style={{
                        color: "var(--primary-color)",
                        fontSize: "11px",
                        cursor: "pointer",
                      }}
                      onClick={() => handleNavigate(`../../mesyuarat`)}
                    ></span>
                  ),
                }}
              />
            </span>
          </Typography>
        </Box>

        {/* Maklumat Mestyuarat Penyata Tahunan */}

        {apiIsLoading ? (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: "300px",
            }}
          >
            <CircularProgress />
          </Box>
        ) : (
          <MaklumatMesyuaratPenyataTahunan
            isDisabled={isDisabled || statementComplete}
            selectedDate={selectedDate}
            meetingId={meetingId}
            setSelectedDate={setSelectedDate}
            filteredMeetings={filteredMeetings}
            handleSetFilteredMeeting={handleSetFilteredMeeting}
            sectionStyle={sectionStyle}
            meetings={meetings}
            meeting={meeting}
            handleSetMeeting={handleSetMeeting}
            availableDateList={availableDateList}
            setSavedMeetingDate={setSavedMeetingDate}
            savedMeetingDate={savedMeetingDate}
            resetChanges={resetChanges}
          />
        )}

        {meeting ? (
          // Maklumat Mestyuarat Penyata Tahunan
          <>
            {/* alamat tempat mesyuarat */}
            <AlamatTempatMesyuarat
              sectionStyle={sectionStyle}
              meeting={meeting}
              businessCoords={businessCoords}
            />
            {/* maklumat mesyuarat */}
            <MaklumatMesyuarat sectionStyle={sectionStyle} meeting={meeting} />
            {/* kehadiran ajk */}
            <KehadiranAJK
              sectionStyle={sectionStyle}
              ahli={meeting?.meetingMemberAttendances}
            />
            {/* Minit Mesyuarat */}
            <MinitMesyuarat sectionStyle={sectionStyle} meeting={meeting} />
          </>
        ) : null}

        {/* Buttons */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 2,
            mt: 2,
          }}
        >
          <ButtonOutline variant="outlined" onClick={() => handleBackActions()}>
            {t("back")}
          </ButtonOutline>
          <ButtonPrimary
            onClick={handleNextActions}
            disabled={(isDisabled && !statementComplete) || !meetingId}
          >
            {t("next")}
          </ButtonPrimary>
        </Box>
      </Box>
      <MessageDialog
        open={isDialogOpen}
        onClickFunction={() => goMeetingPage()}
        buttonText={t("Viewlist")}
        onClose={() => setIsDialogOpen(false)}
        message={t("createMeetingReminder")}
      />
    </>
  );
};

export default CreateMesyuaratAgung;
