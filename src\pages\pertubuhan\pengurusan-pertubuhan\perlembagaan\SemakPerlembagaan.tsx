import {
  Box,
  CircularProgress,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Paper,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { OrganizationStepper } from "../organization-stepper";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { useNavigate } from "react-router-dom";
import { API_URL } from "../../../../api";
import { useCustom } from "@refinedev/core";
import { useItemHighlighted } from "@mui/x-charts";
import InfoQACard from "../InfoQACard";
import { useSelector } from "react-redux";
import { ApplicationStatus } from "../../../../helpers/enums";
import { useDownloadAndExportConstitutions } from "@/helpers/hooks/useDownloadConstitutions";

export const SemakPerlembagaan: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [currentFasal, setCurrentFasal] = useState(0);
  const [activeStep, setActiveStep] = useState(2);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [senaraiFasal, setSenaraiFasal] = useState([]);
  const [constitutionDataStorage, setConstitutionDataStorage] = useState<any>(
    {}
  );
  const [constitutionType, setConstitutionType] = useState("");

  const handlePrevious = () => {
    if (currentFasal > 0) {
      setCurrentFasal(currentFasal - 1);
    }
  };

  const handleNext = () => {
    if (currentFasal < fasalContent.length - 1) {
      setCurrentFasal(currentFasal + 1);
    }
  };

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");
  const decodedId = atob(encodedId ?? "");

  /*useEffect(() => {
    if (encodedId) {
      const decodedId = atob(encodedId);

      fetch(`${API_URL}/society/${decodedId}`, {
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      })
        .then((res) => res.json())
        .then((data) => {
          setNamaPertubuhan(data?.data?.societyName);
        })
        .catch((error) => {
          console.error("Error fetching society details:", error);
        });
    }
  }, []);*/

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setConstitutionDataStorage(societyDataRedux);
      setNamaPertubuhan(societyDataRedux.societyName);
      setConstitutionType(societyDataRedux.constitutionType);
    }
  }, [societyDataRedux]);

  const { data: constitutionData, isLoading: isConstitutionLoading } =
    useCustom({
      url: `${API_URL}/society/admin/constitutionTypeWithClauseContent/list`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
    });

  const constitutions = constitutionData?.data?.data || [];

  //console.log("constitutions",constitutions);

  //const [clauseData, setClauseData] = useState<any>({});

  /*useEffect(() => {
    const clauses: any = {};
    for (let i = 1; i <= 23; i++) {
      const clause: any = localStorage.getItem(`clause${i}`);
      if (clause) {
        clauses[`clause${i}`] = JSON.parse(clause);
      }
    }
    setClauseData(clauses);
  }, []);*/

  const { data: clauseContentData, isLoading: isClauseContentDataIsLoading } =
    useCustom({
      url: `${API_URL}/society/constitutioncontent/get`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        query: {
          societyId: decodedId,
          //clauseContentId: clauseContentId
        },
      },
      queryOptions: {
        enabled: !!decodedId,
      },
    });
  //console.log("clauseContentData",clauseContentData)
  const clauseData = clauseContentData?.data?.data?.data || [];

  //console.log("clauseData",clauseData);

  //console.log(constitutionDataStorage);
  useEffect(() => {
    //console.log(constitutionType)
    if (constitutionType !== "") {
      /*if (constitutionDataStorage.constitutionType == "Perlembagaan Bebas") {
        const matchedConstitution = constitutions?.find(
          (item: any) => item.name === "Perlembagaan Bercawangan Semua NGO"
        );
        setSenaraiFasal(matchedConstitution?.clauseContents || []);
      } else if (
        constitutionDataStorage.constitutionType == "Perlembagaan Keagamaan"
      ) {
        const matchedConstitution = constitutions?.find(
          (item: any) => item.name === "Perlembagaan Induk Keagamaan"
        );
        setSenaraiFasal(matchedConstitution?.clauseContents || []);
      } else {\
        const matchedConstitution = constitutions?.find(
          (item: any) => item.name === constitutionDataStorage.constitutionType
        );
        setSenaraiFasal(matchedConstitution?.clauseContents || []);
      }*/
      const matchedConstitution = constitutions?.find(
        (item: any) =>
          item.name.toLowerCase() === constitutionType.toLowerCase()
      );
      setSenaraiFasal(matchedConstitution?.clauseContents || []);
    } else {
      setSenaraiFasal([]);
    }
  }, [constitutionType, constitutions]);

  const fasalContent = senaraiFasal.map((clause: any, index) => {
    //console.log("clause",clause);
    const isLastClause =
      index === senaraiFasal.length - 1 &&
      (clause.clauseNo == 23 || clause.clauseNo == 18 || clause.clauseNo == 20);
    /*const clauseContent =
      clauseData[`clause${clause?.clauseNo}`]?.description || "";*/
    // @ts-ignore
    const exist = clauseData.findIndex((p) => p.clauseContentId == clause.id);
    //console.log("exist", exist);
    let clauseContent = clause.content;
    //console.log("template", clauseContent)
    if (exist >= 0) {
      clauseContent = clauseData[exist].description;
      //console.log(clauseContent)
    }
    // clauseContent = clauseContent.replaceAll(/<</gi, "&lt;&lt;");
    // clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;");
    return {
      title: `${t("clause")} ${clause?.clauseNo}: ${t(clause?.name)}`,
      content: clauseContent,
    };
  });

  const hideIndex = clauseData.findIndex(
    (p: any) => p.hideConstitution == true
  );

  const hideId = clauseData?.[hideIndex]?.clauseContentId;

  const sectionStyle = {
    backgroundColor: "#CDE4E4",
    padding: "8px 16px",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
  };

  const { getConstitutionsFile, isLoadingDownloadConstitutions } =
    useDownloadAndExportConstitutions();

  const downloadSemakFasal = () => {
    getConstitutionsFile(decodedId, 1);
  };

  // handle semak bebas content
  // =====================================================
  const getAllBebasTambahContent = clauseData
    .filter((item: any) => item.clauseContent === null)
    .map((item: any, index: number) => ({
      title: `${t("clause")} ${item?.clauseNo}: ${t(item?.clauseName)}`,
      content: item.description,
      clauseNo: item?.clauseNo,
      id: item?.id,
    }));

  const finalFasalContent = [...fasalContent, ...getAllBebasTambahContent];
  const finalSenaraiFasal = [...senaraiFasal, ...getAllBebasTambahContent];

  if (isClauseContentDataIsLoading || isConstitutionLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "300px",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        display: "flex",
        gap: 2,
        alignItems: "flex-start",
      }}
    >
      {/* Konten Utama */}
      <Box
        sx={{
          width: "50vw",
          backgroundColor: "white",
          p: 3,
          borderRadius: "15px",
          flex: "none",
        }}
      >
        <Grid container spacing={2} alignItems="flex-start">
          {/* Sidebar fasal navigation */}
          <Grid item xs={2} sx={{ alignSelf: "flex-start" }}>
            <Box
              sx={{
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                height: "auto",
                color: "#666666",
                "& .Mui-selected": {
                  backgroundColor: "var(--primary-color) !important",
                  color: "white",
                },
                "& .Mui-selected:hover": {
                  backgroundColor: "var(--primary-color) !important",
                  color: "white",
                },
              }}
            >
              <List>
                {finalSenaraiFasal.map((fasal: any, index) => {
                  if (fasal.id === hideId) {
                    return;
                  }
                  return (
                    <ListItem key={index} disablePadding>
                      <ListItemButton
                        selected={currentFasal === index}
                        onClick={() => setCurrentFasal(index)}
                      >
                        <ListItemText
                          primary={`${t("clause")} ${fasal.clauseNo}`}
                        />
                      </ListItemButton>
                    </ListItem>
                  );
                })}
              </List>
            </Box>
          </Grid>

          {/* Area Konten */}
          <Grid item xs={10} sx={{ height: "auto", alignItems: "flex-start" }}>
            <Box
              sx={{
                pt: 3,
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                minHeight: "auto",
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
              }}
            >
              {/* Fasal Heading */}
              <Box sx={{ px: 3 }}>
                <Typography variant="h6" textAlign="center" gutterBottom>
                  PERLEMBAGAAN BAGI
                </Typography>
                <Typography variant="h6" textAlign="center" gutterBottom>
                  {namaPertubuhan}
                </Typography>

                {/* Konten Fasal */}
                <Typography
                  variant="h6"
                  sx={{ mt: 3, fontWeight: "400 !important" }}
                >
                  {finalFasalContent[currentFasal]?.title}
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Typography
                    component="div"
                    dangerouslySetInnerHTML={{
                      __html: `<div class="ql-editor">${finalFasalContent[currentFasal]?.content}</div>`,
                    }}
                    sx={{
                      fontWeight: "400 !important",
                      fontFamily: "Poppins, sans-serif !important",
                      "& *": {
                        fontFamily: "Poppins, sans-serif !important",
                      },
                    }}
                  />
                  {/* <Typography
                    component="div"
                 
                    sx={{
                      fontWeight: "400 !important",
                      fontFamily: "Poppins, sans-serif !important",
                      "& *": {
                        fontFamily: "Poppins, sans-serif !important",
                      },
                    }}
                  />
                  {fasalContent[currentFasal]?.content} */}
                </Box>
              </Box>

              {/* Tombol Navigasi */}
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                sx={{
                  mt: 3,
                  backgroundColor: "var(--primary-color)",
                  borderBottomLeftRadius: "14px",
                  borderBottomRightRadius: "14px",
                  flexShrink: 0,
                }}
              >
                <IconButton
                  onClick={handlePrevious}
                  disabled={currentFasal === 0}
                  aria-label="previous"
                  sx={{ color: "white" }}
                >
                  <ArrowBackIcon />
                </IconButton>
                <IconButton
                  onClick={handleNext}
                  disabled={currentFasal === fasalContent.length - 1}
                  aria-label="next"
                  sx={{ color: "white" }}
                >
                  <ArrowForwardIcon />
                </IconButton>
              </Box>
            </Box>
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                mt: 2,
                flexShrink: 0,
              }}
            >
              <Box sx={{ display: "flex", gap: 1 }}>
                <ButtonPrimary onClick={downloadSemakFasal}>
                  {t("download")}
                </ButtonPrimary>
                <ButtonOutline onClick={() => navigate(-1)}>
                  {t("back")}
                </ButtonOutline>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Stepper dan Card */}
      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <OrganizationStepper
          activeStep={activeStep}
          hidePayment={
            societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI
          }
        />

        {societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI ? (
          <Box
            sx={{
              padding: 3,
              backgroundColor: "white",
              borderRadius: "15px",
              maxHeight: "60vh",
              maxWidth: "18vw",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("kuiri")}
            </Typography>
            <Box
              sx={{
                padding: 3,
                backgroundColor: "#DADADA",
                borderRadius: "15px",
                maxHeight: "60vh",
                maxWidth: "18vw",
              }}
            >
              <Typography
                sx={{
                  mb: 8,
                  fontSize: "12px",
                  color: "#666666",
                  fontWeight: "500 !important",
                }}
              >
                {societyDataRedux.queryText}
              </Typography>
            </Box>
          </Box>
        ) : null}

        <InfoQACard />
      </Box>
    </Box>
  );
};

export default SemakPerlembagaan;
