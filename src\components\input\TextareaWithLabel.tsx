import React from 'react';
import { Grid, Typography } from '@mui/material';
import TextareaAutosize from '@mui/material/TextareaAutosize';

interface TextFieldWithLabelProps {
  label: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  minRows?: number;
  maxRows?: number;
  error?: boolean;
  helperText?: string;
}

export const TextareaWithLabel: React.FC<TextFieldWithLabelProps> = ({
  label,
  value,
  onChange,
  placeholder,
  required = false,
  disabled = false,
  minRows = 3,
  maxRows,
  error = false,
  helperText,
}) => {
  return (
    <Grid container spacing={2} alignItems="flex-start">
      {/* Label */}
      <Grid item xs={12} sm={3}>
        <Typography
          variant="body1"
          sx={{
            color: '#666666',
            fontSize: '14px',
            mt: 1,
          }}
        >
          {label}
          {required && <span style={{ color: 'red' }}>*</span>}
        </Typography>
      </Grid>

      {/* Textarea */}
      <Grid item xs={12} sm={9}>
        <TextareaAutosize
          minRows={minRows}
          maxRows={maxRows}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          disabled={disabled}
          style={{
            width: '100%',
            fontSize: '14px',
            padding: '10px',
            borderRadius: '4px',
            border: `1px solid ${error ? 'red' : '#DADADA'}`,
            backgroundColor: disabled ? '#f5f5f5' : 'white',
            resize: 'none',
            lineHeight: '1.5',
            fontFamily: 'inherit',
          }}
        />
        {helperText && (
          <Typography
            variant="caption"
            sx={{ color: error ? 'red' : '#666', mt: 0.5, display: 'block' }}
          >
            {helperText}
          </Typography>
        )}
      </Grid>
    </Grid>
  );
};

export default TextareaWithLabel;
