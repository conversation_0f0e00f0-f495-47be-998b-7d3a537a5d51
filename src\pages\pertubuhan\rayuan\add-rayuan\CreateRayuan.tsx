/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from "react";
import { Box, Typography, TextField, Grid, Card } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate, useSearchParams } from "react-router-dom";
import ButtonPrimary from "../../../../components/button/ButtonPrimary";
import { ButtonOutline } from "../../../../components/button";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import Input from "../../../../components/input/Input";
import { Controller, useForm } from "react-hook-form";
import useMutation from "../../../../helpers/hooks/useMutation";
import {
  IdTypes,
  ListGelaran,
  ListGender,
  OrganisationPositions,
  ROApprovalType,
  SebabRyuanEnum,
} from "../../../../helpers/enums";
import useQuery from "../../../../helpers/hooks/useQuery";
import { DocumentUploadType } from "@/helpers";
import FileUploader from "@/components/input/fileUpload";
import dayjs from "dayjs";
import { useSnackbar } from "@refinedev/mui";

interface ICommitte {
  jobCode: string;
  societyId: number;
  societyNo: string;
  titleCode: string;
  name: string;
  gender: string;
  nationalityStatus: string;
  identificationType: string;
  identificationNo: string;
  registeredDate: string; // Format: YYYY-MM-DD
  rejectedDate: string; // Format: YYYY-MM-DD
  placeOfBirth: string;
  designationCode: string;
  otherDesignationCode: string;
  employerAddressStatus: string;
  employerName: string;
  employerAddress: string;
  employerPostcode: string;
  employerCountryCode: string;
  employerStateCode: string;
  employerCity: string;
  employerDistrict: string;
  residentialAddress: string;
  residentialPostcode: string;
  residentialAddressStatus: string;
  residentialCountryCode: string;
  residentialStateCode: string;
  residentialDistrictCode: string;
  residentialCity: string;
  email: string;
  telephoneNumber: string;
  phoneNumber: string;
  noTelP: string;
  status: number;
  applicationStatusCode: string;
  pegHarta: string;
  tarikhTukarSu: string; // Format: YYYY-MM-DD
  otherPosition: string;
  batalFlat: boolean;
  blacklistNotice: boolean;
  benarAjk: boolean;
  id: string;
  jawatanPertubuhan: string;
  noPengenalan: string;
  pekerjaan: string;
  alamatPertubuhan: string;
  butiran: string;
  emel: string;
  noTelefon: string;
  noTelefonRumah?: string;
  noTelefonPejabat?: string;
  phonePrefix: string;
  homePhonePrefix?: string;
  homePhoneNumber?: string;
  officePhonePrefix?: string;
  officePhoneNumber?: string;
}

interface ROApproval {
  createdDate: string;
  note: string;
  [key: string]: any;
}

export const CreateRayuan: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [appealData, setAppealData] = useState<any>({});
  const [societyData, setSocietyData] = useState<any>({});
  const [userCommitteeInfo, setUserCommitteeInfo] = useState<any | null>({});
  const [ROApprovalHistory, setROApprovalHistory] =
    useState<ROApproval | null>();
  const [idSebab, setIdSebab] = useState<any | null>(appealData?.idSebab);
  const [error, setError] = useState(false);
  const [error2, setError2] = useState(false);
  const [sebabLain, setSebabLain] = useState<string | null>(
    appealData?.sebabLain
  );

  const [searchParams] = useSearchParams();
  const appealId = searchParams.get("appealId");
  const societyId = searchParams.get("societyId");

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");
  const id = atob(encodedId || "");

  const form = useForm<ICommitte>();

  const {
    register,
    formState: { errors },
    handleSubmit,
    control,
    setValue,
    watch,
    reset,
  } = form;

  const mutate = useMutation({
    url: "society/committee/create",
  });

  const {
    data: appealDataById,
    isLoading: isLoadingAppealDataById,
    refetch,
  } = useQuery({
    url: `society/appeal/getById/${appealId}`,
  });

  const { fetch: getQuery, isLoading: isLoadingQuery } = useMutation({
    url: "society/roQuery/getQuery",
    method: "post",
    onSuccess: (data) => {
      const queryData = data?.data?.data;
      if (queryData?.length > 0) {
        const latestROApproval = queryData?.reduce(
          (latest: ROApproval, current: ROApproval) => {
            return new Date(current.createdDate) > new Date(latest.createdDate)
              ? current
              : latest;
          },
          queryData?.[0] as ROApproval
        );

        setROApprovalHistory(latestROApproval);
      }
    },
    onSuccessNotification: () => {},
  });

  const fetchData = async () => {
    if (appealId) {
      try {
        const response = await fetch(
          `${API_URL}/society/appeal/getById/${appealId}`,
          {
            headers: {
              portal: localStorage.getItem("portal") || "",
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          throw new Error(`Error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        setAppealData(data?.data);
        setSebabLain(data?.data?.sebabLain);
        setIdSebab(data?.data?.idSebab);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      }
    }

    try {
      const response = await fetch(`${API_URL}/society/${societyId}`, {
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setSocietyData(data?.data);
    } catch (error) {
      console.error("Failed to fetch data:", error);
      return null;
    }

    try {
      const response = await fetch(
        `${API_URL}/society/appeal/getUserCommitteeInfo?societyId=${societyId}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setUserCommitteeInfo(data?.data);
    } catch (error) {
      console.error("Failed to fetch data:", error);
      return null;
    }
  };

  const handleidSebabChange = (event: any) => {
    const value = event.target.value;
    setIdSebab(value);
  };

  const [uploadedIds, setUploadedIds] = useState<string[]>([]);
  const [oldFiles, setOldFiles] = useState<string[]>([]);

  const [resetFilesSignal, setResetFilesSignal] = useState(0);

  const handleSenaraiAjk = () => {
    if (isAllowedToEdit) {
      setSebabLain(null);
      setResetFilesSignal((prev) => prev + 1);
    } else {
      navigate(-1);
    }
  };

  const { mutate: create, isLoading: isLoadingCreate } = useCustomMutation();
  const Create = (): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    create(
      {
        url: `${API_URL}/society/appeal/create`,
        method: "post",
        values: {
          societyId: societyId,
          societyNo: societyData.societyNo,
          identificationNo: societyData.identificationNo,
          idSebab: idSebab,
          sebabLain: sebabLain,
          applicationStatusCode: 1,
          appealDate: formattedDate,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data && societyId) {
            navigate(
              `/pertubuhan/paparan-pertubuhan/rayuan/bayaran?appealId=${data?.data?.data}&societyId=${societyId}`
            );
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { mutate: edit, isLoading: isLoadingEdit } = useCustomMutation();
  const Edit = (): void => {
    edit(
      {
        url: `${API_URL}/society/appeal/appealApplication`,
        method: "put",
        values: {
          id: appealData.id,
          societyId: societyId,
          societyNo: appealData.societyNo,
          idSebab: appealData.idSebab,
          sebabLain: sebabLain,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (appealDataById?.data?.data?.paymentDate) {
            navigate(`/pertubuhan/paparan-pertubuhan/rayuan/list-data`);
          } else {
            navigate(
              `/pertubuhan/paparan-pertubuhan/rayuan/bayaran?appealId=${appealId}&societyId=${societyId}`
            );
          }

          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };
  const { enqueueSnackbar } = useSnackbar();
  const handleUpdateAppeal = async () => {
    if (!idSebab || (typeof idSebab === "string" && !idSebab.trim())) {
      setError2(true);
    } else {
      setError2(false);
    }

    if (!sebabLain || (typeof sebabLain === "string" && !sebabLain.trim())) {
      setError(true);
      return;
    } else {
      setError(false);
    }

    if (sebabLain && idSebab && societyId) {
      try {
        if (appealId) {
          Edit();
          if (
            ROApprovalHistory?.note &&
            ROApprovalHistory?.finished === false
          ) {
            const payload = {
              societyId: societyId,
              appealId: appealId,
              roApprovalType: ROApprovalType.SOCIETY_APPEAL.code,
              queryReceiver: "PEMOHON",
            };
            UpdateAnswer(payload);
          }
        } else {
          Create();
        }
      } catch (error) {
        console.error("error:", error);
        return null;
      }
    } else {
      enqueueSnackbar("Ralat: Semasa memperolehi alasan rayuan telah berlaku", {
        variant: "error",
      });
    }
  };

  useEffect(() => {
    if (societyId) {
      const payload = {
        societyId: societyId,
        appealId: appealId,
        roApprovalType: ROApprovalType.SOCIETY_APPEAL.code,
        queryReceiver: "PEMOHON",
      };

      getQuery(payload);
      fetchData();
    }
  }, [societyId]);

  const [isFormIncomplete, setIsFormIncomplete] = useState(true);
  const [isAllowedToEdit, setIsAllowedToEdit] = useState(true);

  useEffect(() => {
    if (appealDataById?.data?.data?.applicationStatusCode) {
      if (
        Number(appealDataById?.data?.data?.applicationStatusCode) === 2 ||
        Number(appealDataById?.data?.data?.applicationStatusCode) === 3 ||
        Number(appealDataById?.data?.data?.applicationStatusCode) === 4
      ) {
        //setIsAllowedToEdit(false);
      }
    }
  }, [appealDataById?.data?.data]);

  // useEffect(() => {
  //   if (userCommitteeInfo?.applicationStatusCode) {
  //   }
  // }, [userCommitteeInfo, sebabLain]);

  const handleUploadComplete = (filesId: any) => {
    setUploadedIds((prev) =>
      prev.includes(filesId) ? prev : [...prev, filesId]
    );
  };

  const handleDeleteFile = (deletedId: any) => {
    setUploadedIds((prev) => prev.filter((id) => id !== deletedId));
  };

  const handleOldUploadedFiles = (files: any) => {
    setOldFiles(files);
  };

  const [reloadQuery, setReloadQuery] = useState(1);

  const { mutate: updateAnswer, isLoading: isLoadingUpdateAnswer } =
    useCustomMutation();

  const UpdateAnswer = (values: any) => {
    updateAnswer(
      {
        url: `${API_URL}/society/roDecision/updateQuery`,
        method: "patch",
        values: values,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status !== "ERROR") {
            setReloadQuery((prev) => prev + 1);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else if (data?.data?.msg) {
            return {
              message: t(data?.data?.msg),
              type: "error",
            };
          } else {
            return {
              message: t("An error has occured"),
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: t(data?.data?.msg),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {},
      }
    );
  };

  return (
    <>
      <Box
        sx={{
          backgroundColor: "white",
          p: 3,
          borderRadius: "16px",
          marginBottom: 2,
          mt: -2,
        }}
      >
        <Box
          sx={{
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",

            display: "flex",
            justifyContent: "space-between",
            minHeight: 120,
          }}
        >
          <Box
            sx={{
              display: "grid",
              px: 5,
              py: 2,
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Typography
              sx={{
                fontFamily: "Poppins",
                fontSize: 18,
                fontWeight: "500!important",
                color: "white",
              }}
            >
              {societyData?.societyName}
            </Typography>
            <Typography
              sx={{
                fontFamily: "Poppins",
                fontSize: 18,
                fontWeight: "500!important",
                color: "white",
              }}
            >
              {societyData?.societyNo
                ? societyData?.societyNo
                : societyData?.applicationNo}
            </Typography>
          </Box>
        </Box>
      </Box>
      <form
        style={{
          padding: "20px",
          border: "1px solid #D9D9D9",
          backgroundColor: "#fff",
          borderRadius: "14px",
        }}
        // onSubmit={handleSubmit(onSubmit)}
      >
        <Box
          sx={{
            pl: 2,
            p: 3,
            borderRadius: "10px",
            border: "0.5px solid #dfdfdf",
          }}
        >
          <Box
            sx={{
              mb: 3,
            }}
          >
            <Typography color={"primary"}>{t("organizationInfo")}</Typography>
          </Box>
          <Box sx={{ display: "grid" }}>
            <Grid container>
              <Controller
                name="designationCode"
                rules={{
                  required: t("fieldRequired"),
                }}
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      label={t("organization_category")}
                      error={!!errors.designationCode?.message}
                      helperText={errors.designationCode?.message}
                      value={
                        societyData?.constitutionType
                          ? societyData?.constitutionType
                          : ""
                      }
                      disabled
                    />
                  );
                }}
              />
            </Grid>
            <Grid container>
              <Controller
                name="registeredDate"
                rules={{
                  required: t("fieldRequired"),
                }}
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      error={!!errors.registeredDate?.message}
                      helperText={errors.registeredDate?.message}
                      label={t("organizationApplicationDate")}
                      type="date"
                      value={dayjs(
                        appealDataById?.data?.data?.registeredDate
                      ).format("DD-MM-YYYY")}
                      disabled
                    />
                  );
                }}
              />
            </Grid>
            <Grid container>
              <Controller
                name="rejectedDate"
                rules={{
                  required: t("fieldRequired"),
                }}
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      error={!!errors.rejectedDate?.message}
                      helperText={errors.rejectedDate?.message}
                      label={t("tarikhKeputusanDitolak")}
                      type={"date"}
                      value={dayjs(
                        appealDataById?.data?.data?.rejectedDate
                      ).format("DD-MM-YYYY")}
                      disabled
                    />
                  );
                }}
              />
            </Grid>
            <Grid container>
              <Controller
                name="designationCode"
                rules={{
                  required: t("fieldRequired"),
                }}
                control={control}
                render={({ field }) => {
                  return appealId ? (
                    <Input
                      {...field}
                      label={t("jenisRayuan")}
                      error={!!errors.designationCode?.message}
                      helperText={errors.designationCode?.message}
                      onChange={handleidSebabChange}
                      value={
                        appealData?.idSebab
                          ? t(
                              `${
                                SebabRyuanEnum[
                                  (appealData?.idSebab as keyof typeof SebabRyuanEnum) ||
                                    "0"
                                ]
                              }`
                            )
                          : ""
                      }
                      disabled
                    />
                  ) : (
                    <Input
                      required
                      {...field}
                      type="select"
                      label={t("jenisRayuan")}
                      options={Object.entries(SebabRyuanEnum).map(
                        ([key, value]) => ({
                          value: key,
                          label: t(value),
                        })
                      )}
                      value={idSebab}
                      onChange={handleidSebabChange}
                      error={error2}
                      helperText={error2 ? t("emptyField") : ""}
                    />
                  );
                }}
              />
            </Grid>
          </Box>
        </Box>
        <Box
          sx={{
            pl: 2,
            p: 3,
            borderRadius: "10px",
            border: "0.5px solid #dfdfdf",
            mt: 1,
          }}
        >
          <Box
            sx={{
              mb: 3,
            }}
          >
            <Typography color={"primary"}>
              {t("maklumatPermohonanRayuanPertubuhan")}
            </Typography>
          </Box>
          <Box sx={{ display: "grid" }}>
            <Controller
              name="jawatanPertubuhan"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    // type="select"
                    label={t("jawatanDalamPertubuhan")}
                    error={!!errors.jawatanPertubuhan?.message}
                    helperText={errors.jawatanPertubuhan?.message}
                    value={t(
                      `${
                        OrganisationPositions.find(
                          (item) =>
                            item?.value ===
                            Number(userCommitteeInfo?.designationCode)
                        )?.label || ""
                      }`
                    )}
                    disabled
                  />
                );
              }}
            />

            <Controller
              name="identificationType"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    type="select"
                    options={IdTypes.map((option) => ({
                      ...option,
                      label: t(option.label),
                    }))}
                    label={t("idType")}
                    error={!!errors.identificationType?.message}
                    helperText={errors.identificationType?.message}
                    value={userCommitteeInfo?.identificationType}
                    disabled
                  />
                );
              }}
            />

            <Controller
              name="noPengenalan"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    label={t("idNumber")}
                    error={!!errors.noPengenalan?.message}
                    helperText={errors.noPengenalan?.message}
                    value={userCommitteeInfo?.identificationNo}
                    disabled
                  />
                );
              }}
            />

            <Controller
              name="name"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    label={t("namaPenuh")}
                    error={!!errors.noPengenalan?.message}
                    helperText={errors.noPengenalan?.message}
                    value={userCommitteeInfo?.name}
                    disabled
                  />
                );
              }}
            />

            <Controller
              name="titleCode"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    type="select"
                    options={ListGelaran}
                    label={t("title")}
                    error={!!errors.titleCode?.message}
                    helperText={errors.titleCode?.message}
                    value={userCommitteeInfo?.titleCode}
                    disabled
                  />
                );
              }}
            />

            <Controller
              name="gender"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    type="select"
                    label={t("gender")}
                    options={ListGender.map((option) => ({
                      ...option,
                      label: t(option.label),
                    }))}
                    error={!!errors.gender?.message}
                    helperText={errors.gender?.message}
                    value={userCommitteeInfo?.gender}
                    disabled
                  />
                );
              }}
            />

            <Controller
              name="pekerjaan"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    // type="select"
                    label={t("occupation")}
                    error={!!errors.pekerjaan?.message}
                    helperText={errors.pekerjaan?.message}
                    // options={listOccupation}
                    value={userCommitteeInfo?.jobCode}
                    disabled
                  />
                );
              }}
            />

            <Controller
              name="alamatPertubuhan"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    multiline
                    rows={4}
                    label={t("alamatPertubuhan")}
                    error={!!errors.alamatPertubuhan?.message}
                    helperText={errors.alamatPertubuhan?.message}
                    value={societyData?.address}
                    disabled
                  />
                );
              }}
            />

            <Controller
              name="butiran"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    sx={{}}
                    multiline
                    rows={4}
                    label={t("butiranAlasanPermohonanRayuan")}
                    error={error}
                    helperText={error ? t("emptyField") : ""}
                    value={sebabLain ? sebabLain : ""}
                    onChange={(e) => setSebabLain(() => e.target.value)}
                    disabled={!isAllowedToEdit}
                  />
                );
              }}
            />
            <Controller
              name="emel"
              rules={{
                required: t("fieldRequired"),
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Emel tidak sah",
                },
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    label={t("email")}
                    error={!!errors.emel?.message}
                    helperText={errors.emel?.message}
                    value={userCommitteeInfo?.email}
                    disabled
                  />
                );
              }}
            />

            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography
                  style={{
                    fontFamily: "Poppins",
                    fontSize: "14px",
                    fontWeight: 400,
                    lineHeight: "18px",
                    textAlign: "left",
                    textUnderlinePosition: "from-font",
                    textDecorationSkipInk: "none",
                    color: "#666666",
                  }}
                >
                  {t("phoneNumber")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8} sx={{ display: "flex", gap: 1 }}>
                <TextField
                  size="small"
                  sx={{ width: "80px", backgroundColor: "#E8E9E8" }}
                  {...register("phonePrefix", {
                    required: t("fieldRequired"),
                  })}
                  error={!!errors.phonePrefix}
                  value={"+60"}
                  disabled
                />
                <TextField
                  sx={{ backgroundColor: "#E8E9E8" }}
                  fullWidth
                  size="small"
                  {...register("phoneNumber", {
                    required: t("fieldRequired"),
                  })}
                  error={!!errors.phoneNumber}
                  value={userCommitteeInfo?.phoneNumber}
                  disabled
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography
                  style={{
                    fontFamily: "Poppins",
                    fontSize: "14px",
                    fontWeight: 400,
                    lineHeight: "18px",
                    textAlign: "left",
                    textUnderlinePosition: "from-font",
                    textDecorationSkipInk: "none",
                    color: "#666666",
                  }}
                >
                  {t("nomborTelefonRumah")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8} sx={{ display: "flex", gap: 1 }}>
                <TextField
                  size="small"
                  sx={{ width: "80px", backgroundColor: "#E8E9E8" }}
                  {...register("homePhonePrefix")}
                  value={"+60"}
                  disabled
                />
                <TextField
                  fullWidth
                  size="small"
                  {...register("homePhoneNumber")}
                  value={userCommitteeInfo?.telephoneNumber}
                  sx={{ backgroundColor: "#E8E9E8" }}
                  disabled
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography
                  style={{
                    fontFamily: "Poppins",
                    fontSize: "14px",
                    fontWeight: 400,
                    lineHeight: "18px",
                    textAlign: "left",
                    textUnderlinePosition: "from-font",
                    textDecorationSkipInk: "none",
                    color: "#666666",
                  }}
                >
                  {t("nomborTelefonPejabat")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8} sx={{ display: "flex", gap: 1 }}>
                <TextField
                  size="small"
                  sx={{ width: "80px", backgroundColor: "#E8E9E8" }}
                  {...register("officePhonePrefix")}
                  value={"+60"}
                  disabled
                />
                <TextField
                  fullWidth
                  size="small"
                  {...register("officePhoneNumber")}
                  sx={{ backgroundColor: "#E8E9E8" }}
                  value={userCommitteeInfo?.noTelP}
                  disabled
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        <FileUploader
          required={true}
          title="addSupportingDocument"
          type={DocumentUploadType.APPEAL}
          societyId={societyData?.id}
          societyNo={societyData?.societyNo}
          appealId={appealId ? Number(appealId) : null}
          // meetingId={null}
          validTypes={["application/pdf"]}
          hasDialog={true}
          dialogOpenBtn="lihatSenaraiSemak"
          dialogTitle="senaraiSemakRayuan"
          dialogConent="senaraiSemakRayuanContent"
          onUploadComplete={handleUploadComplete}
          hasOldFiles={handleOldUploadedFiles}
          disabled={!isAllowedToEdit}
          onDeleteFile={handleDeleteFile}
          resetFilesSignal={resetFilesSignal}
        />
        {ROApprovalHistory?.note && (
          <Box
            sx={{
              pl: 2,
              p: 3,
              mt: 1,
              borderRadius: "10px",
              border: "0.5px solid #dfdfdf",
            }}
          >
            <Box
              sx={{
                mb: 2,
              }}
            >
              <Typography color={"primary"}>{t("kuiri")}</Typography>
            </Box>
            <Box sx={{ display: "grid" }}>
              <Controller
                name="alamatPertubuhan"
                rules={{
                  required: t("fieldRequired"),
                }}
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      multiline
                      rows={4}
                      label={t("catatanKuiri")}
                      error={!!errors.alamatPertubuhan?.message}
                      helperText={errors.alamatPertubuhan?.message}
                      value={ROApprovalHistory?.note}
                      disabled
                    />
                  );
                }}
              />
            </Box>
          </Box>
        )}

        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            mt: 2,
            gap: 2,
          }}
        >
          <ButtonOutline onClick={handleSenaraiAjk}>
            {t("previous")}
          </ButtonOutline>
          <ButtonPrimary
            disabled={
              mutate.isLoading ||
              !isFormIncomplete ||
              !isAllowedToEdit ||
              (uploadedIds.length === 0 && oldFiles.length === 0)
            }
            onClick={async () => await handleUpdateAppeal()}
          >
            {t("update")}
          </ButtonPrimary>
        </Box>
      </form>
    </>
  );
};

export default CreateRayuan;
