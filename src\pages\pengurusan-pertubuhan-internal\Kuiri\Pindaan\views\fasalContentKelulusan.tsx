import React, { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Button, useMediaQuery, useTheme } from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";

import { DokumenIcon } from "@/components/icons";
import DisabledTextField from "@/components/input/DisabledTextField";
import FasalContent from "@/components/FasalContent";
import { useCustom } from "@refinedev/core";
import useMutation from "@/helpers/hooks/useMutation";
import { setLocalStorage } from "@/helpers/utils";
import { API_URL } from "@/api";
import { useState } from "react";
import { ConstitutionType, ApplicationStatus } from "@/helpers/enums";
import CustomPopover from "@/components/popover";
import { useSelector } from "react-redux";
import { getLocalStorage } from "@/helpers/utils";
import { useDownloadAndExportConstitutions } from "@/helpers/hooks/useDownloadConstitutions";

function FasalContentKelulusan(item: any) {
  const { societyId } = item;

  const labelStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { t } = useTranslation();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isMediumScreen = useMediaQuery(theme.breakpoints.down("md"));
  const [soceityConstitutionType, setSocietyConstitutionType] = useState("");
  // const [clauseContentId, setClauseContentId] = useState(1);
  const [currentFasal, setCurrentFasal] = useState(0);
  const [beforeFasal, setBeforeFasal] = useState([]);
  const [afterFasal, setAfterFasal] = useState([]);
  //  const [baseFasal,setBaseFasal] = useState([])
  const { amendmentId } = useParams();
  const fasalRedux = useSelector((state: { fasal: any }) => state.fasal.data);
  const { data: clauseContentBefore, isLoading: loadingBeforeData } = useCustom(
    {
      url: `${API_URL}/society/constitutioncontent/get`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        query: {
          societyId: societyId,
          status: ApplicationStatus["AKTIF"],
          // amendmentId:amendmentId
        },
      },
      queryOptions: {
        enabled: !!societyId,
      },
    }
  );

  const { data: clauseContentAfter, isLoading: loadingAfterData } = useCustom({
    url: `${API_URL}/society/constitutioncontent/get`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: societyId,
        // status: ApplicationStatus["AKTIF"],
        amendmentId: amendmentId,
      },
    },
    queryOptions: {
      enabled: !!societyId,
    },
  });

  const clauseContentDataBefore = clauseContentBefore?.data?.data?.data || [];
  const clauseContentDataAfter = clauseContentAfter?.data?.data?.data || [];

  const { data: constitutionData, isLoading: isConstitutionLoading } =
    useCustom({
      url: `${API_URL}/society/admin/constitutionTypeWithClauseContent/list`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
    });

  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${societyId}/`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess(data) {
        const consituitionType = data?.data?.constitutionType;
        setSocietyConstitutionType(consituitionType);
      },
    },
  });

  const allConstitutions = constitutionData?.data?.data || [];
  const constitutionContentRegisterRequest = getLocalStorage(
    "constitutionContentRegisterRequest",
    null
  );

  const constitutionType =
    fasalRedux.currentAmendmentConstitutionType || ConstitutionType.IndukNGO[1];

  const generatePerlembagaanBefore = () => {
    if (constitutionType) {
      const updatedConstitutions = allConstitutions?.map((item: any) => {
        const updatedClauseContents = item?.clauseContents?.map(
          (clause: any, index: any) => {
            const existingItem = clauseContentDataBefore.find(
              (item: any) => item.clauseContentId === clause.id
            );
            if (existingItem) {
              return {
                ...clause,
                content: existingItem?.description,
                description: existingItem?.description,
              };
            }
            return { ...clause, description: clause?.content };
          }
        );
        return {
          ...item,
          clauseContents: updatedClauseContents,
        };
      });
      updatedConstitutions?.map((item: any) => {
        if (item.name === constitutionType) {
          setBeforeFasal(item.clauseContents);
        }
      });
    } else {
      setBeforeFasal([]);
    }
  };

  const generatePerlembagaanAfter = () => {
    if (constitutionType) {
      const updatedConstitutions = allConstitutions?.map((item: any) => {
        const updatedClauseContents = item?.clauseContents?.map(
          (clause: any, index: any) => {
            const existingItem = clauseContentDataAfter.find(
              (item: any) => item.clauseContentId === clause.id
            );
            if (existingItem) {
              return {
                ...clause,
                description: existingItem?.description,
                isChanged: true,
              };
            }
            return { ...clause, description: clause?.content };
          }
        );
        return {
          ...item,
          clauseContents: updatedClauseContents,
        };
      });
      updatedConstitutions?.map((item: any) => {
        if (item.name === constitutionType) {
          setAfterFasal(item.clauseContents);
        }
      });
    } else {
      setAfterFasal([]);
    }
  };

  useEffect(() => {
    if (clauseContentDataBefore) {
      generatePerlembagaanBefore();
    }
    if (clauseContentAfter) {
      generatePerlembagaanAfter();
    }
  }, [
    constitutionType,
    constitutionData,
    clauseContentAfter,
    clauseContentBefore,
  ]);

  const isLoading =
    loadingAfterData ||
    loadingBeforeData ||
    isConstitutionLoading ||
    isSocietyLoading;

  const isConstitutionTypeBeforeAfterSame =
    societyData?.data?.data?.constitutionType ===
    fasalRedux.currentAmendmentConstitutionType;

  //CHECK BEBAS FASAL (ORGINALLY)
  const getAllBebasTambahContentBEFORE = clauseContentDataBefore
    .filter(
      (item: any) =>
        item.clauseContent === null &&
        (Number(item.constitutionTypeId) === 6 ||
          Number(item.constitutionTypeId) === 7)
    )
    .map((item: any, index: number) => ({
      title: `${t("clause")} ${item?.clauseNo}: ${t(item?.clauseName)}`,
      content: item.description,
      clauseNo: item?.clauseNo,
      description: item.description,
      id: item?.id,
      name: item?.clauseName,
    }));

  //CHECK BEBAS FASAL (CURRENT ADMENDMENT)
  const getAllBebasTambahContentAFTER = clauseContentDataAfter
    .filter(
      (item: any) =>
        item.clauseContent === null &&
        (Number(item.constitutionTypeId) === 6 ||
          Number(item.constitutionTypeId) === 7)
    )
    .map((item: any, index: number) => ({
      title: `${t("clause")} ${item?.clauseNo}: ${t(item?.clauseName)}`,
      content: item.description,
      clauseNo: item?.clauseNo,
      description: item.description,
      id: item?.id,
      name: item?.clauseName,
    }));

  const finalBeforeFasal = [...beforeFasal, ...getAllBebasTambahContentBEFORE];
  const finalAfterFasal = [...afterFasal, ...getAllBebasTambahContentAFTER];

  const isTambahBebas =
    getAllBebasTambahContentBEFORE.length > 0 ||
    getAllBebasTambahContentAFTER.length > 0;

  const { getConstitutionsFile, isLoadingDownloadConstitutions } =
    useDownloadAndExportConstitutions();
    
  const hideIndex = clauseContentDataBefore.findIndex(
    (p: any) => p.hideConstitution == true
  );
  const hideId = clauseContentDataBefore?.[hideIndex]?.clauseContentId ?? null;
  return (
    <>
      {!isLoading && afterFasal.length > 0 && beforeFasal.length > 0 && (
        <FasalContent
          scrollable
          fasalContent={finalBeforeFasal}
          isConstitutionTypeSame={isConstitutionTypeBeforeAfterSame}
          HaveBebasContent={isTambahBebas}
          hideId={hideId}
          fasalContentActions={() => (
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                rowGap: "0.5rem",
                marginTop: "1rem",
              }}
            >
              <Button
                startIcon={<DokumenIcon />}
                variant="outlined"
                sx={{ borderRadius: "0.625rem", width: "100%" }}
                onClick={() => societyId && getConstitutionsFile(societyId, 2)}
              >
                Papar Perlembagaan Asal
              </Button>
              <Button
                startIcon={<DownloadIcon />}
                variant="outlined"
                sx={{ borderRadius: "0.625rem", width: "100%" }}
                onClick={() => societyId && getConstitutionsFile(societyId, 1)}
              >
                Muat Turun Perlembagaan Asal
              </Button>
            </div>
          )}
          compareData={finalAfterFasal}
          compareDataActions={() => (
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                rowGap: "0.5rem",
                marginTop: "1rem",
              }}
            >
              <Button
                startIcon={<DokumenIcon />}
                variant="outlined"
                sx={{ borderRadius: "0.625rem", width: "100%" }}
                onClick={() =>
                  societyId && getConstitutionsFile(societyId, 2, amendmentId)
                }
              >
                Papar Perlembagaan Pindaan
              </Button>
              <Button
                startIcon={<DownloadIcon />}
                variant="outlined"
                sx={{ borderRadius: "0.625rem", width: "100%" }}
                onClick={() =>
                  societyId && getConstitutionsFile(societyId, 1, amendmentId)
                }
              >
                Muat Turun Perlembagaan Pindaan
              </Button>
            </div>
          )}
        />
      )}
    </>
  );
}

export default FasalContentKelulusan;
