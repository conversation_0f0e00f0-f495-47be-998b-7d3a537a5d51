import { <PERSON>, Grid, Icon<PERSON>utton, Typography } from "@mui/material";
import { t } from "i18next";
import { useEffect, useMemo, useState } from "react";
import {
  getMalaysiaAddressList,
  getStateNameById,
} from "../../../../helpers/utils";
import { useNavigate } from "react-router-dom";
import { CrudFilter } from "@refinedev/core";
import { SebabRyuanList, StatusPermohonan } from "../../../../helpers/enums";
import ButtonPrevious from "../../../../components/button/ButtonPrevious";
import { ButtonPrimary } from "../../../../components/button";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { EyeIcon } from "../../../../components/icons";
import DataTable, { IColumn } from "@/components/datatable";
import { useQuery } from "@/helpers";
import Input from "@/components/input/Input";
import ForbiddenPage from "@/pages/forbidden";
import { MaklumatTabProps } from "../maklumatSelectionTabs";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "30px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

function RayuanTab({ disabled }: MaklumatTabProps) {
  if (disabled) {
    return <ForbiddenPage internal />;
  } else {
    const navigate = useNavigate();

    const [appeals, setAppeals] = useState<any[]>([]);
    const [total, setTotal] = useState<number>(0);

    const { control, setValue, watch, getValues, reset, handleSubmit } =
      useForm<FieldValues>({
        defaultValues: {
          pageNo: 1,
          pageSize: 10,
          state: "",
          searchQuery: "",
        },
      });

    const { data, isLoading, refetch } = useQuery({
      url: `society/admin/appeal/findAllByParam`,
      filters: [
        {
          field: "pageSize",
          value: watch("pageSize"),
          operator: "eq",
        },
        {
          field: "pageNo",
          value: watch("pageNo"),
          operator: "eq",
        },
      ],
      autoFetch: false,
      onSuccess: (data) => {
        const appealList = data?.data?.data?.data || [];
        const total = data?.data?.data?.total;
        setTotal(total);
        setAppeals(appealList);
      },
    });

    const handleClearSearch = () => {
      reset();
      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: 10 },
        { field: "pageNo", operator: "eq", value: 1 },
        {
          field: "state",
          operator: "eq",
          value: null,
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: null,
        },
      ];
      refetch({ filters });
    };

    const malaysiaAddressList = getMalaysiaAddressList() ?? [];
    const stateOptions = useMemo(() => {
      return malaysiaAddressList.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
    }, []);

    const handleSearch = () => {
      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: getValues("pageSize") },
        { field: "pageNo", operator: "eq", value: getValues("pageNo") },
        {
          field: "state",
          operator: "eq",
          value: getValues("state"),
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      refetch({ filters });
    };

    const handleChangePage = (newPage: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: watch("pageSize"), operator: "eq" },
        { field: "pageNo", value: newPage, operator: "eq" },
        {
          field: "state",
          operator: "eq",
          value: getValues("state"),
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      setValue("pageNo", newPage);
      refetch({ filters });
    };

    const handleChangePageSize = (newPageSize: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: newPageSize, operator: "eq" },
        { field: "pageNo", value: 1, operator: "eq" },
        {
          field: "isBranch",
          value: true,
          operator: "eq",
        },
        {
          field: "state",
          operator: "eq",
          value: getValues("state"),
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      setValue("pageNo", 1);
      setValue("pageSize", newPageSize);
      refetch({ filters });
    };

    const columns: IColumn[] = [
      {
        field: "namaPemegangJawatan",
        headerName: t("pertubuhan"),
        align: "center",
        renderCell: (params) => <Box>{params.row.societyName ?? "-"}</Box>,
      },
      {
        field: "noPengenalanDiri",
        headerName: t("organizationNumber"),
        align: "center",
        renderCell: (params) => <Box>{params.row.societyNo ?? "-"}</Box>,
      },
      {
        field: "jawatan",
        headerName: t("sebabRayuan"),
        align: "center",
        renderCell: (params) => (
          <Box>
            {t(
              SebabRyuanList.find((item) => item.value === params.row.idSebab)
                ?.label ?? "-"
            )}
          </Box>
        ),
      },
      {
        field: "namaPertubuhan",
        headerName: t("statusPermohonan"),
        align: "center",
        renderCell: (params) => (
          <Box>
            {params.row.applicationStatusCode
              ? t(StatusPermohonan[params.row.applicationStatusCode] ?? "-")
              : "-"}
          </Box>
        ),
      },
      {
        field: "stateCode",
        headerName: t("negeri"),
        align: "center",
        renderCell: ({ row }: any) => getStateNameById(row.stateCode),
      },

      {
        field: "actions",
        headerName: "",
        align: "right",
        renderCell: (params: any) => {
          return (
            <>
              <IconButton
                sx={{ color: "black" }}
                onClick={() =>
                  navigate("pertubuhan/rayuan", {
                    state: {
                      appeal: params.row,
                    },
                  })
                }
              >
                <EyeIcon />
              </IconButton>
            </>
          );
        },
      },
    ];

    useEffect(() => {
      const filters: CrudFilter[] = [
        {
          field: "pageSize",
          value: 10,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: 1,
          operator: "eq",
        },
      ];
      refetch({ filters });
    }, []);

    return (
      <>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
            }}
          >
            <form onSubmit={handleSubmit(handleSearch)}>
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("senaraiPertubuhan")}
              </Typography>
              {/* state */}
              <Controller
                name="state"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="select"
                    label={t("state")}
                    options={stateOptions}
                  />
                )}
              />

              {/* finding/carian */}
              <Controller
                name="searchQuery"
                control={control}
                render={({ field }) => <Input {...field} label={t("search")} />}
              />
              <Grid container mt={3} spacing={2}>
                <Grid
                  item
                  xs={12}
                  sx={{
                    mt: 2,
                    display: "flex",
                    // flexDirection: isMobile ? "column" : "row",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <ButtonPrevious onClick={handleClearSearch}>
                    {t("previous")}
                  </ButtonPrevious>
                  <ButtonPrimary type="submit">{t("search")}</ButtonPrimary>
                </Grid>
              </Grid>
            </form>
          </Box>
          {/* ============= */}
          <Box
            sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
          >
            <Box
              sx={{
                textAlign: "center",
                color: "#fff",
                borderRadius: "13px",
                backgroundColor: "var(--primary-color)",
                py: 2,
              }}
            >
              <Typography variant="h5" gutterBottom>
                {total}
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
                {t("rekodDijumpai")}
              </Typography>
            </Box>

            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mt: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("")}
              </Typography>

              <DataTable
                columns={columns as any[]}
                rows={appeals}
                page={watch("pageNo")}
                rowsPerPage={watch("pageSize")}
                totalCount={total}
                onPageChange={handleChangePage}
                onPageSizeChange={handleChangePageSize}
                isLoading={isLoading}
              />
            </Box>
          </Box>
        </Box>
      </>
    );
  }
}

export default RayuanTab;
