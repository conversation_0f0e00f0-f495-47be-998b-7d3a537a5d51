import { createAsyncThunk, createSlice,PayloadAction } from '@reduxjs/toolkit';




const defaultFasalData: fasalData = {
  id: 0,
  constitutionValues: [],
  clauseContent: "",
  clauseContentId: "",
  edit: false,
  status: 0,
  isKuiri:false,  
};
interface fasalData{
  id:string|number|null;
  constitutionValues:[];
  clauseContent:string;
  clauseContentId:number|string;
  edit:boolean;
  status:number;
  isKuiri:boolean; 
}

interface fasalItems{
  currentFasalId:string|number; 
  currentFasalData:fasalData;
  clauseid:string|number; 
  isDisplayConstituition:boolean;
  currentAmendmentConstitutionType:string;
  isKuiri: boolean;
  IsViewPindaan:number|null;
  createdDate:string|null;
}

interface fasal{
  data:fasalItems
}

const initialState:fasal = { data:{createdDate:"", currentFasalId:"",clauseid:"",isDisplayConstituition:false,currentAmendmentConstitutionType:"", isKuiri: false, IsViewPindaan:null,
  currentFasalData: {
  id: 0,
  constitutionValues: [],
  clauseContent: "",
  clauseContentId: "",
  edit: false,
  status: 0,
  isKuiri:false
},}}

export const fasalSlice = createSlice({
  name: 'fasal',  
  initialState:initialState,
  reducers: { 
    setCurrentFasalId:(state,action:PayloadAction<string|number>)=>{
        state.data.currentFasalId = action.payload
    }, 
    setClauseid:(state,action:PayloadAction<string|number>)=>{
      state.data.clauseid = action.payload
    }, 
    setIsDisplayConstituition:(state,action:PayloadAction<boolean>)=>{
      state.data.isDisplayConstituition = action.payload
    },  
    setCurrentAmendmentConstitutionType:(state,action:PayloadAction<string>)=>{
      state.data.currentAmendmentConstitutionType = action.payload
    }, 
    setCurrentFasalData: (state, action: PayloadAction<Partial<fasalData>>) => {
      state.data.currentFasalData = {
        ...defaultFasalData,  
        ...state.data.currentFasalData,  
        ...action.payload,  
      };
    },
    setIsKuiri:(state,action:PayloadAction<boolean>)=>{
      state.data.isKuiri = action.payload
    },   
    setIsViewPindaan:(state,action:PayloadAction<number|null>)=>{
      state.data.IsViewPindaan = action.payload
    },   
    setCreatedDate:(state,action:PayloadAction<string|null>)=>{
      state.data.createdDate = action.payload
    },  
  },
});
 

 
export const { setCreatedDate ,setIsKuiri, setCurrentFasalId,setClauseid,setIsDisplayConstituition,setCurrentAmendmentConstitutionType,setCurrentFasalData , setIsViewPindaan} = fasalSlice.actions;
 
export default fasalSlice.reducer;