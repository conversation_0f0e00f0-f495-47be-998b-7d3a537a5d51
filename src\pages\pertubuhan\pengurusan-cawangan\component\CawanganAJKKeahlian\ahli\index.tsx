import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Typography, IconButton } from "@mui/material";
import { ButtonPrimary } from "@/components/button";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import {
  AhliList,
  CitizenshipStatus,
  COMMITTEE_TASK_TYPE,
  OrganisationPositions,
  otherPositionSwitchList,
} from "@/helpers/enums";
import { EditIcon, EyeIcon, TrashIcon } from "@/components/icons";
import { DataTable, IColumn } from "@/components";
import ConfirmationDialog from "@/components/dialog/confirm";
import { useQuery } from "@/helpers";
import { useBranchContext } from "@/pages/pertubuhan/BranchProvider";

const AhliPertubuhan: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id: societyId } = useParams();
  const [pageSize, setPageSize] = useState(5);
  const [page, setPage] = useState(1);
  const [openConfirmDelete, setOpenConfirmDelete] = useState<boolean>(false);
  const [memberId, setMemberId] = useState<string>("");

  const {
    isBlackListed,
    isAuthorized,
    isAliranModuleAccess,
    branchId,
    fetchAliranTugasAccessHandle,
    branchData,
  } = useBranchContext();

  useEffect(() => {
    fetchAliranTugasAccessHandle(COMMITTEE_TASK_TYPE.PENGURUSAN_AJK);
  }, []);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const {
    data,
    isLoading: isFetchAhli,
    refetch: fetchAhli,
  } = useQuery({
    url: `society/branch/committee/list`,
    filters: [
      { field: "branchId", operator: "eq", value: branchId },
      { field: "pageNo", operator: "eq", value: page },
      { field: "pageSize", operator: "eq", value: pageSize },
    ],
    autoFetch: true,
  });

  const { mutate: deleteMember, isLoading } = useCustomMutation();
  const clickDeleteMebmer = (memberId: string) => {
    setMemberId(memberId);
    setOpenConfirmDelete(true);
  };

  const handleDeleteMember = () => {
    deleteMember(
      {
        url: `${API_URL}/society/branch/committee/${memberId}`,
        method: "delete",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          // memberList.data = memberList.data?.filter(
          //   (e: any) => e.committeeIcNo != memberId
          // );
          setOpenConfirmDelete(false);
          fetchAhli();
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  useEffect(() => {
    fetchAhli();
  }, [page, pageSize]);

  
  const isPendingStatus =
    Number(branchData?.applicationStatusCode) === 2 &&
    branchData?.status === "008";

  const isAccessible = !isBlackListed && (isAuthorized || isAliranModuleAccess) &&!isPendingStatus;

  const columns: IColumn[] = [
    {
      field: "committeeName",
      headerName: t("name"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return <Box> {row.committeeName ? row.committeeName : "-"}</Box>;
      },
      cellClassName: "custom-cell",
    },
    {
      field: "citizen",
      headerName: t("citizen"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box>
            {" "}
            {row.citizenshipStatus
              ? t(
                  CitizenshipStatus.find(
                    (item) => item.value === parseInt(row.citizenshipStatus)
                  )?.label || "-"
                )
              : "-"}
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      flex: 1,
      field: "position",
      headerName: t("position"),
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box>
            {!otherPositionSwitchList.some(
              (item) => item.value === Number(row.designationCode)
            )
              ? t(
                  OrganisationPositions.find(
                    (item) => item.value === Number(row.designationCode)
                  )?.label || "-"
                )
              : t(
                  row?.otherPosition ??
                    t(
                      OrganisationPositions.find(
                        (item) => item.value === Number(row.designationCode)
                      )?.label || "-"
                    )
                )}
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      flex: 1,
      field: "actions",
      headerName: "",
      align: "right",
      renderCell: (params: any) => {
        const row = params?.row;
        return isAccessible ? (
          <>
            <IconButton
              size="small"
              sx={{ color: "#55556D" }}
              onClick={() =>
                navigate(`edit/${row.id}`, {
                  state: {
                    row,
                    isAccess: isAccessible,
                  },
                })
              }
            >
              <EditIcon
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "1rem",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>
            {AhliList.includes(Number(row?.designationCode)) ? (
              <IconButton
                size="small"
                sx={{ color: "#FF6B6B" }}
                onClick={() => clickDeleteMebmer(row.id)}
              >
                <TrashIcon
                  sx={{
                    fontSize: "1rem",
                    width: "1rem",
                    height: "1rem",
                  }}
                />
              </IconButton>
            ) : null}
          </>
        ) : (
          <>
            <IconButton
              size="small"
              sx={{ color: "#55556D" }}
              onClick={() =>
                navigate(`edit/${row.committeeIcNo}`, {
                  state: {
                    row,
                    isAccess: isAccessible,
                  },
                })
              }
            >
              <EyeIcon
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "1rem",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>
          </>
        );
      },
    },
  ];

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            gap: 3,
            mb: 2,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 22,
              fontWeight: "700 !important",
            }}
          >
            {data?.data?.data?.total ?? 0}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            {t("jumlahAhliPertubuhan")}
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("senaraiAhliPertubuhan")}
          </Typography>

          <DataTable
            columns={columns}
            rows={data?.data?.data?.data || []}
            page={page}
            rowsPerPage={pageSize}
            totalCount={data?.data?.data?.total}
            onPageChange={(newPage: number) => setPage(newPage)}
            onPageSizeChange={(newRowsPerPage: number) =>
              setPageSize(newRowsPerPage)
            }
            isLoading={isFetchAhli}
          />
        </Box>
      </Box>
      {isAccessible ? (
        <>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("memberRegister")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  sx={{
                    color: "#666666",
                    fontWeight: "500!important",
                    fontSize: 14,
                  }}
                >
                  {t("memberRegister")}
                </Typography>

                <ButtonPrimary
                  onClick={() => {
                    navigate(`daftar`, {
                      state: {
                        isAccess: isAccessible,
                      },
                    });
                  }}
                >
                  {t("registerButton")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("sertaPertubuhan")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    color: "#666666",
                    fontWeight: "500!important",
                    fontSize: 14,
                  }}
                >
                  {t("permintaanSertaPertubuhan")}
                </Typography>

                <ButtonPrimary
                  onClick={() => {
                    navigate(`serta-pertubuhan`);
                  }}
                >
                  {t("senaraiPermintaan")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>{" "}
        </>
      ) : (
        <></>
      )}
      <ConfirmationDialog
        status={1}
        turn
        open={openConfirmDelete}
        onClose={() => setOpenConfirmDelete(false)}
        title={t("deleteAhliConfirmation")}
        message={`${t("deleteAhliConfirmation")}`}
        onConfirm={handleDeleteMember}
        onCancel={() => setOpenConfirmDelete(false)}
      />
    </>
  );
};

export default AhliPertubuhan;
