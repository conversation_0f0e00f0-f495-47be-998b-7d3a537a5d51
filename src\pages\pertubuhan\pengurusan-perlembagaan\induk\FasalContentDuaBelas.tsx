import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import { RegExNumbers } from "@/helpers";
import AsalContent from "@/components/asalFasalContent";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
interface FasalContentDuaBelasProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: [];
  name: string;
}

export const FasalContentDuaBelas: React.FC<FasalContentDuaBelasProps> = ({
  activeStep,
  setActiveStep,
  clause,
  asalData,
  name,
}) => {
  const [asal, setAsal] = useState<any>(null);
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [lainlain, setLainLain] = useState("");
  const [kekerapanPelaksanaan, setKekerapanPelaksanaan] = useState(
    t("setiapTahun")
  );
  const [tempohPelaksanaan, setTempohPelaksanaan] = useState("");
  const [notisPanggilanMesyuarat, setNotisPanggilanMesyuarat] = useState("");

  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [bilangan, setBilangan] = useState("");
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { id, clauseId } = useParams();

  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName, address, mailingAddress } =
          responseData?.data?.data;
        setNamaPertubuhan(societyName);
      },
    },
  });

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!pemilihanAjk) {
      errors.pemilihanAjk = t("fieldRequired");
    }

    // if (!kekerapanPelaksanaan) {
    //   errors.kekerapanPelaksanaan = t("fieldRequired");
    // }

    if (!notisPanggilanMesyuarat) {
      errors.notisPanggilanMesyuarat = t("fieldRequired");
    }

    if (!tempohPelaksanaan) {
      errors.tempohPelaksanaan = t("fieldRequired");
    }

    if (!bilangan) {
      errors.bilangan = t("fieldRequired");
    }

    return errors;
  };

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause12Data = JSON.parse(clause12);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        setClauseContentId(clause.clauseContentId);
      }
      if((clause.constitutionValues[0]?.definitionName && clause.constitutionValues[0]?.definitionName !== t("annual") && clause.constitutionValues[0]?.definitionName !== t("biennial"))){
        setPemilihanAjk(t("lainLain"));
        setLainLain(clause.constitutionValues[0]?.definitionName)
      }else{
        setPemilihanAjk(clause.constitutionValues[0]?.definitionName);
      }
      setBilangan(clause.constitutionValues[2]?.definitionName);
      setTempohPelaksanaan(clause.constitutionValues[3]?.definitionName);
      setNotisPanggilanMesyuarat(clause.constitutionValues[4]?.definitionName);
      setIsEdit(clause.edit);
      setAsal(
        asalData.find((item: any) => item.clauseNo === clause.clauseNo
        ) || ""
      );
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);
  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<kekerapan pelaksanaan mesyuarat agung baru>>/gi,
    `<b>${
      kekerapanPelaksanaan || "<<kekerapan pelaksanaan mesyuarat agung baru>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${lainlain ? lainlain : pemilihanAjk   || "<<jenis mesyuarat agung>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bilangan tempoh pelaksanaan mesyuarat agung baru>>/gi,
    `<b>${
      bilangan || "<<bilangan tempoh pelaksanaan mesyuarat agung baru>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh pelaksanaan mesyuarat agung baru>>/gi,
    `<b>${
      tempohPelaksanaan || "<<tempoh pelaksanaan mesyuarat agung baru>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<notis panggilan mesyuarat>>/gi,
    `<b>${notisPanggilanMesyuarat || "<<notis panggilan mesyuarat>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  return (
    <>
      <AsalContent
        asalData={asal?.description || ""}
        clauseId={clauseId}
        name={name}
      />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("generalMeeting")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("jenisMesyuaratAgung")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required error={!!formErrors.pemilihanAjk}>
              <Select
                size="small"
                disabled={isViewMode}
                value={pemilihanAjk}
                displayEmpty
                onChange={(e) => {
                  setPemilihanAjk(e.target.value as string);
                  if ((e.target.value as string) == t("annual")) {
                    setLainLain("")
                    setBilangan("12");
                    setTempohPelaksanaan(t("month"));
                  }
                  if ((e.target.value as string) == t("biennial")) {
                    setLainLain("")
                    setBilangan("24");
                    setTempohPelaksanaan(t("month"));
                  }
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    pemilihanAjk: "",
                  }));
                }}
              >
                <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
                <MenuItem value={t("lainLain")}>{t("lainLain")}</MenuItem>
              </Select>
              {formErrors.pemilihanAjk && (
                <FormHelperText>{formErrors.pemilihanAjk}</FormHelperText>
              )}
            </FormControl>
          </Grid>

          {pemilihanAjk === t("lainLain") ?
          <>
              <Grid item xs={12} md={4}></Grid>
              <Grid item xs={12} md={8}>
                <TextField
                  type="text"
                  size="small"
                  placeholder="Yearly, Biannually, Tri-tahunan"
                  fullWidth
                  required
                  value={lainlain}
                  onChange={(e) => {
                    setLainLain(e.target.value as string);
                  }}
                />
              </Grid>
            </>: null
          }

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("notisPanggilanMesyuarat")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.notisPanggilanMesyuarat}
            >
              <Select
                size="small"
                value={notisPanggilanMesyuarat}
                displayEmpty
                disabled={isViewMode}
                onChange={(e) => {
                  setNotisPanggilanMesyuarat(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    notisPanggilanMesyuarat: "",
                  }));
                }}
              >
                <MenuItem value={"7"}>7</MenuItem>
                <MenuItem value={"14"}>14</MenuItem>
                <MenuItem value={"30"}>30</MenuItem>
              </Select>
              {formErrors.notisPanggilanMesyuarat && (
                <FormHelperText>
                  {formErrors.notisPanggilanMesyuarat}
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <Select disabled size="small" value={t("day")} displayEmpty>
              <MenuItem value={t("day")}>{t("day")}</MenuItem>
            </Select>
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("tempohPelaksanaan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={bilangan}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setBilangan(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    setBilangan: "",
                  }));
                } else {
                  setBilangan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    setBilangan: "Invalid Value",
                  }));
                }
              }}
              type="number"
              error={!!formErrors.bilangan}
              helperText={formErrors.bilangan}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.tempohPelaksanaan}
            >
              <Select
                size="small"
                value={tempohPelaksanaan}
                displayEmpty
                onChange={(e) => {
                  setTempohPelaksanaan(e.target.value as string);
                  setFormErrors((prev) => ({
                    ...prev,
                    tempohPelaksanaan: "",
                  }));
                }}
              >
                <MenuItem value={t("month")}>{t("month")}</MenuItem>
                <MenuItem value={t("year")}>{t("year")}</MenuItem>
              </Select>
              {formErrors.tempohPelaksanaan && (
                <FormHelperText>{formErrors.tempohPelaksanaan}</FormHelperText>
              )}
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              const errors = validateForm();
              if (Object.keys(errors).length > 0) {
                setFormErrors(errors);
                return;
              }

              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: lainlain ? lainlain : pemilihanAjk  ,
                    titleName: "Jenis Mesyuarat Agung",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bilangan,
                    titleName:
                      "bilangan tempoh pelaksanaan mesyuarat agung baru",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tempohPelaksanaan,
                    titleName:
                      "Tempoh pelaksanaan mesyuarat agung baru daripada tarikh terakhir Mesyuarat Agung",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: notisPanggilanMesyuarat,
                    titleName: "Notis panggilan mesyuarat",
                  },
                ],
                clause: "clause12",
                clauseCount: 12,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentDuaBelas;
