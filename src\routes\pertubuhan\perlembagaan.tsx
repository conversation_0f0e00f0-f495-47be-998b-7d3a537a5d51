import { Navigate, Outlet, Route } from "react-router-dom";
import { RouteGuard } from "../../components/RouteGuard";
import { registerRoutes } from "../../helpers/routeDetector";
import Layout from "../../pages/pertubuhan/perlembagaan/Layout";
import {
  CreateMaklumatPerlembagaan,
  ListPindaanPerlembagaan,
  SemakPerlembagaan,
  UpdatePindaanPerlembagaan,
} from "../../pages/pertubuhan/perlembagaan/maklumat-perlembagaan";
import { ListSenaraiPindaan } from "../../pages/pertubuhan/perlembagaan/senarai-pindaan";

// Layout component to wrap all perlembagaan routes with protection
const PerlembagaanLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Layout>
      <Outlet />
    </Layout>
  </RouteGuard>
);

// Register routes with their portal types
registerRoutes({
  // Add your route registrations here
});

export const perlembagaan = {
  routes: (
    <Route path="perlembagaan" element={<PerlembagaanLayout />}>
      <Route index element={<Navigate to="maklumat-perlembagaan" />} />

      <Route path="maklumat-perlembagaan">
        <Route index element={<CreateMaklumatPerlembagaan />} />

        <Route path="pindaan-perlembagaan">
          <Route index element={<ListPindaanPerlembagaan />} />

          <Route path="update/:id">
            <Route index element={<UpdatePindaanPerlembagaan />} />
          </Route>

          <Route path="semakan-perlembagaan">
            <Route index element={<SemakPerlembagaan />} />
          </Route>
        </Route>
      </Route>

      <Route path="senarai-pindaan">
        <Route index element={<ListSenaraiPindaan />} />
      </Route>
    </Route>
  ),
};
