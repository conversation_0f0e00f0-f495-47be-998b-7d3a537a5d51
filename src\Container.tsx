import { useOutlet, useLocation } from "react-router-dom";
import { ChatbotChatArea } from "./components/chatbot/ChatArea";
import { useChatbotContext } from "./contexts/chatbot";
import { useMediaQuery, useTheme } from "@mui/material";
import { useEffect, useState } from "react";

export const Container = () => {
  const theme = useTheme();
  const outlet = useOutlet();
  const location = useLocation();
  const { isChatbotOpenedTablet } = useChatbotContext();
  const matchMobileSize = useMediaQuery(theme.breakpoints.down("md"));
  const [storageCleared, setStorageCleared] = useState(false);

  // Clear specific localStorage parameters when accessing /login or /main routes
  useEffect(() => {
    if (location.pathname === "/login" || location.pathname === "/main") {
      // localStorage.removeItem("portal");
      localStorage.removeItem("refine-auth");
      localStorage.removeItem("user-details");
      console.log("Container: Cleared localStorage for login/main routes");
    }
    // Always set storageCleared to true after effect runs
    setStorageCleared(true);
  }, [location.pathname]);

  return (
    <div
      style={{
        width: "100vw",
        height: "100vh",
        overflow: "hidden",
        display: "flex",
      }}
    >
      <div
        style={{
          width: `${isChatbotOpenedTablet ? 80 : 100}%`,
          height: "100vh",
          overflow: "auto",
          transition: "width 0.3s ease-out",
        }}
      >
        {outlet}
      </div>
      <div
        id="chatbot-chat-area"
        style={{
          width: matchMobileSize ? "0" : !isChatbotOpenedTablet ? "0" : "20%",
          height: "100vh",
          position: "relative",
          overflow: "hidden",
          ...(matchMobileSize && {
            display: "none",
          }),
          opacity: isChatbotOpenedTablet ? 1 : 0,
          transform: `translateX(${isChatbotOpenedTablet ? "0" : "100%"})`,
          transition: "opacity 0.3s ease-out, transform 0.3s ease-out",
          visibility: isChatbotOpenedTablet ? "visible" : "hidden",
        }}
      >
        {storageCleared && <ChatbotChatArea />}
      </div>
    </div>
  );
};
