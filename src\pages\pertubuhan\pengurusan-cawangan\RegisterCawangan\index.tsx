import { useState, useEffect, ChangeEvent } from "react";
import {
  Box,
  Typography,
  Grid,
  useMediaQuery,
  Theme,
  useTheme,
  TextField,
} from "@mui/material";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { Resolver, useForm } from "react-hook-form";
import { MALAYSIA } from "../../../../helpers/enums";
import CustomPopover from "../../../../components/popover";
import { useMap } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import { useDispatch } from "react-redux";
import { setAddressDataRedux } from "../../../../redux/addressDataReducer";
import { setBranchDataRedux } from "../../../../redux/branchDataReducer";
import { LoadingOverlay } from "@/components/loading";
import { SocietyAddressListResponseBodyGet } from "@/models";
import { capitalizeWords, useQuery } from "@/helpers";
import { SelectFieldController, TextFieldController } from "@/components";
import { yupResolver } from "@hookform/resolvers/yup";
import { number, object, string } from "yup";
import AWSLocationMap from "@/components/geocoder/geocoder";
import {
  LocationClient,
  SearchPlaceIndexForTextCommand,
} from "@aws-sdk/client-location";
import { withAPIKey } from "@aws/amazon-location-utilities-auth-helper";
import { Switch } from "@/components/switch";

export interface BranchRegistrationRequestBody {
  name: string;
  address: string;
  stateCode: number;
  districtCode: number;
  city: string;
  postcode: string;
  mailingAddress: string;
  mailingStateCode: number;
  mailingDistrictCode: number;
  mailingCity: string;
  mailingPostcode: string;
  societyId: string | number;
  id?: string | number | null;
}

export type BranchResponseBody<
  Base extends BranchRegistrationRequestBody = BranchRegistrationRequestBody
> = Omit<
  Base,
  | "stateCode"
  | "districtCode"
  | "id"
  | "societyId"
  | "mailingStateCode"
  | "mailingDistrictCode"
> & {
  stateCode: string;
  districtCode: string;
  mailingStateCode: string;
  mailingDistrictCode: string;
  societyId: string | number;
  id: string | number;
  applicationStatusCode: number;
  branchNo: string;
  branchApplicationNo: string;
};

const RecenterAutomatically = () => {
  const map = useMap();

  useEffect(() => {
    map.getCenter();
  }, []);

  return null;
};

export const RegisterCawangan = <
  Payload extends BranchRegistrationRequestBody = BranchRegistrationRequestBody,
  BranchResponseData extends BranchResponseBody<Payload> = BranchResponseBody<Payload>,
  AddressList extends SocietyAddressListResponseBodyGet = SocietyAddressListResponseBodyGet
>() => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const primary = theme.palette.primary.main;
  const sectionStyle = {
    color: primary,
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const inputContainer = {
    "& .MuiInputBase-input": {
      padding: "10px",
      borderRadius: 1,
    },
  };
  const labelStyle = {
    color: "#666666",
    fontWeight: "400 !important",
    fontSize: "14px",
  };

  const { data } = useQuery<{ data: AddressList[] }>({
    url: "society/admin/address/list",
  });
  const dispatch = useDispatch();

  const addressData = data?.data?.data ?? [];

  useEffect(() => {
    dispatch(setAddressDataRedux(addressData));
  }, [addressData]);

  const { mutateAsync: registerCawangan } = useCustomMutation();
  const [isSubmitSuccess, setIsSubmitSuccess] = useState(false);

  const [searchParams] = useSearchParams();
  const [branchId, setBranchId] = useState(() => searchParams.get("id"));
  const [sameAddress, setSameAddress] = useState(false);

  const { data: response, isFetching: isFetchingListData } = useCustom<{
    data: { data: BranchResponseData[] };
  }>({
    url: `${API_URL}/society/branch/getBranchesByParam`,
    queryOptions: {
      enabled: branchId !== null && branchId !== "",
      retry: false,
      cacheTime: 0,
    },
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        branchId: branchId,
      },
    },
  });

  const branchData = response?.data?.data?.data?.[0] ?? null;
  const { id: societyId } = useParams();

  const [bName, setBName] = useState("");
  const [pass, setPass] = useState(true);

  const resolver = yupResolver(
    object()
      .shape({
        name: string()
          .label(t("namaCawangan"))
          // .test({
          //   name: "branch_name_validation",
          //   message: t("branchNameHelper1"),
          //   test: (name, context) => {
          //     if (!name) {
          //       return context.createError({ message: t("fieldRequired") });
          //     }

          //     if (!name || name.length <= 6) {
          //       return false;
          //     }

          //     return ["cawangan ", "bahagian ", "negeri ", "daerah "].some(
          //       (prefix) => name.toLowerCase().startsWith(prefix)
          //     );
          //   },
          // })
          .test({
            name: "branch_name_exist",
            message: t("branchNameAlreadyExist"),
            test: async function (name) {
              const trimmedName = name?.trim().toLowerCase();
              const trimmedOldName = branchData?.name?.trim().toLowerCase();
              // Skip API call if name is too short
              if (!trimmedName || trimmedName.length <= 6) {
                return true;
              }

              // Skip API call if name hasn't changed
              if (bName === trimmedName) {
                return pass;
              }

              //skip API call if old and new name are the same
              if (trimmedOldName === trimmedName) {
                return pass;
              }

              try {
                const response = await fetch(
                  `${API_URL}/society/branch/checkBranchNameExist?branchName=${trimmedName}&societyId=${societyId}`,
                  {
                    method: "GET",
                    headers: {
                      portal: localStorage.getItem("portal") || "",
                      Authorization: `Bearer ${localStorage.getItem(
                        "refine-auth"
                      )}`,
                    },
                  }
                );

                if (response.ok) {
                  const data = await response.json();
                  // If data exists, the name is taken (return error)
                  if (!data?.data) {
                    setBName(trimmedName);
                    setPass(false);
                    return this.createError({
                      message: t("organizationNamePlaceholder"),
                    });
                  }
                  // If data doesn't exist, the name is available

                  setBName(trimmedName);
                  setPass(true);
                  return true;
                }

                // If response not OK, consider invalid

                setBName(trimmedName);
                setPass(false);
                return false;
              } catch (e) {
                setBName(trimmedName);
                setPass(false);
                return this.createError({ message: t("error") });
              }
            },
          })
          .required(),
        address: string().label(t("businessAddressLabel")).required(),
        stateCode: number().label(t("state")).required(),
        districtCode: number().label(t("district")).required(),
        postcode: string()
          .label(t("postcode"))
          .test({
            name: "postcode_validation",
            test: (val: string | undefined, context) => {
              if (typeof val === "string") {
                if (
                  !val.split("").every((num) => !Number.isNaN(parseInt(num)))
                ) {
                  return context.createError({
                    message: t("validation.mustBeNumber"),
                  });
                }
                return val.length === 5;
              }
              return true;
            },
            message: t("postcodeValidation"),
          }),
      })
      .required()
  ) as unknown as Resolver<Payload>;
  const form = useForm<Payload>({
    defaultValues: {
      // @ts-expect-error
      id: branchId,
      societyId,
      name: "",
      address: "",
      stateCode: 0,
      districtCode: 0,
      city: "",
      postcode: "",
    },
    resolver,
    mode: "all",
  });
  const {
    control,
    handleSubmit,
    formState: { isValid, isSubmitting },
    watch,
    setValue,
    reset,
  } = form;

  //const dispatch = useDispatch();
  useEffect(() => {
    if (branchData && societyId && branchId) {
      reset({
        // @ts-expect-error
        name: branchData.name,
        address: branchData.address,
        stateCode: parseInt(branchData.stateCode),
        districtCode: parseInt(branchData.districtCode),
        city: branchData.city,
        postcode: branchData.postcode,
        mailingAddress: branchData?.mailingAddress,
        mailingStateCode: parseInt(branchData.mailingStateCode),
        mailingDistrictCode: parseInt(branchData.mailingDistrictCode),
        mailingCity: branchData?.mailingCity,
        mailingPostcode: branchData?.mailingPostcode,
      });
      // setValue();
      dispatch(setBranchDataRedux(branchData));
    }
  }, [branchData]);
  const { name, stateCode, postcode, mailingStateCode, mailingPostcode } =
    watch();

  useEffect(() => {
    if (postcode.length > 5) {
      // @ts-expect-error
      setValue("postcode", postcode.slice(0, 5));
    }
  }, [postcode]);

  const onSubmit = async (values: Payload) => {
    const endpoint = branchId
      ? `${API_URL}/society/branch/update`
      : `${API_URL}/society/branch/create`;

    if (sameAddress) {
      values.mailingAddress = values.address;
      values.mailingCity = values.city;
      values.mailingDistrictCode = values.districtCode;
      values.mailingPostcode = values.postcode;
      values.mailingStateCode = values.stateCode;
    }

    if (branchId) {
      values.id = branchId;
    }
    await registerCawangan(
      {
        url: endpoint,
        method: branchId ? "put" : "post",
        values,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          console.log("successNotification", data);
          if (data?.data?.status === "SUCCESS") {
            if (data?.data?.data) {
              setBranchId(data.data.data.toString());
            }
            setIsSubmitSuccess(true);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
          setIsSubmitSuccess(false);
        },
      }
    );
  };

  const [organizationCoords, setOrganizationCoords] = useState<
    [number, number]
  >([101.707021, 2.745564]);

  const [organizationCoords2, setOrganizationCoords2] = useState<
    [number, number]
  >([101.707021, 2.745564]);

  // aws location
  const PLACE_INDEX_NAME = import.meta.env.VITE_AWS_PLACE_INDEX;
  const AWS_REGION = import.meta.env.VITE_AWS_API_KEY_REGION;
  const API_KEY = import.meta.env.VITE_AWS_API_KEY;
  const [client, setClient] = useState<LocationClient | null>(null);
  const [locations, setLocations] = useState<string[]>([]);

  // ✅ AWS Location Client Initialization
  useEffect(() => {
    const initializeClient = async () => {
      try {
        const authHelper = await withAPIKey(API_KEY, AWS_REGION);
        const locationClient = new LocationClient(authHelper.getClientConfig());
        setClient(locationClient);
      } catch (error) {
        console.error("❌ Failed to initialize AWS Location Client:", error);
      }
    };
    initializeClient();
  }, []);

  // @ts-expect-error
  const addressValue = watch("address");

  // @ts-expect-error
  const addressValue2 = watch("mailingAddress");

  useEffect(() => {
    // Only search if the address is not empty and has more than 3 characters
    if (addressValue && addressValue.length > 3) {
      // Debounce the search to avoid too many calls
      const delayDebounceFn = setTimeout(() => {
        // @ts-expect-error
        searchLocation(addressValue, "address");
      }, 500); // 500ms delay

      return () => clearTimeout(delayDebounceFn);
    }
  }, [addressValue, client]); // re-run if address or client changes

  useEffect(() => {
    // Only search if the addressValue2 is not empty and has more than 3 characters
    if (addressValue2 && addressValue2.length > 3) {
      // Debounce the search to avoid too many calls
      const delayDebounceFn = setTimeout(() => {
        // @ts-expect-error
        searchLocation2(addressValue2, "address");
      }, 500); // 500ms delay

      return () => clearTimeout(delayDebounceFn);
    }
  }, [addressValue2, client]); // re-run if address or client changes

  const searchLocation = async (query: string, type: "address") => {
    if (!query) return;
    if (!client) {
      console.warn("❌ AWS Location Client has not been initialized");
      return;
    }
    try {
      const command = new SearchPlaceIndexForTextCommand({
        IndexName: PLACE_INDEX_NAME,
        Text: query,
        MaxResults: 1,
        FilterCountries: ["MYS"],
      });
      const response = await client.send(command);
      console.log("place", response);
      if (!response?.Results || response.Results.length === 0) {
        console.warn("⚠️ No locations found");
        setLocations([]);
        return;
      }
      const firstResult = response.Results[0];
      console.log("firstResult", firstResult);

      if (firstResult?.Place?.Geometry?.Point) {
        const [longitude, latitude] = firstResult.Place.Geometry.Point;
        console.log("📍 Coordinates:", longitude, latitude);

        const city = firstResult.Place.Municipality || "";
        const postcode = firstResult.Place.PostalCode || "";
        const stateName = firstResult.Place.Region || "";
        const districtName = firstResult.Place.SubRegion || "";

        console.log("🏙️ City:", city);
        console.log("📮 Postcode:", postcode);
        console.log("🗺️ State name:", stateName);
        console.log("🗺️ District name:", districtName);

        if (type === "address") {
          setOrganizationCoords([longitude, latitude]);
          // @ts-expect-error
          setValue("city", city || watch("city"));
          // @ts-expect-error
          setValue("postcode", postcode || watch("postcode"));
        }
      }

      setLocations(
        response.Results.map(
          (place) => place.Place?.Label ?? "Unknown Location"
        )
      );
    } catch (error) {
      console.error("Error searching location:", error);
    }
  };

  const searchLocation2 = async (query: string, type: "address") => {
    if (!query) return;
    if (!client) {
      console.warn("❌ AWS Location Client has not been initialized");
      return;
    }
    try {
      const command = new SearchPlaceIndexForTextCommand({
        IndexName: PLACE_INDEX_NAME,
        Text: query,
        MaxResults: 1,
        FilterCountries: ["MYS"],
      });
      const response = await client.send(command);
      console.log("place", response);
      if (!response?.Results || response.Results.length === 0) {
        console.warn("⚠️ No locations found");
        setLocations([]);
        return;
      }
      const firstResult = response.Results[0];
      console.log("firstResult", firstResult);

      if (firstResult?.Place?.Geometry?.Point) {
        const [longitude, latitude] = firstResult.Place.Geometry.Point;
        console.log("📍 Coordinates:", longitude, latitude);

        const city = firstResult.Place.Municipality || "";
        const postcode = firstResult.Place.PostalCode || "";
        const stateName = firstResult.Place.Region || "";
        const districtName = firstResult.Place.SubRegion || "";

        console.log("🏙️ City:", city);
        console.log("📮 Postcode:", postcode);
        console.log("🗺️ State name:", stateName);
        console.log("🗺️ District name:", districtName);

        if (type === "address") {
          setOrganizationCoords2([longitude, latitude]);
          // @ts-expect-error
          setValue("mailingCity", city || watch("mailingCity"));

          setValue(
            // @ts-expect-error
            "mailingPostcode",
            // @ts-expect-error
            postcode || watch("mailingPostcode")
          );
        }
      }

      setLocations(
        response.Results.map(
          (place) => place.Place?.Label ?? "Unknown Location"
        )
      );
    } catch (error) {
      console.error("Error searching location:", error);
    }
  };

  const handleLocationSelected = (location: {
    fullAddress: string;
    state: string;
    district: string;
    city: string;
    postcode: string;
  }) => {
    // @ts-expect-error
    setValue("address", location.fullAddress);
    // @ts-expect-error
    setValue("city", location.city);
    // @ts-expect-error
    setValue("postcode", location.postcode);
  };

  const handleLocationSelected2 = (location: {
    fullAddress: string;
    state: string;
    district: string;
    city: string;
    postcode: string;
  }) => {
    // @ts-expect-error
    setValue("mailingAddress", location.fullAddress);
    // @ts-expect-error
    setValue("mailingCity", location.city);
    // @ts-expect-error
    setValue("mailingPostcode", location.postcode);
  };

  const showNBID =
    branchData?.applicationStatusCode === 5 ||
    branchData?.applicationStatusCode === 6 ||
    branchData?.applicationStatusCode === 9;

  const handleSwitchOnChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { checked } = event.target;

    setSameAddress(checked);
  };

  const values = watch();
  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "bold" }}>
              {t("peringatan")} :
            </span>{" "}
            {t("peringatanGunakanAlamatSah")}
          </Typography>
        </Box>
      </Box>
      {branchId && isFetchingListData ? (
        <LoadingOverlay />
      ) : (
        <>
          <Box
            mt={4}
            sx={{
              backgroundColor: "white",
              p: 3,
              borderRadius: "15px",
            }}
          >
            {/** @ts-expect-error */}
            <form onSubmit={handleSubmit(onSubmit)}>
              <Box
                sx={{
                  p: { xs: 1, sm: 2, md: 3 },
                  border: "1px solid #D9D9D9",
                  borderRadius: "14px",
                  mb: 2,
                }}
              >
                <Typography variant="subtitle1" sx={sectionStyle}>
                  {t("maklumatCawangan")}
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Typography sx={labelStyle}>
                        {t("namaCawangan")}
                      </Typography>
                      <CustomPopover
                        customStyles={{
                          maxWidth: "210px",
                          backgroundColor: "white",
                          mt: 1,
                        }}
                        content={
                          <Typography
                            sx={{
                              color: "#FF0000",
                              fontSize: "12px",
                            }}
                          >
                            {t("branchNameHelper1")}
                          </Typography>
                        }
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <TextFieldController
                      name="name"
                      // @ts-expect-error
                      control={control}
                      fullWidth
                      required
                      sxInput={inputContainer["& .MuiInputBase-input"]}
                      placeholder="Contoh: Cawangan Perak, Bahagian Perak"
                      FormHelperTextProps={{
                        sx: {
                          color: "#FF0B0B",
                          fontSize: "12px",
                          fontWeight: "600",
                          marginLeft: "-1px",
                          visibility: "visible",
                        },
                      }}
                    />
                  </Grid>
                  {showNBID && (
                    <>
                      <Grid item xs={12} sm={4}>
                        <Typography sx={labelStyle}>
                          {t("noNBIDCawangan")}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={8}>
                        <TextField
                          size="small"
                          sx={{ backgroundColor: "#E8E9E8" }}
                          fullWidth
                          value={
                            branchData.branchNo
                              ? branchData.branchNo
                              : branchData.branchApplicationNo
                          }
                          disabled
                          InputProps={{ readOnly: true }}
                        />
                      </Grid>
                    </>
                  )}
                </Grid>
              </Box>

              <Box
                sx={{
                  p: { xs: 1, sm: 2, md: 3 },
                  border: "1px solid #D9D9D9",
                  borderRadius: "14px",
                  mb: 2,
                }}
              >
                <Typography variant="subtitle1" sx={sectionStyle}>
                  {t("branchBusinessAddress")}
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("meetingLocation")}{" "}
                      <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <div
                      style={{
                        height: "150px",
                        width: "100%",
                        borderRadius: "8px",
                      }}
                    >
                      <AWSLocationMap
                        longitude={organizationCoords[0]}
                        latitude={organizationCoords[1]}
                        // zoom={20}
                        onLocationSelected={handleLocationSelected}
                      />
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("alamatTempatUrusanCawangan")}
                      <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <TextFieldController
                      name="address"
                      // @ts-expect-error
                      control={control}
                      sxInput={inputContainer["& .MuiInputBase-input"]}
                      // {...register("address")}
                      fullWidth
                      required
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("state")} <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <SelectFieldController
                      name="stateCode"
                      // @ts-expect-error
                      control={control}
                      displayEmpty={true}
                      fullWidth
                      required
                      sx={inputContainer}
                      options={[
                        {
                          label: t("pleaseSelect"),
                          value: "",
                          disabled: true,
                        },
                        ...addressData
                          ?.filter((item) => item.pid === MALAYSIA)
                          ?.map((item) => ({
                            label: capitalizeWords(item.name, null, true),
                            value: item.id,
                          })),
                      ]}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("district")} <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <SelectFieldController
                      name="districtCode"
                      // @ts-expect-error
                      control={control}
                      fullWidth
                      required
                      displayEmpty
                      disabled={!stateCode}
                      sx={inputContainer}
                      options={[
                        {
                          label: t("pleaseSelect"),
                          value: "",
                          disabled: true,
                        },
                        ...addressData
                          ?.filter((item) => item.pid === stateCode)
                          ?.map((item) => ({
                            label: capitalizeWords(item.name, null, true),
                            value: item.id,
                          })),
                      ]}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>{t("city")}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <TextFieldController
                      name="city"
                      // @ts-expect-error
                      control={control}
                      sxInput={inputContainer["& .MuiInputBase-input"]}
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("postcode")} <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <TextFieldController
                      name="postcode"
                      // @ts-expect-error
                      control={control}
                      sx={inputContainer}
                      fullWidth
                      required
                      type="number"
                      isPostcode
                    />
                  </Grid>
                </Grid>
              </Box>
              <Box
                sx={{
                  p: { xs: 1, sm: 2, md: 3 },
                  border: "1px solid #D9D9D9",
                  borderRadius: "14px",
                  mb: 2,
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    mb: 2,
                  }}
                >
                  <Typography variant="subtitle1" sx={sectionStyle}>
                    {t("registeredAddressAndPlaceOfBusinessOfTheBranch")}
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    {" "}
                    {/* Add spacing */}
                    <Typography
                      variant="body2"
                      sx={{ fontSize: "12px", color: "#66666680" }}
                    >
                      {t("sameAsAbove")}
                    </Typography>
                    <Switch
                      checked={sameAddress}
                      onChange={handleSwitchOnChange}
                    />
                  </Box>
                </Box>
                {!sameAddress && (
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Typography sx={labelStyle}>
                        {t("meetingLocation")}{" "}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <div
                        style={{
                          height: "150px",
                          width: "100%",
                          borderRadius: "8px",
                        }}
                      >
                        <AWSLocationMap
                          longitude={organizationCoords2[0]}
                          latitude={organizationCoords2[1]}
                          // zoom={20}
                          onLocationSelected={handleLocationSelected2}
                        />
                      </div>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography sx={labelStyle}>
                        {t("alamatSuratMenyuratCawangan")}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <TextFieldController
                        name="mailingAddress"
                        // @ts-expect-error
                        control={control}
                        sxInput={inputContainer["& .MuiInputBase-input"]}
                        disabled={sameAddress}
                        fullWidth
                        required
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography sx={labelStyle}>
                        {t("state")} <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <SelectFieldController
                        name="mailingStateCode"
                        // @ts-expect-error
                        control={control}
                        displayEmpty={true}
                        fullWidth
                        required
                        disabled={sameAddress}
                        sx={inputContainer}
                        options={[
                          {
                            label: t("pleaseSelect"),
                            value: "",
                            disabled: true,
                          },
                          ...addressData
                            ?.filter((item) => item.pid === MALAYSIA)
                            ?.map((item) => ({
                              label: capitalizeWords(item.name, null, true),
                              value: item.id,
                            })),
                        ]}
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography sx={labelStyle}>
                        {t("district")} <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <SelectFieldController
                        name="mailingDistrictCode"
                        // @ts-expect-error
                        control={control}
                        fullWidth
                        required
                        displayEmpty
                        disabled={!mailingStateCode || sameAddress}
                        sx={inputContainer}
                        options={[
                          {
                            label: t("pleaseSelect"),
                            value: "",
                            disabled: true,
                          },
                          ...addressData
                            ?.filter((item) => item.pid === mailingStateCode)
                            ?.map((item) => ({
                              label: capitalizeWords(item.name, null, true),
                              value: item.id,
                            })),
                        ]}
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography sx={labelStyle}>{t("city")}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <TextFieldController
                        name="mailingCity"
                        // @ts-expect-error
                        control={control}
                        disabled={sameAddress}
                        sxInput={inputContainer["& .MuiInputBase-input"]}
                        fullWidth
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography sx={labelStyle}>
                        {t("postcode")} <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <TextFieldController
                        name="mailingPostcode"
                        // @ts-expect-error
                        control={control}
                        disabled={sameAddress}
                        sx={inputContainer}
                        fullWidth
                        required
                        type="number"
                        isPostcode
                      />
                    </Grid>
                  </Grid>
                )}
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                    mt: 2,
                    gap: 1,
                  }}
                >
                  <ButtonOutline
                    sx={{
                      bgcolor: "white",
                      "&:hover": { bgcolor: "white" },
                      width: isMobile ? "100%" : "auto",
                    }}
                    onClick={() => {
                      reset(); // Clears all fields
                      // navigate(-1);
                    }}
                  >
                    {t("semula")}
                  </ButtonOutline>
                  <ButtonPrimary
                    variant="contained"
                    sx={{
                      width: isMobile ? "100%" : "auto",
                      color: "white",
                    }}
                    type="submit"
                    disabled={!isValid || isSubmitting}
                  >
                    {t("update")}
                  </ButtonPrimary>
                </Box>
              </Box>
            </form>
            <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 3 }}>
              <ButtonPrimary
                variant="contained"
                sx={{ width: isMobile ? "100%" : "auto", color: "white" }}
                onClick={() => navigate(`../pembayaran?id=${branchId}`)}
                disabled={!isSubmitSuccess && !branchId}
              >
                {t("next")}
              </ButtonPrimary>
            </Box>
          </Box>
        </>
      )}
    </>
  );
};

export default RegisterCawangan;
