import {
  Box,
  Typography,
  Dialog,
  DialogContent,
  useMediaQuery,
  useTheme,
  Card,
  CircularProgress,
} from "@mui/material";
import { t } from "i18next";
import React, { useCallback, useEffect, useState } from "react";

import { useCustomMutation } from "@refinedev/core";
import { useNavigate, useParams } from "react-router-dom";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import MaklumatPermohonanSection from "./MaklumatPermohonanSection";
import DokumenSokonganSection from "./DokumenSokonganSection";
import PerlembagaanSection from "./PerlembagaanSection";
import PenyataTahuanSection from "./PenyataTahuanSection";
import { API_URL } from "../../../../api";
import KelulusanAccordion from "./KelulusanAccordion";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import Input from "../../../../components/input/Input";
import {
  DecisionOptionsCodeRayuan,
  <PERSON><PERSON><PERSON>an<PERSON><PERSON><PERSON>,
  PermissionNames,
  pageAccessEnum,
  ROApprovalType,
} from "../../../../helpers/enums";
import { AppDispatch } from "../../../../redux/store";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { fetchSocietyByIdData } from "../../../../redux/APIcalls/societyByIdThunks";
import { filterEmptyValuesOnObject } from "../../../../helpers/utils";
import { CheckedIcon } from "../../../../components/input/customRadio";
import { DocumentUploadType, useMutation, useQuery } from "@/helpers";
import FileUploader from "@/components/input/fileUpload";
import { Label } from "@/components";
import AuthHelper from "@/helpers/authHelper";

type ReadStatusType = {
  [key: number]: Boolean;
};

interface ROApproval {
  createdDate: string;
  note: string;
  [key: string]: any;
}

interface FormValuesD {
  appealId?: any | undefined;
  societyId?: any | undefined;
  societyNo?: any | undefined;
  rejectReason?: any;
  decisionNotes?: any;
  approvedBy?: any;
  applicationStatusCode?: any;
  letterReferenceNo?: any;
}

interface FormValuesK {
  appealId?: any | undefined;
  societyId?: any | undefined;
  societyNo?: any | undefined;
  rejectReason?: any;
  approvedBy?: any;
  applicationStatusCode?: any;
  notesQuery?: any;
  queryReceiver?: any;
  queryAnswer?: any;
}

interface SocietyRegistration {
  id: string | number;
  type: string;
  applicationNo: string;
  societyNo: string;
  societyId: string | number;
  branchId: string | number | null;
  branchNo: string | null;
  societyNonCitizenCommitteeId: string | number | null;
  extensionTimeId: string | number | null;
  branchAmendmentId: string | number | null;
  decision: number;
  akta1: string | null;
  akta2: string | null;
  extensionDays: number | null;
  rejectReason: string;
  note: string;
  approvedBy: number;
  decisionDate: string;
  amendmentId: string | number | null;
  appealId: string | number | null;
  principalSecretaryId: string | number | null;
  createdDate: string;
  createdBy: number;
  modifiedDate: string;
  modifiedBy: number;
}

function RayuanKelulusanSemak() {
  const dispatch: AppDispatch = useDispatch();
  const navigate = useNavigate();
  const hasKelulusanUpdatePermission = AuthHelper.hasPageAccess(
    PermissionNames.KELULUSAN.label,
    pageAccessEnum.Update
  );
  const { id, type } = useParams();
  const decodedId = atob(id ?? "");
  const decodedType = atob(type ?? "");
  const decisionOptions = DecisionOptionsCodeRayuan(t);
  const {
    data: societyDataByIdIndex,
    loading: loadingSociety,
    error: errorSociety,
  } = useSelector((state: any) => state.societyByIdData);
  const [handlingSubmitKuiri, setHandlingSubmitKuiri] = useState(false);
  const [handlingSubmitDescision, setHandlingSubmitDescision] = useState(false);
  const {
    data: appealDataById,
    isLoading: isLoadingAppealDataById,
    refetch: fetchAppealDataById,
  } = useQuery({
    url: `society/appeal/getById/${decodedId}`,
    autoFetch: false,
    onSuccess: (data) => {},
  });

  useEffect(() => {
    if (decodedId) {
      fetchAppealDataById();
      fetchUserRoles();
    }
  }, [decodedId]);

  const [reloadQuery, setReloadQuery] = useState(1);

  let keputusan_induk__kelulusan_items = [
    {
      subTitle: t("maklumatPermohonan"),
      component: <MaklumatPermohonanSection />,
    },
    {
      subTitle: t("supportingDocuments"),
      component: <DokumenSokonganSection />,
    },
    {
      subTitle: t("constitution"),
      component: <PerlembagaanSection />,
    },
    {
      subTitle: t("annualStatement"),
      component: <PenyataTahuanSection />,
    },
    // {
    //   subTitle: t("idk2"),
    //   component: <KeputusanPerm />,
    // },
  ];

  if (Number(appealDataById?.data?.data?.idSebab) === 2) {
    keputusan_induk__kelulusan_items = keputusan_induk__kelulusan_items.filter(
      (item) => item.subTitle !== t("annualStatement")
    );
  }

  useEffect(() => {
    if (!hasKelulusanUpdatePermission) {
      navigate("/internal-user");
    }
  }, [hasKelulusanUpdatePermission]);

  const theme = useTheme();
  const [dialogSejarahKuiriSaveOpen, setDialogSejarahKuiriSaveOpen] =
    useState(false);

  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);

  const [readStatus, setReadStatus] = useState<ReadStatusType>({});

  const [rejectNote, setRejectNote] = useState<SocietyRegistration>();
  const handleChangeCurrentExpandSection =
    (item: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);

      if (isExpanded) {
        // setReadStatus((prevState) => ({ ...prevState, [item]: true }));
        setReadStatus((prevState) => {
          const updatedStatus = keputusan_induk__kelulusan_items.reduce(
            (acc, _, i) => {
              if (i + 1 <= item) {
                acc[i + 1] = true;
              } else {
                acc[i + 1] = !!prevState[i + 1] || false;
              }
              return acc;
            },
            {} as Record<number, boolean>
          );
          return updatedStatus;
        });
      }
    };

  useEffect(() => {
    if (appealDataById?.data?.data?.societyId) {
      // @ts-ignore
      const payload = {
        societyId: appealDataById?.data?.data?.societyId,
        appealId: appealDataById?.data?.data?.id,
        roApprovalType: ROApprovalType.SOCIETY_APPEAL.code,
        // queryReceiver: "JPPM NEGERI",
      };

      if (decodedType !== "none") {
        const RoNotespayload = {
          type: decodedType,
          societyId: appealDataById?.data?.data?.societyId,
          decision: 4,
        };

        getRoApproval(RoNotespayload);
      }

      getQuery(payload);
      dispatch(
        fetchSocietyByIdData({ id: appealDataById?.data?.data.societyId })
      );
    }
  }, [appealDataById?.data?.data, reloadQuery]);

  const [hasPPKDNRole, setHasPPKDNRole] = useState(false);
  const fetchUserRoles = async () => {
    try {
      const portal = localStorage.getItem("portal") || "";
      const authToken = localStorage.getItem("refine-auth");

      if (!authToken) {
        throw new Error("Authorization token is missing");
      }

      const response = await fetch(
        `${API_URL}/user/profile/getUserInternalDetails`,
        {
          headers: {
            portal,
            authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      if (
        data?.data?.userRole?.length > 0 &&
        data.data.userRole.includes("PP KDN")
      ) {
        setHasPPKDNRole(true);
      } else {
        setHasPPKDNRole(false);
      }
      // setHasPPKDNRole(true);
    } catch (error) {
      console.error("Failed to fetch user data");
    }
  };

  const [formValuesD, setFormValuesD] = useState<FormValuesD>({});
  const [formValuesK, setFormValuesK] = useState<FormValuesK>({});

  const [errorsD, setErrorsD] = useState<{ [key: string]: string }>({});

  const handleChangeDecision = useCallback((e: any) => {
    const { name, value } = e.target;
    setFormValuesD((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  }, []);

  const handleChangeKuiri = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setFormValuesK((prevValues) => ({
        ...prevValues,
        [name]: value,
      }));
    },
    []
  );

  const handleQueryOpen = async () => {
    setDialogSejarahKuiriSaveOpen(!dialogSejarahKuiriSaveOpen);
  };

  const [ROQuery, setROQuery] = useState(null);

  //JPPM NEGERI
  const { fetch: getQuery, isLoading: isLoadingQuery } = useMutation({
    url: "society/roQuery/getQuery",
    method: "post",
    onSuccess: (data) => {
      const queryData = data?.data?.data;
      if (queryData?.length > 0) {
        setROQuery(queryData);
      }
    },
    onSuccessNotification: () => {},
    onErrorNotification: () => {},
  });

  const { fetch: getRoApproval, isLoading: isLoadingRoApproval } = useMutation({
    url: "society/roApproval/getAll",
    method: "post",
    onSuccess: (data) => {
      if (data?.data?.data?.[0] != null) {
        setRejectNote(data?.data?.data?.[0]);
      }
    },
    onSuccessNotification: () => {},
    onErrorNotification: () => {},
  });

  //RO APPROVAL SUBMIT
  const [isSuccess, setIsSuccess] = useState(false);

  const { mutate: updateDecision, isLoading: isLoadingUpdateDecision } =
    useCustomMutation();

  const UpdateDecision = (values: any) => {
    updateDecision(
      {
        url: `${API_URL}/society/roDecision/updateApprovalStatus`,
        method: "patch",
        values: values,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          setHandlingSubmitKuiri(false);
          setHandlingSubmitDescision(false);
          if (data?.data?.status !== "ERROR") {
            if (values.applicationStatusCode !== 36) {
              navigate("../rayuan");
            }
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else if (data?.data?.msg) {
            return {
              message: t(data?.data?.msg),
              type: "error",
            };
          } else {
            return {
              message: t("error"),
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          setHandlingSubmitKuiri(false);
          setHandlingSubmitDescision(false);
          return {
            message: t(data?.data?.msg),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          setHandlingSubmitKuiri(false);
          setHandlingSubmitDescision(false);
          setIsSuccess(true);
          setReloadQuery((prev) => prev + 1);
        },
      }
    );
  };

  //KUIRI SUBMIT
  const handleSubmitKuiri = async (event: React.FormEvent) => {
    setHandlingSubmitKuiri(true);
    event.preventDefault();
    if (appealDataById?.data?.data) {
      const payload = {
        societyId: societyDataByIdIndex?.id,
        appealId: appealDataById?.data?.data?.id,
        applicationStatusCode: 36,
        note: formValuesK.notesQuery,
        rejectReason: "",
        roApprovalType: ROApprovalType.SOCIETY_APPEAL.code,
        queryReceiver: "PEMOHON",
      };
      const filterPayload = filterEmptyValuesOnObject(payload);
      UpdateDecision(filterPayload);
    }
  };

  //RO APPROVAL SUBMIT
  const handleSubmitDescision = async (event: React.FormEvent) => {
    setHandlingSubmitDescision(true);
    event.preventDefault();
    const newErrors: { [key: string]: string } = {};
    if (!formValuesD?.applicationStatusCode) {
      setHandlingSubmitDescision(false);
      newErrors.applicationStatusCode = t("requiredValidation");
    }

    if (!formValuesD?.letterReferenceNo) {
      setHandlingSubmitDescision(false);
      newErrors.letterReferenceNo = t("requiredValidation");
    }

    if (Object.keys(newErrors).length > 0) {
      setHandlingSubmitDescision(false);
      setErrorsD(newErrors);
      return;
    }

    setErrorsD({});

    if (appealDataById?.data?.data) {
      const payload = {
        societyId: societyDataByIdIndex?.id,
        appealId: appealDataById?.data?.data?.id,
        applicationStatusCode: formValuesD.applicationStatusCode,
        rejectReason:
          formValuesD.applicationStatusCode === 4
            ? formValuesD.decisionNotes
            : "",
        note:
          formValuesD.applicationStatusCode === 4
            ? ""
            : formValuesD.decisionNotes,
        roApprovalType: ROApprovalType.SOCIETY_APPEAL.code,
        letterReferenceNo: formValuesD.letterReferenceNo,
      };
      const filterPayload = filterEmptyValuesOnObject(payload);
      UpdateDecision(filterPayload);
    }
  };

  //ANSWER SUBMIT
  const { mutate: updateAnswer, isLoading: isLoadingUpdateAnswer } =
    useCustomMutation();

  const UpdateAnswer = (values: any) => {
    updateAnswer(
      {
        url: `${API_URL}/society/roDecision/updateQuery`,
        method: "patch",
        values: values,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status !== "ERROR") {
            setReloadQuery((prev) => prev + 1);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else if (data?.data?.msg) {
            return {
              message: t(data?.data?.msg),
              type: "error",
            };
          } else {
            return {
              message: t("error"),
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: t(data?.data?.msg),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          setIsSuccess(true);
        },
      }
    );
  };

  const handleSubmitAnswer = async (event: React.FormEvent) => {
    event.preventDefault();
    if (appealDataById?.data?.data) {
      const payload = {
        societyId: societyDataByIdIndex?.id,
        appealId: appealDataById?.data?.data?.id,
        // note: formValuesK?.queryAnswer,
        roApprovalType: ROApprovalType.SOCIETY_APPEAL.code,
        queryReceiver: "JPPM NEGERI",
      };

      const filterPayload = filterEmptyValuesOnObject(payload);
      UpdateAnswer(filterPayload);
    }
  };

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Card
            sx={{
              p: 3,
              backgroundColor: "var(--primary-color)",
              borderRadius: "15px",
              boxShadow: "none",
              height: "100px",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "flex-start",
              position: "relative",
              overflow: "hidden",
              "&::before": {
                content: '""',
                position: "absolute",
                top: 0,
                right: 0,
                width: "150px",
                height: "100%",
                // backgroundImage: `url("/pattern.svg")`,
                backgroundSize: "contain",
                backgroundRepeat: "no-repeat",
                backgroundPosition: "right center",
                opacity: 0.2,
              },
            }}
          >
            <Box
              sx={{ display: "flex", alignItems: "center", gap: 2, zIndex: 1 }}
            >
              <Typography
                sx={{
                  fontFamily: "Poppins",
                  fontSize: "20px",
                  fontWeight: 600,
                  color: "white",
                }}
              >
                {societyDataByIdIndex?.societyName}
              </Typography>
            </Box>
            <Typography
              sx={{
                fontFamily: "Poppins",
                fontSize: "14px",
                color: "white",
                mt: 1,
                zIndex: 1,
                opacity: 0.8,
              }}
            >
              {societyDataByIdIndex?.applicationNo}
            </Typography>
          </Card>
        </Box>

        <Box sx={{ mt: 4 }}>
          {keputusan_induk__kelulusan_items.map((item, index) => {
            return (
              <KelulusanAccordion
                key={index}
                subTitle={item.subTitle}
                currentIndex={index + 1}
                currentExpand={currentExpandSection}
                readStatus={readStatus}
                onChangeFunc={handleChangeCurrentExpandSection}
              >
                {item.component}
              </KelulusanAccordion>
            );
          })}
        </Box>

        {rejectNote && rejectNote?.note !== "" ? (
          <Box
            sx={{
              backgroundColor: "white",
              p: 3,
              borderRadius: "15px",
              mt: 2,
            }}
          >
            <Box
              sx={{
                pl: 2,
                p: 3,
                mt: 1,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
              }}
            >
              <Box
                sx={{
                  mb: 3,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography color={"primary"}>{t("roDirectorNote")}</Typography>
              </Box>
              <Box
                sx={{
                  backgroundColor: "white",
                  borderRadius: "15px",
                }}
              >
                <Typography
                  sx={{
                    color: "#666666",
                    fontSize: 14,
                    fontWeight: "400 !important",
                  }}
                >
                  {rejectNote?.note}
                </Typography>
              </Box>
            </Box>
          </Box>
        ) : null}

        {hasPPKDNRole ? (
          <>
            <Box
              sx={{
                backgroundColor: "white",
                p: 3,
                borderRadius: "15px",
                mt: 2,
              }}
              component="form"
              onSubmit={handleSubmitKuiri}
            >
              <Box
                sx={{
                  pl: 2,
                  p: 3,
                  mt: 1,
                  borderRadius: "10px",
                  border: "0.5px solid #dfdfdf",
                }}
              >
                <Box
                  sx={{
                    mb: 5,
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Typography color={"primary"}>{t("kuiri")}</Typography>
                  <ButtonOutline
                    sx={{
                      width: "20%",
                      gap: 2,
                    }}
                    //@ts-ignore
                    disabled={ROQuery?.length > 0 ? false : true}
                    onClick={() => handleQueryOpen()}
                  >
                    <img height={16} width={15} src="/addDocument.png" />
                    {t("queryHistory")}
                  </ButtonOutline>
                </Box>
                {/* <Input
                  label={t("queryTo")}
                  //@ts-ignore
                  disabled={ROQuery?.[0]?.finished === false}
                  name="queryReceiver"
                  value={
                    formValuesK?.queryReceiver ? formValuesK.queryReceiver : ""
                  }
                  type="select"
                  options={KelulusanKuiri}
                  onChange={handleChangeKuiri}
                /> */}
                <Input
                  label={t("catatanKuiri")}
                  name="notesQuery"
                  value={formValuesK?.notesQuery}
                  //@ts-ignore
                  disabled={ROQuery?.[0]?.finished === false}
                  multiline
                  rows={4}
                  onChange={handleChangeKuiri}
                />
                <Box
                  sx={{
                    mt: 5,
                    display: "flex",
                    justifyContent: "flex-end",
                  }}
                >
                  <ButtonPrimary
                    type="submit"
                    disabled={
                      handlingSubmitKuiri ||
                      //@ts-ignore
                      ROQuery?.[0]?.finished === false
                    }
                  >
                    {handlingSubmitKuiri ? (
                      <CircularProgress sx={{ my: 0.5 }} size={15} />
                    ) : (
                      t("submitKuiri")
                    )}
                  </ButtonPrimary>
                </Box>
              </Box>
            </Box>
            <Box
              sx={{
                backgroundColor: "white",
                p: 3,
                borderRadius: "15px",
                mt: 2,
              }}
              component="form"
              noValidate
              onSubmit={handleSubmitDescision}
            >
              <Box
                sx={{
                  pl: 2,
                  p: 3,
                  mt: 1,
                  borderRadius: "10px",
                  border: "0.5px solid #dfdfdf",
                }}
              >
                <Box
                  sx={{
                    mb: 5,
                  }}
                >
                  <Typography color={"primary"}>{t("keputusan")}</Typography>
                </Box>
                <Input
                  label={t("statusPermohonan")}
                  name="applicationStatusCode"
                  type="select"
                  options={decisionOptions}
                  onChange={handleChangeDecision}
                  required
                  value={formValuesD?.applicationStatusCode ?? ""}
                  error={!!errorsD.applicationStatusCode}
                  helperText={errorsD.applicationStatusCode}
                />

                <Input
                  name="letterReferenceNo"
                  label={t("referenceNumber")}
                  onChange={handleChangeDecision}
                  required
                  value={formValuesD?.letterReferenceNo ?? ""}
                  error={!!errorsD.letterReferenceNo}
                  helperText={errorsD.letterReferenceNo}
                />
                {formValuesD?.applicationStatusCode === 3 ||
                formValuesD?.applicationStatusCode === 4 ? null : (
                  <Input
                    label={t("remarks")}
                    name="decisionNotes"
                    // disabled={formValuesK?.queryReceiver === "PEMOHON"}
                    // multiline
                    type="richText"
                    rows={4}
                    onChange={handleChangeDecision}
                    value={
                      formValuesD?.decisionNotes !== undefined &&
                      formValuesD?.decisionNotes !== null
                        ? formValuesD.decisionNotes
                        : ""
                    }
                  />
                )}

                <Box
                  sx={{
                    mt: 5,
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: 2,
                  }}
                >
                  <ButtonOutline onClick={() => navigate(-1)}>
                    {t("back2")}
                  </ButtonOutline>
                  <ButtonPrimary
                    disabled={handlingSubmitDescision}
                    type="submit"
                  >
                    {handlingSubmitDescision ? (
                      <CircularProgress sx={{ my: 0.5 }} size={15} />
                    ) : (
                      t("hantar")
                    )}
                  </ButtonPrimary>
                </Box>
              </Box>
            </Box>
          </>
        ) : (
          <>
            <Box
              sx={{
                backgroundColor: "white",
                p: 3,
                borderRadius: "15px",
                mt: 2,
              }}
              component="form"
              onSubmit={handleSubmitAnswer}
            >
              <Box
                sx={{
                  pl: 2,
                  p: 3,
                  mt: 1,
                  borderRadius: "10px",
                  border: "0.5px solid #dfdfdf",
                  display: "grid",
                }}
              >
                <Box
                  sx={{
                    mb: 5,
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Typography color={"primary"}>{t("kuiri")}</Typography>
                  <ButtonOutline
                    sx={{
                      width: "20%",
                      gap: 2,
                    }}
                    //@ts-ignore
                    disabled={ROQuery?.length > 0 ? false : true}
                    onClick={() => handleQueryOpen()}
                  >
                    <img height={16} width={15} src="/addDocument.png" />
                    {t("queryHistory")}
                  </ButtonOutline>
                </Box>

                <Box sx={{ display: "flex", minWidth: "100%", mb: 1 }}>
                  <Box sx={{ flex: 3.2 }}>
                    <Label text={t("catatanKuiri")} />
                  </Box>
                  <Box sx={{ flex: 6.3 }}>
                    <FileUploader
                      // disabled
                      type={DocumentUploadType.APPEAL}
                      societyId={appealDataById?.data?.data?.societyId}
                      societyNo={appealDataById?.data?.data?.societyNo}
                      appealId={
                        appealDataById?.data?.data?.id
                          ? appealDataById?.data?.data?.id
                          : null
                      }
                      validTypes={[
                        "text/plain",
                        // "application/rtf",
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                        // "application/msword",
                        // "application/vnd.oasis.opendocument.text",
                        "application/pdf",
                      ]}
                    />
                  </Box>
                </Box>
                <Input
                  label={t("JPPMComments")}
                  name="queryAnswer"
                  //@ts-ignore
                  value={ROQuery?.[0]?.note}
                  disabled
                  multiline
                  rows={4}
                  onChange={handleChangeKuiri}
                />

                <Box
                  sx={{
                    mt: 5,
                    display: "flex",
                    justifyContent: "flex-end",
                  }}
                >
                  <ButtonPrimary type="submit" disabled={isLoadingUpdateAnswer}>
                    {isLoadingUpdateAnswer ? (
                      <CircularProgress sx={{ my: 0.5 }} size={15} />
                    ) : (
                      t("submitKuiri")
                    )}
                  </ButtonPrimary>
                </Box>
              </Box>
            </Box>
            <Box
              sx={{
                backgroundColor: "white",
                p: 3,
                borderRadius: "15px",
                mt: 2,
              }}
            >
              <Box
                sx={{
                  pl: 2,
                  p: 3,
                  mt: 1,
                  borderRadius: "10px",
                  border: "0.5px solid #dfdfdf",
                }}
              >
                <Box
                  sx={{
                    mb: 5,
                  }}
                >
                  <Typography color={"primary"}>{t("keputusan")}</Typography>
                </Box>
                <Input
                  label={t("statusPermohonan")}
                  name="letterNo"
                  type="select"
                  options={decisionOptions}
                  disabled
                />
                {/* <Input
                  name="letterReferenceNo"
                  label={t("referenceNumber")}
                  disabled
                /> */}
                <Input
                  label={t("remarks")}
                  name="note"
                  multiline
                  rows={4}
                  disabled
                />

                <Box
                  sx={{
                    mt: 5,
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: 2,
                  }}
                >
                  <ButtonOutline onClick={() => navigate(-1)} sx={{ py: 1 }}>
                    {t("back")}
                  </ButtonOutline>
                  <ButtonPrimary disabled>{t("hantar")}</ButtonPrimary>
                </Box>
              </Box>
            </Box>
          </>
        )}
      </Box>
      <Dialog
        open={dialogSejarahKuiriSaveOpen}
        onClose={() => setDialogSejarahKuiriSaveOpen(false)}
        fullScreen={fullScreen}
        PaperProps={{
          style: {
            borderRadius: "8px",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogContent sx={{ py: 4 }}>
          <Box
            sx={{
              p: 3,
              mt: 1,
              borderRadius: "10px",
              border: "0.5px solid #dfdfdf",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                pb: 4,
                color: "var(--primary-color)",
              }}
              gap={2}
            >
              <ChevronLeftIcon
                sx={{ cursor: "pointer" }}
                onClick={() =>
                  setDialogSejarahKuiriSaveOpen(!dialogSejarahKuiriSaveOpen)
                }
              />
              <Typography sx={{ fontWeight: "4001important" }}>
                {t("queryHistory")}
              </Typography>
            </Box>
            {/* @ts-ignore */}
            {ROQuery?.length ? (
              // @ts-ignore
              ROQuery?.map((item, index) => (
                <Box sx={{ display: "flex", mb: 4 }} key={item.id}>
                  <Box sx={{ mr: 2 }}>
                    <Box
                      sx={{
                        width: 35,
                        height: 35,
                        borderRadius: "50%",
                        border: `1px solid ${
                          item.finished ? "var(--primary-color)" : "#FF0000"
                        }`,
                        backgroundColor: item.finished
                          ? "var(--primary-color)80"
                          : "#FF000080",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      {item.finished ? <CheckedIcon /> : null}
                    </Box>
                    {/* @ts-ignore */}
                    {index !== ROQuery?.length - 1 && !item?.finished && (
                      <Box
                        sx={{
                          width: 2,
                          height: "100%",
                          backgroundColor: "#DADADA",
                          ml: 2,
                        }}
                      />
                    )}
                  </Box>
                  <Box sx={{ width: "100%" }}>
                    <Box sx={{ display: "flex", gap: 3 }}>
                      <Typography
                        sx={{
                          fontWeight: "300",
                          border: "1px solid #5088FF",
                          background: "#5088FF80",
                          borderRadius: "9px",
                          color: "#fff",
                          px: 2,
                          py: 1,
                          fontSize: "12px",
                        }}
                      >
                        Kuiri #{index + 1}
                      </Typography>
                      <Typography
                        sx={{
                          fontWeight: "300",
                          border: "1px solid #DADADA",
                          background: "transparent",
                          borderRadius: "9px",
                          color: "#666666",
                          px: 2,
                          py: 1,
                          fontSize: "12px",
                        }}
                      >
                        {item?.createdDate}
                      </Typography>
                      <Typography
                        sx={{
                          fontWeight: "300",
                          border: "1px solid #DADADA",
                          background: "transparent",
                          borderRadius: "9px",
                          color: "#666666",
                          px: 2,
                          py: 1,
                          fontSize: "12px",
                        }}
                      >
                        Pegawai: {item?.queryReceiver}
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        p: 3,
                        border: "1px solid #D9D9D9",
                        borderRadius: "14px",
                        width: "100%",
                        mt: 2,
                        minHeight: "150px",
                        position: "relative",
                      }}
                    >
                      <Typography sx={{ mb: 3, color: "#666666" }}>
                        {item?.notesQuery || item?.note}
                      </Typography>
                      <Box
                        sx={{
                          fontFamily: '"Poppins", sans-serif',
                          backgroundColor: item.finished
                            ? "var(--primary-color)"
                            : "#FF000080",
                          border: `1px solid ${
                            item.finished ? "var(--primary-color)" : "#FF0000"
                          }`,
                          padding: "6px 20px",
                          borderRadius: "18px",
                          color: "#fff",
                          fontSize: "14px",
                          fontWeight: "400",
                          position: "absolute",
                          bottom: "20px",
                          right: "20px",
                        }}
                      >
                        {item.finished ? t("completed") : t("belumselesai")}
                      </Box>
                    </Box>
                  </Box>
                </Box>
              ))
            ) : (
              <Typography className="label">{t("noData")}</Typography>
            )}
          </Box>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default RayuanKelulusanSemak;
