import React, { useState, useEffect } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
} from "@mui/material";
import BreadcrumbComponent from "../../../components/breadcrumb";
import Sidebar from "../../../components/layout/Sidebar";
import MenuItem from "../../../components/layout/MenuItem";
import { API_URL } from "../../../api";
import {
  ApplicationStatus,
  HideOrDisplayInherit,
  ROApprovalType,
} from "../../../helpers/enums";
import { NavigateBefore } from "@mui/icons-material";
import { useDispatch, useSelector } from "react-redux";
import { setSocietyDataRedux } from "../../../redux/societyDataReducer";
import { useCustom, useCustomMutation } from "@refinedev/core";

type LayoutProps = {
  children: React.ReactNode;
};

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const location = useLocation();
  const [activeSection, setActiveSection] = useState("pendaftaran-pertubuhan");
  const [breadcrumbText, setBreadcrumbText] = useState(
    t("organizationInformation")
  );
  const [societyData, setSocietyData] = useState<any>(null);

  const sections = [
    {
      id: "pendaftaran-pertubuhan",
      label: t("registerOrg"),
      path: "pendaftaran",
      icon: "/penyata.png",
    },
    // Tambahkan bagian lain jika diperlukan
  ];

  const pathParts = location.pathname.split("/");
  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");
  const dispatch = useDispatch();

  const { data } = useCustom({
    url: `${API_URL}/society/${atob(encodedId || "")}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !!encodedId,
    },
  });
  //const tempScoiety = data?.data?.data || [];

  const temp = data?.data?.data || [];

  //console.log(temp);

  const { mutate: queryData } = useCustomMutation();
  useEffect(() => {
    if (temp.applicationStatusCode == ApplicationStatus.KUIRI) {
      queryData(
        {
          method: "post",
          url: `${API_URL}/society/roQuery/getQuery`,
          values: {
            roApprovalType: ROApprovalType.SOCIETY_REGISTRATION.code,
            societyId: atob(encodedId || ""),
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
        },
        {
          onSuccess(data) {
            //console.log("data", data);
            const tempData = {
              ...temp,
              queryText: data?.data?.data[0]?.note,
            };
            dispatch(setSocietyDataRedux(tempData));
          },
        }
      );
    } else {
      dispatch(setSocietyDataRedux(temp));
    }
    setSocietyData(temp);
  }, [temp]);

  const handleSectionClick = (sectionIndex: number) => {
    const section = sections[sectionIndex];
    setActiveSection(section.id);
    setBreadcrumbText(t(section.id));
    navigate(`/pertubuhan/pengurusan-pertubuhan/${section.path}/maklumat-am`);
  };

  const breadcrumbs = [
    { label: t("activityLists"), path: "/pertubuhan" },
    {
      label: t("organizationManagement"),
      path: "/pertubuhan/pengurusan-pertubuhan/pendaftaran/maklumat-am",
    },
    {
      label: t("registerOrg"),
      path: "/pertubuhan/pengurusan-pertubuhan/pendaftaran/maklumat-am",
    },
  ];

  const handleBeforeUnload = (event: BeforeUnloadEvent) => {
    const message = "You have unsaved changes. Are you sure you want to leave?";

    // Standard for most modern browsers
    event.returnValue = message;

    // For some browsers (older versions) that don't use `event.returnValue`
    return message;
  };

  // Set up the event listener on component mount
  useEffect(() => {
    // Add the beforeunload event listener when the component mounts
    window.addEventListener("beforeunload", handleBeforeUnload);

    // Clean up the event listener when the component unmounts
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  const { id } = useParams();
  const [fasalName, setFasalName] = useState<string | null>(null);
  const [constitutionType, setConstitutionType] = useState<string | null>(null);

  useEffect(() => {
    if (societyData) {
      setConstitutionType(societyData.constitutionType);
    }
  }, [societyData]);

  /*useEffect(() => {
    if (
      constitutionType === "Perlembagaan Induk NGO" ||
      constitutionType === "Perlembagaan Induk Keagamaan"
    ) {
      if (id == "1") {
        setFasalName(t("name"));
      } else if (id == "2") {
        setFasalName(t("placeOfBusiness"));
      } else if (id == "3") {
        setFasalName(t("goals"));
      } else if (id == "4") {
        setFasalName(t("skills"));
      } else if (id == "5") {
        setFasalName(t("suspensionAndDismissalOfMembers"));
      } else if (id == "6") {
        setFasalName(t("positionOfAuthority"));
      } else if (id == "7") {
        setFasalName(t("positionOfAuthorityTasks"));
      } else if (id == "8") {
        setFasalName(t("financialResources"));
      } else if (id == "9") {
        setFasalName(t("financialMangement"));
      } else if (id == "10") {
        setFasalName(t("auditor"));
      } else if (id == "11") {
        setFasalName(t("propertyOfficer"));
      } else if (id == "12") {
        setFasalName(t("generalMeeting"));
      } else if (id == "13") {
        setFasalName(t("constitutionalAmendment"));
      } else if (id == "14") {
        setFasalName(t("constitutionalInterpretation"));
      } else if (id == "15") {
        setFasalName(t("adviser"));
      } else if (id == "16") {
        setFasalName(t("ban"));
      } else if (id == "17") {
        setFasalName(t("liquidation"));
      } else if (id == "18") {
        setFasalName(t("flagEmblemBadges"));
      }
    } else if (constitutionType === "Perlembagaan Faedah Bersama") {
      if (id === "1") {
        setFasalName("Nama");
      } else if (id === "2") {
        setFasalName("Tempat Urusan");
      } else if (id === "3") {
        setFasalName("Matlamat");
      } else if (id === "4") {
        setFasalName("Keahlian");
      } else if (id === "5") {
        setFasalName("Jawatankuasa");
      } else if (id === "6") {
        setFasalName("Tugas-tugas Jawatankuasa");
      } else if (id === "7") {
        setFasalName("Sumber Kewangan");
      } else if (id === "8") {
        setFasalName("Wang Tabungan");
      } else if (id === "9") {
        setFasalName("Bantuan Kewangan Atau Nafkah");
      } else if (id === "10") {
        setFasalName("Syarat Bantuan Bagi Yang Berkaitan Sahaja");
      } else if (id === "11") {
        setFasalName("Juruaudit");
      } else if (id === "12") {
        setFasalName("Pengamanah");
      } else if (id === "13") {
        setFasalName("Perkara Am");
      } else if (id === "14") {
        setFasalName("Mesyuarat Agung");
      } else if (id === "15") {
        setFasalName("Mesyuarat Agung Khas");
      } else if (id === "16") {
        setFasalName("Pindaan Perlembagaan");
      } else if (id === "17") {
        setFasalName("Tafsiran Perlembagaan Pertubuhan");
      } else if (id === "18") {
        setFasalName("Larangan");
      } else if (id === "19") {
        setFasalName("Pembubaran");
      } else if (id === "20") {
        setFasalName("Bendera, Lambang dan Lencana");
      }
    } else {
      if (id === "1") {
        setFasalName("Nama");
      } else if (id === "2") {
        setFasalName("Tempat Urusan");
      } else if (id === "3") {
        setFasalName("Matlamat");
      } else if (id === "4") {
        setFasalName("Keahlian");
      } else if (id === "5") {
        setFasalName("Pemberhentian dan Pemecatan Ahli");
      } else if (id === "6") {
        setFasalName("Jawatankuasa Induk");
      } else if (id === "7") {
        setFasalName("Tugas-tugas Jawatankuasa Induk");
      } else if (id === "8") {
        setFasalName("Sumber Kewangan");
      } else if (id === "9") {
        setFasalName("Pengurusan Kewangan");
      } else if (id === "10") {
        setFasalName("Juruaudit");
      } else if (id === "11") {
        setFasalName("Pegawai Harta dan/atau Pegawai Awam");
      } else if (id === "12") {
        setFasalName("Mesyuarat Agung");
      } else if (id === "13") {
        setFasalName("Pembubaran");
      } else if (id === "14") {
        setFasalName("Penubuhan dan pembubaran cawangan-cawangan");
      } else if (id === "15") {
        setFasalName("Mesyuarat Agung Cawangan");
      } else if (id === "16") {
        setFasalName("Jawatankuasa Cawangan");
      } else if (id === "17") {
        setFasalName("Tugas-tugas Jawatankuasa Cawangan");
      } else if (id === "18") {
        setFasalName("Kewangan Cawangan");
      } else if (id === "19") {
        setFasalName("Peruntukan-peruntukan Umum Mengenai Cawangan");
      } else if (id === "20") {
        setFasalName("Pindaan Perlembagaan");
      } else if (id === "21") {
        setFasalName("Tafsiran Perlembagaan");
      } else if (id === "22") {
        setFasalName("Larangan");
      } else if (id === "23") {
        setFasalName("Bendera, lambang, dan lencana");
      }
    }
  }, [id, t, constitutionType]);*/

  const navKey = [
    "/pertubuhan/pengurusan-pertubuhan/pendaftaran/maklumat-am",
    "/pertubuhan/pengurusan-pertubuhan/pendaftaran/mesyuarat-penubuhan",
    "/pertubuhan/pengurusan-pertubuhan/pendaftaran/perlembagaan",
    "/pertubuhan/pengurusan-pertubuhan/pendaftaran/senarai-ajk",
    "/pertubuhan/pengurusan-pertubuhan/pendaftaran/dokumen-sokongan",
    "/pertubuhan/pengurusan-pertubuhan/pendaftaran/bayaran",
  ];

  const handleBackClick = () => {
    const currentPath = location.pathname;
    const queryString = location.search;
    const currentIndex = navKey.findIndex((step) => currentPath.includes(step));

    if (
      currentPath.includes("/bayaran/online") ||
      currentPath.includes("/bayaran/kaunter")
    ) {
      navigate(navKey[5] + (queryString || ""));
    } else if (currentIndex > 0) {
      const previousStep = navKey[currentIndex - 1] + (queryString || "");
      navigate(previousStep);
    } else if (currentIndex === 0) {
      navigate("/pengurus-pertubuhan/pertubuhan");
    } else {
      navigate(-1);
    }
  };

  useEffect(() => {
    const fasalName = params.get("name");
    setFasalName(fasalName);
  }, [id]);

  return (
    <Box
      sx={{
        // mt: 10,
        // px: { xs: 3, sm: 6, md: 10, lg: 15 },
        "& .MuiTypography-root": {
          fontFamily: "'Poppins', 'Roboto', 'Helvetica', 'Arial', sans-serif",
          fontWeight: "700",
        },
        transition: "max-height 0.3s ease",
      }}
    >
      {/* <BreadcrumbComponent breadcrumbs={breadcrumbs} /> */}

      <Box
        onClick={() => {
          if (
            location.pathname.includes("create-ajk") ||
            location.pathname.includes("create-ajk-bukanwn")
          ) {
            navigate(
              -1
              // `pertubuhan/pengurusan-pertubuhan/pendaftaran/senarai-ajk?id=${encodedId}`
            );
          } else {
            handleBackClick();
            // navigate(
            //   -1
            //   location.pathname.includes("fasal") ||
            //     location.pathname.includes("semak-perlembagaan")
            //     ? `/pertubuhan/pengurusan-pertubuhan/pendaftaran/perlembagaan?id=${encodedId}`
            //     : "/pertubuhan"
            // );
          }
        }}
        sx={{
          display: "flex",
          alignItems: "center",
          mb: 3,
          gap: 2,
          cursor: "pointer",
        }}
      >
        <IconButton size="small" sx={{ color: "#666666", p: 0 }}>
          <NavigateBefore />
        </IconButton>

        <Typography
          sx={{ color: "#666666", fontSize: 18, fontWeight: "400 !important" }}
        >
          {location.pathname.includes("fasal")
            ? `${t("clause")}  ${id}: ${fasalName}`
            : t("registerOrg")}
        </Typography>
      </Box>

      <Box sx={{ display: "flex" }}>
        {/* <Sidebar>
          {sections.map((section, idx) => (
            <Box key={section.id}>
              <MenuItem
                onClick={() => handleSectionClick(idx)}
                active={section.id === activeSection}
                icon={
                  <img
                    src={section.icon}
                    alt={section.label}
                    style={{
                      width: 15,
                      height: 15,
                      objectFit: "contain",
                      filter: `brightness(0) saturate(100%) invert(${
                        section.id === activeSection ? 1 : 0
                      }) grayscale(${
                        section.id === activeSection ? 0 : 1
                      }) brightness(${section.id === activeSection ? 1 : 0.5})`,
                    }}
                  />
                }
              >
                {section.label}
              </MenuItem>
            </Box>
          ))}
        </Sidebar> */}

        <Box sx={{ flexGrow: 1 }}>
          {/* <Typography
            variant="subtitle1"
            sx={{
              backgroundColor: "var(--primary-color)",
              padding: "8px 30px",
              marginBottom: "16px",
              borderRadius: "14px",
              fontWeight: "bold",
              color: "white",
              boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
              fontSize: "18px",
              display: HideOrDisplayInherit,
            }}
          >
            {t("registerOrg")}
            {societyData &&
              `: ${societyData?.data?.societyName}/ ${societyData?.data?.applicationNo}`}
          </Typography> */}
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default Layout;
