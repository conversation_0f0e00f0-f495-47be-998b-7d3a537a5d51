import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Box, Grid, TextField, Typography } from "@mui/material";
import { useCustom } from "@refinedev/core";
import { API_URL } from "../../../api";
import { useParams } from "react-router-dom";
import { MALAYSIA } from "../../../helpers/enums";
import CustomPopover from "../../../components/popover";
import { Switch } from "../../../components/switch";
import { useDispatch } from "react-redux";
import { setAddressDataRedux } from "../../../redux/addressDataReducer";
import { useSelector } from "react-redux";
import { useQuery } from "@/helpers";
import { setUserPermissionRedux } from "@/redux/userReducer";

interface ListItem {
  value: any;
  label: any;
}

export const MaklumatPertubuhan: React.FC = () => {
  const { t } = useTranslation();
  const [sameAddress, setSameAddress] = useState(false);
  const { id } = useParams();
  const dispatch = useDispatch();

  const { data, isLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData = data?.data?.data || [];

  useQuery({
    url: `society/isManageAuthorized`,
    filters: [{ field: "societyId", operator: "eq", value: id }],
    onSuccess: (data) => {
      console.log("isManageAuthorized", data);
      dispatch(setUserPermissionRedux(data?.data?.data));
    },
  });

  useEffect(() => {
    setAddressDataRedux(addressData);
  }, [addressData]);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const sectionStyleCustom = {
    color: "var(--primary-color)",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
    mt: 1,
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  const StateList = addressData
    ?.filter((item: any) => item.pid === MALAYSIA)
    .map((item: any) => ({ value: item.id, label: item.name }));

  const [districtList, setDistrictList] = useState<ListItem[]>([]);
  const [mailingDistrictList, setMailingDistrictList] = useState<ListItem[]>(
    []
  );

  useEffect(() => {
    if (societyDataRedux?.stateCode) {
      setDistrictList(
        addressData
          ?.filter(
            (item: any) => item.pid === Number(societyDataRedux?.stateCode)
          )
          .map((item: any) => ({ value: item.id, label: item.name }))
      );
    }
    if (societyDataRedux?.mailingStateCode) {
      setMailingDistrictList(
        addressData
          ?.filter(
            (item: any) =>
              item.pid === Number(societyDataRedux?.mailingStateCode)
          )
          .map((item: any) => ({ value: item.id, label: item.name }))
      );
    }
  }, [societyDataRedux]);

  useEffect(() => {
    const isSame =
      societyDataRedux?.address === societyDataRedux?.mailingAddress &&
      societyDataRedux?.stateCode === societyDataRedux?.mailingStateCode &&
      societyDataRedux?.districtCode ===
        societyDataRedux?.mailingDistrictCode &&
      societyDataRedux?.city === societyDataRedux?.mailingCity &&
      societyDataRedux?.postcode === societyDataRedux?.mailingPostcode;

    setSameAddress(isSame);
  }, [societyDataRedux]);

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("organizationInfo")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t("organizationName")}</Typography>
                <CustomPopover
                  customStyles={{
                    maxWidth: "210px",
                    backgroundColor: "white",
                    mt: 1,
                  }}
                  content={
                    <Typography
                      sx={{
                        color: "#FF0000",
                        fontSize: "12px",
                      }}
                    >
                      {t("organizationNameHelper")}
                    </Typography>
                  }
                />
              </Box>
            </Grid>
            <Grid item xs={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                required
                placeholder={t("organizationNamePlaceholder")}
                value={societyDataRedux?.societyName}
                disabled
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("businessAddress")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("alamatTempatUrusan")}{" "}
                <Typography
                  sx={{ color: "red", display: "inline" }}
                  component={"span"}
                >
                  *
                </Typography>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                required
                // value={`${
                //   addressData.find((item: any) => item.pid === 2)?.name || ""
                // }, ${
                //   addressData.find((item: any) => item.pid === 1)?.name || ""
                // }, ${
                //   addressData.find((item: any) => item.pid === 0)?.name || ""
                // }`}
                value={societyDataRedux?.address}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("state")}{" "}
                <Typography
                  sx={{ color: "red", display: "inline" }}
                  component={"span"}
                >
                  *
                </Typography>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                required
                disabled
                value={
                  StateList.find(
                    (item: any) =>
                      item.value === Number(societyDataRedux?.stateCode)
                  )?.label || ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("district")}{" "}
                <Typography
                  sx={{ color: "red", display: "inline" }}
                  component={"span"}
                >
                  *
                </Typography>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                required
                disabled
                value={
                  districtList.find(
                    (item: any) =>
                      item.value === Number(societyDataRedux?.districtCode)
                  )?.label || ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("city")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                value={societyDataRedux?.city}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("postcode")}{" "}
                <Typography
                  sx={{ color: "red", display: "inline" }}
                  component={"span"}
                >
                  *
                </Typography>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                value={societyDataRedux?.postcode}
                disabled
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              mb: sameAddress ? 0.8 : 2,
              gap: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyleCustom}>
              {t("mailingAddress")}
            </Typography>
            <Typography
              variant="body2"
              sx={{ ml: "auto", fontSize: "12px", color: "#666666" }}
            >
              {t("sameAsAbove")}
            </Typography>
            <Switch
              disabled
              checked={sameAddress}
              onChange={(e) => setSameAddress(e.target.checked)}
            />
          </Box>
          {sameAddress ? null : (
            <Grid container spacing={2}>
              <>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("alamatTempatUrusan")}{" "}
                    <Typography
                      sx={{ color: "red", display: "inline" }}
                      component={"span"}
                    >
                      *
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    size="small"
                    sx={{ backgroundColor: "#E8E9E8" }}
                    fullWidth
                    value={societyDataRedux?.mailingAddress}
                    disabled
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("state")}{" "}
                    <Typography
                      sx={{ color: "red", display: "inline" }}
                      component={"span"}
                    >
                      *
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    size="small"
                    sx={{ backgroundColor: "#E8E9E8" }}
                    fullWidth
                    required
                    disabled
                    value={
                      StateList.find(
                        (item: any) =>
                          item.value ===
                          Number(societyDataRedux?.mailingStateCode)
                      )?.label || ""
                    }
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("district")}{" "}
                    <Typography
                      sx={{ color: "red", display: "inline" }}
                      component={"span"}
                    >
                      *
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    size="small"
                    sx={{ backgroundColor: "#E8E9E8" }}
                    fullWidth
                    required
                    disabled
                    value={
                      mailingDistrictList.find(
                        (item: any) =>
                          item.value ===
                          Number(societyDataRedux?.mailingDistrictCode)
                      )?.label || ""
                    }
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("city")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    size="small"
                    sx={{ backgroundColor: "#E8E9E8" }}
                    fullWidth
                    value={societyDataRedux?.mailingCity}
                    disabled
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("postcode")}{" "}
                    <Typography
                      sx={{ color: "red", display: "inline" }}
                      component={"span"}
                    >
                      *
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    size="small"
                    sx={{ backgroundColor: "#E8E9E8" }}
                    fullWidth
                    value={societyDataRedux?.mailingPostcode}
                    disabled
                  />
                </Grid>
              </>
            </Grid>
          )}
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("organizationContactInfo")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("mobileNumber")}{" "}
                <Typography
                  sx={{ color: "red", display: "inline" }}
                  component={"span"}
                >
                  *
                </Typography>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                value={societyDataRedux?.phoneNumber}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("faxNumber")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                value={societyDataRedux?.faxNumber}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("email")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                value={societyDataRedux?.email}
                disabled
              />
            </Grid>
          </Grid>
        </Box>
      </Box>
    </>
  );
};

export default MaklumatPertubuhan;
