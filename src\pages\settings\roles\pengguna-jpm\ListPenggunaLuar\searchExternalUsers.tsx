import { Box, Grid, Theme, Typography, useMediaQuery } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useContext, useEffect, useState } from "react";
import ButtonPrevious from "../../../../../components/button/ButtonPrevious";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { SearchContext } from "../../../../../contexts/searchProvider";
import { ListUserStatus } from "../../../../../helpers/enums";
import Input from "../../../../../components/input/Input";
import { API_URL } from "../../../../../api";

interface FormValues {
  state?: string | null;
  status?: string | null;
  name?: string | null;
  position?: string | null;
}

function SearchExternalUsers() {
  const { t } = useTranslation();
  const {
    setPageExternal,
    setPageSizeExternal,
    pageExternal,
    pageSizeExternal,
    setSearchExternalResult,
  } = useContext(SearchContext);

  function FetchUsers(reset: boolean) {
    fetch(`${API_URL}/user/admin/getExternalUsers`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        portal: localStorage.getItem("portal") || "",
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      body: reset
        ? JSON.stringify({
            state: null,
            status: null,
            name: null,
            position: null,
            pageNo: pageExternal,
            pageSize: pageSizeExternal,
          })
        : JSON.stringify({
            ...formValues,
            pageNo: pageExternal,
            pageSize: pageSizeExternal,
          }),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Erorr when fetching external users");
        }
        return response.json();
      })
      .then((data) => {
        setSearchExternalResult(data);
      })
      .catch((error) => {
        console.error("Error:", error);
      });
  }

  useEffect(() => {
    FetchUsers(false);
  }, [pageExternal, pageSizeExternal]);

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const [formValues, setFormValues] = useState<FormValues>({
    state: null,
    status: null,
    name: null,
    position: null,
  });

  const handleChange = (e: any) => {
    const { name, value } = e.target;

    setFormValues({
      ...formValues,
      [name!]: value as string,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setPageExternal(1);
    setPageSizeExternal(10);
    const newErrors: { [key in keyof FormValues]?: string } = {};

    if (Object.keys(newErrors).length === 0) {
      const updatedFormValues = Object.fromEntries(
        Object.entries(formValues).map(([key, value]) => [
          key,
          typeof value === "string" && value.trim() === "" ? null : value,
        ])
      );

      setFormValues(updatedFormValues);

      FetchUsers(false);
    }
  };

  const handleClearSearch = () => {
    setFormValues({
      state: null,
      status: null,
      name: null,
      position: null,
    });

    FetchUsers(true);
  };

  const [translatedList, setTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);

  useEffect(() => {
    const newList = ListUserStatus.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setTranslatedList(newList);
  }, [t]);

  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Typography className={"title"} sx={{ mb: 4 }}>
        {t("maklumatPengguna")}
      </Typography>
      <Input
        value={formValues.status ? formValues.status : ""}
        name="status"
        onChange={handleChange}
        label={t("status")}
        options={translatedList}
        type="select"
        placeholder={t("pleaseSelectStatus")}
      />

      <Input
        value={formValues.name ? formValues.name : ""}
        name="name"
        onChange={handleChange}
        label={t("search")}
        placeholder={t("pleaseEnterName")}
      />
      <Grid container mt={3} spacing={2}>
        <Grid
          item
          xs={12}
          sx={{
            mt: 2,
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            justifyContent: "flex-end",
            gap: 1,
          }}
        >
          <ButtonOutline onClick={handleClearSearch}>
            {t("previous")}
          </ButtonOutline>
          <ButtonPrimary type="submit">{t("cari")}</ButtonPrimary>
        </Grid>
      </Grid>
    </Box>
  );
}

export default SearchExternalUsers;
