import React, { useState } from "react";
import {
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Typography,
  Box,
  IconButton,
  useMediaQuery,
  useTheme,
  Grid,
} from "@mui/material";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import parse, { domToReact, DOMNode } from "html-react-parser";
import { FasalContentProps } from "..";
import { checkFasalDifferences } from "@/helpers";

const CompareSameType: React.FC<FasalContentProps> = ({
  back,
  fasalContent,
  HaveBebasContent,
  downloadButton,
  downloadFunction,
  isLoadingDownload,
  closeButton,
  compareData,
  fasalContentActions,
  compareDataActions,
  scrollable,
  hideId,
  isConstitutionTypeSame = false,
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isMediumScreen = useMediaQuery(theme.breakpoints.down("md"));
  const [currentFasal, setCurrentFasal] = useState(0);

  if (fasalContent.length === 0) {
    console.log("no data");
    return;
  }

  for (let i = 0; i < fasalContent.length; i++) {
    fasalContent[i].description =
      fasalContent[i].description?.replaceAll(/<</gi, "&lt;&lt;") ||
      fasalContent[i].description;
    fasalContent[i].description =
      fasalContent[i].description?.replaceAll(/>>/gi, "&gt;&gt;") ||
      fasalContent[i].description;
  }

  if (compareData) {
    for (let i = 0; i < compareData.length; i++) {
      compareData[i].description =
        compareData[i].description?.replaceAll(/<</gi, "&lt;&lt;") ||
        compareData[i].description;
      compareData[i].description =
        compareData[i].description?.replaceAll(/>>/gi, "&gt;&gt;") ||
        compareData[i].description;
    }
  }

  const handlePrevious = () => {
    if (currentFasal > 0) {
      setCurrentFasal(currentFasal - 1);
    }
  };

  const handleNext = () => {
    if (currentFasal < fasalContent.length - 1) {
      setCurrentFasal(currentFasal + 1);
    }
  };

  const transform = (node: any) => {
    if ("children" in node) {
      return domToReact(node.children as DOMNode[], { replace: transform });
    }

    return node;
  };

  let compareFasalDescription = "";
  let compareFasalIsChange = false;
  if (compareData) {
    const { description, isChanged } = checkFasalDifferences(
      fasalContent[currentFasal]?.description || "",
      compareData[currentFasal]?.description || ""
    );
    compareFasalDescription = description;
    compareFasalIsChange = isChanged;
  }

  function unescapeHTML(html: string) {
    const temp = document.createElement("textarea");
    temp.innerHTML = html;
    return temp.value;
  }

  return (
    <>
      <Box
        sx={{
          minHeight: "100%",
          gridTemplateColumns: isMediumScreen ? "1fr" : "1fr 1fr",
          rowGap: 2,
          columnGap: 6,
          ...(scrollable ? { overflow: "auto" } : {}),
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={2}>
            <Box
              sx={{
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                color: "#666666",
                "& .Mui-selected": {
                  backgroundColor: "var(--primary-color) !important",
                  color: "white",
                },
                "& .Mui-selected:hover": {
                  backgroundColor: "var(--primary-color) !important",
                  color: "white",
                },
              }}
            >
              <List sx={{ p: 0 }}>
                {/* for comparison bebas content */}
                {HaveBebasContent && compareData
                  ? compareData.map((fasal: any, index: any) => {
                      return (
                        <ListItem key={index} disablePadding>
                          <ListItemButton
                            sx={{
                              py: 1.3,
                              borderBottom: "3px solid #DADADA",
                              "&.Mui-selected": {
                                backgroundColor: "var(--primary-color)",
                                color: "#fff",
                                borderRadius:
                                  currentFasal === 0
                                    ? "14px 14px 0 0"
                                    : currentFasal === compareData.length - 1
                                    ? "0 0 14px 14px"
                                    : "",
                              },
                            }}
                            selected={currentFasal === index}
                            onClick={() => setCurrentFasal(index)}
                          >
                            <ListItemText
                              primary={
                                <Typography
                                  sx={{
                                    fontWeight: "400!important",
                                    display: "flex",
                                    justifyContent: "center",
                                  }}
                                >
                                  {`${t("clause")} ${index + 1}`}
                                </Typography>
                              }
                            />
                          </ListItemButton>
                        </ListItem>
                      );
                    })
                  : fasalContent.map((fasal, index) => {
                      if (hideId) {
                        if (fasal.id === hideId) {
                          return;
                        }
                      }

                      return (
                        <ListItem key={index} disablePadding>
                          <ListItemButton
                            sx={{
                              py: 1.3,
                              borderBottom: "3px solid #DADADA",
                              "&.Mui-selected": {
                                backgroundColor: "var(--primary-color)",
                                color: "#fff",
                                borderRadius:
                                  currentFasal === 0
                                    ? "14px 14px 0 0"
                                    : currentFasal === fasalContent.length - 1
                                    ? "0 0 14px 14px"
                                    : "",
                              },
                            }}
                            selected={currentFasal === index}
                            onClick={() => setCurrentFasal(index)}
                          >
                            <ListItemText
                              primary={
                                <Typography
                                  sx={{
                                    fontWeight: "400!important",
                                    display: "flex",
                                    justifyContent: "center",
                                  }}
                                >
                                  {`${t("clause")} ${index + 1}`}
                                </Typography>
                              }
                            />
                          </ListItemButton>
                        </ListItem>
                      );
                    })}

                {/* {fasalContent.map((fasal, index) => { 

                if(hideId){
                  if(fasal.id === hideId){
                    return
                   } 
                } 
                
                return ( 
                  <ListItem key={index} disablePadding>
                    <ListItemButton
                      sx={{
                        py: 1.3,
                        borderBottom: "3px solid #DADADA",
                        "&.Mui-selected": {
                          backgroundColor: "var(--primary-color)",
                          color: "#fff",
                          borderRadius:
                            currentFasal === 0
                              ? "14px 14px 0 0"
                              : currentFasal === fasalContent.length - 1
                              ? "0 0 14px 14px"
                              : "",
                        },
                      }}
                      selected={currentFasal === index}
                      onClick={() => setCurrentFasal(index)}
                    >
                      <ListItemText
                        primary={
                          <Typography
                            sx={{
                              fontWeight: "400!important",
                              display: "flex",
                              justifyContent: "center",
                            }}
                          >
                            {`${t("clause")} ${index + 1}`}
                          </Typography>
                        }
                      />
                    </ListItemButton>
                  </ListItem>
                );
              })} */}
              </List>
            </Box>
          </Grid>
          <Grid item xs={compareData ? 5 : 10}>
            {compareData && (
              <Box sx={{ fontSize: "14px", color: "#666666", pb: 1 }}>
                Sebelum pindaan
              </Box>
            )}
            <Box
              sx={{
                pt: 3,
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                height: "100%",
                color: "#666666",
                fontWeight: "400 !important",
              }}
            >
              <Box
                sx={{
                  height: "100%",
                  display: "flex",
                  flexDirection: "column",
                }}
              >
                <Box sx={{ mt: 2, height: "100%" }}>
                  <Box
                    sx={{
                      whiteSpace: "pre-wrap",
                      wordWrap: "break-word",
                      py: 2,
                      px: 3,
                      width: "100%",
                      height: "100%",
                    }}
                  >
                    <Typography sx={{ fontWeight: "500", pb: 3 }}>
                      {t("clause")} {currentFasal + 1} :{" "}
                      {fasalContent[currentFasal]?.name}
                    </Typography>
                    <Typography sx={{ fontWeight: "400 !important" }}>
                      {parse(fasalContent[currentFasal]?.description, {
                        replace: transform,
                      })}
                      {/* {fasalContent[currentFasal]?.description} */}
                    </Typography>
                  </Box>
                </Box>
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  sx={{
                    mt: 3,
                    backgroundColor: "var(--primary-color)",
                    borderBottomLeftRadius: "14px",
                    borderBottomRightRadius: "14px",
                    flexShrink: 0,
                  }}
                >
                  <IconButton
                    onClick={handlePrevious}
                    disabled={currentFasal === 0}
                    aria-label="previous"
                    sx={{ color: "white" }}
                  >
                    <ArrowBackIcon />
                  </IconButton>
                  <IconButton
                    onClick={handleNext}
                    disabled={currentFasal === fasalContent.length - 1}
                    aria-label="next"
                    sx={{ color: "white" }}
                  >
                    <ArrowForwardIcon />
                  </IconButton>
                </Box>
              </Box>
            </Box>
            {compareData
              ? fasalContentActions?.(fasalContent[currentFasal], currentFasal)
              : null}
          </Grid>
          {compareData && (
            <Grid item xs={5}>
              <Box sx={{ fontSize: "14px", color: "#666666", pb: 1 }}>
                Selepas pindaan
              </Box>
              <Box
                sx={{
                  pt: 3,
                  border: "1px solid #D9D9D9",
                  borderRadius: "14px",
                  height: "100%",
                  color: "#666666",
                  fontWeight: "400 !important",
                }}
              >
                <Box
                  sx={{
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                  }}
                >
                  <Box sx={{ mt: 2, height: "100%" }}>
                    {compareData && (
                      <>
                        <Box
                          sx={{
                            whiteSpace: "pre-wrap",
                            wordWrap: "break-word",
                            py: 2,
                            px: 3,
                            height: "100%",
                          }}
                        >
                          <Box
                            sx={{
                              fontWeight: "500",
                              pb: 3,
                              display: "flex",
                              flexDirection: "column",
                            }}
                          >
                            {t("clause")} {currentFasal + 1} :{" "}
                            {compareData[currentFasal]?.name}
                            <Typography
                              sx={{ color: "#ff0000", fontSize: "12px" }}
                            >
                              {compareFasalIsChange &&
                              compareData[currentFasal]?.isChanged
                                ? null
                                : t("NoChangesMade")}
                            </Typography>
                          </Box>

                          <Typography sx={{ fontWeight: "400 !important" }}>
                            {parse(
                              unescapeHTML(
                                compareFasalIsChange &&
                                  compareData[currentFasal]?.isChanged
                                  ? compareFasalDescription
                                  : // get back the previous fasal when it is not any change
                                    fasalContent[currentFasal]?.description ||
                                      ""
                              )
                            )}
                          </Typography>
                        </Box>
                      </>
                    )}
                  </Box>
                  <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    sx={{
                      mt: 3,
                      backgroundColor: "var(--primary-color)",
                      borderBottomLeftRadius: "14px",
                      borderBottomRightRadius: "14px",
                      flexShrink: 0,
                    }}
                  >
                    <IconButton
                      onClick={handlePrevious}
                      disabled={currentFasal === 0}
                      aria-label="previous"
                      sx={{ color: "white" }}
                    >
                      <ArrowBackIcon />
                    </IconButton>
                    <IconButton
                      onClick={handleNext}
                      disabled={currentFasal === fasalContent.length - 1}
                      aria-label="next"
                      sx={{ color: "white" }}
                    >
                      <ArrowForwardIcon />
                    </IconButton>
                  </Box>
                </Box>
              </Box>
              {compareDataActions?.(fasalContent[currentFasal], currentFasal) ??
                null}
            </Grid>
          )}
        </Grid>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box
            display="flex"
            justifyContent="flex-end"
            gap={1}
            sx={{ width: "100%", mt: 2 }}
          >
            {back ? (
              <ButtonOutline
                sx={{ gridColumn: "1 / -1", ml: "auto" }}
                onClick={() => {
                  navigate(-1);
                }}
              >
                {t("back")}
              </ButtonOutline>
            ) : null}

            {downloadButton ? (
              <Box display="flex" justifyContent="flex-end">
                <ButtonOutline
                  disabled={isLoadingDownload}
                  sx={{ gridColumn: "1 / -1", ml: "auto" }}
                  onClick={(e) =>
                    downloadFunction && downloadFunction(undefined)
                  }
                >
                  {t("download")}
                </ButtonOutline>
              </Box>
            ) : null}

            {closeButton ? (
              <ButtonPrimary
                variant="contained"
                sx={{ width: isMobile ? "100%" : "auto" }}
                onClick={() => navigate(-1)}
              >
                {t("save")}
              </ButtonPrimary>
            ) : null}
          </Box>
        </Grid>
      </Grid>
    </>
  );
};

export default CompareSameType;
