import React, { useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import {
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Typography,
  CircularProgress,
  Box,
  TablePagination,
  SxProps,
  Theme,
} from "@mui/material";
import CustomPagination from "../pagination/CustomPagination";
import MoreVertIcon from "@mui/icons-material/MoreVert";

type RenderCellParams<T> = {
  row: T;
  rowIndex: number;
};

export interface IColumn<T = Record<string, any>> {
  field: keyof T | "" | string;
  headerName?: string;
  flex?: number;
  renderCell?: ({ row, rowIndex }: RenderCellParams<T>) => React.ReactNode;
  cellClassName?: string;
  idField?: string;
  align?: "left" | "center" | "right";
  headerAlign?: "left" | "center" | "right";
  width?: number;
  background?: string;
}

interface DataTableProps<T extends Record<string, any>> {
  columns: IColumn<T>[];
  rows: T[];
  page: number;
  rowsPerPage: number;
  totalCount: number;
  onPageChange?: (newPage: number) => void;
  onPageSizeChange?: (newPageSize: number) => void;
  pagination?: boolean;
  paginationPosition?: "left" | "center" | "right";
  align?: "left" | "center" | "right";
  sx?: SxProps<Theme>;
  idField?: string;
  customNoDataText?: string;
  isLoading?: boolean;
  paginationType?: "standard" | "custom";
  noPagination?: boolean;
  rowHeight?: number;
  labelRowsPerPage?: string;
  clientPaginationMode?: boolean;
  numbered?: boolean;
  // New props for drag and drop reordering
  enableRowReordering?: boolean;
  isReorderingMode?: boolean;
  onReorder?: (newOrder: string[]) => void;
  saveReorder?: boolean;
}

const tableCellStyle = {
  color: "#666666",
  fontSize: "13px",
};

export const DataTable = <T extends Record<string, any>>({
  columns,
  rows,
  page,
  rowsPerPage,
  totalCount,
  pagination = true,
  onPageChange,
  onPageSizeChange = undefined,
  paginationPosition = "right",
  idField = "id",
  align = "left",
  sx,
  customNoDataText,
  isLoading = false,
  paginationType = "standard",
  noPagination = false,
  rowHeight,
  labelRowsPerPage: initialLabelRowsPerPage,
  clientPaginationMode = false,
  numbered = false,
  // New props for drag and drop
  enableRowReordering = false,
  isReorderingMode = false,
  saveReorder = false,
  onReorder,
}: DataTableProps<T>) => {
  const { t } = useTranslation();
  const [localRows, setLocalRows] = React.useState<T[]>(rows);
  const [draggedRow, setDraggedRow] = React.useState<number | null>(null);

  useEffect(() => {
    setLocalRows(rows);
  }, [rows]);

  const labelRowsPerPage = initialLabelRowsPerPage ?? t("rowsPerPage");
  const justifyContent = {
    left: "flex-start",
    center: "center",
    right: "flex-end",
  }[paginationPosition];

  const handleDragStart = (e: React.DragEvent, rowIndex: number) => {
    setDraggedRow(rowIndex);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/html", rowIndex.toString());
  };

  const handleDragOver = (e: React.DragEvent, rowIndex: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDrop = (e: React.DragEvent, targetRowIndex: number) => {
    e.preventDefault();
    if (draggedRow === null) return;

    const newRows = [...localRows];
    const [movedRow] = newRows.splice(draggedRow, 1);
    newRows.splice(targetRowIndex, 0, movedRow);

    setLocalRows(newRows);
    setDraggedRow(null);
  };

  const handleReorderComplete = () => {
    if (onReorder) {
      const newOrder = localRows.map((row) => String(row[idField]));
      onReorder(newOrder);
    }
  };

  useEffect(() => {
    if (saveReorder) {
      handleReorderComplete();
    } else {
      isReorderingMode = false;
    }
  }, [saveReorder]);

  const paginatedRows = useMemo(() => {
    if (clientPaginationMode) {
      const startIndex = (page - 1) * rowsPerPage;
      if (localRows) {
        return localRows?.slice(startIndex, startIndex + rowsPerPage);
      }
    }
    return localRows;
  }, [localRows, page, rowsPerPage, clientPaginationMode]);
  const actualTotalCount = clientPaginationMode ? localRows.length : totalCount;

  return (
    <>
      <TableContainer sx={{ overflow: "auto", ...sx }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              {/* Numbered column header */}
              {numbered && (
                <TableCell
                  sx={{
                    width: 50,
                    fontWeight: 500,
                    textAlign: "center",
                    background: "#FFF",
                    ...tableCellStyle,
                  }}
                ></TableCell>
              )}
              {/* Drag handle column header - only shown in reordering mode */}
              {enableRowReordering && isReorderingMode && (
                <TableCell
                  sx={{
                    width: 50,
                    fontWeight: 500,
                    textAlign: "center",
                    background: "#FFF",
                    ...tableCellStyle,
                  }}
                ></TableCell>
              )}
              {/* Regular column headers */}
              {columns.map((column, index) => (
                <TableCell
                  key={index}
                  sx={{
                    width: column.width || "auto",
                    flex: column.flex || 1,
                    fontWeight: 500,
                    textAlign: column.headerAlign || "center",
                    background: column.background || "#FFF",
                    ...tableCellStyle,
                  }}
                >
                  {column.headerName}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={
                    columns.length +
                    (numbered ? 1 : 0) +
                    (isReorderingMode ? 1 : 0)
                  }
                  sx={{
                    textAlign: "center",
                    ...tableCellStyle,
                  }}
                >
                  <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                  >
                    <CircularProgress size={24} />
                    <Typography sx={{ ml: 2 }}>{t("loading")}</Typography>
                  </Box>
                </TableCell>
              </TableRow>
            ) : localRows?.length > 0 ? (
              paginatedRows?.map((row, rowIndex) => (
                <TableRow
                  key={row[idField] || rowIndex}
                  sx={{ height: rowHeight }}
                  draggable={isReorderingMode}
                  onDragStart={(e) =>
                    isReorderingMode && handleDragStart(e, rowIndex)
                  }
                  onDragOver={(e) =>
                    isReorderingMode && handleDragOver(e, rowIndex)
                  }
                  onDrop={(e) => isReorderingMode && handleDrop(e, rowIndex)}
                >
                  {numbered && (
                    <TableCell
                      sx={{
                        textAlign: "center",
                        ...tableCellStyle,
                        height: rowHeight,
                        padding: "8px",
                      }}
                    >
                      {(page - 1) * rowsPerPage + rowIndex + 1}.
                    </TableCell>
                  )}
                  {enableRowReordering && isReorderingMode && (
                    <TableCell
                      sx={{
                        width: 50,
                        textAlign: "center",
                        ...tableCellStyle,
                        height: rowHeight,
                        padding: "8px",
                        cursor: "grab",
                      }}
                    >
                      <MoreVertIcon
                        sx={{ color: "#666", verticalAlign: "middle" }}
                      />
                    </TableCell>
                  )}
                  {columns.map((column, colIndex) => (
                    <TableCell
                      key={colIndex}
                      className={column.cellClassName}
                      sx={{
                        width: column.width ? `${column.width}px` : "auto",
                        flex: column.flex || 1,
                        ...tableCellStyle,
                        textAlign: column.align || align || "left",
                        height: rowHeight,
                        padding: "8px",
                      }}
                    >
                      <Box
                        sx={{
                          maxHeight: rowHeight,
                          overflowY: "auto",
                          display: "block",
                          WebkitOverflowScrolling: "touch",
                          scrollbarWidth: "none",
                          "&::-webkit-scrollbar": {
                            display: "none",
                          },
                        }}
                      >
                        {column.renderCell
                          ? column.renderCell({ row, rowIndex })
                          : (row[column.field] as React.ReactNode)}
                      </Box>
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={
                    columns.length +
                    (numbered ? 1 : 0) +
                    (isReorderingMode ? 1 : 0)
                  }
                  sx={{
                    textAlign: "center",
                    ...tableCellStyle,
                  }}
                >
                  <Typography>
                    {customNoDataText ? customNoDataText : t("noData")}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {pagination && localRows?.length > 0 && !noPagination && (
        <>
          {paginationType === "standard" && (
            <Box
              sx={{ display: "flex", justifyContent: justifyContent, mt: 2 }}
            >
              <TablePagination
                component="div"
                count={actualTotalCount}
                page={page - 1}
                rowsPerPage={rowsPerPage}
                onPageChange={(_, newPage) => onPageChange?.(newPage + 1)}
                onRowsPerPageChange={(e) =>
                  onPageSizeChange?.(parseInt(e.target.value, 10))
                }
                rowsPerPageOptions={[5, 10, 25, 50]}
                labelRowsPerPage={labelRowsPerPage}
              />
            </Box>
          )}

          {paginationType === "custom" && (
            <CustomPagination
              count={totalCount}
              page={page}
              onPageChange={(newPage) => onPageChange?.(newPage)}
              rowsPerPage={rowsPerPage}
              position={paginationPosition}
            />
          )}
        </>
      )}
    </>
  );
};

DataTable.displayName = "DataTable";

export default DataTable;
