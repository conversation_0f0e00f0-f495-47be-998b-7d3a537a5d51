import React, { useState } from "react";
import SectionHeader from "../../../../components/header/section/SectionHeader";
import { Stack } from "@mui/material";
import { useTranslation } from "react-i18next";
import { CreateMaklumatPerlembagaanSection } from "./CreateMaklumatPerlembagaanSection";
import { CreatePindaanPerlembagaanSection } from "./CreatePindaanPerlembagaanSection";

export interface Organization {
  id: string | number;
  name: string;
  code: string;
}

export const CreateMaklumatPerlembagaan: React.FC = () => {
  const { t } = useTranslation();
  const [organization, setOrganization] = useState<Organization>({
    id: 1,
    name: "Pendidikan Anak Selangor",
    code: "PPM-013-10-21012015",
  });

  return (
    <Stack sx={{ px: 6, py: 3 }} gap={2}>
      <SectionHeader
        title={`${t("organization")} ${organization.name} / ${
          organization.code
        }`}
        sx={{
          backgroundColor: "#E8E9E8",
          boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
        }}
      />

      <CreateMaklumatPerlembagaanSection />
      <CreatePindaanPerlembagaanSection />
    </Stack>
  );
};

export default CreateMaklumatPerlembagaan;
