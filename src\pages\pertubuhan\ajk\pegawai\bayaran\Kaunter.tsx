import { useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
// import { OrganizationStepper } from "../organization-stepper";
import { useTheme, useMediaQuery, Fade, Grid } from "@mui/material";
import Input from "../../../../../components/input/Input";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { API_URL } from "../../../../../api";
// import InfoQACard from "../InfoQACard";
import { useSelector } from "react-redux";
import { parseDateToISO8601, PaymentPrefixes } from "@/helpers";
import { selectCalculatedPayment } from "@/redux/paymentReducer";
import { useCustom } from "@refinedev/core";
import { LoadingOverlay } from "@/components/loading";
import { getPegawaiState } from "@/redux/pegawaiReducer";

export const Kaunter = () => {
  const { t } = useTranslation();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogAlertSuccessSaveOpen, setDialogAlertSuccessSaveOpen] =
    useState(false);
  //const [societyData, setSocietyData] = useState<any>(null);

  const navigate = useNavigate();

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const { id: societyId } = useParams();
  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${societyId}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const location = useLocation();

  const pegawaiState = useSelector(getPegawaiState);

  const paymentReferenceNo = pegawaiState?.paymentReferenceNo;

  const publicOfficerId = pegawaiState.publicOfficerId;
  const createdDate = pegawaiState.createdDate;

  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const downloadFile = (filePath: string) => {
    window.open(filePath, "_blank");
  };
  const handleCetak = async () => {
    try {
      if (encodedId) {
        const response = await fetch(`${API_URL}/society/pdf/paymentReceipt`, {
          method: "post",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            paymentType: publicOfficerId
              ? `Pendaftaran Pegawai Awam (Pembayaran KAUNTER)`
              : `Pendaftaran Pegawai Harta (Pembayaran KAUNTER);`,
            paymentMethod: "C", // O = online, C = counter
            societyId: societyId,
            registerDateTime: parseDateToISO8601(createdDate),
            amount: paymentRedux?.totalAmount,
            paymentReferenceNo: publicOfficerId
              ? PaymentPrefixes.PEGAWAI_AWAM + paymentReferenceNo
              : PaymentPrefixes.PEGAWAI_HARTA + paymentReferenceNo,
          }),
        });

        const data = await response.json();
        if (data.status === "SUCCESS") {
          const url = data?.data?.url;
          downloadFile(url);
        }
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  // Get payment data from Redux
  const paymentRedux = useSelector(selectCalculatedPayment);

  return (
    <Box sx={{ display: "flex" }}>
      <LoadingOverlay isLoading={isSocietyLoading} />
      <Box sx={{ width: "100%" }}>
        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3,
            }}
          >
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: 600,
                    paddingLeft: 2,
                  }}
                >
                  {t("pengesahan")} {t("payment")}
                </Typography>
              </Box>

              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: 400,
                  paddingLeft: 2,
                }}
              >
                {publicOfficerId
                  ? t("infoPaymentPegawaiAwam")
                  : t("infoPaymentPegawaiHarta")}
              </Typography>

              <Grid container spacing={2} pl={4} pt={2} mb={3} mt={1}>
                <Input
                  disabled
                  label={t("organizationName")}
                  value={societyData?.data?.data?.societyName || ""}
                />
                <Input
                  disabled
                  label={t("referenceNumber")}
                  value={
                    publicOfficerId
                      ? PaymentPrefixes.PEGAWAI_AWAM + paymentReferenceNo
                      : encodedId
                      ? PaymentPrefixes.PEGAWAI_HARTA + paymentReferenceNo
                      : ""
                  }
                />
                <Input
                  disabled
                  label={t("paymentMethod")}
                  value="Pembayaran Kaunter"
                />
                <Input
                  disabled
                  label={t("paymentAmount")}
                  value={`RM ${paymentRedux?.totalAmount?.toFixed(2)}`}
                />
              </Grid>

              <Typography
                variant="body2"
                sx={{
                  color: "#402DFF",
                  fontSize: 14,
                  paddingLeft: 2,
                }}
              >
                {t("noteKaunter")}
              </Typography>
            </Box>

            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
                marginTop: 3,
              }}
            >
              <ButtonOutline
                onClick={() =>
                  navigate(
                    `/pertubuhan/society/${societyId}/senarai/ajk/pegawai/`
                  )
                }
              >
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                onClick={handleCetak}
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  borderRadius: 1,
                  textTransform: "none",
                }}
              >
                {t("cetak")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>

        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
              backgroundColor: "#fff",
              color: "#000",
              minWidth: fullScreen ? "100%" : "1000px",
              maxWidth: "100%",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogTitle sx={{ pb: 2.5 }}>
            <Box
              sx={{
                backgroundColor: "#e0f2f1",
                px: 2.5,
                py: 0.5,
                borderRadius: 2.5,
              }}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{ fontWeight: "bold", fontSize: 16, color: "black" }}
              >
                {t("submitApplication")}
              </Typography>
              <IconButton onClick={handleCloseDialog} size="small">
                <CloseIcon sx={{ color: "black" }} />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ py: 4 }}>
            <DialogContentText
              id="alert-dialog-description"
              sx={{ color: "black", fontSize: 14, ml: 2 }}
            >
              {t("confirmSubmitApplication")}
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ py: 2, px: 3 }}>
            <ButtonOutline onClick={handleCloseDialog} sx={{ fontSize: 14 }}>
              {t("no")}
            </ButtonOutline>
            <ButtonPrimary color="primary" autoFocus sx={{ fontSize: 14 }}>
              {t("yes")}
            </ButtonPrimary>
          </DialogActions>
        </Dialog>

        <Dialog
          open={dialogAlertSuccessSaveOpen}
          onClose={() => setDialogAlertSuccessSaveOpen(false)}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogContent sx={{ p: 4 }}>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <img src="/success.gif" alt="success" width={200} height={200} />
            </Box>
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontSize: 28, textAlign: "center" }}
            >
              Permohonan berjaya dihantar.
            </Typography>

            <Box
              sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
            >
              <ButtonPrimary
                sx={{ backgroundColor: "#51CA47", borderRadius: "18px" }}
                onClick={() => setDialogAlertSuccessSaveOpen(false)}
              >
                {t("Continue")}
              </ButtonPrimary>
            </Box>
          </DialogContent>
        </Dialog>
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        {/* <OrganizationStepper activeStep={activeStep} /> */}

        {/* <InfoQACard /> */}
      </Box>
    </Box>
  );
};

export default Kaunter;
