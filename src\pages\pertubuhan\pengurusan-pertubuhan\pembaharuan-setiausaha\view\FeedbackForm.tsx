import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useGetIdentity, useCustom } from "@refinedev/core";
import { useForm, useFormContext } from "react-hook-form";
import { FieldValues } from "react-hook-form";
import { useSecretaryReformContext } from "../Provider";
import {
  filterEmptyValuesOnObject,
  useMutation,
  OrganisationPositionLabel,
  getCitizenshipLabel,
} from "@/helpers";
import { API_URL } from "@/api";

import { Box, Checkbox, Grid, Typography } from "@mui/material";
import {
  CustomSkeleton,
  TextFieldController,
  ToggleButtonController,
  DisabledTextField,
  FormFieldRow,
  Label,
  ButtonPrimary,
  DialogConfirmation,
} from "@/components";

import { IUser, ICommittee } from "@/types";

const feedbackOptions = (language: string) => [
  {
    value: "Setuju",
    label: language === "my" ? "Setuju" : "Agree",
  },
  {
    value: "Tidak Setuju",
    label: language === "my" ? "Tidak Setuju" : "Disagree",
  },
];

const otherOptions = (language: string) => [
  {
    value: 0,
    label:
      language === "my"
        ? "Tiada bantahan. Menyokong pembaharuan setiausaha ini."
        : "No objection. Support this secretary reform.",
  },
  {
    value: 1,
    label: language === "my" ? "Lain-lain" : "Others",
  },
];

const FeedbackForm: React.FC = () => {
  const { secretaryId } = useParams();
  const { t, i18n } = useTranslation();
  const { getValues: getSecretaryFormValues } = useFormContext();
  const { data: user } = useGetIdentity<IUser>();

  const currentLanguage = i18n.language;

  const { handleSecretaryReformStatus, isSecretaryReformSuccess, societyData } =
    useSecretaryReformContext();

  const secretaryFormValues = getSecretaryFormValues();

  const { fetch: createFeedback, isLoading: isCreatingFeedback } = useMutation({
    url: "society/secretary/principal/feedback/create",
    method: "post",
    onSuccess: () => handleSecretaryReformStatus(true),
  });

  const [isChecked, setIsChecked] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [checkboxError, setCheckboxError] = useState(false);
  const [currentUserCommittee, setCurrentUserCommittee] =
    useState<ICommittee | null>(null);

  const { control, watch, getValues } = useForm<FieldValues>({
    defaultValues: {
      secretaryId: Number(secretaryId),
      identificationNo: user?.identificationNo,
      societyId: secretaryFormValues?.societyId,
      societyNo: secretaryFormValues?.societyNo,
      feedback: "Setuju",
      other: 0,
      otherReason: "",
    },
  });

  const other = watch("other");
  const [other0ValueDisabled, setOther0ValueDisabled] = useState(
    () => other !== 0
  );

  // Fetch society committee data to get user's designation code
  const { data: committeeData, isLoading: isLoadingCommittee } = useCustom({
    url: `${API_URL}/society/committee/getAll`,
    method: "get",
    config: {
      filters: [
        {
          field: "pageNo",
          operator: "eq",
          value: 1,
        },
        {
          field: "pageSize",
          operator: "eq",
          value: 1000,
        },
        {
          field: "societyId",
          operator: "eq",
          value: secretaryFormValues?.societyId,
        },
      ],
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !!secretaryFormValues?.societyId && !!user?.identificationNo,
    },
  });

  // Find current user in committee data and map designation code to position
  useEffect(() => {
    if (committeeData?.data?.data?.data && user?.identificationNo) {
      const currentUserCommittee = committeeData.data.data.data.find(
        (member: any) => member.identificationNo === user.identificationNo
      );

      if (currentUserCommittee) setCurrentUserCommittee(currentUserCommittee);
    }
  }, [committeeData, user?.identificationNo]);

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsChecked(event.target.checked);
    if (event.target.checked) setCheckboxError(false);
  };

  const handleDialogClose = () => setIsDialogOpen(false);

  const handleSubmit = () => {
    if (!isChecked) {
      setCheckboxError(true);
      return;
    }

    setCheckboxError(false);
    setIsDialogOpen(true);
  };

  const handleFormSubmit = () => {
    const data = getValues();

    if (data.other === 0) data.otherReason = "";

    const payload = filterEmptyValuesOnObject(data);

    createFeedback(payload);
  };
  const handleFeedbackFieldOnChange = (value: string | null) => {
    setOther0ValueDisabled(value === "Tidak Setuju");
  };

  return (
    <>
      <Box
        sx={{
          backgroundColor: "white",
          padding: "22px 16px",
          borderRadius: "15px",
          marginBottom: 1,
          boxShadow: "0px 12px 12px 0px #EAE8E866",
        }}
      >
        {isLoadingCommittee ? (
          <CustomSkeleton number={1} />
        ) : (
          <Box
            sx={{
              width: "100%",
              borderRadius: "10px",
              border: "0.5px solid #DADADA",
              padding: "22px",
              marginBottom: 1,
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("maklumbalasJawatankuasa")}
            </Typography>

            <FormFieldRow
              label={<Label text={t("name")} />}
              value={
                <DisabledTextField value={currentUserCommittee?.name ?? "-"} />
              }
            />

            <FormFieldRow
              label={<Label text={t("idNumber")} />}
              value={
                <DisabledTextField value={user?.identificationNo ?? "-"} />
              }
            />

            <FormFieldRow
              label={<Label text={t("position")} />}
              value={
                <DisabledTextField
                  value={
                    currentUserCommittee?.designationCode
                      ? OrganisationPositionLabel[
                          Number(currentUserCommittee.designationCode)
                        ]
                      : "-"
                  }
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("namaPertubuhan")} />}
              value={
                <DisabledTextField value={societyData?.societyName ?? "-"} />
              }
            />

            <FormFieldRow
              label={<Label text={t("citizenship")} />}
              value={
                <DisabledTextField
                  value={t(
                    getCitizenshipLabel(
                      Number(currentUserCommittee?.nationalityStatus)
                    )
                  )}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("organizationNumber2")} />}
              value={
                <DisabledTextField value={societyData?.societyNo ?? "-"} />
              }
            />
          </Box>
        )}

        <Box
          sx={{
            width: "100%",
            borderRadius: "10px",
            border: "0.5px solid #DADADA",
            padding: "22px",
            marginBottom: 1,
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("keputusanJawatankuasa")}
          </Typography>

          <Grid container spacing={2} marginBottom={1}>
            <Grid item xs={8}>
              <ToggleButtonController
                name="feedback"
                control={control}
                options={feedbackOptions(currentLanguage)}
                sx={{
                  gap: 4,
                }}
                disabled={isSecretaryReformSuccess}
                required
                onChange={handleFeedbackFieldOnChange}
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            width: "100%",
            borderRadius: "10px",
            border: "0.5px solid #DADADA",
            padding: "22px",
            marginBottom: 1,
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("committeeReview")}
          </Typography>

          <Grid container spacing={2} marginBottom={1}>
            <Grid item xs={8}>
              <ToggleButtonController
                name="other"
                control={control}
                options={otherOptions(currentLanguage)}
                sx={{
                  gap: 4,
                }}
                disabled={isSecretaryReformSuccess}
                checkDisabledByValue={(val) => other0ValueDisabled && val === 0}
                required
              />
              {other === 1 && (
                <TextFieldController
                  name="otherReason"
                  multiline
                  inputProps={{ maxLength: 100 }}
                  placeholder={
                    currentLanguage === "my"
                      ? "Terhad kepada 100 patah perkataan"
                      : "Limit to 100 words"
                  }
                  control={control}
                  required={other === 1}
                  sx={{
                    maxWidth: "478px",
                    marginTop: 2,
                    marginLeft: "40px",
                  }}
                />
              )}
            </Grid>
          </Grid>
        </Box>

        {!isSecretaryReformSuccess && (
          <Box
            sx={{
              width: "100%",
              borderRadius: "10px",
              border: "0.5px solid #DADADA",
              padding: "22px",
              marginBottom: "32px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {currentLanguage === "my"
                ? "Pengakuan Pembaharuan Setiausaha"
                : "Secretary's Renewal Acknowledgment"}
            </Typography>

            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: "20px",
              }}
            >
              <Checkbox
                checked={isChecked}
                onChange={handleCheckboxChange}
                icon={
                  <Box
                    sx={{
                      width: "12px",
                      height: "12px",
                      border: "0.5px solid #848484",
                      background: "none",
                    }}
                  />
                }
                checkedIcon={
                  <Box
                    sx={{
                      width: "12px",
                      height: "12px",
                      border: "0.5px solid var(--primary-color)",
                      background: "var(--primary-color)",
                    }}
                  />
                }
                sx={{
                  padding: 0,
                }}
              />

              <Typography
                fontSize="12px"
                color="#666666"
                fontWeight="400 !important"
              >
                {currentLanguage === "my"
                  ? "Kenyataan pemohonan pembaharuan setiausaha ini adalah benar"
                  : "This secretary renewal application statement is true."}
              </Typography>
            </Box>

            {checkboxError && (
              <Typography
                fontSize="12px"
                color="error"
                marginTop="8px"
                marginLeft="28px"
              >
                {currentLanguage === "my"
                  ? "Sila tandakan kenyataan ini sebelum menghantar"
                  : "Please check this declaration before submitting."}
              </Typography>
            )}
          </Box>
        )}

        {!isSecretaryReformSuccess && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonPrimary
              onClick={handleSubmit}
              sx={{
                backgroundColor: "var(--primary-color)",
                color: "white",
                textTransform: "none",
                fontWeight: "bold",
                minWidth: "120px",
              }}
            >
              {t("hantar")}
            </ButtonPrimary>
          </Box>
        )}
      </Box>

      <DialogConfirmation
        open={isDialogOpen}
        isSuccess={isSecretaryReformSuccess}
        isMutating={isCreatingFeedback}
        onClose={handleDialogClose}
        onAction={handleFormSubmit}
      />
    </>
  );
};

export default FeedbackForm;
