import { Box } from "@mui/material";
import WrapContent from "../../View/WrapContent";
import { useTabContext } from "../../../../contexts/tabProvider";
import PertubuhanTab from "./PertubuhanTab";
import PemegangJawatanTab from "./pemegangJawatanTab";
import AhliBukanWarganegaraTab from "./AhliBukanWarganegaraTab";
import PertukaranSetiausahaTab from "./PertukaranSetiausahaTab";
import PindaanPerlembagaanTab from "./PindaanPerlembagaanTab";
import PembubaranTab from "./PembubaranTab";
import RayuanTab from "./RayuanTab";
import PenyataTahunanTab from "./PenyataTahunanTab";
import { NEW_PermissionNames, pageAccessEnum } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";
import { MaklumatBoxes } from "../maklumatSelectionTabs";

function MaklumatInduk() {
  const { activeTab, setActiveTab } = useTabContext();

  const tab = [
    {
      name: "Pertubuhan",
      slug: "pertubuhan",
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
          .INDUK.children.PERTUBUHAN_INDUK.label,
    },
    {
      name: "Pemegang jawatan",
      slug: "pemegang-jawatan",
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
          .INDUK.children.PEMEGANG_JAWATAN_INDUK.label,
    },
    {
      name: "Ahli bukan warganegara",
      slug: "ahli-bukan-warganegara",
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
          .INDUK.children.AHLI_BUKAN_WARGANEGARA_INDUK.label,
    },
    {
      name: "Pertukaran setiausaha",
      slug: "pertukaran-setiausaha",
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
          .INDUK.children.PERTUKARAN_SETIAUSAHA.label,
    },
    {
      name: "Pindaan perlembagaan ",
      slug: "pindaan-perlembagaan",
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
          .INDUK.children.PINDAAN_PERLEMBAGAAN_INDUK.label,
    },
    {
      name: "Pembubaran",
      slug: "pembubaran",
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
          .INDUK.children.PEMBUBARAN_INDUK.label,
    },
    {
      name: "Rayuan",
      slug: "rayuan",
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
          .INDUK.children.RAYUAN_INDUK.label,
    },
    {
      name: "Penyata tahunan",
      slug: "penyata-tahunan",
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
          .INDUK.children.PENYATA_TAHUNAN_INDUK.label,
    },
  ];

  const enrichedTabs = tab.map((item) => ({
    ...item,
    hasPermission: AuthHelper.hasPageAccess(
      item.permissionNames,
      pageAccessEnum.Read
    ),
  }));

  const renderTab = (tabs: any) => {
    switch (activeTab) {
      case "pertubuhan":
        return <PertubuhanTab disabled={!tabs[0].hasPermission} />;
      case "pemegang-jawatan":
        return <PemegangJawatanTab disabled={!tabs[1].hasPermission} />;
      case "ahli-bukan-warganegara":
        return <AhliBukanWarganegaraTab disabled={!tabs[2].hasPermission} />;
      case "pertukaran-setiausaha":
        return <PertukaranSetiausahaTab disabled={!tabs[3].hasPermission} />;
      case "pindaan-perlembagaan":
        return <PindaanPerlembagaanTab disabled={!tabs[4].hasPermission} />;
      case "pembubaran":
        return <PembubaranTab disabled={!tabs[5].hasPermission} />;
      case "rayuan":
        return <RayuanTab disabled={!tabs[6].hasPermission} />;
      case "penyata-tahunan":
        return <PenyataTahunanTab disabled={!tabs[7].hasPermission} />;
      default:
        return null;
    }
  };
  return (
    <>
      <Box
        sx={{
          padding: "0px",
        }}
      >
        <WrapContent title="Maklumat Induk">
          <Box
            sx={{
              // paddingTop: "0px",
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(180px, 1fr))",
              gap: "16px",
            }}
          >
            {enrichedTabs.map((data, index) => {
              return (
                <MaklumatBoxes
                  key={index}
                  data={data}
                  isActive={data.slug === activeTab}
                  disabled={!data.hasPermission}
                  onClick={() => {
                    if (data.hasPermission) {
                      setActiveTab(data.slug);
                    }
                  }}
                />
              );
            })}
          </Box>
        </WrapContent>
      </Box>
      {renderTab(enrichedTabs)}
    </>
  );
}

export default MaklumatInduk;
