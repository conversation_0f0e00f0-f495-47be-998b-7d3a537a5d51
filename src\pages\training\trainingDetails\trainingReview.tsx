import React, {useState} from "react";
import {Typography} from "@mui/material";
import Box from "@mui/material/Box";
import TrainingSidebar from "@/pages/training/trainingDetails/trainingSidebar";
import TrainingBreadcrumb from "@/pages/training/breadcrumb";
import TrainingInfoFragment from "@/pages/training/trainingDetails/trainingInfoFragment";
import {useCustom} from "@refinedev/core";
import {API_URL} from "@/api";
import {useLocation} from "react-router-dom";


const TrainingReview: React.FC = () => {

  const location = useLocation();

  const [step, setStep] = useState(0);

  const enrollId = location.state?.enrollId;

  const itemArray = [
    {
      page:0,
      title: "Pengenalan kepada Tatacara asas pengendalian pertubuhan (JPPM )",
      image: "/latihanSample/images5.jpg",
    },
    {
      page:1,
      title: "Pengenalan kepada Tatacara asas pengendalian pertubuhan (JPPM )",
      image: "/latihanSample/images6.jpg",
    },
    {
      page:2,
      title: "Pengenalan kepada Tatacara asas pengendalian pertubuhan (JPPM )",
      image: "/latihanSample/images7.jpg",
    },
    {
      page:3,
      title: "Pengenalan kepada Tatacara asas pengendalian pertubuhan (JPPM )",
      image: "/latihanSample/images8.jpg",
    },
    {
      page:4,
      title: "Pengenalan kepada Tatacara asas pengendalian pertubuhan (JPPM )",
      image: "/latihanSample/images5.jpg",
    },
  ];

  const handleNext = (currentPage: number) => {
    if(currentPage < 5) setStep(currentPage);
  }

  const {data: enrolledTrainingData, isLoading: isEnrolledTrainingLoading} = useCustom({
    url: `${API_URL}/society/training/enrollments/${enrollId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const enrolledTraining = enrolledTrainingData?.data?.data || {};
  console.log("enrolledTraining", enrolledTraining)

  const {data: enrolledTrainingData2, isLoading: isEnrolledTrainingLoading2} = useCustom({
    url: `${API_URL}/society/training/courses/${enrolledTraining.trainingCourseId}/materials`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: enrolledTraining.trainingCourseId!=null,
      retry: false,
      cacheTime: 0,
    },
  });

  const enrolledTraining2 = enrolledTrainingData2?.data?.data || [];
  console.log("enrolledTraining2", enrolledTraining2)

  return (
    <>
      <TrainingBreadcrumb isAdmin={false}/>
      <Box sx={{
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-evenly",
        gap:1
      }}>
        <TrainingSidebar course={enrolledTraining} item={enrolledTraining2}  currentPage={step}/>
        <TrainingInfoFragment course={enrolledTraining} item={enrolledTraining2} handleNext={handleNext} isReview={true} />
      </Box>

    </>
  );
}

export default TrainingReview;
