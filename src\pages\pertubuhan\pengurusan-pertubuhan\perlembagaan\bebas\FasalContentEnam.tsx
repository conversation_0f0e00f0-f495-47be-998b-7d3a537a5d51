import {
  Box,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
  FormControl,
  Select,
  MenuItem,
  FormHelperText,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { handleSaveContent } from "../helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "@/helpers/utils";
import { FasalBebasProps } from "../Fasal";
import EditableFasalTextArea from "@/components/FasalBebasComponent/EditableFasalTextArea";
import FasalNameCom from "@/components/FasalBebasComponent/FasalNameCom";
import ReminderEditable from "@/components/FasalBebasComponent/ReminderEditable";
import ContentBox from "@/components/FasalBebasComponent/ContentBox";
import CheckContent from "@/components/FasalBebasComponent/CheckContent";
import { RegExNumbers } from "@/helpers";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

export const FasalContentEnamBebas: React.FC<FasalBebasProps> = ({
  activeStep,
  setActiveStep,
  clause,
  asalData,
  name,
}) => {
  const requiredText = ["<<bilangan juruaudit dalam>>"];
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);

  const [tempohJawatan, setTempohJawatan] = useState(t("setahun"));
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [jenisJuruAudit, setJenisJuruAudit] = useState(t("internal"));
  const [bilanganEksternal, setBilanganEksternal] = useState("");
  const [bilanganInternal, setBilanganInternal] = useState("");
  const [societyId, setSocietyId] = useState<any>(null);
  const [clauseContentEditable, setClauseContentEditable] = useState(
    clause.clauseContent
  );
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");

  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { id, clauseId } = useParams();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!jenisJuruAudit) {
      errors.jenisJuruAudit = t("fieldRequired");
    }

    if (!bilanganInternal && jenisJuruAudit === t("internal")) {
      errors.bilanganInternal = t("fieldRequired");
    }

    return errors;
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };
  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  const handleChange = (e: any) => {
    setClauseContentEditable(e.target.value);
  };

  useEffect(() => {
    if (clause) {
      //const clause = JSON.parse(clause10);
      setDataId(clause.id);
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause.societyName);
      setJenisJuruAudit(clause.constitutionValues[0]?.definitionName);
      setBilanganInternal(clause.constitutionValues[1]?.definitionName);
      setBilanganEksternal(clause.constitutionValues[2]?.definitionName);
      setPemilihanAjk(clause.constitutionValues[3]?.definitionName);
      setTempohJawatan(clause.constitutionValues[4]?.definitionName);
      setClauseContentEditable(
        clause.clauseModifyContent
          ? clause.clauseModifyContent
          : clause.clauseContent
      );
      setIsEdit(clause.edit);
    }
  }, [clause]);

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);

  const internalAuditor =
    "1. <<bilangan juruaudit dalam>> orang yang bukannya Ahli Jawatankuasa Pertubuhan hendaklah dilantik dalam Mesyuarat Agung <<jenis mesyuarat agung>> sebagai Juruaudit dalam. Mereka yang memegang jawatan selama <<tempoh pelantikan jawatankuasa>> boleh dilantik semula.";
  const externalAuditor =
    "1. Juruaudit luar bertauliah boleh dilantik dalam Mesyuarat Agung/Mesyuarat Jawatankuasa bagi tempoh <<tempoh pelantikan jawatankuasa>> tahun.";

  let clauseContent = clauseContentEditable;

  clauseContent = clauseContent.replaceAll(
    /Display for both jenis juruaudit \(continue from point 1 above\);/gi,
    ""
  );
  clauseContent = clauseContent.replaceAll(
    /If Jenis juruaudit = dalam, display/gi,
    ""
  );
  clauseContent = clauseContent.replaceAll(
    /If Jenis juruaudit = luar, display/gi,
    ""
  );
  clauseContent = clauseContent.replaceAll(
    new RegExp(internalAuditor, "gi"),
    ""
  );
  clauseContent = clauseContent.replaceAll(
    new RegExp(externalAuditor, "gi"),
    "<<jenis juruaudit>>"
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis juruaudit>>/gi,
    jenisJuruAudit === t("internal") ? internalAuditor : externalAuditor
  );
  //clauseContent = clauseContent.replaceAll(/<<jumlah wang tangan yang dibenarkan dalam tangan>>/gi, `<b>${jenisJuruAudit || '<<jumlah wang tangan yang dibenarkan dalam tangan>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<bilangan juruaudit dalam>>/gi,
    `<b>${bilanganInternal || "<<bilangan juruaudit dalam>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<perbelanjaan yg dibenarkan>>/gi, `<b>${bilanganEksternal || '<<perbelanjaan yg dibenarkan>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${pemilihanAjk || "<<jenis mesyuarat agung>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${tempohPelucutanWaktu || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<tempoh pelantikan jawatankuasa>>/gi,
    `<b>${tempohJawatan || "<<tempoh pelantikan jawatankuasa>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  const handlerChangeClauseContentEditable = (val: string) => {
    setClauseContentEditable(val);
  };

  return (
    <>
      <Grid container>
        {/* name section */}
        <FasalNameCom clauseId={id} name={name} />
        <Grid item xs={12}>
          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Typography sx={labelStyle}>
                    {t("auditorType")}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} md={8}>
                  <FormControl
                    fullWidth
                    required
                    error={!!formErrors.jenisJuruAudit}
                  >
                    <Select
                      disabled={isViewMode}
                      size="small"
                      displayEmpty
                      value={jenisJuruAudit}
                      onChange={(e) => {
                        setJenisJuruAudit(e.target.value);
                        setFormErrors((prevErrors) => ({
                          ...prevErrors,
                          jenisJuruAudit: "",
                        }));
                      }}
                    >
                      <MenuItem value={t("internal")}>{t("internal")}</MenuItem>
                      <MenuItem value={t("external")}>{t("external")}</MenuItem>
                    </Select>
                  </FormControl>
                  {formErrors.jenisJuruAudit && (
                    <FormHelperText sx={{ color: "red" }}>
                      {formErrors.jenisJuruAudit}
                    </FormHelperText>
                  )}
                </Grid>

                {jenisJuruAudit == t("internal") && (
                  <>
                    <Grid item xs={12} md={4}>
                      <Typography sx={labelStyle}>
                        {t("internalAuditorNumber")}{" "}
                        <Typography sx={{ display: "inline", color: "red" }}>
                          *
                        </Typography>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={8}>
                      <TextField
                        size="small"
                        disabled={isViewMode}
                        fullWidth
                        required
                        value={bilanganInternal}
                        onChange={(e) => {
                          if (RegExNumbers.test(e.target.value)) {
                            setBilanganInternal(e.target.value);
                            setFormErrors((prevErrors) => ({
                              ...prevErrors,
                              bilanganInternal: "",
                            }));
                          } else {
                            setBilanganInternal("");
                            setFormErrors((prevErrors) => ({
                              ...prevErrors,
                              bilanganInternal: "Invalid Value",
                            }));
                          }
                        }}
                        error={!!formErrors.bilanganInternal}
                        helperText={formErrors.bilanganInternal}
                        type="number"
                      />
                    </Grid>
                  </>
                )}

                {jenisJuruAudit == t("external") && (
                  <>
                    <Grid item xs={12} md={4}>
                      <Typography sx={labelStyle}>
                        {t("externalAuditorNumber")}{" "}
                        <Typography sx={{ display: "inline", color: "red" }}>
                          *
                        </Typography>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={8}>
                      <TextField
                        disabled={isViewMode}
                        size="small"
                        fullWidth
                        required
                        value={bilanganEksternal}
                        onChange={(e) => {
                          if (RegExNumbers.test(e.target.value)) {
                            setBilanganEksternal(e.target.value);
                            setFormErrors((prevErrors) => ({
                              ...prevErrors,
                              bilanganEksternal: "",
                            }));
                          } else {
                            setBilanganEksternal("");
                            setFormErrors((prevErrors) => ({
                              ...prevErrors,
                              bilanganEksternal: "Invalid Value",
                            }));
                          }
                        }}
                        error={!!formErrors.bilanganEksternal}
                        helperText={formErrors.bilanganEksternal}
                        type="number"
                      />
                    </Grid>
                  </>
                )}
              </Grid>
            </Box>
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <ReminderEditable />
            <EditableFasalTextArea
              requiredFieldText={requiredText}
              clauseContentEditable={clauseContentEditable}
              setClauseContentEditable={handlerChangeClauseContentEditable}
            />
          </Box>

          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
            }}
          >
            <ContentBox clauseContent={clauseContent} />
            <CheckContent checked={checked} onChange={handleChangeCheckbox} />
            {/* submit */}
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                variant="contained"
                sx={{
                  width: isMobile ? "100%" : "auto",
                  display: isViewMode ? "none" : "block",
                }}
                onClick={() => {
                  const errors = validateForm();
                  if (Object.keys(errors).length > 0) {
                    setFormErrors(errors);
                    return;
                  }
                 
                  handleSaveContent({
                    i18n,
                    societyId: societyDataRedux.id,
                    societyName: societyDataRedux.societyName,
                    clauseContentId,
                    dataId,
                    isEdit,
                    clauseNo: clauseNo,
                    clauseName: clauseName,
                    createClauseContent,
                    editClauseContent,
                    description: clauseContent,
                    modifiedTemplate: clauseContentEditable,
                    constitutionValues: [
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jenisJuruAudit,
                        titleName: "Jenis Juruaudit",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: bilanganInternal,
                        titleName: "Bilangan Juruaudit Dalam",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: bilanganEksternal,
                        titleName: "Bilangan Juruaudit Luar",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: pemilihanAjk,
                        titleName: "Jenis Mesyuarat Agung",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: tempohJawatan,
                        titleName: "Tempoh Pelantikan Jawatankuasa",
                      },
                    ],
                    clause: "clause",
                    clauseCount: 1,
                  });
                }}
                disabled={isCreatingContent || isEditingContent || !checked}
              >
                {isCreatingContent || isEditingContent
                  ? t("saving")
                  : t("save")}
              </ButtonPrimary>
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </>
  );
};

export default FasalContentEnamBebas;
