import { useMemo } from "react";
import { useParams } from "react-router-dom";
import { FieldValues, useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";
import {
  useMutation,
  useQuery,
  omitKeysFromObject,
  formatDate,
  getAddressList,
  getMalaysiaAddressList,
  NEW_PermissionNames,
  pageAccessEnum,
} from "@/helpers";

import {
  Box,
  Theme,
  Typography,
  useMediaQuery,
  CircularProgress,
} from "@mui/material";
import {
  ButtonPrimary,
  FormFieldRow,
  TextFieldController,
  Label,
  CustomSkeleton,
  SelectFieldController,
} from "@/components";
import ButtonPrevious from "@/components/button/ButtonPrevious";
import Input from "@/components/input/Input";

import { IApiResponse, ILookupDetail } from "@/types";
import AuthHelper from "@/helpers/authHelper";

const Kemaskini = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const hasUpdatePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.SELENGGARA.children.PENYELENGGARAAN_UMUM.children
      .JABATAN_INSOLVENSI.label,
    pageAccessEnum.Update
  );
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const addressList = getAddressList() ?? [];
  const malaysiaAddressList = getMalaysiaAddressList() ?? [];

  const { control, setValue, watch, handleSubmit } = useForm<FieldValues>({
    defaultValues: {
      name: "",
      code: "",
      address: "",
      cityCode: "",
      districtCode: "",
      stateCode: "",
      postcode: "",
      phoneCode: "",
      phoneNumber: "",
      faxCode: "",
      faxNumber: "",
      emailAddress: "",
      status: 1,
      createdDate: "",
      modifiedDate: "",
    },
  });

  const {
    fetch: updateInsolvencyDepartment,
    isLoading: isUpdatingInsolvencyDepartment,
  } = useMutation<IApiResponse<ILookupDetail>>({
    url: `society/lookup/insolvencyDepartment/${id}/edit`,
    method: "put",
    onSuccess: (res) => {
      const resCode = res.data.code;

      if (resCode === 200) navigate("..");
    },
  });

  const stateOptions = useMemo(() => {
    return malaysiaAddressList.map((item: any) => ({
      label: item.name,
      value: item.id,
    }));
  }, []);

  const districtOptions = useMemo(
    () =>
      addressList
        .filter((item: any) => item.pid === Number(watch("stateCode")))
        .map((item: any) => ({
          value: item.id,
          label: item.name,
        })),
    [addressList, watch("stateCode")]
  );

  const { isLoading: isLoadingInsolvencyDepartmentDetail } = useQuery<
    IApiResponse<ILookupDetail>
  >({
    url: `society/lookup/insolvencyDepartment/${id}`,
    onSuccess: (res) => {
      const resCode = res?.data?.code ?? null;
      const detail = res?.data?.data ?? null;

      if (resCode && resCode === 200) {
        setValue("code", detail?.code);
        setValue("name", detail?.name);
        setValue("address", detail?.address);
        setValue("stateCode", Number(detail?.stateCode));
        setValue("districtCode", Number(detail?.districtCode));
        setValue("cityCode", detail?.cityCode);
        setValue("postcode", detail?.postcode);
        setValue("status", detail?.status ? 1 : 0);
        setValue("emailAddress", detail?.emailAddress);
        setValue("createdDate", formatDate(detail?.createdDate));
        setValue("modifiedDate", formatDate(detail?.modifiedDate));

        if (detail?.faxNumber && detail?.faxNumber.includes("-")) {
          const [faxCode = "", faxNumber = ""] = detail.faxNumber.split("-");
          setValue("faxCode", faxCode);
          setValue("faxNumber", faxNumber);
        } else {
          setValue("faxCode", "");
          setValue("faxNumber", detail?.faxNumber ?? "");
        }

        if (detail?.phoneNumber && detail?.phoneNumber.includes("-")) {
          const [phoneCode = "", phoneNumber = ""] =
            detail.phoneNumber.split("-");
          setValue("phoneCode", phoneCode);
          setValue("phoneNumber", phoneNumber);
        } else {
          setValue("phoneCode", "");
          setValue("phoneNumber", detail?.phoneNumber ?? "");
        }
      }
    },
  });

  const onSubmit = (data: FieldValues) => {
    const { faxCode, phoneCode, phoneNumber, faxNumber } = data;
    const keysToSkip = ["createdDate", "modifiedDate", "faxCode", "phoneCode"];
    const filteredValues = omitKeysFromObject(data, keysToSkip);

    const payload = {
      ...filteredValues,
      phoneNumber: `${phoneCode}-${phoneNumber}`,
      faxNumber: `${faxCode}-${faxNumber}`,
    };

    updateInsolvencyDepartment(payload);
  };

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography sx={{ mb: 2 }} className="title">
            {t("tambahJabatanInsolvensi")}
          </Typography>

          {isLoadingInsolvencyDepartmentDetail ? (
            <CustomSkeleton height={50} />
          ) : (
            <Box
              component="form"
              onSubmit={handleSubmit(onSubmit)}
              sx={{ display: "grid" }}
            >
              <FormFieldRow
                label={<Label text={t("kodInsolvensi")} required />}
                value={
                  <TextFieldController
                    disabled={!hasUpdatePermission}
                    control={control}
                    name="code"
                    required
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("jabatanInsolvensi")} required />}
                value={
                  <TextFieldController
                    disabled={!hasUpdatePermission}
                    control={control}
                    name="name"
                    required
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("address")} required />}
                value={
                  <TextFieldController
                    disabled={!hasUpdatePermission}
                    name="address"
                    control={control}
                    required
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("state")} required />}
                value={
                  <SelectFieldController
                    name="stateCode"
                    control={control}
                    options={stateOptions}
                    disabled={!hasUpdatePermission}
                    placeholder={t("selectPlaceholder")}
                    onChange={() => setValue("districtCode", "")}
                    requiredCondition={() => true}
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("district")} required />}
                value={
                  <SelectFieldController
                    name="districtCode"
                    control={control}
                    options={districtOptions}
                    placeholder={t("selectPlaceholder")}
                    disabled={!hasUpdatePermission || !watch("stateCode")}
                    requiredCondition={() => true}
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("city")} required />}
                value={
                  <TextFieldController
                    name="cityCode"
                    control={control}
                    required
                    disabled={!hasUpdatePermission}
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("postcode")} required />}
                value={
                  <TextFieldController
                    name="postcode"
                    control={control}
                    type="number"
                    isPostcode
                    required
                    disabled={!hasUpdatePermission}
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("phoneNumber")} required />}
                value={
                  <Box sx={{ display: "flex", gap: 2 }}>
                    <TextFieldController
                      control={control}
                      name="phoneCode"
                      type="telCode"
                      sx={{ width: "80px" }}
                      inputProps={{ maxLength: 3 }}
                      sxControl={{ width: "80px" }}
                      required
                      disabled={!hasUpdatePermission}
                    />
                    <TextFieldController
                      control={control}
                      name="phoneNumber"
                      type="tel"
                      required
                      disabled={!hasUpdatePermission}
                    />
                  </Box>
                }
              />

              <FormFieldRow
                label={<Label text={t("faxNumber")} required />}
                value={
                  <Box sx={{ display: "flex", gap: 2 }}>
                    <TextFieldController
                      control={control}
                      name="faxCode"
                      type="telCode"
                      sx={{ width: "80px" }}
                      inputProps={{ maxLength: 3 }}
                      sxControl={{ width: "80px" }}
                      required
                      disabled={!hasUpdatePermission}
                    />
                    <TextFieldController
                      control={control}
                      name="faxNumber"
                      type="tel"
                      required
                      disabled={!hasUpdatePermission}
                    />
                  </Box>
                }
              />

              <FormFieldRow
                label={<Label text={t("email")} required />}
                value={
                  <TextFieldController
                    name="emailAddress"
                    control={control}
                    type="email"
                    required
                    disabled={!hasUpdatePermission}
                  />
                }
              />

              <Input
                type="radio"
                label={t("status")}
                value={watch("status")}
                disabled={!hasUpdatePermission}
                options={[
                  { value: 1, label: t("active") },
                  { value: 0, label: t("inactive") },
                ]}
                onChange={(e) => {
                  const value = e.target.value;
                  setValue("status", Number(value));
                }}
              />

              <FormFieldRow
                label={<Label text={t("wujudDaftar")} />}
                value={
                  <TextFieldController
                    control={control}
                    name="createdDate"
                    disabled
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("kemaskiniTerakhir")} />}
                value={
                  <TextFieldController
                    control={control}
                    name="modifiedDate"
                    disabled
                  />
                }
              />

              <Box
                sx={{
                  mt: 2,
                  display: "flex",
                  flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrevious
                  variant="outlined"
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    width: isMobile ? "100%" : "auto",
                  }}
                  onClick={() => navigate(-1)}
                >
                  {t("back")}
                </ButtonPrevious>
                {hasUpdatePermission ? (
                  <ButtonPrimary
                    type="submit"
                    disabled={isUpdatingInsolvencyDepartment}
                    variant="contained"
                    sx={{
                      width: isMobile ? "100%" : "auto",
                      display: "flex",
                      alignItems: "center",
                      gap: "5px",
                    }}
                  >
                    {isUpdatingInsolvencyDepartment && (
                      <CircularProgress size={15} />
                    )}
                    {t("kemaskini")}
                  </ButtonPrimary>
                ) : null}
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default Kemaskini;
