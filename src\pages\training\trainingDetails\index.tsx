import React, {useState} from "react";
import {Box, IconButton, Typography} from "@mui/material";
import TrainingHeader from "@/pages/training/trainingHeader";
import {useLocation, useNavigate} from "react-router-dom";
import {ButtonPrimary} from "@/components";
import {useTranslation} from "react-i18next";
import Dialog from "@mui/material/Dialog";
import {Close as CloseIcon} from "@mui/icons-material";
import DialogContent from "@mui/material/DialogContent";
import {useCustom, useCustomMutation} from "@refinedev/core";
import {API_URL} from "@/api";
import {DocumentUploadType} from "@/helpers";


const TrainingDetailsIndex: React.FC = () => {
  const {t, i18n} = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const course = location.state?.item
  //console.log("course",course);

  const [openModal, setOpenModal] = useState(false);
  const hour = Math.floor(course.duration/60);
  const minute = course.duration % 60;

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  const { data: publishedTrainingData, isLoading: isPublishedTrainingLoading, refetch: refetchTraining} = useCustom({
    url: `${API_URL}/society/training/courses-history/${course.id}`,
    method: "post",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const {mutate: enroll, isLoading: isLoadingCreate} = useCustomMutation();
  const Enroll = (): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    enroll(
      {
        url: `${API_URL}/society/training/courses/enroll`,
        method: "post",
        values: {
          trainingCourseId : course.id,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            const enroll = data?.data?.data;
            let msg = "";
            if(enroll.completionStatus === "COMPLETED"){
              msg = t("TRAINING_SUCCESS");
            }
            else if(enroll.completionStatus === "IN_PROGRESS"){
              msg = data?.data?.msg.includes("Successfully") ? t("TRAINING_ENROLL_SUCCESSFUL") : t("TRAINING_ENROLLED");
              navigate("/latihan/info", {state:{enroll:enroll}});
            }
            return {
              message: msg || data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const handleStart = () => {
    Enroll();
  }

  return (
    <>
      <TrainingHeader/>
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box
          sx={{
            height: "100%",
            zIndex: 0,
            backgroundImage: `url('${course.poster ?? '/detailsImage1.png'}')`,
            backgroundSize: "cover",
            backgroundRepeat: "no-repeat",
            backgroundPosition: "center center",
            justifyContent: "left",
            display: "flex",
            px: 5,
            py: 5,
            borderRadius: 2.5,
          }}>
          <Box>
            <Box sx={{width: "60%"}}>
              <Typography
                sx={{
                  color: "#FFF",
                  fontSize: "35px",

                }}
              >
                {course.title}
              </Typography>
            </Box>
            <Box sx={{width: "60%"}}>
              <Typography
                sx={{
                  mt: 2,
                  color: "#FFF",
                  fontSize: "12px",
                }}
              >
                {course.description}
              </Typography>
            </Box>
            <Box sx={{mt: 5, width: "40%"}}>
              <ButtonPrimary
                variant="outlined"
                sx={{
                  bgcolor: "#0CA6A6",
                  "&:hover": {bgcolor: "#0CA6A6"},
                  color: "#fff",
                  fontWeight: "400",
                }}
                onClick={() => setOpenModal(true)}
              >
                {t("join")}
              </ButtonPrimary>
            </Box>
            <Typography
              sx={{
                mt: 2,
                color: "#FFF",
                fontSize: "12px",
              }}
            >
              {`${course.participants ?? 0} ${t("participated")}`}
            </Typography>
          </Box>
        </Box>
        <Box sx={{
          position: "absolute",
          top: 0,
          left: "10%",
          right: 0,
          bottom: 0,
          margin: "auto",
          height: "120px",
          width: "40%",
          backgroundColor: "#fff",
          zIndex: 10,
          borderRadius: 2.5,
          display: "flex",
          flexDirection: "row",
          gap: 5,
          boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
          justifyContent: "space-evenly",
          textAlign: "center"
        }}>
          <Box sx={{mx: 1}}>
            <Typography
              sx={{
                mt: 6,
                color: "#666666",
                fontWeight: 500,
                fontSize: "16px",
              }}
            >
              {`${t("level")} ${course.difficultyLevel}`}
            </Typography>
            <Typography
              sx={{
                color: "#666666",
                fontWeight: 400,
                fontSize: "12px",
                //width: "80%",
              }}
            >
              {`${t("get")} ${course.totalPoints ?? 0} ${t("pointsWithThisCourse")}`}
            </Typography>
          </Box>
          <Box sx={{mx: 1}}>
            <Typography
              sx={{
                mt: 6,
                color: "#666666",
                fontWeight: 500,
                fontSize: "16px",
              }}
            >
              {t("flexibleSchedule")}
            </Typography>
            <Typography
              sx={{
                color: "#666666",
                fontWeight: 400,
                fontSize: "12px",
                //width: "80%",
              }}
            >
              {t("noPresetTime")}
            </Typography>
          </Box>
          <Box sx={{mx: 1,}}>
            <Typography
              sx={{
                mt: 6,
                color: "#666666",
                fontWeight: 500,
                fontSize: "16px",
              }}
            >
              {t("courseDuration")}
            </Typography>
            <Typography
              sx={{
                color: "#666666",
                fontWeight: 400,
                fontSize: "12px",
              }}
            >
              {`${t("trainingDuration")} ${t("time")} ${t("timeUnitHour", {
                count: hour,
              })} ${t("timeUnitMinute", {
                count: minute,
              })}`}
            </Typography>
          </Box>
        </Box>
        <Box
          sx={{
            px: 5,
            py: 2,
            //pl: {xs: 2, md: 6},
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            //flex: 1,
            mt: 2,
            //width: "20%"
          }}
        >
          <Typography
            sx={{
              mt: 6,
              color: "#666666",
              fontWeight: 700,
              fontSize: "20px",
            }}
          >
            {t("courseDescription")}
          </Typography>
          <Typography
            sx={{
              mt: 2,
              color: "#666666",
              fontSize: "14px",
              overflowWrap: 'break-word !important',
              wordBreak: 'break-word !important'
            }}
          >
            {course.explanation}
          </Typography>
          <Typography
            sx={{
              mt: 6,
              color: "#666666",
              fontWeight: 700,
              fontSize: "20px",
            }}
          >
            {t("courseObjective")}
          </Typography>
          <Typography
            sx={{
              mt: 2,
              color: "#666666",
              fontSize: "14px",
              overflowWrap: 'break-word !important',
              wordBreak: 'break-word !important'
            }}
          >
            {course.objective}
          </Typography>
        </Box>
      </Box>
      <Dialog
        open={openModal}
        onClose={handleCloseModal}
        maxWidth="xl"
        fullWidth
        //sx={{p:0}}
      >
        <DialogContent dividers={false} sx={{position: "relative", p: 0, pt: 4}}>
          <IconButton
            aria-label="close"
            onClick={handleCloseModal}
            sx={{
              position: "absolute",
              right: 8,
              top: 1,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon/>
          </IconButton>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              gap: 2,
              marginTop: 4,
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                mb: 3,
                gap: 1,
                flexWrap: "wrap",
              }}
            >
              <Box sx={{width: {xs: "100px", sm: "146px"}}}>
                <img
                  src="/jata-negara.jpg"
                  alt="Logo"
                  style={{
                    height: "80px", // Default height
                    marginRight: "10px",
                    width: "100%",
                    objectFit: "contain",
                  }}
                  className="responsive-logo"
                />
              </Box>
              <Box sx={{width: {xs: "100px", sm: "146px"}}}>
                <img
                  src="/logo.png"
                  alt="Logo 2"
                  style={{
                    height: "80px", // Default height
                    width: "100%",
                    objectFit: "contain",
                  }}
                  className="responsive-logo"
                />
              </Box>
            </Box>
            <Box sx={{justifyContent: "center",}}>
              <Typography
                sx={{
                  mt: 6,
                  color: "#666666",
                  fontWeight: 500,
                  fontSize: "14px",
                  textAlign: "center",
                }}
              >
                {t("courseWelcome")}
              </Typography>
              <Box sx={{justifyContent: "center",}}>
                <Typography
                  sx={{
                    mt: 6,
                    color: "#666666",
                    fontWeight: 600,
                    fontSize: "20px",
                    textAlign: "center",
                  }}
                >
                  {course.title}
                </Typography>
              </Box>
              <Box sx={{display: "flex", flexDirection: "row", justifyContent: "center", gap: 5}}>
                <Box>
                  <Typography
                    sx={{
                      mt: 6,
                      color: "#0CA6A6",
                      fontWeight: 500,
                      fontSize: "12px",
                      textAlign: "center",
                    }}
                  >
                     {`${t("courseLevel")} ${course.difficultyLevel}`}
                  </Typography>
                </Box>
                <Box>
                  <Typography
                    sx={{
                      mt: 6,
                      color: "#0CA6A6",
                      fontWeight: 500,
                      fontSize: "12px",
                      textAlign: "center",
                    }}
                  >
                    {`${t("time")} ${t("timeUnitHour", {
                      count: hour,
                    })} ${t("timeUnitMinute", {
                      count: minute,
                    })}`}
                  </Typography>
                </Box>
              </Box>
              <Box>
                <Typography
                  sx={{
                    mt: 6,
                    color: "#666666E5",
                    fontWeight: 500,
                    fontSize: "14px",
                    textAlign: "center",
                  }}
                >
                  {t("importantTrainingCourseAnnouncement")}
                </Typography>
              </Box>
              <Box>
                <Typography
                  sx={{
                    mt: 6,
                    color: "#666666E5",
                    fontWeight: 500,
                    fontSize: "14px",
                    textAlign: "center",
                  }}
                >
                  {t("GOODLUCK")}
                </Typography>
              </Box>
              <Box sx={{display: "flex", mt: 5, justifyContent: "center"}}>
                <ButtonPrimary
                  variant="outlined"
                  sx={{
                    bgcolor: "#0CA6A6",
                    "&:hover": {bgcolor: "#0CA6A6"},
                    color: "#fff",
                    fontWeight: "400",
                  }}
                  onClick={handleStart}
                >
                  {t("mula")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>
          <Box
            sx={{
              position: "relative",
              mt: 5,
              height: "300px",
              zIndex: 0,
              backgroundImage: `url('${course.poster ?? '/detailsImage1.png'}')`,
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
              backgroundPosition: "center center",
            }}/>
        </DialogContent>

      </Dialog>
    </>
  )
    ;
}

export default TrainingDetailsIndex;
