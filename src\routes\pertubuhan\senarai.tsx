import { Outlet, Route } from "react-router-dom";
import { RouteGuard } from "../../components/RouteGuard";
import { registerRoutes } from "../../helpers/routeDetector";
import SenaraiLayout from "../../pages/pertubuhan/SenaraiLayout";
import CreateCawanganMam from "../../pages/pertubuhan/pengurusan-cawangan/maklumat-am";
import ListCawangan from "../../pages/pertubuhan/pengurusan-cawangan/ListCawangan";
import RegisterCawangan from "../../pages/pertubuhan/pengurusan-cawangan/RegisterCawangan";
import DokumenSokongan from "../../pages/pertubuhan/pengurusan-cawangan/dokumen-sokongan";
import MinuteMesyuarat from "../../pages/pertubuhan/pengurusan-cawangan/minute-mesyuarat";
import AhliJawatanKuasa from "../../pages/pertubuhan/pengurusan-cawangan/ahli-jawatankuasa";
import CreateAjkCawangan from "../../pages/pertubuhan/pengurusan-cawangan/ahli-jawatankuasa/CreateAjk";
import CreateAjkBukanWnCawangan from "../../pages/pertubuhan/pengurusan-cawangan/ahli-jawatankuasa/CreateAjkBukanWn";
import CreateLanjutMasa from "../../pages/pertubuhan/pengurusan-cawangan/lanjut-masa/CreateLanjutMasa";
import PembayaranCawangan from "../../pages/pertubuhan/pengurusan-cawangan/pembayaran-cawangan";
import PembaharuanSetiausahaCawangan from "../../pages/pertubuhan/pengurusan-cawangan/pembaharuan-setiausaha";
import Pelantikan from "../../pages/pertubuhan/pengurusan-cawangan/pembaharuan-setiausaha/Pelantikan";
import Cetak from "../../pages/pertubuhan/pengurusan-cawangan/pembaharuan-setiausaha/Cetak";
import { Online } from "../../pages/pertubuhan/pengurusan-cawangan/pembayaran-cawangan/Online";
import Kaunter from "../../pages/pertubuhan/pengurusan-cawangan/pembayaran-cawangan/Kaunter";
import { Term } from "../../pages/pertubuhan/pengurusan-cawangan/pembayaran-cawangan/Term";
import Butiran from "../../pages/pertubuhan/pengurusan-cawangan/pembayaran-cawangan/Butiran";
import PaparCawangan from "../../pages/pertubuhan/pengurusan-cawangan/papar";
import ViewAjkBukanWnCawangan from "@/pages/pertubuhan/pengurusan-cawangan/ahli-jawatankuasa/ViewAjkBukanWn";

// Layout component to wrap all senarai routes with protection
const SenaraiGuardedLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <SenaraiLayout>
      <Outlet />
    </SenaraiLayout>
  </RouteGuard>
);

// Register routes with their portal types
registerRoutes({
  // Add your route registrations here
});

export const senarai = {
  routes: (
    <Route path="senarai" element={<SenaraiGuardedLayout />}>
        <Route path="cawangan">
          <Route index element={<ListCawangan />} />

          <Route path=":id/papar" element={<PaparCawangan />} />

          <Route path="register" element={<RegisterCawangan />} />

          <Route path="pembaharuan">
            <Route index element={<PembaharuanSetiausahaCawangan />} />

            <Route path="pelantikan/:branchId" element={<Pelantikan />} />

            <Route path="cetak" element={<Cetak />} />
          </Route>

          <Route path="pembayaran">
            <Route index element={<PembayaranCawangan />} />

            <Route path="kaunter" element={<Kaunter />} />

            <Route path="online">
              <Route index element={<Online />} />

              <Route path="term" element={<Term />} />

              <Route path="butiran" element={<Butiran />} />
            </Route>
          </Route>

          <Route path="maklumat-am" element={<CreateCawanganMam />} />

          <Route path="minute-mesyuarat" element={<MinuteMesyuarat />} />

          <Route path="ahlijawatankuasa" element={<AhliJawatanKuasa />} />

          <Route path="create-ajk">
            <Route index element={<CreateAjkCawangan />} />
          </Route>

          <Route path="create-ajk-bukan-wn">
            <Route index element={<CreateAjkBukanWnCawangan />} />
          </Route>
          <Route path="view-ajk-bukan-wn">
            <Route index element={<ViewAjkBukanWnCawangan />} />
          </Route>
          <Route path="dokumen-sokongan" element={<DokumenSokongan />} />

          <Route path="lanjut-masa">
            <Route path="create">
              <Route index element={<CreateLanjutMasa />} />
            </Route>
          </Route>
        </Route>
      </Route>
  ),
};
