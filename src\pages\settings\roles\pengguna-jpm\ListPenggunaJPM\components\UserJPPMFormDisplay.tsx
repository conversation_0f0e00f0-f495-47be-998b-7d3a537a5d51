import { ButtonFormSubmitFormik, Select } from "@/components";
import ButtonPrevious from "@/components/button/ButtonPrevious";
import Input from "@/components/input/Input";
import {
  BranchListResponseBodyGet,
  PositionListResponseBodyGet,
  SettingsJPPMUserCreateRequestBody,
  UserRoleListResponseBodyGet,
} from "@/controllers";
import { capitalizeWords, formatDate, useQuery } from "@/helpers";
import { ListGelaran } from "@/helpers/enums";
import { Radio, Theme, useMediaQuery } from "@mui/material";
import Box from "@mui/material/Box/Box";
import Chip from "@mui/material/Chip/Chip";
import FormControlLabel from "@mui/material/FormControlLabel/FormControlLabel";
import FormHelperText from "@mui/material/FormHelperText/FormHelperText";
import Grid from "@mui/material/Grid/Grid";
import MenuItem from "@mui/material/MenuItem/MenuItem";
import RadioGroup from "@mui/material/RadioGroup/RadioGroup";
import Typography from "@mui/material/Typography/Typography";
import { CrudFilter, useBack } from "@refinedev/core";
import {
  Field,
  FieldArray,
  FieldArrayRenderProps,
  FieldProps,
  Form,
  useFormikContext,
} from "formik";

import { useTranslation } from "react-i18next";
import { useSearchParams, useNavigate } from "react-router-dom";

export interface UserJPPMFormDisplayProps<
  Payload extends SettingsJPPMUserCreateRequestBody = SettingsJPPMUserCreateRequestBody
> {
  /**
   * @default false
   */
  isLoadingUserData?: boolean;

  initialValues: Payload;

  /**
   * @default false
   */
  isReadOnly?: boolean;

  /**
   * @default false
   */
  showButtons?: boolean;

  /**
   * @default false
   */
  showHistoricalLink?: boolean;

  /**
   * Custom title for the form
   */
  title?: string;

  /**
   * Pre-populated user data for historical records
   */
  userData?: any;

  /**
   * Skip fetching additional user data (for historical records)
   */
  skipUserDataFetch?: boolean;
}

export const UserJPPMFormDisplay = <
  Payload extends SettingsJPPMUserCreateRequestBody = SettingsJPPMUserCreateRequestBody,
  PropType extends UserJPPMFormDisplayProps<Payload> = UserJPPMFormDisplayProps<Payload>,
  BranchListType extends BranchListResponseBodyGet = BranchListResponseBodyGet,
  PositionDataType extends PositionListResponseBodyGet = PositionListResponseBodyGet,
  UserRoleType extends UserRoleListResponseBodyGet = UserRoleListResponseBodyGet
>({
  isLoadingUserData = false,
  initialValues,
  isReadOnly = false,
  showButtons = true,
  showHistoricalLink = true,
  title,
  userData,
  skipUserDataFetch = false,
}: PropType) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const userId = searchParams.get("id");
  const { data: branchListsResponse, isLoading: isLoadingBranch } = useQuery<{
    data: BranchListType[];
  }>({
    url: `society/admin/branch/list`,
  });
  const { data: positionListsResponse, isLoading: isLoadingPosition } =
    useQuery<{ data: PositionDataType[] }>({
      url: `society/admin/jppmPostion/list`,
    });
  const { data: userRolesResponse } = useQuery<{
    data: { data: UserRoleType[] };
  }>({
    url: `user/userRole/getAllUserRolesList`,
    filters: [
      { field: "size", value: 1000, operator: "eq" },
      { field: "page", value: 1, operator: "eq" },
    ],
  });
  const { values, errors, resetForm, setFieldValue, isSubmitting } =
    useFormikContext<Payload>();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const back = useBack();

  const hasUserId = !!userId;
  const branchLists = branchListsResponse?.data?.data ?? [];
  const branchListOptions = branchLists.map((branch) => ({
    value: branch.id,
    label: capitalizeWords(branch.description, null, true),
  }));
  const positionLists = positionListsResponse?.data?.data ?? [];
  const seen = new Set();
  const positionListOptions = positionLists.map((position) => ({
    value: position.id,
    label: `${capitalizeWords(position.name, null, true)} ${position.grade}`,
  }));

  const userRoles = userRolesResponse?.data?.data?.data ?? [];
  const userRoleOptions = userRoles.map((userRole) => ({
    value: userRole.role,
    label: userRole.role,
    id: userRole.id,
  }));
  // console.log("userRoleOptions", userRoleOptions);
  // Use userData directly (contains all needed information)
  const userUpdater = userData ?? null;
  const isLoadingUpdateForm = isLoadingUserData;
  const isUserExist = false; // Not needed for historical view

  // Check if status is deactivated (value 4) or if form is read-only
  const isStatusDeactivated = Number(values.status) === 4;
  const isStatusRejected = Number(values.status) === 5;
  const isFieldDisabled =
    isReadOnly ||
    isLoadingUpdateForm ||
    isSubmitting ||
    isStatusDeactivated ||
    isStatusRejected;

  const handleClear = () => {
    resetForm({
      values: hasUserId
        ? initialValues
        : ({
            name: "",
            identificationNo: "",
            email: "",
            userRole: [],
          } as unknown as Payload),
    });
  };
  const generatePositionLabel = (input: string) => {
    const trimmedInput = input.trim();

    const result = capitalizeWords(
      trimmedInput,
      (word) => {
        if (word === "") {
          return "";
        }

        const shouldPreserve = ["RO", "(RO)", "PT"].includes(word);
        return shouldPreserve ? word : null;
      },
      true
    );

    return result;
  };

  return (
    <Form>
      <Box>
        {title && (
          <Typography className={"title"} sx={{ mb: 4 }}>
            {title}
          </Typography>
        )}
        {!title && (
          <Typography className={"title"} sx={{ mb: 4 }}>
            {t("maklumatPenggunaJPPM")}
          </Typography>
        )}
        <Field name="title">
          {({ field, meta }: FieldProps) => (
            <Input
              {...field}
              required
              label={t("gelaran")}
              options={ListGelaran}
              disabled={isFieldDisabled}
              type="select"
              error={!!meta.error}
              helperText={meta.error ?? " "}
            />
          )}
        </Field>
        <Field name="name">
          {({ field, meta, form }: FieldProps) => (
            <Input
              {...field}
              required
              disabled={hasUserId || isFieldDisabled}
              label={t("fullName")}
              {...(!hasUserId && {
                error: !!meta.error,
                helperText: meta.error ?? " ",
              })}
            />
          )}
        </Field>
        <Field name="identificationNo">
          {({ field, meta, form }: FieldProps) => (
            <Input
              {...field}
              required
              disabled={hasUserId || isFieldDisabled}
              inputProps={{
                minLength: 12,
                maxLength: 12,
                inputMode: "numeric",
              }}
              digitsLimit={12}
              label={t("idNumberPlaceholder")}
              {...(!hasUserId && {
                error: !!meta.error || isUserExist,
                helperText: isUserExist
                  ? t("errorUserExistUserCreation")
                  : meta.error ?? " ",
              })}
            />
          )}
        </Field>
        <Field name="jppmBranch">
          {({ field, meta, form }: FieldProps) => (
            <Input
              {...field}
              required
              disabled={isFieldDisabled}
              label={t("DivisionStateJPPMBranch")}
              type="select"
              error={!!meta.error}
              helperText={meta.error ?? " "}
              isLoadingData={isLoadingBranch}
              options={branchListOptions}
            />
          )}
        </Field>
        <Field name="position">
          {({ field, meta, form }: FieldProps) => (
            <Input
              {...field}
              required
              disabled={isFieldDisabled}
              label={capitalizeWords(t("actualPosition"))}
              type="select"
              error={!!meta.error}
              helperText={meta.error ?? " "}
              isLoadingData={isLoadingPosition}
              options={positionListOptions}
            />
          )}
        </Field>
        <Grid container spacing={2} sx={{ pb: 1, alignItems: "center" }}>
          <Grid
            item
            xs={12}
            sm={4}
            sx={{ display: "flex", alignItems: "flex-start" }}
          >
            <Typography
              sx={{
                color: "#666666",
                fontWeight: "400 !important",
                fontSize: "14px",
              }}
            >
              {t("peranan")}
              <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            {userRoleOptions?.length > 0 && (
              <>
                <FieldArray name="userRole">
                  {(arrayHelpers: FieldArrayRenderProps) => (
                    <>
                      <Select
                        required
                        multiple
                        disabled={isFieldDisabled}
                        name="userRole"
                        value={values.userRole as string[]}
                        onChange={(e) => {
                          const selected = e.target.value as string[];
                          setFieldValue("userRole", selected);
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: 224,
                            },
                          },
                        }}
                        t={t}
                        fullWidth
                        sx={{
                          minHeight: "40px",
                          borderRadius: "5px",
                          fontSize: "14px",
                          "& .MuiSelect-select": {
                            color: "black",
                            fontSize: "14px",
                          },
                          pt: 1,
                          pb: 1,
                        }}
                        renderValue={(selected: string[]) => (
                          <Box
                            style={{
                              display: "flex",
                              gap: 5,
                              flexWrap: "wrap",
                            }}
                          >
                            {selected?.map((value) => {
                              const matchingItem = userRoleOptions.find(
                                (item) => item.value === value
                              );
                              return (
                                <Chip
                                  key={value}
                                  sx={{
                                    border: "1px solid #147C7C",
                                    bgcolor: "#147C7C80",
                                    color: "white",
                                  }}
                                  label={generatePositionLabel(
                                    matchingItem?.label ?? value
                                  )}
                                  onMouseDown={(e) => e.stopPropagation()} // Prevent Select from opening
                                  {...(!isFieldDisabled && {
                                    onDelete: () => {
                                      const index = selected.findIndex(
                                        (item) => item === value
                                      );
                                      arrayHelpers.remove(index);
                                    },
                                  })}
                                />
                              );
                            })}
                          </Box>
                        )}
                      >
                        {userRoleOptions?.map((item) => (
                          <MenuItem key={item.value} value={item.value}>
                            {generatePositionLabel(item.label)}
                          </MenuItem>
                        ))}
                      </Select>
                    </>
                  )}
                </FieldArray>
                {errors?.userRole && (
                  //@ts-expect-error
                  <FormHelperText error>{errors.userRole}</FormHelperText>
                )}
              </>
            )}
          </Grid>
        </Grid>
        <Field name="email">
          {({ field, meta, form }: FieldProps) => (
            <Input
              {...field}
              required
              label={t("email")}
              type="email"
              error={!!meta.error}
              helperText={meta.error ?? ""}
              disabled={isFieldDisabled}
            />
          )}
        </Field>
        <Grid container spacing={2} sx={{ mb: 3, alignItems: "center" }}>
          <Grid item xs={12} sm={4}>
            <Typography
              variant="body1"
              sx={{
                color: "#666666",
                fontWeight: "400 !important",
                fontSize: "14px",
              }}
            >
              {t("status")}
              <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <Field name="status" type="radio">
              {({ field, meta, form }: FieldProps) => (
                <>
                  <RadioGroup
                    {...field}
                    onChange={(e) => {
                      form.setFieldValue("status", e.target.value);
                    }}
                    name="status"
                    row
                  >
                    <FormControlLabel
                      value={1}
                      checked={values.status == 1}
                      control={<Radio />}
                      disabled={isFieldDisabled || !userId}
                      label={t("userStatusActive")}
                    />
                    <FormControlLabel
                      value={3}
                      checked={values.status == 3}
                      control={<Radio />}
                      disabled={isFieldDisabled || !userId}
                      label={t("userStatusInactive")}
                    />
                    <FormControlLabel
                      value={4}
                      checked={values.status == 4}
                      control={<Radio />}
                      disabled={isFieldDisabled || !userId}
                      label={t("userStatusDeactivated")}
                    />
                    {isStatusRejected && (
                      <FormControlLabel
                        value={5}
                        checked={values.status == 5}
                        control={<Radio />}
                        disabled={isFieldDisabled || !userId}
                        label={t("tolak")}
                      />
                    )}
                  </RadioGroup>
                  {meta.error && (
                    <FormHelperText error>{meta.error}</FormHelperText>
                  )}
                </>
              )}
            </Field>
          </Grid>
        </Grid>
        <Field name="statusLog">
          {({ field, meta }: FieldProps) => (
            <Input
              {...field}
              required
              value={
                formatDate(userUpdater?.lastLogin, "DD-MM-YYYY HH:mm A") ?? "-"
              }
              label={t("statusLogMasuk")}
              error={!!meta.error}
              helperText={meta.error ?? ""}
              disabled
              selectStyleProfileId={2}
            />
          )}
        </Field>
      </Box>
      <Box sx={{ mb: 6, mt: 1 }}>
        <Input
          value={userUpdater?.createdByName ?? "-"}
          name="createBy"
          label={t("createBy")}
          disabled
        />
        <Input
          value={
            formatDate(userUpdater?.lastUpdateDate, "DD-MM-YYYY HH:mm A") ?? "-"
          }
          name="lastUpdate"
          label={t("lastUpdate")}
          disabled
        />
        <Input
          value={
            formatDate(userUpdater?.lastPasswordChange, "DD-MM-YYYY HH:mm A") ??
            "-"
          }
          name="lastUpdateChangePassword"
          label={t("lastUpdateChangePassword")}
          disabled
        />
        {showHistoricalLink && (
          <Box
            sx={{
              cursor:
                !(values as any).historicalUsers ||
                (values as any).historicalUsers.length === 0
                  ? "default"
                  : "pointer",
            }}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              if (
                (values as any).historicalUsers &&
                (values as any).historicalUsers.length > 0
              ) {
                navigate(
                  `/pertubuhan/settings/pengurusan-pengguna-jpm/sejarah-akaun?id=${userId}`
                );
              }
            }}
          >
            <Input
              value={
                !(values as any).historicalUsers ||
                (values as any).historicalUsers.length === 0
                  ? t("tiadaMaklumat")
                  : `${t("klikUntukLihatSenaraiPendaftaran")} (${
                      (values as any).historicalUsers.length
                    })`
              }
              name="sejarahAkaun"
              label={t("sejarahAkaun")}
              disabled
              sx={{
                "& .MuiInputBase-input": {
                  color:
                    !(values as any).historicalUsers ||
                    (values as any).historicalUsers.length === 0
                      ? "inherit"
                      : "#3A13E5 !important",
                  WebkitTextFillColor:
                    !(values as any).historicalUsers ||
                    (values as any).historicalUsers.length === 0
                      ? "inherit"
                      : "#3A13E5 !important",
                  cursor:
                    !(values as any).historicalUsers ||
                    (values as any).historicalUsers.length === 0
                      ? "default"
                      : "pointer",
                },
                pointerEvents: "none", // Disable pointer events on the input to prevent conflicts
              }}
            />
          </Box>
        )}
      </Box>
      {showButtons && (
        <Grid
          item
          xs={12}
          sx={{
            mt: 2,
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            justifyContent: "flex-end",
            gap: 1,
          }}
        >
          <ButtonPrevious
            variant="outlined"
            sx={{
              bgcolor: "white",
              "&:hover": { bgcolor: "white" },
              width: isMobile ? "100%" : "auto",
            }}
            onClick={() => handleClear()}
          >
            {t("previous")}
          </ButtonPrevious>
          <ButtonFormSubmitFormik
            variant="contained"
            type="submit"
            disabled={
              hasUserId
                ? isStatusDeactivated
                : isUserExist || isStatusDeactivated || isStatusRejected
            }
            label={t(hasUserId ? "update" : "add")}
            sx={{
              width: isMobile ? "100%" : "auto",
              boxShadow: "none",
            }}
          />
        </Grid>
      )}
    </Form>
  );
};
