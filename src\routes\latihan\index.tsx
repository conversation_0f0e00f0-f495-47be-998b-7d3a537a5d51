import { Route, Outlet, Navigate } from "react-router-dom";
import TrainingIndex from "@/pages/training";
import TrainingDetailsIndex from "@/pages/training/trainingDetails";
import CertificateDetailsIndex from "@/pages/training/certificateDetails";
import Certificate from "@/pages/training/certificateDetails/certificate";
import TrainingPreview from "@/pages/training/trainingDetails/trainingPreview";
import TrainingInfo from "@/pages/training/trainingDetails/trainingInfo";
import InternalTrainingIndex from "@/pages/internal-training";
import InternalTrainingCreate from "@/pages/internal-training/createTraining";
import { NEW_PermissionNames, registerRoutes } from "@/helpers";
import { RouteGuard } from "@/components/RouteGuard";
import QuizAttemptList from "@/pages/internal-training/attempts";
import FeedbackPage from "@/pages/internal-training/feedbackPage";
import AuthHelper from "@/helpers/authHelper";

// Layout component to wrap all training routes with protection
const TrainingLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === "development"}
  >
    <Outlet />
  </RouteGuard>
);

// Layout component for internal training routes
const InternalTrainingLayout = () => {
  if (
    !AuthHelper.hasAuthority([NEW_PermissionNames.LATIHAN.label]) &&
    localStorage.getItem("portal") === "2"
  ) {
    return <Navigate to="/forbidden" replace />;
  }
  return (
    <RouteGuard
      autoUpdatePortal={true}
      showDebugInfo={process.env.NODE_ENV === "development"}
    >
      <Outlet />
    </RouteGuard>
  );
};

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  "/latihan-internal": "internal",
  "/latihan-internal/create": "internal",
  "/latihan-internal/update/:id": "internal",
  "/latihan-internal/attemptList/:id": "internal",
  "/latihan-internal/attemptList/feedback/:id": "internal",
  "/latihan": "external",
  "/latihan/maklumat": "external",
  "/latihan/info": "external",
  "/latihan/sijil": "external",
  "/latihan/sijil/detail": "external",
});

export const latihan = {
  routes: (
    <>
      <Route path="/latihan" element={<TrainingLayout />}>
        <Route index element={<TrainingIndex />} />
        <Route path="maklumat">
          <Route index element={<TrainingDetailsIndex />} />
        </Route>
        <Route path="info">
          <Route index element={<TrainingInfo />} />
        </Route>
        <Route path="sijil">
          <Route index element={<Certificate />} />
          <Route path="detail">
            <Route index element={<TrainingPreview />} />
          </Route>
        </Route>
      </Route>
      <Route path="/latihan-internal" element={<InternalTrainingLayout />}>
        <Route index element={<InternalTrainingIndex />} />
        <Route path="create">
          <Route index element={<InternalTrainingCreate />} />
        </Route>
        <Route path="update/:id">
          <Route index element={<InternalTrainingCreate isUpdate={true} />} />
        </Route>
        <Route path="attemptList/:id">
          <Route index element={<QuizAttemptList />} />
        </Route>
        <Route path="attemptList/feedback/:id">
          <Route index element={<FeedbackPage />} />
        </Route>
      </Route>
    </>
  ),
};
