import {Route, Outlet} from "react-router-dom";
import TrainingIndex from "@/pages/training";
import TrainingDetailsIndex from "@/pages/training/trainingDetails";
import CertificateDetailsIndex from "@/pages/training/certificateDetails";
import Certificate from "@/pages/training/certificateDetails/certificate";
import TrainingReview from "@/pages/training/trainingDetails/trainingReview";
import TrainingInfo from "@/pages/training/trainingDetails/trainingInfo";
import InternalTrainingIndex from "@/pages/internal-training";
import InternalTrainingCreate from "@/pages/internal-training/createTraining";
import { registerRoutes } from "../../helpers/routeDetector";
import { RouteGuard } from "../../components/RouteGuard";

// Layout component to wrap all training routes with protection
const TrainingLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Outlet />
  </RouteGuard>
);

// Layout component for internal training routes
const InternalTrainingLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Outlet />
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example: '/latihan': 'shared',
  // Example: '/latihan-internal': 'internal',
  // Add your route registrations here
});

export const latihan = {
  routes: (
    <>
      <Route path="/latihan" element={<TrainingLayout />}>
        <Route index element={<TrainingIndex/>}/>
        <Route path="maklumat">
          <Route index element={<TrainingDetailsIndex />} />
        </Route>
        <Route path="info">
          <Route index element={<TrainingInfo />} />
        </Route>
        <Route path="quiz">
          <Route index element={<CertificateDetailsIndex />} />
        </Route>
        <Route path="sijil">
          <Route index element={<Certificate />} />
          <Route path="detail">
            <Route index element={<TrainingReview />} />
          </Route>
        </Route>
      </Route>
      <Route path="/latihan-internal" element={<InternalTrainingLayout />}>
        <Route index element={<InternalTrainingIndex/>}/>
        <Route path="create">
          <Route index element={<InternalTrainingCreate />} />
        </Route>
        <Route path="update/:id">
          <Route index element={<InternalTrainingCreate isUpdate={true} />} />
        </Route>
      </Route>
    </>
  ),
};
