import { useEffect, useState } from "react";
import dayjs from "@/helpers/dayjs";
import {
  Box,
  TextField,
  Typography,
  Grid,
  useMediaQuery,
  Theme,
  Button,
  IconButton,
} from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ay<PERSON> } from "react-leaflet";
import { TrashIcon } from "@/components/icons";
import Input from "@/components/input/Input";
import { useBackendLocalization } from "@/helpers/hooks/useBackendLocalization";
import useQuery from "@/helpers/hooks/useQuery";
import { MeetingMethods, MeetingTypeOption } from "@/helpers/enums";
import {
  SocietyAddressListResponseBodyGet,
  SocietyMeetingListResponseBodyGet,
  SocietyMeetingResponseBodyGet,
} from "@/models";
import { DocumentUploadType } from "@/helpers";
import { API_URL } from "@/api";
import { useCustomMutation } from "@refinedev/core";
import { useSelector } from "react-redux";
import { getAliranTugasAccess } from "@/redux/userReducer";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

export interface ViewMesyuaratProps {
  /**
   * @default false
   */
  viewOnly?: boolean
}

export const ViewMesyuarat = <
  MeetingData extends SocietyMeetingResponseBodyGet = SocietyMeetingResponseBodyGet,
  MeetingListData extends SocietyMeetingListResponseBodyGet = SocietyMeetingListResponseBodyGet,
  AddressListData extends SocietyAddressListResponseBodyGet = SocietyAddressListResponseBodyGet,
  PropType extends ViewMesyuaratProps = ViewMesyuaratProps
>({ viewOnly = false }: PropType) => {
  const { t } = useTranslation();
  const { id, meetingId } = useParams();
  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const { getTranslation } = useBackendLocalization<MeetingListData>();

  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  const isAliranTugasAccess = useSelector(getAliranTugasAccess);

  const { data: meetingResponse } = useQuery<{ data: MeetingData }>({
    url: `society/meeting/${meetingId}`,
  });
  const { data: meetingListResponse } = useQuery<{ data: MeetingListData[] }>({
    url: "society/admin/meeting/list",
  });
  const { data: addressListResponse } = useQuery<{ data: AddressListData[] }>({
    url: "society/admin/address/list",
  });

  const { data: documentResponse, refetch: refetchDocument } = useQuery({
    url: "society/document/documentByParam",
    filters: [
      {
        field: "meetingId",
        operator: "eq",
        value: meetingId,
      },
      {
        field: "type",
        operator: "eq",
        value: DocumentUploadType.MEETING,
      },
    ],
  });

  const meetingData = meetingResponse?.data?.data ?? null;
  const meetingListData = meetingListResponse?.data?.data ?? [];
  const addressData = addressListResponse?.data?.data ?? [];

  const meetingMethodList =
    meetingListData?.find((item) =>
      meetingData?.meetingMethod
        ? parseInt(meetingData.meetingMethod) === item.id
        : false
    ) ?? null;
  const meetingMethod = meetingMethodList
    ? getTranslation(meetingMethodList)
    : "-";
  const platformType =
    meetingData?.platformType?.length === 1
      ? null
      : meetingListData?.find((item) =>
          meetingData?.platformType
            ? parseInt(meetingData.platformType) === item.id
            : false
        ) ?? null;
  const state = meetingData?.state
    ? addressData?.find((item) => item.id === parseInt(meetingData.state)) ??
      null
    : null;
  const district = meetingData?.district
    ? addressData?.find((item) => item.id === parseInt(meetingData.district)) ??
      null
    : null;

  const calculateDuration = () => {
    if (meetingData?.meetingTime && meetingData?.meetingTimeTo) {
      const start = new Date(`2000-01-01T${meetingData?.meetingTime}`);
      const end = new Date(`2000-01-01T${meetingData?.meetingTimeTo}`);
      const diff = (end.getTime() - start.getTime()) / (1000 * 60 * 60);
      return diff.toFixed(2);
    }
    return "";
  };

  const handleDeleteFile = (id: number | undefined) => {
    DeleteFile(id);
  };

  const handlePreviewDocument = (fileUrl: string) => {
    window.open(fileUrl, "_blank");
  };
  const { mutate: deleteFile, isLoading: isLoadingDelete } =
    useCustomMutation();

  const DeleteFile: (id: number | undefined) => void = (id) => {
    deleteFile(
      {
        url: `${API_URL}/society/document/deleteDocument?id=${id}`,
        method: "put",
        values: {},
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          refetchDocument();
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  useEffect(() => {
    setUploadedFiles(documentResponse?.data?.data);
  }, [documentResponse?.data?.data]);

  const meetingTypeOptions = viewOnly
    ? MeetingTypeOption
    : MeetingTypeOption.filter((option) =>
        [2, 3, 4].includes(option.value)
  );

  const getMeetingTypeValue = () => {
    const val = meetingData?.meetingType ?? null
    if (val && !isNaN(parseInt(val))) {
      return parseInt(val)
    }
    if (val && isNaN(parseInt(val))) {
      const selectedType = MeetingTypeOption.find(item => item.value === parseInt(val)) ?? undefined
      if (selectedType === null) {
        return undefined
      }
      return selectedType?.value ?? undefined
    }
    return undefined
  }

  return (
    <>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px" }}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("mesyuaratPenubuhan")}
          </Typography>

          <Grid container>
            {/* <Grid item sm={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t("meetingType")}</Typography>
              </Box>
            </Grid> */}
            <Grid item sm={12}>
              {/* <DisabledTextField
                value={
                  meetingData?.meetingType
                    ? getMeetingLabel(Number(meetingData?.meetingType))
                    : "-"
                }
              /> */}
              <Input
                value={getMeetingTypeValue()}
                name="meetingType"
                disabled
                options={meetingTypeOptions}
                type="select"
                label={t("meetingType")}
                required
              />
            </Grid>

            <Grid item sm={12}>
              <Input
                value={meetingMethod}
                name="meetingMethod"
                disabled
                options={[{ value: meetingMethod, label: meetingMethod }]}
                type="select"
                label={t("meetingMethod")}
                required
              />
            </Grid>

            {meetingMethodList &&
              meetingData?.meetingMethod &&
              [MeetingMethods.ATAS_TALIAN, MeetingMethods.HYBRID].includes(
                // @ts-expect-error
                meetingData.meetingMethod
              ) &&
              platformType && (
                <Grid item sm={12}>
                  <Input
                    value={getTranslation(platformType)}
                    name="platformType"
                    disabled
                    options={[
                      {
                        value: getTranslation(platformType),
                        label: getTranslation(platformType),
                      },
                    ]}
                    type="select"
                    label={t("platformType")}
                    required
                  />
                </Grid>
              )}

            <Grid item sm={12}>
              <Input
                value={meetingData?.meetingPurpose ?? "-"}
                name="meetingPurpose"
                disabled
                label={t("tujuanMesyuarat")}
                required
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("masaDanTarikhMesyuarat")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item sm={12}>
              <Input
                value={
                  dayjs(meetingData?.meetingDate).format("DD-MM-YYYY") ?? "-"
                }
                name="meetinmeetingDategType"
                disabled
                label={t("meetingDate")}
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("time")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8} sx={{ mb: 1 }}>
              <Box display="flex" alignItems="center" flexWrap="wrap">
                <TextField
                  disabled
                  size="small"
                  value={
                    meetingData?.meetingTime
                      ? dayjs(meetingData.meetingTime, "HH:mm:ss").format(
                          "HH:mm"
                        )
                      : "-"
                  }
                  type="time"
                  sx={{
                    width: {
                      xs: "100%",
                      sm: "40%",
                      md: "40%",
                      lg: "30%",
                      xl: "20%",
                    },
                    backgroundColor: "var(--disabled-field)",
                    borderRadius: 2,
                    mb: { xs: 1, sm: 0 },
                  }}
                />
                <Box sx={{ mx: 1 }}>—</Box>
                <TextField
                  disabled
                  size="small"
                  value={
                    meetingData?.meetingTimeTo
                      ? dayjs(meetingData.meetingTimeTo, "HH:mm:ss").format(
                          "HH:mm"
                        )
                      : "-"
                  }
                  type="time"
                  sx={{
                    width: {
                      xs: "100%",
                      sm: "40%",
                      md: "40%",
                      lg: "30%",
                      xl: "20%",
                    },
                    backgroundColor: "var(--disabled-field)",
                    borderRadius: 2,
                    mb: { xs: 1, sm: 0 },
                  }}
                />
                <Typography
                  sx={{
                    ml: { xs: 0, sm: 2 },
                    width: { xs: "100%", sm: "auto" },
                    textAlign: { xs: "center", sm: "left" },
                    mt: { xs: 1, sm: 0 },
                  }}
                >
                  {calculateDuration() && `${calculateDuration()} ${t("jam")}`}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("alamatTempatMesyuarat")}
          </Typography>

          <Grid container>
            <Grid item sm={12}>
              <Input
                value={meetingData?.meetingPlace ?? "-"}
                name="meetinmeetingDategType"
                disabled
                label={t("namaTempatMesyuarat")}
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("meetingLocation")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <Box sx={{ m: 0, mb: 1 }}>
                <MapContainer
                  /**
                   * @todo change meetingLocation with data from backend
                   */
                  center={[2.745564, 101.707021]}
                  zoom={13}
                  style={{
                    height: "10rem",
                    width: "inherit",
                    borderRadius: "0.5rem",
                  }}
                >
                  <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                  {/**
                   * @todo change meetingLocation with data from backend
                   */}
                  <Marker position={[2.745564, 101.707021]} />
                </MapContainer>
              </Box>
            </Grid>
            <Grid item sm={12}>
              <Input
                value={meetingData?.meetingPlace ?? "-"}
                name="meetingPlaceAddress"
                disabled
                label={t("meetingPlaceAddress")}
                required
              />
            </Grid>

            <Grid item sm={12}>
              <Input
                value={state?.name ?? "-"}
                name="state"
                disabled
                options={[
                  { value: state?.name ?? "-", label: state?.name ?? "-" },
                ]}
                type="select"
                label={t("state")}
                required
              />
            </Grid>

            <Grid item sm={12}>
              <Input
                value={district?.name ?? "-"}
                name="district"
                disabled
                options={[
                  {
                    value: district?.name ?? "-",
                    label: district?.name ?? "-",
                  },
                ]}
                type="select"
                label={t("district")}
                required
              />
            </Grid>

            <Grid item sm={12}>
              <Input
                value={meetingData?.city ?? "-"}
                name="city"
                disabled
                label={t("city")}
              />
            </Grid>

            <Grid item sm={12}>
              <Input
                value={meetingData?.postcode ?? "-"}
                name="postcode"
                disabled
                label={t("postcode")}
                required
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("maklumatMesyuarat")}
          </Typography>
          <Grid container spacing={2}>
            <Grid item sm={12}>
              <Input
                value={meetingData?.totalAttendees ?? "-"}
                name="totalAttendees"
                disabled
                label={t("kehadiranAhliMesyuarat")}
                required
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("minitMesyuarat")}
          </Typography>

          {uploadedFiles && (
            <Box>
              {uploadedFiles?.map((file, index) => (
                <Box
                  key={`${file.id}-${index}`}
                  sx={{
                    border: "1px solid #E0E0E0",
                    borderRadius: "8px",
                    backgroundColor: "#fff",
                    p: 2,
                    mb: 1,
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <Typography
                      sx={{
                        cursor: "pointer",
                        "&:hover": {
                          color: "var(--primary-color)",
                          textDecoration: "underline",
                        },
                      }}
                      onClick={() => handlePreviewDocument(file.url)}
                    >
                      {file.name}
                    </Typography>
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 1,
                      }}
                    >
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M23 12L20.56 9.21L20.9 5.52L17.29 4.7L15.4 1.5L12 2.96L8.6 1.5L6.71 4.69L3.1 5.5L3.44 9.2L1 12L3.44 14.79L3.1 18.49L6.71 19.31L8.6 22.5L12 21.03L15.4 22.49L17.29 19.3L20.9 18.48L20.56 14.79L23 12ZM9.38 16.01L7 13.61C6.9073 13.5175 6.83375 13.4076 6.78357 13.2866C6.73339 13.1657 6.70756 13.036 6.70756 12.905C6.70756 12.774 6.73339 12.6443 6.78357 12.5234C6.83375 12.4024 6.9073 12.2925 7 12.2L7.07 12.13C7.46 11.74 8.1 11.74 8.49 12.13L10.1 13.75L15.25 8.59C15.64 8.2 16.28 8.2 16.67 8.59L16.74 8.66C17.13 9.05 17.13 9.68 16.74 10.07L10.82 16.01C10.41 16.4 9.78 16.4 9.38 16.01Z"
                          fill="var(--primary-color)"
                        />
                      </svg>
                      <IconButton
                        onClick={() => handleDeleteFile(file.id)}
                        sx={{
                          color: "#FF0000",
                          p: 1,
                        }}
                      >
                        <TrashIcon sx={{ width: 20, height: 20 }} />
                      </IconButton>
                    </Box>
                  </Box>
                </Box>
              ))}
            </Box>
          )}
        </Box>

        <Grid container spacing={2}>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <Button
              size="medium"
              variant="contained"
              onClick={() => navigate(-1)}
              sx={{ minWidth: "12rem", textTransform: "capitalize", color: "white" }}
            >
              {t("back")}
            </Button>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default ViewMesyuarat;
