import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  CircularProgress,
  Dialog,
  DialogContent,
  Grid,
} from "@mui/material";
import { useForm } from "@refinedev/react-hook-form";
import { useCustom, useLogin } from "@refinedev/core";
import {
  Link,
  Link as RouterLink,
  useNavigate,
  useLocation,
} from "react-router-dom";
import { useTranslation } from "react-i18next";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import { ComingSoon } from "./comingSoon";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import { ButtonPrimary, ButtonText } from "../../components/button";
import FeedbackDialog from "./feedbackDialog";
import { ChevronRightRounded, DoneRounded } from "@mui/icons-material";
import { GridCloseIcon } from "@mui/x-data-grid";
import { ForgotPassword } from "../forgotPassword";
import { Otp } from "../forgotPassword/otp";
import { Recovery } from "../forgotPassword/recovery";
import { API_URL } from "@/api";
import MYDID_icon from "@/assets/svg/MYDID_icon.svg";

interface LoginProps {
  onClose: () => void;
}

export const Login = ({ onClose }: LoginProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const { mutate: login } = useLogin();
  const { t, i18n } = useTranslation();

  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [showOTP, setShowOTP] = useState(false);
  const [showRecovery, setShowRecovery] = useState(false);
  const [showSuccessRecovery, setShowSuccessRecovery] = useState(false);
  const [showOnSuccessRecovery, setShowOnSuccessRecovery] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();
  const [currentComponent, setCurrentComponent] = useState<string | null>(null);
  const [languageAnchorEl, setLanguageAnchorEl] = useState<null | HTMLElement>(
    null
  );
  const [mobileMenuAnchorEl, setMobileMenuAnchorEl] =
    useState<null | HTMLElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMyDigitalId, setIsLoadingMyDigitalId] = useState(false);
  const [dialogAlertSaveOpen, setDialogAlertSaveOpen] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [openFeedbackDialog, setOpenFeedbackDialog] = useState(false);

  useEffect(() => {
    const showComponent = location.state?.showComponent;
    if (showComponent) {
      setCurrentComponent(showComponent);
    }
  }, [location]);

  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm();

  // const identificationNo = watch("identificationNo");

  const onSubmit = (data: any) => {
    setIsLoading(true);
    login(data, {
      onSuccess: (data) => {
        if (data?.name === "ResetPassword") {
          setIsSubmitted(true);
          setDialogAlertSaveOpen(true);
        }
        setIsLoading(false);
      },
    });
  };

  const handleClickShowPassword = () => setShowPassword((show) => !show);

  const menuItems = [
    { label: "Panduan", path: "panduan" },
    { label: "Maklum Balas", path: "feedback" },
    { label: "Pewartaan", path: "pewartaan" },
  ];

  const identificationNo = watch("identificationNo");

  const { data, isLoading: isForgotPasswordLoading } = useCustom<any>({
    url: `${API_URL}/user/auth/forgotPassword`,
    method: "get",
    config: {
      query: {
        identificationNo,
      },
    },
    queryOptions: {
      enabled: isSubmitted,
      retry: false,
      cacheTime: 0,
    },
    successNotification(data, values, resource) {
      console.log("success", data);
      setIsSubmitted(false);

      if (localStorage.getItem("forgotPassword")) {
        localStorage.removeItem("forgotPassword");
      }

      localStorage.setItem(
        "forgotPassword",
        JSON.stringify(data?.data?.data?.email)
      );

      if (localStorage.getItem("identificationNo")) {
        localStorage.removeItem("identificationNo");
      }

      localStorage.setItem(
        "identificationNo",
        JSON.stringify(identificationNo)
      );

      localStorage.setItem("resetEmail", JSON.stringify(true));

      return {
        message: t("success"),
        type: "success",
      };
    },
    errorNotification(error, values, resource) {
      console.log("error", error);
      setIsSubmitted(false);
      return {
        message: t("error"),
        type: "error",
      };
    },
  });

  const handleBack = () => {
    setCurrentComponent(null);
  };

  const handleLanguageMenuClose = () => {
    setLanguageAnchorEl(null);
  };

  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
    handleLanguageMenuClose();
  };

  const handleMobileMenuClose = () => {
    setMobileMenuAnchorEl(null);
  };

  const navigateToMyDigitalIdFlow = async () => {
    setIsLoadingMyDigitalId(true);
    try {
      const res = await fetch(`${API_URL}/user/auth/myDigitalId/login`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });
      const data = await res.json();
      if (data?.loginUrl) {
        window.location.href = data.loginUrl;
      }
    } catch (error) {
      console.error("Failed to fetch login URL:", error);
    } finally {
      setIsLoadingMyDigitalId(false);
    }
  };

  useEffect(() => {
    const isLogout = localStorage.getItem("isLogout");
    const portal = localStorage.getItem("portal");
    console.log("isLogout", isLogout, portal);
    if (Boolean(isLogout) && portal === "1") {
      setOpenFeedbackDialog(Boolean(t));
    }
    localStorage.removeItem("isLogout");
    localStorage.removeItem("portal");
  }, []);

  useEffect(() => {
    const handleBeforeUnload = () => {
      localStorage.removeItem("tmp-identificationNo");
    };

    // Attach the event listener
    window.addEventListener("beforeunload", handleBeforeUnload);

    // Cleanup the event listener on unmount
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  const handlePasswordOTPSwitch = () => {
    setShowForgotPassword(false);
    setShowOTP(true);
  };

  const handleOTPPass = () => {
    setShowOTP(false);
    setShowRecovery(true);
  };

  const handleBackToForgotPassword = () => {
    setShowOTP(false);
    setShowForgotPassword(true);
  };

  return (
    <Box>
      {currentComponent === null && (
        <Grid container spacing={0}>
          <Grid
            item
            xs={4}
            sm={4}
            md={4}
            sx={{
              margin: 0,
              p: 0,
              mt: showOTP || showRecovery ? 8 : 0,
              mb: showOTP || showRecovery ? 8 : 0,
              display: { xs: "none", sm: "block" }, // Hide on xs, show on sm and up
              borderRight:
                showOTP || showRecovery ? "2px solid #66666640" : null, // Adds a right border
            }}
          >
            {showOTP || showRecovery ? (
              <Grid
                sx={{
                  pr: showOnSuccessRecovery ? 4 : 6,
                  pl: showOnSuccessRecovery ? 4 : 6,
                  display: "grid",
                  gap: 3,
                }}
              >
                <Grid container spacing={2}>
                  <Grid item md={3}>
                    {showRecovery && (
                      <Box
                        sx={{
                          width: 38,
                          height: 38,
                          border: "1px solid var(--primary-color)",
                          borderRadius: "50%",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <Box
                          sx={{
                            width: 22,
                            height: 22,
                            bgcolor: "var(--primary-color)",
                            borderRadius: "50%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <DoneRounded sx={{ color: "#fff", fontSize: 15 }} />
                        </Box>
                      </Box>
                    )}
                  </Grid>
                  <Grid item md={9}>
                    <Typography className="label">{t("Langkah")} 1</Typography>
                    <Box>
                      <Typography
                        sx={{ color: showOTP ? "var(--primary-color)" : "" }}
                        className="label"
                      >
                        {t("OTPVerification")}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
                <Grid container spacing={2}>
                  <Grid item md={3}>
                    {(showSuccessRecovery || showOnSuccessRecovery) && (
                      <Box
                        sx={{
                          width: 38,
                          height: 38,
                          border: "1px solid var(--primary-color)",
                          borderRadius: "50%",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <Box
                          sx={{
                            width: 22,
                            height: 22,
                            bgcolor: "var(--primary-color)",
                            borderRadius: "50%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <DoneRounded sx={{ color: "#fff", fontSize: 15 }} />
                        </Box>
                      </Box>
                    )}
                  </Grid>
                  <Grid item md={9}>
                    <Typography className="label">{t("Langkah")} 2</Typography>
                    <Box>
                      <Typography
                        sx={{
                          color: showRecovery ? "var(--primary-color)" : "",
                        }}
                        className="label"
                      >
                        {t("resetPasswordButton")}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Grid>
            ) : (
              <img
                src="/bg-feedback.jpg"
                alt="loginPageBuilding"
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                  objectPosition: "center",
                  display: "block",
                }}
              />
            )}
          </Grid>
          <Grid item xs={12} sm={8} md={8} sx={{ p: 3 }}>
            {
              // showRegistrationOptions ? (
              //   <RegistrationOptions />
              // ) :
              showForgotPassword ? (
                <ForgotPassword
                  onClose={() => setShowForgotPassword(false)}
                  onOTP={() => {
                    handlePasswordOTPSwitch();
                  }}
                />
              ) : showOTP ? (
                <Otp
                  onClose={() => setShowOTP(false)}
                  onPassOTP={() => {
                    handleOTPPass();
                  }}
                  onBackToForgotPassword={() => {
                    handleBackToForgotPassword();
                  }}
                />
              ) : showRecovery ? (
                <Recovery
                  onClose={() => setShowRecovery(false)}
                  onRecovery={() => setShowSuccessRecovery(true)}
                  onSuccessRecovery={() => setShowOnSuccessRecovery(true)}
                />
              ) : (
                <Box
                  sx={{
                    display: "grid",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "flex-end",
                      mb: 1,
                    }}
                  >
                    <IconButton onClick={onClose}>
                      <GridCloseIcon sx={{ color: "var(--secondary-color)" }} />
                    </IconButton>
                  </Box>

                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      mb: 3,
                      gap: 1,
                      flexWrap: "wrap",
                    }}
                  >
                    <Box sx={{ width: { xs: "100px", sm: "146px" } }}>
                      <img
                        src="/jata-negara.jpg"
                        alt="Logo"
                        style={{
                          height: "80px", // Default height
                          marginRight: "10px",
                          width: "100%",
                          objectFit: "contain",
                        }}
                        className="responsive-logo"
                      />
                    </Box>
                    <Box sx={{ width: { xs: "100px", sm: "146px" } }}>
                      <img
                        src="/logo.png"
                        alt="Logo 2"
                        style={{
                          height: "80px", // Default height
                          width: "100%",
                          objectFit: "contain",
                        }}
                        className="responsive-logo"
                      />
                    </Box>
                  </Box>
                  <Typography
                    variant="h6"
                    component="h1"
                    sx={{
                      fontFamily: "Poppins, sans-serif",
                      fontSize: "16px",
                      fontWeight: 500,
                      lineHeight: "24px",
                      textAlign: "center",
                      color: "#55556DA1",
                    }}
                  >
                    {t("titleLogin")}
                  </Typography>

                  <Typography
                    variant="h6"
                    component="h1"
                    sx={{
                      fontFamily: "Poppins, sans-serif",
                      fontSize: "16px",
                      fontWeight: 500,
                      lineHeight: "24px",
                      textAlign: "center",
                      color: "#55556DA1",
                      mb: 5,
                    }}
                  >
                    {t("jppm")}
                  </Typography>

                  <Box
                    sx={{
                      pl: { xs: 2, sm: 8, md: 16 },
                      pr: { xs: 2, sm: 8, md: 16 },
                    }}
                    component="form"
                    noValidate
                    onSubmit={handleSubmit(onSubmit)}
                  >
                    <Typography
                      variant="body1"
                      sx={{
                        mb: 0,
                        fontFamily: "Poppins, sans-serif",
                        fontSize: "14px",
                        fontWeight: 400,
                        lineHeight: "21px",
                        textAlign: "left",
                        color: "#55556DA1",
                      }}
                    >
                      {t("idNumberPlaceholder")}
                    </Typography>
                    <TextField
                      margin="none"
                      required
                      fullWidth
                      id="identificationNo"
                      placeholder={t("myKadPlaceholder")}
                      error={!!errors.identificationNo}
                      helperText={errors.identificationNo?.message as string}
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          backgroundColor: "white",
                          borderRadius: "10px",
                          "& fieldset": {
                            borderColor: "rgba(0, 0, 0, 0.23)",
                            borderRadius: "10px",
                          },
                          "&:hover fieldset": {
                            borderColor: "rgba(0, 0, 0, 0.87)",
                          },
                          "&.Mui-focused fieldset": {
                            borderColor: "#147C7C",
                          },
                        },
                        "& .MuiInputBase-input": {
                          fontFamily: "Poppins, sans-serif",
                          padding: "8px 14px",
                        },
                        "& .MuiFormHelperText-root": {
                          fontFamily: "Poppins, sans-serif",
                          fontSize: "12px",
                          marginTop: "4px",
                          color: "#d32f2f",
                          backgroundColor: "transparent",
                        },
                      }}
                      variant="outlined"
                      {...control.register("identificationNo", {
                        required: t("idNumberRequired"),
                      })}
                    />
                    <Typography
                      variant="body1"
                      sx={{
                        mb: 0,
                        fontFamily: "Poppins, sans-serif",
                        fontSize: "14px",
                        fontWeight: 400,
                        lineHeight: "21px",
                        textAlign: "left",
                        mt: 2,
                        color: "#55556DA1",
                      }}
                    >
                      {t("password")}
                    </Typography>
                    <TextField
                      margin="none"
                      required
                      fullWidth
                      type={showPassword ? "text" : "password"}
                      placeholder={t("password")}
                      id="password"
                      error={!!errors.password}
                      helperText={errors.password?.message as string}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              aria-label="toggle password visibility"
                              onClick={handleClickShowPassword}
                              edge="end"
                            >
                              {showPassword ? (
                                <VisibilityOffIcon />
                              ) : (
                                <VisibilityIcon />
                              )}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          backgroundColor: "white",
                          borderRadius: "10px",
                          "& fieldset": {
                            borderColor: "rgba(0, 0, 0, 0.23)",
                            borderRadius: "10px",
                          },
                          "&:hover fieldset": {
                            borderColor: "rgba(0, 0, 0, 0.87)",
                          },
                          "&.Mui-focused fieldset": {
                            borderColor: "#147C7C",
                          },
                        },
                        "& .MuiInputBase-input": {
                          fontFamily: "Poppins, sans-serif",
                          padding: "8px 14px",
                        },
                        "& .MuiFormHelperText-root": {
                          fontFamily: "Poppins, sans-serif",
                          fontSize: "12px",
                          marginTop: "4px",
                          color: "#d32f2f",
                          backgroundColor: "transparent",
                        },
                      }}
                      variant="outlined"
                      {...register("password", {
                        required: t("passwordRequired"),
                      })}
                    />
                    <Typography
                      className="label"
                      align="right"
                      sx={{
                        mt: 1,
                        color: "#55556DA1",
                        textDecoration: "underline",
                        fontFamily: "Poppins, sans-serif",
                        fontSize: "12px !important",
                        cursor: "pointer",
                      }}
                      onClick={() => setShowForgotPassword(true)}
                    >
                      {t("forgotPassword")}
                    </Typography>
                    <ButtonPrimary
                      type="submit"
                      fullWidth
                      variant="contained"
                      disabled={isLoading || isLoadingMyDigitalId}
                      sx={{
                        mt: 4,
                        p: 1,
                        fontWeight: 400,
                        fontSize: "14px",
                      }}
                    >
                      {isLoading ? (
                        <CircularProgress size={24} color="inherit" />
                      ) : (
                        t("login")
                      )}
                    </ButtonPrimary>
                    <ButtonPrimary
                      onClick={navigateToMyDigitalIdFlow}
                      fullWidth
                      variant="contained"
                      disabled={isLoading || isLoadingMyDigitalId}
                      sx={{
                        mt: 1,
                        p: 1,
                        fontWeight: 400,
                        fontSize: "14px",
                        backgroundColor: "white",
                        color: "var(--myDigitalId)",
                        border: "1px solid var(--myDigitalId)",
                        display: "flex",
                        alignItems: "center",
                        gap: 0.5,
                        "&:hover": {
                          transition: "0.4s ease",
                          backgroundColor: "#f0f4ff",
                          borderRadius: "7px",
                          color: "var(--myDigitalId)",
                          border: "1px solid var(--myDigitalId)",
                        },
                      }}
                    >
                      {isLoadingMyDigitalId ? (
                        <CircularProgress size={24} color="inherit" />
                      ) : (
                        <>
                          <img
                            src={MYDID_icon}
                            alt="MyDigitalID"
                            style={{
                              width: "35px",
                              height: "35px",
                            }}
                          />
                          {t("loginMyDigitalId")}
                        </>
                      )}
                    </ButtonPrimary>
                    <Typography
                      className="label"
                      align="center"
                      sx={{
                        mt: 1,
                        color: "#55556DA1",
                        fontFamily: "Poppins, sans-serif",
                        fontSize: "12px !important",
                      }}
                    >
                      {t("byContinuingYouAgreeTo")}{" "}
                      <Link
                        to="/terma-penggunaan"
                        style={{
                          color: "#55556DA1",
                          fontFamily: "Poppins, sans-serif",
                          fontSize: "12px",
                          textDecoration: "underline",
                        }}
                      >
                        {t("termsOfUse")}
                      </Link>{" "}
                      {t("and")}{" "}
                      <Link
                        to="/login"
                        style={{
                          color: "#55556DA1",
                          fontFamily: "Poppins, sans-serif",
                          fontSize: "12px",
                          textDecoration: "underline",
                        }}
                      >
                        {t("privacyPolicy")}.
                      </Link>
                    </Typography>
                  </Box>

                  <ButtonText
                    className="label"
                    sx={{
                      mt: { sm: 3, md: 3, lg: 3, xl: 8 },
                      mb: 2,
                      ml: 1.5,
                      color: "#666666",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                    // onClick={() => setShowRegistrationOptions(true)}
                    onClick={() => navigate("/register")}
                  >
                    {t("registerNewUser")} <ChevronRightRounded />
                  </ButtonText>
                </Box>
              )
            }
          </Grid>
        </Grid>
      )}
      {currentComponent === "commingsoon" && <ComingSoon onBack={handleBack} />}

      {/* <Fab
          sx={{
            position: "fixed",
            bottom: { xs: 16, sm: 24 },
            left: { xs: 16, sm: 24 },
            width: { xs: 60, sm: 82 },
            height: { xs: 60, sm: 82 },
            borderRadius: "50%",
            backgroundColor: "transparent",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
            boxShadow: "none",
            overflow: "hidden",
          }}
        >
          <img
            src="/Group.png"
            alt="Group"
            style={{
              width: "100%",
              height: "100%",
              borderRadius: "50%",
              objectFit: "cover",
            }}
          />
        </Fab> */}

      {/* Language Menu */}
      <Menu
        anchorEl={languageAnchorEl}
        open={Boolean(languageAnchorEl)}
        onClose={handleLanguageMenuClose}
      >
        <MenuItem onClick={() => changeLanguage("en")}>English</MenuItem>
        <MenuItem onClick={() => changeLanguage("my")}>Bahasa Melayu</MenuItem>
      </Menu>

      {/* Mobile Menu */}
      <Menu
        anchorEl={mobileMenuAnchorEl}
        open={Boolean(mobileMenuAnchorEl)}
        onClose={handleMobileMenuClose}
      >
        {menuItems.map((item) => (
          <MenuItem
            key={item.label}
            component={RouterLink}
            to={`/${item.path}`}
            onClick={handleMobileMenuClose}
          >
            {item.label}
          </MenuItem>
        ))}
      </Menu>

      <Dialog
        open={dialogAlertSaveOpen}
        onClose={() => setDialogAlertSaveOpen(false)}
        PaperProps={{
          style: {
            borderRadius: "8px",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogContent sx={{ p: 4 }}>
          <Typography
            variant="h6"
            component="h2"
            sx={{
              fontSize: 14,
              textAlign: "center",
              fontFamily: "Poppins, sans-serif",
            }}
          >
            {t("otpSentMessage")}
          </Typography>

          <Box
            sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
          >
            <ButtonPrimary
              onClick={() => {
                setShowOTP(true);
                setDialogAlertSaveOpen(false);
                // navigate("/otp");
              }}
              sx={{
                backgroundColor: "#147C7C",
                borderRadius: "18px",
                fontFamily: "Poppins, sans-serif",
              }}
            >
              {t("back")}
            </ButtonPrimary>
          </Box>
        </DialogContent>
      </Dialog>

      <FeedbackDialog
        open={openFeedbackDialog}
        onClose={() => {
          setOpenFeedbackDialog(false);
          localStorage.removeItem("tmp-identificationNo");
        }}
      />
    </Box>
  );
};
