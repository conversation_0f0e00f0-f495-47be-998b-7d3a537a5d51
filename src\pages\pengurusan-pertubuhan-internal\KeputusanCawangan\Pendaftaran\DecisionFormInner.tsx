import {
  ButtonOutline,
  ButtonPrimary,
  SelectFieldControllerFormik,
  TextFieldControllerFormik,
} from "@/components";
import Box from "@mui/material/Box/Box";
import Grid from "@mui/material/Grid/Grid";
import Typography from "@mui/material/Typography/Typography";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { DecisionBranchRegistrationPayload } from "./KeputusanCawanganPendaftaran";
import { DecisionOptionsCode } from "@/helpers";
import { useState } from "react";
import { Form, useFormikContext } from "formik";
import ConfirmationDialog from "@/components/dialog/confirm";
import NewAlertDialog from "@/components/dialog/newAlert";
import { useTheme } from "@mui/material";

type DecisionFormInnerProps<Payload extends DecisionBranchRegistrationPayload> =
  {
    disabled?: boolean;
  };

export const DecisionFormInner = <
  Payload extends DecisionBranchRegistrationPayload = DecisionBranchRegistrationPayload
>({
  disabled = false,
}: DecisionFormInnerProps<Payload>) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const { values, submitForm, isSubmitting, setFieldValue, isValid } =
    useFormikContext<Payload>();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitSuccessful, setSubmitSuccessfull] = useState(false);

  const { applicationStatusCode } = values;
  const isFormDisabled = disabled || isSubmitting;
  const decisionOptions = DecisionOptionsCode(t);
  const labelStyle = {
    fontSize: "16px",
    color: "#666666",
    fontWeight: "400 !important",
  };
  const error = theme.palette.error.main;

  const handleConfirm = () => {
    try {
      const newWords = values?.note?.trim();
      setFieldValue("note", newWords);
      submitForm();
      setSubmitSuccessfull(true);
    } catch {
      setSubmitSuccessfull(false);
    }
  };

  return (
    <>
      <Form>
        <Box
          sx={{
            pl: 2,
            p: 3,
            mt: 1,
            borderRadius: "10px",
            border: "0.5px solid #dfdfdf",
          }}
        >
          <Box
            sx={{
              mb: 3,
            }}
          >
            <Typography color={"primary"}>{t("keputusan")}</Typography>
          </Box>
          <Grid container>
            <Grid item xs={12} sm={4}>
              <Typography className="label">{t("statusPermohonan")}</Typography>
            </Grid>

            <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
              <SelectFieldControllerFormik
                name="applicationStatusCode"
                options={decisionOptions}
                disabled={isFormDisabled}
                sx={{
                  background: isFormDisabled ? "rgba(218, 218, 218, 0.5)" : "",
                }}
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <Typography className="label" sx={labelStyle}>
                {t("remarks")} <span style={{ color: error }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextFieldControllerFormik
                name="note"
                multiline
                required={applicationStatusCode === 36}
                sx={{
                  minHeight: "92px",
                  background: isFormDisabled ? "rgba(218, 218, 218, 0.5)" : "",
                }}
                sxInput={{
                  minHeight: "92px",
                }}
                disabled={isFormDisabled}
              />
            </Grid>
          </Grid>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            marginTop: "34px",
            gap: "10px",
          }}
        >
          <ButtonOutline
            onClick={() =>
              navigate(
                "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk"
              )
            }
          >
            {t("back")}
          </ButtonOutline>

          <ButtonPrimary
            disabled={isFormDisabled || !isValid}
            onClick={() => {
              setIsDialogOpen(true);
            }}
          >
            {t("hantar")}
          </ButtonPrimary>
        </Box>
      </Form>
      <ConfirmationDialog
        open={isSubmitting || isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        title="Keputusan Permohonan"
        message="Apakah anda pasti dengan keputusan permohonan ini?"
        decisionLabel={
          decisionOptions?.find((item) => item.value === applicationStatusCode)
            ?.label
        }
        isMutation={isSubmitting}
        onConfirm={handleConfirm}
        onCancel={() => setIsDialogOpen(false)}
      />
      <NewAlertDialog
        open={!isSubmitting && isSubmitSuccessful}
        onClose={() => {
          setSubmitSuccessfull(false);
          setIsDialogOpen(false);
        }}
        message={t("applicationSuccessfulySubmited")}
      />
    </>
  );
};
