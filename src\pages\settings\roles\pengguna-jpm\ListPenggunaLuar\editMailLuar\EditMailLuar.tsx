import {
  Box,
  FormControlLabel,
  FormHelperText,
  Grid,
  Radio,
  RadioGroup,
  styled,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { Trans, useTranslation } from "react-i18next";
import { ButtonPrimary } from "../../../../../../components/button";
import { useEffect, useState } from "react";
import ButtonPrevious from "../../../../../../components/button/ButtonPrevious";
import {
  IdTypes,
  ListGelaran,
  ListUserStatus,
  NEW_PermissionNames,
  pageAccessEnum,
} from "../../../../../../helpers/enums";
import Input from "../../../../../../components/input/Input";
import { API_URL } from "../../../../../../api";
import { useNavigate, useSearchParams } from "react-router-dom";
import AuthHelper from "@/helpers/authHelper";

interface FormValues {
  userId?: string | number | null;
  titleCode?: string | null;
  name?: string | null;
  email?: string | null;
  idType?: string | null;
  identificationNo?: string | null;
  citizenshipTitle?: number | null;
  address?: string | null;
  state?: string | null;
  district?: string | null;
  city?: string | null;
  postcode?: string | null;
  telephoneNoHome?: string | null;
  telephoneNoMobile?: string | null;
  status?: number | null;
}

interface list {
  value: any;
  label: any;
}

interface UserData {
  userId?: string | number | null;
  title?: string | null;
  name?: string | null;
  email?: string | null;
  idType?: string | null;
  identificationNo?: string | null;
  citizenship?: boolean | null;
  address?: string | null;
  state?: string | null;
  district?: string | null;
  city?: string | null;
  postcode?: string | null;
  telephoneNoHome?: string | null;
  telephoneNoMobile?: string | null;
  status?: number | null;
}

function EditMailLuar() {
  const hasUpdatePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.SELENGGARA.children.PENGURUSAN_PENGGUNA.children
      .SENARAI_PENGGUNA_LUAR.label,
    pageAccessEnum.Update
  );
  const [searchParams] = useSearchParams();
  const userId = searchParams.get("id");
  const navigate = useNavigate();
  const { t } = useTranslation();

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const [formValues, setFormValues] = useState<FormValues>({
    titleCode: null,
    name: null,
    email: null,
    idType: null,
    identificationNo: null,
    citizenshipTitle: null,
    address: null,
    state: null,
    district: null,
    city: null,
    postcode: null,
    telephoneNoHome: null,
    telephoneNoMobile: null,
  });
  const [errors, setErrors] = useState<{ [key in keyof FormValues]?: string }>(
    {}
  );

  const [idTypeTranslatedList, setIdTypeTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  const [titleTranslatedList, setTitleTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    const newIdTypeList = IdTypes.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setIdTypeTranslatedList(newIdTypeList);

    const newTitleList = ListGelaran.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setTitleTranslatedList(newTitleList);
  }, [t]);

  const [userData, setUserData] = useState<UserData>();
  const [userStatus, setUserStatus] = useState<string>();
  const [loading, setLoading] = useState(true);

  const CustomRadio = styled(Radio)(({ theme }) => ({
    "& .MuiSvgIcon-root": {
      borderRadius: "2px",
      width: 16,
      height: 16,
    },
    // Styles for unchecked state
    "& .MuiSvgIcon-root:first-of-type": {
      border: "2px solid #979797",
      background: "white",
    },
    // Styles for checked state
    "& .MuiSvgIcon-root:last-child": {
      border: "2px solid #1976d2",
      background: "#1976d2",
      "&:before": {
        display: "block",
        width: 16,
        height: 16,
        backgroundImage: "radial-gradient(#fff,#fff 28%,transparent 32%)",
        content: '""',
      },
    },
  }));

  const CheckboxIcon = () => (
    <svg width="16" height="16" viewBox="0 0 16 16">
      <rect
        width="16"
        height="16"
        fill="none"
        stroke="#979797"
        strokeWidth="1"
        rx="3"
        ry="3"
      />

      <rect x="2" y="2" width="12" height="12" fill="#979797" />
    </svg>
  );

  const CheckedIcon = () => (
    <svg width="16" height="16" viewBox="0 0 16 16">
      <rect
        width="16"
        height="16"
        fill="none"
        stroke="#979797"
        strokeWidth="1"
        rx="3"
        ry="3"
      />
      <rect x="2" y="2" width="12" height="12" fill="currentColor" />
    </svg>
  );

  function EditUser() {
    fetch(`${API_URL}/user/admin/updateExternalUser`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        portal: localStorage.getItem("portal") || "",
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      body: JSON.stringify(formValues),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Erorr when fetching users");
        }
        return response.json();
      })
      .then((data) => {
        if (data.status === "ERROR") {
          throw new Error("Erorr when fetching users");
        }

        setFormValues({
          userId: null,
          email: null,
        });
        setErrors({});
      })
      .catch((error) => {
        console.error("Error:", error);
      });
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newErrors: { [key in keyof FormValues]?: string } = {};

    if (!formValues.email) {
      newErrors.email = t("requiredField");
    }

    if (Object.keys(newErrors).length === 0) {
      EditUser();
    } else {
      setErrors(newErrors);
    }
  };

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setFormValues({
      ...formValues,
      [name!]: value as string,
    });
  };

  useEffect(() => {
    if (userId) {
      const fetchData = async () => {
        try {
          const response = await fetch(
            `${API_URL}/user/admin/external/${userId}`,
            {
              method: "GET",
              headers: {
                portal: localStorage.getItem("portal") || "",
                Authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
                "Content-Type": "application/json",
              },
            }
          );
          if (!response.ok) {
            throw new Error("Failed to fetch data");
          }
          const result = await response.json();
          const data = result?.data;
          const matchingStatus = ListUserStatus.find(
            (item) => item.value === data.status
          );
          setUserStatus(
            ListUserStatus.find((item) => item.value === data.status)?.label ||
              ""
          );
          console.log("data: ", data);
          setFormValues({
            titleCode: data?.titleCode,
            name: data?.name,
            email: data?.email,
            idType: String(data?.identificationType ?? ""),
            identificationNo: data?.identificationNo,
            citizenshipTitle: data?.citizenshipTitle,
            address: data?.address,
            state: data?.state,
            district: data?.district,
            city: data?.city,
            postcode: data?.postcode,
            telephoneNoHome: data?.telephoneNoHome,
            telephoneNoMobile: data?.telephoneNoMobile,
            status: data?.status,
          });
          setUserData(data);
        } catch (error) {
          console.error("Error fetching data:", error);
        } finally {
          setLoading(false);
        }
      };
      fetchData();
    }
  }, [userId]);

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          component="form"
          onSubmit={handleSubmit}
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography sx={{ mb: 4 }} className={"title"}>
            {t("externalUserupdate")}
          </Typography>
          <Box sx={{ mb: 6 }}>
            <Input
              value={formValues.titleCode ? formValues.titleCode : ""}
              name="title"
              disabled
              onChange={handleChange}
              label={t("gelaran")}
              options={titleTranslatedList}
              type="select"
              error={!!errors.titleCode}
              helperText={errors.titleCode}
            />

            <Input
              value={formValues.name ? formValues.name : ""}
              name="name"
              disabled
              onChange={handleChange}
              label={t("fullName")}
              error={!!errors.name}
              helperText={errors.name}
            />

            <Grid container spacing={2} sx={{ pt: 2, pb: 2 }}>
              <Grid
                item
                xs={12}
                sm={4}
                sx={{ display: "flex", alignItems: "center" }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    color: "#666666",
                    fontWeight: "400 !important",
                    fontSize: "14px",
                  }}
                >
                  {t("citizenship")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <RadioGroup
                  value={formValues.citizenshipTitle}
                  // onChange={handleChange}
                  name={"citizenship"}
                  row
                >
                  {formValues?.citizenshipTitle === 1 ? (
                    <>
                      <FormControlLabel
                        value={1}
                        className="label"
                        control={<Radio />}
                        label={t("citizen")}
                      />
                      <FormControlLabel
                        value={2}
                        className="label"
                        control={<Radio />}
                        disabled
                        label={t("nonCitizen")}
                      />
                    </>
                  ) : formValues?.citizenshipTitle === 2 ? (
                    <>
                      <FormControlLabel
                        value={1}
                        className="label"
                        control={<Radio />}
                        disabled
                        label={t("citizen")}
                      />
                      <FormControlLabel
                        value={2}
                        className="label"
                        control={<Radio />}
                        label={t("nonCitizen")}
                      />
                    </>
                  ) : (
                    <>
                      <FormControlLabel
                        value={1}
                        className="label"
                        control={<Radio />}
                        disabled
                        label={t("citizen")}
                      />
                      <FormControlLabel
                        value={2}
                        className="label"
                        control={<Radio />}
                        disabled
                        label={t("nonCitizen")}
                      />
                    </>
                  )}
                </RadioGroup>
                {errors.citizenshipTitle && (
                  <FormHelperText error>
                    {errors.citizenshipTitle}
                  </FormHelperText>
                )}
              </Grid>
            </Grid>

            <Input
              value={formValues.idType ? formValues.idType : ""}
              name="idType"
              onChange={handleChange}
              label={t("idType")}
              type="select"
              options={idTypeTranslatedList}
              error={!!errors.idType}
              helperText={errors.idType}
              disabled
            />

            <Input
              value={
                formValues?.identificationNo ? formValues.identificationNo : ""
              }
              name="identificationNo"
              onChange={handleChange}
              label={t("identificationNumber")}
              error={!!errors.identificationNo}
              helperText={errors.identificationNo}
              disabled
            />
          </Box>
          {/* SECTION 2 */}
          <Box sx={{ mb: 6 }}>
            <Input
              value={formValues.email ? formValues.email : ""}
              name="email"
              onChange={handleChange}
              label={t("emailWithoutDash")}
              type="email"
              required
              error={!!errors.email}
              helperText={errors.email}
            />
            <Grid container spacing={2} sx={{ pt: 2, pb: 2 }}>
              <Grid
                item
                xs={12}
                sm={4}
                sx={{ display: "flex", alignItems: "center" }}
              ></Grid>
              <Grid sx={{ display: "grid", gap: 1 }} item xs={12} sm={8}>
                {/* <Typography className="label">
                  <span style={{ color: "red" }}>{"\u002a"}</span>{" "}
                  {t("userCannotActivateEmail")}
                </Typography> */}
                <Typography className="label">
                  <span style={{ color: "red" }}>{"\u002a"}</span>{" "}
                  <Trans
                    i18nKey="userCannotActivateEmail"
                    components={{ b: <strong /> }}
                  />
                </Typography>
                <Typography className="label">
                  <span style={{ color: "red" }}>{"\u002a"}</span>{" "}
                  <Trans
                    i18nKey="clickUpdateToResend"
                    components={{ b: <strong /> }}
                  />
                </Typography>
              </Grid>
            </Grid>
            <Grid container spacing={2} sx={{ pt: 2, pb: 2 }}>
              <Grid
                item
                xs={12}
                sm={4}
                sx={{ display: "flex", alignItems: "center" }}
              >
                {" "}
                <Typography className="label">{t("kandunganEMEL")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <Box
                  sx={{
                    display: "grid",
                    border: "1px solid rgba(0, 0, 0, 0.12)",
                    borderRadius: "14px",
                    p: 2,
                    gap: 3,
                  }}
                >
                  <Box sx={{ display: "grid", gap: 1 }}>
                    <Typography
                      sx={{ fontWeight: "bold!important" }}
                      className="label"
                    >
                      {t("pendaftaranAkauneROSES")}
                    </Typography>
                    <Box>
                      <Typography
                        // sx={{ fontWeight: "bold!important" }}
                        className="label"
                      >
                        Jenis Pengenalan Diri:{" "}
                        {t(
                          `${
                            idTypeTranslatedList.find(
                              (item) => item.value === formValues?.idType
                            )?.label || ""
                          }`
                        )}
                      </Typography>
                      <Typography
                        // sx={{ fontWeight: "bold!important" }}
                        className="label"
                      >
                        No Pengenalan Diri: {formValues.identificationNo}
                      </Typography>
                      <Typography
                        // sx={{ fontWeight: "bold!important" }}
                        className="label"
                      >
                        Kata Laluan Sementara: password123
                      </Typography>
                    </Box>
                  </Box>

                  <Box sx={{ display: "grid", gap: 1 }}>
                    <Typography
                      sx={{ fontWeight: "bold!important" }}
                      className="label"
                    >
                      {t("pengaktifanAkauneROSES")}
                    </Typography>
                    <Typography className="label">
                      {t("pendaftaranAkauneROSESDesc")}
                    </Typography>
                  </Box>
                  <Box sx={{ pt: 2, pb: 2 }}>
                    {userStatus ? (
                      <Box
                        sx={{
                          pt: 2,
                          pb: 2,
                          bgcolor: "var(--primary-color)",
                          width: 160,
                          borderRadius: "8px",
                          color: "white",
                          justifyContent: "center",
                          alignItems: "center",
                          display: "flex",
                        }}
                      >
                        <Typography>
                          {userStatus ? t(userStatus) : t("unknown")}
                        </Typography>
                      </Box>
                    ) : null}
                  </Box>
                  <Box>
                    <Typography className="label">
                      {t("thankYouForRegistering")}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography className="label">
                      {t("thisEmailIsComputerGenerated")}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>

          {/* SECTION 3 */}
          <Box sx={{ mb: 6 }}></Box>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonPrevious
              variant="outlined"
              sx={{
                bgcolor: "white",
                "&:hover": { bgcolor: "white" },
                width: isMobile ? "100%" : "auto",
              }}
              onClick={() => navigate(-1)}
            >
              {t("Kembali")}
            </ButtonPrevious>
            {hasUpdatePermission ? (
              <ButtonPrimary
                variant="contained"
                type="submit"
                sx={{
                  width: isMobile ? "100%" : "auto",
                  boxShadow: "none",
                }}
              >
                {t("update")}
              </ButtonPrimary>
            ) : null}
          </Grid>
        </Box>
      </Box>
    </Box>
  );
}

export default EditMailLuar;
