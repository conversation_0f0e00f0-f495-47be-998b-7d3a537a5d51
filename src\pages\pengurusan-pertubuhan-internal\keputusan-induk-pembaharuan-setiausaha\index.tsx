/* eslint-disable react-refresh/only-export-components */
import { useState, useContext, createContext, useEffect, useMemo } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { TFunction } from "i18next";
import { useForm, FieldValues } from "react-hook-form";
import { useTranslation } from "react-i18next";
import {
  DecisionOptionsCode,
  useQuery,
  DocumentUploadType,
  filterEmptyValuesOnObject,
  useMutation,
  NEW_PermissionNames,
  pageAccessEnum,
} from "@/helpers";

import {
  Box,
  Grid,
  Typography,
  Fade,
  useMediaQuery,
  Theme,
} from "@mui/material";
import {
  DialogConfirmation,
  ButtonPrimary,
  Label,
  SelectFieldController,
  TextFieldController,
  CustomSkeleton,
  FormFieldRow,
  DisabledTextField,
  ButtonOutline,
} from "@/components";
import MaklumatSetiausahaSection from "./views/MaklumatSetiausahaSection";
import MaklumatMesyuaratSection from "./views/MaklumatMesyuaratSection";
import KeputusanPermohonanSection from "./views/KeputusanPermohonanSection";
import MaklumbalasTable from "./views/MaklumbalasTable";
import AccordionComp from "../View/Accordion";
import DialogQueries from "./views/DialogQueries";

import {
  IApprovalSecretaryDetail,
  IMeetingDetail,
  ISocietyDetail,
  IApiResponse,
  IROList,
} from "@/types";
import PerlembagaanSection from "./views/PerlembagaanSection";
import ButtonPrevious from "@/components/button/ButtonPrevious";
import AuthHelper from "@/helpers/authHelper";

interface KeputusanIndukPembaharuanSetiausahaContextProps {
  secretaryDetailData: IApprovalSecretaryDetail | null;
  meetingDetailData: IMeetingDetail | null;
  societyDetailData: ISocietyDetail | null;
  meetingFilePreview: string;
  roList: IROList[];
}

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

const KeputusanIndukPembahruanSetiuasahaContext = createContext<
  KeputusanIndukPembaharuanSetiausahaContextProps | undefined
>(undefined);

export const useKeputusanIndukPembaharuanSetiausahaContext =
  (): KeputusanIndukPembaharuanSetiausahaContextProps => {
    const context = useContext(KeputusanIndukPembahruanSetiuasahaContext);

    if (!context) {
      throw new Error(
        "useKeputusanIndukPembahruanSetiuasahaContext must be used within a KeputusanIndukPembaharuanSetiusahaProvider"
      );
    }
    return context;
  };

type ReadStatusType = {
  [key: number]: boolean;
};

function KeputusanIndukPembaharuanSetiausaha() {
  const location = useLocation();
  const hasUpdatePermission = location.pathname.includes("kuiri")
    ? AuthHelper.hasPageAccess(
        NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
          .KUIRI.children.PEMBAHARUAN_SETIAUSAHA_KUIRI.label,
        pageAccessEnum.Update
      )
    : AuthHelper.hasPageAccess(
        NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
          .KEPUTUSAN_INDUK.children.PEMBAHARUAN_SETIAUSAHA.label,
        pageAccessEnum.Update
      );

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const { id } = useParams();
  const { type } = location.state || {};
  const isViewOnly = type;
  const { t } = useTranslation();

  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isOpenDialogQueries, setIsOpenDialogQueries] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [openMaklumbalasTable, setOpenMaklumbalasTable] = useState(false);

  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);

  const [readStatus, setReadStatus] = useState<ReadStatusType>({});
  const [meetingFilePreview, setMeetingFilePreview] = useState("");

  const { data: secretaryRes, isLoading: isLoadingSecretaryDetail } = useQuery<
    IApiResponse<IApprovalSecretaryDetail>
  >({
    url: `society/new/secretaries/${id}`,
    onSuccess: (res) => {
      const data = res?.data?.data;

      if (data) {
        const societyId = data?.societyId;
        const meetingId = data?.meetingId;

        fetchSociety();
        fetchMeeting();
        fetchRoList({
          filters: [
            {
              field: "societyId",
              operator: "eq",
              value: societyId,
            },
          ],
        });

        getMeetingDocumentFile({
          filters: [
            {
              field: "societyId",
              operator: "eq",
              value: societyId,
            },
            {
              field: "meetingId",
              operator: "eq",
              value: meetingId,
            },
            {
              field: "type",
              operator: "eq",
              value: DocumentUploadType.MEETING,
            },
          ],
        });
      }
    },
  });

  const secretaryDetailData = secretaryRes?.data?.data ?? null;
  const userRo = secretaryDetailData?.userRo ?? false;
  const queriesList = secretaryDetailData?.queries ?? [];
  const lastQuery = queriesList.at(-1) ?? null;

  const {
    data: societyRes,
    refetch: fetchSociety,
    isLoading: isLoadingSociety,
  } = useQuery<IApiResponse<ISocietyDetail>>({
    url: `society/${secretaryDetailData?.societyId}`,
    autoFetch: false,
  });

  const { data: meetingRes, refetch: fetchMeeting } = useQuery<
    IApiResponse<IMeetingDetail>
  >({
    url: `society/meeting/${secretaryDetailData?.meetingId}`,
    autoFetch: false,
  });

  const { refetch: getMeetingDocumentFile } = useQuery({
    url: "document/documentByParam",
    autoFetch: false,
    onSuccess: ({ data }) => {
      const meetingDocuments = data?.data;

      if (Array.isArray(meetingDocuments) && meetingDocuments.length > 0) {
        const [firstDocument] = meetingDocuments;
        const meetingUrl = firstDocument?.url;

        if (meetingUrl) {
          setMeetingFilePreview(meetingUrl);
        } else {
          console.warn("Meeting URL not found");
        }
      } else {
        console.warn("No meeting documents available.");
      }
    },
  });

  const { data: roListData, refetch: fetchRoList } = useQuery<
    IApiResponse<IROList[]>
  >({
    url: "society/user/getRoList",
    autoFetch: false,
  });

  const meetingDetailData = meetingRes?.data?.data ?? null;
  const societyDetailData = societyRes?.data?.data ?? null;
  const roList = roListData?.data?.data ?? [];

  const { fetch: createApproval, isLoading } = useMutation({
    url: "society/roDecision/updateApprovalStatus",
    method: "patch",
    onSuccess: () => setIsSuccess(true),
  });
  const { fetch: updateRO, isLoading: isUpdatingRO } = useMutation({
    url: "society/roDecision/updateRo",
    method: "patch",
  });
  const { control, handleSubmit, getValues, watch, setValue } =
    useForm<FieldValues>({
      defaultValues: {
        principalSecretaryId: id,
        societyId: societyDetailData?.id,
        societyNo: societyDetailData?.societyNo,
        roApprovalType: "SOCIETY_NEW_SECRETARY",
        applicationStatusCode: "",
        note: "",
      },
    });

  const applicationStatusCode = watch("applicationStatusCode");
  const requiredNote =
    (userRo && applicationStatusCode === 4) || applicationStatusCode === 36; // decision tolak for RO or decision Kuiri

  const methodsRoAction = useForm<FieldValues>({
    defaultValues: {
      principalSecretaryId: id,
      roId: "",
      noteRo: "",
      roApprovalType: "SOCIETY_NEW_SECRETARY",
    },
  });

  const {
    handleSubmit: handleSubmitRoAction,
    control: controlRoAction,
    setValue: setValueRoAction,
    getValues: getValuesRoAction,
    watch: watchRoAction,
  } = methodsRoAction;

  useEffect(() => {
    if (secretaryDetailData) {
      setValueRoAction(
        "roId",
        secretaryDetailData?.ro ? Number(secretaryDetailData.ro) : ""
      );
      setValueRoAction("noteRo", secretaryDetailData.noteRo ?? "");
    }
  }, [secretaryDetailData]);

  const getDecisionOptions = (
    t: TFunction<"translation", undefined>,
    userRo: boolean
  ) => {
    let options = DecisionOptionsCode(t);

    if (userRo) {
      options = options
        .map((option) => {
          if (option.value === 3) {
            return { ...option, label: t("DISYORKAN") };
          }
          if (option.value === 4) {
            return { ...option, label: t("TIDAK_DISYORKAN") };
          }
          return option;
        })
        .filter((option) => option.value !== 36);
    }

    return options;
  };

  const decisionOptions = useMemo(
    () => getDecisionOptions(t, userRo),
    [userRo]
  );

  const decisionLabel = useMemo(
    () =>
      decisionOptions.find(
        (item) => item.value === Number(applicationStatusCode)
      )?.label || "",
    [applicationStatusCode, decisionOptions]
  );

  const sectionItems = [
    {
      subTitle: t("secretaryInformation"),
      component: (
        <MaklumatSetiausahaSection
          onClickMaklumbalas={() => setOpenMaklumbalasTable(true)}
        />
      ),
    },
    {
      subTitle: t("maklumatMesyuarat"),
      component: <MaklumatMesyuaratSection />,
    },
    {
      subTitle: t("perlembagaanPertubuhan"),
      component: <PerlembagaanSection />,
    },
    {
      subTitle: t("keputusanPermohonan"),
      component: <KeputusanPermohonanSection />,
    },
  ];

  const handleChangeCurrentExpandSection =
    (item: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);

      if (isExpanded) {
        setReadStatus((prevState) => {
          const updatedStatus = sectionItems.reduce((acc, _, i) => {
            if (i + 1 <= item) {
              acc[i + 1] = true;
            } else {
              acc[i + 1] = !!prevState[i + 1] || false;
            }
            return acc;
          }, {} as Record<number, boolean>);
          return updatedStatus;
        });
      }
    };

  const onSubmit = () => setIsDialogOpen(true);
  const handleDialogClose = () => setIsDialogOpen(false);
  const handleFormSubmit = () => {
    const payload: Record<string, any> = {
      ...getValues(),
    };

    const { note, applicationStatusCode } = payload;

    switch (applicationStatusCode) {
      case 4:
        payload.rejectReason = note;
        delete payload.note;
        break;
      default:
        break;
    }

    const filterPayload = filterEmptyValuesOnObject(payload);

    createApproval(filterPayload);
  };

  useEffect(() => {
    if (!secretaryDetailData) return;

    const { reviews, societyId, societyNo } = secretaryDetailData;

    const lastReview =
      reviews
        ?.filter((review: any) => [3, 4].includes(review.decision))
        ?.at(-1) ?? null;

    setValue("societyId", societyId);
    setValue("societyNo", societyNo);

    if (lastReview) {
      const { decision, note } = lastReview;

      setValue("decisionCode", decision);
      setValue("note", note);
      setIsFormDisabled(true);
    } else {
      setIsFormDisabled;
    }
  }, [secretaryDetailData]);

  if (isLoadingSecretaryDetail || !secretaryDetailData)
    return <CustomSkeleton />;

  const roListOptions =
    roList.map((item: any) => ({
      value: item.id,
      label: item.name,
    })) || [];

  const onSubmitRoAction = (data: FieldValues) => {
    updateRO(data);
  };

  const renderSuccessText = () => {
    switch (decisionLabel) {
      case "Lulus":
        return t("ApplicationApproved");
      case "Tolak":
        return t("ApplicationRejected");
      case "Kuiri":
        return t("ApplicationRequestInquiry");
      default:
        return t("applicationSuccessSubmited");
    }
  };

  return (
    <KeputusanIndukPembahruanSetiuasahaContext.Provider
      value={{
        roList,
        secretaryDetailData,
        meetingDetailData,
        societyDetailData,
        meetingFilePreview,
      }}
    >
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            <Typography fontSize="14px" fontWeight="500 !important">
              {isLoadingSociety ? (
                "Loading..."
              ) : (
                <>
                  {societyDetailData?.societyName} <br />
                  {societyDetailData?.societyNo}
                </>
              )}
            </Typography>
          </Box>
        </Box>
        <Fade in={openMaklumbalasTable} timeout={500} unmountOnExit>
          <Box>
            <MaklumbalasTable onBack={() => setOpenMaklumbalasTable(false)} />
          </Box>
        </Fade>
        <Fade in={!openMaklumbalasTable} timeout={500} unmountOnExit>
          <Box>
            <Box sx={{ mt: 4 }}>
              {sectionItems.map((item, index) => {
                return (
                  <AccordionComp
                    key={index}
                    subTitle={item.subTitle}
                    currentIndex={index + 1}
                    currentExpand={currentExpandSection}
                    readStatus={readStatus}
                    onChangeFunc={handleChangeCurrentExpandSection}
                  >
                    {item.component}
                  </AccordionComp>
                );
              })}
            </Box>
            {!isViewOnly ? (
              <>
                <Box
                  sx={{
                    backgroundColor: "white",
                    p: 3,
                    borderRadius: "15px",
                    mt: 2,
                  }}
                >
                  <Box
                    sx={{
                      pl: 2,
                      p: 3,
                      mt: 1,
                      borderRadius: "10px",
                      border: "0.5px solid #dfdfdf",
                    }}
                  >
                    <Box
                      sx={{
                        mb: 3,
                      }}
                    >
                      <Typography color={"primary"}>{t("kuiri")}</Typography>
                    </Box>
                    <Box
                      sx={{
                        display: "flex",
                        gap: "4px",
                        justifyContent: "flex-end",
                        marginBottom: "27px",
                      }}
                    >
                      <Box
                        sx={{
                          background: "var(--primary-color)",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "center",
                          alignContent: "center",
                          borderRadius: "10px",
                          p: 1,
                        }}
                      >
                        <img
                          width={26}
                          height={26}
                          src="/addkuiri.svg"
                          alt=""
                        />
                      </Box>

                      <ButtonOutline
                        onClick={() => setIsOpenDialogQueries(true)}
                      >
                        {t("historyInquiry")}
                      </ButtonOutline>
                    </Box>

                    <FormFieldRow
                      align="flex-start"
                      label={<Label text={`${t("remarks")} ${t("kuiri")}`} />}
                      value={
                        <DisabledTextField
                          multiline
                          row={3}
                          value={lastQuery?.note ?? ""}
                        />
                      }
                    />
                  </Box>
                </Box>

                <Box
                  sx={{
                    backgroundColor: "white",
                    p: 3,
                    borderRadius: "15px",
                    mt: 2,
                  }}
                >
                  <form onSubmit={handleSubmitRoAction(onSubmitRoAction)}>
                    <Box
                      sx={{
                        p: 3,
                        mb: 3,
                        border: "1px solid #D9D9D9",
                        borderRadius: "14px",
                      }}
                    >
                      <Typography
                        variant="h6"
                        component="h2"
                        sx={subTitleStyle}
                      >
                        Tindakan RO
                      </Typography>

                      <FormFieldRow
                        label={<Label text={t("responsibleRO")} />}
                        value={
                          <SelectFieldController
                            control={controlRoAction}
                            name="roId"
                            options={roListOptions}
                            onChange={(e) => {
                              const value = e.target.value;
                              const selectedRo = roList.find(
                                (ro) => ro.identificationNo === value
                              );

                              setValue("roName", selectedRo?.name ?? "");
                            }}
                            disabled={!hasUpdatePermission}
                          />
                        }
                      />

                      <FormFieldRow
                        align="flex-start"
                        label={<Label text={`${t("remarks")}`} />}
                        value={
                          <TextFieldController
                            control={controlRoAction}
                            name="noteRo"
                            multiline
                            sx={{
                              minHeight: "126px",
                            }}
                            sxInput={{
                              minHeight: "126px",
                            }}
                            disabled={!hasUpdatePermission || userRo}
                          />
                        }
                      />
                    </Box>

                    <Grid
                      item
                      xs={12}
                      sx={{
                        mt: 2,
                        display: "flex",
                        flexDirection: isMobile ? "column" : "row",
                        justifyContent: "flex-end",
                        gap: 1,
                      }}
                    >
                      <ButtonPrimary
                        type="submit"
                        disabled={
                          !hasUpdatePermission || userRo || isUpdatingRO
                        }
                      >
                        {t("update")}
                      </ButtonPrimary>
                    </Grid>
                  </form>
                  <form onSubmit={handleSubmit(onSubmit)}>
                    <Box
                      sx={{
                        pl: 2,
                        p: 3,
                        mt: 1,
                        borderRadius: "10px",
                        border: "0.5px solid #dfdfdf",
                      }}
                    >
                      <Box
                        sx={{
                          mb: 3,
                        }}
                      >
                        <Typography color={"primary"}>
                          {t("keputusan")}
                        </Typography>
                      </Box>
                      <Grid container>
                        <Grid item xs={12} sm={4}>
                          <Label text={t("statusPermohonan")} />
                        </Grid>

                        <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                          <SelectFieldController
                            control={control}
                            name="applicationStatusCode"
                            options={decisionOptions}
                            disabled={!hasUpdatePermission || isFormDisabled}
                            required
                          />
                        </Grid>

                        <Grid item xs={12} sm={4}>
                          <Label text={t("remarks")} required={requiredNote} />
                        </Grid>
                        <Grid item xs={12} sm={8}>
                          <TextFieldController
                            control={control}
                            name="note"
                            multiline
                            disabled={!hasUpdatePermission || isFormDisabled}
                            sx={{
                              minHeight: "92px",
                            }}
                            sxInput={{
                              minHeight: "92px",
                            }}
                            required={requiredNote}
                          />
                        </Grid>
                      </Grid>
                    </Box>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        marginTop: "34px",
                        gap: "10px",
                      }}
                    >
                      <ButtonPrevious
                        onClick={() =>
                          navigate(
                            "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk"
                          )
                        }
                      >
                        {t("back")}
                      </ButtonPrevious>

                      <ButtonPrimary
                        disabled={!hasUpdatePermission}
                        type="submit"
                      >
                        {t("hantar")}
                      </ButtonPrimary>
                    </Box>
                  </form>
                </Box>
              </>
            ) : (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  marginTop: "34px",
                  gap: "10px",
                }}
              >
                <ButtonPrimary onClick={() => navigate(-1)}>
                  {t("back")}
                </ButtonPrimary>
              </Box>
            )}
          </Box>
        </Fade>
      </Box>

      <DialogQueries
        open={isOpenDialogQueries}
        onClose={() => setIsOpenDialogQueries(false)}
        queries={queriesList}
      />

      <DialogConfirmation
        open={isDialogOpen}
        onClose={handleDialogClose}
        onAction={handleFormSubmit}
        isMutating={isLoading}
        onConfirmationText={t("sureToSubmit")}
        onSuccessText={renderSuccessText()}
        isSuccess={isSuccess}
        decisionLabel={decisionLabel}
      />
    </KeputusanIndukPembahruanSetiuasahaContext.Provider>
  );
}

export default KeputusanIndukPembaharuanSetiausaha;
