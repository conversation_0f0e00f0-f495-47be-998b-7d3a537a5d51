import { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Typography, IconButton } from "@mui/material";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { EyeIcon } from "@/components/icons";
import {
  OrganisationPositions,
  otherPositionSwitchList,
} from "@/helpers/enums";
import { useQuery } from "@/helpers";
import { Ajk } from "../../CawanganPenyataTahunan/interface";
import { useDispatch } from "react-redux";
import {
  resetJawatankuasaAJKFormValues,
  setJawatankuasaAJKFormValues,
} from "@/redux/slices/jawatankuasaAJKFormSlice";
import { DataTable, IColumn } from "@/components";
import { useBranchContext } from "@/pages/pertubuhan/BranchProvider";
import { API_URL } from "@/api";
import { useCustomMutation } from "@refinedev/core";

const JawatankuasaCawangan = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id: societyId } = useParams();
  const location = useLocation();
  // const branchId = location.state?.branchId ?? null;
  const disabled = location.state?.disabled ?? false;
  const { mutate: updateList, isLoading: isUpdatingList } = useCustomMutation();

  const [isReordering, setIsReordering] = useState(false);
  const [saveReorder, setSaveReorder] = useState(false);
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const [pageSize, setPageSize] = useState(5);
  const [page, setPage] = useState(1);

  const {
    branchId,
    isBlackListed,
    isAuthorized,
    isAliranModuleAccess,
    fetchAddressList,
    addressList,
    branchData,
    // fetchAliranTugasAccess,
    // isAliranModuleStatus,
    // fetchAliranTugasStatus,
  } = useBranchContext();
  useEffect(() => {
    fetchAjkList();
    fetchAddressList();
  }, []);

  const dispatch = useDispatch();

  const {
    data: ajks,
    isLoading: isLoadingAJKs,
    refetch: fetchAjkList,
  } = useQuery({
    url: `society/branch/committee/listAjk`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "status", operator: "eq", value: branchData?.status || "008" },
      { field: "branchId", operator: "eq", value: branchId },
      { field: "listAll", operator: "eq" as const, value: 1 },
      ...(!isReordering
        ? [
            { field: "pageNo", operator: "eq" as const, value: page },
            { field: "pageSize", operator: "eq" as const, value: pageSize },
            { field: "listAll", operator: "eq" as const, value: 0 },
          ]
        : []),
    ],
    autoFetch: true,
  });

  const handleViewAjk = (ajk: Ajk) => {
    navigate(`../jawatankuasa/create-ajk`, {
      state: {
        ajk: ajk,
        view: true,
        branchId,
      },
    });
  };

  useEffect(() => {
    dispatch(
      setJawatankuasaAJKFormValues({
        type: null,
        savedMeetingDate: null,
        savedMeetingDetail: null,
        appointmentDate: null,
        uploadedIds: [],
        citizenAJKs: [],
      })
    );
  }, []);

  const isPendingStatus =
    Number(branchData?.applicationStatusCode) === 2 &&
    branchData?.status === "008";

  const isAccessible = !isBlackListed && (isAuthorized || isAliranModuleAccess) && !isPendingStatus;

  const columns: IColumn[] = [
    {
      field: "designationCode",
      headerName: t("position"),
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box>
            {!otherPositionSwitchList.some(
              (item) => item.value === Number(row.designationCode)
            )
              ? t(
                  OrganisationPositions.find(
                    (item) => item.value === Number(row.designationCode)
                  )?.label || "-"
                )
              : row?.otherDesignationCode
              ? t(
                  row?.otherDesignationCode ??
                    t(
                      OrganisationPositions.find(
                        (item) => item.value === Number(row.designationCode)
                      )?.label || "-"
                    )
                )
              : row?.otherPosition}
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "committeeName",
      headerName: t("name"),
      align: "center",
      cellClassName: "custom-cell",
    },
    {
      field: "email",
      headerName: t("email"),
      align: "center",
      cellClassName: "custom-cell",
    },
    {
      field: "phoneNumber",
      headerName: t("phoneNumber"),
      align: "center",
      cellClassName: "custom-cell",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box>
            {row?.phoneNumber
              ? row?.phoneNumber
              : row?.telephoneNumber
              ? row?.telephoneNumber
              : row?.noTelP
              ? row?.noTelP
              : "-"}
          </Box>
        );
      },
    },
    {
      field: "residentialStateCode",
      headerName: t("state"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box>
            {
              addressList.find(
                (item: any) => item.id.toString() === row.committeeStateCode
              )?.name
            }
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      flex: 1,
      field: "actions",
      headerName: "",
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <>
            <IconButton onClick={() => handleViewAjk(row)}>
              <EyeIcon />
            </IconButton>
          </>
        );
      },
    },
  ];

  const handleReorder = (newOrder: string[]) => {
    if (saveReorder) {
      updateList(
        {
          url: `${API_URL}/branch/committee/arrangeCommittee`,
          method: "put",
          values: { branchId: branchId, committeeIdList: newOrder },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            if (data?.data?.status === "SUCCESS") {
              return {
                message: data?.data?.msg,
                type: "success",
              };
            } else {
              return {
                message: data?.data?.msg,
                type: "error",
              };
            }
          },
          errorNotification: (data) => {
            return {
              message: data?.response?.data?.msg,
              type: "error",
            };
          },
        },
        {
          onError(error, variables, context) {
            console.log(error);
          },
          onSuccess: () => {
            setIsReordering(false);
          },
        }
      );
    }
  };

  useEffect(() => {
    if (isReordering) {
      fetchAjkList();
    }
  }, [isReordering]);

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            textAlign: "center",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {t("bilanganAhliJawatankuasaTerkini")}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {ajks?.data?.data?.total} {t("orang")}
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("ajkList")}
          </Typography>
          <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 1 }}>
            {isReordering ? (
              <>
                <ButtonPrimary
                  onClick={() => {
                    setSaveReorder(true);
                  }}
                >
                  {t("save")}
                </ButtonPrimary>
                <ButtonOutline
                  onClick={() => {
                    setSaveReorder(false);
                    setIsReordering(false);
                  }}
                >
                  {t("cancel")}
                </ButtonOutline>
              </>
            ) : (
              <ButtonOutline
                onClick={() => {
                  setSaveReorder(false);
                  setIsReordering(true);
                }}
              >
                {t("reorder")}
              </ButtonOutline>
            )}
          </Box>
          <DataTable
            columns={columns}
            rows={ajks?.data?.data?.data || []}
            page={page}
            rowsPerPage={pageSize}
            isLoading={isLoadingAJKs}
            totalCount={ajks?.data?.data?.total}
            onPageChange={(newPage: number) => setPage(newPage)}
            onPageSizeChange={(newRowsPerPage: number) =>
              setPageSize(newRowsPerPage)
            }
            enableRowReordering={true}
            isReorderingMode={isReordering}
            onReorder={handleReorder}
            noPagination={isReordering}
            saveReorder={saveReorder}
          />
        </Box>
      </Box>
      {isAccessible ? (
        <>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("update")} {t("ahliJawatanKuasa")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{ color: "#666666", fontSize: 14 }}
                >
                  {t("update")} {t("ahliJawatanKuasa")}
                </Typography>

                <ButtonPrimary
                  disabled={disabled}
                  onClick={() => {
                    dispatch(resetJawatankuasaAJKFormValues());
                    navigate(`update-ajk`);
                  }}
                >
                  {t("update")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>
        </>
      ) : (
        <></>
      )}
    </>
    // :
    //  <></>
    // }
    // </>
  );
};

export default JawatankuasaCawangan;
