import Stack from "@mui/material/Stack";
import { useTranslation } from "react-i18next";
import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import {
  Box,
  Grid,
  Typography,
  Fade,
  Checkbox,
  FormControlLabel,
  FormControl,
  RadioGroup,
  Radio,
} from "@mui/material";
import CustomPopover from "@/components/popover";
import { API_URL } from "@/api";
import { useCustom } from "@refinedev/core";
import {} from "@/redux/fasalReducer";
import { ConstitutionType, OrganizationLevelOption } from "@/helpers/enums";
import { useDispatch } from "react-redux";
import { capitalizeWords } from "@/helpers/utils";
import ConfirmationDialog from "@/components/dialog/confirm";
import { useFormContext, Controller } from "react-hook-form";
import { removeFromStorage } from "@/pages/pertubuhan/pengurusan-pertubuhan/perlembagaan/removeFasal";
import {
  DisabledTextField,
  SelectFieldController,
  TextFieldController,
} from "@/components";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};
export interface PindaanPerlembagaan {
  id: number;
  fasal: number;
  namaFasal: string;
  applicationStatusCode: 1 | 2;
  constitutionType: string | null;
}

export const MaklumatPindaanPerlembagaan = ({
  handleSubmitAmendment,
  amendmentId,
  isViewMode,
  constitutionType,
}: {
  handleSubmitAmendment: (data: boolean) => void;
  amendmentId: string | number | null;
  isViewMode: boolean;
  constitutionType: string | null;
}) => {
  const { t, i18n } = useTranslation();
  const { id } = useParams();
  const params = new URLSearchParams(window.location.search);
  const isEdit = params.get("isEdit");
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const { control, setValue, getValues, watch, handleSubmit } =
    useFormContext();
  const { data: categoryData, isLoading: isCategoryLoading } = useCustom({
    url: `${API_URL}/society/admin/category/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const isBebasCatergory =
    constitutionType === ConstitutionType.Bebas[1] ||
    constitutionType === ConstitutionType.CawanganBebas[1];

  const societyLevel = OrganizationLevelOption;
  const categories = categoryData?.data?.data || [];

  const mainCategories = categories
    .filter((cat: any) => cat.level === 1)
    .map((cat: any) => ({
      value: cat.id,
      label: cat.categoryNameEn,
    }));

  const subCategories = categories.filter((cat: any) => cat.level === 2);
  const finalSubCategories = subCategories
    .filter(
      (subCat: any) =>
        subCat.pid === parseInt(getValues("organizationCategory"))
    )
    .map((cat: any) => ({
      value: cat.id,
      label: cat.categoryNameEn,
    }));

  const hasBranchList = [
    {
      value: "0",
      label: "Tidak",
    },
    { value: "1", label: "Ya" },
  ];

  const mainCategoryValue = watch("organizationCategory");
  const templateType = watch("constitutionsTemplateType");
  const hasBranch = watch("hasBranch");
  const IsFeadah = Number(mainCategoryValue) === 10;
  const IsAgama = Number(mainCategoryValue) === 11;
  const organizationCategory = [2, 3, 4, 5, 6, 7, 8, 9];
  // ===========================

  useEffect(() => {
    if (Number(templateType) === 0) {
      //CHECK IF CHOOSING BEBAS TYPE
      if (Number(hasBranch) === 1) {
        setValue("constitutionType", ConstitutionType.CawanganBebas[1]);
      } else {
        setValue("constitutionType", ConstitutionType.Bebas[1]);
      }
    } else if (IsFeadah && Number(templateType) === 1) {
      //CHECK IF FAEDAH TYPE
      setValue("constitutionType", ConstitutionType.FaedahBersama[1]);
    } else if (IsAgama && Number(templateType) === 1) {
      //CHECK IF AGAMA TYPE
      if (Number(hasBranch) === 1) {
        setValue("constitutionType", ConstitutionType.CawanganAgama[1]);
      } else {
        setValue("constitutionType", ConstitutionType.IndukAgama[1]);
      }
    } else if (
      Number(templateType) === 1 &&
      organizationCategory.includes(getValues("organizationCategory"))
    ) {
      if (Number(hasBranch) === 1) {
        setValue("constitutionType", ConstitutionType.CawanganNGO[1]);
      } else {
        setValue("constitutionType", ConstitutionType.IndukNGO[1]);
      }
    } else {
      setValue("constitutionType", "-");
    }
  }, [mainCategoryValue, templateType, hasBranch]);

  // ===========================

  const onSubmit = (isEdit: boolean) => (data: any) => {
    handleSubmitAmendment(isEdit);
    setOpenConfirmDialog(false);
  };

  return (
    <>
      {/* ======================================================================================================================================= */}
      {/* ======================================================================================================================================= */}
      {/* ======================================================================================================================================= */}
      {/* ======================================================================================================================================= */}
      <Fade in={true} timeout={500}>
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("maklumatPindaanPerlembagaan")}
          </Typography>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={3}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography sx={labelStyle}>
                  {t("PurposeOfConstitutionalAmendment")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
                <CustomPopover
                  customStyles={{ maxWidth: "250px" }}
                  content={
                    <Typography sx={{ color: "#666666" }}>
                      Keterangan tujuan pindaan perlembagaan ini dibuat
                      <br />
                      • Meminda peruntukan perlembagaan berkaitan pelantikan AJK
                      <br />• Menaikkan yuran ahli
                    </Typography>
                  }
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={9}>
              <TextFieldController
                variant="outlined"
                size="small"
                name={`organizationGoals`}
                control={control}
                disabled={isViewMode}
                required
              />
            </Grid>
          </Grid>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={3}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography sx={labelStyle}>{t("amendedClause")}</Typography>
                <CustomPopover
                  customStyles={{ maxWidth: "250px" }}
                  content={
                    <Typography sx={{ color: "#666666" }}>
                      Senaraikan fasal yang akan dipinda <br />
                      Contoh : "fasal 1,2,3 & 7
                    </Typography>
                  }
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={9}>
              <TextFieldController
                variant="outlined"
                size="small"
                name={`amendedConstitutionRemarks`}
                control={control}
                disabled={isViewMode}
                required
              />
            </Grid>
          </Grid>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={3}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography sx={labelStyle}>Jenis perlembagaan</Typography>
                <CustomPopover
                  customStyles={{ maxWidth: "250px" }}
                  content={
                    <Typography sx={{ color: "#666666" }}>
                      Mengikut templat : Pengisian Perlembagaan mengikut format
                      yang telah disediakan JPPM.
                      <br />
                      Pengguna hanya perlu mengisi ruang kosong yang disediakan.
                      <br />
                      Bebas : Pengisian perlembagaan mengikut format pertubuhan
                      sendiri.
                    </Typography>
                  }
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={9}>
              <Controller
                name="constitutionsTemplateType"
                control={control}
                rules={{ required: "Please select an option" }}
                render={({ field }) => (
                  <RadioGroup {...field} row>
                    <FormControlLabel
                      disabled={isBebasCatergory}
                      sx={{
                        "&.MuiFormControlLabel-root .MuiFormControlLabel-label":
                          {
                            fontSize: "14px !important",
                            fontWeight: "500!important",
                            color: "#666666",
                          },
                      }}
                      value={"1"}
                      control={
                        <Radio
                          icon={
                            <Box
                              sx={{
                                width: 16,
                                height: 16,
                                borderRadius: "2px",
                                border: "1px solid #848484",
                                backgroundColor: isBebasCatergory
                                  ? "#E8E8E8"
                                  : "transparent",
                              }}
                            />
                          }
                          checkedIcon={
                            <Box
                              sx={{
                                width: 16,
                                height: 16,
                                borderRadius: "2px",
                                border: isBebasCatergory
                                  ? "1px solid  #848484"
                                  : "none",
                                backgroundColor: isBebasCatergory
                                  ? "#E8E8E8"
                                  : "#41C3C3",
                              }}
                            ></Box>
                          }
                        />
                      }
                      label="Mengikut Templat"
                    />
                    <FormControlLabel
                      disabled={isBebasCatergory}
                      sx={{
                        "&.MuiFormControlLabel-root .MuiFormControlLabel-label":
                          {
                            fontSize: "14px !important",
                            fontWeight: "500!important",
                            color: "#666666",
                          },
                      }}
                      value={"0"}
                      control={
                        <Radio
                          icon={
                            <Box
                              sx={{
                                width: 16,
                                height: 16,
                                borderRadius: "2px",
                                border: "1px solid #848484",
                                backgroundColor: isBebasCatergory
                                  ? "#E8E8E8"
                                  : "transparent",
                              }}
                            />
                          }
                          checkedIcon={
                            <Box
                              sx={{
                                width: 16,
                                height: 16,
                                borderRadius: "2px",
                                border: isBebasCatergory
                                  ? "1px solid #848484"
                                  : "none",
                                backgroundColor: isBebasCatergory
                                  ? "#E8E8E8"
                                  : "41C3C3",
                              }}
                            ></Box>
                          }
                        />
                      }
                      label="Bebas"
                    />
                  </RadioGroup>
                )}
              />
            </Grid>
          </Grid>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={3}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography sx={labelStyle}>Bercawangan</Typography>
                <CustomPopover
                  customStyles={{ maxWidth: "250px" }}
                  content={
                    <Typography sx={{ color: "#666666" }}>
                      Ya : Untuk pertubuhan yang mempunyai atau bakal menubuhkan
                      cawangan
                      <br />
                      Tidak : Pertubuhan tidak mempunyai cawangan
                    </Typography>
                  }
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={9}>
              <Controller
                name="hasBranch"
                control={control}
                rules={{ required: "Please select an option" }}
                render={({ field }) => (
                  <RadioGroup {...field} row>
                    <FormControlLabel
                      sx={{
                        "&.MuiFormControlLabel-root .MuiFormControlLabel-label":
                          {
                            fontSize: "14px !important",
                            fontWeight: "500!important",
                            color: "#666666",
                          },
                      }}
                      value={"1"}
                      control={
                        <Radio
                          icon={
                            <Box
                              sx={{
                                width: 16,
                                height: 16,
                                borderRadius: "2px",
                                border: "1px solid #848484",
                              }}
                            />
                          }
                          checkedIcon={
                            <Box
                              sx={{
                                width: 16,
                                height: 16,
                                borderRadius: "2px",
                                backgroundColor: "#41C3C3",
                              }}
                            ></Box>
                          }
                        />
                      }
                      label="Ya"
                    />
                    <FormControlLabel
                      sx={{
                        "&.MuiFormControlLabel-root .MuiFormControlLabel-label":
                          {
                            fontSize: "14px !important",
                            fontWeight: "500!important",
                            color: "#666666",
                          },
                      }}
                      value={"0"}
                      control={
                        <Radio
                          icon={
                            <Box
                              sx={{
                                width: 16,
                                height: 16,
                                borderRadius: "2px",
                                border: "1px solid #848484",
                              }}
                            />
                          }
                          checkedIcon={
                            <Box
                              sx={{
                                width: 16,
                                height: 16,
                                borderRadius: "2px",
                                backgroundColor: "#41C3C3",
                              }}
                            ></Box>
                          }
                        />
                      }
                      label="Tidak"
                    />
                  </RadioGroup>
                )}
              />
            </Grid>
          </Grid>

          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={3}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography sx={labelStyle}>
                  Jenis Templat Perlembagaan
                </Typography>
                <CustomPopover
                  customStyles={{ maxWidth: "500px" }}
                  content={
                    <Typography sx={{ color: "#666666" }}>
                      Perlembagaan hendaklah mengikut format yang disediakan
                      oleh JPPM <br />
                      <br />
                      • PERLEMBAGAAN INDUK NGO : Templat perlembagaan JPPM bagi
                      semua kategori pertubuhan kecuali Politik, Keagamaan atau
                      Faedah Bersama.
                      <br />
                      • PERLEMBAGAAN BERCAWANGAN SEMUA NGO : Templat
                      perlembagaan JPPM bagi semua kategori pertubuhan kecuali
                      Politik, Keagamaan atau Faedah Bersama bagi pertubuhan
                      yang mempunyai cawangan.
                      <br />
                      • PERLEMBAGAAN INDUK KEAGAMAAN : Templat perlembagaan JPPM
                      bagi pertubuhan kategori Keagamaan.
                      <br />
                      • PERLEMBAGAAN BERCAWANGAN KEAGAMAAN : Templat
                      perlembagaan JPPM bagi pertubuhan kategori Keagamaan bagi
                      pertubuhan yang mempunyai cawangan.
                      <br />
                      • PERLEMBAGAAN FAEDAH BERSAMA : Templat perlembagaan JPPM
                      bagi pertubuhan kategori Faedah Bersama.
                      <br />
                      • Perlembagaan Bebas : Pengisian perlembagaan tanpa
                      menggunakan templat.
                      <br />• Perlembagaan Bebas Bercawangan : Pengisian
                      perlembagaan tanpa menggunakan templat bagi pertubuhan
                      yang mempunyai cawangan
                    </Typography>
                  }
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={9}>
              <DisabledTextField value={watch("constitutionType")} />
            </Grid>
          </Grid>
          <Stack
            direction="row"
            spacing={2}
            mt={2}
            sx={{ pl: 1 }}
            justifyContent="flex-end"
          >
            <ButtonPrimary
              sx={{ display: isViewMode ? "none" : "block" }}
              // disabled={AmendmentCreateIsLoading}
              onClick={
                amendmentId
                  ? () => setOpenConfirmDialog(true)
                  : handleSubmit(onSubmit(false))
              }
            >
              {t("save")}
            </ButtonPrimary>
          </Stack>
        </Box>
      </Fade>
      <ConfirmationDialog
        open={openConfirmDialog}
        onClose={() => setOpenConfirmDialog(false)}
        onConfirm={handleSubmit(onSubmit(true))}
        onCancel={() => setOpenConfirmDialog(false)}
        title={t("updateConstitution")}
        message={t("comfirmUpdatePindaan")}
      />
    </>
  );
};

export default MaklumatPindaanPerlembagaan;
