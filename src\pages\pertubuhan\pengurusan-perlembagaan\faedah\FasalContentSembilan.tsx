import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import { formatAndValidateNumber, RegExNumbers, RegExText } from "@/helpers";
import AsalContent from "@/components/asalFasalContent";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import CustomPopover from "@/components/popover";
interface FasalContentSembilanFaedahProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: [];
  name: string;
}

const renderTitle = (title: string) => {
  const { t } = useTranslation();
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  return (
    <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
      <Typography sx={labelStyle}>
        {title}{" "}
        <Typography sx={{ display: "inline", color: "red" }}>*</Typography>
      </Typography>
      <CustomPopover
        customStyles={{ maxWidth: "250px" }}
        content={
          <Typography sx={{ color: "#666666" }}>{t("max2000")}</Typography>
        }
      />
    </Box>
  );
};

export const FasalContentSembilanFaedah: React.FC<
  FasalContentSembilanFaedahProps
> = ({ activeStep, setActiveStep, clause, asalData, name }) => {
  const [asal, setAsal] = useState<any>(null);
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");

  const { id, clauseId } = useParams();
  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName, address, mailingAddress } =
          responseData?.data?.data;
        setNamaPertubuhan(societyName);
      },
    },
  });

  const [bantuanKelahiranAnakPertama, setBantuanKelahiranAnakPertama] =
    useState("");
  const [bantuanKelahiranAnakPertamaKata, setBantuanKelahiranAnakPertamaKata] =
    useState("");
  const [bantuanKelahiranAnakKedua, setBantuanKelahiranAnakKedua] =
    useState("");
  const [bantuanKelahiranAnakKeduaKata, setBantuanKelahiranAnakKeduaKata] =
    useState("");
  const [bantuanKelahiranAnakKetiga, setBantuanKelahiranAnakKetiga] =
    useState("");
  const [bantuanKelahiranAnakKetigaKata, setBantuanKelahiranAnakKetigaKata] =
    useState("");
  const [bantuanKelahiranAnakSeterusnya, setBantuanKelahiranAnakSeterusnya] =
    useState("");
  const [
    bantuanKelahiranAnakSeterusnyaKata,
    setBantuanKelahiranAnakSeterusnyaKata,
  ] = useState("");
  const [bantuanKematianAhliSendiri, setBantuanKematianAhliSendiri] =
    useState("");
  const [bantuanKematianAhliSendiriKata, setBantuanKematianAhliSendiriKata] =
    useState("");
  const [bantuanKematianSuamiIsteriAhli, setBantuanKematianSuamiIsteriAhli] =
    useState("");
  const [
    bantuanKematianSuamiIsteriAhliKata,
    setBantuanKematianSuamiIsteriAhliKata,
  ] = useState("");
  const [bantuanKematianAnakAhli, setBantuanKematianAnakAhli] = useState("");
  const [bantuanKematianAnakAhliKata, setBantuanKematianAnakAhliKata] =
    useState("");
  const [bantuanKematianIbuBapaAhli, setBantuanKematianIbuBapaAhli] =
    useState("");
  const [bantuanKematianIbuBapaAhliKata, setBantuanKematianIbuBapaAhliKata] =
    useState("");
  const [bantuanKematianNenekDatukAhli, setBantuanKematianNenekDatukAhli] =
    useState("");
  const [
    bantuanKematianNenekDatukAhliKata,
    setBantuanKematianNenekDatukAhliKata,
  ] = useState("");
  const [bantuanPengebumian, setBantuanPengebumian] = useState("");
  const [bantuanPengebumianKata, setBantuanPengebumianKata] = useState("");
  const [bantuanNafkahAhliYangKeuzuran, setBantuanNafkahAhliYangKeuzuran] =
    useState("");
  const [
    bantuanNafkahAhliYangKeuzuranKata,
    setBantuanNafkahAhliYangKeuzuranKata,
  ] = useState("");
  const [
    bantuanNafkahAhliYangMenjadiBalu,
    setBantuanNafkahAhliYangMenjadiBalu,
  ] = useState("");
  const [
    bantuanNafkahAhliYangMenjadiBaluKata,
    setBantuanNafkahAhliYangMenjadiBaluKata,
  ] = useState("");
  const [
    bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun,
    setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun,
  ] = useState("");
  const [
    bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata,
    setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata,
  ] = useState("");

  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause = JSON.parse(clause9);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause3Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause.societyName);
      setBantuanKelahiranAnakPertama(
        clause.constitutionValues[0]?.definitionName
      );
      setBantuanKelahiranAnakPertamaKata(
        clause.constitutionValues[1]?.definitionName
      );
      setBantuanKelahiranAnakKedua(
        clause.constitutionValues[2]?.definitionName
      );
      setBantuanKelahiranAnakKeduaKata(
        clause.constitutionValues[3]?.definitionName
      );
      setBantuanKelahiranAnakKetiga(
        clause.constitutionValues[4]?.definitionName
      );
      setBantuanKelahiranAnakKetigaKata(
        clause.constitutionValues[5]?.definitionName
      );
      setBantuanKelahiranAnakSeterusnya(
        clause.constitutionValues[6]?.definitionName
      );
      setBantuanKelahiranAnakSeterusnyaKata(
        clause.constitutionValues[7]?.definitionName
      );
      setBantuanKematianAhliSendiri(
        clause.constitutionValues[8]?.definitionName
      );
      setBantuanKematianAhliSendiriKata(
        clause.constitutionValues[9]?.definitionName
      );
      setBantuanKematianSuamiIsteriAhli(
        clause.constitutionValues[10]?.definitionName
      );
      setBantuanKematianSuamiIsteriAhliKata(
        clause.constitutionValues[11]?.definitionName
      );
      setBantuanKematianAnakAhli(clause.constitutionValues[12]?.definitionName);
      setBantuanKematianAnakAhliKata(
        clause.constitutionValues[13]?.definitionName
      );
      setBantuanKematianIbuBapaAhli(
        clause.constitutionValues[14]?.definitionName
      );
      setBantuanKematianIbuBapaAhliKata(
        clause.constitutionValues[15]?.definitionName
      );
      setBantuanKematianNenekDatukAhli(
        clause.constitutionValues[16]?.definitionName
      );
      setBantuanKematianNenekDatukAhliKata(
        clause.constitutionValues[17]?.definitionName
      );
      setBantuanPengebumian(clause.constitutionValues[18]?.definitionName);
      setBantuanPengebumianKata(clause.constitutionValues[19]?.definitionName);
      setBantuanNafkahAhliYangKeuzuran(
        clause.constitutionValues[20]?.definitionName
      );
      setBantuanNafkahAhliYangKeuzuranKata(
        clause.constitutionValues[21]?.definitionName
      );
      setBantuanNafkahAhliYangMenjadiBalu(
        clause.constitutionValues[22]?.definitionName
      );
      setBantuanNafkahAhliYangMenjadiBaluKata(
        clause.constitutionValues[23]?.definitionName
      );
      setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun(
        clause.constitutionValues[24]?.definitionName
      );
      setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata(
        clause.constitutionValues[25]?.definitionName
      );

      setIsEdit(clause.edit);
      setAsal(
        asalData.find((item: any) => item.clauseNo === clause.clauseNo
        ) || ""
      );
    }
  }, [clause]);

  const limitAmount = 2000;
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak pertama>>/gi,
    `<b>${
      bantuanKelahiranAnakPertama || "<<bantuan kelahiran anak pertama>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak pertama-dalam perkataan>>/gi,
    `<b>${
      bantuanKelahiranAnakPertamaKata ||
      "<<bantuan kelahiran anak pertama-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak kedua>>/gi,
    `<b>${bantuanKelahiranAnakKedua || "<<bantuan kelahiran anak kedua>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak kedua-dalam perkataan>>/gi,
    `<b>${
      bantuanKelahiranAnakKeduaKata ||
      "<<bantuan kelahiran anak kedua-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak ketiga>>/gi,
    `<b>${
      bantuanKelahiranAnakKetiga || "<<bantuan kelahiran anak ketiga>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak ketiga-dalam perkataan>>/gi,
    `<b>${
      bantuanKelahiranAnakKetigaKata ||
      "<<bantuan kelahiran anak ketiga-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak seterusnya>>/gi,
    `<b>${
      bantuanKelahiranAnakSeterusnya || "<<bantuan kelahiran anak seterusnya>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kelahiran anak seterusnya-dalam perkataan>>/gi,
    `<b>${
      bantuanKelahiranAnakSeterusnyaKata ||
      "<<bantuan kelahiran anak seterusnya-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian ahli sendiri>>/gi,
    `<b>${
      bantuanKematianAhliSendiri || "<<bantuan kematian ahli sendiri>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian ahli sendiri-dalam perkataan>>/gi,
    `<b>${
      bantuanKematianAhliSendiriKata ||
      "<<bantuan kematian ahli sendiri-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian suami\/isteri ahli>>/gi,
    `<b>${
      bantuanKematianSuamiIsteriAhli || "<<bantuan kematian suami/isteri ahli>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian suami\/isteri ahli-dalam perkataan>>/gi,
    `<b>${
      bantuanKematianSuamiIsteriAhliKata ||
      "<<bantuan kematian suami/isteri ahli-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian anak ahli>>/gi,
    `<b>${bantuanKematianAnakAhli || "<<bantuan kematian anak ahli>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian anak ahli-dalam perkataan>>/gi,
    `<b>${
      bantuanKematianAnakAhliKata ||
      "<<bantuan kematian anak ahli-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian ibu\/bapa ahli>>/gi,
    `<b>${
      bantuanKematianIbuBapaAhli || "<<bantuan kematian ibu/bapa ahli>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian ibu\/bapa ahli-dalam perkataan>>/gi,
    `<b>${
      bantuanKematianIbuBapaAhliKata ||
      "<<bantuan kematian ibu/bapa ahli-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian nenek\/datuk ahli>>/gi,
    `<b>${
      bantuanKematianNenekDatukAhli || "<<bantuan kematian nenek/datuk ahli>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan kematian nenek\/datuk ahli-dalam perkataan>>/gi,
    `<b>${
      bantuanKematianNenekDatukAhliKata ||
      "<<bantuan kematian nenek/datuk ahli-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan pengebumian>>/gi,
    `<b>${bantuanPengebumian || "<<bantuan pengebumian>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan pengebumian-dalam perkataan>>/gi,
    `<b>${
      bantuanPengebumianKata || "<<bantuan pengebumian-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan\/nafkah ahli yang menjadi balu>>/gi,
    `<b>${
      bantuanNafkahAhliYangMenjadiBalu ||
      "<<bantuan/nafkah ahli yang menjadi balu>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan\/nafkah ahli yang menjadi balu-dalam perkataan>>/gi,
    `<b>${
      bantuanNafkahAhliYangMenjadiBaluKata ||
      "<<bantuan nafkah ahli yang menjadi balu-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan\/nafkah ahli yang keuzuran>>/gi,
    `<b>${
      bantuanNafkahAhliYangKeuzuran || "<<bantuan nafkah ahli yang keuzuran>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan\/nafkah ahli yang keuzuran-dalam perkataan>>/gi,
    `<b>${
      bantuanNafkahAhliYangKeuzuranKata ||
      "<<bantuan/nafkah ahli yang keuzuran-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan\/nafkah ahli balu yang mempunyai anak di bawah umur 12 tahun>>/gi,
    `<b>${
      bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun ||
      "<<bantuan/nafkah ahli balu yang mempunyai anak di bawah umur 12 tahun>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bantuan\/nafkah ahli balu yang mempunyai anak di bawah umur 12 tahun-dalam perkataan>>/gi,
    `<b>${
      bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata ||
      "<<bantuan/nafkah ahli balu yang mempunyai anak di bawah umur 12 tahun-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");
  return (
    <>
      <AsalContent
        asalData={asal?.description || ""}
        clauseId={clauseId}
        name={asal?.clauseName}
      />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKelahiranAnakPertama")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKelahiranAnakPertama"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bantuanKelahiranAnakPertama}
              error={!!formErrors.bantuanKelahiranAnakPertama}
              helperText={formErrors.bantuanKelahiranAnakPertama}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKelahiranAnakPertama(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakPertama:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakPertama: "",
                    }));
                  }
                } else {
                  setBantuanKelahiranAnakPertama("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              placeholder="contoh : dua puluh."
              value={bantuanKelahiranAnakPertamaKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKelahiranAnakPertamaKata(e.target.value);
                } else {
                  setBantuanKelahiranAnakPertamaKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKelahiranAnakKedua")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKelahiranAnakKedua"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              value={bantuanKelahiranAnakKedua}
              error={!!formErrors.bantuanKelahiranAnakKedua}
              helperText={formErrors.bantuanKelahiranAnakKedua}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKelahiranAnakKedua(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakKedua:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakKedua: "",
                    }));
                  }
                } else {
                  setBantuanKelahiranAnakKedua("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              placeholder="contoh : dua puluh."
              value={bantuanKelahiranAnakKeduaKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKelahiranAnakKeduaKata(e.target.value);
                } else {
                  setBantuanKelahiranAnakKeduaKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKelahiranAnakKetiga")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKelahiranAnakKetiga"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bantuanKelahiranAnakKetiga}
              error={!!formErrors.bantuanKelahiranAnakKetiga}
              helperText={formErrors.bantuanKelahiranAnakKetiga}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKelahiranAnakKetiga(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakKetiga:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakKetiga: "",
                    }));
                  }
                } else {
                  setBantuanKelahiranAnakKetiga("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              placeholder="contoh : dua puluh."
              value={bantuanKelahiranAnakKetigaKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKelahiranAnakKetigaKata(e.target.value);
                } else {
                  setBantuanKelahiranAnakKetigaKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKelahiranAnakSeterusnya")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKelahiranAnakSeterusnya"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bantuanKelahiranAnakSeterusnya}
              error={!!formErrors.bantuanKelahiranAnakSeterusnya}
              helperText={formErrors.bantuanKelahiranAnakSeterusnya}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKelahiranAnakSeterusnya(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakSeterusnya:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKelahiranAnakSeterusnya: "",
                    }));
                  }
                } else {
                  setBantuanKelahiranAnakSeterusnya("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              placeholder="contoh : dua puluh."
              value={bantuanKelahiranAnakSeterusnyaKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKelahiranAnakSeterusnyaKata(e.target.value);
                } else {
                  setBantuanKelahiranAnakSeterusnyaKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKematianAhliSendiri")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKematianAhliSendiri"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bantuanKematianAhliSendiri}
              error={!!formErrors.bantuanKematianAhliSendiri}
              helperText={formErrors.bantuanKematianAhliSendiri}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKematianAhliSendiri(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianAhliSendiri:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianAhliSendiri: "",
                    }));
                  }
                } else {
                  setBantuanKematianAhliSendiri("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              placeholder="contoh : dua puluh."
              value={bantuanKematianAhliSendiriKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKematianAhliSendiriKata(e.target.value);
                } else {
                  setBantuanKematianAhliSendiriKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKematianPasanganAhli")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKematianPasanganAhli"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bantuanKematianSuamiIsteriAhli}
              error={!!formErrors.bantuanKematianSuamiIsteriAhli}
              helperText={formErrors.bantuanKematianSuamiIsteriAhli}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKematianSuamiIsteriAhli(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianSuamiIsteriAhli:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianSuamiIsteriAhli: "",
                    }));
                  }
                } else {
                  setBantuanKematianSuamiIsteriAhli("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              placeholder="contoh : dua puluh."
              value={bantuanKematianSuamiIsteriAhliKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKematianSuamiIsteriAhliKata(e.target.value);
                } else {
                  setBantuanKematianSuamiIsteriAhliKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKematianAnakAhli")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKematianAnakAhli"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bantuanKematianAnakAhli}
              error={!!formErrors.bantuanKematianAnakAhli}
              helperText={formErrors.bantuanKematianAnakAhli}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKematianAnakAhli(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianAnakAhli:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianAnakAhli: "",
                    }));
                  }
                } else {
                  setBantuanKematianAnakAhli("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              placeholder="contoh : dua puluh."
              value={bantuanKematianAnakAhliKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKematianAnakAhliKata(e.target.value);
                } else {
                  setBantuanKematianAnakAhliKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKematianIbuBapaAhli")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKematianIbuBapaAhli"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              value={bantuanKematianIbuBapaAhli}
              error={!!formErrors.bantuanKematianIbuBapaAhli}
              helperText={formErrors.bantuanKematianIbuBapaAhli}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKematianIbuBapaAhli(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianIbuBapaAhli:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianIbuBapaAhli: "",
                    }));
                  }
                } else {
                  setBantuanKematianIbuBapaAhli("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              placeholder="contoh : dua puluh."
              value={bantuanKematianIbuBapaAhliKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKematianIbuBapaAhliKata(e.target.value);
                } else {
                  setBantuanKematianIbuBapaAhliKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanKematianNenekDatukAhli")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanKematianNenekDatukAhli"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              value={bantuanKematianNenekDatukAhli}
              error={!!formErrors.bantuanKematianNenekDatukAhli}
              helperText={formErrors.bantuanKematianNenekDatukAhli}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanKematianNenekDatukAhli(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianNenekDatukAhli:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanKematianNenekDatukAhli: "",
                    }));
                  }
                } else {
                  setBantuanKematianNenekDatukAhli("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              placeholder="contoh : dua puluh."
              value={bantuanKematianNenekDatukAhliKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanKematianNenekDatukAhliKata(e.target.value);
                } else {
                  setBantuanKematianNenekDatukAhliKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanPengebumian")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanPengebumian"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bantuanPengebumian}
              error={!!formErrors.bantuanPengebumian}
              helperText={formErrors.bantuanPengebumian}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanPengebumian(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanPengebumian: "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanPengebumian: "",
                    }));
                  }
                } else {
                  setBantuanPengebumian("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              placeholder="contoh : dua puluh."
              value={bantuanPengebumianKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanPengebumianKata(e.target.value);
                } else {
                  setBantuanPengebumianKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanNafkahAhliKeuzuran")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanNafkahAhliKeuzuran"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bantuanNafkahAhliYangKeuzuran}
              error={!!formErrors.bantuanNafkahAhliYangKeuzuran}
              helperText={formErrors.bantuanNafkahAhliYangKeuzuran}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanNafkahAhliYangKeuzuran(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanNafkahAhliYangKeuzuran:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanNafkahAhliYangKeuzuran: "",
                    }));
                  }
                } else {
                  setBantuanNafkahAhliYangKeuzuran("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              placeholder="contoh : dua puluh."
              value={bantuanNafkahAhliYangKeuzuranKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanNafkahAhliYangKeuzuranKata(e.target.value);
                } else {
                  setBantuanNafkahAhliYangKeuzuranKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanNafkahAhliBalu")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanNafkahAhliBalu"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              value={bantuanNafkahAhliYangMenjadiBalu}
              error={!!formErrors.bantuanNafkahAhliYangMenjadiBalu}
              helperText={formErrors.bantuanNafkahAhliYangMenjadiBalu}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanNafkahAhliYangMenjadiBalu(formattedValue);
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanNafkahAhliYangMenjadiBalu:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanNafkahAhliYangMenjadiBalu: "",
                    }));
                  }
                } else {
                  setBantuanNafkahAhliYangMenjadiBalu("");
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              placeholder="contoh : dua puluh."
              value={bantuanNafkahAhliYangMenjadiBaluKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanNafkahAhliYangMenjadiBaluKata(e.target.value);
                } else {
                  setBantuanNafkahAhliYangMenjadiBaluKata("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bantuanNafkahAhliBaluAnak")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            {renderTitle(t("bantuanNafkahAhliBaluAnak"))}
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              value={bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun}
              error={
                !!formErrors.bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun
              }
              helperText={
                formErrors.bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun
              }
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun(
                    formattedValue
                  );
                  if (Number(e.target.value.replace(/,/g, "")) > limitAmount) {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun:
                        "Jumlah tidak boleh melebihi RM2,000",
                    }));
                  } else {
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun:
                        "",
                    }));
                  }
                } else {
                  setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun(
                    ""
                  );
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              placeholder="contoh : dua puluh."
              value={
                bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata
              }
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata(
                    e.target.value
                  );
                } else {
                  setBantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata(
                    ""
                  );
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKelahiranAnakPertama,
                    titleName: "Bantuan Kelahiran Anak Pertama",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKelahiranAnakPertamaKata,
                    titleName:
                      "Bantuan Kelahiran Anak Pertama (Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKelahiranAnakKedua,
                    titleName: "Bantuan Kelahiran Anak Kedua",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKelahiranAnakKeduaKata,
                    titleName: "Bantuan Kelahiran Anak Kedua (Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKelahiranAnakKetiga,
                    titleName: "Bantuan Kelahiran Anak Ketiga",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKelahiranAnakKetigaKata,
                    titleName:
                      "Bantuan Kelahiran Anak Ketiga (Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKelahiranAnakSeterusnya,
                    titleName: "Bantuan Kelahiran Anak Seterusnya",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKelahiranAnakSeterusnyaKata,
                    titleName:
                      "Bantuan Kelahiran Anak Seterusnya (Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKematianAhliSendiri,
                    titleName: "Bantuan Kematian Ahli Sendiri",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKematianAhliSendiriKata,
                    titleName:
                      "Bantuan Kematian Ahli Sendiri (Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKematianSuamiIsteriAhli,
                    titleName: "Bantuan Kematian Suami/Isteri Ahli",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKematianSuamiIsteriAhliKata,
                    titleName:
                      "Bantuan Kematian Suami/Isteri Ahli (Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKematianAnakAhli,
                    titleName: "Bantuan Kematian Anak Ahli",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKematianAnakAhliKata,
                    titleName: "Bantuan Kematian Anak Ahli (Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKematianIbuBapaAhli,
                    titleName: "Bantuan Kematian Ibu/Bapa Ahli",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKematianIbuBapaAhliKata,
                    titleName:
                      "Bantuan Kematian Ibu/Bapa Ahli (Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKematianNenekDatukAhli,
                    titleName: "Bantuan Kematian Nenek/Datuk Ahli",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanKematianNenekDatukAhliKata,
                    titleName:
                      "Bantuan Kematian Nenek/Datuk Ahli (Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanPengebumian,
                    titleName: "Bantuan Pengebumian",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanPengebumianKata,
                    titleName: "Bantuan Pengebumian (Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanNafkahAhliYangKeuzuran,
                    titleName: "Bantuan Nafkah Ahli Yang Keuzuran",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanNafkahAhliYangKeuzuranKata,
                    titleName:
                      "Bantuan Nafkah Ahli Yang Keuzuran (Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanNafkahAhliYangMenjadiBalu,
                    titleName: "Bantuan Nafkah Ahli Yang Menjadi Balu",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bantuanNafkahAhliYangMenjadiBaluKata,
                    titleName:
                      "Bantuan Nafkah Ahli Yang Menjadi Balu (Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName:
                      bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12Tahun,
                    titleName:
                      "Bantuan Nafkah Ahli Balu Yang Memiliki Anak Dibawah Umur 12 Tahun",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName:
                      bantuanNafkahAhliBaluYangMemilikiAnakDibawahUmur12TahunKata,
                    titleName:
                      "Bantuan Nafkah Ahli Balu Yang Memiliki Anak Dibawah Umur 12 Tahun (Dalam Perkataan)",
                  },
                ],
                clause: "clause9",
                clauseCount: 9,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentSembilanFaedah;
