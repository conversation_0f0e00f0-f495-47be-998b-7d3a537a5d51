import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import { RegExNumbers } from "@/helpers";
import AsalContent from "@/components/asalFasalContent";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
interface FasalContentSepuluhCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: [];
  name: string;
}

export const FasalContentSepuluhCawangan: React.FC<
  FasalContentSepuluhCawanganProps
> = ({ activeStep, setActiveStep, clause, asalData, name }) => {
  const [asal, setAsal] = useState<any>(null);
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [jenisJuruAudit, setJenisJuruAudit] = useState("");
  const [bilanganInternal, setBilanganInternal] = useState("");
  const [bilanganEksternal, setBilanganEksternal] = useState("");
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [tempohJawatan, setTempohJawatan] = useState(t("setiapTahun"));
  const [lainlain, setLainLain] = useState("");
  const [dataId, setDataId] = useState<number | null>(null);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const { id, clauseId } = useParams();
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName } = responseData?.data?.data;
        setNamaPertubuhan(societyName);
      },
    },
  });

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (jenisJuruAudit == t("internal") && !bilanganInternal) {
      errors.bilanganInternal = t("fieldRequired");
    }

    if (jenisJuruAudit == t("external") && !bilanganEksternal) {
      errors.bilanganEksternal = t("fieldRequired");
    }

    if (!pemilihanAjk) {
      errors.pemilihanAjk = t("fieldRequired");
    }
    return errors;
  };

  const clause10 = localStorage.getItem("clause10");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause10Data = JSON.parse(clause10);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause10Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      } 
      if(clause.constitutionValues.length > 0){ 
          const fieldMappings: Record<string, (value: string) => void> = {
          'Jenis Mesyuarat Agung': (value: string) => {
            if (value && value !== t("annual") && value !== t("biennial")) {
              setPemilihanAjk(t("lainLain"));
              setLainLain(value);
            } else {
              setPemilihanAjk(value);
            }}, 
            "Jenis Juruaudit": setJenisJuruAudit,
            "Bilangan Juruaudit Dalam": setBilanganInternal,
            "Bilangan Juruaudit Luar": setBilanganEksternal, 
            "Tempoh Pelantikan Jawatankuasa": setTempohJawatan
          };

          Object.values(fieldMappings).forEach(setter => setter(''));
          
          if(clause.constitutionValues){
            clause.constitutionValues.forEach((item:any) => {
              const setter = fieldMappings[item.titleName];
              if (setter && item.definitionName) {
                setter(item.definitionName);
              }
            });
          } 
        } 
      setIsEdit(clause.edit);
      setAsal(
        asalData.find((item: any) => item.clauseNo === clause.clauseNo
        ) || ""
      );
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);

  const internalAuditor =
    "1. <<bilangan juruaudit dalam>> orang yang bukannya Ahli Jawatankuasa Pertubuhan hendaklah dilantik dalam Mesyuarat Agung <<jenis mesyuarat agung>> sebagai Juruaudit dalam. Mereka yang memegang jawatan selama <<tempoh pelantikan jawatankuasa>> boleh dilantik semula.";
  const externalAuditor =
    "1. Juruaudit luar bertauliah boleh dilantik dalam Mesyuarat Agung/ Mesyuarat Jawatankuasa bagi tempoh <<tempoh pelantikan jawatankuasa>> tahun.";
  let clauseContent = clause.clauseContent;

  clauseContent = clauseContent.replaceAll(
    /Display for both jenis juruaudit \(continue from point 1 above\);/gi,
    ""
  );
  clauseContent = clauseContent.replaceAll(
    /If Jenis juruaudit = dalam, display/gi,
    ""
  );
  clauseContent = clauseContent.replaceAll(
    /If Jenis juruaudit = luar, display/gi,
    ""
  );
  clauseContent = clauseContent.replaceAll(
    new RegExp(internalAuditor, "gi"),
    ""
  );
  clauseContent = clauseContent.replaceAll(
    new RegExp(externalAuditor, "gi"),
    "<<jenis juruaudit>>"
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis juruaudit>>/gi,
    jenisJuruAudit === t("internal") ? internalAuditor : externalAuditor
  );
  //clauseContent = clauseContent.replaceAll(/<<jumlah wang tangan yang dibenarkan dalam tangan>>/gi, `<b>${jenisJuruAudit || '<<jumlah wang tangan yang dibenarkan dalam tangan>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<bilangan juruaudit dalam>>/gi,
    `<b>${bilanganInternal || "<<bilangan juruaudit dalam>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<perbelanjaan yg dibenarkan>>/gi, `<b>${bilanganEksternal || '<<perbelanjaan yg dibenarkan>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${lainlain ? lainlain : pemilihanAjk || "<<jenis mesyuarat agung>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${tempohPelucutanWaktu || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<tempoh pelantikan jawatankuasa>>/gi,
    `<b>${tempohJawatan || "<<tempoh pelantikan jawatankuasa>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  return (
    <>
      <AsalContent
        asalData={asal?.description || ""}
        clauseId={clauseId}
        name={asal?.clauseName}
      />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("auditorType")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("auditorType")}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth>
              <Select
                size="small"
                displayEmpty
                disabled={isViewMode}
                value={jenisJuruAudit}
                onChange={(e) => {
                  setJenisJuruAudit(e.target.value);
                  /*if(e.target.value === t("internal")) {
                    clauseContent = clauseContent.replaceAll(/<<jenis juruaudit>>/gi, "1. <<bilangan juruaudit dalam>> orang yang bukannya Ahli Jawatankuasa Pertubuhan hendaklah dilantik dalam Mesyuarat Agung <<jenis mesyuarat agung>> sebagai Juruaudit dalam. Mereka yang memegang jawatan selama <<tempoh pelantikan jawatankuasa>> boleh dilantik semula.");
                  }else if(e.target.value === t("external")) {
                    clauseContent = clauseContent.replaceAll(/<<jenis juruaudit>>/gi,"1. Juruaudit luar bertauliah boleh dilantik dalam Mesyuarat Agung/ Mesyuarat Jawatankuasa bagi tempoh <<tempoh pelantikan jawatankuasa>> tahun.");
                  }*/
                }}
              >
                <MenuItem value={t("internal")}>{t("internal")}</MenuItem>
                <MenuItem value={t("external")}>{t("external")}</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {jenisJuruAudit == t("internal") && (
            <>
              <Grid item xs={12} md={4}>
                <Typography sx={labelStyle}>
                  {t("internalAuditorNumber")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </Typography>
              </Grid>
              <Grid item xs={12} md={8}>
                <TextField
                  size="small"
                  fullWidth
                  required
                  disabled={isViewMode}
                  value={bilanganInternal}
                  onChange={(e) => {
                    if (RegExNumbers.test(e.target.value)) {
                      setBilanganInternal(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        bilanganInternal: "",
                      }));
                    } else {
                      setBilanganInternal("");
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        bilanganInternal: "Invalid Value",
                      }));
                    }
                  }}
                  error={!!formErrors.bilanganInternal}
                  helperText={formErrors.bilanganInternal}
                  type="number"
                />
              </Grid>
            </>
          )}

          {jenisJuruAudit == t("external") && (
            <>
              <Grid item xs={12} md={4}>
                <Typography sx={labelStyle}>
                  {t("externalAuditorNumber")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </Typography>
              </Grid>
              <Grid item xs={12} md={8}>
                <TextField
                  fullWidth
                  required
                  disabled={isViewMode}
                  value={bilanganEksternal}
                  onChange={(e) => {
                    if (RegExNumbers.test(e.target.value)) {
                      setBilanganEksternal(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        bilanganEksternal: "",
                      }));
                    } else {
                      setBilanganEksternal("");
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        bilanganEksternal: "Invalid Value",
                      }));
                    }
                  }}
                  error={!!formErrors.bilanganEksternal}
                  helperText={formErrors.bilanganEksternal}
                  type="number"
                />
              </Grid>
            </>
          )}
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("jenisMesyuaratAgungDanTempohLantikan")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("jenisMesyuaratAgung")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required error={!!formErrors.pemilihanAjk}>
              <Select
                size="small"
                value={pemilihanAjk}
                disabled={isViewMode}
                displayEmpty
                onChange={(e) => {
                  setPemilihanAjk(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    pemilihanAjk: "",
                  }));
                  if ((e.target.value as string) == t("annual")) {
                    setLainLain("")
                  } else if ((e.target.value as string) == t("biennial")) {
                    setLainLain("")
                  }
                }}
              >
                <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
                <MenuItem value={t("lainLain")}>{t("lainLain")}</MenuItem>
              </Select>
            </FormControl>
            {formErrors.pemilihanAjk && (
              <FormHelperText>{formErrors.pemilihanAjk}</FormHelperText>
            )}
          </Grid>

          {pemilihanAjk === t("lainLain") ?
          <>
            <Grid item xs={12} md={4}></Grid>
            <Grid item xs={12} md={8}>
                <TextField
                  type="text"
                  size="small"
                  placeholder="Yearly, Biannually, Tri-tahunan"
                  fullWidth
                  required
                  value={lainlain}
                  onChange={(e) => {
                    setLainLain(e.target.value as string);
                  }}
                />
              </Grid>
              </> : null
          }

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("auditorAppointmentPeriod")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required>
              <Select
                size="small"
                value={tempohJawatan}
                disabled={isViewMode}
                displayEmpty
                onChange={(e) => setTempohJawatan(e.target.value as string)}
              >
                <MenuItem value={t("setahun")}>{t("setahun")}</MenuItem>
                <MenuItem value={t("duaTahun")}>{t("duaTahun")}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              const errors = validateForm();
              if (Object.keys(errors).length > 0) {
                console.log(errors);
                setFormErrors(errors);
                return;
              }

              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jenisJuruAudit,
                    titleName: "Jenis Juruaudit",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bilanganInternal,
                    titleName: "Bilangan Juruaudit Dalam",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bilanganEksternal,
                    titleName: "Bilangan Juruaudit Luar",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: lainlain ? lainlain : pemilihanAjk,
                    titleName: "Jenis Mesyuarat Agung",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tempohJawatan,
                    titleName: "Tempoh Pelantikan Jawatankuasa",
                  },
                ],
                clause: "clause10",
                clauseCount: 10,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentSepuluhCawangan;
