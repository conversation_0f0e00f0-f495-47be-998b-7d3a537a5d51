import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, FormHelperText, Typography } from "@mui/material";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import {
  CitizenshipStatus,
  ListGender,
  IdTypes,
  OrganisationPositionsAhli,
  AhliList,
} from "../../../../helpers/enums";
import Input from "../../../../components/input/Input";
import { ListGelaran } from "../../../../helpers/enums";
import { autoGenderSetByIC, getLocalStorage } from "@/helpers";
import dayjs from "dayjs";
import { useICValidation } from "@/helpers/hooks/useICValidation.ts";

const DaftarAhli: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { id: societyId, memberId } = useParams();

  const isMyLanguage = i18n.language === "my";

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const [similarId, setsimilarId] = useState(false);
  const memberData = location.state?.row;
  const isEdit = location.state?.isEdit;
  const isManager = location?.state?.isManager;
  const isAliranModuleAccess = location?.state?.isAliranModuleAccess;
  const userDetail = getLocalStorage("user-details", {});

  useEffect(() => {
    if (userDetail?.identificationNo === memberId) {
      setsimilarId(true);
    } else {
      setsimilarId(false);
    }
  }, [userDetail]);

  const [titleCode, setTitleCode] = useState(
    memberData ? memberData?.titleCode : ""
  );
  const [titleCodeError, setTitleCodeError] = useState("");
  const [fullName, setFullName] = useState(memberData ? memberData?.name : "");
  const [fullNameError, setFullNameError] = useState("");
  const [idType, setIdType] = useState(
    memberData ? memberData?.identificationType : ""
  );
  const [idTypeError, setIdTypeError] = useState("");
  const [identificationNo, setIdentificationNo] = useState(
    memberData ? memberData?.identificationNo : ""
  );
  const [idNumberError, setIdNumberError] = useState("");
  const [gender, setGender] = useState(memberData ? memberData?.gender : "");
  const [genderError, setGenderError] = useState("");
  const [citizen, setCitizen] = useState(
    memberData ? parseInt(memberData?.nationalityStatus) : 1
  );
  const [citizenError, setCitizenError] = useState("");
  const [email, setEmail] = useState(memberData ? memberData?.email : "");
  const [emailError, setEmailError] = useState("");
  const [phoneNumber, setPhoneNumber] = useState(
    memberData ? memberData?.phoneNumber : ""
  );
  const [phoneNumberError, setPhoneNumberError] = useState("");
  const [membershipNo, setMembershipNo] = useState(
    memberData ? memberData?.membershipNo : ""
  );
  const [membershipNoError, setMembershipNoError] = useState("");
  const [membershipRegistrationDate, setMemberRegistrationDate] = useState(
    memberData ? memberData?.membershipRegistrationDate : ""
  );
  const [designationCode, setDesignationCode] = useState(
    memberData ? memberData?.designationCode : ""
  );
  const [telephoneNumber, setTelephoneNumber] = useState(
    memberData ? memberData?.telephoneNumber : ""
  );

  const { mutate: registerMember } = useCreate();
  const { mutate: editMember, isLoading } = useCustomMutation();

  const [JPNError, setJPNError] = useState(false);

  const {
    userICCorrect,
    userNameMatchIC,
    triggerICValidation,
    setUserICCorrect,
    setUserNameMatchIC,
    resetICValidation,
    integrationStatus,
  } = useICValidation({
    idType,
    idNumber: identificationNo,
    fullName,
  });

  useEffect(() => {
    const isMyKad = Number(idType) === 1 || Number(idType) === 4;
    const nameReady = fullName?.trim() !== "";
    const idReady = identificationNo?.length === 12;
    if (integrationStatus === 0 && isMyKad) {
      setJPNError(true);
    } else {
      setJPNError(false);
    }
    if (integrationStatus === 1 && isMyKad && nameReady && idReady) {
      triggerICValidation();
    }
  }, [idType, identificationNo, fullName, integrationStatus]);

  const handleRegisterMember = () => {
    let validated = validateEmail();

    if (
      (Number(idType) === 1 && identificationNo?.length < 12) ||
      identificationNo?.length < 1
    ) {
      setIdNumberError("invalidIdNumber");
    } else {
      setIdNumberError("");
    }
    if (!titleCode) {
      setTitleCodeError(t("validation.required"));
      validated = false;
    }
    if (!fullName) {
      setFullNameError(t("validation.required"));
      validated = false;
    }
    if (!idType) {
      setIdTypeError(t("validation.required"));
      validated = false;
    }
    if (!identificationNo) {
      setIdNumberError(t("validation.required"));
      validated = false;
    }
    if (!gender) {
      setGenderError(t("validation.required"));
      validated = false;
    }
    if (!citizen) {
      setCitizenError(t("validation.required"));
      validated = false;
    }
    if (!phoneNumber) {
      setPhoneNumberError(t("validation.required"));
      validated = false;
    }
    if (!membershipNo) {
      setMembershipNoError(t("validation.required"));
      validated = false;
    }
    if (!validated) {
      return;
    }
    setTitleCodeError("");
    setFullNameError("");
    setIdTypeError("");
    setIdNumberError("");
    setGenderError("");
    setCitizenError("");
    setPhoneNumberError("");

    if (memberId) {
      editMember(
        {
          url: `${API_URL}/society/${societyId}/members/${memberId}/edit`,
          method: "put",
          values: {
            titleCode: titleCode,
            name: fullName,
            identificationType: idType,
            identificationNo: identificationNo,
            gender: gender,
            nationalityStatus: citizen,
            email: email,
            phoneNumber: phoneNumber,
            telephoneNumber: telephoneNumber,
            membershipRegistrationDate: membershipRegistrationDate,
            designationCode: designationCode,
            membershipNo: membershipNo,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            navigate(-1);
            if (data?.data?.status === "SUCCESS") {
              return {
                message: isMyLanguage
                  ? "Ahli berjaya dikemaskini"
                  : "Committee Update Successfully",
                type: "success",
              };
            } else {
              return {
                message: data?.data?.msg
                  ? t(data?.data?.msg)
                  : isMyLanguage
                  ? "Pengguna Sudah Berada Dalam Masyarakat Ini"
                  : "User Already In This Society",
                type: "error",
              };
            }
          },
          errorNotification: (data) => {
            return {
              message: t(data?.response?.data?.msg),
              type: "error",
            };
          },
        },
        {
          onError(error, variables, context) {
            console.log(error);
          },
        }
      );
    } else {
      registerMember({
        resource: `society/${societyId}/members/register`,
        values: {
          titleCode: titleCode,
          name: fullName,
          identificationType: idType,
          identificationNo: identificationNo,
          gender: gender,
          nationalityStatus: citizen,
          email: email,
          phoneNumber: phoneNumber,
          telephoneNumber: telephoneNumber,
          membershipRegistrationDate: membershipRegistrationDate,
          designationCode: designationCode,
          membershipNo: membershipNo,
        },
        meta: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status === "SUCCESS") {
            navigate(-1);
            return {
              message: isMyLanguage
                ? "Ahli Berjaya Mendaftar"
                : "Member Registered Successfully",
              type: "success",
            };
          } else {
            if (data?.data?.status === "SUCCESS") {
              return {
                message: isMyLanguage
                  ? "Ahli berjaya dikemaskini"
                  : "Committee Update Successfully",
                type: "success",
              };
            } else {
              return {
                message: data?.data?.msg
                  ? t(data?.data?.msg)
                  : isMyLanguage
                  ? "Pengguna Sudah Berada Dalam Masyarakat Ini"
                  : "User Already In This Society",
                type: "error",
              };
            }
          }
        },
        errorNotification: (data) => {
          return {
            message: t(data?.response?.data?.msg),
            type: "error",
          };
        },
      });
    }
  };

  const validateEmail = () => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError(t("emailRequired"));
      return false;
    }
    if (!regex.test(email)) {
      setEmailError(t("invalidEmail"));
      return false;
    }
    setEmail(email);
    setEmailError("");
    return true;
  };

  const [idTypeTranslatedList, setIdTypeTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    const newOList = IdTypes.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    console.log(newOList);
    setIdTypeTranslatedList(newOList);
  }, [t]);

  const [isAllow, setIsAllow] = useState(false);

  useEffect(() => {
    setIsAllow((isAliranModuleAccess || isManager || similarId) && isEdit);
  }, [isAliranModuleAccess, isManager, similarId, isEdit]);

  const [designationCodeTranslatedList, setDesignationCodeTranslatedList] =
    useState<{ value: number; label: string }[]>([]);

  useEffect(() => {
    const validLabels = [
      "ahliBiasa",
      "ahliBersekutu",
      "ahliKehormat",
      "seumurHidup",
      "ahliRemaja",
    ];

    const newPList = OrganisationPositionsAhli.filter((item) =>
      validLabels.includes(item.label)
    ).map((item) => ({
      ...item,
      label: t(item.label),
    }));

    setDesignationCodeTranslatedList(newPList);
  }, [t]);

  useEffect(() => {
    const type = Number(idType);
    if (type === 1 || type === 4) {
      setGender(autoGenderSetByIC(type, memberData?.gender, identificationNo));
    }
  }, [identificationNo]);

  useEffect(() => {
    const type = Number(idType);
    if (type === 1 || !type) {
      setCitizen(1);
    } else {
      setCitizen(2);
    }
  }, [idType]);

  const [designationBasedDisable, setDesignationBasedDisable] = useState(true);

  useEffect(() => {
    if (memberData?.designationCode) {
    }
    if (
      !memberData?.designationCode ||
      AhliList.includes(Number(memberData?.designationCode))
    ) {
      setDesignationBasedDisable(false);
    }
  }, [memberData?.designationCode]);

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("maklumatPeribadiAhli")}
          </Typography>
          {JPNError ? (
            <Box sx={{ mt: 2, mb: 2 }}>
              <FormHelperText sx={{ color: "var(--error)" }}>
                {t("JPNError")}
              </FormHelperText>
            </Box>
          ) : null}
          <Input
            //textColor="#666666"
            value={titleCode}
            onChange={(e) => {
              setTitleCodeError("");
              setTitleCode(e.target.value);
            }}
            required
            disabled={!isAllow || designationBasedDisable}
            label={t("gelaran")}
            options={ListGelaran.map((item) => ({
              ...item,
              label: t(item.label),
            }))}
            type="select"
            error={!!titleCodeError}
            helperText={titleCodeError}
          />
          <Input
            //textColor="#666666"
            value={fullName}
            onChange={(e) => {
              setFullNameError("");
              setFullName(e.target.value);
            }}
            required
            disabled={!isAllow || JPNError || designationBasedDisable}
            label={t("fullName")}
            error={
              !!fullNameError ||
              ((Number(idType) === 1 || Number(idType) === 4) &&
                identificationNo?.length === 12 &&
                fullName === "") ||
              !userNameMatchIC
            }
            helperText={
              (Number(idType) === 1 || Number(idType) === 4) &&
              identificationNo?.length === 12
                ? fullName?.trim() === ""
                  ? t("fieldRequired")
                  : !userNameMatchIC
                  ? t("invalidName")
                  : undefined
                : undefined
            }
          />
          <Input
            value={idType}
            disabled={!!memberId || !isAllow || designationBasedDisable}
            onChange={(e) => {
              if (e.target.value === "1" || e.target.value === "4") {
                setIdentificationNo("");
                setIdType(e.target.value);
                // Auto-select Warganegara when MyKad is selected
                setCitizen(1);
              } else {
                setUserICCorrect(true);
                setUserNameMatchIC(true);
                setIdType(e.target.value);
                // Auto-select Bukan warganegara when other ID types are selected
                setCitizen(2);
              }
            }}
            required
            type="select"
            options={IdTypes.filter(
              (item) =>
                Number(item.value) === 1 ||
                Number(item.value) === 4 ||
                Number(item.value) === 5
            ).map((item) => ({
              ...item,
              label: t(item.label),
            }))}
            label={t("idType")}
            error={!!idTypeError}
            helperText={idTypeError}
          />
          <Input
            value={identificationNo}
            disabled={
              !!memberId || !isAllow || JPNError || designationBasedDisable
            }
            onChange={(e) => {
              setIdNumberError("");
              resetICValidation();
              if (Number(idType) === 1 || Number(idType) === 4) {
                const numericValue = e.target.value.replace(/[^0-9]/g, "");
                if (numericValue.length <= 12) {
                  setIdentificationNo(numericValue);
                }
              } else {
                setIdentificationNo(e.target.value);
              }
            }}
            required
            label={t("idNumber")}
            error={
              !!idNumberError ||
              !userICCorrect ||
              ((Number(idType) === 1 || Number(idType) === 4) &&
                identificationNo?.length < 12)
            }
            helperText={
              idNumberError || !userICCorrect
                ? t("IcDoesNotExist")
                : (Number(idType) === 1 || Number(idType) === 4) &&
                  identificationNo?.length < 12
                ? t("idNumberOnlyDigits")
                : undefined
            }
            inputProps={{
              inputMode: "numeric",
              pattern: "[0-9]*",
              required: true,
            }}
          />
          <Input
            value={gender}
            required
            onChange={(e) => setGender(e.target.value)}
            type="select"
            options={ListGender.map((gender) => ({
              ...gender,
              label: t(gender.label),
            }))}
            disabled={!isAllow || designationBasedDisable}
            label={t("gender")}
            error={!!genderError}
            helperText={genderError}
          />
          <Input
            value={citizen}
            disabled={true}
            // onChange={(e) => setCitizen(e.target.value)}
            onChange={(e) => {
              if (e.target.value === 1) {
                setIdentificationNo("");
                setCitizen(e.target.value);
              } else {
                setUserICCorrect(true);
                setUserNameMatchIC(true);
                setCitizen(e.target.value);
              }
            }}
            required
            type="select"
            options={CitizenshipStatus.map((item) => ({
              ...item,
              label: t(item.label), // Apply translation on the label
            }))}
            label={t("citizen")}
            error={!!citizenError}
            helperText={citizenError}
          />
          <Input
            //textColor="#666666"
            value={email}
            onChange={(e) => {
              setEmailError("");
              setEmail(e.target.value);
            }}
            required
            type="email"
            label={t("email")}
            disabled={!isAllow || designationBasedDisable}
            error={!!emailError}
            helperText={emailError}
          />
          <Input
            value={phoneNumber}
            required
            label={t("phoneNumber")}
            error={!!phoneNumberError}
            disabled={!isAllow || designationBasedDisable}
            helperText={phoneNumberError}
            inputProps={{
              inputMode: "numeric",
              maxLength: 15,
            }}
            onChange={(e) => {
              const type = Number(idType);
              const input = e.target as HTMLInputElement;
              let raw = input.value.replace(/[^\d]/g, "");
              if (type === 1) {
                // Allow empty input
                if (!raw) {
                  setPhoneNumber(null);
                  return;
                }

                // Remove leading 60 if present
                if (raw.startsWith("60")) {
                  raw = raw.slice(2);
                }

                const limitedDigits = raw.slice(0, 10);
                const formatted = "+60" + limitedDigits;

                let error = "";
                if (limitedDigits.length < 8) {
                  error = t("phoneDigitLimitWarning");
                }

                setPhoneNumber(formatted);
                setPhoneNumberError(error);
                // setPhoneNumber(error);
              } else {
                const numericValue = e.target.value.replace(/[^0-9]/g, "");
                setPhoneNumber(numericValue);
              }
            }}
            onKeyDown={(e) => {
              const type = Number(idType);
              if (type === 1) {
                const input = e.target as HTMLInputElement;
                const pos = input.selectionStart ?? 0;
                const hasValue = input.value.length > 0;

                // restrictions
                if (hasValue) {
                  if (
                    (e.key.length === 1 && pos < 3) || // typing characters in +60
                    (e.key === "Backspace" && pos <= 3) || // backspacing into +60
                    (e.key === "Delete" && pos < 3) // deleting inside +60
                  ) {
                    e.preventDefault();
                  }
                }
              }
            }}
            onClick={(e) => {
              const type = Number(idType);
              if (type === 1) {
                const input = e.target as HTMLInputElement;
                if (
                  input.value &&
                  input.selectionStart !== null &&
                  input.selectionStart < 3
                ) {
                  // Move cursor to after +60 if user clicks inside prefix
                  setTimeout(() => {
                    input.setSelectionRange(3, 3);
                  }, 0);
                }
              }
            }}
            onFocus={(e) => {
              const type = Number(idType);
              if (type === 1) {
                const input = e.target as HTMLInputElement;
                if (
                  input.value &&
                  input.selectionStart !== null &&
                  input.selectionStart < 3
                ) {
                  // move cursor to after +60 on focus
                  setTimeout(() => {
                    input.setSelectionRange(3, 3);
                  }, 0);
                }
              }
            }}
            placeholder={Number(idType) === 1 ? "+60" : ""}
          />
          <Input
            //textColor="#666666"
            value={membershipNo}
            onChange={(e) => {
              setMembershipNoError("");
              setMembershipNo(e.target.value);
            }}
            required
            label={t("membershipNo")}
            error={!!membershipNoError}
            disabled={!isAllow}
            helperText={membershipNoError}
          />
          <Input
            //textColor="#666666"
            value={
              membershipRegistrationDate
                ? dayjs(membershipRegistrationDate).format("DD-MM-YYYY")
                : ""
            }
            onChange={(e) => setMemberRegistrationDate(e.target.value)}
            disabled={!isAllow}
            type="date"
            label={t("memberApprovalDate")}
          />
          <Input
            //textColor="#666666"
            value={Number(designationCode)}
            onChange={(e) => setDesignationCode(e.target.value)}
            disabled={!isAllow}
            type="select"
            options={designationCodeTranslatedList}
            label={t("memberType")}
          />
        </Box>

        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 2,
            mt: 2,
          }}
        >
          <ButtonOutline onClick={() => navigate(-1)}>
            {t("back")}
          </ButtonOutline>
          {isAllow ? (
            <ButtonPrimary
              disabled={
                JPNError ||
                ([1, 4].includes(Number(idType)) &&
                  (identificationNo?.length < 12 ||
                    fullName?.trim() === "" ||
                    !userICCorrect ||
                    !userNameMatchIC))
              }
              onClick={handleRegisterMember}
            >
              {memberId ? t("update") : t("registerButton")}
            </ButtonPrimary>
          ) : null}
        </Box>
      </Box>
    </>
  );
};

export default DaftarAhli;
