import { createAsyncThunk } from '@reduxjs/toolkit';
import { API_URL } from '../../api';
import { setPermissionsListDataRedux, setPermissionsListError, setPermissionsListLoading } from '../permissionsListDataReducer';

export const fetchPermissionsListData = createAsyncThunk(
  'permissionsList/fetchData',
  async (_, { dispatch }) => {
    dispatch(setPermissionsListLoading(true));
    try {
      const response = await fetch(`${API_URL}/user/pageAccess/getPageTemplate?template=1`, {
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          "Content-Type": "application/json",
        },
      });
      const data = await response.json();

      if (data.data?.errors) {
        dispatch(setPermissionsListDataRedux([]));
      } else {
        dispatch(setPermissionsListDataRedux(data.data));
      }
    } catch (error: any) {
      dispatch(setPermissionsListError(error.message));
    } finally {
      dispatch(setPermissionsListLoading(false));
    }
  }
);
