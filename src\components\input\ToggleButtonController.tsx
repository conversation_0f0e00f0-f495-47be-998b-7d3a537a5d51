import React from "react";
import { useTranslation } from "react-i18next";
import {
  Control,
  Controller,
  FieldValues,
  RegisterOptions,
} from "react-hook-form";
import {
  Box,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from "@mui/material";

interface ToggleButtonControllerProps {
  name: string;
  control: Control<FieldValues>;
  options: { value: string | number; label: string }[];
  required?: boolean;
  rules?: RegisterOptions;
  onChange?: (value: string | null) => void;
  sx?: React.CSSProperties;
  sxButton?: React.CSSProperties;
  sxLabel?: React.CSSProperties;
  disabled?: boolean;
  direction?: "column" | "row";
  toggleGap?: number;
  checkDisabledByValue?: null | ((val: string | number | null) => boolean);
}

export const ToggleButtonController: React.FC<ToggleButtonControllerProps> = ({
  name,
  control,
  options,
  required = false,
  rules,
  onChange,
  sx,
  sxButton,
  sxLabel,
  disabled = false,
  direction = "column",
  toggleGap = 1,
  checkDisabledByValue = null,
}) => {
  const { t } = useTranslation();

  return (
    <Controller
      name={name}
      control={control}
      rules={{
        required: required ? t("validation.required") : undefined,
        ...rules,
      }}
      render={({ field, fieldState: { error } }) => (
        <>
          <ToggleButtonGroup
            value={field.value}
            exclusive
            onChange={(_, value) => {
              if (!disabled) {
                field.onChange(value);
                if (onChange) onChange(value);
              }
            }}
            aria-label={name}
            sx={{ display: "flex", flexDirection: direction, gap: toggleGap }}
            disabled={disabled}
          >
            {options.map((option) => (
              <Box
                key={option.value}
                sx={{ display: "flex", gap: 2, alignItems: "center", ...sx }}
              >
                <ToggleButton
                  value={option.value}
                  aria-label={option.value as string}
                  disabled={checkDisabledByValue?.(option.value) ?? disabled}
                  sx={{
                    flexShrink: 0,
                    padding: 0,
                    margin: "0 !important",
                    width: "12px",
                    height: "12px",
                    border: "1px solid #848484 !important",
                    borderRadius: "2px",
                    "&.Mui-selected": {
                      backgroundColor: "var(--primary-color)",
                      border: "1px solid var(--primary-color) !important",
                      color: "white",
                      "&:hover": { backgroundColor: "#19ADAD" },
                    },
                    "&:disabled": {
                      backgroundColor: "rgba(0, 0, 0, 0.38)",
                      border: "none !important",
                    },
                    ...sxButton,
                  }}
                />
                <Typography
                  fontSize="12px"
                  color="#666666"
                  sx={{ fontWeight: "400 !important", ...sxLabel }}
                >
                  {option.label}
                </Typography>
              </Box>
            ))}
          </ToggleButtonGroup>
          {error && (
            <Typography
              variant="caption"
              fontWeight="400 !important"
              color="error"
            >
              {error.message}
            </Typography>
          )}
        </>
      )}
    />
  );
};

ToggleButtonController.displayName = "ToggleButtonController";

export default ToggleButtonController;
