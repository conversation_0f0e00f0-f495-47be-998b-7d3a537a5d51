import { Navigate, Outlet, Route } from "react-router-dom";
import { RouteGuard } from "../../components/RouteGuard";
import { registerRoutes } from "../../helpers/routeDetector";
import PaparanLayout from "../../pages/pertubuhan/pengurusan-pertubuhan/PaparanLayout";
import CreateMam from "../../pages/pertubuhan/pernyata-tahunan/maklumat-am";
import CreateMesyuaratAgung from "../../pages/pertubuhan/pernyata-tahunan/maklumat-mesyuarat";
import UpdateMam from "../../pages/pertubuhan/pengurusan-pertubuhan/maklumat-am/UpdateMam";
import ListPenyata from "../../pages/pertubuhan/pernyata-tahunan/ListPenyata";
import ListMaklumatAjk from "../../pages/pertubuhan/pernyata-tahunan/maklumat-ajk/ListMaklumatAjk";
import CreateAjk from "../../pages/pertubuhan/pernyata-tahunan/maklumat-ajk/CreateAjk";
import ListCawangan from "../../pages/pertubuhan/pengurusan-cawangan/ListCawangan";
import MainListCawangan from "../../pages/pertubuhan/pengurusan-cawangan/MainListCawangan";
import { CreatePernyataPendapatan } from "../../pages/pertubuhan/pernyata-tahunan/pernyata-pendapatan";
import { CreateAsetDanLiabiliti } from "../../pages/pertubuhan/pernyata-tahunan/aset-dan-liabiliti";
import AddCawangan from "../../pages/pertubuhan/pengurusan-cawangan/maklumat-am";
import MinuteMesyuarat from "../../pages/pertubuhan/pengurusan-cawangan/minute-mesyuarat";
import AhliJawatanKuasa from "../../pages/pertubuhan/pengurusan-cawangan/ahli-jawatankuasa";
import CreateAjkCawangan from "../../pages/pertubuhan/pengurusan-cawangan/ahli-jawatankuasa/CreateAjk";
import CreateAjkBukanWnCawangan from "../../pages/pertubuhan/pengurusan-cawangan/ahli-jawatankuasa/CreateAjkBukanWn";
import DokumenSokongan from "../../pages/pertubuhan/pengurusan-cawangan/dokumen-sokongan";
import ListMesyuarat from "../../pages/pertubuhan/mesyuarat/ListMesyuarat";
import CreateMesyuarat from "../../pages/pertubuhan/mesyuarat/add-mesyuarat";
import { IndexSumbanganNegara } from "../../pages/pertubuhan/pernyata-tahunan/sumbangan-negara";
import ListPembubaran from "../../pages/pertubuhan/pembubaran/ListPembubaran";
import CreatePembubaran from "../../pages/pertubuhan/pembubaran/add-pembubaran/CreatePembubaran";
import ListRayuan from "../../pages/pertubuhan/rayuan/ListRayuan";
import CreateRayuan from "../../pages/pertubuhan/rayuan/add-rayuan/CreateRayuan";
import ListBayaran from "../../pages/pertubuhan/rayuan/bayaran/ListBayaran";
import CreateLanjutMasa from "../../pages/pertubuhan/pengurusan-cawangan/lanjut-masa/CreateLanjutMasa";
import RegisterCawangan from "../../pages/pertubuhan/pengurusan-cawangan/RegisterCawangan";
import { Online } from "../../pages/pertubuhan/rayuan/bayaran/Online";
import Kaunter from "../../pages/pertubuhan/rayuan/bayaran/Kaunter";
import { Butiran } from "../../pages/pertubuhan/rayuan/bayaran/Butiran";
import Term from "../../pages/pertubuhan/rayuan/bayaran/Term";
import ViewAjkBukanWnCawangan from "@/pages/pertubuhan/pengurusan-cawangan/ahli-jawatankuasa/ViewAjkBukanWn";
import ViewRayuan from "@/pages/pertubuhan/rayuan/view-rayuan/ViewRayuan";
import CreateLaporanAktiviti from "@/pages/pertubuhan/pernyata-tahunan/laporan-aktiviti/CreateLaporanAktiviti";

// Layout component to wrap all paparan pertubuhan routes with protection
const PaparanPertubuhanLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === "development"}
  >
    <PaparanLayout>
      <Outlet />
    </PaparanLayout>
  </RouteGuard>
);

// Register routes with their portal types
registerRoutes({
  // Add your route registrations here
  //Appeal
  "/pertubuhan/paparan-pertubuhan/rayuan/list-data": "external",
  "/pertubuhan/paparan-pertubuhan/rayuan/view": "external",
  "/pertubuhan/paparan-pertubuhan/rayuan/add-rayuan": "external",
  "/pertubuhan/paparan-pertubuhan/rayuan/bayaran": "external",
  "/pertubuhan/paparan-pertubuhan/rayuan/bayaran/kaunter": "external",
  "/pertubuhan/paparan-pertubuhan/rayuan/view/online": "external",
  "/pertubuhan/paparan-pertubuhan/rayuan/view/online/term": "external",
  "/pertubuhan/paparan-pertubuhan/rayuan/view/online/butiran": "external",

  //Branch
  "/pertubuhan/paparan-pertubuhan/cawangan/main-list-cawangan": "external",
  "/pertubuhan/paparan-pertubuhan/cawangan/register-cawangan": "external",
  "/pertubuhan/paparan-pertubuhan/cawangan/maklumat-am": "external",
  "/pertubuhan/paparan-pertubuhan/cawangan/lanjut-masa/create": "external",
});

export const paparanPertubuhan = {
  routes: (
    <>
      <Route path="paparan-pertubuhan" element={<PaparanPertubuhanLayout />}>
        <Route index element={<Navigate to="paparan/maklumat-am-update" />} />

        <Route path="paparan">
          <Route path="maklumat-am-update">
            <Route index element={<UpdateMam />} />
          </Route>
        </Route>

        <Route path="cawangan">
          <Route path="main-list-cawangan">
            <Route index element={<MainListCawangan />} />
          </Route>

          <Route path="list-cawangan">
            <Route index element={<ListCawangan />} />
          </Route>

          <Route path="register-cawangan">
            <Route index element={<RegisterCawangan />} />
          </Route>

          <Route path="maklumat-am">
            <Route index element={<AddCawangan />} />
          </Route>

          <Route path="minute-mesyuarat">
            <Route index element={<MinuteMesyuarat />} />
          </Route>

          <Route path="ahlijawatankuasa">
            <Route index element={<AhliJawatanKuasa />} />
          </Route>

          <Route path="create-ajk">
            <Route index element={<CreateAjkCawangan />} />
          </Route>

          <Route path="create-ajk-bukan-wn">
            <Route index element={<CreateAjkBukanWnCawangan />} />
          </Route>

          <Route path="view-ajk-bukan-wn">
            <Route index element={<ViewAjkBukanWnCawangan />} />
          </Route>

          <Route path="dokumen-sokongan">
            <Route index element={<DokumenSokongan />} />
          </Route>

          <Route path="lanjut-masa">
            <Route path="create">
              <Route index element={<CreateLanjutMasa />} />
            </Route>
          </Route>
        </Route>

        <Route path="mesyuarat">
          <Route path="list-data">
            <Route index element={<ListMesyuarat />} />
          </Route>

          <Route path="add-mesyuarat">
            <Route index element={<CreateMesyuarat />} />
          </Route>
        </Route>

        <Route path="pernyata">
          <Route path="list-penyata-tahunan">
            <Route index element={<ListPenyata />} />
          </Route>

          <Route path="maklumat-am">
            <Route index element={<CreateMam />} />
          </Route>

          <Route path="maklumat-mesyuarat-agung">
            <Route index element={<CreateMesyuaratAgung />} />
          </Route>

          <Route path="maklumat-ajk">
            <Route index element={<ListMaklumatAjk />} />
          </Route>

          <Route path="create-ajk">
            <Route index element={<CreateAjk />} />
          </Route>

          <Route path="pernyata-pendapatan">
            <Route index element={<CreatePernyataPendapatan />} />
          </Route>

          {/* <Route path="aset-liabiliti">
            <Route index element={<CreateAsetDanLiabiliti />} />
          </Route> */}

          <Route path="laporan-aktiviti">
            <Route index element={<CreateLaporanAktiviti />} />
          </Route>

          <Route path="sumbangan">
            <Route index element={<IndexSumbanganNegara />} />
          </Route>
        </Route>

        <Route path="pembubaran">
          <Route path="list-data">
            <Route index element={<ListPembubaran />} />
          </Route>

          <Route path="add-pembubaran">
            <Route index element={<CreatePembubaran />} />
          </Route>
        </Route>

        <Route path="rayuan">
          <Route path="list-data">
            <Route index element={<ListRayuan />} />
          </Route>

          <Route path="add-rayuan">
            <Route index element={<CreateRayuan />} />
          </Route>

          <Route path="view">
            <Route index element={<ViewRayuan />} />
          </Route>

          <Route path="bayaran">
            <Route index element={<ListBayaran />} />

            <Route path="kaunter" element={<Kaunter />} />

            <Route path="online">
              <Route index element={<Online />} />

              <Route path="term" element={<Term />} />

              <Route path="butiran" element={<Butiran />} />
            </Route>
          </Route>
        </Route>
      </Route>
    </>
  ),
};
