import {
  <PERSON>,
  Typography,
  Grid,
  useMedia<PERSON><PERSON><PERSON>,
  <PERSON>,
  Button,
  useTheme,
  IconButton,
} from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-leaflet";

import DisabledTextField from "../../../components/input/DisabledTextField";
import Input from "../../../components/input/Input";

import { useBackendLocalization } from "../../../helpers/hooks/useBackendLocalization";
import useQuery from "@/helpers/hooks/useQuery";
import { EyeIcon } from "@/components/icons";
import {
  MeetingContent,
  MeetingMethods,
  MeetingTypeOption,
} from "../../../helpers/enums";

import {
  SocietyAddressListResponseBodyGet,
  SocietyMeetingListResponseBodyGet,
  SocietyMeetingResponseBodyGet,
} from "../../../models";
import { capitalizeWords, DocumentUploadType } from "@/helpers";
import { FormMeetingDateTime } from "@/components/form/meeting/DateTime";
import dayjs from "@/helpers/dayjs";
import { FormMeetingAttendees } from "@/components/form/meeting/Attendees";
import { getAliranTugasAccess } from "@/redux/userReducer";
import { useSelector } from "react-redux";
import { useState } from "react";
import { FileUploader } from "@/components";

export const ViewMesyuarat = <
  MeetingData extends SocietyMeetingResponseBodyGet = SocietyMeetingResponseBodyGet,
  MeetingListData extends SocietyMeetingListResponseBodyGet = SocietyMeetingListResponseBodyGet,
  AddressListData extends SocietyAddressListResponseBodyGet = SocietyAddressListResponseBodyGet
>() => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { meetingId } = useParams<{ meetingId: string }>();
  const params = useParams();
  const societyId: string = params.id || "";
  const navigate = useNavigate();

  const isAliranTugasAccess = useSelector(getAliranTugasAccess);

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const { data: meetingResponse, isLoading: isMeetingResponseLoading } =
    useQuery<{ data: MeetingData }>({
      url: `society/meeting/${meetingId}`,
    });
  const { data: meetingListResponse } = useQuery<{ data: MeetingListData[] }>({
    url: "society/admin/meeting/list",
  });
  const { data: addressListResponse } = useQuery<{ data: AddressListData[] }>({
    url: "society/admin/address/list",
  });

  const [fileName, setFileName] = useState<string>("");
  const [fileUrl, setFileUrl] = useState<string>("");
  const { getTranslation } = useBackendLocalization<MeetingListData>();
  const meetingData = meetingResponse?.data?.data ?? null;
  const meetingListData = meetingListResponse?.data?.data ?? [];
  const addressData = addressListResponse?.data?.data ?? [];
  // const MeetingTypeSelected = MeetingTypeOption.map((option) => ({
  //   label: option.label,
  //   value: option.label,
  // }));
  const getMeetingLabel = (value: number): string => {
    const meeting = MeetingTypeOption.find(
      (option) => Number(option.value) === Number(value)
    );
    return meeting ? meeting.label : "Unknown Meeting Type";
  };
  // const meetingType = meetingData?.meetingType
  //   ? MeetingTypeOption?.find((val) => {
  //       if (!Number.isNaN(parseInt(meetingData.meetingContent))) {
  //         return val.value == parseInt(meetingData.meetingType);
  //       }
  //       return val.label === meetingData.meetingType;
  //     }) ?? null
  //   : null;
  const meetingMethodList =
    meetingListData?.find((item) =>
      meetingData?.meetingMethod
        ? parseInt(meetingData.meetingMethod) === item.id
        : false
    ) ?? null;
  const meetingMethod = getTranslation(meetingMethodList);
  const platformType =
    meetingData?.platformType?.length === 1
      ? null
      : meetingListData?.find((item) =>
          meetingData?.platformType
            ? parseInt(meetingData.platformType) === item.id
            : false
        ) ?? null;
  const state = meetingData?.state
    ? addressData?.find((item) => item.id === parseInt(meetingData.state)) ??
      null
    : null;
  const district = meetingData?.district
    ? addressData?.find((item) => item.id === parseInt(meetingData.district)) ??
      null
    : null;
  const primaryColor = theme.palette.primary.main;

  const sectionStyle = {
    color: primaryColor,
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const { refetch: fetchDocuments, isLoading: fetchDocumentsIsLoading } =
    useQuery({
      url: `society/document/documentByParam`,
      filters: [
        { field: "societyId", operator: "eq", value: societyId },
        { field: "meetingId", operator: "eq", value: meetingId },
        {
          field: "type",
          operator: "eq",
          value: DocumentUploadType.MEETING,
        },
      ],
      onSuccess: (data) => {
        const fileInfo = data?.data?.data?.[0];
        if (fileInfo) {
          setFileName(fileInfo?.name);
          setFileUrl(fileInfo?.url);
        }
      },
    });

  const downloadFile = (filePath: string) => {
    window.open(filePath, "_blank");
  };

  return (
    <>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px" }}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("maklumatMesyuarat")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t("meetingType")}</Typography>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <DisabledTextField
                value={
                  meetingData?.meetingType
                    ? getMeetingLabel(Number(meetingData?.meetingType))
                    : "-"
                }
              />
            </Grid>

            <Grid item sm={12}>
              <Input
                value={meetingMethod}
                name="meetingMethod"
                disabled
                options={[{ value: meetingMethod, label: meetingMethod }]}
                type="select"
                label={t("meetingMethod")}
                required
                selectStyleProfileId={2}
              />
            </Grid>

            {meetingMethodList &&
              meetingData?.meetingMethod &&
              [MeetingMethods.ATAS_TALIAN, MeetingMethods.HYBRID].includes(
                // @ts-expect-error
                meetingData.meetingMethod
              ) &&
              platformType && (
                <Grid item sm={12}>
                  <Input
                    value={getTranslation(platformType)}
                    name="platformType"
                    disabled
                    options={[
                      {
                        value: getTranslation(platformType),
                        label: getTranslation(platformType),
                      },
                    ]}
                    type="select"
                    label={t("platformType")}
                    required
                    selectStyleProfileId={2}
                  />
                </Grid>
              )}
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("tujuanMesyuarat")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <DisabledTextField
                styleProfileId={2}
                value={meetingData?.meetingPurpose ?? "-"}
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("masaDanTarikhMesyuarat")}
          </Typography>
          <FormMeetingDateTime
            viewOnly
            meetingTimeFromAttribute="meetingTime"
            defaultValues={{
              meetingDate: meetingData?.meetingDate
                ? dayjs(meetingData.meetingDate)
                : null,
              meetingTime: meetingData?.meetingDate
                ? dayjs(
                    `${meetingData.meetingDate} ${
                      meetingData?.meetingTime ?? "00:00:00"
                    }`,
                    "YYYY-MM-DD HH:mm:[00]"
                  )
                : null,
              meetingTimeTo: meetingData?.meetingDate
                ? dayjs(
                    `${meetingData.meetingDate} ${
                      meetingData?.meetingTimeTo ?? "00:00:00"
                    }`,
                    "YYYY-MM-DD HH:mm:[00]"
                  )
                : null,
            }}
          />
        </Box>
        {meetingMethodList &&
        meetingData?.meetingMethod &&
        meetingData.meetingMethod == MeetingMethods.ATAS_TALIAN ? null : (
          <>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mb: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("alamatTempatMesyuarat")}
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("namaTempatMesyuarat")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <DisabledTextField
                    styleProfileId={2}
                    value={meetingData?.meetingPlace ?? "-"}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("locationMap")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Box>
                    <MapContainer
                      /**
                       * @todo change meetingLocation with data from backend
                       */
                      center={[2.745564, 101.707021]}
                      zoom={13}
                      style={{
                        height: "10rem",
                        width: "100%",
                        borderRadius: "0.5rem",
                      }}
                    >
                      <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                      {/**
                       * @todo change meetingLocation with data from backend
                       */}
                      <Marker position={[2.745564, 101.707021]} />
                    </MapContainer>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("meetingPlaceAddress")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <DisabledTextField
                    styleProfileId={2}
                    value={meetingData?.meetingPlace ?? "-"}
                  />
                </Grid>

                <Grid item sm={12}>
                  <Input
                    value={state?.name ?? "-"}
                    name="state"
                    disabled
                    options={[
                      {
                        value: state?.name ?? "-",
                        label: state?.name ? capitalizeWords(state.name) : "-",
                      },
                    ]}
                    type="select"
                    label={t("state")}
                    required
                    selectStyleProfileId={2}
                  />
                </Grid>

                <Grid item sm={12}>
                  <Input
                    value={district?.name ?? "-"}
                    name="district"
                    disabled
                    options={[
                      {
                        value: district?.name ?? "-",
                        label: district?.name
                          ? capitalizeWords(district.name)
                          : "-",
                      },
                    ]}
                    type="select"
                    label={t("district")}
                    required
                    selectStyleProfileId={2}
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("city")}</Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <DisabledTextField
                    styleProfileId={2}
                    value={meetingData?.city ?? "-"}
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("postcode")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <DisabledTextField
                    styleProfileId={2}
                    value={meetingData?.postcode ?? "-"}
                  />
                </Grid>
              </Grid>
            </Box>
          </>
        )}
        {!isMeetingResponseLoading ? (
          <FormMeetingAttendees
            defaultValues={{
              totalAttendees: meetingData?.totalAttendees
                ? meetingData.totalAttendees
                : 7,
            }}
            viewOnly
          />
        ) : null}

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("minitMesyuarat")}
          </Typography>

          {fileUrl ? (
            <Grid container spacing={2} marginBottom={1} alignItems={"center"}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("minitMesyuarat")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <FileUploader
                  societyId={parseInt(societyId)}
                  meetingId={parseInt(meetingId ?? '0')}
                  type={DocumentUploadType.MEETING}
                  validTypes={[]}
                  disabled
                />
              </Grid>
            </Grid>
          ) : null}
        </Box>
        <Grid container spacing={2}>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <Button
              size="medium"
              variant="contained"
              onClick={() => navigate(-1)}
              sx={{ minWidth: "12rem", textTransform: "capitalize" }}
            >
              {t("back")}
            </Button>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default ViewMesyuarat;
