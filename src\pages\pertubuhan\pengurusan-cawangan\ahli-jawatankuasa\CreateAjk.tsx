import React, { useEffect, useState } from "react";
import { Box, FormHelperText, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate, useSearchParams } from "react-router-dom";
import ButtonPrimary from "../../../../components/button/ButtonPrimary";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import {
  CitizenshipStatus,
  IdTypes,
  ListGelaran,
  ListGender,
  MALAYSIA,
  OccupationList,
  OrganisationPositions,
} from "../../../../helpers/enums";
import Input from "../../../../components/input/Input";
import {
  autoDOBSetByIC,
  autoGenderSetByIC,
  capitalizeWords,
  getLocalStorage,
} from "../../../../helpers/utils";
import { DocumentUploadType } from "@/helpers";
import FileUploader from "@/components/input/fileUpload";
import dayjs from "dayjs";
import { useICValidation } from "@/helpers/hooks/useICValidation.ts";
import { phoneNumbers } from "@elevenlabs/elevenlabs-js/api/resources/conversationalAi";

interface CommitteeDetails {
  id: string | number;
  committeeCode: string;
  titleCode: string;
  committeeName: string;
  gender: string;
  citizenshipStatus: string;
  identityType: string;
  committeeIcNo: string;
  dateOfBirth: string;
  placeOfBirth: string;
  designationCode: string;
  committeeAddressStatus: string;
  committeeAddress: string;
  committeeCountryCode: string;
  committeeStateCode: string;
  committeeDistrict: string;
  committeeSmallDistrict: string;
  committeeCity: string;
  postcode: string;
  email: string;
  homePhoneNumber: string;
  phoneNumber: string;
  officePhoneNumber: string;
  committeeEmployerName: string;
  committeeEmployerAddressStatus: string;
  committeeEmployerAddress: string;
  committeeEmployerCountryCode: string;
  committeeEmployerStateCode: string;
  committeeEmployerDistrict: string;
  committeeEmployerCity: string;
  committeeEmployerPostcode: string;
  batalFlat: string;
  applicationStatusCode: string;
  pegHarta: string;
  otherPosition: string;
}

interface FormValues {
  designationCode: number | null;
  pegHarta: any;
  titleCode: any;
  committeeName: any;
  gender: any;
  citizenshipStatus: any;
  identityType: any;
  committeeIcNo: any;
  dateOfBirth: any;
  placeOfBirth: any;
  otherPosition: any;
  committeeAddress: any;
  committeeStateCode: any;
  committeeDistrict: any;
  committeeCity: any;
  postcode: any;
  email: any;
  phoneNumber: any;
  homePhoneNumber: any;
  officePhoneNumber: any;
  committeeEmployerName: any;
  committeeEmployerAddress: any;
  committeeEmployerCountryCode: any;
  committeeEmployerStateCode: any;
  committeeEmployerDistrict: any;
  committeeEmployerCity: any;
  committeeEmployerPostcode: any;
}
interface ListItem {
  value: any;
  label: any;
}

export const CreateAjk: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [errors, setErrors] = useState<{ [key in keyof FormValues]?: string }>(
    {}
  );
  const [searchParams] = useSearchParams();
  const branchId = searchParams.get("id");
  const memberId = searchParams.get("mId");
  const [selectedCommittee, setSelectedCommittee] =
    useState<CommitteeDetails | null>(null);

  const addressList = getLocalStorage("address_list", null);
  const CountryData = addressList
    ?.filter((item: any) => item.level === 0)
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));
  const [districtList, setDistrictList] = useState<ListItem[]>([]);
  const [employerDistrictList, setEmployerDistrictList] = useState<ListItem[]>(
    []
  );
  const StateList = addressList
    ?.filter((item: any) => item.pid === MALAYSIA)
    ?.map((item: any) => ({ value: item.id, label: item.name }));

  const [branchData, setBranchData] = useState<any>({});
  const [ajkId, setAjkId] = useState<string | undefined>(undefined);

  const { data: branchList, isLoading: isLoadingBranch } = useCustom<any>({
    url: `${API_URL}/society/branch/getById/${branchId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: branchId !== null,
      retry: false,
      cacheTime: 0,
    },
  });

  useEffect(() => {
    if (branchList?.data?.data) {
      setBranchData(branchList?.data?.data);
    }
  }, [branchList]);

  const [formValues, setFormValues] = useState<FormValues>({
    designationCode: 1,
    pegHarta: "",
    titleCode: "",
    committeeName: "",
    gender: "",
    citizenshipStatus: 1,
    identityType: "1",
    committeeIcNo: "",
    dateOfBirth: null,
    placeOfBirth: "",
    otherPosition: "",
    committeeAddress: "",
    committeeStateCode: "",
    committeeDistrict: "",
    committeeCity: "",
    postcode: "",
    email: "",
    phoneNumber: "",
    homePhoneNumber: "",
    officePhoneNumber: "",
    committeeEmployerName: "",
    committeeEmployerAddress: "",
    committeeEmployerCountryCode: MALAYSIA,
    committeeEmployerStateCode: "",
    committeeEmployerDistrict: "",
    committeeEmployerCity: "",
    committeeEmployerPostcode: "",
  });

  useEffect(() => {
    if (branchData?.branchCommittees) {
      const committee = branchData.branchCommittees.find(
        (item: CommitteeDetails) => item.id === memberId
      );

      if (committee) {
        setFormValues((prevValues) => ({
          ...prevValues,
          designationCode: Number(committee.designationCode),
          pegHarta: committee.pegHarta,
          titleCode: committee.titleCode,
          committeeName: committee.committeeName,
          gender: committee.gender,
          citizenshipStatus: committee.citizenshipStatus,
          identityType: committee.identityType ?? "1",
          committeeIcNo: committee.committeeIcNo,
          dateOfBirth: committee?.dateOfBirth,
          placeOfBirth: committee.placeOfBirth,
          otherPosition: committee.otherPosition,
          committeeAddress: committee.committeeAddress,
          committeeStateCode: Number(committee.committeeStateCode),
          committeeDistrict: Number(committee.committeeDistrict),
          committeeCity: committee.committeeCity,
          postcode: committee.postcode,
          email: committee.email,
          phoneNumber: committee.phoneNumber,
          homePhoneNumber: committee.homePhoneNumber,
          officePhoneNumber: committee.officePhoneNumber,
          committeeEmployerName: committee.committeeEmployerName,
          committeeEmployerAddress: committee.committeeEmployerAddress,
          committeeEmployerCountryCode:
            Number(committee.committeeEmployerCountryCode) ?? MALAYSIA,
          committeeEmployerStateCode: Number(
            committee.committeeEmployerStateCode
          ),
          committeeEmployerDistrict: committee.committeeEmployerDistrict,
          committeeEmployerCity: committee.committeeEmployerCity,
          committeeEmployerPostcode: committee.committeeEmployerPostcode,
        }));
      }
      setSelectedCommittee(committee || null);
    }
  }, [branchData, memberId]);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "32px",
    borderRadius: "16px",
    fontSize: "16px",
    fontWeight: "500 !important",
  };
  const handleBack = () => {
    navigate(-1);
  };

  const handleChange = (e: any) => {
    let { name, value } = e.target;

    if (name === "committeeIcNo" && !/^\d*$/.test(value)) return; // Allow only numbers
    if (name === "committeeIcNo" && value.length > 12) return; // Limit to 12 digits

    if (name === "dateOfBirth") {
      value = dayjs(value).format("YYYY-MM-DD");
    }
    if (name === "postcode" || name === "committeeEmployerPostcode") {
      if (!/^\d*$/.test(value) || value.length > 5) {
        return;
      }

      setFormValues({
        ...formValues,
        [name!]: value,
      });
      if (value.length > 0 && value.length < 5) {
        setErrors({
          ...errors,
          [name!]: "Postcode must be exactly 5 digits",
        });
      } else {
        setErrors({
          ...errors,
          [name!]: "",
        });
      }
      return;
    }

    setFormValues({
      ...formValues,
      [name!]: value as string,
    });
  };

  const { mutate: edit, isLoading: isLoadingEdit } = useCustomMutation();
  const { mutate: create, isLoading: isLoadingCreate } = useCustomMutation();

  const CreateBranchCommittee: (filteredData: any) => void = (filteredData) => {
    create(
      {
        url: `${API_URL}/society/branch/committee/create`,
        method: "post",
        values: {
          ...filteredData,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          setErrors({});
          const ajkId = data?.data?.data?.id;
          setAjkId(ajkId);
          setTimeout(() => {
            setErrors({});
            navigate(`../ahlijawatankuasa?id=${branchId}`);
          }, 5000);
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const EditBranchCommittee: (filteredData: any) => void = (filteredData) => {
    edit(
      {
        url: `${API_URL}/society/branch/committee/${memberId}`,
        method: "put",
        values: {
          ...filteredData,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          setErrors({});
          navigate(`../ahlijawatankuasa?id=${branchId}`);
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const [positionsTranslatedList, setPositionsTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);

  const [occupationTranslatedList, setOccupationTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    const newPList = OrganisationPositions.map((item) => ({
      ...item,
      label: t(item.label),
    }));

    const newOList = OccupationList.map((item) => ({
      ...item,
      label: t(item.label),
    }));

    setPositionsTranslatedList(newPList);
    setOccupationTranslatedList(newOList);
  }, [t]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    const newErrors: { [key in keyof FormValues]?: string } = {};
    const requiredFields: (keyof FormValues)[] = [
      "titleCode",
      "committeeName",
      "gender",
      "identityType",
      "dateOfBirth",
      "committeeAddress",
      "committeeStateCode",
      "committeeDistrict",
      "postcode",
      "email",
      "phoneNumber",
    ];

    requiredFields.forEach((field) => {
      if (!formValues[field]) {
        newErrors[field] = t("requiredField");
      }
    });

    const filteredData = Object.fromEntries(
      Object.entries(formValues).filter(([key, value]) => value !== "")
    );

    if (memberId) {
      filteredData.id = memberId;
    }
    filteredData.branchId = branchId;
    filteredData.branchNo = branchData.branchNo;
    filteredData.societyId = branchData.societyId;
    filteredData.societyNo = branchData.societyNo;
    filteredData.citizenshipStatus = 1;
    if (memberId) {
      filteredData.id = memberId ? memberId : null;
      EditBranchCommittee(filteredData);
    } else {
      CreateBranchCommittee(filteredData);
    }
  };

  const [employerState, setEmployerState] = useState<any | null>(
    selectedCommittee?.committeeEmployerStateCode
  );
  const [employerDistrict, setEmployerDistrict] = useState<any | null>(
    selectedCommittee?.committeeEmployerDistrict
  );

  useEffect(() => {
    if (formValues.committeeStateCode) {
      setDistrictList(
        addressList
          ?.filter((item: any) => item.pid === formValues.committeeStateCode)
          ?.map((item: any) => ({ value: item.id, label: item.name }))
      );
    }
  }, [formValues?.committeeStateCode]);

  useEffect(() => {
    if (formValues.committeeEmployerStateCode) {
      setEmployerDistrictList(
        addressList
          ?.filter(
            (item: any) => item.pid === formValues.committeeEmployerStateCode
          )
          ?.map((item: any) => ({ value: item.id, label: item.name }))
      );
    }
  }, [formValues?.committeeEmployerStateCode]);

  useEffect(() => {
    if (formValues?.committeeEmployerCountryCode) {
      setEmployerState(
        addressList
          ?.filter(
            (item: any) => item.pid === formValues?.committeeEmployerCountryCode
          )
          ?.map((item: any) => ({ value: item.id, label: item.name }))
      );
    }
  }, [formValues?.committeeEmployerCountryCode]);

  useEffect(() => {
    if (formValues.committeeEmployerStateCode) {
      setEmployerDistrict(
        addressList
          ?.filter(
            (item: any) => item.pid === formValues.committeeEmployerStateCode
          )
          ?.map((item: any) => ({ value: item.id, label: item.name }))
      );
    }
  }, [formValues?.committeeEmployerStateCode]);

  const [idTypeTranslatedList, setIdTypeTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    const newOList = IdTypes.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setIdTypeTranslatedList(newOList);
  }, [t]);

  const { data, isLoading: isLoadingICValidation } = useCustom<any>({
    url: `${API_URL}/user/auth/validateId`,
    method: "get",
    config: {
      query: {
        identificationNo: formValues.committeeIcNo,
        name: formValues.committeeName?.trim().toUpperCase(),
        sessionIdentificationNo: formValues.committeeIcNo,
      },
    },
    queryOptions: {
      // enabled: formValues.committeeIcNo.length === 12,
      enabled:
        (Number(formValues.identityType) === 1 ||
          Number(formValues.identityType) === 4) &&
        Number(formValues?.citizenshipStatus) === 1 &&
        formValues.committeeIcNo?.length > 11 &&
        formValues.committeeName !== "",
      retry: false,
      cacheTime: 0,
    },
    successNotification(data) {
      //reset before new call
      setUserICCorrect(false);
      setUserNameMatchIC(false);
      // setVerifyAllFetch(false);

      const { name, message, status, userExist, integrationOff } =
        data?.data?.data || {};

      if (!integrationOff) {
        if (status === "Y") {
          setUserICCorrect(true);
          if (name) {
            setUserNameMatchIC(true);
          }
        } else {
          setUserICCorrect(false);
          setUserNameMatchIC(true);
        }
      }

      return false;
    },
    errorNotification(error) {
      setUserICCorrect(false);
      setUserNameMatchIC(false);

      return false;
    },
  });

  const [JPNError, setJPNError] = useState(false);
  const {
    userICCorrect,
    userNameMatchIC,
    triggerICValidation,
    setUserICCorrect,
    setUserNameMatchIC,
    resetICValidation,
    integrationStatus,
  } = useICValidation({
    idType: formValues?.identityType,
    idNumber: formValues?.committeeIcNo,
    fullName: formValues?.committeeName,
  });

  useEffect(() => {
    const isMyKad =
      Number(formValues?.identityType) === 1 ||
      Number(formValues?.identityType) === 4;
    const nameReady = formValues?.committeeName?.trim() !== "";
    const idReady = formValues?.committeeIcNo?.length === 12;
    if (integrationStatus === 0 && isMyKad) {
      setJPNError(true);
    } else {
      setJPNError(false);
    }
    if (integrationStatus === 1 && isMyKad && nameReady && idReady) {
      triggerICValidation();
    }
  }, [
    formValues?.identityType,
    formValues?.committeeIcNo,
    formValues?.committeeName,
    integrationStatus,
  ]);

  useEffect(() => {
    const type = Number(formValues?.identityType);
    if (type === 1 || type === 4)
      setFormValues({
        ...formValues,
        gender: autoGenderSetByIC(
          type,
          formValues?.gender,
          formValues?.committeeIcNo
        ),
        dateOfBirth: autoDOBSetByIC(
          type,
          formValues?.dateOfBirth,
          formValues?.committeeIcNo
        ),
      });
  }, [formValues?.committeeIcNo]);

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
      }}
    >
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography sx={sectionStyle}>{t("chairman")}</Typography>
        <Input
          value={formValues.designationCode ? formValues.designationCode : 1}
          name="designationCode"
          onChange={handleChange}
          label={t("position")}
          options={positionsTranslatedList}
          type="select"
          error={!!errors.designationCode}
          helperText={errors.designationCode}
          // disabled
        />
      </Box>
      {/* section 2 */}
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography sx={sectionStyle}>{t("personalInfo")}</Typography>
        {JPNError ? (
          <Box sx={{ mt: 2, mb: 2 }}>
            <FormHelperText sx={{ color: "var(--error)" }}>
              {t("JPNError")}
            </FormHelperText>
          </Box>
        ) : null}
        <Input
          value={formValues.identityType ? formValues.identityType : ""}
          name="identityType"
          onChange={(e) => {
            if (e.target.value === "1" || e.target.value === "4") {
              // setIdNumber("");
              setFormValues((prevValues) => ({
                ...prevValues,
                committeeIcNo: null,
              }));
              handleChange(e.target.value);
            } else {
              setUserICCorrect(true);
              setUserNameMatchIC(true);
              handleChange(e.target.value);
              setFormValues((prevValues) => ({
                ...prevValues,
                committeeIcNo: null,
              }));
            }
          }}
          // onChange={handleChange}
          required
          disabled
          label={t("idType")}
          options={idTypeTranslatedList}
          type="select"
          error={!!errors.identityType}
          helperText={errors.identityType}
        />
        <Input
          value={formValues.committeeIcNo ? formValues.committeeIcNo : ""}
          name="committeeIcNo"
          disabled={JPNError}
          onChange={handleChange}
          inputProps={
            formValues.identityType === "4" || formValues.identityType === "1"
              ? {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  maxLength: 12,
                  minLength: 12,
                }
              : undefined
          }
          onKeyDown={
            formValues.identityType === "4" || formValues.identityType === "1"
              ? (e) => {
                  const allowedKeys = [
                    "Backspace",
                    "ArrowLeft",
                    "ArrowRight",
                    "Tab",
                    "Delete",
                    "Control",
                    "Meta",
                    "v",
                  ];

                  // Allows copy paste
                  if (
                    (e.ctrlKey || e.metaKey) &&
                    (e.key === "v" || e.key === "V")
                  ) {
                    return;
                  }

                  if (!/^\d$/.test(e.key) && !allowedKeys.includes(e.key)) {
                    e.preventDefault();
                  }
                }
              : undefined
          }
          label={t("idNumber")}
          error={
            !!errors.committeeIcNo ||
            ((Number(formValues?.identityType) === 1 ||
              Number(formValues?.identityType) === 4) &&
              !userICCorrect) ||
            (formValues.committeeIcNo && formValues.committeeIcNo.length < 12)
          }
          helperText={
            errors.committeeIcNo
              ? t("invalidName")
              : (Number(formValues?.identityType) === 1 ||
                  Number(formValues?.identityType) === 4) &&
                (!userICCorrect
                  ? t("invalidName")
                  : formValues.committeeIcNo?.length < 12
                  ? t("idNumberOnlyDigits")
                  : undefined)
          }
        />
        <Input
          value={formValues.titleCode ? formValues.titleCode : ""}
          name="titleCode"
          onChange={handleChange}
          required
          label={t("gelaran")}
          options={ListGelaran.map((item) => ({
            value: item.value,
            label: t(item.label),
          }))}
          type="select"
          error={!!errors.titleCode}
          helperText={errors.titleCode}
        />
        <Input
          value={formValues.committeeName ? formValues.committeeName : ""}
          name="committeeName"
          disabled={JPNError}
          onChange={handleChange}
          required
          label={t("fullName")}
          error={!!errors.committeeName || !userNameMatchIC}
          helperText={
            errors.committeeName || !userNameMatchIC
              ? t("invalidName")
              : undefined
          }
        />
        <Input
          value={formValues.gender}
          name="gender"
          onChange={handleChange}
          required
          label={t("gender")}
          options={ListGender.map((option) => ({
            ...option,
            label: t(option.label),
          }))}
          type="select"
          error={!!errors.gender}
          helperText={errors.gender}
        />
        <Input
          value={formValues?.identityType === "1" ? 1 : 2}
          name="citizenshipStatus"
          onChange={handleChange}
          label={t("citizenship")}
          required
          type="select"
          options={CitizenshipStatus.map((item) => ({
            ...item,
            label: t(item.label),
          }))}
          disabled
        />
        <Input
          value={
            formValues.dateOfBirth
              ? dayjs(formValues.dateOfBirth).format("DD-MM-YYYY")
              : ""
          }
          name="dateOfBirth"
          type="date"
          required
          onChange={handleChange}
          label={t("dateOfBirth")}
          error={!!errors.dateOfBirth}
          helperText={errors.dateOfBirth}
        />
        <Input
          value={formValues.placeOfBirth ? formValues.placeOfBirth : ""}
          name="placeOfBirth"
          onChange={handleChange}
          label={t("placeOfBirth")}
          error={!!errors.placeOfBirth}
          helperText={errors.placeOfBirth}
        />
        <Input
          value={formValues.pegHarta ? formValues.pegHarta : ""}
          name="pegHarta"
          onChange={handleChange}
          required
          label={t("occupation")}
          options={occupationTranslatedList}
          type="select"
          error={!!errors.pegHarta}
          helperText={errors.pegHarta}
        />
        <Input
          value={formValues.committeeAddress ? formValues.committeeAddress : ""}
          name="committeeAddress"
          required
          multiline
          rows={4}
          onChange={handleChange}
          label={t("residentialAddress")}
          error={!!errors.committeeAddress}
          helperText={errors.committeeAddress}
        />
        <Input
          value={
            formValues.committeeStateCode ? formValues.committeeStateCode : ""
          }
          name="committeeStateCode"
          onChange={handleChange}
          required
          options={StateList}
          type="select"
          label={t("state")}
          error={!!errors.committeeStateCode}
          helperText={errors.committeeStateCode}
        />
        <Input
          value={
            formValues.committeeDistrict ? formValues.committeeDistrict : ""
          }
          name="committeeDistrict"
          onChange={handleChange}
          required
          label={t("daerah")}
          type="select"
          disabled={!formValues?.committeeStateCode}
          options={districtList ? districtList : []}
          error={!!errors.committeeDistrict}
          helperText={errors.committeeDistrict}
        />
        <Input
          value={formValues.committeeCity ? formValues.committeeCity : ""}
          name="committeeCity"
          onChange={handleChange}
          label={t("city")}
          error={!!errors.committeeCity}
          helperText={errors.committeeCity}
        />
        <Input
          value={formValues.postcode ? formValues.postcode : ""}
          name="postcode"
          required
          onChange={handleChange}
          label={t("postcode")}
          error={!!errors.postcode}
          helperText={errors.postcode}
        />
        <Input
          value={formValues.email ? formValues.email : ""}
          name="email"
          type="email"
          required
          onChange={handleChange}
          label={t("email")}
          error={!!errors.email}
          helperText={errors.email}
        />
        <Input
          value={formValues.phoneNumber ? formValues.phoneNumber : ""}
          name="phoneNumber"
          required
          // onChange={handleChange}
          label={t("phoneNumber")}
          error={!!errors.phoneNumber}
          helperText={errors.phoneNumber}
          inputProps={{
            inputMode: "numeric",
          }}
          onChange={(e) => {
            const type = Number(formValues?.identityType);
            const input = e.target as HTMLInputElement;
            let raw = input.value.replace(/[^\d]/g, "");
            if (type === 1) {
              // Allow empty input
              if (!raw) {
                setFormValues({ ...formValues, phoneNumber: null });
                return;
              }

              // Remove leading 60 if present
              if (raw.startsWith("60")) {
                raw = raw.slice(2);
              }

              const limitedDigits = raw.slice(0, 10);
              const formatted = "+60" + limitedDigits;

              let error = "";
              if (limitedDigits.length < 8) {
                error = t("phoneDigitLimitWarning");
              }
              setFormValues({ ...formValues, phoneNumber: formatted });
              setErrors({
                ...errors,
                phoneNumber: error,
              });
            } else {
              handleChange(e);
            }
          }}
          onKeyDown={(e) => {
            const type = Number(formValues?.identityType);
            if (type === 1) {
              const input = e.target as HTMLInputElement;
              const pos = input.selectionStart ?? 0;
              const hasValue = input.value.length > 0;

              // restrictions
              if (hasValue) {
                if (
                  (e.key.length === 1 && pos < 3) || // typing characters in +60
                  (e.key === "Backspace" && pos <= 3) || // backspacing into +60
                  (e.key === "Delete" && pos < 3) // deleting inside +60
                ) {
                  e.preventDefault();
                }
              }
            }
          }}
          onClick={(e) => {
            const type = Number(formValues?.identityType);
            if (type === 1) {
              const input = e.target as HTMLInputElement;
              if (
                input.value &&
                input.selectionStart !== null &&
                input.selectionStart < 3
              ) {
                // Move cursor to after +60 if user clicks inside prefix
                setTimeout(() => {
                  input.setSelectionRange(3, 3);
                }, 0);
              }
            }
          }}
          onFocus={(e) => {
            const type = Number(formValues?.identityType);
            if (type === 1) {
              const input = e.target as HTMLInputElement;
              if (
                input.value &&
                input.selectionStart !== null &&
                input.selectionStart < 3
              ) {
                // move cursor to after +60 on focus
                setTimeout(() => {
                  input.setSelectionRange(3, 3);
                }, 0);
              }
            }
          }}
          placeholder={Number(formValues?.identityType) === 1 ? "+60" : ""}
        />
        <Input
          value={formValues.homePhoneNumber ? formValues.homePhoneNumber : ""}
          name="homePhoneNumber"
          onChange={handleChange}
          label={t("homeNumber")}
          error={!!errors.homePhoneNumber}
          helperText={errors.homePhoneNumber}
          inputMode={"numeric"}
          onInput={(e) => {
            const input = e.target as HTMLInputElement;
            input.value = input.value.replace(/\D/g, "").slice(0, 12);
          }}
        />
        <Input
          value={
            formValues.officePhoneNumber ? formValues.officePhoneNumber : ""
          }
          name="officePhoneNumber"
          onChange={handleChange}
          label={t("officeNumber")}
          error={!!errors.officePhoneNumber}
          helperText={errors.officePhoneNumber}
          inputMode={"numeric"}
          onInput={(e) => {
            const input = e.target as HTMLInputElement;
            input.value = input.value.replace(/\D/g, "").slice(0, 12);
          }}
        />
      </Box>
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <span
          style={{
            color: "red",
            fontWeight: "500",
            alignContent: "center",
            fontSize: "12px",
          }}
        >
          {t("peringatan")}:
        </span>
        <span
          style={{
            color: "#666666",
            fontWeight: "500",
            alignContent: "center",
            fontSize: "12px",
          }}
        >
          {t("maklumatMajikanWarning")}
        </span>
      </Box>
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography sx={sectionStyle}>{t("employerInfo")}</Typography>
        <Input
          value={
            formValues.committeeEmployerName
              ? formValues.committeeEmployerName
              : ""
          }
          name="committeeEmployerName"
          onChange={handleChange}
          label={t("employerName")}
          error={!!errors.committeeEmployerName}
          helperText={errors.committeeEmployerName}
        />
        <Input
          value={
            formValues.committeeEmployerAddress
              ? formValues.committeeEmployerAddress
              : ""
          }
          name="committeeEmployerAddress"
          onChange={handleChange}
          label={t("employerAddress")}
          error={!!errors.committeeEmployerAddress}
          helperText={errors.committeeEmployerAddress}
        />
        <Input
          value={
            formValues.committeeEmployerCountryCode
              ? formValues.committeeEmployerCountryCode
              : ""
          }
          name="committeeEmployerCountryCode"
          onChange={handleChange}
          label={t("originCountry")}
          options={CountryData}
          type="select"
          error={!!errors.committeeEmployerCountryCode}
          helperText={errors.committeeEmployerCountryCode}
        />
        {formValues?.committeeEmployerCountryCode === MALAYSIA ? (
          <>
            <Input
              value={
                formValues.committeeEmployerStateCode
                  ? formValues.committeeEmployerStateCode
                  : ""
              }
              name="committeeEmployerStateCode"
              onChange={handleChange}
              disabled={!formValues?.committeeEmployerStateCode}
              options={StateList}
              type="select"
              label={t("state")}
              error={!!errors.committeeEmployerStateCode}
              helperText={errors.committeeEmployerStateCode}
            />
            <Input
              value={
                formValues.committeeEmployerDistrict
                  ? formValues.committeeEmployerDistrict
                  : ""
              }
              name="committeeEmployerDistrict"
              onChange={handleChange}
              label={t("district")}
              type="select"
              disabled={!formValues?.committeeEmployerStateCode}
              options={employerDistrictList ? employerDistrictList : []}
              error={!!errors.committeeEmployerDistrict}
              helperText={errors.committeeEmployerDistrict}
            />
            <Input
              value={
                formValues.committeeEmployerCity
                  ? formValues.committeeEmployerCity
                  : ""
              }
              name="committeeEmployerCity"
              onChange={handleChange}
              label={t("city")}
              error={!!errors.committeeEmployerCity}
              helperText={errors.committeeEmployerCity}
            />
            <Input
              value={
                formValues.committeeEmployerPostcode
                  ? formValues.committeeEmployerPostcode
                  : ""
              }
              name="committeeEmployerPostcode"
              onChange={handleChange}
              label={t("postcode")}
              error={!!errors.committeeEmployerPostcode}
              helperText={errors.committeeEmployerPostcode}
            />
          </>
        ) : null}
      </Box>
      <FileUploader
        title="ajkEligibilityCheck"
        type={DocumentUploadType.CITIZEN_COMMITTEE}
        uploadAfterSubmitIndicator={ajkId}
        uploadAfterSubmit={memberId ? false : true}
        icNo={Number(formValues?.committeeIcNo)}
        disabled={formValues?.committeeIcNo === null}
        societyId={ajkId || memberId ? Number(branchData.societyId) : undefined}
        branchId={ajkId || memberId ? Number(branchId) : undefined}
        branchCommitteeId={ajkId ? ajkId : memberId ? memberId : undefined}
        validTypes={[
          "text/plain",
          "application/rtf",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/msword",
          "application/vnd.oasis.opendocument.text",
          "application/pdf",
        ]}
      />
      <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 2 }}>
        <ButtonPrimary
          sx={{
            background: "white",
            border: "1px solid #DDDDDD",
            color: "#666666",
            boxShadow: "none",
          }}
          onClick={handleBack}
        >
          {t("previous")}
        </ButtonPrimary>
        <ButtonPrimary
          sx={{
            boxShadow: "none",
          }}
          disabled={JPNError || !userNameMatchIC || !userICCorrect}
          type="submit"
        >
          {t("update")}
        </ButtonPrimary>
      </Box>
    </Box>
  );
};

export default CreateAjk;
