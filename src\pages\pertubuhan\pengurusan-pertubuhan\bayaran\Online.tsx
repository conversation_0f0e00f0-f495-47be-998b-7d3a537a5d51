import { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import { SelectChangeEvent } from "@mui/material/Select";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { OrganizationStepper } from "../organization-stepper";
import { useTheme, useMediaQuery, Fade, Grid } from "@mui/material";
import Input from "../../../../components/input/Input";
import { useNavigate, useSearchParams } from "react-router-dom";
import { API_URL } from "../../../../api";
import { LoadingOverlay } from "../../../../components/loading";
import InfoQACard from "../InfoQACard";
import { useSelector } from "react-redux";
import { parseDateToISO8601 } from "@/helpers";
import { selectCalculatedPayment } from "@/redux/paymentReducer";

export const Online = () => {
  const [paymentMethod, setPaymentMethod] = useState("");
  const { t } = useTranslation();
  const [activeStep, setActiveStep] = useState(5);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogAlertSuccessSaveOpen, setDialogAlertSuccessSaveOpen] =
    useState(false);

  const navigate = useNavigate();

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");
  const decodedId = atob(encodedId || "");

  const [searchParams] = useSearchParams();
  const [societyData, setSocietyData] = useState<any>(null);
  const [isLoadingData, setIsLoadingData] = useState(false);

  const getUserDetails = localStorage.getItem("user-details");
  const userName = getUserDetails ? JSON.parse(getUserDetails).name : "";
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);
  const calculatedPayment = useSelector(selectCalculatedPayment);

  useEffect(() => {
    const fetchSocietyData = async () => {
      try {
        setIsLoadingData(true);
        const encodedId = searchParams.get("id") || "";
        const decodedId = atob(encodedId);
        /*const response = await fetch(`${API_URL}/society/${decodedId}`, {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        });

        const data = await response.json();
        if (data.status === "SUCCESS") {
          setSocietyData(data.data);
        }*/
      } catch (error) {
        console.error("Error fetching society data:", error);
      } finally {
        setIsLoadingData(false);
      }
    };

    fetchSocietyData();
  }, [searchParams]);

  const handleChange = (event: SelectChangeEvent) => {
    setPaymentMethod(event.target.value as string);
  };

  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const downloadFile = (filePath: string) => {
    window.open(filePath, "_blank");
  };

  const handleCetak = async () => {
    try {
      if (decodedId) {
        const response = await fetch(`${API_URL}/society/pdf/paymentReceipt`, {
          method: "post",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            paymentType: "Pendaftaran Pertubuhan Baru (Pembayaran ONLINE)",
            paymentMethod: "o", // O = online, C = counter
            societyId: decodedId,
            branchId: "",
            registerDateTime: parseDateToISO8601(societyDataRedux?.createdDate),
            amount: calculatedPayment?.totalAmount,
          }),
        });

        const data = await response.json();
        if (data.status === "SUCCESS") {
          const url = data?.data?.url;
          downloadFile(url);
        }
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box sx={{ width: "55vw" }}>
        <LoadingOverlay isLoading={isLoadingData} />

        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3,
            }}
          >
            {/* Payment Header Box */}
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: 600,
                    paddingLeft: 2,
                  }}
                >
                  {t("pengesahan")} {t("payment")}
                </Typography>
              </Box>

              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: 400,
                  paddingLeft: 2,
                }}
              >
                {t("infoPayment")}
              </Typography>

              <Grid container spacing={2} pl={4} pt={2} mb={3} mt={3}>
                <Input
                  disabled
                  label={t("organizationName")}
                  value={societyDataRedux?.societyName || ""}
                />
                <Input
                  disabled
                  label={t("tarikhPermohonan")}
                  value={societyDataRedux?.createdDate}
                  type="datetime"
                />
                <Input
                  disabled
                  label={t("referenceNumber")}
                  value={societyDataRedux?.applicationNo || ""}
                />
                <Input
                  disabled
                  label={t("paymentMethod")}
                  value={societyDataRedux?.paymentMethod || "Pembayaran Online"}
                />
                <Input
                  disabled
                  label={t("paymentType")}
                  value="Pendaftaran pertubuhan"
                />
                <Input
                  disabled
                  label={t("namePemohon")}
                  value={userName || ""}
                />
                <Input
                  disabled
                  label={t("email")}
                  value={societyDataRedux?.email || ""}
                />
                <Input
                  disabled
                  label={t("idNumberPlaceholder")}
                  value={societyDataRedux?.identificationNo || ""}
                />
                <Input disabled label={t("kodOsolAmanah")} value="724999" />
                <Input
                  disabled
                  label={t("jabatan")}
                  value="Jabatan Pendaftaran Pertubuhan"
                />
                <Input
                  disabled
                  label={t("pusatPenerimaan")}
                  value="Ibu Pejabat Jab. Pendataran Pertubuhan"
                />
              </Grid>

              <Typography
                variant="body2"
                sx={{
                  color: "#402DFF",
                  fontSize: 14,
                  paddingLeft: 2,
                }}
              >
                {t("noteOnline")}
              </Typography>
            </Box>

            {/* Action Buttons */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
                marginTop: 15,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                onClick={handleCetak}
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  borderRadius: 1,
                  textTransform: "none",
                }}
              >
                {t("cetak")}
              </ButtonPrimary>
              <ButtonPrimary
                onClick={() => navigate("term?id=" + encodedId)}
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  borderRadius: 1,
                  textTransform: "none",
                }}
              >
                {t("bayar")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>

        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
              backgroundColor: "#fff",
              color: "#000",
              minWidth: fullScreen ? "100%" : "1000px",
              maxWidth: "100%",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogTitle sx={{ pb: 2.5 }}>
            <Box
              sx={{
                backgroundColor: "#e0f2f1",
                px: 2.5,
                py: 0.5,
                borderRadius: 2.5,
              }}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{ fontWeight: "bold", fontSize: 16, color: "black" }}
              >
                {t("submitApplication")}
              </Typography>
              <IconButton onClick={handleCloseDialog} size="small">
                <CloseIcon sx={{ color: "black" }} />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ py: 4 }}>
            <DialogContentText
              id="alert-dialog-description"
              sx={{ color: "black", fontSize: 14, ml: 2 }}
            >
              {t("confirmSubmitApplication")}
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ py: 2, px: 3 }}>
            <ButtonOutline onClick={handleCloseDialog} sx={{ fontSize: 14 }}>
              {t("no")}
            </ButtonOutline>
            <ButtonPrimary color="primary" autoFocus sx={{ fontSize: 14 }}>
              {t("yes")}
            </ButtonPrimary>
          </DialogActions>
        </Dialog>

        <Dialog
          open={dialogAlertSuccessSaveOpen}
          onClose={() => setDialogAlertSuccessSaveOpen(false)}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogContent sx={{ p: 4 }}>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <img src="/success.gif" alt="success" width={200} height={200} />
            </Box>
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontSize: 28, textAlign: "center" }}
            >
              Permohonan berjaya dihantar.
            </Typography>

            <Box
              sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
            >
              <ButtonPrimary
                sx={{ backgroundColor: "#51CA47", borderRadius: "18px" }}
                onClick={() => setDialogAlertSuccessSaveOpen(false)}
              >
                {t("Continue")}
              </ButtonPrimary>
            </Box>
          </DialogContent>
        </Dialog>
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <OrganizationStepper activeStep={activeStep} />
        <InfoQACard />
      </Box>
    </Box>
  );
};

export default Online;
