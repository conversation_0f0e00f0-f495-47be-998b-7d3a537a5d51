import {
  Box,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  MenuItem,
  Paper,
  Radio,
  RadioGroup,
  Select,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
  Fade,
  FormHelperText,
  CircularProgress,
} from "@mui/material";
import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { OrganizationStepper } from "../organization-stepper";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { useCustom } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { removeFromStorage } from "./removeFasal";
import ConfirmationDialog from "../../../../components/dialog/confirm";
import {
  ApplicationStatus,
  ConstitutionType,
  HideOrDisplayInherit,
  OrganizationLevelOption,
} from "../../../../helpers/enums";
import InfoQACard from "../InfoQACard";
import InfoIcon from "@mui/icons-material/Info";
import CustomPopover from "../../../../components/popover";
import { setSocietyDataRedux } from "../../../../redux/societyDataReducer";
import { capitalizeWords, useMutation, useQuery } from "@/helpers";
import { DialogConfirmation } from "@/components";
import RemoveIcon from "@mui/icons-material/Remove";
import { useCheckAndUpdateRegistration } from "@/helpers/hooks/useCheckAndupdateRegister";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { TemplateIcon1 } from "@/components/icons/templateIcon1";
import { TemplateIcon2 } from "@/components/icons/templateIcon2";
import { TemplateIcon3 } from "@/components/icons/templateIcon3";
import { TemplateIcon4 } from "@/components/icons/templateIcon4";
import { TemplateIcon5 } from "@/components/icons/templateIcon5";

const TemplateBox = ({
  title,
  type,
  icon,
}: {
  title: string;
  type: string;
  icon: string;
}) => {
  const { data: templateData, refetch: fetchTemplate } = useQuery({
    autoFetch: false,
    url: "society/document/getDocumentTemplateUrl",
    onSuccess: (data) => {
      const url = data?.data?.data;
      window.open(url, "_blank");
    },
  });

  const showTemplate = (type: string) => {
    fetchTemplate({
      filters: [
        {
          field: "documentTemplateType",
          operator: "eq",
          value: type,
        },
      ],
    });
  };

  return (
    <Box
      onClick={() => showTemplate(type)}
      sx={{
        backgroundImage: `url("data:image/svg+xml;utf8,${encodeURIComponent(
          icon
        )}")`,
        backgroundRepeat: "no-repeat",
        backgroundColor: "var(--primary-color)",
        backgroundPosition: "calc(100% - 10px) bottom",
        color: "#fff",
        position: "relative",
        fontSize: "14px",
        p: 1,
        borderRadius: "10px",
        minHeight: "90px",
        cursor: "pointer",
        flex: "1",
      }}
    >
      {title}
    </Box>
  );
};

const TemplatePerlembagaanSection = () => {
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const templateList = [
    {
      title: "Perlembagaan Induk NGO",
      type: "DRAF_PERLEMBAGAAN_INDUK_NGO",
      icon: TemplateIcon1,
    },
    {
      title: "Perlembagaan Bercawangan Semua NGO",
      type: "DRAF_PERLEMBAGAAN_BERCAWANGAN_SEMUA_NGO",
      icon: TemplateIcon2,
    },
    {
      title: "Perlembagaan Induk Keagamaan",
      type: "DRAF_PERLEMBAGAAN_INDUK_KEAGAMAAN",
      icon: TemplateIcon3,
    },
    {
      title: "Perlembagaan Bercawangan Keagamaan",
      type: "DRAF_PERLEMBAGAAN_BERCAWANGAN_KEAGAMAAN",
      icon: TemplateIcon4,
    },
    {
      title: "Perlembagaan Faedah Bersama",
      type: "DRAF_PERLEMBAGAAN_FAEDAH_BERSAMA",
      icon: TemplateIcon5,
    },
  ];

  return (
    <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mb: 2 }}>
      <Typography variant="subtitle1" sx={sectionStyle}>
        Pilihan Templat Perlembagaan
      </Typography>
      <Box sx={{ display: "flex" }} gap={1}>
        {templateList.map((items) => {
          return (
            <TemplateBox
              title={items.title}
              type={items.type}
              icon={items.icon}
            />
          );
        })}
      </Box>
    </Box>
  );
};

export const CreatePerlembagaan: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [activeStep, setActiveStep] = useState(2);
  const [refresh, setRefresh] = useState(0);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);

  const [isCreated, setIsCreated] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [dataId, setDataId] = useState(null);
  const [societyId, setSocietyId] = useState<any>(null);
  const [senaraiFasal, setSenaraiFasal] = useState([]);
  const [constitutionType, setConstitutionType] = useState("");

  const [showConfirmDeleteDialog, setShowConfirmDeleteDialog] = useState(false);
  const [selectedIdForDelete, setSelectedIdForDelete] = useState(null);

  const { data: categoryData, isLoading: isCategoryLoading } = useCustom({
    url: `${API_URL}/society/admin/category/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const categories = categoryData?.data?.data || [];
  const mainCategories = categories.filter((cat: any) => cat.level === 1);
  const subCategories = categories.filter((cat: any) => cat.level === 2);

  const {
    data: clauseContentData,
    isLoading: clauseContentDataIsLoading,
    refetch: refetchSavedClauseContentData,
  } = useCustom({
    url: `${API_URL}/society/constitutioncontent/get`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: societyId,
      },
    },
    queryOptions: {
      cacheTime: 0,
      enabled: !!societyId,
    },
  });

  const {
    data: constitutionData,
    isLoading: isConstitutionLoading,
    refetch: refetchConstitutionData,
  } = useCustom({
    url: `${API_URL}/society/admin/constitutionTypeWithClauseContent/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const clauseContent = clauseContentData?.data?.data?.data || [];
  const constitutions = constitutionData?.data?.data || [];
  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");
  const isBebasCatergory =
    constitutionType === ConstitutionType.Bebas[1] ||
    constitutionType === ConstitutionType.CawanganBebas[1];

  useEffect(() => {
    if (encodedId) {
      const decodedId = atob(encodedId);
      setSocietyId(decodedId);
    }
  }, []);

  const handleSemakPerlembagaan = () => {
    navigate(
      `/pertubuhan/pengurusan-pertubuhan/pendaftaran/semak-perlembagaan?id=${encodedId}`
    );
  };

  const handleFasalKemaskini = (id: string | number, name: string) => {
    navigate(
      `/pertubuhan/pengurusan-pertubuhan/pendaftaran/fasal/${id}?id=${encodedId}&name=${name}`
    );
  };

  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const statusText = (status: number) => {
    //console.log(status);
    if (status === 2) {
      return t("complete");
    } else {
      return t("inComplete");
    }
  };

  const [formData, setFormData] = useState({
    organizationGoals: "",
    organizationLevel: "Negeri",
    organizationCategory: "",
    organizationSubCategory: "",
    branchOrganization: "",
    constitutionType: ConstitutionType.None[1],
  });

  useEffect(() => {
    if (
      (formData.organizationCategory == "2" ||
        formData.organizationCategory == "3" ||
        formData.organizationCategory == "4" ||
        formData.organizationCategory == "5" ||
        formData.organizationCategory == "6" ||
        formData.organizationCategory == "7" ||
        formData.organizationCategory == "8" ||
        formData.organizationCategory == "9") &&
      formData.branchOrganization == "no"
    ) {
      setFormData({
        ...formData,
        constitutionType: ConstitutionType.IndukNGO[1],
      });
    } else if (
      (formData.organizationCategory == "2" ||
        formData.organizationCategory == "3" ||
        formData.organizationCategory == "4" ||
        formData.organizationCategory == "5" ||
        formData.organizationCategory == "6" ||
        formData.organizationCategory == "7" ||
        formData.organizationCategory == "8" ||
        formData.organizationCategory == "9") &&
      formData.branchOrganization == "yes"
    ) {
      setFormData({
        ...formData,
        constitutionType: ConstitutionType.CawanganNGO[1],
      });
    } else if (formData.organizationCategory == "10") {
      setFormData({
        ...formData,
        constitutionType: ConstitutionType.FaedahBersama[1],
      });
    } else if (
      formData.organizationCategory == "11" &&
      formData.branchOrganization == "no"
    ) {
      setFormData({
        ...formData,
        constitutionType: ConstitutionType.IndukAgama[1],
      });
    } else if (
      formData.organizationCategory == "11" &&
      formData.branchOrganization == "yes"
    ) {
      setFormData({
        ...formData,
        constitutionType: ConstitutionType.CawanganAgama[1],
      });
    } else if (formData.organizationCategory == "12") {
      if (formData.branchOrganization == "yes") {
        setFormData({
          ...formData,
          constitutionType: ConstitutionType.CawanganBebas[1],
        });
      } else {
        setFormData({
          ...formData,
          constitutionType: ConstitutionType.Bebas[1],
        });
      }
    } else {
      setFormData({
        ...formData,
        constitutionType: "",
      });
    }
  }, [formData.organizationCategory, formData.branchOrganization]);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    /*if (formData.organizationGoals === "" || !formData.organizationGoals) {
      errors.organizationGoals = t("requiredValidation");
    }*/
    if (formData.organizationLevel === "" || !formData.organizationLevel) {
      errors.organizationLevel = t("requiredValidation");
    }
    if (
      formData.organizationCategory === "" ||
      !formData.organizationCategory
    ) {
      errors.organizationCategory = t("requiredValidation");
    }
    if (
      (formData.organizationSubCategory === "" ||
        !formData.organizationSubCategory) &&
      formData.organizationCategory != "12" &&
      formData.organizationCategory != "11"
    ) {
      errors.organizationSubCategory = t("requiredValidation");
    }
    if (formData.branchOrganization === "" || !formData.branchOrganization) {
      errors.branchOrganization = t("requiredValidation");
    }
    if (formData.constitutionType === "" || !formData.constitutionType) {
      errors.constitutionType = t("requiredValidation");
    }

    return errors;
  };

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  const dispatch = useDispatch();

  const getConstitutionTypeIdFromLabel = (
    label: string | number
  ): string | number => {
    const entry = Object.values(ConstitutionType).find(
      ([id, name]) => name === label
    );
    return entry ? entry[0] : "";
  };

  const generatePerlembagaan = () => {
    if (formData.constitutionType != "") {
      const selectedTypeId = getConstitutionTypeIdFromLabel(
        formData.constitutionType
      );
      let addedClauseContent = [];

      if (isBebasCatergory) {
        addedClauseContent = clauseContent
          .map(
            // @ts-ignore
            (i) => {
              if (selectedTypeId == i.constitutionTypeId && !i.clauseContent) {
                return i;
              }
            }
          )
          .filter(Boolean)
          .map((i: any) => ({
            ...i,
            status: 2,
            name: i.clauseName,
            added: true,
          }))
          .filter(
            (i: any) => i.clauseName !== "Tempat Urusan" && i.clauseNo !== "2"
          );
      }

      //console.log("clauseContent",clauseContent);
      const updatedConstitutions = constitutions?.map((item: any) => {
        const updatedClauseContents = item?.clauseContents?.map(
          (clause: any, index: any) => {
            const exist = clauseContent.findIndex(
              // @ts-ignore
              (p) => p.clauseContentId == clause.id && p.checkUpdate == 1
            );
            if (exist >= 0) {
              return {
                ...clause,
                //status: isClauseInLocalStorage ? 2 : clause.status,
                status: 2,
              };
            } else {
              //console.log("3")
              return {
                ...clause,
                //status: isClauseInLocalStorage ? 2 : clause.status,
                status: 0,
              };
            }
          }
        );
        // @ts-ignore
        const finished = updatedClauseContents.forEach((a) => {
          if (a.status == 0) {
            return true;
          }
          return true;
        });
        setIsCreated(true);
        return {
          ...item,
          clauseContents: [...updatedClauseContents, ...addedClauseContent],
        };
      });

      /*if (formData.constitutionType == ConstitutionType.Bebas[1]) {
        updatedConstitutions?.filter((item: any) => {
          if (item.name === "Perlembagaan Bercawangan Semua NGO") {
            setSenaraiFasal(item.clauseContents);
          }
        });
      } else if (formData.constitutionType == "Perlembagaan Keagamaan") {
        updatedConstitutions?.filter((item: any) => {
          if (item.name === "Perlembagaan Induk Keagamaan") {
            setSenaraiFasal(item.clauseContents);
          }
        });
      } else {
        updatedConstitutions?.map((item: any) => {
          if (item.name === formData.constitutionType) {
            setSenaraiFasal(item.clauseContents);
          }
        });
      }*/
      updatedConstitutions?.map((item: any) => {
        if (item.name === formData.constitutionType) {
          setSenaraiFasal(item.clauseContents);
        }
      });
    } else {
      setSenaraiFasal([]);
    }
  };

  const { fetch: createApproval, isLoading } = useMutation({
    url: `society/${societyId}/edit`,
    method: "put",
    onSuccess: (data) => {
      const temp = {
        ...societyDataRedux,
        societyLevel: formData.organizationLevel,
        categoryCodeJppm: formData.organizationCategory,
        subCategoryCode: formData.organizationSubCategory,
        constitutionType: formData.constitutionType,
        hasBranch: formData.branchOrganization == "yes" ? 1 : 0,
      };
      refetchSavedClauseContentData();
      refetchConstitutionData();
      dispatch(setSocietyDataRedux(temp));
      generatePerlembagaan();
      setOpenConfirmDialog(false);
    },
  });

  const handleSave = async () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    const sendData = {
      societyLevel: formData.organizationLevel,
      categoryCodeJppm: formData.organizationCategory,
      subCategoryCode: formData.organizationSubCategory,
      constitutionType: formData.constitutionType,
      hasBranch: formData.branchOrganization == "yes" ? 1 : 0,
    };

    removeFromStorage();
    createApproval(sendData);
  };

  useEffect(() => {
    if (societyDataRedux) {
      //console.log(societyDataRedux);
      setFormData((prevState) => ({
        ...prevState,
        organizationGoals: societyDataRedux?.organizationGoals,
        organizationLevel: societyDataRedux?.societyLevel,
        organizationCategory: societyDataRedux?.categoryCodeJppm,
        organizationSubCategory: societyDataRedux?.subCategoryCode,
        branchOrganization: societyDataRedux?.hasBranch === 1 ? "yes" : "no",
        constitutionType: societyDataRedux?.constitutionType,
      }));

      setConstitutionType(societyDataRedux?.constitutionType);

      //setIsCreated(true);
      setDataId(societyDataRedux?.id);
      setIsEdit(true);

      //generatePerlembagaan();
    }
  }, [societyDataRedux, constitutionType]);

  useEffect(() => {
    generatePerlembagaan();
  }, [clauseContentData, constitutions]);

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  const handleAddFasal = () => {
    const currentNo = senaraiFasal.length + 1;
    const id = btoa(currentNo.toString());
    navigate(`/pertubuhan/pengurusan-pertubuhan/pendaftaran/add/${id}`);
  };

  const { fetch: deleteFasal, isLoading: isLoadingDeleteFasal } = useMutation({
    url: `society/constitutioncontent/hardDeleteConstitution?constitutionContentId=${selectedIdForDelete}`,
    method: "delete",
    onSuccess: (data) => {
      refetchSavedClauseContentData();
      setSenaraiFasal((prev: any) =>
        prev.filter((item: any) => item.id !== selectedIdForDelete)
      );
      setRefresh((prev) => prev + 1);
      setShowConfirmDeleteDialog(false);
    },
  });

  const handleOpenDeleteFasalModal = (data: any) => {
    const id = data.id;
    setSelectedIdForDelete(id);
    setShowConfirmDeleteDialog(true);
  };

  const handleDeleteRow = () => {
    deleteFasal();
  };

  const { data: checkAndUpdateRegistrationData, fetch: fetchCheck } =
    useCheckAndUpdateRegistration({
      id: societyDataRedux?.id,
      pageNo: 3,
      enabled: false,
      onSuccessNotification: (data) => {
        const responseData = data?.data?.data;
        //  const message = data?.data?.msg
        //  if(!responseData){
        //   return {
        //     message: message,
        //     type: "error",
        //   };
        //  }
      },
      onSuccess: (data) => {
        const responseData = data?.data?.data;
        if (responseData) {
          navigate(
            `/pertubuhan/pengurusan-pertubuhan/pendaftaran/senarai-ajk?id=${encodedId}`
          );
        }
      },
    });

  const handleGoNext = () => {
    fetchCheck();
  };

  const apiIsloadingFasal = isConstitutionLoading || clauseContentDataIsLoading;

  return (
    <>
      <Box sx={{ display: "flex", gap: 2 }}>
        <Box sx={{ width: "55vw" }}>
          {/* <LoadingOverlay
          isLoading={isCategoryLoading || isConstitutionLoading}
        /> */}
          <TemplatePerlembagaanSection />
          <Fade in={true} timeout={500}>
            <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px" }}>
              <Box
                sx={{
                  p: { xs: 1, sm: 2, md: 3 },
                  border: "1px solid #D9D9D9",
                  borderRadius: "14px",
                  mb: 2,
                }}
              >
                <Typography variant="subtitle1" sx={sectionStyle}>
                  {capitalizeWords(t("constitutionType"))}
                </Typography>

                <Grid container spacing={2} sx={{ mt: 0 }}>
                  <Grid item xs={12} md={3}>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                      <Typography sx={labelStyle}>
                        {t("organizationLevel")}{" "}
                        <Typography sx={{ display: "inline", color: "red" }}>
                          *
                        </Typography>
                      </Typography>
                      <CustomPopover
                        content={
                          <Typography sx={{ color: "#666666" }}>
                            {t("KebangsaanPopText")}
                            <br />
                            {t("negeriPopText")} <br />
                            {t("lainlainPopText")}
                          </Typography>
                        }
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl
                      fullWidth
                      required
                      error={!!formErrors.organizationLevel}
                    >
                      <Select
                        size="small"
                        value={formData.organizationLevel}
                        displayEmpty
                        onChange={(e) => {
                          setFormData((prevState) => ({
                            ...prevState,
                            organizationLevel: e.target.value,
                          }));
                          setFormErrors((prev) => ({
                            ...prev,
                            organizationLevel: "",
                          }));
                        }}
                        renderValue={(selected) =>
                          selected
                            ? OrganizationLevelOption.find(
                                (cat: any) => cat.value === selected
                              )?.value
                            : t("selectPlaceholder")
                        }
                      >
                        {OrganizationLevelOption.map((i) => {
                          return <MenuItem value={i.value}>{i.value}</MenuItem>;
                        })}
                      </Select>
                      {formErrors.organizationLevel && (
                        <FormHelperText>
                          {formErrors.organizationLevel}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={3}>
                    <Typography sx={labelStyle}>
                      {t("organizationCategory")}{" "}
                      <span style={{ color: "red" }}>*</span>{" "}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl
                      fullWidth
                      required
                      error={!!formErrors.organizationCategory}
                    >
                      <Select
                        size="small"
                        value={formData.organizationCategory}
                        displayEmpty
                        onChange={(e) => {
                          setFormData((prevState) => ({
                            ...prevState,
                            organizationCategory: e.target.value,
                          }));
                          // Reset sub-category when main category changes
                          setFormData((prevState) => ({
                            ...prevState,
                            organizationSubCategory: "",
                          }));

                          setFormErrors((prev) => ({
                            ...prev,
                            organizationCategory: "",
                          }));
                        }}
                        renderValue={(selected) =>
                          selected
                            ? mainCategories.find(
                                (cat: any) => cat.id === parseInt(selected)
                              )?.categoryNameEn
                            : t("selectPlaceholder")
                        }
                        disabled={isCategoryLoading}
                      >
                        <MenuItem value="" disabled>
                          {isCategoryLoading
                            ? "Loading..."
                            : t("selectPlaceholder")}
                        </MenuItem>
                        {mainCategories.map((category: any) => (
                          <MenuItem
                            key={category.id}
                            value={category.id.toString()}
                          >
                            {category.categoryNameEn}
                          </MenuItem>
                        ))}
                      </Select>
                      {formErrors.organizationCategory && (
                        <FormHelperText>
                          {formErrors.organizationCategory}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid>

                <Grid container spacing={2} sx={{ mt: 0 }}>
                  <Grid item xs={12} md={3}>
                    <Typography sx={labelStyle}>
                      {t("branchOrganization")}{" "}
                      <span style={{ color: "red" }}>*</span>{" "}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl
                      fullWidth
                      required
                      error={!!formErrors.branchOrganization}
                    >
                      <Select
                        size="small"
                        displayEmpty
                        value={
                          formData.organizationCategory === "10"
                            ? "no"
                            : formData.branchOrganization
                        }
                        disabled={formData.organizationCategory === "10"}
                        onChange={(e) => {
                          setFormData((prevState) => ({
                            ...prevState,
                            branchOrganization: e.target.value,
                          }));
                          setFormErrors((prev) => ({
                            ...prev,
                            branchOrganization: "",
                          }));
                        }}
                      >
                        <MenuItem value="yes">{t("yes")}</MenuItem>
                        <MenuItem value="no">{t("no")}</MenuItem>
                      </Select>
                      {formErrors.branchOrganization && (
                        <FormHelperText>
                          {formErrors.branchOrganization}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Typography sx={labelStyle}>
                      {t("organizationSubCategory")}{" "}
                      <span style={{ color: "red" }}>*</span>{" "}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl
                      fullWidth
                      required
                      error={!!formErrors.organizationSubCategory}
                    >
                      <Select
                        size="small"
                        value={formData.organizationSubCategory}
                        displayEmpty
                        onChange={(e) => {
                          setFormData((prevState) => ({
                            ...prevState,
                            organizationSubCategory: e.target.value,
                          }));
                          setFormErrors((prev) => ({
                            ...prev,
                            organizationSubCategory: "",
                          }));
                        }}
                        renderValue={(selected) =>
                          selected
                            ? subCategories.find(
                                (cat: any) => cat.id === parseInt(selected)
                              )?.categoryNameEn
                            : t("selectPlaceholder")
                        }
                        disabled={
                          (societyDataRedux
                            ? isCategoryLoading
                            : isCategoryLoading ||
                              !formData.organizationCategory) ||
                          formData.organizationCategory === "12"
                        }
                      >
                        <MenuItem value="" disabled>
                          {isCategoryLoading
                            ? "Loading..."
                            : t("selectPlaceholder")}
                        </MenuItem>
                        {subCategories
                          .filter(
                            (subCat: any) =>
                              subCat.pid ===
                              parseInt(formData.organizationCategory)
                          )
                          .map((category: any) => (
                            <MenuItem
                              key={category.id}
                              value={category.id.toString()}
                            >
                              {category.categoryNameEn}
                            </MenuItem>
                          ))}
                      </Select>
                      {formErrors.organizationSubCategory && (
                        <FormHelperText>
                          {formErrors.organizationSubCategory}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid>

                <Grid container spacing={2} sx={{ mt: 0 }}>
                  <Grid item xs={12} md={3}>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                      <Typography sx={labelStyle}>
                        {capitalizeWords(t("constitutionType"))}
                      </Typography>

                      <CustomPopover
                        customStyles={{ maxWidth: "250px" }}
                        content={
                          <Typography sx={{ color: "#666666" }}>
                            {t("jenisPerlembagaanPopover")}
                          </Typography>
                        }
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={9}>
                    <TextField
                      value={formData.constitutionType}
                      fullWidth
                      required
                      disabled
                      size="small"
                      sx={{ backgroundColor: "#66666626" }}
                    />
                  </Grid>
                </Grid>

                <Grid
                  item
                  xs={12}
                  sx={{
                    mt: 6,
                    display: "flex",
                    flexDirection: isMobile ? "column" : "row",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <ButtonPrimary
                    onClick={
                      isEdit ? () => setOpenConfirmDialog(true) : handleSave
                    }
                    variant="contained"
                    sx={{ width: isMobile ? "100%" : "auto" }}
                  >
                    {t("save")}
                  </ButtonPrimary>
                </Grid>
              </Box>

              <Box
                sx={{
                  p: { xs: 1, sm: 2, md: 3 },
                  border: "1px solid #D9D9D9",
                  borderRadius: "14px",
                  mb: 2,
                }}
              >
                <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                  <Typography variant="subtitle1" sx={sectionStyle}>
                    {t("clauseList")}
                  </Typography>

                  {isBebasCatergory ? (
                    <Box>
                      <ButtonOutline
                        onClick={handleAddFasal}
                        sx={{
                          textTransform: "none",
                          fontWeight: 400,
                          fontSize: "13px",
                        }}
                      >
                        {t("AddClause")}
                      </ButtonOutline>
                    </Box>
                  ) : null}
                </Box>

                {apiIsloadingFasal ? (
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      minHeight: "300px",
                    }}
                  >
                    <CircularProgress />
                  </Box>
                ) : (
                  <Box sx={{ overflowX: "auto" }}>
                    <Table sx={{ minWidth: 600 }}>
                      <TableHead>
                        <TableRow>
                          {["clause", "matter", "status", "activity"].map(
                            (header, index) => (
                              <TableCell
                                key={index}
                                sx={{
                                  fontWeight: "bold",
                                  color: "#666666",
                                  p: 1,
                                  textAlign: "center",
                                  // index === 2
                                  //   ? "center"
                                  //   : index === 3
                                  //   ? "right"
                                  //   : "left",
                                  whiteSpace: "nowrap",
                                }}
                              >
                                {index < 3 ? t(header) : null}
                              </TableCell>
                            )
                          )}
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {senaraiFasal?.map((row: any, index) => (
                          <TableRow key={index}>
                            <TableCell
                              sx={{
                                color: "#666666",
                                borderBottom: "1px solid #e0e0e0",
                                p: 1,
                                textAlign: "center",
                              }}
                            >
                              {t("clause")} {row.clauseNo}
                            </TableCell>
                            <TableCell
                              sx={{
                                color: "#666666",
                                p: 1,
                                minWidth: "30px",
                                maxWidth: "200px",
                                whiteSpace: "wrap",
                                textAlign: "center",
                              }}
                            >
                              {row.name}
                            </TableCell>
                            <TableCell
                              sx={{
                                color: "white",
                                borderBottom: "1px solid #e0e0e0",
                                p: 1,
                                maxWidth: "50px",
                              }}
                            >
                              <Box
                                sx={{
                                  p: 1,
                                  border:
                                    row?.status == 2
                                      ? "0.5px solid #00B69B"
                                      : "0.5px solid #FF0000",
                                  backgroundColor:
                                    row?.status == 2
                                      ? "#00B69B66"
                                      : "#FF000080",
                                  textAlign: "center",
                                  borderRadius: "7px",
                                }}
                              >
                                {statusText(row.status)}
                              </Box>
                            </TableCell>
                            <TableCell
                              sx={{
                                borderBottom: "1px solid #e0e0e0",
                                p: 1,
                                maxWidth: "80px",
                              }}
                            >
                              <Box
                                sx={{
                                  display: "flex",
                                  justifyContent: "flex-end",
                                  alignItems: "center",
                                }}
                              >
                                <ButtonOutline
                                  sx={{
                                    display: HideOrDisplayInherit,
                                    minWidth: "auto",
                                    textAlign: "center",
                                    width: {
                                      xs: "85px",
                                      fontWeight: "400",
                                    },
                                  }}
                                  onClick={() =>
                                    handleFasalKemaskini(row.clauseNo, row.name)
                                  }
                                >
                                  {row?.status === 2 ? t("update") : t("isi")}
                                </ButtonOutline>
                              </Box>
                            </TableCell>
                            <TableCell
                              sx={{
                                width: {
                                  borderTop: "none",
                                  borderBottom: "none",
                                  xs: "50px",
                                },
                              }}
                            >
                              {row.added && (
                                <IconButton
                                  size="small"
                                  onClick={() =>
                                    handleOpenDeleteFasalModal(row)
                                  }
                                >
                                  <Box
                                    sx={{
                                      background: "#FF0000",
                                      display: "flex",
                                      alignItems: "center",
                                      justifyContent: "center",
                                      borderRadius: "100%",
                                      height: "16px",
                                      width: "16px",
                                    }}
                                  >
                                    <RemoveIcon
                                      sx={{ color: "#fff", width: "16px" }}
                                    />
                                  </Box>
                                </IconButton>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </Box>
                )}
              </Box>

              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonOutline
                  disabled={!societyDataRedux || senaraiFasal.length === 0}
                  onClick={handleSemakPerlembagaan}
                >
                  {t("semakFasal")}
                </ButtonOutline>
                <ButtonPrimary
                  sx={{ width: isMobile ? "100%" : "auto" }}
                  onClick={() => handleGoNext()}
                  disabled={!isCreated}
                >
                  {t("next")}
                </ButtonPrimary>
              </Grid>
            </Box>
          </Fade>

          <ConfirmationDialog
            open={openConfirmDialog}
            onClose={() => setOpenConfirmDialog(false)}
            onConfirm={handleSave}
            onCancel={() => setOpenConfirmDialog(false)}
            title={"Kemaskini Perlembagaan?"}
            message={
              "Adakah anda pasti ingin mengemaskini jenis perlembagaan pertubuhan ini? Jika ya, maklumat yang telah diisi sebelum ini akan dipadam. "
            }
            turn
            isMutation={isLoading}
          />
        </Box>

        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          <OrganizationStepper
            activeStep={activeStep}
            hidePayment={
              societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI
            }
            isFasalFinished={isCreated}
          />

          {societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI ? (
            <Box
              sx={{
                padding: 3,
                backgroundColor: "white",
                borderRadius: "15px",
                maxHeight: "60vh",
                maxWidth: "18vw",
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("kuiri")}
              </Typography>
              <Box
                sx={{
                  padding: 3,
                  backgroundColor: "#DADADA",
                  borderRadius: "15px",
                  maxHeight: "60vh",
                  maxWidth: "18vw",
                }}
              >
                <Typography
                  sx={{
                    mb: 8,
                    fontSize: "12px",
                    color: "#666666",
                    fontWeight: "500 !important",
                  }}
                >
                  {societyDataRedux.queryText}
                </Typography>
              </Box>
            </Box>
          ) : null}

          <InfoQACard />
        </Box>
      </Box>
      <DialogConfirmation
        open={showConfirmDeleteDialog}
        onClose={() => {
          setShowConfirmDeleteDialog(false);
          setSelectedIdForDelete(null);
        }}
        onAction={handleDeleteRow}
        isMutating={isLoadingDeleteFasal}
        onConfirmationText={t("confirmdeletefasalText")}
      />
    </>
  );
};

export default CreatePerlembagaan;
