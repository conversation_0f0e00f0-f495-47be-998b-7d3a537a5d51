import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent"; 
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import { 
  RegExNumbers,
  RegExText,
} from "../../../../../helpers/enums";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import { formatAndValidateNumber } from "@/helpers";
import { API_URL } from "@/api";

interface FasalContentDelapanCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentDelapanCawangan: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [yuranKemasukan, setYuranKemasukan] = useState("");
  const [yuranKemasukanKata, setYuranKemasukanKata] = useState("");
  const [jumlahYuran, setJumlahYuran] = useState("");
  const [jumlahYuranKata, setJumlahYuranKata] = useState("");
  const [yuranSeumurHidup, setYuranSeumurHidup] = useState("");
  const [yuranSeumurHidupKata, setYuranSeumurHidupKata] = useState("");
  const [jenisYuran, setJenisYuran] = useState(t("monthly"));
  const [tempohPembayaran, setTempohPembayaran] = useState("");
  const [ketetapanPembayaran, setKetetapanPembayaran] = useState(t("month"));
  const [tempohMaksimum, setTempohMaksimum] = useState(t("month"));
  const [sumbangan, setSumbangan] = useState("");
  const [aktivitiEkonomi, setAktivitiEkonomi] = useState("");
  const [clauseContentDescription, setClauseContentDescription] = useState<string|undefined>("");
  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState("");
  //const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [bilangan, setBilangan] = useState("6");
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");

  const [isShowYuranSeumurHidup,setIsShowYuranSeumurHidup] = useState("default");

  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!yuranKemasukan) {
      errors.yuranKemasukan = t("fieldRequired");
    }

    if (!yuranKemasukanKata) {
      errors.yuranKemasukanKata = t("fieldRequired");
    }

    if (!jumlahYuran) {
      errors.jumlahYuran = t("fieldRequired");
    }

    if (!jumlahYuranKata) {
      errors.jumlahYuranKata = t("fieldRequired");
    }

    if (!jenisYuran) {
      errors.jenisYuran = t("fieldRequired");
    }

    if (!tempohPembayaran) {
      errors.tempohPembayaran = t("fieldRequired");
    }

    if (!ketetapanPembayaran) {
      errors.ketetapanPembayaran = t("fieldRequired");
    }

    if (!tempohMaksimum) {
      errors.tempohMaksimum = t("fieldRequired");
    }

    return errors;
  };

  /*const clauseContent = `
1. Sumber kewangan Pertubuhan adalah daripada:
   Bayaran masuk:
   RM ${yuranKemasukan || "<<Bayaran Masuk>>"}
   (Ringgit Malaysia ${
     yuranKemasukanKata || "<<Bayaran Masuk Dalam Perkataan>>"
   } Sahaja)

   Yuran:
   RM ${jumlahYuran || "<<Amaun Yuran>>"}
   (Ringgit Malaysia ${
     jumlahYuranKata || "<<Amaun Yuran Dalam Perkataan>>"
   } Sahaja)

   a. Yuran ${
     jenisYuran || "<<Jenis Yuran>>"
   } hendaklah dijelaskan kepada Bendahari terlebih dahulu dalam tempoh ${
    tempohPembayaran || "<<Tempoh Bayaran Yuran>>"
  } hari dari awal tiap-tiap ${
    ketetapanPembayaran || "<<Ketetapan Bayaran Yuran>>"
  }.

   b. Yuran seumur hidup sebanyak RM ${
     yuranSeumurHidup || "<<Yuran Seumur Hidup>>"
   } perlu dijelaskan oleh ahli semasa permohonan.

   c. Ahli yang mempunyai tunggakan yuran lebih daripada ${
     tempohMaksimum || "<<Maksimum Tempoh Tunggakan>>"
   } akan menerima surat peringatan yang ditandatangani oleh Setiausaha atau wakilnya, dan hilanglah hak-hak keistimewaannya sebagai ahli sehingga tunggakannya dijelaskan.

   d. Ahli yang mempunyai tunggakan yuran lebih daripada jumlah yuran bagi tempoh ${
     tempohMaksimum || "<<Maksimum Tempoh Tunggakan>>"
   } dengan sendirinya terhenti daripada menjadi ahli Pertubuhan dan Jawatankuasa boleh mengarahkan supaya tindakan yang sah diambil terhadapnya, dengan syarat mereka berpuas hati yang ahli itu telah menerima kenyataan berkenaan tunggakannya terlebih dahulu.

   e. Jawatankuasa mempunyai kuasa menetapkan yuran masuk semula bagi sesiapa yang telah terlucut keahliannya disebabkan tunggakan yuran.

   f. Yuran khas atau kutipan wang daripada ahli-ahli untuk perkara yang tertentu boleh dipungut dengan persetujuan Mesyuarat Agung. Sekiranya ada ahli yang gagal membayar yuran khas atau wang tersebut dalam tempoh yang telah ditetapkan, maka yuran khas atau wang itu akan dianggap sama seperti tunggakan yuran.

3. Sumbangan: ${sumbangan || "<<Sumbangan>>"}

4. Kegiatan Ekonomi: ${aktivitiEkonomi || "<<Kegiatan Ekonomi>>"}
`;*/

  //const clause8 = localStorage.getItem("clause8");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
  
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent);
        setClauseContentId(clause.clauseContentId);
      }

      if(clause.clauseDescription){
        setClauseContentDescription(clause.clauseDescription);
      }else{
        setClauseContentDescription(undefined);
      }
      setIsEdit(clause?.clauseDescription ? true : clause.edit);
      //setNamaPertubuhan(clause.societyName);
      if(clause.constitutionValues.length > 0){ 
      const fieldMappings: Record<string, (value: string) => void> = {
        "Bayaran Masuk": setYuranKemasukan,
        "Bayaran Masuk Dalam Perkataan": setYuranKemasukanKata,
        "Amaun Yuran": setJumlahYuran,
        "Amaun Yuran Dalam Perkataan": setJumlahYuranKata,
        "Yuran Seumur Hidup": setYuranSeumurHidup,
        "Yuran Seumur Hidup Dalam Perkataan": setYuranSeumurHidupKata,
        "Jenis Yuran": setJenisYuran,
        "Tempoh Pembayaran Yuran": setTempohPembayaran,
        "Ketetapan Pembayaran Yuran": setKetetapanPembayaran,
        "bilangan maksimum tempoh tunggakkan": setBilangan,
        "Maksimum Tempoh Tunggakan Yuran": setTempohMaksimum,
        "Sumbangan": setSumbangan,
        "Kegiatan Ekonomi": setAktivitiEkonomi
      };

      // Object.values(fieldMappings).forEach(setter => setter(''));
      
      if(clause.constitutionValues){
        clause.constitutionValues.forEach((item:any) => {
          const setter = fieldMappings[item.titleName];
          if (setter && item.definitionName) {
            setter(item.definitionName);
          }
        });
      } 
    
    }
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id } = useParams();

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);
  let clauseContent = clauseContentDescription ? clauseContentDescription : clause.clauseContent; 
  clauseContent = clauseContent.replaceAll(
    /<<bayaran masuk>>/gi,
    `<b>${yuranKemasukan || "<<bayaran masuk>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bayaran masuk-dalam perkataan>>/gi,
    `<b>${yuranKemasukanKata || "<<bayaran masuk-dalam perkataan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<amaun yuran>>/gi,
    `<b>${jumlahYuran || "<<amaun yuran>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<amaun yuran-dalam perkataan>>/gi,
    `<b>${jumlahYuranKata || "<<amaun yuran-dalam perkataan>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${tempohPelucutanWaktu || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<yuran seumur hidup>>/gi,
    `<b>${yuranSeumurHidup || "<<yuran seumur hidup>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahPengerusi || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<jawatan Timbalan Pengerusi>>/gi,
    `<b>${yuranSeumurHidupKata || "<<jawatan Timbalan Pengerusi>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis yuran>>/gi,
    `<b>${jenisYuran || "<<jenis yuran>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh bayaran yuran>>/gi,
    `<b>${tempohPembayaran || "<<tempoh bayaran yuran>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<ketetapan bayaran yuran>>/gi,
    `<b>${ketetapanPembayaran || "<<ketetapan bayaran yuran>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<maksimum tempoh tunggakan>>/gi,
    `<b>${tempohMaksimum || "<<maksimum tempoh tunggakan>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahBendahari || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<sumbangan>>/gi,
    `<b>${sumbangan || "<<sumbangan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kegiatan ekonomi>>/gi,
    `<b>${aktivitiEkonomi || "<<kegiatan ekonomi>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bilangan maksimum tempoh tunggakkan>>/gi,
    `<b>${bilangan || "<<bilangan maksimum tempoh tunggakkan>>"}</b>`
  ); 

 const hasYuranSemurHidup = clauseContent.includes("yuran seumur hidup"); 

  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");
 
  // const { data: clauseValueData } = useCustom({
  //   url: `${API_URL}/society/constitutionvalue/getAll`,
  //   method: "get",
  //   config: {
  //     headers: {
  //       portal: localStorage.getItem("portal"),
  //       authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
  //     },
  //     query: {
  //       societyId: societyId,
  //       clauseContentId: 22,
  //     },
  //   },
  //   queryOptions: {
  //     enabled: !!societyId,
  //     onSuccess: (responseData) => {
  //       const data = responseData?.data?.data?.data;
  //       if(data){ 
  //         setIsShowYuranSeumurHidup(data[9].definitionName) 
  //       } 
  //     },
  //   },
  // });

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bayaranMasuk")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("bayaranMasuk")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={yuranKemasukan}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setYuranKemasukan(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranKemasukan: "",
                  }));
                } else {
                  setYuranKemasukan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranKemasukan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.yuranKemasukan}
              helperText={formErrors.yuranKemasukan}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh "
              value={yuranKemasukanKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setYuranKemasukanKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranKemasukanKata: "",
                  }));
                } else {
                  setYuranKemasukanKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranKemasukanKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.yuranKemasukanKata}
              helperText={formErrors.yuranKemasukanKata}
            />
          </Grid>
        </Grid>
      </Box> 
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("typeOfFee")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("typeOfFee")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth required error={!!formErrors.jenisYuran}>
              <Select
                size="small"
                value={jenisYuran}
                displayEmpty
                onChange={(e) => {
                  setJenisYuran(e.target.value as string);
                  if (e.target.value == t("monthly")) {
                    setKetetapanPembayaran(t("month"));
                    setTempohMaksimum(t("month"));
                    setBilangan("6");
                  } else if (e.target.value == t("annual")) {
                    setKetetapanPembayaran(t("year"));
                    setTempohMaksimum(t("year"));
                    setBilangan("3");
                  }
                  setFormErrors((prev) => ({
                    ...prev,
                    jenisYuran: "",
                  }));
                  setFormErrors((prev) => ({
                    ...prev,
                    jenisYuran: "",
                  }));
                }}
              >
                <MenuItem value={t("monthly")}>{t("monthly")}</MenuItem>
                <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
              </Select>
              {formErrors.jenisYuran && (
                <FormHelperText>{formErrors.jenisYuran}</FormHelperText>
              )}
            </FormControl>
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("feeAmount")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={jumlahYuran}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setJumlahYuran(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahYuran: "",
                  }));
                } else {
                  setJumlahYuran("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahYuran: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.jumlahYuran}
              helperText={formErrors.jumlahYuran}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh "
              value={jumlahYuranKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setJumlahYuranKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahYuranKata: "",
                  }));
                } else {
                  setJumlahYuranKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahYuranKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.jumlahYuranKata}
              helperText={formErrors.jumlahYuranKata}
            />
          </Grid>
        </Grid>
      </Box>
 
      {hasYuranSemurHidup ?  <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >

        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("lifetimeFee")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("lifetimeFee")} </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={yuranSeumurHidup}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setYuranSeumurHidup(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranSeumurHidup: "",
                  }));
                } else {
                  setYuranSeumurHidup("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranSeumurHidup: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.yuranSeumurHidup}
              helperText={formErrors.yuranSeumurHidup}
            />
          </Grid>
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              placeholder="contoh : dua puluh "
              required
              value={yuranSeumurHidupKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setYuranSeumurHidupKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranSeumurHidupKata: "",
                  }));
                } else {
                  setYuranSeumurHidupKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranSeumurHidupKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.yuranSeumurHidupKata}
              helperText={formErrors.yuranSeumurHidupKata}
            />
          </Grid>
        </Grid>

      </Box> : null }
       


      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("others")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("feePaymentStipulation")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.ketetapanPembayaran}
            >
              <Select
                size="small"
                value={ketetapanPembayaran}
                disabled
                displayEmpty
                onChange={(e) => {
                  setKetetapanPembayaran(e.target.value as string);
                  setFormErrors((prev) => ({
                    ...prev,
                    ketetapanPembayaran: "",
                  }));
                }}
              >
                <MenuItem value={t("month")}>{t("month")}</MenuItem>
                <MenuItem value={t("year")}>{t("year")}</MenuItem>
              </Select>
              {formErrors.ketetapanPembayaran && (
                <FormHelperText>
                  {formErrors.ketetapanPembayaran}
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("maximumPeriod")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled
              required
              value={bilangan}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setBilangan(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilangan: "",
                  }));
                } else {
                  setBilangan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilangan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.bilangan}
              helperText={formErrors.bilangan}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth required error={!!formErrors.tempohMaksimum}>
              <Select
                size="small"
                value={tempohMaksimum}
                displayEmpty
                disabled
                onChange={(e) => {
                  setTempohMaksimum(e.target.value as string);
                  setFormErrors((prev) => ({
                    ...prev,
                    tempohMaksimum: "",
                  }));
                }}
              >
                <MenuItem value={t("month")}>{t("month")}</MenuItem>
                <MenuItem value={t("year")}>{t("year")}</MenuItem>
              </Select>
              {formErrors.tempohMaksimum && (
                <FormHelperText>{formErrors.tempohMaksimum}</FormHelperText>
              )}
            </FormControl>
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("feePaymentPeriod")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.tempohPembayaran}
            >
              <Select
                size="small"
                value={tempohPembayaran}
                displayEmpty
                onChange={(e) => {
                  setTempohPembayaran(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    tempohPembayaran: "",
                  }));
                }}
              >
                <MenuItem value={"7"}>7</MenuItem>
                <MenuItem value={"14"}>14</MenuItem>
              </Select>
              {formErrors.tempohPembayaran && (
                <FormHelperText>{formErrors.tempohPembayaran}</FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth required>
              <Select
                disabled
                size="small"
                defaultValue={t("day")}
                value={t("day")}
                displayEmpty
              >
                <MenuItem value={t("day")}>{t("day")}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("contribution")}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <RadioGroup
              row
              value={sumbangan}
              onChange={(e) => setSumbangan(e.target.value)}
            >
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("contributionOption")}
                  control={<Radio />}
                  label={t("contributionOption")}
                />
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("none")}
                  control={<Radio />}
                  label={t("none")}
                />
              </Grid>
            </RadioGroup>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("economicActivity")}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <RadioGroup
              row
              value={aktivitiEkonomi}
              onChange={(e) => setAktivitiEkonomi(e.target.value)}
            >
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("economicActivityOption")}
                  control={<Radio />}
                  label={t("economicActivityOption")}
                />
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("none")}
                  control={<Radio />}
                  label={t("none")}
                />
              </Grid>
            </RadioGroup>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
             <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            const errors = validateForm();
            if (Object.keys(errors).length > 0) {
              setFormErrors(errors);
              return;
            }

            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: yuranKemasukan,
                  titleName: "Bayaran Masuk",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: yuranKemasukanKata,
                  titleName: "Bayaran Masuk Dalam Perkataan",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jumlahYuran,
                  titleName: "Amaun Yuran",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jumlahYuranKata,
                  titleName: "Amaun Yuran Dalam Perkataan",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: yuranSeumurHidup,
                  titleName: "Yuran Seumur Hidup",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: yuranSeumurHidupKata,
                  titleName: "Yuran Seumur Hidup Dalam Perkataan",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jenisYuran,
                  titleName: "Jenis Yuran",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tempohPembayaran,
                  titleName: "Tempoh Pembayaran Yuran",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: ketetapanPembayaran,
                  titleName: "Ketetapan Pembayaran Yuran",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bilangan,
                  titleName: "bilangan maksimum tempoh tunggakkan",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tempohMaksimum,
                  titleName: "Maksimum Tempoh Tunggakan Yuran",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: sumbangan,
                  titleName: "Sumbangan",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: aktivitiEkonomi,
                  titleName: "Kegiatan Ekonomi",
                },
              ],
              clause: "clause8",
              clauseCount: 8,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentDelapanCawangan;
