import React, { useState } from "react";
import {
  Box,
  Grid,
  Typography,
  IconButton,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { EditIcon } from "../../../../components/icons";
import { TrashIcon } from "../../../../components/icons";
import Input from "../../../../components/input/Input";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { useSenaraiContext } from "../../SenaraiContext";
import useQuery from "../../../../helpers/hooks/useQuery";
import ConfirmationDialog from "../../../../components/dialog/confirm";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { Bank, Society } from "../interface";
import {
  Controller,
  FieldValues,
  SubmitHandler,
  useForm,
} from "react-hook-form";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { ApplicationStatus } from "@/helpers";
import dayjs from "dayjs";
import { DataTable, IColumn } from "@/components";

export const CreateMam: React.FC = () => {
  type GeneralInfo = {
    categoryCodeJppm: string;
    subCategoryCode: string;
    phoneNumber: string;
    financialYearStart: number;
    regMemberCount: number;
    positionHolderNo: number;
    branchCount: number;
    federation: boolean;
    societyPhoneNo: string;
    committeeCount: number;
  };

  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const [bankId, setBankId] = useState<string | number>("0");

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);
  const [society, setSociety] = useState<Society>(societyDataRedux);
  const [generalInfo, setGeneralInfo] = useState<GeneralInfo>();
  const [totalList, setTotalList] = useState(0);
  const { t } = useTranslation();
  // @ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);

  const isviewStatement = useSelector(
    //@ts-ignore
    (state) => state?.statementData?.isViewStatement
  );
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);
  const [statementComplete, setStatementComplete] = useState(false);

  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;

  const navigate = useNavigate();

  const {
    handleNextPenyataTahunan: handleNext,
    handleBackPenyataTahunan: handleBack,
  } = useSenaraiContext();

  const [bankAccounts, setBankAccounts] = useState<Bank[]>([]);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { data: categoryData, isLoading: isCategoryLoading } = useCustom({
    url: `${API_URL}/society/admin/category/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const categories = categoryData?.data?.data || [];

  const { isLoading: infoIsLoading } = useQuery({
    url: `society/statement/societyInfo/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "year", operator: "eq", value: year },
    ],
    onSuccess: (data) => {
      const generalInfo = data?.data?.data || [];
      if (
        Number(data?.data?.data?.applicationStatusCode) ===
        ApplicationStatus.SELESAI
      ) {
        setStatementComplete(true);
      }
      setGeneralInfo(generalInfo);
      setValue("regMemberCount", generalInfo?.regMemberCount);
      setValue("federation", generalInfo?.federation ? "ya" : "tidak");
    },
  });

  const handleConfirmDelete = (id: string | number) => {
    setBankId(id);
    setOpenConfirm(true);
  };

  const handleBackActions = () => {
    handleBack();
    navigate(-1);
  };

  const { mutate: deleteBank } = useCustomMutation();

  const handleDeleteBank = () => {
    deleteBank(
      {
        url: `${API_URL}/society/statement/bankInfo/${bankId}/delete`,
        method: "put",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
      },
      {
        onSuccess: () => {
          refreshBank();
          setOpenConfirm(false);
        },
      }
    );
  };
  const defaultFormValues = {
    regMemberCount: 0,
    federation: true,
    page: 1,
    pageSize: 5,
  };

  const {
    control,
    setValue,
    watch,
    getValues,
    handleSubmit,
    reset: resetForm,
  } = useForm<FieldValues>({
    defaultValues: defaultFormValues,
  });

  const page = watch("page");
  const pageSize = watch("pageSize");
  
  const { refetch: refreshBank, isLoading: isBankListIsLoading } = useQuery({
    url: `society/statement/bankInfo/list`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      {
        field: "pageSize",
        value: pageSize,
        operator: "eq",
      },
      {
        field: "pageNo",
        value: page,
        operator: "eq",
      },
    ],
    onSuccess: (data) => {
      const bank = data?.data?.data?.data || [];

      setTotalList(data?.data?.data?.total || 0);
      setBankAccounts(bank);
    },
  });

  const columns: IColumn[] = [
    {
      field: "pilihanBank",
      headerName: t("pilihanBank"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.bankName}
          </Box>
        );
      },
    },
    {
      field: "accountNumber",
      headerName: t("accountNumber"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.accountNo}
          </Box>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      renderCell: (params: any) => {
        const row = params.row;
        return (
          <Box sx={{ textAlign: "end" }}>
            {isDisabled || statementComplete ? null : (
              <>
                <IconButton
                  onClick={() =>
                    navigate("../cawangan-penyata-tahunan-bank", {
                      state: {
                        statementId: statementId,
                        societyId: societyId,
                        bankId: row.id,
                      },
                    })
                  }
                >
                  <EditIcon sx={{ color: "var(--primary-color)" }} />
                </IconButton>
                <IconButton onClick={() => handleConfirmDelete(row.id)}>
                  <TrashIcon sx={{ color: "red" }} />
                </IconButton>
              </>
            )}
          </Box>
        );
      },
    },
  ];

  const { mutate: saveGeneralInfo, isLoading: isLoadingSaveBank } =
    useCustomMutation();

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    const payload = {
      regMemberCount: parseInt(data.regMemberCount, 10), // Convert to integer
      federation: data.federation == "ya" ? true : false,
    };
    if (isDisabled || statementComplete) {
      handleNext();
      navigate(`../penyata-tahunan-ajk`, {
        state: {
          societyId: societyId,
          statementId: statementId,
          year: year,
        },
      });
    } else {
      saveGeneralInfo(
        {
          url: `${API_URL}/society/statement/societyInfo/update`,
          method: "put",
          values: {
            ...payload,
            societyId: societyId,
            statementId: statementId,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
        },
        {
          onSuccess: () => {
            handleNext();
            navigate(`../penyata-tahunan-ajk`, {
              state: {
                societyId: societyId,
                statementId: statementId,
                year: year,
              },
            });
          },
        }
      );
    }
  };

  const formatYearStartDate = (val: string | number | undefined) => {
    const newVal = dayjs(val).format("DD-MM");
    return newVal ? newVal : "-";
  };

  const categoryCode = generalInfo?.categoryCodeJppm
    ? categories.find(
        (cat: any) => cat.id === parseInt(society?.categoryCodeJppm)
      )?.categoryNameBm
    : "-";

  const subCategoryCode = generalInfo?.subCategoryCode
    ? categories.find(
        (cat: any) => cat.id === parseInt(society?.subCategoryCode)
      )?.categoryNameBm
    : "-";

  const isDisabled = (!isManager && !isAliranTugasAccess) || isviewStatement;

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("tambahPenyataTahunan")}
          </Typography>
          <Grid>
            <Input
              label={t("organization_category")}
              value={categoryCode ? categoryCode : "-"}
              disabled
            />
            <Input
              label={t("sub_category")}
              disabled
              value={subCategoryCode ? subCategoryCode : "-"}
            />
            <Input
              label={t("organization_phone")}
              disabled
              value={
                generalInfo?.societyPhoneNo ? generalInfo?.societyPhoneNo : "-"
              }
            />
            <Input
              label={t("financial_year_start")}
              disabled
              value={formatYearStartDate(generalInfo?.financialYearStart)}
            />
            <Controller
              name="regMemberCount"
              control={control}
              defaultValue={getValues("regMemberCount")} // Default value or empty string
              render={({ field }) => (
                <Input
                  {...field}
                  label={t("registered_members_count")}
                  type="number"
                  required
                  disabled
                  defaultValue={generalInfo?.regMemberCount ?? 0}
                />
              )}
            />
            <Input
              label={t("office_bearers_count")}
              type="number"
              required
              disabled
              value={generalInfo?.committeeCount ?? 0}
            />
            <Input
              label={t("branches_count")}
              disabled
              value={
                !generalInfo?.branchCount ? t("none") : generalInfo?.branchCount
              }
            />
            <Controller
              name="federation"
              control={control}
              defaultValue={getValues("federation") === true ? "ya" : "tidak"} // Default value or empty string
              render={({ field }) => (
                <Input
                  {...field}
                  type="select"
                  label={t("affiliations")}
                  required
                  disabled={isDisabled || statementComplete}
                  options={[
                    { value: "tidak", label: t("no") },
                    { value: "ya", label: t("yes") },
                  ]}
                  // value={getValues("federation") === true ? "ya" : "tidak"}

                  onChange={(e) => setValue("federation", e.target.value)}
                />
              )}
            />
          </Grid>
        </Box>
      </Box>

      {/* <CreateAccountBankDialog
          open={dialogSaveOpen}
          onClose={() => setDialogSaveOpen(false)}
        /> */}
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{ ...sectionStyle, borderRadius: "10px" }}
          >
            {t("organizationBankAccountInfo")}
          </Typography>

          <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
            {!isDisabled && !statementComplete ? (
              <ButtonPrimary
                type="button"
                sx={{
                  bgcolor: "transparent",
                  color: "#666666",
                  boxShadow: "none",
                  border: "1px solid #67D1D1",
                  fontSize: "12px",
                  fontWeight: "500 !important",
                }}
                onClick={() =>
                  navigate(
                    `/pertubuhan/society/${societyId}/senarai/penyataTahunan/penyata-tahunan-bank`,
                    {
                      state: {
                        statementId: statementId,
                        societyId: societyId,
                      },
                    }
                  )
                }
              >
                {t("registerBankAccount")}
              </ButtonPrimary>
            ) : null}
          </Box>

          <Box
            sx={{
              mt: 2,
              mb: 2,
              border: "1px solid #e0e0e0",
              borderRadius: "4px",
              overflow: "hidden",
            }}
          >
            <DataTable
              columns={columns}
              rows={bankAccounts}
              page={page}
              rowsPerPage={pageSize}
              totalCount={totalList}
              onPageChange={(newPage) => setValue("page", newPage)}
              onPageSizeChange={(newPageSize) => {
                setValue("page", 1);
                setValue("pageSize", newPageSize);
              }}
              isLoading={isBankListIsLoading}
            />
          </Box>

          <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 4 }}>
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline
                sx={{
                  bgcolor: "white",
                  "&:hover": { bgcolor: "white" },
                }}
                onClick={handleBackActions}
              >
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                type="submit"
                variant="contained"
                // onClick={handleNextActions}
                // disabled={createFeedBackIsloading}
              >
                {t("next")}
              </ButtonPrimary>
            </Grid>
          </Box>
        </Box>
        <ConfirmationDialog
          status={1}
          open={openConfirm}
          onClose={() => setOpenConfirm(false)}
          title={t("confirmDelete")}
          message={`${t("deleteConfirmationMessage")}`}
          onConfirm={handleDeleteBank}
          onCancel={() => setOpenConfirm(false)}
        />
      </Box>
    </form>
  );
};

export default CreateMam;
