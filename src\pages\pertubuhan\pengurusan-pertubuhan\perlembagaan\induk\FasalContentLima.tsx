import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { useSelector } from "react-redux";
import { API_URL } from "../../../../../api";
import { FasalContentProps } from "../Fasal";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";

interface FasalContentLimaProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentLima: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [notis, setNotis] = useState("");
  const [notisWaktu, setNotisWaktu] = useState(t("day"));
  const [tempoh, setTempoh] = useState("");
  const [tempohWaktu, setTempohWaktu] = useState(t("day"));
  const [combinedTempoh, setCombinedTempoh] = useState("");
  const [combinedNotis, setCombinedNotis] = useState("");

  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  //const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!notis) {
      errors.notis = t("fieldRequired");
    }

    if (!tempoh) {
      errors.tempoh = t("fieldRequired");
    }

    return errors;
  };

  /*const clauseContent = `
              1. Ahli yang hendak berhenti daripada menjadi ahli Pertubuhan
              hendaklah memberi kenyataan bertulis ${
                notis || "<<Notis Berhenti>>"
              } ${
    notisWaktu || ""
  } terlebih dahulu kepada Setiausaha dan menjelaskan segala
              terlebih dahulu kepada Setiausaha dan menjelaskan segala
              tunggakannya.

              2. Mana-mana ahli yang gagal mematuhi Perlembagaan Pertubuhan atau
              bertindak dengan cara yang akan mencemarkan nama baik Pertubuhan
              boleh dipecat atau digantung keahliannya bagi suatu tempoh masa
              yang difikirkan munasabah oleh Jawatankuasa. Sebelum Jawatankuasa
              memecat atau menggantung keahlian ahli tersebut, ahli itu
              hendaklah diberitahu akan sebab-sebab bagi pemecatan atau
              penggantungannya secara bertulis. Ahli tersebut juga hendaklah
              diberi peluang dalam tempoh ${tempoh || "<<Tempoh Bela Diri>>"} ${
    tempohWaktu || ""
  } dari tarikh pemecatan atau penggantungannya untuk memberi
              penjelasan dan membela dirinya. Pemecatan atau penggantungannya
              itu hendaklah dilaksanakan melainkan keputusan Mesyuarat Agung
              menunda atau membatalkan keputusan itu atas rayuan oleh ahli
              tersebut.
`;*/

  //const clause5 = localStorage.getItem("clause5");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause = JSON.parse(clause5);
      setDataId(clause.id);
      setClauseContentId(clause.clauseContentId);
      //setNamaPertubuhan(clause.societyName);
      setNotis(clause.constitutionValues[0]?.definitionName);
      setNotisWaktu(clause.constitutionValues[1]?.definitionName);
      setTempoh(clause.constitutionValues[2]?.definitionName);
      setTempohWaktu(clause.constitutionValues[3]?.definitionName);
      if (
        clause.constitutionValues[0]?.definitionName &&
        clause.constitutionValues[1]?.definitionName &&
        clause.constitutionValues[2]?.definitionName &&
        clause.constitutionValues[3]?.definitionName
      ) {
        setCombinedNotis(
          `${clause.constitutionValues[0]?.definitionName} ${clause.constitutionValues[1]?.definitionName}`
        );
        setCombinedTempoh(
          `${clause.constitutionValues[2]?.definitionName} ${clause.constitutionValues[3]?.definitionName}`
        );
      }
      setIsEdit(clause.edit);
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  const { id } = useParams();

  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<NOTIS BERHENTI>>/gi,
    `<b>${combinedNotis || "<<NOTIS BERHENTI>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<TEMPOH BELA DIRI>>/gi,
    `<b>${combinedTempoh || "<<TEMPOH BELA DIRI>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          Pemberhentian dan Pemecatan Ahli
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("terminationNotice")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              value={notis}
              onChange={(e) => {
                setNotis(e.target.value);
                if (notisWaktu) {
                  setCombinedNotis(`${e.target.value} ${notisWaktu}`);
                }
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  notis: "",
                }));
              }}
              error={!!formErrors.notis}
              helperText={formErrors.notis}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth required>
              <Select
                size="small"
                value={notisWaktu}
                displayEmpty
                onChange={(e) => {
                  setNotisWaktu(e.target.value as string);
                  if (notis) {
                    setCombinedNotis(`${notis} ${e.target.value as string}`);
                  }
                }}
              >
                <MenuItem value={t("day")} selected>
                  {t("day")}
                </MenuItem>
                <MenuItem value={t("week")}>{t("week")}</MenuItem>
                <MenuItem value={t("month")}>{t("month")}</MenuItem>
                <MenuItem value={t("year")}>{t("year")}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("periodOfDefendingOneself")}{" "}
              <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              value={tempoh}
              onChange={(e) => {
                setTempoh(e.target.value);
                if (tempohWaktu) {
                  setCombinedTempoh(`${e.target.value} ${tempohWaktu}`);
                }
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  tempoh: "",
                }));
              }}
              error={!!formErrors.tempoh}
              helperText={formErrors.tempoh}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth required>
              <Select
                size="small"
                value={tempohWaktu}
                displayEmpty
                onChange={(e) => {
                  setTempohWaktu(e.target.value as string);
                  if (tempoh) {
                    setCombinedTempoh(`${tempoh} ${e.target.value as string}`);
                  }
                }}
              >
                <MenuItem value={t("day")} selected>
                  {t("day")}
                </MenuItem>
                <MenuItem value={t("week")}>{t("week")}</MenuItem>
                <MenuItem value={t("month")}>{t("month")}</MenuItem>
                <MenuItem value={t("year")}>{t("year")}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 2 }}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            const errors = validateForm();
            if (Object.keys(errors).length > 0) {
              setFormErrors(errors);
              return;
            }

            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: notis,
                  titleName: "Notis Berhenti Menjadi Ahli",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: notisWaktu,
                  titleName: "Notis Berhenti Menjadi Ahli",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tempoh,
                  titleName: "Tempoh Bela Diri",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tempohWaktu,
                  titleName: "Tempoh Bela Diri",
                },
              ],
              clause: "clause5",
              clauseCount: 5,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentLima;
