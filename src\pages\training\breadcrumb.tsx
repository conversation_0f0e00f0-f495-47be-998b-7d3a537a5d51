import React from "react";
import {Box, IconButton, Typography} from "@mui/material";
import {useNavigate} from "react-router-dom";


const TrainingBreadcrumb: React.FC<{isAdmin:boolean}> = ({isAdmin}) => {
  const navigate = useNavigate();
  const breadcrumbs = [
    {
      label: "Latihan",
      path: isAdmin ? "/latihan-internal" : "/latihan",
    },
  ];
  return (
    <>
      <Box
        sx={{
          mb: 2,
          display: "flex",
          alignItems: "center",
          gap: 1,
          mx: 3,
          mt: 3,
        }}
      >
        <IconButton onClick={() => navigate(-1)}>
          <svg
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8.03065 10.7726C8.17126 10.632 8.25024 10.4412 8.25024 10.2423C8.25024 10.0435 8.17126 9.85274 8.03065 9.7121L4.31815 5.9996L8.03065 2.2871C8.16727 2.14565 8.24287 1.95619 8.24116 1.75955C8.23945 1.5629 8.16057 1.37479 8.02152 1.23573C7.88246 1.09668 7.69435 1.0178 7.4977 1.01609C7.30106 1.01438 7.1116 1.08998 6.97015 1.2266L2.7274 5.46935C2.5868 5.60999 2.50781 5.80072 2.50781 5.9996C2.50781 6.19847 2.5868 6.3892 2.7274 6.52985L6.97015 10.7726C7.1108 10.9132 7.30153 10.9922 7.5004 10.9922C7.69928 10.9922 7.89001 10.9132 8.03065 10.7726Z"
              fill="#666666"
            />
          </svg>
        </IconButton>
        <Box sx={{display: "flex", alignItems: "center", gap: 1}}>
          {breadcrumbs.map((item, index) => (
            <React.Fragment key={item.path}>
              <Typography
                sx={{
                  color: "#666666",
                  fontSize: "18px",
                  cursor: "pointer",
                  "&:hover": {
                    textDecoration: "underline",
                  },
                }}
                onClick={() => navigate(item.path)}
              >
                {item.label}
              </Typography>
              {index < breadcrumbs.length - 1 && (
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 12 12"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8.03065 10.7726C8.17126 10.632 8.25024 10.4412 8.25024 10.2423C8.25024 10.0435 8.17126 9.85274 8.03065 9.7121L4.31815 5.9996L8.03065 2.2871C8.16727 2.14565 8.24287 1.95619 8.24116 1.75955C8.23945 1.5629 8.16057 1.37479 8.02152 1.23573C7.88246 1.09668 7.69435 1.0178 7.4977 1.01609C7.30106 1.01438 7.1116 1.08998 6.97015 1.2266L2.7274 5.46935C2.5868 5.60999 2.50781 5.80072 2.50781 5.9996C2.50781 6.19847 2.5868 6.3892 2.7274 6.52985L6.97015 10.7726C7.1108 10.9132 7.30153 10.9922 7.5004 10.9922C7.69928 10.9922 7.89001 10.9132 8.03065 10.7726Z"
                    fill="#666666"
                  />
                </svg>
              )}
            </React.Fragment>
          ))}
        </Box>
      </Box>
    </>
  );
}

export default TrainingBreadcrumb;
