import { Box, Grid, Typography } from "@mui/material";
import { t } from "i18next";
import { useEffect, useState } from "react";
import { ApplicationStatusEnum } from "../../../../helpers/enums";
import ButtonPrevious from "../../../../components/button/ButtonPrevious";
import { ButtonPrimary } from "../../../../components/button";
import { Controller, FieldValues, useForm } from "react-hook-form";
import DataTable, { IColumn } from "@/components/datatable";
import Input from "@/components/input/Input";
import { getMalaysiaAddressList, useQuery } from "@/helpers";
import { CrudFilter } from "@refinedev/core";
import { MaklumatTabProps } from "../maklumatSelectionTabs";
import ForbiddenPage from "@/pages/forbidden";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "30px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

function PindaanTab({ disabled }: MaklumatTabProps) {
  if (disabled) {
    return <ForbiddenPage internal />;
  } else {
    const { control, setValue, watch, getValues, reset, handleSubmit } =
      useForm<FieldValues>({
        defaultValues: {
          pageNo: 1,
          pageSize: 10,
          searchQuery: "",
        },
      });

    const [total, setTotal] = useState<number>(0);
    const [statementList, setStatementList] = useState([]);

    const malaysiaAddressList = getMalaysiaAddressList() ?? [];

    const getStateName = (stateCode: any) => {
      const stateName = malaysiaAddressList.filter(
        (i: any) => i.id == stateCode
      );
      return stateName[0]?.name ? stateName[0]?.name : "-";
    };

    const handleClearSearch = () => {
      reset();
      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: 10 },
        { field: "pageNo", operator: "eq", value: 1 },
        {
          field: "isBranch",
          value: true,
          operator: "eq",
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: null,
        },
      ];
      refetchStatementList({ filters });
    };

    const handleSearch = () => {
      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: getValues("pageSize") },
        { field: "pageNo", operator: "eq", value: getValues("pageNo") },
        {
          field: "isBranch",
          value: true,
          operator: "eq",
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      refetchStatementList({ filters });
    };

    const columns: IColumn[] = [
      {
        field: "societyName",
        headerName: `${t("organizationName")} : ${t("branchName")}`,
        align: "center",
        renderCell: ({ row }: any) => {
          return (
            <Box>
              {row?.societyName} : {row?.branchName}
            </Box>
          );
        },
      },
      {
        field: "ro",
        headerName: t("roBertanggungjawab"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.ro ?? "-"}</Box>;
        },
      },
      {
        field: "branchNo",
        headerName: t("noPPMCawangan"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.branchNo ?? "-"}</Box>;
        },
      },
      {
        field: "meetingDate",
        headerName: t("meetingDate"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.meetingDate ?? "-"}</Box>;
        },
      },
      {
        field: "paymentDate",
        headerName: t("tarikhBayar"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.paymentDate ?? "-"}</Box>;
        },
      },
      {
        field: "stateId",
        headerName: t("branchState"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{getStateName(row?.stateId)}</Box>;
        },
      },
      {
        field: "statusCawangan",
        headerName: t("statusCawangan"),

        align: "center",
        renderCell: ({ row }: any) => {
          return (
            <Box>
              {t(
                ApplicationStatusEnum[
                  (row?.applicationStatusCode as keyof typeof ApplicationStatusEnum) ||
                    "0"
                ]
              )}
            </Box>
          );
        },
      },
    ];

    const handleChangePage = (newPage: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: watch("pageSize"), operator: "eq" },
        { field: "pageNo", value: newPage, operator: "eq" },
        {
          field: "isBranch",
          value: true,
          operator: "eq",
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      setValue("pageNo", newPage);
      refetchStatementList({ filters });
    };

    const handleChangePageSize = (newPageSize: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: newPageSize, operator: "eq" },
        { field: "pageNo", value: 1, operator: "eq" },
        {
          field: "isBranch",
          value: true,
          operator: "eq",
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      setValue("pageNo", 1);
      setValue("pageSize", newPageSize);
      refetchStatementList({ filters });
    };

    const {
      data: statementListData,
      isLoading: statementListIsLoading,
      refetch: refetchStatementList,
    } = useQuery({
      url: `society/admin/branch/amendment/findAllByParam`,
      filters: [
        {
          field: "pageSize",
          value: watch("pageSize"),
          operator: "eq",
        },
        {
          field: "pageNo",
          value: watch("pageNo"),
          operator: "eq",
        },
        {
          field: "isBranch",
          value: true,
          operator: "eq",
        },
      ],
      autoFetch: false,
      onSuccess: (data) => {
        const statementData = data?.data?.data?.data || [];
        const total = data?.data?.data?.total;
        setTotal(total);
        setStatementList(statementData);
      },
    });

    useEffect(() => {
      const filters: CrudFilter[] = [
        {
          field: "pageSize",
          value: watch("pageSize"),
          operator: "eq",
        },
        {
          field: "pageNo",
          value: watch("pageNo"),
          operator: "eq",
        },
        {
          field: "isBranch",
          value: true,
          operator: "eq",
        },
      ];
      refetchStatementList({ filters });
    }, []);

    return (
      <>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <form onSubmit={handleSubmit(handleSearch)}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography sx={sectionStyle}>
                {t("pindaanNameAlamatBranch")}
              </Typography>
              <Box>
                {/* search */}
                <Controller
                  name="searchQuery"
                  control={control}
                  render={({ field }) => (
                    <Input {...field} label={t("search")} />
                  )}
                />
              </Box>
              <Grid container mt={1} spacing={2}>
                <Grid
                  item
                  xs={12}
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <ButtonPrevious onClick={handleClearSearch}>
                    {t("previous")}
                  </ButtonPrevious>
                  <ButtonPrimary type="submit">{t("search")}</ButtonPrimary>
                </Grid>
              </Grid>
            </Box>
          </form>
        </Box>

        {/* ============= */}
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              textAlign: "center",
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              py: 2,
            }}
          >
            <Typography variant="h5" gutterBottom>
              {total}
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
              {t("rekodDijumpai")}
            </Typography>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mt: 2,
            }}
          >
            <Typography sx={sectionStyle}>
              {t("senaraiPindaanNamadanAlamatCawangan")}
            </Typography>
            <DataTable
              columns={columns}
              rows={statementList}
              page={watch("pageNo")}
              rowsPerPage={watch("pageSize")}
              totalCount={total}
              onPageChange={handleChangePage}
              onPageSizeChange={handleChangePageSize}
              isLoading={statementListIsLoading}
              customNoDataText={t("noRecordForStatusBranch")}
            />
          </Box>
        </Box>
      </>
    );
  }
}

export default PindaanTab;
