import {
  Box,
  CircularProgress,
  Grid,
  Icon<PERSON>utton,
  Typography,
} from "@mui/material";
import { t } from "i18next";
import { useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import ButtonPrevious from "../../../../components/button/ButtonPrevious";
import { ButtonPrimary } from "../../../../components/button";
import { EyeIcon } from "../../../../components/icons";
import DataTable, { IColumn } from "@/components/datatable";
import {
  ApplicationStatusList,
  formatDate,
  getMalaysiaAddressList,
  MALAYSIA,
  useQuery,
} from "@/helpers";
import { API_URL } from "@/api";
import { CrudFilter, useCustom } from "@refinedev/core";
import Input from "@/components/input/Input";
import { Controller, FieldValues, useForm } from "react-hook-form";
import ForbiddenPage from "@/pages/forbidden";
import { MaklumatTabProps } from "../maklumatSelectionTabs";

function PembubaranTab({ disabled }: MaklumatTabProps) {
  if (disabled) {
    return <ForbiddenPage internal />;
  } else {
    const navigate = useNavigate();

    const [displaySenaraiAjk, setDisplaySenaraiAjk] = useState<any[]>([]);
    const [total, setTotal] = useState<number>(0);
    const { control, setValue, watch, getValues, reset, handleSubmit } =
      useForm<FieldValues>({
        defaultValues: {
          pageNo: 1,
          pageSize: 10,
          state: "",
          searchQuery: "",
        },
      });

    const { data, isLoading, refetch } = useQuery({
      url: `society/admin/liquidation/findAllByParam`,
      filters: [
        {
          field: "pageSize",
          value: watch("pageSize"),
          operator: "eq",
        },
        {
          field: "pageNo",
          value: watch("pageNo"),
          operator: "eq",
        },
      ],
      autoFetch: false,
      onSuccess: (data) => {
        const AJKdata = data?.data?.data?.data || [];
        const total = data?.data?.data?.total;
        setTotal(total);
        setDisplaySenaraiAjk(AJKdata);
      },
    });

    const { data: addressList, isLoading: isAddressLoading } = useCustom({
      url: `${API_URL}/society/admin/address/list`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
    });
    const StateList = addressList?.data?.data
      .filter((item: any) => item.pid === MALAYSIA)
      .map((item: any) => ({ value: item.id, label: item.name }));

    const sectionStyle = {
      color: "var(--primary-color)",
      marginBottom: "30px",
      borderRadius: "16px",
      fontSize: "14px",
      fontWeight: "500 !important",
    };

    const columns: IColumn[] = [
      {
        field: "societyName",
        headerName: t("pertubuhan"),
        align: "center",
        renderCell: (params) => (
          <Box>
            <Box>{params.row.societyName ?? "-"}</Box>
          </Box>
        ),
      },
      {
        field: "noPengenalanDiri",
        headerName: t("noPPM"),
        align: "center",

        renderCell: (params) => <Box>{params.row.societyNo ?? "-"}</Box>,
      },
      {
        field: "applicationStatusCode",
        headerName: t("applicationStatus"),
        align: "center",
        renderCell: (params: any) => {
          const status = ApplicationStatusList.find(
            (item) => item.id === params?.row?.applicationStatusCode
          );
          return status ? t(status.value) : t("-");
        },
      },
      {
        field: "submissionDate",
        headerName: t("tarikhPermohonan"),
        align: "center",
        renderCell: (params) => (
          <Box>{formatDate(params.row.submissionDate)}</Box>
        ),
      },
      {
        field: "societyStateCode",
        headerName: t("state"),
        align: "center",
        renderCell: (params: any) => {
          const row = params?.row;
          const state = StateList?.find(
            (item: any) => Number(item.value) === Number(row?.societyStateCode)
          );
          return (
            <>
              {isAddressLoading ? (
                <CircularProgress size={15} />
              ) : (
                <Typography
                  style={{
                    fontSize: 14,
                    color: "var(--text-grey)",
                    fontWeight: 400,
                  }}
                >
                  {state?.label ? state?.label : "-"}
                </Typography>
              )}
            </>
          );
        },
      },

      {
        field: "actions",
        headerName: "",
        align: "right",
        renderCell: (params: any) => {
          const encodedSocietyId = btoa(
            params?.row?.societyId?.toString() || ""
          );
          const encodedLiquidationId = btoa(params?.row?.id?.toString() || "");
          return (
            <>
              <IconButton
                sx={{ color: "black" }}
                onClick={() =>
                  navigate(
                    `pertubuhan/pembubaran/${encodedSocietyId}/${encodedLiquidationId}`
                  )
                }
              >
                <EyeIcon />
              </IconButton>
            </>
          );
        },
      },
    ];

    const handleClearSearch = () => {
      reset();
      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: 10 },
        { field: "pageNo", operator: "eq", value: 1 },
        {
          field: "state",
          operator: "eq",
          value: null,
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: null,
        },
      ];
      refetch({ filters });
    };

    const malaysiaAddressList = getMalaysiaAddressList() ?? [];
    const stateOptions = useMemo(() => {
      return malaysiaAddressList.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
    }, []);

    const handleSearch = () => {
      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: getValues("pageSize") },
        { field: "pageNo", operator: "eq", value: getValues("pageNo") },
        {
          field: "state",
          operator: "eq",
          value: getValues("state"),
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      refetch({ filters });
    };

    const handleChangePage = (newPage: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: watch("pageSize"), operator: "eq" },
        { field: "pageNo", value: newPage, operator: "eq" },
        {
          field: "state",
          operator: "eq",
          value: getValues("state"),
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      setValue("pageNo", newPage);
      refetch({ filters });
    };

    const handleChangePageSize = (newPageSize: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: newPageSize, operator: "eq" },
        { field: "pageNo", value: 1, operator: "eq" },
        {
          field: "isBranch",
          value: true,
          operator: "eq",
        },
        {
          field: "state",
          operator: "eq",
          value: getValues("state"),
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      setValue("pageNo", 1);
      setValue("pageSize", newPageSize);
      refetch({ filters });
    };

    useEffect(() => {
      const filters: CrudFilter[] = [
        {
          field: "pageSize",
          value: 10,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: 1,
          operator: "eq",
        },
      ];
      refetch({ filters });
    }, []);

    return (
      <>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <form onSubmit={handleSubmit(handleSearch)}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("senaraiPertubuhan")}
              </Typography>

              {/* state */}
              <Controller
                name="state"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="select"
                    label={t("state")}
                    options={stateOptions}
                  />
                )}
              />

              {/* finding/carian */}
              <Controller
                name="searchQuery"
                control={control}
                render={({ field }) => <Input {...field} label={t("search")} />}
              />
              <Grid container mt={3} spacing={2}>
                <Grid
                  item
                  xs={12}
                  sx={{
                    mt: 2,
                    display: "flex",
                    // flexDirection: isMobile ? "column" : "row",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <ButtonPrevious onClick={handleClearSearch}>
                    {t("previous")}
                  </ButtonPrevious>
                  <ButtonPrimary type="submit">{t("search")}</ButtonPrimary>
                </Grid>
              </Grid>
            </Box>
          </form>
        </Box>
        {/* ============= */}
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              textAlign: "center",
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              py: 2,
            }}
          >
            <Typography variant="h5" gutterBottom>
              {total}
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
              {t("rekodDijumpai")}
            </Typography>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mt: 2,
            }}
          >
            <DataTable
              columns={columns as any[]}
              rows={displaySenaraiAjk}
              page={watch("pageNo")}
              rowsPerPage={watch("pageSize")}
              isLoading={isLoading}
              totalCount={total}
              onPageChange={handleChangePage}
              onPageSizeChange={handleChangePageSize}
            />
          </Box>
        </Box>
      </>
    );
  }
}

export default PembubaranTab;
