import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { GridColDef } from "@mui/x-data-grid";
import {
  Box,
  Typography,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  TextField,
  InputAdornment,
  Alert,
  Menu,
  MenuItem,
  Button,
  ListItemIcon,
  Dialog,
  DialogTitle,
  DialogContent,
  SvgIcon,
  Tooltip,
} from "@mui/material";
import { Search, FilterList, Close } from "@mui/icons-material";
import CampaignIcon from "@mui/icons-material/Campaign";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility";
import ListAltIcon from "@mui/icons-material/ListAlt";
import PaymentIcon from "@mui/icons-material/Payment";
import DeleteIcon from "@/assets/svg/icon-trash.svg?react";
import CloseIcon from "@mui/icons-material/Close";
import InfoIcon from "@mui/icons-material/Info";
import CheckIcon from "@mui/icons-material/Check";
import { API_URL } from "../../../api";
import CustomDataGrid from "../../../components/datagrid";
import { HideOrDisplayInherit } from "../../../helpers/enums";

export const ListSenaraiPertubuhan: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openMenuId, setOpenMenuId] = useState<number | null>(null);
  const [showAlert, setShowAlert] = useState(true);
  const [openSearchModal, setOpenSearchModal] = useState(false);
  const [openSertaModal, setOpenSertaModal] = useState(false);

  const [searchPertubuhan, setSearchPertubuhan] = useState("");
  const [showNoResults, setShowNoResults] = useState(false);

  const columns: GridColDef[] = [
    {
      field: "societyName",
      headerName: t("namaPertubuhan"),
      flex: 1,
      renderCell: (params: any) => (
        <span
          style={{
            cursor:
              params.row.applicationStatusCode == "1"
                ? "pointer"
                : "not-allowed",
            pointerEvents:
              params.row.applicationStatusCode == "1" ? "auto" : "none",
          }}
          onClick={() =>
            params.row.applicationStatusCode == "1" &&
            navigate(`society/${params.row.id}/dashboard`)
          }
        >
          {params.value}
        </span>
      ),
    },
    {
      field: "applicationStatusCode",
      headerName: t("applicationStatus"),
      flex: 1,
      renderCell: (params: any) =>
        params.row.applicationStatusCode == "1"
          ? t("lulus")
          : params.row.applicationStatusCode == "0"
          ? t("pending")
          : params.row.applicationStatusCode == "2"
          ? t("tolak")
          : "",
    },
    {
      field: "statusCode",
      headerName: t("organizationStatus"),
      flex: 1,
      renderCell: (params: any) =>
        params.row.statusCode == "1"
          ? t("active")
          : params.row.statusCode == "0"
          ? t("inactive")
          : "",
    },
    {
      field: "societyLevel",
      headerName: t("jenisPertubuhan"),
      flex: 1,
      renderCell: (params: any) => params.row.societyLevel ?? "-",
    },
    {
      field: "statementData",
      headerName: t("dateApplied"),
      flex: 1,
      renderCell: (params: any) => params.row.statementData ?? "-",
    },
    {
      field: "jawatan",
      headerName: t("position"),
      flex: 1,
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      align: "right",
      renderCell: (params: any) => {
        const row = params.row;
        return row.applicationStatusCode == "0" ? (
          <>
            <Button sx={{ color: "red", display: HideOrDisplayInherit }}>
              <DeleteIcon />
            </Button>
            <Button sx={{ color: "black", display: HideOrDisplayInherit }}>
              <EditIcon />
            </Button>
          </>
        ) : null;
      },
    },
  ];

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchPertubuhan(e.target.value);
    setShowNoResults(e.target.value.length > 0);
  };

  const [isSerta, setIsSerta] = useState(true);

  const handleMenuClose = () => {
    setAnchorEl(null);
    setOpenMenuId(null);
  };
  const handleOpenSearchModal = () => {
    setOpenSearchModal(true);
  };

  const handleOpenSertaModal = () => {
    setOpenSertaModal(true);
  };

  const handleCloseSertaModal = () => {
    setOpenSertaModal(false);
  };

  const handleSertaClick = () => {
    setIsSerta(!isSerta);
  };

  const BatalIcon = () => (
    <SvgIcon>
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_5370_2840)">
          <path
            d="M10.525 9.3C10.554 9.27589 10.5768 9.24509 10.5913 9.21026C10.6058 9.17542 10.6117 9.13759 10.6083 9.1C10.6011 9.06458 10.5851 9.03154 10.5618 9.00388C10.5385 8.97623 10.5087 8.95484 10.475 8.94167L9.58333 8.60833L9.05833 8.41667C8.99685 8.34266 8.95654 8.25339 8.94167 8.15833C8.86549 7.79888 8.8827 7.42591 8.99167 7.075C9.43625 6.59397 9.77499 6.02503 9.98599 5.40493C10.197 4.78483 10.2756 4.12736 10.2167 3.475C10.2532 3.03805 10.1998 2.59825 10.0597 2.18275C9.91962 1.76725 9.69586 1.38487 9.40223 1.05923C9.10861 0.733583 8.75135 0.471582 8.35251 0.289401C7.95367 0.10722 7.52172 0.00872563 7.08333 0C6.64494 0.00872563 6.213 0.10722 5.81416 0.289401C5.41532 0.471582 5.05806 0.733583 4.76443 1.05923C4.47081 1.38487 4.24705 1.76725 4.10697 2.18275C3.96689 2.59825 3.91347 3.03805 3.95 3.475C3.88974 4.12423 3.96695 4.77888 4.17658 5.39629C4.38621 6.01369 4.72356 6.58001 5.16667 7.05833C5.28285 7.41343 5.30298 7.79294 5.225 8.15833C5.21013 8.25339 5.16981 8.34266 5.10833 8.41667L4.58333 8.60833C2.54167 9.35833 1.075 9.9 0.625 10.8333C0.22772 11.7553 0.0153682 12.7462 0 13.75C0 13.8605 0.0438987 13.9665 0.122039 14.0446C0.200179 14.1228 0.30616 14.1667 0.416667 14.1667H7.74167C7.79415 14.1668 7.84476 14.1472 7.88339 14.1117C7.92201 14.0761 7.9458 14.0273 7.95 13.975C8.03403 13.0604 8.30614 12.173 8.74924 11.3686C9.19235 10.5641 9.79691 9.85984 10.525 9.3Z"
            fill="white"
          />
          <path
            d="M14.5827 9.16699C13.5114 9.16699 12.4641 9.48467 11.5733 10.0799C10.6826 10.6751 9.98831 11.521 9.57834 12.5108C9.16836 13.5006 9.06109 14.5897 9.2701 15.6404C9.4791 16.6911 9.99499 17.6563 10.7525 18.4138C11.5101 19.1714 12.4752 19.6872 13.5259 19.8962C14.5767 20.1053 15.6658 19.998 16.6556 19.588C17.6453 19.178 18.4913 18.4838 19.0865 17.593C19.6817 16.7022 19.9994 15.655 19.9994 14.5837C19.9971 13.1477 19.4258 11.7713 18.4104 10.7559C17.3951 9.74059 16.0186 9.1692 14.5827 9.16699ZM17.2327 16.0587C17.3879 16.2148 17.475 16.426 17.475 16.6462C17.475 16.8663 17.3879 17.0775 17.2327 17.2337C17.0766 17.3889 16.8653 17.476 16.6452 17.476C16.425 17.476 16.2138 17.3889 16.0577 17.2337L14.8744 16.0587C14.8369 16.0188 14.7918 15.987 14.7416 15.9653C14.6914 15.9436 14.6373 15.9324 14.5827 15.9324C14.528 15.9324 14.4739 15.9436 14.4238 15.9653C14.3736 15.987 14.3284 16.0188 14.291 16.0587L13.1077 17.2337C12.9515 17.3889 12.7403 17.476 12.5202 17.476C12.3 17.476 12.0888 17.3889 11.9327 17.2337C11.7775 17.0775 11.6904 16.8663 11.6904 16.6462C11.6904 16.426 11.7775 16.2148 11.9327 16.0587L13.1077 14.8753C13.1475 14.8379 13.1793 14.7927 13.201 14.7426C13.2227 14.6924 13.2339 14.6383 13.2339 14.5837C13.2339 14.529 13.2227 14.4749 13.201 14.4247C13.1793 14.3746 13.1475 14.3294 13.1077 14.292L11.9327 13.1087C11.7962 12.9492 11.7248 12.7442 11.7329 12.5344C11.741 12.3247 11.828 12.1258 11.9764 11.9774C12.1248 11.8289 12.3237 11.742 12.5335 11.7339C12.7432 11.7258 12.9483 11.7971 13.1077 11.9337L14.291 13.1087C14.3284 13.1485 14.3736 13.1803 14.4238 13.202C14.4739 13.2237 14.528 13.2349 14.5827 13.2349C14.6373 13.2349 14.6914 13.2237 14.7416 13.202C14.7918 13.1803 14.8369 13.1485 14.8744 13.1087L16.0577 11.9337C16.1324 11.8464 16.2243 11.7756 16.3277 11.7256C16.4311 11.6755 16.5437 11.6474 16.6585 11.643C16.7732 11.6386 16.8877 11.6579 16.9946 11.6998C17.1016 11.7417 17.1987 11.8052 17.2799 11.8865C17.3611 11.9677 17.4247 12.0648 17.4665 12.1717C17.5084 12.2787 17.5278 12.3931 17.5234 12.5079C17.5189 12.6226 17.4908 12.7352 17.4408 12.8386C17.3908 12.942 17.3199 13.034 17.2327 13.1087L16.0577 14.292C16.0178 14.3294 15.9861 14.3746 15.9643 14.4247C15.9426 14.4749 15.9314 14.529 15.9314 14.5837C15.9314 14.6383 15.9426 14.6924 15.9643 14.7426C15.9861 14.7927 16.0178 14.8379 16.0577 14.8753L17.2327 16.0587Z"
            fill="white"
          />
        </g>
        <defs>
          <clipPath id="clip0_5370_2840">
            <rect width="20" height="20" fill="white" />
          </clipPath>
        </defs>
      </svg>
    </SvgIcon>
  );
  const SertaIcon = () => (
    <SvgIcon>
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_5370_2798)">
          <path
            d="M14.5834 7.91703C14.6414 7.91756 14.6981 7.89913 14.7447 7.86454C14.7913 7.82994 14.8254 7.78108 14.8417 7.72537C14.8576 7.66948 14.8548 7.60993 14.8337 7.55578C14.8127 7.50163 14.7745 7.45583 14.725 7.42537C13.9313 6.92679 13.0124 6.66381 12.075 6.66703C11.3597 6.66414 10.6522 6.81475 10 7.1087C9.94311 7.13644 9.89281 7.17609 9.85255 7.22498C9.81229 7.27387 9.78302 7.33084 9.76671 7.39203C9.75225 7.45416 9.75224 7.51877 9.76669 7.5809C9.78114 7.64302 9.80966 7.701 9.85005 7.75037C10.0989 8.06045 10.3192 8.39235 10.5084 8.74203C10.5356 8.79203 10.5725 8.83616 10.6168 8.87191C10.6611 8.90766 10.7121 8.93432 10.7667 8.95037C10.8775 8.97529 10.9936 8.95743 11.0917 8.90037C12.1409 8.2529 13.3505 7.91225 14.5834 7.91703Z"
            fill="currentColor"
          />
          <path
            d="M8.75 2.91667C8.75 3.69021 9.05817 4.43208 9.60671 4.97906C10.1553 5.52604 10.8992 5.83333 11.675 5.83333C12.4508 5.83333 13.1947 5.52604 13.7433 4.97906C14.2918 4.43208 14.6 3.69021 14.6 2.91667C14.6 2.14312 14.2918 1.40125 13.7433 0.854272C13.1947 0.307291 12.4508 0 11.675 0C10.8992 0 10.1553 0.307291 9.60671 0.854272C9.05817 1.40125 8.75 2.14312 8.75 2.91667Z"
            fill="currentColor"
          />
          <path
            d="M5.00064 6.66709C3.76586 6.65937 2.57189 7.10884 1.64872 7.92891C0.725559 8.74899 0.138505 9.88167 0.000641561 11.1088C-0.00643687 11.1791 0.001005 11.2502 0.0225063 11.3176C0.0440077 11.3849 0.0791107 11.4472 0.125642 11.5004C0.173132 11.5527 0.231008 11.5946 0.295576 11.6233C0.360143 11.652 0.429984 11.6669 0.500642 11.6671H8.33397C8.40911 11.6682 8.4831 11.6486 8.54779 11.6103C8.61249 11.5721 8.66538 11.5168 8.70064 11.4504C8.9294 11.0057 9.20905 10.589 9.53397 10.2088C9.58342 10.1494 9.61575 10.0777 9.6275 10.0013C9.63925 9.925 9.62996 9.84689 9.60064 9.77542C9.22683 8.86077 8.59039 8.07738 7.77169 7.52416C6.95298 6.97094 5.98872 6.6727 5.00064 6.66709Z"
            fill="currentColor"
          />
          <path
            d="M1.66797 2.91667C1.66797 3.69021 1.97614 4.43208 2.52468 4.97906C3.07323 5.52604 3.81721 5.83333 4.59297 5.83333C5.36873 5.83333 6.11271 5.52604 6.66126 4.97906C7.2098 4.43208 7.51797 3.69021 7.51797 2.91667C7.51797 2.14312 7.2098 1.40125 6.66126 0.854272C6.11271 0.307291 5.36873 0 4.59297 0C3.81721 0 3.07323 0.307291 2.52468 0.854272C1.97614 1.40125 1.66797 2.14312 1.66797 2.91667Z"
            fill="currentColor"
          />
          <path
            d="M14.5846 9.16699C13.5133 9.16699 12.4661 9.48467 11.5753 10.0799C10.6845 10.6751 9.99027 11.521 9.58029 12.5108C9.17032 13.5006 9.06305 14.5897 9.27205 15.6404C9.48105 16.6911 9.99694 17.6563 10.7545 18.4138C11.512 19.1714 12.4772 19.6872 13.5279 19.8962C14.5786 20.1053 15.6677 19.998 16.6575 19.588C17.6473 19.178 18.4932 18.4838 19.0884 17.593C19.6836 16.7022 20.0013 15.655 20.0013 14.5837C19.9991 13.1477 19.4277 11.7713 18.4124 10.7559C17.397 9.74059 16.0206 9.1692 14.5846 9.16699ZM17.5013 15.417H15.8346C15.7241 15.417 15.6181 15.4609 15.54 15.539C15.4619 15.6172 15.418 15.7232 15.418 15.8337V17.5003C15.418 17.7213 15.3302 17.9333 15.1739 18.0896C15.0176 18.2459 14.8057 18.3337 14.5846 18.3337C14.3636 18.3337 14.1517 18.2459 13.9954 18.0896C13.8391 17.9333 13.7513 17.7213 13.7513 17.5003V15.8337C13.7513 15.7232 13.7074 15.6172 13.6293 15.539C13.5511 15.4609 13.4451 15.417 13.3346 15.417H11.668C11.447 15.417 11.235 15.3292 11.0787 15.1729C10.9224 15.0166 10.8346 14.8047 10.8346 14.5837C10.8346 14.3626 10.9224 14.1507 11.0787 13.9944C11.235 13.8381 11.447 13.7503 11.668 13.7503H13.3346C13.4451 13.7503 13.5511 13.7064 13.6293 13.6283C13.7074 13.5501 13.7513 13.4442 13.7513 13.3337V11.667C13.7513 11.446 13.8391 11.234 13.9954 11.0777C14.1517 10.9215 14.3636 10.8337 14.5846 10.8337C14.8057 10.8337 15.0176 10.9215 15.1739 11.0777C15.3302 11.234 15.418 11.446 15.418 11.667V13.3337C15.418 13.4442 15.4619 13.5501 15.54 13.6283C15.6181 13.7064 15.7241 13.7503 15.8346 13.7503H17.5013C17.7223 13.7503 17.9343 13.8381 18.0906 13.9944C18.2468 14.1507 18.3346 14.3626 18.3346 14.5837C18.3346 14.8047 18.2468 15.0166 18.0906 15.1729C17.9343 15.3292 17.7223 15.417 17.5013 15.417Z"
            fill="currentColor"
          />
        </g>
        <defs>
          <clipPath id="clip0_5370_2798">
            <rect width="20" height="20" fill="white" />
          </clipPath>
        </defs>
      </svg>
    </SvgIcon>
  );

  return (
    <Box
      sx={{
        mt: 10,
        p: { xs: 2, sm: 3, md: 4 },
        px: { xs: 3, sm: 6, md: 10, lg: 15 },
      }}
    >
      <Box sx={{ display: "flex", justifyContent: "center", mb: 5, gap: 8 }}>
        <Box
          sx={{
            boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
            borderRadius: "8px",
            p: 2,
            display: "flex",
            backgroundColor: "#76BBBB",
            alignItems: "center",
            gap: 1,
          }}
        >
          <Box
            sx={{
              borderRadius: "50%",
              width: "40px",
              height: "40px",
              backgroundColor: "#CBE7E7",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <img
              src="/daftar-pertubuhan.png"
              alt="daftar-pertubuhan"
              width={30}
              height={30}
            />
          </Box>
          <Box>
            <Typography
              sx={{
                fontFamily:
                  "'Poppins', 'Roboto', 'Helvetica', 'Arial', sans-serif",
                fontWeight: "bold",
                color: "white",
                cursor: "pointer",
                display: HideOrDisplayInherit,
              }}
              //onClick={handleDaftarPertubuhan}
            >
              {t("registerOrg")}
            </Typography>
          </Box>
        </Box>

        <Box
          sx={{
            boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
            borderRadius: "8px",
            p: 2,
            display: "flex",
            backgroundColor: "#F3D997",
            alignItems: "center",
            gap: 1,
            cursor: "pointer",
          }}
          onClick={handleOpenSertaModal}
        >
          <Box
            sx={{
              borderRadius: "50%",
              width: "40px",
              height: "40px",
              backgroundColor: "#EAB632",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <img
              src="/serta-pertubuhan.png"
              alt="serta-pertubuhan"
              width={30}
              height={30}
            />
          </Box>
          <Box>
            <Typography
              sx={{
                fontFamily:
                  "'Poppins', 'Roboto', 'Helvetica', 'Arial', sans-serif",
                fontWeight: "bold",
                color: "#147C7C",
              }}
            >
              {t("sertaPertubuhan")}
            </Typography>
          </Box>
        </Box>

        <Box
          sx={{
            boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
            borderRadius: "8px",
            p: 2,
            display: "flex",
            backgroundColor: "#B3B7F1",
            alignItems: "center",
            gap: 1,
            cursor: "pointer",
          }}
          onClick={() => navigate("pembaharuan-setiausaha")}
        >
          <Box
            sx={{
              borderRadius: "50%",
              width: "40px",
              height: "40px",
              backgroundColor: "#E2E3FB",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <img
              src="/pembaharuan-setiausaha.png"
              alt="pembaharuan-setiausaha"
              width={30}
              height={30}
            />
          </Box>
          <Box>
            <Typography
              sx={{
                fontFamily:
                  "'Poppins', 'Roboto', 'Helvetica', 'Arial', sans-serif",
                fontWeight: "bold",
                color: "black",
              }}
            >
              {t("pembaharuanSetiausaha")}
            </Typography>
          </Box>
        </Box>
      </Box>

      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column", sm: "row" },
          justifyContent: "space-between",
          alignItems: { xs: "stretch", sm: "end" },
          mb: 5,
          gap: 2,
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", sm: "row" },
            alignItems: { xs: "stretch", sm: "center" },
            flex: 1,
            gap: 2,
            justifyContent: "center",
          }}
        >
          <Typography
            variant="h6"
            component="h1"
            sx={{
              fontWeight: "bold",
              whiteSpace: "nowrap",
              fontSize: "16px",
              fontFamily:
                "'Poppins', 'Roboto', 'Helvetica', 'Arial', sans-serif",
            }}
          >
            {t("searchInfo")}
          </Typography>
          <TextField
            placeholder={t("enterOrgName")}
            variant="outlined"
            size="small"
            fullWidth
            value={searchPertubuhan || ""}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={handleOpenSearchModal}>
                    <FilterList />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{
              backgroundColor: "white",
              "& .MuiOutlinedInput-root": {
                "& fieldset": {
                  borderColor: "#e0e0e0",
                },
                "&:hover fieldset": {
                  borderColor: "#CDE4E4",
                },
                "&.Mui-focused fieldset": {
                  borderColor: "#CDE4E4",
                  boxShadow: "0 0 0 4px rgba(205, 228, 228, 0.3)",
                },
              },
              maxWidth: { sm: "500px" },
              fontSize: "16px",
            }}
          />
        </Box>
      </Box>

      {showAlert && (
        <Alert
          severity="info"
          sx={{
            mb: 2,
            backgroundColor: "#D3E3FD",
            fontSize: "14px",
            fontWeight: "bold",
            borderRadius: "12px",
            fontFamily: "'Poppins', 'Roboto', 'Helvetica', 'Arial', sans-serif",
          }}
          icon={<CampaignIcon />}
          action={
            <IconButton
              aria-label="close"
              size="small"
              onClick={() => setShowAlert(false)}
            >
              <Close fontSize="inherit" />
            </IconButton>
          }
        >
          {t("paymentStatusAlert")}
        </Alert>
      )}

      {/* {isMobile ? (
        <Grid container spacing={2}>
          {filteredData.map((row: any) => (
            <Grid item xs={12} key={row.id}>
              <Card>
                <CardContent>
                  <Typography variant="subtitle1" fontWeight="bold">
                    {row.namaPertubuhan}
                  </Typography>
                  <Typography>
                    {t("applicationStatus")}: {row.statusPermohonan}
                  </Typography>
                  <Typography>
                    {t("organizationStatus")}: {row.statusPertubuhan}
                  </Typography>
                  <Typography>
                    {t("position")}: {row.jawatan}
                  </Typography>
                  <Typography>
                    {t("dateApplied")}: {row.tarikhMohon}
                  </Typography>
                  <Box
                    sx={{ display: "flex", justifyContent: "flex-end", mt: 1 }}
                  >
                    <IconButton
                      onClick={(event) => handleMenuOpen(event, row.id)}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      ) : (
        <DataGrid
          autoHeight
          apiRef={apiRef}
          rows={filteredData}
          columns={columns as any}
          initialState={{
            pagination: { paginationModel: { pageSize: 5 } },
          }}
          pageSizeOptions={[5, 10, 15]}
          sx={{
            backgroundColor: "white",
            borderRadius: "4px",
            fontFamily: "'Poppins', 'Roboto', 'Helvetica', 'Arial', sans-serif",
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: "#f5f5f5",
              fontWeight: "bold",
              fontSize: "16px",
            },
            "& .MuiDataGrid-cell": {
              fontSize: "16px",
            },
            minHeight: "100px",
          }}
          getRowId={(row) => row.id}
          loading={isLoading || isSearchLoading}
          slots={{
            noRowsOverlay: () => (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  aligntItems: "center",
                  pt: 3,
                }}
              >
                <Typography>{t("noOrganizationFound")}</Typography>
              </Box>
            ),
            noResultsOverlay: () => (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  aligntItems: "center",
                  pt: 3,
                }}
              >
                <Typography>{t("noOrganizationFound")}</Typography>
              </Box>
            ),
          }}
        />
      )} */}

      <CustomDataGrid
        url={`${API_URL}/society/getAll`}
        searchUrl={`${API_URL}/society/search`}
        columns={columns}
        searchField="societyName"
        searchTerm={searchPertubuhan}
        filters={[{ field: "pageNo", operator: "eq", value: "1" }]}
        enableSearch={true}
        noResultMessage={t("noOrganizationFound")}
      />

      <Menu
        anchorEl={anchorEl}
        open={Boolean(openMenuId)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          {t("updateOrgInfo")}
        </MenuItem>
        <MenuItem
          onClick={() => {
            handleMenuClose();
            navigate("/pertubuhan/dashboard");
          }}
        >
          <ListItemIcon>
            <VisibilityIcon fontSize="small" />
          </ListItemIcon>
          {t("viewOrg")}
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <ListAltIcon fontSize="small" />
          </ListItemIcon>
          {t("orgDocList")}
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <PaymentIcon fontSize="small" />
          </ListItemIcon>
          {t("onlinePayment")}
        </MenuItem>
        <MenuItem
          onClick={handleMenuClose}
          sx={{ display: HideOrDisplayInherit }}
        >
          <ListItemIcon>
            <DeleteIcon />
          </ListItemIcon>
          {t("deleteOrgInfo")}
        </MenuItem>
      </Menu>

      <Dialog
        open={openSertaModal}
        onClose={handleCloseSertaModal}
        PaperProps={{
          style: {
            borderRadius: "8px",
            backgroundColor: "#fff",
            color: "#000",
            width: "80%",
            maxWidth: "1000px",
            height: "auto",
            maxHeight: "80vh",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogTitle sx={{ pb: 2.5 }}>
          <Box
            sx={{
              backgroundColor: "#e0f2f1",
              px: 2.5,
              py: 0.5,
              borderRadius: 2.5,
            }}
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontWeight: "bold", fontSize: 14 }}
            >
              {t("sertaPertubuhan")}
            </Typography>
            <IconButton onClick={handleCloseSertaModal} size="small">
              <CloseIcon sx={{ color: "black" }} />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ py: 2, px: 5, overflowY: "auto" }}>
          <Box
            sx={{ display: "flex", alignItems: "center", mb: 2, pl: 5, pr: 5 }}
          >
            <Tooltip title={t("enterOrgNameOrNumber")} arrow>
              <InfoIcon
                sx={{
                  fontSize: 18,
                  color: "#7FBFBF",
                  cursor: "pointer",
                  "&:hover": {
                    color: "#6EAEAE",
                  },
                }}
              />
            </Tooltip>
            <TextField
              fullWidth
              variant="outlined"
              placeholder={t("searchOrganization")}
              value={searchPertubuhan}
              onChange={handleSearchChange}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton>
                      <Search />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{ ml: 1 }}
            />
          </Box>

          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t("organizationName")}</TableCell>
                <TableCell>{t("organizationNumber")}</TableCell>
                <TableCell align="right"></TableCell>
              </TableRow>
            </TableHead>
            <TableBody sx={{ marginBottom: 5 }}>
              {showNoResults ? (
                <TableRow>
                  <TableCell colSpan={3} align="center">
                    {t("noOrganizationFound")}
                  </TableCell>
                </TableRow>
              ) : (
                <>
                  <TableRow>
                    <TableCell>Pertubuhan Silat Cekak Malaysia</TableCell>
                    <TableCell>PPM-001-10102010</TableCell>
                    <TableCell align="right">
                      <Button
                        variant="contained"
                        startIcon={isSerta ? <SertaIcon /> : <BatalIcon />}
                        onClick={handleSertaClick}
                        sx={{
                          bgcolor: isSerta ? "#7FBFBF" : "#FF6B6B",
                          color: "white",
                          "&:hover": {
                            bgcolor: isSerta ? "#6EAEAE" : "#FF5252",
                          },
                        }}
                      >
                        {isSerta ? t("join") : t("cancel")}
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Persatuan Penduduk Taman Melati</TableCell>
                    <TableCell>PPM-002-20202020</TableCell>
                    <TableCell align="right">
                      <Button
                        variant="contained"
                        startIcon={<CheckIcon />}
                        disabled
                        sx={{
                          bgcolor: "#E0E0E0",
                          color: "#757575",
                          "&:hover": { bgcolor: "#E0E0E0" },
                        }}
                      >
                        {t("alreadyMember")}
                      </Button>
                    </TableCell>
                  </TableRow>
                </>
              )}
            </TableBody>
          </Table>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default ListSenaraiPertubuhan;
