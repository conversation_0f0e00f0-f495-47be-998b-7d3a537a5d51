import {
  Box,
  Typography,
  TextField,
  Grid,
  FormControl,
  Select,
  MenuItem,
  Checkbox,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { MALAYSIA } from "../../../../helpers/enums";
import { useState, FormEvent } from "react";
import { API_URL } from "../../../../api";
import { useCustom } from "@refinedev/core";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";

export const CreatePegawaiAwam: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const renderFormField = (
    label: string,
    component: React.ReactNode,
    required = false
  ) => (
    <Grid container spacing={2} alignItems="center" sx={{ mb: 1 }}>
      <Grid item xs={12} sm={3}>
        <Typography
          variant="body1"
          sx={{ color: "#666666", fontWeight: "400 !important", fontSize: 14 }}
        >
          {label}
          {required && <span style={{ color: "red" }}>*</span>}
        </Typography>
      </Grid>
      <Grid item xs={12} sm={9}>
        {component}
      </Grid>
    </Grid>
  );

  const [businessState, setBusinessState] = useState("");
  const [businessDistrict, setBusinessDistrict] = useState("");
  const [isChecked, setIsChecked] = useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);

  const handleBack = () => {
    navigate(-1);
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    setFormSubmitted(true);

    if (isChecked) {
      // Proceed with form submission
      console.log("Form submitted successfully");
      // Add your form submission logic here
    }
  };

  const { data, isLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData = data?.data?.data || [];

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
        mb: 2,
      }}
    >
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("pilihanPegawaiAwam")}
        </Typography>

        {renderFormField(
          t("jenisPegawai"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            type="text"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("namaAhliPertubuhan"),
          <FormControl fullWidth required>
            <Select
              size="small"
              displayEmpty
              required
              renderValue={(selected) => {
                return <>{t("selectPlaceholder")}</>;
              }}
            >
              {/* <MenuItem value="" disabled>
                {t("pleaseSelect")}
              </MenuItem> */}
            </Select>
          </FormControl>,
          true
        )}
      </Box>

      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("publicOfficialsInformation")}
        </Typography>

        {renderFormField(
          t("title"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("fullName"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("gender"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("citizenship"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("idType"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("idNumberPlaceholder"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("dateOfBirth"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            type="date"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("placeOfBirth"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("occupation"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("residentialAddress"),
          <TextField
            size="small"
            fullWidth
            multiline
            rows={2}
            variant="outlined"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("state"),
          <FormControl fullWidth required>
            <Select
              size="small"
              value={businessState}
              displayEmpty
              required
              disabled={isLoading}
              onChange={(e) => {
                setBusinessState(e.target.value);
              }}
            >
              <MenuItem value="" disabled>
                {isLoading ? "Loading..." : t("pleaseSelect")}
              </MenuItem>
              {!isLoading &&
                addressData
                  .filter((item: any) => item.pid === MALAYSIA)
                  .map((item: any) => (
                    <MenuItem key={item.id} value={item.id}>
                      {item.name}
                    </MenuItem>
                  ))}
            </Select>
          </FormControl>,
          true
        )}
        {renderFormField(
          t("district"),
          <FormControl fullWidth required>
            <Select
              size="small"
              value={businessDistrict}
              displayEmpty
              required
              onChange={(e) => {
                setBusinessDistrict(e.target.value);
              }}
              disabled={isLoading || !businessState}
            >
              <MenuItem value="" disabled>
                {isLoading ? "Loading..." : t("selectPlaceholder")}
              </MenuItem>
              {!isLoading &&
                addressData
                  .filter((item: any) => item.pid == businessState)
                  .map((item: any) => (
                    <MenuItem key={item.id} value={item.id}>
                      {item.name}
                    </MenuItem>
                  ))}
            </Select>
          </FormControl>,
          true
        )}
        {renderFormField(
          t("city"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("poskod"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("email"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("phoneNumber"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("homeNumber"),
          <TextField
            size="small"
            fullWidth
            variant="outlined"
            sx={{
              "& .MuiInputBase-input": { color: "#666666" },
            }}
          />,
          true
        )}
      </Box>

      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 4,
          }}
        >
          <Checkbox
            checked={isChecked}
            onChange={(e) => setIsChecked(e.target.checked)}
            sx={{
              color: "#00A7A7",
              "&.Mui-checked": {
                color: "#00A7A7",
              },
              padding: "0",
            }}
          />
          <Typography
            sx={{
              fontWeight: "400 !important",
              color: "#666666",
              fontSize: 14,
            }}
          >
            {t("pegawaiAwamDeclaration")}
            <span style={{ color: "red" }}>*</span>
          </Typography>
        </Box>
        {formSubmitted && !isChecked && (
          <Typography color="error" sx={{ mt: 1, fontSize: 12 }}>
            {t("pleaseSelect")}
          </Typography>
        )}
      </Box>

      <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 2 }}>
        <ButtonOutline onClick={handleBack}>{t("semula")}</ButtonOutline>
        <ButtonPrimary type="submit" disabled={formSubmitted && !isChecked}>
          {t("update")}
        </ButtonPrimary>
      </Box>
    </Box>
  );
};

export default CreatePegawaiAwam;
