import { useState } from "react";
import { ButtonPrimary } from "../../../../components/button";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Stack } from "@mui/material";
import TableSumbangan from "./TableSumbangan";
import { useNavigate } from "react-router-dom";
import { Sumbangan } from "../interface";
import useQuery from "../../../../helpers/hooks/useQuery";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import ConfirmationDialog from "../../../../components/dialog/confirm";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { useGetStatementContributionList } from "@/helpers/hooks/useGetStatementList";
import { ApplicationStatus, getLocalStorage, toDollarFormat } from "@/helpers";
import { DataTable, IColumn } from "@/components";
import { FieldValues, useForm } from "react-hook-form";
import { EditIcon, TrashIcon } from "@/components/icons";

export const ListSumbanganKeLuarNegara = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  // Mock data for dokumenList

  //@ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);
  //@ts-ignore
  const isviewStatement = useSelector(
    //@ts-ignore
    (state) => state?.statementData?.isViewStatement
  );
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);

  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 5,
      searchQuery: undefined,
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");

  const [sumbanganList, setSumbanganList] = useState<Sumbangan[]>([]);

  const [total, totalList] = useState(0);

  const addressList = getLocalStorage("address_list", null);

  function getCountryLabelById(id: string): string | undefined {
    const country = addressList
      .filter((item: any) => item.pid === 0)
      .map((item: any) => ({
        label: item.name,
        value: item.id.toString(),
      }))
      .find((item: any) => item.value === id);
    return country?.label; // Returns undefined if not found
  }

  const {
    data: listData,
    refetch: fetchList,
    isLoading: isStatementContributionListIsLoading,
  } = useGetStatementContributionList({
    id: societyId,
    statementId: statementId,
    contributionCode: 2,
    enabled: true,
    onSuccess: (data) => {
      const list = data?.data?.data?.data || [];
      totalList(list.length || 0);
      setSumbanganList(list);
    },
  });

  const add = () => {
    navigate("createSumbangan", {
      state: {
        societyId: societyId,
        statementId: statementId,
        year: year,
        contributionCode: "2",
      },
    });
  };
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const handleEdit = (id: string | number, contributionCode: string) => {
    navigate("createSumbangan", {
      state: {
        societyId: societyId,
        statementId: statementId,
        year: year,
        contributionId: id,
        contributionCode: contributionCode,
      },
    });
  };

  const [statementComplete, setStatementComplete] = useState(false);
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const [contributionId, setContributionId] = useState<
    string | number | undefined
  >();
  const handleConfirmDelete = (id: string | number) => {
    setContributionId(id);
    setOpenConfirm(true);
  };

  const columns: IColumn[] = [
    {
      field: "penerimaSumbangan",
      headerName: t("penerimaSumbangan"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.contribution}
          </Box>
        );
      },
    },
    {
      field: "countryOfOriginRecipient2",
      headerName: t("countryOfOriginRecipient2"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.societyNo ? getCountryLabelById(row.countryOrigin) : "-"}
          </Box>
        );
      },
    },
    {
      field: "value",
      headerName: t("value"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.value ? `RM ${toDollarFormat(row.value)}` : "-"}
          </Box>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      renderCell: (params: any) => {
        const row = params.row;
        return (
          <Box sx={{ textAlign: "end" }}>
            {isDisabled || statementComplete ? null : (
              <>
                <IconButton
                  onClick={() => handleEdit(row.id, row.contributionCode)}
                >
                  <EditIcon sx={{ color: "var(--primary-color)" }} />
                </IconButton>
                <IconButton onClick={() => handleConfirmDelete(row.id)}>
                  <TrashIcon sx={{ color: "red" }} />
                </IconButton>
              </>
            )}
          </Box>
        );
      },
    },
  ];

  const { mutate: deleteContribution } = useCustomMutation();

  const handleDeleteContribution = () => {
    deleteContribution(
      {
        url: `${API_URL}/society/statement/statement-contribution/${contributionId}/delete`,
        method: "put",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          fetchList();
          setOpenConfirm(false);
        },
      }
    );
  };

  const isDisabled = (!isManager && !isAliranTugasAccess) || isviewStatement;

  useQuery({
    url: `society/statement/societyInfo/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "year", operator: "eq", value: year },
    ],
    onSuccess: (data) => {
      if (
        Number(data?.data?.data?.applicationStatusCode) ===
        ApplicationStatus.SELESAI
      ) {
        setStatementComplete(true);
      }
    },
  });

  return (
    <Stack spacing={2}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            py: 2,
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              borderRadius: "16px",
              fontSize: "14px",
              fontWeight: "500 !important",
            }}
          >
            {t("contributionToAbroad")}
          </Typography>
        </Box>
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            py: 2,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("listOfContributionsFromInbroad")}
          </Typography>
          <Stack
            direction="row"
            spacing={2}
            mt={2}
            sx={{ pl: 1, width: "100%" }}
            justifyContent="flex-end"
          >
            {isDisabled || statementComplete ? null : (
              <ButtonPrimary
                sx={{
                  bgcolor: "transparent",
                  color: "#666666",
                  boxShadow: "none",
                  border: "1px solid #67D1D1",
                  fontSize: "12px",
                  fontWeight: "500 !important",
                }}
                onClick={add}
              >
                {t("daftarSumbanganKeLuarNegara")}
              </ButtonPrimary>
            )}
          </Stack>
          <ConfirmationDialog
            status={1}
            open={openConfirm}
            onClose={() => setOpenConfirm(false)}
            title={t("confirmDeleteContribution")}
            message={`${t("confirmDeleteContribution")}?`}
            onConfirm={handleDeleteContribution}
            onCancel={() => setOpenConfirm(false)}
          />
          <DataTable
            columns={columns}
            rows={sumbanganList}
            page={page}
            rowsPerPage={pageSize}
            totalCount={total}
            onPageChange={(newPage) => setValue("page", newPage)}
            onPageSizeChange={(newPageSize) => {
              setValue("page", 1);
              setValue("pageSize", newPageSize);
            }}
            isLoading={isStatementContributionListIsLoading}
          />

          {/* <TableSumbangan
            items={sumbanganList}
            isDisable={isDisabled || statementComplete}
            headerCountry={t("countryOfOriginRecipient")}
            handleEdit={handleEdit}
            handleDelete={handleConfirmDelete}
          /> */}
        </Box>
      </Box>
    </Stack>
  );
};

export default ListSumbanganKeLuarNegara;
