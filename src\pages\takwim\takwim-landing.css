/* Takwim Landing Page Styles */

.takwim-hero {
  position: relative;
  height: 600px;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
}

.takwim-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.takwim-hero-content {
  position: relative;
  z-index: 2;
}

.takwim-hero-title {
  color: white;
  font-weight: 600;
  margin-bottom: 8px;
  width: 60%;
}

.takwim-calendar-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.takwim-month-calendar {
  width: calc(33.333% - 11px);
  border: 1px solid #dadada99;
  border-radius: 0.5rem;
}

@media (min-width: 1300px) {
  .takwim-hero-content {
    left: -80px;
  }
}
@media (max-width: 960px) {
  .takwim-month-calendar {
    width: calc(50% - 8px);
  }
}

@media (max-width: 600px) {
  .takwim-month-calendar {
    width: 100%;
  }
}

.takwim-sidebar {
  position: sticky;
  top: 6rem;
  box-shadow: 0 0.75rem 0.75rem 0 #eae8e866;
  border-radius: 1.25rem;
  padding: 1.5rem;
}

.takwim-event-item {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #dadada99;
  border-radius: 0.5rem;
}

.takwim-event-time {
  font-weight: bold;
  font-size: 1.1rem;
}

.takwim-event-period {
  color: #666;
  font-size: 0.8rem;
}

.takwim-event-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.takwim-event-location {
  color: #666;
  font-size: 0.8rem;
}

/* Calendar styles */
.calendar-activities-this-month-date {
  min-height: 40px;
  min-width: 40px;
  font-size: 0.9rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.takwim-calendar-custom .MuiBox-root,
.takwim-calendar-custom [class*="MuiBox-root"] {
  box-sizing: border-box !important;
}

/* detail page */
.calendar-add-link {
  color: #144eb6 !important;
  text-decoration: none !important;
  font-size: 10px !important;
  font-weight: 400 !important;
  cursor: pointer;
  transition: color 0.2s ease;
}


.link{
  color: #666666 !important;
  text-decoration: none !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  cursor: pointer;
  transition: color 0.2s ease;
}
.calendar-add-link:hover {
  color: #0d3580;
  text-decoration: underline;
}

.detail-sub-title {
  font-size: 12px !important;
  font-weight: 400 !important;
}
