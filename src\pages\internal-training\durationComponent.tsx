import React, { useState } from "react";
import { Grid, SxProps, TextField, Theme, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";


interface DurationComponentProps {
  setHour: (e: number) => void,
  setMinute: (e: number) => void,
  labelStyle: SxProps<Theme>,
  hour: number,
  minute: number,
  minuteError: String
}


const DurationComponent: React.FC<DurationComponentProps> =
  ({
    setHour,
    setMinute,
    labelStyle,
    hour,
    minute,
    minuteError
  }) => {

    const { t, i18n } = useTranslation();

    const onHourChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (/[0-9]/.test(e.target.value)) {
        const temp = parseInt(e.target.value);
        setHour(temp);
      }

    }

    const onMinuteChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (/[0-9]/.test(e.target.value)) {
        const temp = parseInt(e.target.value);
        if (temp > 59) {
          const tempH = Math.floor(temp / 60);
          setHour(tempH)
        }
        setMinute(temp % 60);
      }
    }

    return (<>
      <Grid item xs={12} sm={4}>
        <Typography sx={labelStyle}>
          {t("trainingDuration")} <span style={{ color: "red" }}>*</span>
        </Typography>
      </Grid>
      <Grid item xs={6} sm={1}>
        <TextField
          size={"small"}
          required
          type={"number"}
          InputProps={{ inputProps: { min: 0, } }}
          name="hour"
          value={hour}
          onChange={onHourChange}
        />
      </Grid>
      <Grid item xs={6} sm={1}>
        <Typography sx={labelStyle}>
          {t("hour")}
        </Typography>
      </Grid>
      <Grid item xs={6} sm={1}>
        <TextField
          size={"small"}
          required
          type={"number"}
          InputProps={{ inputProps: { min: 0, } }}
          name="minute"
          value={minute}
          onChange={onMinuteChange}
          error={!!minuteError}
          helperText={minuteError}
        />
      </Grid>
      <Grid item xs={6} sm={1}>
        <Typography sx={labelStyle}>
          {t("minute")}
        </Typography>
      </Grid>
      <Grid item xs={12} sm={4}></Grid>
    </>);
  }
// @ts-ignore
export default DurationComponent
