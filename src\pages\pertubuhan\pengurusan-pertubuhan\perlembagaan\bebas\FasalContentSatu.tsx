import {
  Box,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { handleSaveContent } from "../helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "@/helpers/utils";
import { FasalBebasProps } from "../Fasal";
import EditableFasalTextArea from "@/components/FasalBebasComponent/EditableFasalTextArea";
import FasalNameCom from "@/components/FasalBebasComponent/FasalNameCom";
import ReminderEditable from "@/components/FasalBebasComponent/ReminderEditable";
import ContentBox from "@/components/FasalBebasComponent/ContentBox";
import CheckContent from "@/components/FasalBebasComponent/CheckContent";
import { ConstitutionType, OrganizationLevelOption, useQuery } from "@/helpers";
import { API_URL } from "@/api";
import { useDispatch } from "react-redux";
import { setSocietyDataRedux } from "@/redux/societyDataReducer";

export const FasalContentSatuBebas: React.FC<FasalBebasProps> = ({
  activeStep,
  setActiveStep,
  clause,
  asalData,
  name,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const requiredText = [
    "<<NAMA PERTUBUHAN>>",
    "<<SINGKATAN NAMA>>",
    "<<TAKRIF NAMA>>",
    "<<TARAF PERTUBUHAN>>",
  ];

  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [singkatanNama, setSingkatanNama] = useState("");
  const [takrifNama, setTakrifNama] = useState("");
  const [tarafPertubuhan, setTarafPertubuhan] = useState("");
  const [clauseContentId, setClauseContentId] = useState("");
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [cursorCoords, setCursorCoords] = useState({ top: 0, left: 0 });
  const [clauseContentEditable, setClauseContentEditable] = useState(
    clause.clauseContent
  );
  const [currentFieldTextIndex, setCurrentFieldTextIndex] = useState(-1);
  const [checked, setChecked] = useState(false);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [currentSuggestionIndex, setCurrentSuggestionIndex] = useState(-1);
  const textareaRef = useRef<any>(null);

  const [applicationStatusCode, setApplicationStatusCode] = useState(0);
  const [filteredSuggestions, setFilteredSuggestions] = useState<any>([]);
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  const [isNameExist, setIsNameExist] = useState("");
  const [oldName, setOldName] = useState("");
  
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.selectionStart = cursorPosition;
      textareaRef.current.selectionEnd = cursorPosition;
    }
  }, [cursorPosition]);

  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { data: amendmentData } = useCustom({
    url: `${API_URL}/society/amendment/getAmendmentByParam`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        id: amendmentId,
      },
    },
    queryOptions: {
      enabled: !!amendmentId,
    },
  });

  const { id, clauseId } = useParams(); 

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data); 

  useEffect(() => {
    if (societyDataRedux) {
      setTarafPertubuhan(societyDataRedux.societyLevel);
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
      setOldName(societyDataRedux.societyName); 
      setApplicationStatusCode(societyDataRedux.applicationStatusCode);
      console.log(
        "Fasal 1 perlambangan Bebas",
        societyDataRedux?.constitutionType
      );
    }
  }, [societyDataRedux]);

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (namaPertubuhan?.length === 0) {
      errors.namaPertubuhan = t("fieldRequired");
    } else if (namaPertubuhan.length < 7) {
      errors.namaPertubuhan = "Sila masukkan sekurang-kurangnya 5 aksara";
    }

    return errors;
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  const handleChange = (e: any) => {
    setClauseContentEditable(e.target.value);

    setCursorPosition(e.target.selectionStart);

    const lastTwoChars = e.target.value.slice(
      e.target.selectionStart - 2,
      e.target.selectionStart
    );
    if (lastTwoChars === "<<") {
      setFilteredSuggestions(requiredText);
      // updateCursorPosition();
    } else {
      setFilteredSuggestions([]);
    }
  };

  const replaceCurrentWord = (newWord: any) => {
    const cursorPos = textareaRef.current.selectionStart;
    const startIndex = clauseContentEditable.lastIndexOf("<<", cursorPos);
    if (startIndex !== -1) {
      const newValue =
        clauseContentEditable.substring(0, startIndex + 2) +
        newWord +
        ">>" +
        clauseContentEditable.substring(cursorPos);
      setClauseContentEditable(newValue);
      setFilteredSuggestions([]);
    }
  };

  const handleKeyDown = (e: any) => {
    if (!["ArrowDown", "ArrowUp", "Enter", "Escape"].includes(e.key)) return;
    if (filteredSuggestions.length === 0) return;

    e.preventDefault();
    switch (e.key) {
      case "ArrowDown":
        setCurrentSuggestionIndex((prev) =>
          Math.min(prev + 1, filteredSuggestions.length - 1)
        );
        break;
      case "ArrowUp":
        setCurrentSuggestionIndex((prev) => Math.max(prev - 1, 0));
        break;
      case "Enter":
        if (currentSuggestionIndex >= 0) {
          replaceCurrentWord(filteredSuggestions[currentSuggestionIndex]);
          setCurrentSuggestionIndex(-1);
        }
        break;
      case "Escape":
        setFilteredSuggestions([]);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (clause) {  
      if(clause.id){
        setDataId(clause.id)
      }
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      if(clause.constitutionValues.length > 0){
        const fieldMappings: Record<string, (value: string) => void> = {
          // "Nama Pertubuhan": setNamaPertubuhan,
          "Singkatan Nama": setSingkatanNama,
          "Takrif Nama": setTakrifNama,
          "Taraf Pertubuhan": setTarafPertubuhan, 
        };
  
        Object.values(fieldMappings).forEach(setter => setter(''));
        
        if(clause.constitutionValues){
          clause.constitutionValues.forEach((item:any) => {
            const setter = fieldMappings[item.titleName];
            if (setter && item.definitionName) {
              setter(item.definitionName);
            }
          });
        }  
      }
      if (clause.clauseContentId) {
        setClauseContentId(clause.clauseContentId);
      }
      setClauseContentEditable(
        clause.clauseModifyContent
          ? clause.clauseModifyContent
          : clause.clauseContent
      );
      setIsEdit(clause.edit);
    }
  }, [clause]);

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  let clauseContent = clauseContentEditable;
  clauseContent = clauseContent.replaceAll(
    /<<NAMA PERTUBUHAN>>/gi,
    `<b>${namaPertubuhan || "<<NAMA PERTUBUHAN>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<SINGKATAN NAMA>>/gi,
    `<b>${singkatanNama || "<<SINGKATAN NAMA>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<TAKRIF NAMA>>/gi,
    `<b>${takrifNama || "<<TAKRIF NAMA>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<TARAF PERTUBUHAN>>/gi,
    `<b>${tarafPertubuhan || "<<TARAF PERTUBUHAN>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  const handlerChangeClauseContentEditable = (val: string) => {
    setClauseContentEditable(val);
  };

  const [debouncedSocietyName, setDebouncedSocietyName] = useState("");
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSocietyName(namaPertubuhan);
    }, 500); // Adjust 500ms delay as needed

    return () => clearTimeout(timer);
  }, [namaPertubuhan]);

  useEffect(() => {
    if (debouncedSocietyName !== oldName && debouncedSocietyName?.length > 5) {
      refetch();
    }
  }, [debouncedSocietyName]);

  const {
    data: societyNameExist,
    isLoading: isLoadingSocietyNameExist,
    refetch,
  } = useQuery({
    autoFetch: false,
    url: `society/checkSocietyNameExists`,
    filters: [
      {
        field: "societyName",
        operator: "eq",
        value: debouncedSocietyName,
      },
    ],
    onSuccess: (data) => {
      const response = data?.data;

      const errors: { [key: string]: string } = {};

      if (response?.data === true) {
        errors.societyName = t(response?.msg);
        setIsNameExist(t(response?.msg));
      } else {
        setIsNameExist("");
      }

      if (!namaPertubuhan) {
        // Organization name validation
      } else {
        const namePattern = /^(pertubuhan|persatuan|kelab)\s/i;
        if (!namePattern.test(namaPertubuhan)) {
          errors.societyName =
            "Nama pertubuhan hendaklah bermula dengan Pertubuhan/ persatuan/ kelab";
        }
      }
      setFormErrors(errors);
    },
  });

  const handleUpdateSociety = () => {
    Edit();
  };
  const dispatch = useDispatch();

  const { mutate: edit, isLoading: isLoadingEdit } = useCustomMutation();
  const Edit = (): void => {
    edit(
      {
        url: `society/${societyId}/edit`,
        method: "put",
        values: {
          societyName: namaPertubuhan,
          societyLevel: tarafPertubuhan,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status === "SUCCESS") {
            const temp = {
              ...societyDataRedux,
              societyName: namaPertubuhan,
              societyLevel: tarafPertubuhan,
            };
            dispatch(setSocietyDataRedux(temp));
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  return (
    <>
      <Grid container>
        <FasalNameCom clauseId={id} name={name} />
        {/* name section */}
        <Grid item xs={12}>
          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <Typography sx={{ mb: 1, ...sectionStyle }}>{name}</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Typography sx={labelStyle}>{t("namaPertubuhan")}</Typography>
                </Grid>
                <Grid item xs={12} md={8}>
                  <TextField
                    name="namaPertubuhan"
                    size="small"
                    value={namaPertubuhan}
                    placeholder={`${t("namaPertubuhan")}`}
                    fullWidth
                    required
                    disabled
                    sx={applicationStatusCode !== 36 ? { background: "#E8E9E8" } : {}}
                    error={!!isNameExist || !!formErrors.namaPertubuhan}
                    helperText={isNameExist}
                    onChange={(e) => {
                      setNamaPertubuhan(e.target.value);

                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        namaPertubuhan: "",
                      }));
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <Typography sx={labelStyle}>
                    {t("taraf")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} md={8}>
                  <Select
                    size="small"
                    fullWidth
                    required
                    value={tarafPertubuhan}
                    displayEmpty
                    disabled
                    sx={{ background: "#E8E9E8" }}
                    onChange={(e) => {
                      setTarafPertubuhan(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        tarafPertubuhan: "",
                      }));
                    }}
                    renderValue={(selected) =>
                      selected
                        ? OrganizationLevelOption.find(
                            (item) => item.value === selected
                          )?.value
                        : t("organizationLevel")
                    }
                  >
                    {OrganizationLevelOption.map((item) => (
                      <MenuItem key={item.value} value={item.value}>
                        {item.value}
                      </MenuItem>
                    ))}
                  </Select>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Typography sx={labelStyle}>
                    {t("nameAbbreviation")}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={8}>
                  <TextField
                    size="small"
                    value={singkatanNama}
                    disabled={isViewMode}
                    sx={{ background: isViewMode && "#E8E9E8" }}
                    fullWidth
                    onChange={(e) => {
                      setSingkatanNama(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        singkatanNama: "",
                      }));
                    }}
                    error={!!formErrors.singkatanNama}
                    helperText={formErrors.singkatanNama}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <Typography sx={labelStyle}>{t("nameDefinition")}</Typography>
                </Grid>
                <Grid item xs={12} md={8}>
                  <TextField
                    size="small"
                    disabled={isViewMode}
                    sx={{ background: isViewMode && "#E8E9E8" }}
                    value={takrifNama}
                    placeholder={`${t("nameDefinitionPlaceholder")}`}
                    fullWidth
                    multiline
                    rows={5}
                    onChange={(e) => {
                      setTakrifNama(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        takrifNama: "",
                      }));
                    }}
                    error={!!formErrors.takrifNama}
                    helperText={formErrors.takrifNama}
                  />
                </Grid>
              </Grid>
            </Box>
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <ReminderEditable />
            <EditableFasalTextArea
              requiredFieldText={requiredText}
              clauseContentEditable={clauseContentEditable}
              setClauseContentEditable={handlerChangeClauseContentEditable}
            />
          </Box>

          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
            }}
          >
            <ContentBox clauseContent={clauseContent} />
            <CheckContent checked={checked} onChange={handleChangeCheckbox} />
            {/* submit */}
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                variant="contained"
                sx={{
                  width: isMobile ? "100%" : "auto",
                  display: isViewMode ? "none" : "block",
                }}
                onClick={() => {
                  const errors = validateForm();
                  if (Object.keys(errors).length > 0) {
                    setFormErrors(errors);
                    return;
                  }
            
                  // handleUpdateSociety();
                  handleSaveContent({
                    i18n,
                    societyId: societyDataRedux.id,
                    societyName: societyDataRedux.societyName,
                    clauseContentId,
                    dataId,
                    isEdit,
                    clauseNo: clauseNo,
                    clauseName: clauseName,
                    createClauseContent,
                    editClauseContent,
                    description: clauseContent,
                    modifiedTemplate: clauseContentEditable,
                    constitutionValues: [
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: namaPertubuhan,
                        titleName: "Nama Pertubuhan",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: singkatanNama,
                        titleName: "Singkatan Nama",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: takrifNama,
                        titleName: "Takrif Nama",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: tarafPertubuhan,
                        titleName: "Taraf Pertubuhan",
                      },
                    ],
                    clause: "clause1",
                    clauseCount: 1,
                  });
                }}
                disabled={
                  isCreatingContent ||
                  isEditingContent ||
                  !checked ||
                  isNameExist?.length > 0 ||
                  namaPertubuhan?.length < 7
                }
              >
                {isCreatingContent || isEditingContent
                  ? t("saving")
                  : t("save")}
              </ButtonPrimary>
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </>
  );
};

export default FasalContentSatuBebas;
