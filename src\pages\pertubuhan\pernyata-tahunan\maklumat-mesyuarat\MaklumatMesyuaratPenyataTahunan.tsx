import { Box, Grid, Typography } from "@mui/material";
import React, { useEffect } from "react";
import Input from "../../../../components/input/Input";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, useMap } from "react-leaflet";
import { Meeting } from "../interface";
import { MeetingMethods } from "../../../../helpers/enums";
import dayjs from "dayjs";
import {
  getLocalStorage,
  getMeetingType,
  useBackendLocalization,
} from "@/helpers";
import { FormMeetingDateTime } from "@/components/form/meeting";
import { useSelector } from "react-redux";

type props = {
  sectionStyle: any;
  meetings: Meeting[];
  meeting: Meeting | undefined | null;
  handleSetMeeting: (selectedMeeting: Meeting | null) => void;
  handleSetFilteredMeeting: (filteredMeeting: Meeting[]) => void;
  setSelectedDate: (date: string) => void;
  selectedDate: string;
  filteredMeetings: Meeting[];
  meetingId: string | number | null;
  isDisabled: boolean;
  availableDateList: string[];
  setSavedMeetingDate: any;
  savedMeetingDate: string;
  resetChanges: () => void;
  setMeetingIdLabelreplace: (data: any) => void;
  MeetingIdLabelreplace: string;
};

const MaklumatMesyuaratPenyataTahunan: React.FC<props> = ({
  meetingId,
  selectedDate,
  filteredMeetings,
  sectionStyle,
  meetings,
  meeting,
  setSelectedDate,
  handleSetMeeting,
  handleSetFilteredMeeting,
  isDisabled,
  availableDateList = [],
  setSavedMeetingDate,
  savedMeetingDate,
  resetChanges,
  setMeetingIdLabelreplace,
  MeetingIdLabelreplace,
}) => {
  const { t, i18n } = useTranslation();
  const isviewStatement = useSelector(
    //@ts-ignore
    (state) => state?.statementData?.isViewStatement
  );
  const meetingList = getLocalStorage("meeting_list", null);
  useEffect(() => {
    if (meetingId) {
      // const selectedMeeting = meetings.find(
      //   (meeting) => meeting.id === meetingId
      // );
      const selectedMeeting = meetings.find(
        (meeting) =>
          `${meeting?.meetingType + "@" + meeting?.meetingDate}` ===
          MeetingIdLabelreplace
      );
      if (selectedMeeting) {
        handleSetMeeting(selectedMeeting);
      }
    }
  }, [meetingId, meetings]);

  useEffect(() => {
    if (selectedDate && meetings) {
      const filteredMeeting = meetings.filter(
        (meeting) =>
          meeting?.meetingDate?.toString() ===
          dayjs(selectedDate).format("YYYY-MM-DD")
      );
      handleSetFilteredMeeting(filteredMeeting);
    }
  }, [selectedDate]);

  const { getTranslation } = useBackendLocalization();

  const getPlatformType = (type: number) => {
    const platform =
      meetingList.filter((item: any) => item.id === type)?.[0] || {};
    return platform ? getTranslation(platform) : "-";
  };

  const getMeetingMethod = (meetingId: string | number | undefined) => {
    const meetingMethod = meetingList.find(
      (m: any) => m.id === Number(meetingId)
    );
    return meetingMethod ? getTranslation(meetingMethod) : "-";
  };
  const clearMeeting = () => handleSetMeeting(null);

  return (
    <Box
      sx={{
        background: "white",
        border: "1px solid rgba(0, 0, 0, 0.12)",
        borderRadius: "14px",
        p: 3,
        mb: 2,
      }}
    >
      <Typography variant="subtitle1" sx={sectionStyle}>
        {t("maklumatMesyuaratPenyataTahunan")}
      </Typography>
      <Grid item xs={12}>
        <Input
          required
          disabled={isviewStatement}
          label={t("meetingDate")}
          type="date"
          availableDate={availableDateList}
          value={selectedDate ? selectedDate : savedMeetingDate}
          onChange={(e) => {
            clearMeeting();
            setSavedMeetingDate(e.target.value);
            setSelectedDate(e.target.value);
            resetChanges();
            const filteredMeeting = meetings.filter(
              (meeting) =>
                meeting?.meetingDate?.toString() ===
                dayjs(selectedDate ? selectedDate : e.target.value).format(
                  "YYYY-MM-DD"
                )
            );
            handleSetFilteredMeeting(filteredMeeting);
          }}
        />
        {meetingId || filteredMeetings.length > 0 ? (
          <>
            <Input
              label={t("meetingList")}
              type="select"
              required
              disabled={isDisabled}
              value={MeetingIdLabelreplace}
              onChange={(e) => {
                const selectedMeeting = meetings.find((meeting) => {
                  return (
                    `${meeting?.meetingType + "@" + meeting?.meetingDate}` ===
                    e.target.value
                  );
                });
                setMeetingIdLabelreplace(
                  `${
                    selectedMeeting?.meetingType +
                    "@" +
                    selectedMeeting?.meetingDate
                  }`
                );
                if (selectedMeeting) {
                  handleSetMeeting(selectedMeeting); // Pass selected meeting to parent
                }
              }}
              options={filteredMeetings.map((meeting) => ({
                value: `${meeting.meetingType}@${meeting.meetingDate}`,
                label: `${
                  getMeetingType(Number(meeting.meetingType))?.label
                } (${dayjs(meeting.meetingDate).format("DD-MM-YYYY")})`,
              }))}
            />
          </>
        ) : null}
        {meeting ? (
          <>
            <Input
              label={t("meetingMethod")}
              disabled
              value={
                meeting?.meetingMethod
                  ? getMeetingMethod(meeting?.meetingMethod)
                  : "-"
              }
              required
            />

            {meeting?.meetingMethod == MeetingMethods.BERSEMUKA ? null : (
              <Input
                label={t("platformType")}
                disabled
                value={
                  meeting?.platformType
                    ? getPlatformType(Number(meeting?.platformType))
                    : "-"
                }
                required
              />
            )}

            <Input
              label={t("meetingPurpose")}
              disabled
              value={meeting?.meetingPurpose ? meeting?.meetingPurpose : "-"}
              required
            />

            <FormMeetingDateTime
              viewOnly
              meetingTimeFromAttribute="meetingTime"
              defaultValues={{
                meetingDate: meeting?.meetingDate
                  ? dayjs(meeting.meetingDate)
                  : null,
                meetingTime: meeting?.meetingDate
                  ? dayjs(
                      `${meeting.meetingDate} ${
                        meeting?.meetingTime ?? "00:00:00"
                      }`,
                      "YYYY-MM-DD HH:mm:[00]"
                    )
                  : null,
                meetingTimeTo: meeting?.meetingDate
                  ? dayjs(
                      `${meeting.meetingDate} ${
                        meeting?.meetingTimeTo ?? "00:00:00"
                      }`,
                      "YYYY-MM-DD HH:mm:[00]"
                    )
                  : null,
              }}
            />
          </>
        ) : null}
      </Grid>
    </Box>
  );
};

export default MaklumatMesyuaratPenyataTahunan;
