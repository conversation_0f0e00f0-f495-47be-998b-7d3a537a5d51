import { useEffect, useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { CrudFilter } from "@refinedev/core";
import { useForm, FieldValues } from "react-hook-form";
import {
  MALAYSIA,
  getLocalStorage,
  useQuery,
  debounce,
  formatDate,
  getStateNameById,
} from "@/helpers";

import { Box, Typography, IconButton } from "@mui/material";
import DataTable, { IColumn } from "@/components/datatable";
import FormFieldRow from "@/components/form-field-row";
import Label from "@/components/label/Label";
import TextFieldController from "@/components/input/TextFieldController";
import SelectFieldController from "@/components/input/select/SelectFieldController";

import { IApprovalSecretaryList, IApiPaginatedResponse } from "@/types";

import { EditIcon } from "../../../components/icons";

interface Props {
  number: number;
}

const PembaharuanSetiausahaTab: React.FC<Props> = ({ number }) => {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language;
  const addressList = getLocalStorage("address_list", null);
  const malaysiaList =
    addressList.filter((address: any) => address.pid === MALAYSIA) ?? [];

  const columns: IColumn<IApprovalSecretaryList>[] = [
    {
      field: "societyNo",
      headerName: "No. PPM/NSID",
      flex: 1,
      align: "center",
    },
    {
      field: "societyName",
      headerName: t("pertubuhan"),
      flex: 1,
      align: "center",
    },
    {
      field: "applicantName",
      headerName: t("namePemohon"),
      flex: 1,
      align: "center",
    },
    {
      field: "submitDate",
      headerName: t("submissionDate"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => row.submitDate ?? "-",
    },
    {
      field: "transferDate",
      headerName: t("tarikhAlir"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => row.transferDate ?? "-",
    },
    {
      field: "roName",
      headerName: "RO",
      flex: 1,
      align: "center",
      renderCell: ({ row }) => row.roName ?? "-",
    },
    {
      field: "state",
      headerName: "Negeri",
      flex: 1,
      align: "center",
      renderCell: ({ row }) => getStateNameById(row.stateCode),
    },
    {
      field: "actions",
      headerName: t("action"),
      renderCell: ({ row }) => (
        <IconButton
          color="primary"
          onClick={() =>
            navigate(
              `/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/pembaharuan-setiausaha/${row.id}`
            )
          }
          sx={{ padding: 0, display: "block", marginInline: "auto" }}
        >
          <EditIcon />
        </IconButton>
      ),
    },
  ];

  const { control, setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      categoryCode: "",
      subCategoryCode: "",
      societyName: "",
      page: 1,
      pageSize: 10,
    },
  });

  const { page, societyName, pageSize, categoryCode, subCategoryCode } =
    watch();

  const {
    data: secretariesApprovalResponse,
    refetch: fetchSecretariesApprovalList,
    isLoading,
  } = useQuery<IApiPaginatedResponse<IApprovalSecretaryList>>({
    url: "society/roDecision/getAllPending/society/newSecretary",
    autoFetch: false,
  });

  const categories = getLocalStorage("category_list", []);
  const mainCategories = categories.filter((cat: any) => cat.level === 1) ?? [];
  const subCategories = categories.filter((cat: any) => cat.level === 2) ?? [];

  const mainCategoriesOptions = useMemo(
    () =>
      mainCategories.map((category: any) => ({
        value: category.id.toString(),
        label: category.categoryNameEn,
      })),
    [mainCategories]
  );

  const subCategoriesOptions = useMemo(
    () =>
      subCategories.map((category: any) => ({
        value: category.id.toString(),
        label: category.categoryNameEn,
      })),
    [subCategories]
  );

  const stateNameMap = useMemo(() => {
    return malaysiaList.reduce((acc: Record<string, string>, state: any) => {
      acc[state.id] = state.name;
      return acc;
    }, {});
  }, [malaysiaList]);

  const totalCount = secretariesApprovalResponse?.data?.data?.total ?? 0;
  const secretariesApprovalList =
    secretariesApprovalResponse?.data?.data?.data ?? [];

  const getBaseFilters = (pageSize: number, pageNo: number): CrudFilter[] => [
    { field: "pageSize", value: pageSize, operator: "eq" },
    { field: "pageNo", value: pageNo, operator: "eq" },
    { field: "isQuery", value: 0, operator: "eq" },
  ];

  const buildFilters = (
    pageSize: number,
    pageNo: number,
    options: {
      societyName?: string;
      categoryCode?: string;
      subCategoryCode?: string;
    }
  ): CrudFilter[] => {
    const filters = getBaseFilters(pageSize, pageNo);

    if (options.societyName) {
      filters.push({
        field: "societyName",
        value: options.societyName,
        operator: "eq",
      });
    }

    if (options.categoryCode) {
      filters.push({
        field: "categoryCode",
        value: options.categoryCode,
        operator: "eq",
      });
    }

    if (options.subCategoryCode) {
      filters.push({
        field: "subCategoryCode",
        value: options.subCategoryCode,
        operator: "eq",
      });
    }

    return filters;
  };

  const handleSearchSocietyName = useCallback(
    debounce((e) => {
      const value = e.target.value.trim();
      const filters = buildFilters(pageSize, 1, {
        societyName: value,
        categoryCode,
        subCategoryCode,
      });

      fetchSecretariesApprovalList({ filters });
    }, 2000),
    [pageSize]
  );

  const handleChangeCategories = () => {
    setValue("page", 1);
    setValue("pageSize", 10);
    const filters = buildFilters(10, 1, {
      categoryCode,
      subCategoryCode,
    });

    fetchSecretariesApprovalList({ filters });
  };

  const handleChangePage = (newPage: number) => {
    const filters = buildFilters(pageSize, newPage, {
      societyName,
      categoryCode,
      subCategoryCode,
    });
    setValue("page", newPage);
    fetchSecretariesApprovalList({ filters });
  };

  const handleChangePageSize = (newPageSize: number) => {
    const filters = buildFilters(newPageSize, 1, {
      societyName,
      categoryCode,
      subCategoryCode,
    });
    setValue("pageSize", newPageSize);
    fetchSecretariesApprovalList({ filters });
  };

  useEffect(() => {
    fetchSecretariesApprovalList({
      filters: getBaseFilters(pageSize, 1),
    });
  }, []);

  useEffect(() => {
    handleChangeCategories();
  }, [categoryCode, subCategoryCode]);

  return (
    <>
      <Box
        sx={{
          padding: "22px 16px",
          background: "#FFF",
          borderRadius: "15px",
          boxShadow: "0px 12px 12px 0px #EAE8E866",
          marginBottom: 1,
        }}
      >
        <Box
          sx={{
            width: "100%",
            border: "0.5px solid #DADADA",
            borderRadius: "10px",
            padding: "22px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {currentLanguage === "my"
              ? "Senarai  Pembaharuan Setiausaha"
              : "Secretary Renewal List"}
          </Typography>

          <FormFieldRow
            label={<Label text={t("organization_category")} />}
            value={
              <SelectFieldController
                name="categoryCode"
                control={control}
                options={mainCategoriesOptions}
                placeholder={t("selectPlaceholder")}
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("organizationSubCategory2")} />}
            value={
              <SelectFieldController
                name="subCategoryCode"
                control={control}
                options={subCategoriesOptions}
                placeholder={t("selectPlaceholder")}
                disabled={!categoryCode}
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("namaPertubuhan")} />}
            value={
              <TextFieldController
                name="societyName"
                control={control}
                onChange={handleSearchSocietyName}
              />
            }
          />
        </Box>
      </Box>

      <Box
        sx={{
          padding: "22px 16px",
          background: "#FFF",
          borderRadius: "15px",
          boxShadow: "0px 12px 12px 0px #EAE8E866",
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: "13px",
            padding: "15px 0",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            marginBottom: 1,
            backgroundColor: "var(--primary-color)",
          }}
        >
          <Typography
            fontWeight="500 !important"
            fontSize="36px"
            color="#FFF"
            textAlign="center"
            lineHeight="30px"
            sx={{
              "& span": {
                fontSize: "20px",
              },
            }}
          >
            {number}
            <br />
            <span>
              {currentLanguage === "my"
                ? "Permohonan Pembaharuan Setiausaha"
                : "Secretary Renewal Application"}
            </span>
          </Typography>
        </Box>
        <Box
          sx={{
            width: "100%",
            border: "0.5px solid #DADADA",
            borderRadius: "10px",
            padding: "22px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="37px"
          >
            {currentLanguage === "my"
              ? "Senarai Pembaharuan Setiausaha"
              : "Secretary Renewal List"}
          </Typography>

          <DataTable
            columns={columns}
            rows={secretariesApprovalList}
            page={page}
            isLoading={isLoading}
            rowsPerPage={pageSize}
            totalCount={totalCount}
            onPageChange={handleChangePage}
            onPageSizeChange={handleChangePageSize}
          />
        </Box>
      </Box>
    </>
  );
};

export default PembaharuanSetiausahaTab;
