import {
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Grid,
  TextField,
  Typography,
  Dialog,
  DialogContent,
  useMediaQuery,
  useTheme,
  Theme,
} from "@mui/material";
import { t } from "i18next";
import React, { useEffect, useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { ButtonOutline, ButtonPrimary } from "../../../components/button";
import CheckIcon from "@mui/icons-material/Check";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../api";
import { useNavigate, useParams } from "react-router-dom";
import MaklumatAmSection from "./views/MaklumatAmSection";
import MesyuaratPenubuhanSection from "./views/MesyuaratPenubuhanSection";
import KelulusanAccordion from "./views/KelulusanAccordion";
import PerlembagaanSection from "./views/PerlembagaanSection";
import <PERSON><PERSON>i<PERSON>jk from "./views/SenaraiAjkSection";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import DokumenSokonganSection from "./views/DokumenSokonganSection";
import {
  ApplicationStatusList,
  DecisionOptionsCode,
  NEW_PermissionNames,
  ROApprovalType,
} from "../../../helpers/enums";
import { capitalizeWords } from "../../../helpers/utils";
// import TextArea from "@/components/input/TextArea";
import {
  DialogConfirmation,
  SelectFieldController,
  TextFieldController,
} from "@/components";
// import DialogConfirmation from "../keputusan-induk-pembubaran/views/DialogConfirmation";
import { PermissionNames, pageAccessEnum, useQuery } from "@/helpers";
import dayjs from "dayjs";
import AuthHelper from "@/helpers/authHelper";
import { setSocietyDataRedux } from "@/redux/societyDataReducer";
import { AppDispatch } from "@/redux/store";
import { useDispatch } from "react-redux";
import { fetchSocietyByIdData } from "@/redux/APIcalls/societyByIdThunks";

const labelStyle = {
  fontSize: "16px",
  color: "#666666",
  fontWeight: "400 !important",
};

const optionlabelStyle = {
  fontSize: "16px",
  color: "var(--primary-color)",
  fontWeight: "500 !important",
};

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

type ReadStatusType = {
  [key: number]: Boolean;
};

const options = [
  {
    code: "Seksyen 7(2)(a)",
    description:
      "Pertubuhan yang ingin didaftarkan adalah pertubuhan atau cawangan pertubuhan yang pernah dibatalkan pendaftarannya mengikut Seksyen 13(1)(c) Akta Pertubuhan 1966",
  },
  {
    code: "Seksyen 7(2)(b)",
    description:
      "Pertubuhan yang ingin didaftarkan tidak mematuhi kehendak peruntukan Akta Pertubuhan 1966 atau peraturan-peraturan berkaitannya",
  },
  {
    code: "Seksyen 7(2)(c)",
    description:
      "Wujud pertikaian di kalangan anggota pertubuhan yang ingin didaftarkan tentang pemegangan jawatan atau harta pentadbiran harta penubuhan",
  },
  {
    code: "Seksyen 7(3)(a)",
    description:
      "Pertubuhan yang ingin didaftarkan adalah sebuah pertubuhan yang menyalahi undang-undang mengikut peruntukan Akta Pertubuhan 1966 atau mana-mana peruntukan undang-undang bertulis yang lain. Pertubuhan yang ingin didaftarkan dikuatiri akan digunakan bagi tujuan yang menyalahi undang-undang atau bagi mengugat keselamatan dan ketenteraman awam.",
  },
  {
    code: "Seksyen 7(3)(b)",
    description:
      "Pertubuhan yang ingin didaftarkan telahpun diisytiharkan sebagai menyalahi undang-undang oleh Menteri mengikut peruntukan Seksyen 5 Akta Pertubuhan 1966",
  },
  {
    code: "Seksyen 7(3)(c)",
    description: "Pertubuhan yang ingin didaftarkan didapati tidak wujud",
  },
  {
    code: "Seksyen 7(3)(a)(i)",
    description:
      "Pertubuhan yang ingin didaftarkan mempunyai nama yang hampir sama dengan pertubuhan berdaftar yang lain sehingga boleh menimbulkan kekeliruan",
  },
  {
    code: "Seksyen 7(3)(a)(ii)",
    description:
      "Pertubuhan yang ingin didaftarkan mempunyai nama yang sama dengan pertubuhan berdaftar yang lain.",
  },
  {
    code: "Seksyen 7(3)(a)(iii)",
    description:
      "Pertubuhan yang hendak didaftarkan mempunyai nama yang tidak diingini.",
  },
  {
    code: "Seksyen 7(3)(d)(e)(v)",
    description:
      "Pertubuhan yang ingin didaftarkan gagal memenuhi kehendak Jadual 1/2 Akta Pertubuhan 1966 yang mewajibkan peruntukan beberapa fasal dalam perlembagaan pertubuhan sebelum ianya boleh didaftarkan.",
  },
  {
    code: "",
    description: "Tiada",
  },
];

function KeputusanIndukKelulusanSemak() {
  const hasUpdatePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_INDUK.children.PENDAFTARAN_PERTUBUHAN_INDUK.label,
    pageAccessEnum.Update
  );

  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);
  const keputusan_induk__kelulusan_items = [
    {
      subTitle: t("generalInformation"),
      component: <MaklumatAmSection />,
    },
    {
      subTitle: capitalizeWords(t("establishmentMeeting")),
      component: <MesyuaratPenubuhanSection />,
    },
    {
      subTitle: t("constitution"),
      component: <PerlembagaanSection />,
    },
    {
      subTitle: t("ajkList"),
      component: <SenaraiAjk />,
    },
    {
      subTitle: t("supportingDocuments"),
      component: <DokumenSokonganSection />,
    },
  ];
  const navigate = useNavigate();
  const decisionOptions = DecisionOptionsCode(t);
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const dispatch: AppDispatch = useDispatch();
  const { id } = useParams();
  const decodedId = atob(id || "");
  const theme = useTheme();
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [dialogSejarahKuiriSaveOpen, setDialogSejarahKuiriSaveOpen] =
    useState(false);
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));
  const [societyName, setSocietyName] = useState("");
  const [societyNo, setSocietyNo] = useState("");
  const [query, setQuery] = useState([]);
  const [readStatus, setReadStatus] = useState<ReadStatusType>({});
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [noteUlasanAgensiLuarList, setNoteUlasanAgensiLuarList] = useState([]);
  const [modalUlasanAgensiLuarList, setModalUlasanAgensiLuarList] =
    useState(false);

  const methodsRoAction = useForm();

  const {
    handleSubmit: handleSubmitRoAction,
    control: controlRoAction,
    setValue: setValueRoAction,
    getValues: getValuesRoAction,
    watch: watchRoAction,
  } = methodsRoAction;

  const {
    control,
    handleSubmit,
    getValues,
    watch,
    setValue,
    setError,
    formState: { errors },
  } = useForm<FieldValues>({
    defaultValues: {
      societyId: decodedId,
      applicationStatusCode: "",
      noteKuiri: "",
      roApprovalType: ROApprovalType.SOCIETY_REGISTRATION.code,
      note: "",
      rejectReason: "",
    },
  });

  const { applicationStatusCode } = watch();
  const onSubmit = () => setIsDialogOpen(true);

  const handleChangeCurrentExpandSection =
    (item: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);
      if (isExpanded) {
        // setReadStatus((prevState) => ({ ...prevState, [item]: true }));
        setReadStatus((prevState) => {
          const updatedStatus = keputusan_induk__kelulusan_items.reduce(
            (acc, _, i) => {
              if (i + 1 <= item) {
                acc[i + 1] = true;
              } else {
                acc[i + 1] = !!prevState[i + 1] || false;
              }
              return acc;
            },
            {} as Record<number, boolean>
          );
          return updatedStatus;
        });
      }
      // console.log(readStatus);
    };

  const handleChangeOption = (code: string) => {
    setSelectedOptions((prev: string[]) =>
      prev.includes(code)
        ? prev.filter((item) => item !== code)
        : [...prev, code]
    );
  };

  const { mutate: updateApprovalStatus, isLoading: isUpdatingStatus } =
    useCustomMutation();
  const handleDialogClose = () => setIsDialogOpen(false);
  const handleFormSubmit = () => {
    const payload = getValues();
    updateApprovalStatus(
      {
        url: `${API_URL}/society/roDecision/updateApprovalStatus`,
        method: "patch",
        values: payload,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess() {
          setIsSuccess(true);
          navigate("/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk");
        },
      }
    );
  };

  //get query record
  const { mutate: getQuery, isLoading } = useCustomMutation();
  useEffect(() => {
    const data = {
      societyId: decodedId,
      roApprovalType: ROApprovalType.SOCIETY_REGISTRATION.code,
    };
    dispatch(fetchSocietyByIdData({ id: decodedId }));
    getQuery(
      {
        method: "post",
        url: `${API_URL}/society/roQuery/getQuery`,
        values: data,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess(data) {
          const queryData = data?.data?.data;
          setQuery(queryData);
          if (queryData?.length > 0) {
            setValue("noteKuiri", queryData[0].note || "");
          }
        },
      }
    );
  }, []);
  // get society detail
  const { data } = useCustom({
    url: `${API_URL}/society/${decodedId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        dispatch(setSocietyDataRedux(responseData?.data?.data));
        const { applicationNo, societyName } = responseData?.data?.data;
        setSocietyName(societyName);
        setSocietyNo(applicationNo);
      },
    },
  });

  const { data: roListData, isLoading: isRoListLoading } = useCustom({
    url: `${API_URL}/society/user/getRoList`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: decodedId,
      },
    },
  });

  // GetAllExternalAgencyReview record by SocietyId
  const {
    data: externalAgencyData,
    isLoading: externalAgencyDataLoading,
    refetch: fetchAgencyRecordData,
  } = useQuery({
    url: `society/internal/externalAgencyReview/getAllBySocietyId`,
    autoFetch: true,
    filters: [{ field: "societyId", operator: "eq", value: decodedId }],
    onSuccess: (data) => {
      const responseData = data?.data?.data?.[0] || undefined;
      setNoteUlasanAgensiLuarList(data?.data?.data);
      if (responseData) {
        setValue("noteUlasanAgensiLuar", responseData.note);
      }
    },
  });

  const roList = roListData?.data?.data ?? [];

  const roListOptions =
    roList.map((item: any) => ({
      value: item.id,
      label: item.name,
    })) || [];

  const { mutate: updateRo, isLoading: isLoadingRoUpdate } =
    useCustomMutation();

  const onSubmitRoAction = (data: FieldValues) => {
    updateRo(
      {
        url: `${API_URL}/society/roDecision/updateRo`,
        method: "patch",
        values: {
          societyId: decodedId,
          roApprovalType: "SOCIETY_REGISTRATION",
          roId: data.ro,
          noteRo: data.noteRo,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${decodedId}/getPendingBySocietyId`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const societyDetail = societyData?.data?.data?.societyPendingResponse ?? null;
  console.log("societyDetail", societyDetail);
  useEffect(() => {
    if (societyDetail) {
      console.log("societyDetail", societyDetail);
      setValueRoAction("noteRo", societyDetail.noteRo);
      setValueRoAction("ro", societyDetail.ro);
    }
  }, [societyDetail]);

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              position: "relative",
              display: "grid",
              borderRadius: "13px",
              overflow: "hidden",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
              gap: 1,
            }}
          >
            <Typography sx={{ fontWeight: "400!important" }}>
              {societyName}
            </Typography>
            <Typography sx={{ fontWeight: "400!important" }}>
              {societyNo}
            </Typography>
            <Box
              sx={{
                position: "absolute",
                right: "30px",
                bottom: 0,
              }}
            >
              <img src="/titleBarIcon.svg" alt="" />
            </Box>
          </Box>
        </Box>
        <Box sx={{ mt: 4 }}>
          {keputusan_induk__kelulusan_items.map((item, index) => {
            return (
              <KelulusanAccordion
                key={index}
                subTitle={item.subTitle}
                currentIndex={index + 1}
                currentExpand={currentExpandSection}
                readStatus={readStatus}
                onChangeFunc={handleChangeCurrentExpandSection}
              >
                {item.component}
              </KelulusanAccordion>
            );
          })}
        </Box>
        {query?.length > 0 && query ? (
          <Box
            sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
          >
            <Box
              sx={{
                pl: 2,
                p: 3,
                mt: 1,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
              }}
            >
              <Box
                sx={{
                  mb: 3,
                }}
              >
                <Typography color={"primary"}>{t("kuiri")}</Typography>
              </Box>
              <Grid container>
                <Grid
                  item
                  xs={12}
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                    alignContent: "center",
                    pb: 4,
                  }}
                  gap={2}
                >
                  <Box
                    sx={{
                      background: "var(--primary-color)",
                      cursor: "pointer",
                      display: "flex",
                      justifyContent: "center",
                      alignContent: "center",
                      borderRadius: "10px",
                      p: 1,
                    }}
                  >
                    <img width={26} height={26} src="/addkuiri.svg" alt="" />
                  </Box>
                  <ButtonOutline
                    onClick={() =>
                      setDialogSejarahKuiriSaveOpen(!dialogSejarahKuiriSaveOpen)
                    }
                  >
                    {t("queryHistory")}
                  </ButtonOutline>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("catatanKuiri")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    name="noteKuiri"
                    value={getValues("noteKuiri")}
                    disabled
                  />
                </Grid>
              </Grid>
            </Box>
          </Box>
        ) : (
          ""
        )}
        <Box
          sx={{
            backgroundColor: "white",
            p: 3,
            borderRadius: "15px",
            mt: 2,
            display:
              noteUlasanAgensiLuarList && noteUlasanAgensiLuarList.length > 0
                ? "block"
                : "none",
          }}
        >
          <Box
            sx={{
              pl: 2,
              p: 3,
              mt: 1,
              borderRadius: "10px",
              border: "0.5px solid #dfdfdf",
            }}
          >
            <Box
              sx={{
                mb: 3,
              }}
            >
              <Typography color={"primary"}>
                {t("externalAgencyReviews")}
              </Typography>
            </Box>
            <Grid container spacing={2}>
              <Grid
                item
                xs={12}
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignContent: "center",
                  pb: 4,
                }}
                gap={2}
              >
                <ButtonOutline
                  onClick={() => setModalUlasanAgensiLuarList(true)}
                >
                  {t("sejarahUlasanLuar")}
                </ButtonOutline>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography variant="body1" sx={labelStyle}>
                  {t("remarks")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextFieldController
                  control={control}
                  name="noteUlasanAgensiLuar"
                  multiline
                  disabled
                  sx={{
                    minHeight: "92px",
                  }}
                  sxInput={{
                    minHeight: "92px",
                  }}
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          {/* Tindakan RO */}
          <form onSubmit={handleSubmitRoAction(onSubmitRoAction)}>
            <Box
              sx={{
                p: 3,
                mb: 3,
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography variant="h6" component="h2" sx={subTitleStyle}>
                {t("ROAction")}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("responsibleRO")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <SelectFieldController
                    name="ro"
                    defaultValue={getValuesRoAction("ro")}
                    control={controlRoAction}
                    options={roListOptions}
                    disabled={!hasUpdatePermission}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("remarks")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={controlRoAction}
                    disabled={!hasUpdatePermission}
                    name="noteRo"
                    multiline
                    defaultValue={getValuesRoAction("noteRo")}
                    sx={{
                      minHeight: "92px",
                    }}
                    sxInput={{
                      minHeight: "92px",
                    }}
                  />
                </Grid>
              </Grid>
            </Box>

            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonPrimary
                variant="contained"
                type="submit"
                sx={{ width: isMobile ? "100%" : "auto", mb: 3 }}
                disabled={!hasUpdatePermission}
              >
                {t("update")}
              </ButtonPrimary>
            </Grid>
          </form>

          {/* Keputusan */}
          <form onSubmit={handleSubmit(onSubmit)}>
            <Box
              sx={{
                pl: 2,
                p: 3,
                mt: 1,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
              }}
            >
              <Box
                sx={{
                  mb: 3,
                }}
              >
                <Typography variant="h6" component="h2" sx={subTitleStyle}>
                  {t("keputusan")}
                </Typography>
              </Box>
              <Grid container>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("statusPermohonan")}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                  <SelectFieldController
                    control={control}
                    name="applicationStatusCode"
                    options={decisionOptions}
                    disabled={!hasUpdatePermission}
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("remarks")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={control}
                    disabled={!hasUpdatePermission}
                    name="note"
                    multiline
                    sx={{
                      minHeight: "92px",
                    }}
                    sxInput={{
                      minHeight: "92px",
                    }}
                    required={watch("applicationStatusCode") === 36}
                  />
                </Grid>
              </Grid>
            </Box>
            {getValues("applicationStatusCode") == "4" ? (
              <Box
                sx={{
                  pl: 2,
                  p: 3,
                  mt: 1,
                  borderRadius: "10px",
                  border: "0.5px solid #dfdfdf",
                }}
              >
                <Box
                  sx={{
                    mb: 3,
                  }}
                >
                  <Typography color={"primary"}>
                    {t("rejectionReason")}
                  </Typography>

                  <FormGroup>
                    {options.map((option) => (
                      <FormControlLabel
                        sx={{ mb: 3, mt: 3 }}
                        key={option.code}
                        disabled={!hasUpdatePermission}
                        control={
                          <Checkbox
                            checked={selectedOptions.includes(option.code)}
                            onChange={() => handleChangeOption(option.code)}
                          />
                        }
                        label={
                          <Box sx={{ ml: 2 }}>
                            <Typography
                              sx={optionlabelStyle}
                              variant="subtitle1"
                              component="span"
                            >
                              {option.code}
                            </Typography>
                            {option.description && (
                              <Typography
                                sx={labelStyle}
                                variant="body2"
                                color="textSecondary"
                              >
                                {option.description}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                    ))}
                  </FormGroup>
                </Box>
              </Box>
            ) : null}

            <Box
              sx={{
                mt: 5,
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
              }}
            >
              <ButtonOutline sx={{ py: 1 }} onClick={() => navigate(-1)}>
                {t("back2")}
              </ButtonOutline>
              <ButtonPrimary
                disabled={!hasUpdatePermission}
                type="submit"
                sx={{ py: 1 }}
              >
                {t("hantar")}
              </ButtonPrimary>
            </Box>
          </form>
        </Box>
      </Box>

      <Dialog
        open={dialogSejarahKuiriSaveOpen}
        onClose={() => setDialogSejarahKuiriSaveOpen(false)}
        fullScreen={fullScreen}
        PaperProps={{
          style: {
            borderRadius: "8px",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogContent sx={{ py: 4 }}>
          <Box
            sx={{
              p: 3,
              mt: 1,
              borderRadius: "10px",
              border: "0.5px solid #dfdfdf",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                pb: 4,
                color: "var(--primary-color)",
              }}
              gap={2}
            >
              <ChevronLeftIcon
                sx={{ cursor: "pointer" }}
                onClick={() =>
                  setDialogSejarahKuiriSaveOpen(!dialogSejarahKuiriSaveOpen)
                }
              />
              <Typography sx={{ fontWeight: "4001important" }}>
                {t("queryHistory")}
              </Typography>
            </Box>
            {query?.length > 0 &&
              query?.map((item: any, index) => {
                const isCompleted = item?.finished;
                const lastIndex = query?.length - 1;
                return (
                  <Box sx={{ display: "flex", mb: 4 }} key={index}>
                    <Box sx={{ mr: 2 }}>
                      <Box
                        sx={{
                          width: 35,
                          height: 35,
                          borderRadius: "50%",
                          border: isCompleted
                            ? "1px solid var(--primary-color)"
                            : "1px solid #FF0000",
                          backgroundColor: "#FF000080",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        {isCompleted ? (
                          <CheckIcon sx={{ color: "white" }} />
                        ) : null}
                      </Box>
                      {lastIndex === index ? (
                        ""
                      ) : (
                        <>
                          <Box
                            sx={{
                              width: 2,
                              height: "100%",
                              backgroundColor: "#DADADA",
                              ml: 2,
                            }}
                          />
                        </>
                      )}
                    </Box>
                    <Box sx={{ width: "100%" }}>
                      <Box sx={{ display: "flex", gap: 3 }}>
                        <Typography
                          sx={{
                            fontWeight: "300",
                            border: "1px solid #5088FF",
                            background: "#5088FF80",
                            borderRadius: "9px",
                            color: "#fff",
                            px: 2,
                            py: 1,
                            fontSize: "12px",
                          }}
                        >
                          {t("kuiri")} #{item.id}
                        </Typography>
                        <Typography
                          sx={{
                            fontWeight: "300",
                            border: "1px solid #DADADA",
                            background: "transparent",
                            borderRadius: "9px",
                            color: "#666666",
                            px: 2,
                            py: 1,
                            fontSize: "12px",
                          }}
                        >
                          {item.createdDate}
                        </Typography>
                        <Typography
                          sx={{
                            fontWeight: "300",
                            border: "1px solid #DADADA",
                            background: "#transparent",
                            borderRadius: "9px",
                            color: "#666666",
                            px: 2,
                            py: 1,
                            fontSize: "12px",
                          }}
                        >
                          {item.officerName}
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          p: 3,
                          border: "1px solid #D9D9D9",
                          borderRadius: "14px",
                          width: "100%",
                          mt: 2,
                          minHeight: "150px",
                          position: "relative",
                        }}
                      >
                        <Typography sx={{ mb: 3, color: "#666666" }}>
                          {item.note ? item.note : t("sureAjk")}
                        </Typography>
                        <span
                          style={{
                            fontFamily: '"Poppins", sans-serif',
                            backgroundColor: "#FF000080",
                            border: "1px solid #FF0000",
                            padding: "6px 20px",
                            borderRadius: "18px",
                            color: "#fff",
                            fontSize: "14px",
                            fontWeight: "400",
                            position: "absolute",
                            bottom: "20px",
                            right: "20px",
                          }}
                        >
                          {isCompleted ? t("SELESAI") : t("belumselesai")}
                        </span>
                      </Box>
                    </Box>
                  </Box>
                );
              })}
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <Button
                sx={{
                  textDecoration: "underline",
                  color: "var(--primary-color)",
                  mt: 2,
                }}
              >
                {t("lihatpaparanpenuh")}
              </Button>
            </Box>
          </Box>
        </DialogContent>
      </Dialog>
      {/* modalUlasanAgensiLuarList */}
      <Dialog
        open={modalUlasanAgensiLuarList}
        onClose={() => setModalUlasanAgensiLuarList(false)}
        fullScreen={fullScreen}
        PaperProps={{
          style: {
            borderRadius: "8px",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogContent sx={{ py: 4 }}>
          <Box
            sx={{
              p: 3,
              mt: 1,
              borderRadius: "10px",
              border: "0.5px solid #dfdfdf",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                pb: 4,
                color: "var(--primary-color)",
              }}
              gap={2}
            >
              <ChevronLeftIcon
                sx={{ cursor: "pointer" }}
                onClick={() => setModalUlasanAgensiLuarList(false)}
              />
              <Typography sx={{ fontWeight: "4001important" }}>
                {t("sejarahUlasanLuar")}
              </Typography>
            </Box>
            {noteUlasanAgensiLuarList?.length > 0 &&
              noteUlasanAgensiLuarList?.map((item: any, index) => {
                const isCompleted = item?.finished;
                const lastIndex = query?.length - 1;

                const formattedDate = dayjs(item.submissionDate).format(
                  "DD-MM-YYYY | HH:mm"
                );

                return (
                  <Box sx={{ display: "flex", mb: 4 }} key={index}>
                    <Box sx={{ mr: 2 }}>
                      <Box
                        sx={{
                          width: 35,
                          height: 35,
                          borderRadius: "50%",
                          border: isCompleted
                            ? "1px solid var(--primary-color)"
                            : "1px solid #FF0000",
                          backgroundColor: "#FF000080",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        {isCompleted ? (
                          <CheckIcon sx={{ color: "white" }} />
                        ) : null}
                      </Box>
                      {lastIndex === index ? (
                        ""
                      ) : (
                        <>
                          <Box
                            sx={{
                              width: 2,
                              height: "100%",
                              backgroundColor: "#DADADA",
                              ml: 2,
                            }}
                          />
                        </>
                      )}
                    </Box>
                    <Box sx={{ width: "100%" }}>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          gap: 3,
                        }}
                      >
                        <Typography
                          sx={{
                            fontWeight: "300",
                            border: "1px solid #5088FF",
                            background: "#5088FF80",
                            borderRadius: "9px",
                            color: "#fff",
                            px: 2,
                            py: 1,
                            fontSize: "12px",
                          }}
                        >
                          {t("ulasan")} #{index + 1}
                        </Typography>
                        <Typography
                          sx={{
                            fontWeight: "300",
                            border: "1px solid #DADADA",
                            background: "transparent",
                            borderRadius: "9px",
                            color: "#666666",
                            px: 2,
                            py: 1,
                            fontSize: "12px",
                          }}
                        >
                          {formattedDate}
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          p: 3,
                          border: "1px solid #D9D9D9",
                          borderRadius: "14px",
                          width: "100%",
                          mt: 2,
                          minHeight: "150px",
                          position: "relative",
                        }}
                      >
                        <Typography sx={{ mb: 3, color: "#666666" }}>
                          {item.note}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                );
              })}
          </Box>
        </DialogContent>
      </Dialog>

      <DialogConfirmation
        open={isDialogOpen}
        onClose={handleDialogClose}
        onAction={handleFormSubmit}
        isMutating={isUpdatingStatus}
        onConfirmationText={t("permohonanConfirmation")}
        onSuccessText={t("applicationSuccessSubmited")}
        isSuccess={isSuccess}
        decisionLabel={
          ApplicationStatusList.find(
            (item) => item.id === getValues().applicationStatusCode
          )?.value || "-"
        }
      />
    </>
  );
}

export default KeputusanIndukKelulusanSemak;
