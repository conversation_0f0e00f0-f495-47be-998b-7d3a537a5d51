import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import {
  formatAndValidateNumber,
  months,
  RegExNumbers,
  RegExText,
} from "@/helpers";
import AsalContent from "@/components/asalFasalContent";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import CustomPopover from "@/components/popover";
interface FasalContentTigaBelasFaedahProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: [];
  name: string;
}

export const FasalContentTigaBelasFaedah: React.FC<
  FasalContentTigaBelasFaedahProps
> = ({ activeStep, setActiveStep, clause, asalData, name }) => {
  const [asal, setAsal] = useState<any>(null);
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [kuasaPerbelanjaan, setKuasaPerbelanjaan] = useState("");
  const [kuasaPerbelanjaanKata, setKuasaPerbelanjaanKata] = useState("");
  const [kuasaPerbelanjaanJawatankuasa, setKuasaPerbelanjaanJawatankuasa] =
    useState("");
  const [
    kuasaPerbelanjaanJawatankuasaKata,
    setKuasaPerbelanjaanJawatankuasaKata,
  ] = useState("");
  const [perbelanjaanDibenarkan, setPerbelanjaanDibenarkan] = useState("");
  const [perbelanjaanDibenarkanKata, setPerbelanjaanDibenarkanKata] =
    useState("");
  const [tahunKewanganBermula, setTahunKewanganBermula] = useState("");

  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!kuasaPerbelanjaan) {
      errors.kuasaPerbelanjaan = t("fieldRequired");
    }

    if (!kuasaPerbelanjaanKata) {
      errors.kuasaPerbelanjaanKata = t("fieldRequired");
    }

    if (!kuasaPerbelanjaanJawatankuasa) {
      errors.kuasaPerbelanjaanJawatankuasa = t("fieldRequired");
    }

    if (!kuasaPerbelanjaanJawatankuasaKata) {
      errors.kuasaPerbelanjaanJawatankuasaKata = t("fieldRequired");
    }

    if (!perbelanjaanDibenarkan) {
      errors.perbelanjaanDibenarkan = t("fieldRequired");
    }

    if (!perbelanjaanDibenarkanKata) {
      errors.perbelanjaanDibenarkanKata = t("fieldRequired");
    }

    if (!tahunKewanganBermula) {
      errors.tahunKewanganBermula = t("fieldRequired");
    }

    return errors;
  };

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  const { id, clauseId } = useParams();
  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName, address, mailingAddress } =
          responseData?.data?.data;
        setNamaPertubuhan(societyName);
      },
    },
  });

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause = JSON.parse(clause13);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause9Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause.societyName);
      setKuasaPerbelanjaan(clause.constitutionValues[0]?.definitionName);
      setKuasaPerbelanjaanKata(clause.constitutionValues[1]?.definitionName);
      setKuasaPerbelanjaanJawatankuasa(
        clause.constitutionValues[2]?.definitionName
      );
      setKuasaPerbelanjaanJawatankuasaKata(
        clause.constitutionValues[3]?.definitionName
      );
      setPerbelanjaanDibenarkan(clause.constitutionValues[4]?.definitionName);
      setPerbelanjaanDibenarkanKata(
        clause.constitutionValues[5]?.definitionName
      );
      setTahunKewanganBermula(clause.constitutionValues[6]?.definitionName);
      setIsEdit(clause.edit);
      setAsal(
        asalData.find((item: any) => item.clauseNo === clause.clauseNo
        ) || ""
      );
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  let clauseContent = clause.clauseContent;
  //clauseContent = clauseContent.replaceAll(/<<jumlah wang tangan yang dibenarkan dalam tangan>>/gi, `<b>${jumlahWangTangan || '<<jumlah wang tangan yang dibenarkan dalam tangan>>'}</b>`);
  //clauseContent = clauseContent.replaceAll(/<<jumlah wang dibenarkan-tulis dalam perkataan>>/gi, `<b>${jumlahWangTanganKata || '<<jumlah wang dibenarkan-tulis dalam perkataan>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<perbelanjaan yang dibenarkan>>/gi,
    `<b>${perbelanjaanDibenarkan || "<<perbelanjaan yang dibenarkan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<perbelanjaan yang dibenarkan -dalam perkataan>>/gi,
    `<b>${
      perbelanjaanDibenarkanKata ||
      "<<perbelanjaan yang dibenarkan -dalam perkataan>>"
    }</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${tempohPelucutanWaktu || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan mesyuarat agung>>/gi,
    `<b>${kuasaPerbelanjaan || "<<kuasa perbelanjaan mesyuarat agung>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahPengerusi || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan mesyuarat agung-dalam perkataan>>/gi,
    `<b>${
      kuasaPerbelanjaanKata ||
      "<<kuasa perbelanjaan mesyuarat agung-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan jawatankuasa>>/gi,
    `<b>${
      kuasaPerbelanjaanJawatankuasa || "<<kuasa perbelanjaan jawatankuasa>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan jawatankuasa-dalam perkataan>>/gi,
    `<b>${
      kuasaPerbelanjaanJawatankuasaKata ||
      "<<kuasa perbelanjaan jawatankuasa-dalam perkataan>>"
    }</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<tempoh yang dibenarkan utk masukkan wang ke dalam bank>>/gi, `<b>${tempohDibenarkan || '<<tempoh yang dibenarkan utk masukkan wang ke dalam bank>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<tahun kewangan bermula>>/gi,
    `<b>${tahunKewanganBermula || "<<tahun kewangan bermula>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  return (
    <>
      <AsalContent
        asalData={asal?.description || ""}
        clauseId={clauseId}
        name={name}
      />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("permulaanTahunKewangan")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography sx={labelStyle}>
                {t("tahunKewanganBermula")}{" "}
                <Typography sx={{ display: "inline", color: "red" }}>
                  *
                </Typography>
              </Typography>
              <CustomPopover
                customStyles={{ maxWidth: "250px" }}
                content={
                  <Typography sx={{ color: "#666666" }}>
                    {t("contohJan")}
                  </Typography>
                }
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required>
              <Select
                size="small"
                disabled={isViewMode}
                value={tahunKewanganBermula}
                displayEmpty
                required
                onChange={(e) => {
                  setTahunKewanganBermula(e.target.value);
                }}
              >
                {months.map((i) => {
                  return (
                    <MenuItem value={i.value} selected>
                      {i.label}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("kuasaPerbelanjaan")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("kuasaPerbelanjaan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={kuasaPerbelanjaan}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setKuasaPerbelanjaan(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaan: "",
                  }));
                } else {
                  e.preventDefault();
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaan}
              helperText={formErrors.kuasaPerbelanjaan}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              placeholder="contoh : dua puluh"
              value={kuasaPerbelanjaanKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setKuasaPerbelanjaanKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanKata: "",
                  }));
                } else {
                  e.preventDefault();
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaanKata}
              helperText={formErrors.kuasaPerbelanjaanKata}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("kuasaPerbelanjaanJawatankuasa")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("kuasaPerbelanjaanJawatankuasa")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              disabled={isViewMode}
              value={kuasaPerbelanjaanJawatankuasa}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setKuasaPerbelanjaanJawatankuasa(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasa: "",
                  }));
                } else {
                  e.preventDefault();
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasa: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaanJawatankuasa}
              helperText={formErrors.kuasaPerbelanjaanJawatankuasa}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              placeholder="contoh : dua puluh"
              required
              value={kuasaPerbelanjaanJawatankuasaKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setKuasaPerbelanjaanJawatankuasaKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasaKata: "",
                  }));
                } else {
                  e.preventDefault();
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasaKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaanJawatankuasaKata}
              helperText={formErrors.kuasaPerbelanjaanJawatankuasaKata}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("perbelanjaanDibenarkan")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("perbelanjaanDibenarkan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={perbelanjaanDibenarkan}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setPerbelanjaanDibenarkan(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    perbelanjaanDibenarkan: "",
                  }));
                } else {
                  setPerbelanjaanDibenarkan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    perbelanjaanDibenarkan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.perbelanjaanDibenarkan}
              helperText={formErrors.perbelanjaanDibenarkan}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              placeholder="contoh : dua puluh"
              required
              value={perbelanjaanDibenarkanKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setPerbelanjaanDibenarkanKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    perbelanjaanDibenarkanKata: "",
                  }));
                } else {
                  setPerbelanjaanDibenarkanKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    perbelanjaanDibenarkanKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.perbelanjaanDibenarkanKata}
              helperText={formErrors.perbelanjaanDibenarkanKata}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              const errors = validateForm();
              if (Object.keys(errors).length > 0) {
                setFormErrors(errors);
                return;
              }

              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: kuasaPerbelanjaan,
                    titleName: "Kuasa Perbelanjaan Mesyuarat Agung",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: kuasaPerbelanjaanKata,
                    titleName:
                      "Kuasa Perbelanjaan Mesyuarat Agung (Tulis Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: kuasaPerbelanjaanJawatankuasa,
                    titleName: "Kuasa Perbelanjaan Jawatankuasa",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: kuasaPerbelanjaanJawatankuasaKata,
                    titleName:
                      "Kuasa Perbelanjaan Jawatankuasa (Tulis Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: perbelanjaanDibenarkan,
                    titleName: "Perbelanjaan yang Dibenarkan",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: perbelanjaanDibenarkanKata,
                    titleName:
                      "Perbelanjaan yang Dibenarkan (Tulis Dalam Perkataan)",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tahunKewanganBermula,
                    titleName:
                      "Tahun Kewangan Bermula (Tahun Kewangan Bermula Dari Tahun Kewangan Yang Dibenarkan)",
                  },
                ],
                clause: "clause13",
                clauseCount: 13,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentTigaBelasFaedah;
