import { useParams } from "react-router-dom";
import { FieldValues, useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  useMutation,
  useQuery,
  omit<PERSON><PERSON>sFromObject,
  NEW_PermissionNames,
  pageAccessEnum,
} from "@/helpers";

import {
  Box,
  Theme,
  Typography,
  useMediaQuery,
  CircularProgress,
} from "@mui/material";
import {
  ButtonPrimary,
  FormFieldRow,
  TextFieldController,
  Label,
  CustomSkeleton,
} from "@/components";
import ButtonPrevious from "@/components/button/ButtonPrevious";

import { IApiResponse } from "@/types";
import { ILookupEmail } from "@/types";
import AuthHelper from "@/helpers/authHelper";

const Kemaskini = () => {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { id } = useParams();

  const hasUpdatePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.SELENGGARA.children.PENYELENGGARAAN_UMUM.children
      .EMAIL_QUEUE.label,
    pageAccessEnum.Update
  );

  const isMyLanguage = i18n.language === "my";
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const { control, setValue, handleSubmit } = useForm<FieldValues>({
    defaultValues: {
      notificationTrackerId: id,
      recipientIdentificationNo: "",
      recipientUserGroup: "",
      lastSentEmailAddress: "",
      societyId: "",
      branchId: "",
      subject: "",
      content: "",
      createdDate: "",
    },
  });

  const { fetch: resendEmail, isLoading: isResendingEmail } = useMutation<
    IApiResponse<ILookupEmail>
  >({
    url: `society/lookup/emailQueue/resend`,
    onSuccess: (res) => {
      const resCode = res.data.code;

      if (resCode === 200) navigate("..");
    },
  });

  const { isLoading: isLoadingEmailDetail } = useQuery<
    IApiResponse<ILookupEmail>
  >({
    url: `notification/notificationTracker/${id}`,
    onSuccess: (res) => {
      const resCode = res?.data?.code ?? null;
      const detail = res?.data?.data ?? null;

      if (resCode && resCode === 200) {
        setValue(
          "recipientIdentificationNo",
          detail?.recipientIdentificationNo
        );
        setValue("recipientUserGroup", detail?.recipientUserGroup);
        setValue("lastSentEmailAddress", detail?.recipientEmailAddress);
        setValue("societyId", detail?.societyId);
        setValue("branchId", detail?.branchId);
        setValue("subject", detail?.subject);
        setValue("content", detail?.content);
        setValue("createdDate", detail?.createdDate);
      }
    },
  });

  const onSubmit = (data: FieldValues) => {
    const keysToSkip = ["content", "subject", "createdDate"];
    const payload = omitKeysFromObject(data, keysToSkip);

    resendEmail(payload);
  };

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography sx={{ mb: 2 }} className="title">
            {isMyLanguage ? "Maklumat Emel" : "Email Information"}
          </Typography>

          {isLoadingEmailDetail ? (
            <CustomSkeleton height={50} />
          ) : (
            <Box
              component="form"
              onSubmit={handleSubmit(onSubmit)}
              sx={{ display: "grid" }}
            >
              <FormFieldRow
                label={
                  <Label
                    text={isMyLanguage ? "Email Penerima" : "Recipient Email"}
                  />
                }
                value={
                  <TextFieldController
                    control={control}
                    name="lastSentEmailAddress"
                    disabled
                  />
                }
              />

              <FormFieldRow
                label={
                  <Label text={isMyLanguage ? "Tajuk Email" : "Email Title"} />
                }
                value={
                  <TextFieldController
                    control={control}
                    name="subject"
                    disabled
                  />
                }
              />

              <FormFieldRow
                label={
                  <Label
                    text={isMyLanguage ? "Kandungan Email" : "Email Content"}
                  />
                }
                value={
                  <TextFieldController
                    control={control}
                    name="content"
                    multiline
                    rows={3}
                    disabled
                  />
                }
              />

              <FormFieldRow
                label={<Label text={t("tarikhDihantar")} />}
                value={
                  <TextFieldController
                    control={control}
                    name="createdDate"
                    disabled
                  />
                }
              />

              <Box
                sx={{
                  mt: 2,
                  display: "flex",
                  flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrevious
                  variant="outlined"
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    width: isMobile ? "100%" : "auto",
                  }}
                  onClick={() => navigate(-1)}
                >
                  {t("back")}
                </ButtonPrevious>
                {hasUpdatePermission ? (
                  <ButtonPrimary
                    type="submit"
                    disabled={isResendingEmail}
                    variant="contained"
                    sx={{
                      width: isMobile ? "100%" : "auto",
                      display: "flex",
                      alignItems: "center",
                      gap: "5px",
                    }}
                  >
                    {isResendingEmail && <CircularProgress size={15} />}
                    Hantar Semua
                  </ButtonPrimary>
                ) : null}
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default Kemaskini;
