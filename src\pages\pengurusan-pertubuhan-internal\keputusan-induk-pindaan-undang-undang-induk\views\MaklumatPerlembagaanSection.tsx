import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

import {
  Box,
  Grid,
  Button,
  Typography,
  TextField,
  FormControl,
  Select,
  MenuItem,
  CircularProgress,
} from "@mui/material";

import { DokumenIcon } from "../../../../components/icons";
import { useCustom } from "@refinedev/core";
import { formatDateToDDMMYYYY } from "../../../../helpers/utils";
import { API_URL } from "../../../../api";
import { useState } from "react";
import { MeetingTypeOption } from "../../../../helpers/enums";
import FasalContentKelulusan from "./fasalContentKelulusan";
import dayjs from "dayjs";
import { useQuery } from "@/helpers";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
  "& span": {
    color: "red",
  },
};

const getMeetingLabel = (value: number): string => {
  const meeting = MeetingTypeOption.find(
    (option) => Number(option.value) === Number(value)
  );
  return meeting ? meeting.label : "Unknown Meeting Type";
};

const MaklumatPerlembagaanSection = () => {
  const { t } = useTranslation();
  const [meetingType, setMeetingType] = useState("");
  const [meetingList, setMeetingList] = useState([]);
  const [meetingIdLabelreplace, setMeetingIdLabelreplace] = useState("");
  const [meetingId, setMeetingId] = useState("");
  const [meetingDate, setMeetingDate] = useState("");
  const [fileUrl, setFileUrl] = useState<string>("");
  const [societyId, setSocietId] = useState("");
  const { id, amendmentId } = useParams();
  const decodedId = atob(id || "");

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setMeetingForm((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const [formData, setFormData] = useState({
    organizationGoals: "",
    organizationLevel: "Negeri",
    organizationCategory: "",
    organizationSubCategory: "",
    hasBranch: 0,
    constitutionType: "",
  });

  const [meetingForm, setMeetingForm] = useState({
    meetingId: "",
    meetingDate: "",
    totalAttendees: 0,
    meetingMemberAttendances: [],
    branchId: "",
    branchNo: "",
    city: "",
    closing: "",
    confirmBy: "",
    district: "",
    id: "",
    mattersDiscussed: "",
    meetingAddress: "",
    meetingContent: "",
    meetingMethod: "",
    meetingMinute: "",
    meetingPlace: "",
    meetingPurpose: "",
    meetingTime: "",
    meetingTimeDurationMinutes: "",
    meetingTimeTo: "",
    meetingType: "",
    openingRemarks: "",
    otherMatters: "",
    othersDiscussionRemarks: "",
    closingRemarks: "",
    platformType: "",
    postcode: "",
    providedBy: "",
    societyId: "",
    societyNo: "",
    state: "",
  });

  const { data: amendmentData, isLoading: getAmendmentParamIsLoading } =
    useCustom({
      url: `${API_URL}/society/amendment/getAmendmentByParam`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        query: {
          id: amendmentId,
        },
      },
      queryOptions: {
        enabled: !!amendmentId,
        onSuccess: (responseData) => {
          if (responseData.data.code === 200) {
            const data = responseData?.data?.data?.data?.[0];
            setSocietId(data?.societyId);
            // setFormData((prevState) => ({
            //   ...prevState,
            //   organizationGoals: data?.goal,
            //   organizationLevel: data?.societyLevel,
            //   organizationCategory: data?.categoryCodeJppm,
            //   organizationSubCategory: data?.subCategoryCode,
            //   hasBranch: data?.hasBranch == "0" ? 0 : 1,
            //   constitutionType: data?.constitutionType,
            // }));
            if (data.meetingType) {
              setMeetingType(data?.meetingType);
              setMeetingIdLabelreplace(
                `${data?.meetingType + "@" + data?.meetingDate}`
              );
              setMeetingId(data?.meetingId);
              setMeetingDate(data?.createdDate);
              // setMeetingForm((prevState) => ({
              //   ...prevState,
              //   meetingDate: data?.createdDate,
              //   meetingId: data?.meetingId,
              // }));
            }
          }
        },
      },
    });

  // GET MEETING DETAILS
  const {
    data: meetingData,
    isLoading: isLoadingMeetingData,
    refetch: refetchMeetingData,
  } = useCustom({
    url: `${API_URL}/society/meeting/search`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        pageSize: 100,
        societyId: decodedId,
        meetingDate: meetingDate,
      },
    },
    queryOptions: {
      enabled: !!societyId && !!meetingType && !!meetingDate && !!decodedId,
      onSuccess: (data) => {
        const meetingSearchlist = data?.data?.data?.data || [];
        if (meetingSearchlist.length > 0) {
          const meetingListData = meetingSearchlist.filter(
            (item: any) => item?.meetingType === meetingType
          );
          const finalList = meetingListData.map((meeting: any) => ({
            value: `${meeting.meetingType}@${meeting.meetingDate}`,
            label: `${getMeetingLabel(Number(meeting.meetingType))} (${dayjs(
              meeting.meetingDate
            ).format("DD-MM-YYYY")})`,
          }));
          setMeetingList(finalList);
          fetchMeeting();
        }
      },
    },
  });

  const {
    data: meeting,
    isLoading: isMeetingDocuLoading,
    refetch: fetchMeeting,
  } = useCustom({
    url: `${API_URL}/society/meeting/${meetingId}`,
    method: "get",
    queryOptions: {
      enabled: !!meetingId,
      onSuccess: (data) => {
        const response = data?.data?.data;
        setMeetingForm((prevState) => ({
          ...prevState,
          totalAttendees: response?.totalAttendees,
          meetingMemberAttendances: response?.meetingMemberAttendances,
          ...response,
        }));
      },
    },
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const meetingTypeOptions = MeetingTypeOption.filter((option) =>
    [2, 3, 4].includes(option.value)
  );

  const { refetch: fetchDocuments, isLoading: fetchDocumentsIsLoading } =
    useCustom({
      url: `${API_URL}/document/documentByParam`,
      method: "get",
      queryOptions: {
        enabled: !!meetingId && !!societyId,
        onSuccess: (data) => {
          const fileInfo = data?.data?.data?.[0];
          if (fileInfo) {
            setFileUrl(fileInfo?.url);
          }
        },
      },
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        query: {
          societyId: societyId,
          meetingId:meetingId
        },
      },
    });

  const downloadFile = (filePath: string) => {
    window.open(filePath, "_blank");
  };

  const isApiLoading =
    isMeetingDocuLoading ||
    isLoadingMeetingData ||
    getAmendmentParamIsLoading ||
    fetchDocumentsIsLoading;

  return (
    <>
      {isApiLoading ? (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "300px",
          }}
        >
          <CircularProgress />
        </Box>
      ) : (
        <Box
          sx={{
            borderRadius: "10px",
            padding: "41px 25px 25px",
            border: "0.5px solid #DADADA",
            marginBottom: "13px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("maklumatMesyuarat")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={3}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography sx={labelStyle}>
                  {t("meetingType")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={9}>
              <FormControl fullWidth>
                <Select
                  size="small"
                  value={meetingType}
                  disabled
                  displayEmpty
                  required
                  onChange={(e) => {
                    setMeetingType(e.target.value);
                    setFormData((prevState: any) => ({
                      ...prevState,
                      meetingType: e.target.value,
                    }));
                  }}
                >
                  {MeetingTypeOption.map((item: any, index) => (
                    <MenuItem key={index} value={item.value}>
                      {item.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={3}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography sx={labelStyle}>
                  {t("meetingDate")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={9}>
              <TextField
                size="small"
                fullWidth
                required
                disabled
                name="meetingDate"
                value={formatDateToDDMMYYYY(meetingForm.meetingDate)}
                onChange={handleInputChange}
              />
            </Grid>

            {meetingList.length > 0 ? (
              <>
                <Grid item xs={12} sm={3}>
                  <Typography sx={labelStyle}>
                    {t("meetingList")}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={9}>
                  <FormControl fullWidth>
                    <Select
                      size="small"
                      value={meetingIdLabelreplace}
                      displayEmpty
                      required
                      disabled
                      onChange={(e) => {
                        setMeetingForm((prevState: any) => ({
                          ...prevState,
                          meetingId: e.target.value,
                        }));
                      }}
                    >
                      {meetingList.map((items: any, index) => {
                        return (
                          <MenuItem key={index} value={items.value}>
                            {items.label}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </FormControl>
                </Grid>
              </>
            ) : null}

            {meetingForm.totalAttendees || meetingForm.totalAttendees === 0 ? (
              <>
                <Grid item xs={12} sm={3}>
                  <Typography sx={labelStyle}>
                    {t("bilanganAhliYangHadir")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={9}>
                  <TextField
                    size="small"
                    fullWidth
                    required
                    name="bilanganAhliYangHadir"
                    value={meetingForm.totalAttendees}
                    placeholder="Contoh: 10 orang"
                    onChange={handleInputChange}
                    disabled
                  />
                </Grid>
              </>
            ) : null}

            {meetingForm.meetingMemberAttendances &&
            meetingForm.meetingMemberAttendances.length > 0 ? (
              <>
                <Grid item xs={12} sm={3}>
                  <Typography sx={labelStyle}>
                    {t("bilanganPemegangJawatan")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={9} container spacing={2}>
                  {meetingForm.meetingMemberAttendances?.map(
                    (member: any, index) => (
                      <Grid container item xs={12} spacing={2} key={index}>
                        <Grid item xs={6}>
                          <TextField
                            size="small"
                            fullWidth
                            name="bilanganPemegangJawatan"
                            value={member.name}
                            onChange={handleInputChange}
                            disabled
                          />
                        </Grid>
                        <Grid item xs={6}>
                          <TextField
                            size="small"
                            fullWidth
                            name="bilanganPemegangJawatan"
                            value={member.position}
                            onChange={handleInputChange}
                            disabled
                          />
                        </Grid>
                      </Grid>
                    )
                  )}
                </Grid>
              </>
            ) : null}

            <Grid item xs={12} sm={3}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t("minitMesyuarat")}</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={9}>
              <Button
                variant="outlined"
                startIcon={
                  <DokumenIcon sx={{ color: "var(--primary-color)" }} />
                }
                fullWidth
                onClick={() => downloadFile(fileUrl)}
                sx={{
                  textTransform: "none",
                  borderRadius: "8px",
                  fontWeight: 500,
                  color: "#666666",
                }}
              >
                {t("paparMinitMesyuarat")}
              </Button>
            </Grid>
          </Grid>
        </Box>
      )}

      <Box
        sx={{
          borderRadius: "10px",
          padding: "41px 25px 25px",
          border: "0.5px solid #DADADA",
          marginBottom: "13px",
        }}
      >
        <Typography
          fontSize="14px"
          color="var(--primary-color)"
          fontWeight="500 !important"
          marginBottom="20px"
        >
          {t("checkTheConstitution")}
        </Typography>
        <FasalContentKelulusan societyId={decodedId} />
      </Box>
    </>
  );
};

export default MaklumatPerlembagaanSection;
