import { useTranslation } from "react-i18next";
import {
  Button,
  Typography,
  Dialog,
  Box,
  Divider,
  <PERSON>Field,
  Grid,
} from "@mui/material";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import React, { ReactNode } from "react";
import { formatDate } from "@/helpers";

export interface TrailValues {
  id?: string | number;
  feedbackRecipientId?: string | number;
  feedbackRecipientBranchId?: string | number;
  jppmBranchId?: string | number;
  feedbackAction?: number;
  complaintLevel?: number;
  feedbackContent?: ReactNode;
  feedbackNote?: string;
  status?: number;
  createdDate?: string;
  feedbackRecipientName?: string;
}

export interface ModalSejarahAduanProps<
  ComplaintData extends TrailValues = TrailValues
> {
  title?: string;
  open: boolean;
  onClose: () => void;
  onConfirm?: () => void;
  complaintData: ComplaintData[];
  /**
   * @default true
   */
  showFullViewButton?: boolean;
}

const ModalSejarahAduan = <
  ComplaintData extends TrailValues = TrailValues,
  PropType extends ModalSejarahAduanProps<ComplaintData> = ModalSejarahAduanProps<ComplaintData>
>({
  title: initialTitle,
  open,
  onClose,
  complaintData,
  showFullViewButton = true,
}: PropType) => {
  const { t } = useTranslation();

  const title = initialTitle ?? t("sejarahAduanCadangan");
  console.log(
    "formatDate(complaint.createdDate) ",
    formatDate(complaintData[0]?.createdDate)
  );

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <Box
          sx={{
            px: 2,
            py: 3,
          }}
        >
          <Box
            sx={{
              p: 2,
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
            }}
          >
            <Button
              variant="text"
              startIcon={<ChevronLeftIcon />}
              sx={{
                flex: 1,
                height: "36px",
                textTransform: "none",
                textWrap: "nowrap",
              }}
              onClick={() => onClose()}
            >
              {title}
            </Button>

            {complaintData?.map((complaint, index) => (
              <Grid
                key={complaint?.id}
                sx={{
                  display: "flex",
                  position: "relative",
                  my: 4,
                }}
                container
              >
                <Grid item md={1} xs={1} mt={0.7}>
                  {/* Left timeline circle */}
                  <Box
                    sx={{
                      width: "30px",
                      height: "30px",
                      borderRadius: "50%",
                      backgroundColor: "var(--primary-color)",
                      border: "2px solid #DADADA",
                      zIndex: 10,
                      display: "block",
                    }}
                  />

                  {/* Timeline connector */}
                  {index < complaintData?.length - 1 && (
                    <Box
                      sx={{
                        width: 2,
                        backgroundColor: "#DADADA",
                        position: "absolute",
                        left: 14,
                        top: 37,
                        bottom: "-100%",
                      }}
                    />
                  )}
                </Grid>
                <Grid item md={11} xs={11}>
                  <Box
                    display="flex"
                    alignItems="center"
                    mb={1}
                    sx={{
                      display: "flex",
                      flexDirection: "row",
                      gap: 1,
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        border: "2px solid #5088FF",
                        backgroundColor: "rgba(80, 136, 255, 0.5)",
                        color: "#FFFFFF",
                        borderRadius: "8px",
                        minWidth: 50,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        padding: "8px",
                      }}
                    >
                      {`#${complaint?.id}`}
                    </Typography>

                    <Typography
                      variant="body2"
                      sx={{
                        border: "2px solid #DADADA",
                        color: "#666666",
                        borderRadius: "8px",
                        padding: "8px",
                      }}
                    >
                      {formatDate(complaint.createdDate) === "Invalid Date"
                        ? complaint?.createdDate
                        : formatDate(complaint.createdDate)}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        border: "2px solid #DADADA",
                        color: "#666666",
                        borderRadius: "8px",
                        padding: "8px",
                      }}
                    >
                      {`${t("pegawai")}: ${complaint?.feedbackRecipientName}`}
                    </Typography>
                  </Box>

                  {/* Updates */}
                  {React.isValidElement(complaint?.feedbackContent) ? (
                    <Box
                      sx={{
                        border: "2px solid #DADADA",
                        borderRadius: "8px",
                        padding: 2,
                      }}
                    >
                      {complaint.feedbackContent}
                    </Box>
                  ) : complaint?.feedbackNote ? (
                    <Box
                      sx={{
                        maxHeight: 200,
                        overflow: "scroll",
                        border: "2px solid #DADADA",
                        borderRadius: "8px",
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          padding: 1.5,
                          fontSize: 16,
                        }}
                      >
                        {complaint.feedbackNote}
                      </Typography>
                    </Box>
                  ) : null}
                </Grid>
              </Grid>
            ))}

            {showFullViewButton && (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                }}
              >
                <Button
                  variant="text"
                  sx={{
                    flex: 1,
                    height: "36px",
                    textTransform: "none",
                    textWrap: "nowrap",
                    textDecoration: "underline",
                  }}
                >
                  {t("lihatpaparanpenuh")}
                </Button>
              </Box>
            )}
          </Box>
        </Box>
      </Dialog>
    </>
  );
};

export default ModalSejarahAduan;
