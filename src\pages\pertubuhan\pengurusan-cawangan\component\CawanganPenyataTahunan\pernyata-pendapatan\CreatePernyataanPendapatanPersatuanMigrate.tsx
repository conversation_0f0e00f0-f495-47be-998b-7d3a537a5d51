import React from "react";
import type { TFunction } from "i18next";
import Box from "@mui/material/Box";
import { Grid, TextField, Typography } from "@mui/material";
import {
  Control,
  Controller,
  FieldValues,
  UseFormGetValues,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { formatAndValidateNumber } from "@/helpers";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

export const CreatePernyataanPendapatanPersatuanMigrate: React.FC<{
  t: TFunction;
  control: Control<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  checked: boolean;
  isDisabled: boolean;
}> = ({ t, watch, setValue, getValues, control, checked, isDisabled }) => {
  function formatNumber(num: string) {
    return Number(num.replace(/,/g, ""));
  }
  const renderInputGroup = (
    title: string,
    items: { label: string; variable: string }[]
  ) => (
    <>
      <Box
        sx={{
          background: "white",
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Grid container spacing={2} sx={{ display: "flex" }}>
          <Grid item xs={4}>
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontSize: "14px",
                fontWeight: "500 !important",
              }}
            >
              {t(title)}
            </Typography>
          </Grid>
          <Grid item xs={12}>
            {items.map((item, index) => {
              return (
                <Grid container spacing={2} sx={{ mb: 1 }}>
                  <Grid item xs={4}>
                    <Typography sx={labelStyle}>
                      {t(item.label)} (RM)
                    </Typography>
                  </Grid>
                  <Grid item xs={8}>
                    <Controller
                      name={item.variable}
                      key={index}
                      control={control}
                      defaultValue={getValues(item.variable)}
                      render={({ field }) => {
                        return (
                          <TextField
                            size="small"
                            fullWidth
                            disabled={checked || isDisabled}
                            value={getValues(item.variable)}
                            placeholder="0"
                            onChange={(e) => {
                              const formattedValue = formatAndValidateNumber(
                                e.target.value
                              );

                              const currentValue = formatNumber(
                                getValues(item.variable)
                              );
                              const newValue = formatNumber(e.target.value);
                              const totalIncome = formatNumber(
                                getValues("totalIncome")
                              );

                              const updatedTotalIncome =
                                totalIncome - currentValue + newValue;

                              setValue(
                                "totalIncome",
                                formatAndValidateNumber(
                                  updatedTotalIncome.toString()
                                )
                              );

                              // console.log(typeof currentValue);
                              if (formattedValue !== null) {
                                field.onChange(formattedValue);
                                // setValue("totalIncome", formattedValue);
                              }
                            }}
                          />
                        );
                      }}
                    />{" "}
                  </Grid>
                </Grid>
              );
            })}
          </Grid>
        </Grid>
      </Box>
    </>
  );

  return (
    <>
      {renderInputGroup("operatingIncomeSection", [
        { label: "entranceFeeIncome", variable: "feeIncome" },
        { label: "membershipFeeIncome", variable: "feeIncomeMember" },
        { label: "donationIncome", variable: "donationIncome" },
        { label: "otherOperatingIncome", variable: "othersIncome" },
      ])}

      {renderInputGroup("fundraisingActivitiesIncomeSection", [
        { label: "dinnerEventIncome", variable: "foodRevenue" },
        { label: "charitySaleIncome", variable: "bookRevenue" },
        { label: "servicesIncome", variable: "serviceRevenue" },
        { label: "otherFundraisingIncome", variable: "otherRevenue" },
      ])}

      {renderInputGroup("investmentIncomeSection", [
        { label: "rentalIncome", variable: "rentalInvestment" },
        { label: "dividendIncome", variable: "dividendInvestment" },
        { label: "fixedDepositInterestIncome", variable: "interestInvestment" },
        { label: "propertyProfitIncome", variable: "propertyInvestment" },
        { label: "otherInvestmentIncome", variable: "otherInvestment" },
      ])}

      {renderInputGroup("grantsSection", [
        { label: "governmentAgencyGrant", variable: "govGrant" },
        { label: "privateAgencyGrant", variable: "privateGrant" },
        { label: "individualGrant", variable: "individualGrant" },
        { label: "otherGrants", variable: "otherGrant" },
      ])}

      {renderInputGroup("lainLainPendapatan", [
        { label: "lainLainPendapatan", variable: "otherIncome" },
      ])}

      {renderInputGroup("", [
        { label: "totalIncome", variable: "totalIncome" },
      ])}
    </>
  );
};

export default CreatePernyataanPendapatanPersatuanMigrate;
