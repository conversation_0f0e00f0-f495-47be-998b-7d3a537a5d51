import { Box, Grid, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import Input from '../../../../components/input/Input'
import { useTranslation } from 'react-i18next';
import { AddressList, Meeting } from '../interface';
import useQuery from '../../../../helpers/hooks/useQuery';
import { MALAYSIA, MeetingMethods } from '../../../../helpers/enums';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ile<PERSON>ayer, useMap } from 'react-leaflet';

type props = {
  sectionStyle: any,
  meeting?: Meeting
  businessCoords: [number, number]
};

const RecenterAutomatically = () => {
    const map = useMap();
    const center = map.getCenter();

    useEffect(() => {
      map.setView(center);
    }, [center]);

    return null;
  };

const AlamatTempatMesyuarat: React.FC<props> = ({ sectionStyle, meeting, businessCoords }) => {
  const { t } = useTranslation();
  const [addressList, setAddressList] = useState<AddressList[]>([])

  useQuery({
    url: `society/admin/address/list`,
    onSuccess: (data) => {
      const list = data?.data?.data || [];
      setAddressList(list)
    },
  });

  if(meeting?.meetingMethod == MeetingMethods.ATAS_TALIAN){
    return
  }

  return (
    <Box sx={{
      background: "white",
      border: "1px solid rgba(0, 0, 0, 0.12)",
      borderRadius: "14px",
      p: 3,
      mb: 2,
    }}>
      <Typography variant="subtitle1" sx={sectionStyle}>
        {t("alamatTempatMesyuarat")}
      </Typography>
      <Grid item xs={12}>
        {/** @ts-expect-error */}
        {meeting?.meetingMethod == MeetingMethods.ATAS_TALIAN ? null : (
          <>
            <Input
              label={t("namaTempatMesyuarat")}
              value={meeting?.meetingPlace}
              disabled
            />
            <Grid
              container
              spacing={2}
              alignItems="flex-start"
              sx={{ mb: 1 }}
            >
              <Grid item xs={12} sm={4}>
                <Typography
                  variant="body1"
                  sx={{
                    color: "#666666",
                    fontWeight: "400 !important",
                    fontSize: "14px",
                  }}
                >
                  {t("meetingLocation")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <MapContainer
                  center={businessCoords}
                  zoom={13}
                  style={{
                    height: "150px",
                    width: "100%",
                    borderRadius: "8px",
                  }}
                >
                  <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                  <Marker position={businessCoords} />
                  <RecenterAutomatically />
                </MapContainer>
              </Grid>
            </Grid>
          </>
        )}
        <Input label={t("alamatTempatMesyuarat")} disabled value={meeting?.meetingAddress} />
        <Input
          label={t("negeri")}
          type='select'
          value={meeting ? parseInt(meeting?.state) : 0}
          disabled
          options={addressList
            .filter((item: any) => item.pid === MALAYSIA)
            .map((item: any) => (
              { value: item.id, label: item.name }
            ))}
        />
        <Input
          label={t("daerah")}
          type='select'
          value={meeting ? parseInt(meeting?.district) : 0}
          disabled
          options={addressList
            .map((item: any) => (
              { value: item.id, label: item.name }
            ))}
        />
        <Input label={t("bandar")} disabled value={meeting?.city} />
        <Input label={t("poskod")} disabled value={meeting?.postcode} />
      </Grid>
    </Box>
  )
}

export default AlamatTempatMesyuarat
