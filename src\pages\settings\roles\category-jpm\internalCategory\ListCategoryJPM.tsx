import {
  <PERSON>,
  <PERSON><PERSON><PERSON>utt<PERSON>,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useNavigate } from "react-router-dom";
import Input from "../../../../../components/input/Input";
import { useCallback, useEffect, useState } from "react";
import {
  // ListCategoryRoleStatus,
  NewSocietyBranchStatus,
} from "../../../../../helpers/enums";
import ButtonPrevious from "../../../../../components/button/ButtonPrevious";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../../../redux/store";
import { fetchRolesData } from "../../../../../redux/APIcalls/rolesThunks";
import { setRolesSearchParams } from "../../../../../redux/rolesDataReducer";
import { EditIcon } from "@/components/icons";
import DataTable from "@/components/datatable";
import { fetchPermissionsListData } from "@/redux/APIcalls/permissionsListThunks";

interface FormValues {
  search: string | null;
  // status: string | null;
}

interface PageAccessByUserRoleResponseBodyGet {
  /**
   * 2025-03-18 02:42:13
   */
  createdDate: string;
  createdBy: number;
  /**
   * "2025-03-18 02:47:14"
   */
  modifiedDate: string;
  modifiedBy: number;
  id: string | number;
  pid: string | number;
  level: number;
  name: string;
  userRole: string;
  oldRoleId: string | number | null;
  description: string;
  status: number;
}

function ListCategoryJPM() {
  const { t } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const [paginationModel, setPaginationModel] = useState({
    page: 1,
    size: 5,
  });

  const dispatch: AppDispatch = useDispatch();

  const { data, loading, searchParams } = useSelector(
    (state: RootState) => state.rolesData
  );

  const totalList = data?.data?.total ?? 0;
  const rowData = data?.data?.data ?? [];

  const handleChangePage = (newPage: number) =>
    setPaginationModel((prev) => ({ ...prev, page: newPage }));
  const handlePageSizeChange = (newSize: number) =>
    setPaginationModel((prev) => ({ ...prev, size: newSize, page: 1 }));

  const handleReset = () => {
    setFormValues({
      search: null,
      // , status: null
    });
    setPaginationModel((prev) => ({ ...prev, page: 1 }));
    dispatch(setRolesSearchParams(null));
  };

  useEffect(() => {
    dispatch(
      fetchRolesData({
        searchParams,
        size: paginationModel.size,
        page: paginationModel.page,
      })
    );
  }, [searchParams, paginationModel, dispatch]);

  const handleSearchChange = (params: Record<string, any>) => {
    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(([_, v]) => v !== null && v !== undefined)
    );
    dispatch(setRolesSearchParams({ ...searchParams, ...filteredParams }));
  };

  const [formValues, setFormValues] = useState<FormValues>({
    search: "",
    // status: null,
  });

  const navigate = useNavigate();

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  }, []);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setPaginationModel((prev) => ({ ...prev, page: 1 }));
    handleSearchChange({
      searchQuery: formValues.search,
      // status: formValues.status,
    });
  };

  const active = [NewSocietyBranchStatus.AKTIF_1, 1];
  const columns = [
    {
      field: "role",
      headerName: t("roleName"),
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      align: "left",
      headerAlign: "left",
    },
    {
      field: "userCount",
      headerName: t("bilPengguna"),
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      headerAlign: "center",
      align: "center",
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      headerAlign: "center",
      align: "center",
      renderCell: (params: any) => {
        const isActive = active.includes(params?.row?.status);
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Typography
              className="status-pertubuhan-text"
              sx={{
                backgroundColor: "#fff",
                border: `2px solid ${
                  isActive ? "var(--success)" : "var(--error)"
                }`,
              }}
            >
              {isActive ? t("active") : t("inactive")}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: "actions",
      headerName: t("action"),
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      align: "right",
      headerAlign: "right",
      renderCell: (params: any) => {
        const row = params?.row;

        return (
          <IconButton
            onClick={() => {
              navigate(
                `../create-category-pengurusan-peranan-jpm?role=${encodeURIComponent(
                  row.role
                )}&id=${row.id}`
              );
            }}
          >
            <EditIcon
              sx={{
                // mt: "2px",
                fontSize: "2rem",
                width: "1rem",
                height: "1rem",
                color: "var(--primary-color)",
              }}
            />
          </IconButton>
        );
      },
    },
  ];

  const permissionsList = useSelector<
    RootState,
    PageAccessByUserRoleResponseBodyGet[]
  >((state) => state.permissionsListData.data);

  useEffect(() => {
    if (permissionsList?.length < 1) {
      dispatch(fetchPermissionsListData());
    }
  }, []);

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography sx={{ mb: 2 }} className="title">
            {t("search")}
          </Typography>

          <Box
            component="form"
            onSubmit={handleSubmit}
            sx={{ display: "grid" }}
          >
            <Input
              name="search"
              label={t("search")}
              value={formValues?.search ? formValues.search : ""}
              onChange={handleChange}
              placeholder={t("pleaseEnterName")}
            />
            {/*  <Input
              value={formValues.status === null ? "" : formValues?.status}
              name="status"
              onChange={handleChange}
              label={t("status")}
              options={translatedList}
              type="select"
            /> */}

            <Box
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonPrevious
                onClick={() => handleReset()}
                variant="outlined"
                sx={{
                  bgcolor: "white",
                  "&:hover": { bgcolor: "white" },
                  width: isMobile ? "100%" : "auto",
                }}
              >
                {t("previous")}
              </ButtonPrevious>
              <ButtonPrimary
                type="submit"
                variant="contained"
                sx={{
                  width: isMobile ? "100%" : "auto",
                }}
              >
                {t("cari")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Box>
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography className="title">{t("JPPMUserCategoryList")}</Typography>
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
            }}
            onClick={() => {
              navigate("../create-category-pengurusan-peranan-jpm");
            }}
          >
            <ButtonOutline>{t("addCategory")}</ButtonOutline>
          </Box>
          <Box
            sx={{
              borderRadius: "14px",
              mt: 3,
            }}
          >
            {data?.data && (
              <DataTable
                columns={columns as any}
                rows={rowData}
                page={paginationModel.page}
                rowsPerPage={paginationModel.size}
                totalCount={totalList}
                onPageChange={handleChangePage}
                isLoading={loading}
                pagination={true}
                labelRowsPerPage={t("rowsPerPageListCategoryJPM")}
                onPageSizeChange={handlePageSizeChange}
              />
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
}

export default ListCategoryJPM;
