import { useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Checkbox from "@mui/material/Checkbox";
import type { SelectChangeEvent } from "@mui/material/Select";
import InputLabel from "@mui/material/InputLabel";
import { useTranslation } from "react-i18next";
import { ButtonPrimary } from "../../../../../components/button";
import { Select, Option } from "../../../../../components/input";
import { Fade, Grid } from "@mui/material";
import { useCustom } from "@refinedev/core";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { API_URL } from "../../../../../api";
import { ApplicationStatus, PaymentItemCode } from "../../../../../helpers/enums";
import { DialogConfirmation } from "@/components";
import { useMutation } from "@/helpers";
import usePaymentService from "@/helpers/hooks/usePaymentService";
import { useDispatch } from "react-redux";
import { setCalculatedPayment } from "@/redux/paymentReducer";

export const ListBayaran = () => {
  const [paymentMethod, setPaymentMethod] = useState("");
  const { t } = useTranslation();
  const [dialogOpen, setDialogOpen] = useState(false);
  const navigate = useNavigate();
  const [isChecked, setIsChecked] = useState(false);
  const { id: societyId } = useParams();
  const location = useLocation();
  const publicOfficerId = location.state?.publicOfficerId;
  const propertyOfficerId = location.state?.propertyOfficerId;
  const publicOfficerName = location.state?.publicOfficerName;

  // Payment service hooks
  const { calculatePayment, processPayment } = usePaymentService();
  const dispatch = useDispatch();

  const handleChange = (event: SelectChangeEvent) => {
    setPaymentMethod(event.target.value as string);
  };

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const { fetchAsync: editOfficer, isLoading: isLoadingEditOfficer } = useMutation({
    method: "put",
    generateSuccessMessageFromPayload: () => {
      return t(
        `${
          publicOfficerId ? "public" : "property"
        }OfficerCreatedSuccessfully`
      );
    },
    async onSuccess(data: any) {
      const createdDate = data?.data?.data?.createdDate;

      // Determine the correct item code based on payment method and officer type
      let itemCode: string;
      if (paymentMethod === "online") {
        itemCode = publicOfficerId
          ? PaymentItemCode.PENDAFTARAN_PEGAWAI_AWAM_ONLINE
          : PaymentItemCode.PENDAFTARAN_PEGAWAI_HARTA_ONLINE;
      } else {
        itemCode = publicOfficerId
          ? PaymentItemCode.PENDAFTARAN_PEGAWAI_AWAM_KAUNTER
          : PaymentItemCode.PENDAFTARAN_PEGAWAI_HARTA_KAUNTER;
      }

      // Call calculate API first
      const calculateRequest = {
        items: [
          {
            itemCode: itemCode,
            quantity: 1
          }
        ]
      };

      try {
        const calculateResponse = await calculatePayment(calculateRequest);

        if (calculateResponse.data) {
          // Store calculated payment in Redux
          dispatch(setCalculatedPayment(calculateResponse.data));

          if (paymentMethod === "online") {
            const state = {
              createdDate,
              societyId: societyId,
              ...(propertyOfficerId && { propertyOfficerId }),
              ...(publicOfficerId && { publicOfficerId }),
              ...(publicOfficerName && { publicOfficerName }),
            };
            navigate(`online?id=${societyId}`, {
              state: state,
            });
          } else if (paymentMethod === "kaunter") {
            // Use processPayment API instead of makePayment
            const processPaymentRequest = {
              societyId: societyId ? parseInt(societyId) : undefined,
              ...(propertyOfficerId && { propertyOfficerId }),
              ...(publicOfficerId && { publicOfficerId }),
              amount: calculateResponse.data.totalAmount,
              email: "",
              signature: calculateResponse.data.signature
            };

            const processResponse = await processPayment(processPaymentRequest);

            if (processResponse.data) {
              navigate(`kaunter?id=${societyId}`, {
                state: {
                  createdDate,
                  societyId: societyId,
                  ...(propertyOfficerId && { propertyOfficerId }),
                  ...(publicOfficerId && { publicOfficerId }),
                  ...(publicOfficerName && { publicOfficerName }),
                },
              });
            }
          }
        }
      } catch (error) {
        console.error('Payment calculation or processing failed:', error);
        // Handle error appropriately
      }
    }
  });

  const editSociety = async () => {
    if (societyId) {
      const data = {
        applicationStatusCode:
          paymentMethod === "kaunter"
            ? ApplicationStatus.MENUNGGU_BAYARAN_KAUNTER
            : ApplicationStatus.MENUNGGU_BAYARAN_ONLINE,
      };
      await editOfficer(
        data,
        () =>
          `society/${publicOfficerId ? `public_officer/${publicOfficerId}` : `property_officer/${propertyOfficerId}`}`,
      );
    }
  };

  const { data: paymentStatus } = useCustom({
    url: `${API_URL}/society/admin/integration/payment/status`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const onlinePaymentEnabled: boolean =
    paymentStatus?.data?.data?.payment?.toLowerCase() == "enabled";
  const alert: boolean = paymentStatus?.data?.data?.alert;
  const isSubmitting = isLoadingEditOfficer;

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsChecked(event.target.checked);
  };

  const handleSubmit = () => {
    handleOpenDialog();
  };

  return (
    <Box sx={{ display: "flex" }}>
      <Box sx={{ width: "100%" }}>
        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3,
              width: "100%",
            }}
          >
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: "500 !important",
                  }}
                >
                  {t("payment")}
                </Typography>
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color: "#666666",
                  fontFamily: "Poppins",
                  fontSize: 14,
                  fontWeight: "400 !important",
                  lineHeight: "21px",
                  textAlign: "left",
                  textUnderlinePosition: "from-font",
                  textDecorationSkipInk: "none",
                  mb: 2,
                }}
              >
                {t("agreementText")}
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "flex-start",
                  gap: 1,
                }}
              >
                <Checkbox
                  id="akuan-setuju-terima"
                  checked={isChecked}
                  onChange={handleCheckboxChange}
                  sx={{
                    color: "#00A7A7",
                    "&.Mui-checked": {
                      color: "#00A7A7",
                    },
                    padding: "0",
                  }}
                />
                <InputLabel
                  htmlFor="akuan-setuju-terima"
                  required
                  sx={{
                    color: "#333333",
                    fontSize: 14,
                    fontWeight: 400,
                    lineHeight: 1.4,
                    "& .MuiFormLabel-asterisk": {
                      color: "var(--error)",
                    },
                  }}
                >
                  {t("agreementAcceptance")}
                </InputLabel>
              </Box>
            </Box>

            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 3,
              }}
            >
              <Box sx={{ mb: 2 }}>
                <Box sx={{ mb: 1 }}>
                  <Typography
                    variant="h6"
                    component="h2"
                    sx={{
                      color: "#00A7A7",
                      fontSize: 16,
                      fontWeight: "500 !important",
                    }}
                  >
                    {t("paymentMethod")}
                  </Typography>
                </Box>
                <Grid
                  container
                  rowSpacing={1}
                  columnSpacing={{ xs: 1, sm: 2, md: 3 }}
                >
                  <Grid item xs={4} />
                  <Grid item xs={8}>
                    <Typography
                      sx={{
                        fontFamily: "Poppins, sans-serif",
                        fontSize: "12px",
                        fontWeight: 500,
                        lineHeight: "14px",
                        color: "#FF0000",
                        marginLeft: "15px",
                      }}
                    >
                      {alert}
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <InputLabel
                      htmlFor="cara-pembayaran"
                      required
                      sx={{
                        color: "#333333",
                        fontSize: 14,
                        fontWeight: 400,
                        minWidth: "150px",
                        "& .MuiFormLabel-asterisk": {
                          color: "var(--error)",
                        },
                      }}
                    >
                      {t("paymentMethod")}
                    </InputLabel>
                  </Grid>
                  <Grid item xs={8}>
                    <Select
                      value={paymentMethod}
                      onChange={handleChange}
                      id="cara-pembayaran"
                      t={t}
                      sx={{
                        width: "100%",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#E5E5E5",
                          borderRadius: 1,
                        },
                      }}
                    >
                      <Option value="kaunter">{t("pembayaranKaunter")}</Option>
                      <Option value="online" disabled={!onlinePaymentEnabled}>
                        {t("pembayaranOnline")}
                      </Option>
                    </Select>
                  </Grid>
                </Grid>
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color: "#666666",
                  fontSize: 12,
                  marginTop: 10,
                  textAlign: "center",
                }}
              >
                {t("paymentNote")}
              </Typography>
            </Box>

            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              <ButtonPrimary
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  borderRadius: 1,
                  textTransform: "none",
                }}
                disabled={!isChecked || !paymentMethod || isSubmitting}
                onClick={handleSubmit}
              >
                {t("hantar")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>

        <DialogConfirmation
          open={dialogOpen}
          onClose={handleCloseDialog}
          onConfirmationText={t("confirmSubmitApplication")}
          isMutating={isSubmitting}
          onAction={async () => {
            try {
              await editSociety();
            } finally {
              handleCloseDialog();
            }
          }}
        />
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }} />
    </Box>
  );
};

export default ListBayaran;
