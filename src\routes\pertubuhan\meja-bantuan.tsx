import { Route, Outlet, Navigate } from "react-router-dom";
import { RouteGuard } from "../../components/RouteGuard";
import { registerRoutes } from "../../helpers/routeDetector";
import MejaBantuanLayout from "../../pages/meja-bantuan/MejaBantuanLayout";
// import PembayaranKaunter from "../../pages/meja-bantuan/perkhidmatan-kaunter/pembayaran-kaunter";
// import Kemaskini from "../../pages/meja-bantuan/perkhidmatan-kaunter/pembayaran-kaunter/Kemaskini";
// import RekodPembayaran from "../../pages/meja-bantuan/perkhidmatan-kaunter/rekod-pembayaran";
import AduanCadangan from "../../pages/meja-bantuan/aduan-makluman/aduan-cadangan";
import AduanCadanganKemaskini from "../../pages/meja-bantuan/aduan-makluman/aduan-cadangan/Kemaskini";
import KepuasanPelanggan from "../../pages/meja-bantuan/aduan-makluman/kepuasan-pelanggan";
import MaklumanFAQ from "../../pages/meja-bantuan/aduan-makluman/makluman-faq";
import MaklumanFAQKemaskini from "../../pages/meja-bantuan/aduan-makluman/makluman-faq/Kemaskini";
import AuthHelper from "@/helpers/authHelper";
import { NEW_PermissionNames, pageAccessEnum } from "@/helpers";
import { TabGuard } from "@/layouts/tabGuard";

// Layout component to wrap all meja bantuan routes with protection
const MejaBantuanGuardedLayout = () => {
  if (!AuthHelper.hasAuthority([NEW_PermissionNames.MEJA_BANTUAN.label])) {
    return <Navigate to="/forbidden" replace />;
  }
  return (
    <RouteGuard
      autoUpdatePortal={true}
      showDebugInfo={process.env.NODE_ENV === "development"}
    >
      <MejaBantuanLayout>
        <Outlet />
      </MejaBantuanLayout>
    </RouteGuard>
  );
};

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example route registrations (uncomment and modify as needed):
  "/meja-bantuan": "internal",
  "/meja-bantuan/aduan-makluman": "internal",
  "/meja-bantuan/aduan-makluman/aduan-cadangan": "internal",
  "/meja-bantuan/aduan-makluman/aduan-cadangan/kemaskini/:id": "internal",
  "/meja-bantuan/aduan-makluman/kepuasan-pelanggan": "internal",
  "/meja-bantuan/aduan-makluman/faq-makluman": "internal",
  "/meja-bantuan/aduan-makluman/faq-makluman/kemaskini": "internal",
  // Add your route registrations here
});

export const mejaBantuan = {
  routes: (
    <Route element={<MejaBantuanGuardedLayout />}>
      <Route path="meja-bantuan">
        {/* Redirect /meja-bantuan to /meja-bantuan/aduan-makluman/aduan-cadangan */}
        <Route
          index
          element={<Navigate to="aduan-makluman/aduan-cadangan" replace />}
        />

        <Route path="aduan-makluman">
          {/* Redirect /meja-bantuan/aduan-makluman to /meja-bantuan/aduan-makluman/aduan-cadangan */}
          <Route index element={<Navigate to="aduan-cadangan" replace />} />

          <Route
            index
            element={
              <TabGuard
                permissionList={[
                  NEW_PermissionNames.MEJA_BANTUAN.children.ADUAN_MAKLUM_BALAS
                    .children.ADUAN_CADANGAN.label,
                ]}
              >
                <AduanCadangan />
              </TabGuard>
            }
          />
          <Route
            path="aduan-cadangan"
            element={
              <TabGuard
                permissionName={
                  NEW_PermissionNames.MEJA_BANTUAN.children.ADUAN_MAKLUM_BALAS
                    .children.ADUAN_CADANGAN.label
                }
                withAccess
                permissionType={pageAccessEnum.Read}
              >
                <AduanCadangan />
              </TabGuard>
            }
          />
          <Route
            path="aduan-cadangan/kemaskini/:id"
            element={
              <TabGuard
                permissionName={
                  NEW_PermissionNames.MEJA_BANTUAN.children.ADUAN_MAKLUM_BALAS
                    .children.ADUAN_CADANGAN.label
                }
                withAccess
                permissionType={pageAccessEnum.Read}
              >
                <AduanCadanganKemaskini />
              </TabGuard>
            }
          />

          <Route
            path="kepuasan-pelanggan"
            element={
              <TabGuard
                permissionName={
                  NEW_PermissionNames.MEJA_BANTUAN.children.ADUAN_MAKLUM_BALAS
                    .children.KEPUASAN_PELANGGAN.label
                }
                withAccess
                permissionType={pageAccessEnum.Read}
              >
                <KepuasanPelanggan />
              </TabGuard>
            }
          />

          <Route
            path="faq-makluman"
            element={
              <TabGuard
                permissionName={
                  NEW_PermissionNames.MEJA_BANTUAN.children.ADUAN_MAKLUM_BALAS
                    .children.FAQ_MAKLUMAN.label
                }
                withAccess
                permissionType={pageAccessEnum.Read}
              >
                <MaklumanFAQ />
              </TabGuard>
            }
          />
          <Route
            path="faq-makluman/kemaskini"
            element={
              <TabGuard
                permissionName={
                  NEW_PermissionNames.MEJA_BANTUAN.children.ADUAN_MAKLUM_BALAS
                    .children.FAQ_MAKLUMAN.label
                }
                withAccess
                permissionType={pageAccessEnum.Read}
              >
                <MaklumanFAQKemaskini />
              </TabGuard>
            }
          />
        </Route>
      </Route>
    </Route>
  ),
};
