import { Route, Outlet, Navigate } from "react-router-dom";
import { RouteGuard } from "../../components/RouteGuard";
import { registerRoutes } from "../../helpers/routeDetector";
import MejaBantuanLayout from "../../pages/meja-bantuan/MejaBantuanLayout";
// import PembayaranKaunter from "../../pages/meja-bantuan/perkhidmatan-kaunter/pembayaran-kaunter";
// import Kemaskini from "../../pages/meja-bantuan/perkhidmatan-kaunter/pembayaran-kaunter/Kemaskini";
// import RekodPembayaran from "../../pages/meja-bantuan/perkhidmatan-kaunter/rekod-pembayaran";
import AduanCadangan from "../../pages/meja-bantuan/aduan-makluman/aduan-cadangan";
import AduanCadanganKemaskini from "../../pages/meja-bantuan/aduan-makluman/aduan-cadangan/Kemaskini";
import KepuasanPelanggan from "../../pages/meja-bantuan/aduan-makluman/kepuasan-pelanggan";
import MaklumanFAQ from "../../pages/meja-bantuan/aduan-makluman/makluman-faq";
import MaklumanFAQKemaskini from "../../pages/meja-bantuan/aduan-makluman/makluman-faq/Kemaskini";

// Layout component to wrap all meja bantuan routes with protection
const MejaBantuanGuardedLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <MejaBantuanLayout>
      <Outlet />
    </MejaBantuanLayout>
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example route registrations (uncomment and modify as needed):
  // '/meja-bantuan': 'internal',
  // '/meja-bantuan/aduan-makluman': 'internal',
  // '/meja-bantuan/aduan-makluman/aduan-cadangan': 'internal',
  // '/meja-bantuan/aduan-makluman/kepuasan-pelanggan': 'internal',
  // '/meja-bantuan/aduan-makluman/faq-makluman': 'internal',
  // Add your route registrations here
});

export const mejaBantuan = {
  routes: (
    <Route element={<MejaBantuanGuardedLayout />}>
      <Route path="meja-bantuan">
        {/* Redirect /meja-bantuan to /meja-bantuan/aduan-makluman/aduan-cadangan */}
        <Route
          index
          element={<Navigate to="aduan-makluman/aduan-cadangan" replace />}
        />

        <Route path="aduan-makluman">
          {/* Redirect /meja-bantuan/aduan-makluman to /meja-bantuan/aduan-makluman/aduan-cadangan */}
          <Route index element={<Navigate to="aduan-cadangan" replace />} />

          <Route index element={<AduanCadangan />} />
          <Route path="aduan-cadangan" element={<AduanCadangan />} />
          <Route
            path="aduan-cadangan/kemaskini/:id"
            element={<AduanCadanganKemaskini />}
          />

          <Route path="kepuasan-pelanggan" element={<KepuasanPelanggan />} />

          <Route path="faq-makluman" element={<MaklumanFAQ />} />
          <Route
            path="faq-makluman/kemaskini"
            element={<MaklumanFAQKemaskini />}
          />
        </Route>

        {/* <Route path="perkhidmatan">
          <Route index element={<PembayaranKaunter />} />
          <Route path="pembayaran-kaunter" element={<PembayaranKaunter />} />
          <Route path="pembayaran-kaunter/kemaskini" element={<Kemaskini />} />

          <Route path="rekod-pembayaran" element={<RekodPembayaran />} />
        </Route> */}
      </Route>
    </Route>
  ),
};
