import { Box } from "@mui/material";
import { useTranslation } from "react-i18next";
import WrapContent from "../../View/WrapContent";
import CawanganTab from "./CawanganTab";
import PindaanTab from "./PindaanTab";
import PemegangJawatanTab from "./PemegangJawatanTab";
import AhliBukanWarganegaraTab from "./AhliBukanWarganegaraTab";
import SijilMigrasiCawanganTab from "./SijilMigrasiCawanganTab";
import PenyataTahunanTab from "./PenyataTahunanTab";
import { useTabContext } from "@/contexts/tabProvider";
import AuthHelper from "@/helpers/authHelper";
import { NEW_PermissionNames, pageAccessEnum } from "@/helpers";
import ForbiddenPage from "@/pages/forbidden";
import { MaklumatBoxes } from "../maklumatSelectionTabs";

function cawangan() {
  const hasCawanganPagePermission = AuthHelper.hasAuthority([
    NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
      .CAWANGAN.label,
  ]);

  if (!hasCawanganPagePermission) {
    return <ForbiddenPage internal />;
  } else {
    const { activeTab, setActiveTab } = useTabContext();

    const { t } = useTranslation();

    const tab = [
      {
        name: t("cawangan"),
        slug: "cawangan",
        permissionNames:
          NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
            .CAWANGAN.children.CAWANGAN_DETAIL.label,
      },
      {
        name: t("changeOfBranchSecretary"),
        slug: "penukaran-setiausaha-cawangan",
        permissionNames:
          NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
            .CAWANGAN.children.SIJIL_MIGRASI_CAWANGAN.label,
      },
      {
        name: t("senaraiPindaanNamadanAlamatCawangan"),
        slug: "pindaan",
        permissionNames:
          NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
            .CAWANGAN.children.SENARAI_PINDAAN_NAMA_DAN_ALAMAT_CAWANGAN.label,
      },
      {
        name: t("pemegangJawatan"),
        slug: "ajk",
        permissionNames:
          NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
            .CAWANGAN.children.PEMEGANG_JAWATAN_CAWANGAN.label,
      },
      {
        name: t("ahliBukanWarganegara"),
        slug: "ahli-bukan-warganegara",
        permissionNames:
          NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
            .CAWANGAN.children.AHLI_BUKAN_WARGANEGARA_CAWANGAN.label,
      },
      {
        name: t("annualStatement"),
        slug: "penyata-tahunan",
        permissionNames:
          NEW_PermissionNames.PERTUBUHAN.children.MAKLUMAT_PERTUBUHAN.children
            .CAWANGAN.children.PENYATA_TAHUNAN_CAWANGAN.label,
      },
    ];

    const enrichedTabs = tab.map((item) => ({
      ...item,
      hasPermission: AuthHelper.hasPageAccess(
        item.permissionNames,
        pageAccessEnum.Read
      ),
    }));

    const renderTab = (tabs: any) => {
      switch (activeTab) {
        case "cawangan":
          return <CawanganTab disabled={!tabs[0].hasPermission} />;
        case "pindaan":
          return <PindaanTab disabled={!tabs[0].hasPermission} />;
        case "ajk":
          return <PemegangJawatanTab disabled={!tabs[0].hasPermission} />;
        case "ahli-bukan-warganegara":
          return <AhliBukanWarganegaraTab disabled={!tabs[0].hasPermission} />;
        case "penukaran-setiausaha-cawangan":
          return <SijilMigrasiCawanganTab disabled={!tabs[0].hasPermission} />;
        case "penyata-tahunan":
          return <PenyataTahunanTab disabled={!tabs[0].hasPermission} />;
        default:
          return null;
      }
    };
    return (
      <>
        <Box
          sx={{
            padding: "0px",
          }}
        >
          <WrapContent title={t("maklumatCawangan")}>
            <Box
              sx={{
                // paddingTop: "0px",
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(180px, 1fr))",
                gap: "16px",
              }}
            >
              {enrichedTabs.map((data, index) => {
                return (
                  <MaklumatBoxes
                    key={index}
                    data={data}
                    isActive={data.slug === activeTab}
                    disabled={!data.hasPermission}
                    onClick={() => {
                      if (data.hasPermission) {
                        setActiveTab(data.slug);
                      }
                    }}
                  />
                );
              })}
            </Box>
          </WrapContent>
        </Box>
        {renderTab(enrichedTabs)}
      </>
    );
  }
}

export default cawangan;
