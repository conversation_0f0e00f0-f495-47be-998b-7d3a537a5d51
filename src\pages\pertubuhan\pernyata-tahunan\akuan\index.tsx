import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  Input<PERSON><PERSON>l,
  <PERSON>ack,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import Input from "../../../../components/input/Input";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { useSenaraiContext } from "../../SenaraiContext";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Controller,
  FieldValues,
  SubmitHandler,
  useForm,
} from "react-hook-form";
import { API_URL } from "@/api";
import { useCustomMutation } from "@refinedev/core";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { DialogConfirmation } from "@/components";
import ConfirmationDialog from "@/components/dialog/confirm";
import NewAlertDialog from "@/components/dialog/newAlert";
import { ApplicationStatus, useQuery } from "@/helpers";

const index = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  //@ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);

  const isviewStatement = useSelector(
    //@ts-ignore
    (state) => state?.statementData?.isViewStatement
  );
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);

  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;
  const {
    handleNextPenyataTahunan: handleNext,
    handleBackPenyataTahunan: handleBack,
    setPenyataTahunanSuccess: handleSuccess,
  } = useSenaraiContext();
  const [statementComplete, setStatementComplete] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogAlertSaveOpen, setDialogAlertSaveOpen] = useState(false);

  const [isChecked, setIsChecked] = useState(false);
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const handleOnCloseNewAlert = () => {
    setDialogAlertSaveOpen(false);
    navigate(`/pertubuhan/society/${societyId}/senarai/penyataTahunan/`);
  };

  const handleBackActions = () => {
    handleBack();
    navigate(-1);
  };

  const {
    control,
    setValue,
    watch,
    getValues,
    handleSubmit,
    trigger,
    reset: resetForm,
  } = useForm<FieldValues>({
    defaultValues: {
      akuanSetuju: false,
      // akuanSetuju: false,
    },
  });

  const { mutate: saveGeneralInfo, isLoading: isLoadingSubmit } =
    useCustomMutation();

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    const payload = {
      // akuanSetujuInduk: data.akuanSetujuInduk,
      // akuanSetuju: data.akuanSetuju,
      // statementId: statementId,
      societyId: societyId,
      akuanSetuju: true,
    };
    saveGeneralInfo(
      {
        url: `${API_URL}/society/statement/${statementId}/submit`,
        method: "put",
        values: payload,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          setIsDialogOpen(false);
          setDialogAlertSaveOpen(true);
        },
      }
    );
  };

  useQuery({
    url: `society/statement/societyInfo/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "year", operator: "eq", value: year },
    ],
    onSuccess: (data) => {
      const akuanSetuju = data?.data?.data?.akuanSetuju;
      if (akuanSetuju) {
        setIsChecked(akuanSetuju);
      }

      if (
        Number(data?.data?.data?.applicationStatusCode) ===
        ApplicationStatus.SELESAI
      ) {
        setStatementComplete(true);
      }
    },
  });

  const handleOpenDialog = async () => {
    const isValid = await trigger(["akuanSetuju"]);

    if (!isChecked) {
      return;
    }

    if (isValid) {
      setIsDialogOpen(true);
    }
  };

  const isDisabled = (!isManager && !isAliranTugasAccess) || isviewStatement;

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsChecked(event.target.checked);
  };
  
  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            backgroundColor: "white",
            borderRadius: "14px",
          }}
        >
          <Box
            sx={{
              background: "white",
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
              py: 2,
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("akuan")}
            </Typography>
            <Typography
              variant="subtitle1"
              sx={{
                color: "#666666",
                fontSize: 14,
                fontWeight: "400 !important",
              }}
            >
              <span>{t("pengakuan_description_1")}</span>
              <br />
              <br />
              <span>{t("pengakuan_description_2")}</span>
            </Typography>
          </Box>
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              px: 3,
              py: 2,
              mb: 2,
            }}
          >
            <Box sx={{ display: "flex", alignContent: "center" }} gap={1}>
              <Checkbox
                name="akuanSetuju"
                id="akuanSetuju"
                checked={isChecked}
                onChange={handleCheckboxChange}
                sx={{
                  color: "#00A7A7",
                  "&.Mui-checked": {
                    color: "#00A7A7",
                  },
                  padding: "0",
                }}
              />
              <InputLabel
                htmlFor="akuanSetuju"
                required
                sx={{
                  color: "#333333",
                  fontSize: 14,
                  fontWeight: 400,
                  "& .MuiFormLabel-asterisk": {
                    color: "#FF0000",
                  },
                }}
              >
                {t("agreementAcceptance")}
              </InputLabel>
            </Box>

            {!isChecked && (
              <Typography
                color="error"
                sx={{ fontWeight: "400!important", fontSize: "14px" }}
              >
                {t("selectPlaceholder")}
              </Typography>
            )}
          </Box>
          <Stack
            direction="row"
            spacing={2}
            sx={{ mt: 4, pl: 1 }}
            justifyContent="flex-end"
          >
            <ButtonOutline onClick={handleBackActions}>
              {t("back")}
            </ButtonOutline>
            {isDisabled || statementComplete ? null : (
              <ButtonPrimary onClick={handleOpenDialog}>
                {t("hantar")}
              </ButtonPrimary>
            )}
          </Stack>
        </Box>
        <ConfirmationDialog
          open={isDialogOpen}
          isMutation={isLoadingSubmit}
          turn={false}
          onClose={() => setIsDialogOpen(false)}
          onConfirm={handleSubmit(onSubmit)}
          onCancel={() => setIsDialogOpen(false)}
          title={""}
          message={t("confirmSubmitStatementText")}
        />
      </form>
      <NewAlertDialog
        open={dialogAlertSaveOpen}
        onClose={handleOnCloseNewAlert}
        message={t("confessionRecorded")}
      />
    </>
  );
};

export default index;
