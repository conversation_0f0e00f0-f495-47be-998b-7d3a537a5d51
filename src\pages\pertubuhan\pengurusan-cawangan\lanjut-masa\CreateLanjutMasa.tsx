import React, { useState } from "react";
import {
  Box,
  Grid,
  Typography,
  useMediaQuery,
  Theme,
  Dialog,
  DialogContent,
  DialogActions,
  useTheme,
  IconButton,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import {
  ButtonOutline,
  ButtonPrimary,
  ButtonText,
} from "../../../../components/button";
import { useNavigate, useParams } from "react-router-dom";
import { useMutation, useQuery } from "@/helpers";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import { useForm, FieldValues } from "react-hook-form";
import { TextFieldController } from "@/components";
import { NavigateBefore } from "@mui/icons-material";

export const CreateLanjutMasa: React.FC = () => {
  const { t } = useTranslation();
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const [extensionExist, setExtensionExist] = useState(false);
  const extensionItems = useSelector((state: RootState) => state.extensionTime);
  const {
    data: societyDataById,
    loading: loadingSociety,
    error: errorSociety,
  } = useSelector((state: any) => state.societyByIdData);
  const breadcrumbs = [
    {
      label: t("cawangan"),
      path: "/",
    },
  ];

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const navigate = useNavigate();

  // useCustomMutation
  const { mutate: createApproval, isLoading: isUpdatingStatus } =
    useCustomMutation();

  const { control, handleSubmit, getValues, watch, setValue } =
    useForm<FieldValues>({
      defaultValues: {
        note: "",
        extensionDays: "",
      },
    });

  console.log("extensionItems", extensionItems);

  const {
    data: existingData,
    isLoading: existingDataLoading,
    refetch: fetchExistingData,
  } = useQuery({
    url: `society/extensionTime/findExisting`,
    autoFetch: true,
    filters: [
      { field: "branchId", operator: "eq", value: extensionItems?.branchId },
    ],
    onSuccess: (data) => {
      const checkExist = data?.data?.data;
      if (checkExist) {
        setExtensionExist(true);
        const { note, extensionDays } = data?.data?.data;
        setValue("extensionDays", extensionDays);
        setValue("note", note);
      }
    },
  });

  const onSubmit = handleSubmit((data: any) => {
    setConfirmDialogOpen(false);
    createExtensionTime();
  });

  const { data: branchList, isLoading: isLoadingBranch } = useCustom<any>({
    url: `${API_URL}/society/branch/getById/${extensionItems?.branchId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: extensionItems?.branchId !== null,
      retry: false,
      cacheTime: 0,
    },
  });

  console.log("branchList?.data?.data?", branchList?.data?.data);

  const createExtensionTime = () => {
    const data = {
      societyId: extensionItems?.societyId,
      societyNo: extensionItems?.societyNo,
      branchId: extensionItems?.branchId,
      branchNo: extensionItems?.branchNo,
      extensionDays: getValues("extensionDays"),
      note: getValues("note"),
      applicationStatusCode: 2,
    };
    createApproval(
      {
        url: `${API_URL}/society/extensionTime/create`,
        method: "post",
        values: data,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: t("messageKeputusanPermohonanSuccessLanjutmasa"),
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: t("messageKeputusanPermohonanErrorLanjutmasa"),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          setConfirmDialogOpen(false);
          navigate(-1);
        },
      }
    );
  };

  return (
    <>
      <Box
        sx={{
          mb: 2,
          display: "flex",
          alignItems: "center",
          gap: 1,
        }}
      >
        <IconButton onClick={() => navigate(-1)}>
          <IconButton size="small" sx={{ color: "#666666", p: 0 }}>
            <NavigateBefore />
          </IconButton>
        </IconButton>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {breadcrumbs.map((item, index) => (
            <React.Fragment key={item.path}>
              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 18,
                  fontWeight: "400 !important",
                }}
                onClick={() => navigate(item.path)}
              >
                {item.label}
              </Typography>
              {index < breadcrumbs.length - 1 && (
                <IconButton size="small" sx={{ color: "#666666", p: 0 }}>
                  <NavigateBefore />
                </IconButton>
              )}
            </React.Fragment>
          ))}
        </Box>
      </Box>
      <Box
        sx={{
          backgroundColor: "white",
          padding: "18px 16px",
          marginBottom: 2,
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            backgroundColor: "var(--primary-color)",
            padding: "27px 41px",
            borderRadius: "14px",
            position: "relative",
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{ fontWeight: "bold", color: "white", fontSize: "18px" }}
          >
            {loadingSociety ? (
              "Loading..."
            ) : (
              <>
                {branchList?.data?.data?.name}
                <br />
                {branchList?.data?.data?.branchNo ??
                  branchList?.data?.data?.branchApplicationNo ??
                  "-"}
              </>
            )}
          </Typography>

          <Box
            sx={{
              width: "94px",
              position: "absolute",
              right: "37px",
              bottom: "-20px",
            }}
          >
            <img src="/ornament.svg" alt="ornament" width="100%" />
          </Box>
        </Box>
      </Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mb: 2 }}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            display: "flex",
            gap: 1,
          }}
        >
          <Typography sx={{ fontSize: 14, color: "red" }}>
            {t("peringatan")} :{" "}
          </Typography>
          <Typography
            sx={{
              fontSize: 14,
              color: "#666666",
              fontWeight: "400 !important",
              display: "flex",
            }}
          >
            {t("lanjutMasaWarning1")}
            <Typography sx={{ color: "red", px: "2px" }}>*</Typography>
            {t("lanjutMasaWarning2")}
          </Typography>
        </Box>
      </Box>

      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mb: 2 }}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("permohonanLanjutanMasa")}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("bilanganHari")}{" "}
                    <span
                      style={{
                        color: "red",
                      }}
                    >
                      *
                    </span>{" "}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={control}
                    fullWidth
                    required
                    name="extensionDays"
                    disabled={extensionExist}
                    isNumber
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("remarks")}{" "}
                    <span
                      style={{
                        color: "red",
                      }}
                    >
                      *
                    </span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={control}
                    fullWidth
                    required
                    rows={3}
                    multiline
                    limitWord={100}
                    name="note"
                    disabled={extensionExist}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>

        <Grid
          item
          xs={12}
          sx={{
            mt: 2,
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            justifyContent: "flex-end",
            gap: 1,
          }}
        >
          <ButtonOutline
            sx={{
              bgcolor: "white",
              "&:hover": { bgcolor: "white" },
              width: isMobile ? "100%" : "auto",
            }}
            onClick={() => navigate(-1)}
          >
            {t("back")}
          </ButtonOutline>
          {extensionExist ? null : (
            <ButtonPrimary
              variant="contained"
              sx={{
                width: isMobile ? "100%" : "auto",
              }}
              onClick={() => setConfirmDialogOpen(true)}
            >
              {t("hantar")}
            </ButtonPrimary>
          )}
        </Grid>

        <Dialog
          open={confirmDialogOpen}
          onClose={() => setConfirmDialogOpen(false)}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
              backgroundColor: "#fff",
              color: "#000",
              maxWidth: "500px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogContent sx={{ py: 2, px: 2.5, textAlign: "center" }}>
            <Typography>
              {t("adakahAndaPastiUntukMenghantarPermohonanIni")}
            </Typography>
          </DialogContent>
          <DialogActions
            sx={{
              py: 2,
              px: 3,
              justifyContent: "center",
              flexDirection: "column",
              gap: 1,
            }}
          >
            <ButtonPrimary onClick={onSubmit}>{t("ya")}</ButtonPrimary>
            <ButtonText
              onClick={() => setConfirmDialogOpen(false)}
              sx={{
                marginLeft: "0px !important",
              }}
            >
              {t("tidak")}
            </ButtonText>
          </DialogActions>
        </Dialog>
      </Box>
    </>
  );
};

export default CreateLanjutMasa;
