import Stack from "@mui/material/Stack";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { Box, Typography, Fade, useTheme } from "@mui/material";
import { API_URL } from "@/api";
import { useCustom, useCustomMutation } from "@refinedev/core";
import useMutation from "@/helpers/hooks/useMutation";
import { useSelector } from "react-redux";
import {
  setClauseid,
  setIsDisplayConstituition,
  setCurrentAmendmentConstitutionType,
  setCreatedDate,
} from "@/redux/fasalReducer";
import {
  ApplicationStatus,
  ConstitutionType,
  MeetingTypeOption,
  ROApprovalType,
} from "@/helpers/enums";
import { useDispatch } from "react-redux";
import {
  capitalizeWords,
  getLocalStorage,
  setLocalStorage,
} from "@/helpers/utils";
import ConfirmationDialog from "@/components/dialog/confirm";
import { useForm, FieldValues, FormProvider } from "react-hook-form";
import { removeFromStorage } from "../../pengurusan-pertubuhan/perlembagaan/removeFasal";
import MaklumatPindaanPerlembagaan from "./view/MaklumatPindaanPerlembagaan";
import MeetingPindaanPerlembagaan from "./view/MeetingPindaanPerlembagaan";
import SenaraiFasal from "./view/SenaraiFasal";
import { DialogConfirmation } from "@/components";

interface FormData {
  organizationGoals: string;
  amendedConstitutionRemarks: string;
  organizationLevel: string;
  organizationCategory: string;
  organizationSubCategory: string;
  hasBranch: number;
  constitutionType: string | number;
  originalConstuitionType?: string | number;
  constitutionsTemplateType?: string;
}
export interface PindaanPerlembagaan {
  id: number;
  fasal: number;
  namaFasal: string;
  applicationStatusCode: 1 | 2;
}

interface AmendmentData {
  id?: string | number | null;
  societyNo: string | number;
  societyId?: string | number;
  categoryCodeJppm: string;
  goal: string;
  societyLevel: string;
  subCategoryCode: string;
  hasBranch?: number;
  constitutionType: string | number;
  originalConstuitionType?: string | number;
  amendedConstitutionRemarks: string;
  constitutionsTemplateType: string;
}

export const ListPindaanPerlembagaan = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [isViewMode, setIsViewMode] = useState(
    fasalItems.IsViewPindaan ? true : false
  );
  const [refresh, setRefresh] = useState(0);
  const [senaraiFasal, setSenaraiFasal] = useState<any>([]);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [meetingType, setMeetingType] = useState("");
  const [amendmentId, setAmendmentId] = useState<string | number | null>(
    getLocalStorage("amendmentId", null)
  );
  const [meetingId, setMeetingId] = useState<string | number | null>(null);
  const [meetingList, setMeetingList] = useState([]);
  const [constitutionType, setConstitutionType] = useState("");
  const [originalConstuitionType, setOriginalConstuitionType] = useState("");
  const [addedFasalList, setAddedFasalList] = useState([]);
  const [query, setQuery] = useState([]);
  const [showConfirmDeleteDialog, setShowConfirmDeleteDialog] = useState(false);
  const [selectedIdForDelete, setSelectedIdForDelete] = useState(null);

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [retrieveMeetingMeetingId, setRetrieveMeetingMeetingId] = useState<
    string | number | null
  >(null);

  const [currentStatus, setCurrentStatus] = useState<string | number | null>(
    null
  );
  const [noteKuiri, setNoteKuiri] = useState([]);

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);
  const [formData, setFormData] = useState<FormData>({
    organizationGoals: societyDataRedux?.goal || "",
    amendedConstitutionRemarks:
      societyDataRedux?.amendedConstitutionRemarks || "",
    organizationLevel: societyDataRedux?.societyLevel || "",
    organizationCategory: societyDataRedux?.categoryCodeJppm || "",
    organizationSubCategory: societyDataRedux?.subCategoryCode || "",
    hasBranch: societyDataRedux?.hasBranch == "0" ? 0 : 1,
    constitutionType: societyDataRedux?.constitutionType || "",
    originalConstuitionType: societyDataRedux?.constitutionType || "",
    constitutionsTemplateType: "1",
  });

  const [meetingForm, setMeetingForm] = useState<any>({
    meetingId: "",
    meetingDate: "",
    totalAttendees: 0,
    meetingMemberAttendances: [],
  });

  const methodsForm = useForm<FieldValues>({
    defaultValues: {
      organizationGoals: societyDataRedux?.goal || "",
      amendedConstitutionRemarks:
        societyDataRedux?.amendedConstitutionRemarks || "",
      organizationLevel: societyDataRedux?.societyLevel || "",
      organizationCategory: Number(societyDataRedux?.categoryCodeJppm) || "",
      organizationSubCategory: Number(societyDataRedux?.subCategoryCode) || "",
      hasBranch: societyDataRedux?.hasBranch == "0" ? "0" : "1",
      constitutionType: societyDataRedux?.constitutionType || "",
      originalConstuitionType: societyDataRedux?.constitutionType || "",
      constitutionsTemplateType: "1",
    },
  });

  const {
    getValues: getValuesMethods,
    setValue: setValuesMethods,
    watch: watchMethods,
  } = methodsForm;

  const methodsMeetingForm = useForm<FieldValues>({
    defaultValues: {
      meetingId: "",
      //
      GISInformation: "",
      branchId: null,
      branchNo: null,
      city: "",
      closing: "",
      confirmBy: "",
      district: "",
      id: null,
      mattersDiscussed: "",
      meetingAddress: "",
      meetingContent: "",
      meetingDate: "",
      meetingMemberAttendances: [],
      meetingMethod: "",
      meetingMinute: "",
      meetingPlace: "",
      meetingPurpose: "",
      meetingTime: "",
      meetingTimeDurationMinutes: "",
      meetingTimeTo: "",
      meetingType: "",
      openingRemarks: "",
      otherMatters: "",
      platformType: "",
      postcode: "",
      providedBy: "",
      societyId: null,
      societyNo: null,
      state: "",
      totalAttendees: 0,
      meetingList: [],
    },
  });

  const {
    getValues: getValueMeeting,
    setValue: setValueMeeting,
    watch: watchMeeting,
  } = methodsMeetingForm;

  //GET ID PARAMS
  const { id } = useParams();
  const params = new URLSearchParams(window.location.search);
  const isEdit = params.get("isEdit");
  const isView = fasalItems.IsViewPindaan || null;
  const encodedId = id;

  useEffect(() => {
    if (encodedId) {
      const decodedId = atob(encodedId);
      setSocietyId(decodedId);
    }
  }, []);

  const getMeetingLabel = (value: number): string => {
    const meeting = MeetingTypeOption.find(
      (option) => Number(option.value) === Number(value)
    );
    return meeting ? meeting.label : "Unknown Meeting Type";
  };

  // @ts-ignore
  useEffect(() => {
    if (!societyDataRedux) return;
    setOriginalConstuitionType(societyDataRedux?.constitutionType);
    setValuesMethods("organizationGoals", societyDataRedux?.goal);
    setValuesMethods("organizationLevel", societyDataRedux?.societyLevel);
    setValuesMethods(
      "organizationCategory",
      Number(societyDataRedux?.categoryCodeJppm)
    );
    if (Number(societyDataRedux?.categoryCodeJppm) == 12) {
      setValuesMethods("constitutionsTemplateType", "0");
    } else {
      setValuesMethods("constitutionsTemplateType", "1");
    }
    setValuesMethods(
      "organizationSubCategory",
      Number(societyDataRedux?.subCategoryCode)
    );
    setValuesMethods(
      "hasBranch",
      societyDataRedux?.hasBranch == "0" ? "0" : "1"
    );
    setValuesMethods("constitutionType", societyDataRedux?.constitutionType);

    setConstitutionType(societyDataRedux?.constitutionType);
    dispatch(
      setCurrentAmendmentConstitutionType(societyDataRedux?.constitutionType)
    );
  }, [societyDataRedux]);

  // (CREATE AMENDMENT ID)
  //=========================================
  const { fetch: createAmendment, isLoading: AmendmentCreateIsLoading } =
    useMutation({
      url: "society/amendment/create",
      method: "post",
      onSuccess: (data) => {
        const id = data?.data?.data?.id;
        const type = data?.data?.data?.constitutionType;
        setAmendmentId(id);
        setLocalStorage("amendmentId", id);
      },
    });

  //  (UPDATE  AMENDMENT)
  const { fetch: updateAmendmentId, isLoading: isupdatingAmendmentId } =
    useMutation({
      url: "society/amendment/update/",
      method: "put",
      onSuccess: (data) => {
        const id = data?.data?.data?.id;
        setRetrieveMeetingMeetingId(getValueMeeting("meetingId"));
        dispatch(setIsDisplayConstituition(true));
        setAmendmentId(id);
        setLocalStorage("amendmentId", id);
      },
    });

  // API CALL CONSTITUITION CONTENT
  //======================================
  // GET ALL
  const { data: constitutionData, isLoading: isConstitutionLoading } =
    useCustom({
      url: `${API_URL}/society/admin/constitutionTypeWithClauseContent/list`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
    });

  const constitutions = constitutionData?.data?.data || [];

  //GET ALL CURRENT CONSTITUTION CONTENT
  const { data: clauseValueData } = useCustom({
    url: `${API_URL}/society/constitutionvalue/getAll`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: encodedId,
        amendmentId: amendmentId,
      },
    },
    queryOptions: {
      enabled: !!encodedId && !!amendmentId && senaraiFasal.length > 0,
      onSuccess: (data) => {
        const responseData = data?.data?.data?.data;
        if (responseData && responseData.length > 0) {
          const addedFasalExist = responseData.filter((i: any) => i.clauseNo);
          const updatedFasalExist = addedFasalExist.map((item: any) => ({
            ...item,
            status: 2,
          }));
          setAddedFasalList(updatedFasalExist);
        }
      },
    },
  });

  const {
    data: savedClauseContentData,
    isLoading: savedClauseContentDataIsLoading,
    refetch: refetchSavedClauseContentData,
  } = useCustom({
    url: `${API_URL}/society/constitutioncontent/get`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: encodedId,
        amendmentId: amendmentId,
      },
    },
    queryOptions: {
      enabled: !!encodedId && !!amendmentId && !isConstitutionLoading,
      cacheTime:0
    },
  });

  const allSavedClauseContentData =
    savedClauseContentData?.data?.data?.data || [];

  const isBebasCatergory =
    watchMethods("constitutionType") === ConstitutionType.Bebas[1] ||
    watchMethods("constitutionType") === ConstitutionType.CawanganBebas[1];

  const getConstitutionTypeIdFromLabel = (
    label: number | string
  ): number | string => {
    const entry = Object.values(ConstitutionType).find(
      ([id, name]) => name === label
    );
    return entry ? entry[0] : "";
  };

  // GENERATE CONSTITUTION DATA
  //====================================
  const generatePerlembagaan = () => {
    if (watchMethods("constitutionType") != "") {
      const selectedTypeId = getConstitutionTypeIdFromLabel(
        watchMethods("constitutionType")
      );
      let addedClauseContent = [];

      if (isBebasCatergory) {
        addedClauseContent = allSavedClauseContentData
          .map(
            // @ts-ignore
            (i) => {
              if (selectedTypeId == i.constitutionTypeId && !i.clauseContent) {
                return i;
              }
            }
          )
          .filter(Boolean)
          .map((i: any) => ({
            ...i,
            status: 2,
            name: i.clauseName,
            added: true,
            isToggle: i.amendmentToggle,
            constitutionContentId: i.id,
          }))
          .filter(
            (i: any) => i.clauseName !== "Tempat Urusan" && i.clauseNo !== "2"
          );
      }
      const updatedConstitutions = constitutions?.map((item: any) => {
        const updatedClauseContents = item?.clauseContents?.map(
          (clause: any, index: any) => {
            const exist = allSavedClauseContentData.findIndex(
              // @ts-ignore
              (p) => p.clauseContentId == clause.id && p.checkUpdate == 1
            );
            if (exist >= 0) {
              return {
                ...clause,
                isToggle: allSavedClauseContentData[exist].amendmentToggle,
                constitutionContentId: allSavedClauseContentData[exist].id,
                status: 2,
              };
            } else {
              return {
                ...clause,
                isToggle: false,
                status: 0,
              };
            }
          }
        );
        return {
          ...item,
          clauseContents: [...updatedClauseContents, ...addedClauseContent],
        };
      });

      updatedConstitutions?.map((item: any) => {
        if (item.name === getValuesMethods("constitutionType")) {
          setSenaraiFasal(item.clauseContents);
        }
      });
    } else {
      setSenaraiFasal([]);
    }
  }; 
  // FUNCTION HANDLE/MUTATE
  // ====================================================
  // ====================================================

  const handleSubmitAmendment = async (isEdit: boolean) => {
    removeFromStorage();
    // getValuesMethods("organizationCategory")
    let sendData: AmendmentData = {
      societyNo: "",
      societyId: id,
      categoryCodeJppm:
        getValuesMethods("constitutionsTemplateType") === "1"
          ? societyDataRedux?.categoryCodeJppm
          : 12,
      goal: getValuesMethods("organizationGoals"),
      constitutionsTemplateType: getValuesMethods("constitutionsTemplateType"),
      amendedConstitutionRemarks: getValuesMethods(
        "amendedConstitutionRemarks"
      ),
      societyLevel: getValuesMethods("organizationLevel"),
      subCategoryCode: getValuesMethods("organizationSubCategory"),
      hasBranch: Number(getValuesMethods("hasBranch")),
      constitutionType: getValuesMethods("constitutionType"),
    };
    if (isEdit) {
      sendData.id = amendmentId;
      await updateAmendmentId(sendData);
    } else {
      await createAmendment(sendData);
    }
    await generatePerlembagaan();
    setOpenConfirmDialog(false);
  };

  const handleSaveMeeting = async () => {
    const data = {
      meetingDate: getValueMeeting("meetingDate"),
      meetingId: getValueMeeting("meetingId"),
      meetingType: getValueMeeting("meetingType"),
      societyId: id,
      id: amendmentId,
    };
    await updateAmendmentId(data);
  };

  // ====================================================

  //trigger if amendment ID exist
  const { data: amendmentData } = useCustom({
    url: `${API_URL}/society/amendment/getAmendmentByParam`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        id: amendmentId,
      },
    },
    queryOptions: {
      enabled: !!amendmentId,
      onSuccess: (responseData) => {
        if (responseData.data.code === 200) {
          const data = responseData?.data?.data?.data?.[0];
          dispatch(setCreatedDate(data?.createdDate));
          dispatch(setCurrentAmendmentConstitutionType(data?.constitutionType));
          setCurrentStatus(data?.applicationStatusCode);
          setValuesMethods("organizationGoals", data?.goal);
          setValuesMethods(
            "amendedConstitutionRemarks",
            data?.amendedConstitutionRemarks
          );
          setValuesMethods("organizationLevel", data?.societyLevel);
          setValuesMethods(
            "organizationCategory",
            Number(data?.categoryCodeJppm)
          );
          if (Number(data?.categoryCodeJppm) == 12) {
            setValuesMethods("constitutionsTemplateType", "0");
          } else {
            setValuesMethods("constitutionsTemplateType", "1");
          }
          setValuesMethods(
            "organizationSubCategory",
            Number(data?.subCategoryCode)
          );
          setValuesMethods("hasBranch", data?.hasBranch == "0" ? "0" : "1");
          setValuesMethods("constitutionType", data?.constitutionType);
          setConstitutionType(data?.constitutionType);

          if (data.meetingType) {
            setValueMeeting("meetingType", Number(data?.meetingType));
            setValueMeeting("meetingId", data?.meetingId);
            setValueMeeting("meetingDate", data?.meetingDate);
            setMeetingType(data?.meetingType);
            setRetrieveMeetingMeetingId(data?.meetingId);
          }
        }
      },
    },
  });

  useEffect(() => {
    if (isEdit || isView) {
      // In view
      if (isView) {
        setLocalStorage("isViewMode", true);
        setIsViewMode(true);
      } else {
        setIsViewMode(false);
      }
      // IN EDIT
      setAmendmentId(isEdit || isView);
      setLocalStorage("amendmentId", isEdit || isView);
      generatePerlembagaan();
      dispatch(setIsDisplayConstituition(true));
    } else if (amendmentData && (!isEdit || !isView)) {
      // CRAETED AMENDMENT ID AND SYNC BACK THE DATA
      generatePerlembagaan();
    } else {
      // ADD PINDAAN
    }
  }, [
    constitutions,
    constitutionType,
    fasalItems.isDisplayConstituition,
    isEdit,
    isView,
    clauseValueData,
  ]);

  // MEETING API
  //GET MEETING DETAILS
  const {
    data: meetingSearchReuslt,
    isLoading: isLoadingSearchReuslt,
    refetch: refetchSearchReuslt,
  } = useCustom({
    url: `${API_URL}/society/meeting/${getValueMeeting("meetingId")}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled:
        !!id &&
        !!getValueMeeting("meetingType") &&
        !!watchMeeting("meetingId") &&
        !!amendmentId,
      onSuccess: (data) => {
        const meetingDataById = data?.data?.data;
        const meetingListById = data?.data?.data;
        if (meetingDataById) {
          methodsMeetingForm.reset({
            GISInformation: meetingDataById?.GISInformation || "",
            branchId: meetingDataById?.branchId || null,
            branchNo: meetingDataById?.branchNo || null,
            city: meetingDataById?.city || "",
            closing: meetingDataById?.closing || "",
            confirmBy: meetingDataById?.confirmBy || "",
            district: meetingDataById?.district || "",
            meetingId: meetingDataById?.id,
            id: meetingDataById?.id || null,
            mattersDiscussed: meetingDataById?.mattersDiscussed || "",
            meetingAddress: meetingDataById?.meetingAddress || "",
            meetingContent: meetingDataById?.meetingContent || "",
            meetingDate: meetingDataById?.meetingDate || "",
            meetingMemberAttendances:
              meetingDataById?.meetingMemberAttendances || [],
            meetingMethod: meetingDataById?.meetingMethod || "",
            meetingMinute: meetingDataById?.meetingMinute || "",
            meetingPlace: meetingDataById?.meetingPlace || "",
            meetingPurpose: meetingDataById?.meetingPurpose || "",
            meetingTime: meetingDataById?.meetingTime || "",
            meetingTimeDurationMinutes:
              meetingDataById?.meetingTimeDurationMinutes || "",
            meetingTimeTo: meetingDataById?.meetingTimeTo || "",
            meetingType: Number(meetingDataById?.meetingType) || "",
            openingRemarks: meetingDataById?.openingRemarks || "",
            otherMatters: meetingDataById?.otherMatters || "",
            platformType: meetingDataById?.platformType || "",
            postcode: meetingDataById?.postcode || "",
            providedBy: meetingDataById?.providedBy || "",
            societyId: meetingDataById?.societyId || null,
            societyNo: meetingDataById?.societyNo || null,
            state: meetingDataById?.state || "",
            totalAttendees: meetingDataById?.totalAttendees || "",
          });
          setMeetingForm((prevState: any) => ({
            ...prevState,
            ...meetingDataById,
            totalAttendees: meetingListById.totalAttendees,
            meetingMemberAttendances: meetingListById.memberAttendances,
          }));
        }
      },
    },
  });

  // GET MEETING DETAILS
  const {
    data: meetingData,
    isLoading: isLoadingMeetingData,
    refetch: refetchMeetingData,
  } = useCustom({
    url: `${API_URL}/society/meeting/search`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        pageSize: 100,
        societyId: id,
        meetingDate: getValueMeeting("meetingDate"),
      },
    },
    queryOptions: {
      enabled:
        !!id &&
        !!getValueMeeting("meetingType") &&
        !!getValueMeeting("meetingDate") &&
        !!amendmentId,
      onSuccess: (data) => {
        const meetingSearchlist = data?.data?.data?.data || [];
        if (meetingSearchlist.length > 0) {
          const meetingListData = meetingSearchlist
            .filter((item: any) => {
              return (
                Number(item?.meetingType) ===
                Number(watchMeeting("meetingType"))
              );
            })
            .map((i: any) => ({
              value: i.id,
              label: `${getMeetingLabel(i.meetingType)} ${i.meetingDate}`,
            }));
          setValueMeeting("totalAttendees", 0);
          setValueMeeting("meetingMemberAttendances", []);
          setValueMeeting("meetingList", meetingListData);
          setMeetingList(meetingListData);
        }
      },
    },
  });

  const { fetch: deleteFasal, isLoading: isLoadingDeleteFasal } = useMutation({
    url: `society/constitutioncontent/hardDeleteConstitution?constitutionContentId=${selectedIdForDelete}`,
    method: "delete",
    onSuccess: (data) => {
      refetchSavedClauseContentData();
      setSenaraiFasal((prev: any) =>
        prev.filter((item: any) => item.id !== selectedIdForDelete)
      );
      setRefresh((prev) => prev + 1);
      setShowConfirmDeleteDialog(false);
    },
  });

  const handleAddFasal = () => {
    const currentNo = senaraiFasal.length + 1;
    const id = btoa(currentNo.toString());
    navigate(`add/${id}`);
  };

  const handleOpenDeleteFasalModal = (data: any) => {
    const id = data.id;
    setSelectedIdForDelete(id);
    setShowConfirmDeleteDialog(true);
  };

  const handleDeleteRow = () => {
    deleteFasal();
  };

  //
  const { mutate: getQuery, isLoading } = useCustomMutation();

  function getQueryData() {
    const data = {
      societyId: id,
      amendmentId: amendmentId,
      roApprovalType: ROApprovalType.SOCIETY_AMENDMENT.code,
    };
    getQuery(
      {
        method: "post",
        url: `${API_URL}/society/roQuery/getQuery`,
        values: data,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess(data: any) {
          const queryData = data?.data?.data;
          setQuery(queryData);
          if (queryData?.length > 0) {
            setNoteKuiri(queryData[0].note || "");
          }
        },
      }
    );
  }

  useEffect(() => {
    if (currentStatus == ApplicationStatus.KUIRI) {
      getQueryData();
    }
  }, [currentStatus]);

  const createdDate = amendmentData?.data?.data?.data?.[0]?.createdDate;

  const fasalIsLoading = isLoadingMeetingData || isLoadingSearchReuslt;

  // ===========================
  return (
    <>
      <Fade in={true} timeout={500}>
        <Stack
          gap={2}
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            backgroundColor: "white",
            borderRadius: "14px",
          }}
        >
          {/* pergingatan box */}
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
            }}
          >
            <Typography
              sx={{
                color: "#666666",
                fontSize: 14,
                fontWeight: "400 !important",
              }}
            >
              <span style={{ color: "red", fontWeight: "bold" }}>
                {t("peringatan")} :
              </span>{" "}
              <br />- {t("mandatoryFieldsAlert")}
              <span style={{ color: "red" }}>*</span> <br />-{" "}
              {t("pindaanRemindText")}
            </Typography>
          </Box>

          {/* note kuiri */}
          {noteKuiri.length > 0 && (
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography
                variant="subtitle1"
                sx={{
                  color: "var(--primary-color)",
                  marginBottom: "16px",
                  borderRadius: "16px",
                  fontSize: "14px",
                  fontWeight: "500 !important",
                }}
              >
                {t("kuiri")}
              </Typography>
              <Box>{noteKuiri}</Box>
            </Box>
          )}

          {/* Maklumat Pindaan Perlembagaan */}
          <FormProvider {...methodsForm}>
            <MaklumatPindaanPerlembagaan
              amendmentId={amendmentId}
              isViewMode={isViewMode}
              handleSubmitAmendment={handleSubmitAmendment}
            />
          </FormProvider>

          {amendmentId && (
            <FormProvider {...methodsMeetingForm}>
              <MeetingPindaanPerlembagaan
                amendmentId={amendmentId}
                isViewMode={isViewMode}
                handleSaveMeeting={handleSaveMeeting}
                meetingList={meetingList}
              />
            </FormProvider>
          )}

          {/* Senarai fasal */}
          {!fasalIsLoading &&
          fasalItems.isDisplayConstituition &&
          senaraiFasal &&
          amendmentId &&
          retrieveMeetingMeetingId ? (
            <FormProvider {...methodsMeetingForm}>
              <SenaraiFasal
                key={refresh}
                handleAddFasal={handleAddFasal}
                handleDeleteFasal={handleOpenDeleteFasalModal}
                senaraiFasal={senaraiFasal}
                originalConstuitionType={originalConstuitionType}
                constitutionType={constitutionType}
                createdDate={createdDate}
              />
            </FormProvider>
          ) : null}
        </Stack>
      </Fade>

      <ConfirmationDialog
        open={openConfirmDialog}
        onClose={() => setOpenConfirmDialog(false)}
        onConfirm={() => handleSubmitAmendment(true)}
        onCancel={() => setOpenConfirmDialog(false)}
        title={t("updateConstitution")}
        message={t("comfirmUpdatePindaan")}
      />

      <DialogConfirmation
        open={showConfirmDeleteDialog}
        onClose={() => {
          setShowConfirmDeleteDialog(false);
          setSelectedIdForDelete(null);
        }}
        onAction={handleDeleteRow}
        isMutating={isLoadingDeleteFasal}
        onConfirmationText={t("confirmdeletefasalText")}
      />
    </>
  );
};

export default ListPindaanPerlembagaan;
