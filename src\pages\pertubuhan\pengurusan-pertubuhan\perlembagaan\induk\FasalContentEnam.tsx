import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
  FormHelperText,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { useSelector } from "react-redux";
import { API_URL } from "../../../../../api";
import { useDispatch } from "react-redux";
import { FasalContentProps } from "../Fasal";
import { useCustom } from "@refinedev/core";
import { CommiteeEnum, ConstitutionType } from "../../../../../helpers/enums";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import { DialogConfirmation } from "@/components";
import { capitalizeWords } from "@/helpers";
import MessageDialog from "@/components/dialog/message";

interface FasalContentEnamProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
  clauseValue: string;
}

export const FasalContentEnam: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [tempohJawatan, setTempohJawatan] = useState("");
  const [pemilihanAjk, setPemilihanAjk] = useState("");
  const [kekerapan, setKekerapan] = useState("4");
  const [tempohPelucutan, setTempohPelucutan] = useState("0");
  const [isLoadingUpdate, setIsLoadingUpdate] = useState(false);
  const [tempohPelucutanWaktu, setTempohPelucutanWaktu] = useState("day");
  const [pengerusi, setPengerusi] = useState("");
  const [jumlahPengerusi, setJumlahPengerusi] = useState<any>("1");
  const [timbalan, setTimbalan] = useState("");
  const [jumlahTimbalan, setJumlahTimbalan] = useState<any>("0");
  const [naib, setNaib] = useState("");
  const [jumlahNaib, setJumlahNaib] = useState<any>("0");
  const [setiaUsaha, setSetiaUsaha] = useState("");
  const [jumlahSetiaUsaha, setJumlahSetiaUsaha] = useState<any>("1");
  const [penolongSetiaUsaha, setPenolongSetiaUsaha] = useState(
    t("asistantSecretary")
  );
  const [jumlahPenolongSetiaUsaha, setJumlahPenolongSetiaUsaha] =
    useState<any>("0");
  const [bendahari, setBendahari] = useState(t("treasurer"));
  const [jumlahBendahari, setJumlahBendahari] = useState<any>("1");
  const [penolongBendahari, setPenolongBendahari] = useState("");
  const [jumlahPenolongBendahari, setJumlahPenolongBendahari] =
    useState<any>("0");
  const [ahliBiasa, setAhliBiasa] = useState("");
  const [jumlahAhliBiasa, setJumlahAhliBiasa] = useState<any>("0");
  const [dialogSaveOpen, setDialogSaveOpen] = useState(false);
  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState("");
  //const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [isRequiredConstitution, setIsRequiredConstitution] = useState(true);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };
  const [showErrorAjk, setShowErrorAjk] = useState(false);

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  /*const clauseContent = `
1. Satu Jawatankuasa seperti berikut yang dinamakan Ahli Jawatankuasa Pertubuhan hendaklah dipilih ${
    tempohJawatan || "<<Tempoh Pelantikan Jawatankuasa>>"
  } dalam Mesyuarat Agung ${pemilihanAjk || "<<Jenis Mesyuarat>>"}:

i. ${jumlahPengerusi || "<<Bilangan Pengerusi>>"} orang ${
    pengerusi || "<<Jawatan Pengerusi>>"
  }

ii. ${jumlahTimbalan || "<<Bilangan Timbalan Pengerusi>>"} orang ${
    timbalan || "<<Jawatan Timbalan Pengerusi>>"
  }

iii. ${jumlahNaib || "<<Bilangan Naib Pengerusi>>"} orang ${
    naib || "<<Jawatan Naib Pengerusi>>"
  } (pilihan)

iv. ${jumlahSetiaUsaha || "<<Bilangan Setiausaha>>"} orang ${
    setiaUsaha || "<<Jawatan Setiausaha>>"
  }

v. ${jumlahPenolongSetiaUsaha || "<<Bilangan Penolong Setiausaha>>"} orang ${
    penolongSetiaUsaha || "<<Jawatan Penolong Setiausaha>>"
  }

vi. ${jumlahBendahari || "<<Bilangan Bendahari>>"} orang ${
    bendahari || "<<Jawatan Bendahari>>"
  }

vii. ${jumlahPenolongBendahari || "<<Bilangan Penolong Bendahari>>"} orang ${
    penolongBendahari || "<<Jawatan Penolong Bendahari>>"
  } (pilihan)

viii. ${jumlahAhliBiasa || "<<Bilangan Ahli Jawatankuasa Biasa>>"} orang ${
    ahliBiasa || "<<Jawatan Ahli Jawatankuasa Biasa>>"
  }

2. Pemegang-pemegang jawatan Pertubuhan ini dan tiap-tiap pegawai yang menjalankan tugas eksekutif dalam Pertubuhan ini hendaklah warganegara Malaysia dan bukan warganegara yang mendapat kebenaran Pendaftar Pertubuhan. (Nota: selaraskan dengan pilihan di Fasal 4 Keahlian)

3. Nama calon untuk jawatan-jawatan di atas hendaklah dicadang serta disokong dan pemilihan dijalankan dengan kaedah mengundi oleh ahli-ahli dalam Mesyuarat Agung ${
    pemilihanAjk || "<<Jenis Mesyuarat>>"
  }. Ahli Jawatankuasa sebelumnya boleh dicalonkan semula untuk jawatan-jawatan tersebut.

4. Fungsi Jawatankuasa ialah mengelola dan mengaturkan kerja-kerja harian Pertubuhan dan membuat keputusan atas perkara-perkara mengenai perjalanan Pertubuhan mengikut dasar am yang telah ditetapkan oleh Mesyuarat Agung. Jawatankuasa tidak boleh mengambil tindakan yang bertentangan dengan keputusan Mesyuarat Agung melainkan Jawatankuasa terlebih dahulu merujuk kepada Mesyuarat Agung. Jawatankuasa hendaklah mengemukakan laporan berkenaan kegiatan Pertubuhan bagi tahun sebelumnya dalam tiap-tiap Mesyuarat Agung.

5. Jawatankuasa hendaklah bermesyuarat sekurang-kurangnya ${
    kekerapan || "<<Kekerapan Mesyuarat>>"
  } kali setahun. Notis bagi tiap-tiap mesyuarat hendaklah diberikan kepada Ahli Jawatankuasa sekurang-kurangnya tujuh (7) hari sebelum tarikh mesyuarat. Pengerusi dengan bersendirian, atau tidak kurang daripada empat (4) orang Ahli Jawatankuasa bersama-sama, boleh memanggil supaya diadakan Mesyuarat Jawatankuasa pada bila-bila masa. Sekurang-kurangnya satu perdua (1/2) daripada bilangan Ahli Jawatankuasa hendaklah hadir bagi mengesahkan perjalanan dan mencukupkan kuorum mesyuarat.

6. Jika timbul perkara mustahak yang memerlukan kelulusan Jawatankuasa dan Mesyuarat Jawatankuasa tidak dapat diadakan, maka Setiausaha boleh mendapatkan kelulusan daripada Jawatankuasa secara edaran kepada Ahli Jawatankuasa. Syarat-syarat berikut hendaklah disempurnakan sebelum mendapat keputusan Jawatankuasa:
  a. Perkara yang dibangkitkan itu hendaklah dijelaskan dengan terang kepada tiap-tiap Ahli Jawatankuasa mengikut kaedah edaran yang ditetapkan;
  b. Sekurang-kurangnya satu perdua (1/2) daripada bilangan Ahli Jawatankuasa mestilah menyatakan persetujuan atau bantahan mereka terhadap perkara itu; dan
  c. Keputusan hendaklah dengan undi yang terbanyak. Sebarang keputusan yang diperolehi melalui kaedah edaran yang ditetapkan hendaklah dilaporkan oleh Setiausaha kepada Mesyuarat Jawatankuasa berikutnya untuk disahkan dan dicatatkan dalam minit mesyuarat.

7. Ahli Jawatankuasa yang tidak menghadiri Mesyuarat Jawatankuasa tiga (3) kali berturut-turut tanpa alasan yang memuaskan akan disifatkan sebagai telah meletakkan jawatan.

8. Jika seorang Ahli Jawatankuasa meninggal dunia atau meletakkan jawatan atau kekosongan oleh apa-apa sebab sekalipun, calon yang kedua atau seterusnya mendapat undi terbanyak dalam pemilihan yang lalu hendaklah dipanggil untuk mengisi kekosongan itu. Jika calon yang sedemikian tidak ada atau menolak jawatan itu, maka Jawatankuasa mempunyai kuasa untuk melantik ahli Pertubuhan yang lain mengisi kekosongan itu sehingga Mesyuarat Agung ${
    pemilihanAjk || "<<Jenis Mesyuarat>>"
  } diadakan.

9. Jawatankuasa melalui mesyuarat boleh memberi arahan kepada Setiausaha dan pegawai-pegawai lain untuk menjalankan urusan-urusan Pertubuhan, dan melantik pengurus dan kakitangan yang difikirkan mustahak. Jawatankuasa boleh menggantung atau melucutkan jawatan mana-mana pengurus atau kakitangan kerana cuai dalam pekerjaan, tidak jujur, tidak cekap, khinat, ingkar menjalankan keputusan Jawatankuasa, atau sebab-sebab yang difikirkan boleh menjejaskan kepentingan Pertubuhan.

10. Jawatankuasa boleh menubuhkan Jawatankuasa Kecil jika difikirkan mustahak dan memberikan tugas khas kepada mana-mana Ahli Jawatankuasa.

11. Jawatankuasa hendaklah bertanggungjawab memelihara dokumen Pertubuhan pada setiap masa.

12. Jawatankuasa yang lama hendaklah menyerahkan semua dokumen Pertubuhan seperti sijil pendaftaran, perlembagaan berdaftar, salinan Penyata Tahunan, surat-surat maklum balas, buku-buku akaun, buku bank, buku cek dan resit, geran-geran tanah, senarai aset dan sebagainya kepada Jawatankuasa baharu.

13. Jawatankuasa melalui mesyuarat boleh menggantung atau melucutkan mana-mana Ahli Jawatankuasa kerana cuai dalam menjalankan tugas, ingkar menjalankan keputusan Jawatankuasa atau sebab-sebab yang difikirkan boleh menjejaskan kepentingan Pertubuhan. Sebelum Jawatankuasa menggantung atau melucutkan jawatan seseorang Ahli Jawatankuasa, Ahli Jawatankuasa tersebut hendaklah diberitahu akan sebab-sebab bagi penggantungan atau pelucutannya secara bertulis. Ahli Jawatankuasa tersebut juga hendaklah diberi peluang dalam tempoh ${
    tempohPelucutan || "<<Tempoh Membela Diri Pelucutan>>"
  } ${
    tempohPelucutanWaktu || ""
  } hari dari tarikh penggantungan atau pelucutannya untuk memberi penjelasan dan membela dirinya. Penggantungan atau pelucutannya itu hendaklah dilaksanakan melainkan keputusan Mesyuarat Agung menunda atau membatalkan keputusan itu atas rayuan oleh ahli jawatankuasa tersebut.
`;*/

  //const clause6 = localStorage.getItem("clause6");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();
  const { mutate: preCreateAjk } = useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (
      societyDataRedux?.constitutionType === ConstitutionType.IndukAgama[1] ||
      societyDataRedux?.constitutionType ===
        ConstitutionType.CawanganAgama[1] ||
      societyDataRedux?.constitutionType === ConstitutionType.FaedahBersama[1]
    ) {
      setIsRequiredConstitution(false);
    } else {
      setIsRequiredConstitution(true);
    }
  }, [societyDataRedux?.constitutionType]);

  useEffect(() => { 
    if (clause.clauseNo) { 
      setClauseNo(clause.clauseNo);
    }
    if (clause.clauseContentId) {
      //setClauseContent(clause.clauseContent)
      setClauseContentId(clause.clauseContentId);
    }
    if (clause?.constitutionValues?.length > 0) {
      //const clause = JSON.parse(clauseValue);
      setDataId(clause.id);
   

      const fieldMappings: Record<string, (value: string) => void> = {
        "Tempoh Pelantikan Jawatankuasa": setTempohJawatan,
        "Jenis Mesyuarat Agung": setPemilihanAjk,
        "Kekerapan Mesyuarat Jawatankuasa": setKekerapan,
        "Bilangan tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa": setTempohPelucutan,
        "Tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa": setTempohPelucutanWaktu,
        "Pengerusi": setPengerusi,
        "Bilangan Pengerusi": setJumlahPengerusi,
        "Timbalan Pengerusi": setTimbalan,
        "Bilangan Timbalan Pengerusi": setJumlahTimbalan,
        "Naib Pengerusi": setNaib,
        "Bilangan Naib Pengerusi": setJumlahNaib,
        "Setiausaha": setSetiaUsaha,
        "Bilangan Setiausaha": setJumlahSetiaUsaha,
        "Penolong Setiausaha": setPenolongSetiaUsaha,
        "Bilangan Penolong Setiausaha": setJumlahPenolongSetiaUsaha,
        "Bendahari": setBendahari,
        "Bilangan Bendahari": setJumlahBendahari,
        "Penolong Bendahari": setPenolongBendahari,
        "Bilangan Penolong Bendahari": setJumlahPenolongBendahari,
        "Ahli Jawatankuasa Biasa": setAhliBiasa,
        "Bilangan Ahli Jawatankuasa Biasa": setJumlahAhliBiasa
      };
      if(clause.constitutionValues){
        clause.constitutionValues.forEach((item:any) => {
          const setter = fieldMappings[item.titleName];
          if (setter && item.definitionName) {
            setter(item.definitionName);
          }
        });
      }
      // setTempohJawatan(clause.constitutionValues[0]?.definitionName);
      // setPemilihanAjk(clause.constitutionValues[1]?.definitionName);
      // setKekerapan(clause.constitutionValues[2]?.definitionName);
      // setTempohPelucutan(clause.constitutionValues[3]?.definitionName); 
      // setPengerusi(clause.constitutionValues[5]?.definitionName); 
      // setTimbalan(clause.constitutionValues[7]?.definitionName);
      // setJumlahTimbalan(clause.constitutionValues[8]?.definitionName);
      // setNaib(t(clause.constitutionValues[9]?.definitionName));
      // setJumlahNaib(clause.constitutionValues[10]?.definitionName);
      // setSetiaUsaha(clause.constitutionValues[11]?.definitionName); 
      // setPenolongSetiaUsaha(clause.constitutionValues[13]?.definitionName);
      // setJumlahPenolongSetiaUsaha(
      //   clause.constitutionValues[14]?.definitionName
      // );
      // setBendahari(clause.constitutionValues[15]?.definitionName); 
      // setPenolongBendahari(clause.constitutionValues[17]?.definitionName);
      // setJumlahPenolongBendahari(clause.constitutionValues[18]?.definitionName);
      // setAhliBiasa(clause.constitutionValues[19]?.definitionName);
      // setJumlahAhliBiasa(clause.constitutionValues[20]?.definitionName);
      setIsEdit(clause.edit);
    } else {
      setClauseContentId(clause.clauseContentId);
      //Jawatankuasa
      setTempohJawatan(t("setahun"));
      setPemilihanAjk(t("annual"));
      setTempohPelucutanWaktu("day");

      //Ahli Jawatankuasa
      setPengerusi(t("chairman"));
      setTimbalan(t("timbalanPengerusi"));
      setNaib(t("naibPengerusi"));
      setSetiaUsaha(t("secretary"));
      setPenolongSetiaUsaha(t("asistantSecretary"));
      setBendahari(t("treasurer"));
      setPenolongBendahari(t("asistantTreasurer"));
      setAhliBiasa(t("ordinaryCommitteeMember"));
    } 
    if (clause.clauseName) {
      setClauseName(clause.clauseName);
    }
  }, [clause, t]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id } = useParams();

  const dispatch = useDispatch();
  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<tempoh pelantikan jawatankuasa>>/gi,
    `<b>${tempohJawatan || "<<tempoh pelantikan jawatankuasa>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${pemilihanAjk || "<<jenis mesyuarat agung>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kekerapan mesyuarat>>/gi,
    `<b>${kekerapan || "<<kekerapan mesyuarat>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh membela diri pelucutan>>/gi,
    `<b>${tempohPelucutan || "<<tempoh membela diri pelucutan>>"}</b>`
  );
 
  clauseContent = clauseContent.replaceAll(
    /<<jawatan Pengerusi>>/gi,
    `<b>${pengerusi || "<<jawatan Pengerusi>>"}</b>`
  );
 
  if (jumlahTimbalan > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Timbalan Pengerusi>>/gi,
      `<b>${timbalan || "<<jawatan Timbalan Pengerusi>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan Timbalan Pengerusi>>/gi,
      `<b>${
        Number(jumlahTimbalan) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahTimbalan} orang` || "<<bilangan Timbalan Pengerusi>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*ii\.\s*<<bilangan Timbalan Pengerusi>> <<jawatan Timbalan Pengerusi>>\s*[\r\n\t]?/gim,
      "    "
    );
  } 

  if (jumlahNaib > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Naib Pengerusi>>/gi,
      `<b>${naib || "<<jawatan Naib Pengerusi>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan Naib Pengerusi>>/gi,
      `<b>${
        Number(jumlahNaib) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahNaib} orang` || "<<bilangan Naib Pengerusi>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*iii\.\s*<<bilangan Naib Pengerusi>> <<jawatan Naib Pengerusi>>\s*[\r\n\t]?/gim,
      "    "
    );
  }

  clauseContent = clauseContent.replaceAll(
    /<<jawatan Bendahari Agung>>/gi,
    `<b>${bendahari || "<<jawatan Bendahari Agung>>"}</b>`
  );
 
  if (jumlahPenolongBendahari > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Penolong Bendahari>>/gi,
      `<b>${penolongBendahari || "<<jawatan Penolong Bendahari>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan pen. bendahari>>/gi,
      `<b>${
        Number(jumlahPenolongBendahari) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahPenolongBendahari} orang` || "<<bilangan pen. bendahari>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*vii\.\s*<<bilangan pen. bendahari>> <<jawatan Penolong Bendahari>>\s*[\r\n]?/gim,
      "    "
    );
  }
  
  clauseContent = clauseContent.replaceAll(
    /<<jawatan Setiausaha Agung>>/gi,
    `<b>${setiaUsaha || "<<jawatan Setiausaha Agung>>"}</b>`
  );
 
  if (jumlahPenolongSetiaUsaha > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Penolong Setiausaha>>/gi,
      `<b>${penolongSetiaUsaha || "<<jawatan Penolong Setiausaha>>"}</b>`
    );

    clauseContent = clauseContent.replaceAll(
      /<<bilangan pen. SU>>/gi,
      `<b>${
        Number(jumlahPenolongSetiaUsaha) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahPenolongSetiaUsaha} orang` || "<<bilangan pen. SU>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*v\.\s*<<bilangan pen. SU>> <<jawatan Penolong Setiausaha>>\s*[\r\n]?/gim,
      "    "
    );
  }
 
  if (jumlahAhliBiasa > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Ahli Jawatankuasa Biasa>>/gi,
      `<b>${ahliBiasa || "<<jawatan Ahli Jawatankuasa Biasa>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan ajk>>/gi,
      `<b>${
        Number(jumlahAhliBiasa) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahAhliBiasa} orang` || "<<bilangan ajk>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*viii\.\s*<<bilangan ajk>> <<jawatan Ahli Jawatankuasa Biasa>>\s*[\r\n]?/gim,
      ""
    );
  }

   clauseContent = clauseContent.replace(
    /<<jawatan telah diubahsuai>>\s*/gi,
    ''
  ); 
  clauseContent = clauseContent.replace(
   /\s*<<penambahan jawatan diubahsuai>>/gi, 
    ``
    );

  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  clauseContent = renumberRomanList(clauseContent);

  function renumberRomanList(content: any) {
    const romanNumerals = [
      "i",
      "ii",
      "iii",
      "iv",
      "v",
      "vi",
      "vii",
      "viii",
      "ix",
      "x",
    ];
    let index = 0;

    return content.replace(
      /^(\s*)(?:i{1,3}|iv|v?i{0,3}|ix|x)\.\s/gim,
      (_: any, indent: any) => {
        const roman = romanNumerals[index++] || `${index}.`;
        return `${indent}${roman}. `;
      }
    );
  }

  const { data: listCommittee } = useCustom({
    url: `${API_URL}/society/committee/getAll`,
    method: "get",
    config: {
      filters: [
        {
          field: "pageNo",
          operator: "eq",
          value: 1,
        },
        {
          field: "pageSize",
          operator: "eq",
          value: 1000,
        },
        {
          field: "societyId",
          operator: "eq",
          value: societyDataRedux.id,
        },
      ],
      // query: {
      //   // societyId: id,
      //   pageNo: 1,
      //   // pageSize: 10,
      // },

      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !isEdit,
    },
  });

  const committeeData = listCommittee?.data?.data?.data || [];

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!tempohJawatan) {
      errors.tempohJawatan = t("fieldRequired");
    }
    if (!pemilihanAjk) {
      errors.pemilihanAjk = t("fieldRequired");
    }
    if (!kekerapan) {
      errors.kekerapan = t("fieldRequired");
    }
    if (!tempohPelucutan) {
      errors.tempohPelucutan = t("fieldRequired");
    }
    if (!tempohPelucutanWaktu) {
      errors.tempohPelucutanWaktu = t("fieldRequired");
    }
    if (!jumlahPengerusi) {
      errors.jumlahPengerusi = t("fieldRequired");
    }
    if (!pengerusi) {
      errors.pengerusi = t("fieldRequired");
    }
    if (!jumlahTimbalan && isRequiredConstitution) {
      errors.jumlahTimbalan = t("fieldRequired");
    }
    if (!timbalan && isRequiredConstitution) {
      errors.timbalan = t("fieldRequired");
    }
    if (!jumlahNaib && isRequiredConstitution) {
      errors.jumlahNaib = t("fieldRequired");
    }
    if (!naib && isRequiredConstitution) {
      errors.naib = t("fieldRequired");
    }
    if (!jumlahSetiaUsaha) {
      errors.jumlahSetiaUsaha = t("fieldRequired");
    }
    if (!setiaUsaha) {
      errors.setiaUsaha = t("fieldRequired");
    }
    if (!jumlahPenolongSetiaUsaha && isRequiredConstitution) {
      errors.jumlahPenolongSetiaUsaha = t("fieldRequired");
    }
    if (!penolongSetiaUsaha && isRequiredConstitution) {
      errors.penolongSetiaUsaha = t("fieldRequired");
    }
    if (!jumlahBendahari) {
      errors.jumlahBendahari = t("fieldRequired");
    }
    if (!bendahari) {
      errors.bendahari = t("fieldRequired");
    }
    if (!jumlahPenolongBendahari && isRequiredConstitution) {
      errors.jumlahPenolongBendahari = t("fieldRequired");
    }
    if (!penolongBendahari && isRequiredConstitution) {
      errors.penolongBendahari = t("fieldRequired");
    }
    if (!jumlahAhliBiasa && isRequiredConstitution) {
      errors.jumlahAhliBiasa = t("fieldRequired");
    }
    if (!ahliBiasa && isRequiredConstitution) {
      errors.ahliBiasa = t("fieldRequired");
    }

    return errors;
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  const handleConfirm = () => {
    const designationCode = [];
    for (let i = 0; i < jumlahPengerusi; i++) {
      designationCode.push(
        CommiteeEnum[pengerusi as keyof typeof CommiteeEnum]
      );
    }
    for (let i = 0; i < jumlahTimbalan; i++) {
      designationCode.push(CommiteeEnum[timbalan as keyof typeof CommiteeEnum]);
    }
    for (let i = 0; i < jumlahNaib; i++) {
      designationCode.push(CommiteeEnum[naib as keyof typeof CommiteeEnum]);
    }
    for (let i = 0; i < jumlahSetiaUsaha; i++) {
      designationCode.push(
        CommiteeEnum[setiaUsaha as keyof typeof CommiteeEnum]
      );
    }
    for (let i = 0; i < jumlahPenolongSetiaUsaha; i++) {
      designationCode.push(
        CommiteeEnum[penolongSetiaUsaha as keyof typeof CommiteeEnum]
      );
    }
    for (let i = 0; i < jumlahBendahari; i++) {
      designationCode.push(
        CommiteeEnum[bendahari as keyof typeof CommiteeEnum]
      );
    }
    for (let i = 0; i < jumlahPenolongBendahari; i++) {
      designationCode.push(
        CommiteeEnum[penolongBendahari as keyof typeof CommiteeEnum]
      );
    }
    for (let i = 0; i < jumlahAhliBiasa; i++) {
      designationCode.push(CommiteeEnum["Ahli Jawatankuasa Biasa"]);
    }
    setIsLoadingUpdate(true);
    preCreateAjk(
      {
        url: `${API_URL}/society/committee/updateForRegistration`,
        method: "put",
        values: {
          societyId,
          designationCode,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: false,
      },
      {
        onSuccess(data: any, variables: any, context: any) {
          handleSaveContent({
            i18n,
            societyId,
            societyName: namaPertubuhan,
            dataId,
            isEdit,
            clauseNo,
            createClauseContent,
            editClauseContent,
            description: clauseContent,
            constitutionValues: [
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: tempohJawatan,
                titleName: "Tempoh Pelantikan Jawatankuasa",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: pemilihanAjk,
                titleName: "Jenis Mesyuarat Agung",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: kekerapan,
                titleName: "Kekerapan Mesyuarat Jawatankuasa",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: tempohPelucutan,
                titleName:
                  "Bilangan tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: tempohPelucutanWaktu,
                titleName:
                  "Tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: pengerusi,
                titleName: "Pengerusi",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahPengerusi,
                titleName: "Bilangan Pengerusi",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: timbalan,
                titleName: "Timbalan Pengerusi",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahTimbalan,
                titleName: "Bilangan Timbalan Pengerusi",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: naib,
                titleName: "Naib Pengerusi",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahNaib,
                titleName: "Bilangan Naib Pengerusi",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: setiaUsaha,
                titleName: "Setiausaha",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahSetiaUsaha,
                titleName: "Bilangan Setiausaha",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: penolongSetiaUsaha,
                titleName: "Penolong Setiausaha",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahPenolongSetiaUsaha,
                titleName: "Bilangan Penolong Setiausaha",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: bendahari,
                titleName: "Bendahari",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahBendahari,
                titleName: "Bilangan Bendahari",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: penolongBendahari,
                titleName: "Penolong Bendahari",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahPenolongBendahari,
                titleName: "Bilangan Penolong Bendahari",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: ahliBiasa,
                titleName: "Ahli Jawatankuasa Biasa",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahAhliBiasa,
                titleName: "Bilangan Ahli Jawatankuasa Biasa",
              },
            ],
            clause: "clause6",
            clauseCount: 6,
            clauseContentId,
          });

          if (!isEditingContent) {
            setIsLoadingUpdate(false);
            setDialogSaveOpen(false);
          }
        },
      }
    );
  };

  const totalBilangan =
    parseInt(jumlahPengerusi || 0) +
    parseInt(jumlahTimbalan || 0) +
    parseInt(jumlahNaib || 0) +
    parseInt(jumlahSetiaUsaha || 0) +
    parseInt(jumlahPenolongSetiaUsaha || 0) +
    parseInt(jumlahBendahari || 0) +
    parseInt(jumlahPenolongBendahari || 0) +
    parseInt(jumlahAhliBiasa || 0);

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("positionOfAuthority")} 
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("jenisMesyuaratAgung")} 
              <Typography sx={{ display: "inline", color: "red" }}>
                                        *
              </Typography></Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth required error={!!formErrors.pemilihanAjk}>
              <Select
                size="small"
                value={pemilihanAjk}
                displayEmpty
                onChange={(e) => {
                  setPemilihanAjk(e.target.value as string);
                  if ((e.target.value as string) == t("annual")) {
                    setTempohJawatan(t("setahun"));
                  } else if ((e.target.value as string) == t("biennial")) {
                    setTempohJawatan(t("duaTahun"));
                  }
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    pemilihanAjk: "",
                  }));
                }}
              >
                <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
              </Select>
            </FormControl>
            {formErrors.pemilihanAjk && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.pemilihanAjk}
              </FormHelperText>
            )}
          </Grid>
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("electionPeriod")}
                  <Typography sx={{ display: "inline", color: "red" }}>
                                                    *
                  </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth required error={!!formErrors.tempohJawatan}>
              <Select
                size="small"
                value={tempohJawatan}
                disabled={pemilihanAjk == t("biennial")}
                displayEmpty
                onChange={(e) => {
                  setTempohJawatan(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    tempohJawatan: "",
                  }));
                }}
              >
                <MenuItem value={t("setahun")}>{t("setahun")}</MenuItem>
                <MenuItem value={t("duaTahun")}>{t("duaTahun")}</MenuItem>
              </Select>
            </FormControl>
            {formErrors.tempohJawatan && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.tempohJawatan}
              </FormHelperText>
            )}
          </Grid>
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("meetingFrequency")}
              <Typography sx={{ display: "inline", color: "red" }}>
                                                    *
              </Typography></Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="4"
              fullWidth
              required
              value={kekerapan}
              onChange={(e) => {
                setKekerapan(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  kekerapan: "",
                }));
              }}
              error={!!formErrors.kekerapan}
              helperText={formErrors.kekerapan}
              InputProps={{
                endAdornment: (
                  <Typography sx={{ ...labelStyle, mt: 1 }}>
                    {t("times")}
                  </Typography>
                ),
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item />
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("periodOfDefendingOneselfTwo")}    
              <Typography sx={{ display: "inline", color: "red" }}>
                                        *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              value={tempohPelucutan}
              onChange={(e) => {
                setTempohPelucutan(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  tempohPelucutan: "",
                }));
              }}
              error={!!formErrors.tempohPelucutan}
              helperText={formErrors.tempohPelucutan}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.tempohPelucutanWaktu}
            >
              <Select
                size="small"
                value={tempohPelucutanWaktu}
                displayEmpty
                onChange={(e) => {
                  setTempohPelucutanWaktu(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    tempohPelucutanWaktu: "",
                  }));
                }}
                disabled
              >
                <MenuItem value={"day"}>{t("day")}</MenuItem>
                <MenuItem value={"week"}>{t("week")}</MenuItem>
                <MenuItem value={"month"}>{t("month")}</MenuItem>
                <MenuItem value={"year"}>{t("year")}</MenuItem>
              </Select>
            </FormControl>
            {formErrors.tempohPelucutanWaktu && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.tempohPelucutanWaktu}
              </FormHelperText>
            )}
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("ahliJawatanKuasa")} 
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>     
            <Typography variant="subtitle1" sx={sectionStyle}>{t("NumberofPositions")}</Typography> 
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" sx={sectionStyle}>{t("JobTitle")}</Typography>
          </Grid>
        </Grid>
        <Grid container spacing={2} >
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              disabled
              sx={{ background: "#E8E9E8" }}
              required
              value={jumlahPengerusi}
              onChange={(e) => {
                setJumlahPengerusi(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahPengerusi: "",
                }));
              }}
              error={!!formErrors.jumlahPengerusi}
              helperText={formErrors.jumlahPengerusi}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required error={!!formErrors.pengerusi}>
              <Select
                size="small"
                value={pengerusi}
                displayEmpty
                onChange={(e) => {
                  setPengerusi(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    pengerusi: "",
                  }));
                }}
              >
                <MenuItem value={t("chairman")}>
                  {t("chairman")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                <MenuItem value={t("presiden")}>
                  {t("presiden")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                <MenuItem value={t("pengarah")}>
                  {t("pengarah")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                <MenuItem value={t("thepresident")}>
                  {t("thepresident")}
                </MenuItem>
              </Select>
            </FormControl>
            {formErrors.pengerusi && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.pengerusi}
              </FormHelperText>
            )}
          </Grid>
          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required={isRequiredConstitution}
              value={jumlahTimbalan}
              onChange={(e) => {
                console.log("e.target.value", e.target.value);
                setJumlahTimbalan(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahTimbalan: "",
                }));
              }}
              error={!!formErrors.jumlahTimbalan}
              helperText={formErrors.jumlahTimbalan}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            {isRequiredConstitution ? (
              <FormControl fullWidth required error={!!formErrors.timbalan}>
                <Select
                  size="small"
                  value={timbalan}
                  displayEmpty
                  onChange={(e) => {
                    setTimbalan(e.target.value as string);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      timbalan: "",
                    }));
                  }}
                >
                  <MenuItem value={t("timbalanPengerusi")}>
                    {t("timbalanPengerusi")}
                  </MenuItem>
                  <MenuItem value={t("vicePresident")}>
                    {t("vicePresident")}
                  </MenuItem>
                  <MenuItem value={t("timbalanPengarah")}>
                    {t("timbalanPengarah")}
                  </MenuItem>
                </Select>
              </FormControl>
            ) : (
              <FormControl fullWidth error={!!formErrors.timbalan}>
                <Select
                  size="small"
                  value={timbalan}
                  displayEmpty
                  onChange={(e) => {
                    setTimbalan(e.target.value as string);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      timbalan: "",
                    }));
                  }}
                >
                  <MenuItem value={t("timbalanPengerusi")}>
                    {t("timbalanPengerusi")}
                  </MenuItem>
                  <MenuItem value={t("vicePresident")}>
                    {t("vicePresident")}
                  </MenuItem>
                  <MenuItem value={t("timbalanPengarah")}>
                    {t("timbalanPengarah")}
                  </MenuItem>
                </Select>
              </FormControl>
            )}

            {formErrors.timbalan && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.timbalan}
              </FormHelperText>
            )}
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required={isRequiredConstitution}
              value={jumlahNaib}
              onChange={(e) => {
                setJumlahNaib(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahNaib: "",
                }));
              }}
              error={!!formErrors.jumlahNaib}
              helperText={formErrors.jumlahNaib}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            {isRequiredConstitution ? (
              <FormControl fullWidth required error={!!formErrors.naib}>
                <Select
                  size="small"
                  value={naib}
                  displayEmpty
                  onChange={(e) => {
                    setNaib(e.target.value as string);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      naib: "",
                    }));
                  }}
                >
                  <MenuItem value={t("naibPengerusi")}>
                    {t("naibPengerusi")}
                  </MenuItem>
                  <MenuItem value={t("naibPresiden")}>
                    {t("naibPresiden")}
                  </MenuItem>
                  <MenuItem value={t("naibPengarah")}>
                    {t("naibPengarah")}
                  </MenuItem>
                </Select>
              </FormControl>
            ) : (
              <FormControl fullWidth error={!!formErrors.naib}>
                <Select
                  size="small"
                  value={naib}
                  displayEmpty
                  onChange={(e) => {
                    setNaib(e.target.value as string);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      naib: "",
                    }));
                  }}
                >
                  <MenuItem value={t("naibPengerusi")}>
                    {t("naibPengerusi")}
                  </MenuItem>
                  <MenuItem value={t("naibPresiden")}>
                    {t("naibPresiden")}
                  </MenuItem>
                  <MenuItem value={t("naibPengarah")}>
                    {t("naibPengarah")}
                  </MenuItem>
                </Select>
              </FormControl>
            )}

            {formErrors.naib && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.naib}
              </FormHelperText>
            )}
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              disabled
              sx={{ background: "#E8E9E8" }}
              value={jumlahSetiaUsaha}
              onChange={(e) => {
                setJumlahSetiaUsaha(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahSetiaUsaha: "",
                }));
              }}
              error={!!formErrors.jumlahSetiaUsaha}
              helperText={formErrors.jumlahSetiaUsaha}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required error={!!formErrors.setiaUsaha}>
              <Select
                size="small"
                value={setiaUsaha}
                displayEmpty
                onChange={(e) => {
                  setSetiaUsaha(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    setiaUsaha: "",
                  }));
                }}
              >
                <MenuItem value={t("secretary")}>
                  {t("secretary")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                <MenuItem value={t("generalSecretary")}>
                  {t("generalSecretary")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
              </Select>
            </FormControl>
            {formErrors.setiaUsaha && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.setiaUsaha}
              </FormHelperText>
            )}
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required={isRequiredConstitution}
              value={jumlahPenolongSetiaUsaha}
              onChange={(e) => {
                setJumlahPenolongSetiaUsaha(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahPenolongSetiaUsaha: "",
                }));
              }}
              error={!!formErrors.jumlahPenolongSetiaUsaha}
              helperText={formErrors.jumlahPenolongSetiaUsaha}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            {isRequiredConstitution ? (
              <FormControl
                fullWidth
                required
                error={!!formErrors.penolongSetiaUsaha}
              >
                <Select
                  size="small"
                  value={penolongSetiaUsaha}
                  displayEmpty
                  onChange={(e) => {
                    setPenolongSetiaUsaha(e.target.value as string);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      penolongSetiaUsaha: "",
                    }));
                  }}
                >
                  <MenuItem value={t("asistantSecretary")}>
                    { t("asistantSecretary")}
                  </MenuItem>
                  <MenuItem value={t("generalAssistantSecretary")}>
                    {t("generalAssistantSecretary")}
                  </MenuItem>
                </Select>
              </FormControl>
            ) : (
              <FormControl fullWidth error={!!formErrors.penolongSetiaUsaha}>
                <Select
                  size="small"
                  value={penolongSetiaUsaha}
                  displayEmpty
                  onChange={(e) => {
                    setPenolongSetiaUsaha(e.target.value as string);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      penolongSetiaUsaha: "",
                    }));
                  }}
                >
                  <MenuItem value={t("asistantSecretary")}>
                    {t("asistantSecretary")}
                  </MenuItem>
                  <MenuItem value={t("generalAssistantSecretary")}>
                    {t("generalAssistantSecretary")}
                  </MenuItem>
                </Select>
              </FormControl>
            )}

            {formErrors.penolongSetiaUsaha && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.penolongSetiaUsaha}
              </FormHelperText>
            )}
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              disabled
              sx={{ background: "#E8E9E8" }}
              value={jumlahBendahari}
              onChange={(e) => {
                setJumlahBendahari(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahBendahari: "",
                }));
              }}
              error={!!formErrors.jumlahBendahari}
              helperText={formErrors.jumlahBendahari}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required error={!!formErrors.bendahari}>
              <Select
                size="small"
                value={bendahari}
                displayEmpty
                onChange={(e) => {
                  setBendahari(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bendahari: "",
                  }));
                }}
              >
                <MenuItem value={t("treasurer")}>
                  {t("bendahari")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                <MenuItem value={t("chiefTreasurer")}>
                  {t("chiefTreasurer")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                {/* <MenuItem value={t("honoraryTreasurer")}>
                  {t("honoraryTreasurer")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem> */}
              </Select>
            </FormControl>
            {formErrors.bendahari && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.bendahari}
              </FormHelperText>
            )}
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required={isRequiredConstitution}
              value={jumlahPenolongBendahari}
              onChange={(e) => {
                setJumlahPenolongBendahari(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahPenolongBendahari: "",
                }));
              }}
              error={!!formErrors.jumlahPenolongBendahari}
              helperText={formErrors.jumlahPenolongBendahari}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            {isRequiredConstitution ? (
              <FormControl
                fullWidth
                required
                error={!!formErrors.penolongBendahari}
              >
                <Select
                  size="small"
                  value={penolongBendahari}
                  displayEmpty
                  onChange={(e) => {
                    setPenolongBendahari(e.target.value as string);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      penolongBendahari: "",
                    }));
                  }}
                >
                  <MenuItem value={t("asistantTreasurer")}>
                    {t("asistantTreasurer")}
                  </MenuItem>
                  <MenuItem value={t("chiefAssistantTreasurer")}>
                    {t("chiefAssistantTreasurer")}
                  </MenuItem>
                  {/* <MenuItem value={t("honoraryAssistantTreasurer")}>
                    {t("honoraryAssistantTreasurer")}
                  </MenuItem> */}
                </Select>
              </FormControl>
            ) : (
              <FormControl fullWidth error={!!formErrors.penolongBendahari}>
                <Select
                  size="small"
                  value={penolongBendahari}
                  displayEmpty
                  onChange={(e) => {
                    setPenolongBendahari(e.target.value as string);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      penolongBendahari: "",
                    }));
                  }}
                >
                  <MenuItem value={t("asistantTreasurer")}>
                    {t("asistantTreasurer")}
                  </MenuItem>
                  <MenuItem value={t("chiefAssistantTreasurer")}>
                    {t("chiefAssistantTreasurer")}
                  </MenuItem>
                  {/* <MenuItem value={t("honoraryAssistantTreasurer")}>
                    {t("honoraryAssistantTreasurer")}
                  </MenuItem> */}
                </Select>
              </FormControl>
            )}

            {formErrors.penolongBendahari && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.penolongBendahari}
              </FormHelperText>
            )}
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required={isRequiredConstitution}
              value={jumlahAhliBiasa}
              onChange={(e) => {
                setJumlahAhliBiasa(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  jumlahAhliBiasa: "",
                }));
              }}
              error={!!formErrors.jumlahAhliBiasa}
              helperText={formErrors.jumlahAhliBiasa}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            {isRequiredConstitution ? (
              <FormControl fullWidth required error={!!formErrors.ahliBiasa}>
                <Select
                  size="small"
                  value={ahliBiasa}
                  displayEmpty
                  onChange={(e) => {
                    setAhliBiasa(e.target.value as string);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      ahliBiasa: "",
                    }));
                  }}
                >
                  <MenuItem value={t("ordinaryCommitteeMember")}>
                    {t("ordinaryCommitteeMember")}
                  </MenuItem>
                </Select>
              </FormControl>
            ) : (
              <FormControl fullWidth error={!!formErrors.ahliBiasa}>
                <Select
                  size="small"
                  value={ahliBiasa}
                  displayEmpty
                  onChange={(e) => {
                    setAhliBiasa(e.target.value as string);
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      ahliBiasa: "",
                    }));
                  }}
                >
                  <MenuItem value={t("ordinaryCommitteeMember")}>
                    {t("ordinaryCommitteeMember")}
                  </MenuItem>
                </Select>
              </FormControl>
            )}

            {formErrors.ahliBiasa && (
              <FormHelperText sx={{ color: "red" }}>
                {formErrors.ahliBiasa}
              </FormHelperText>
            )}
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={8}>
            <Typography
              sx={{
                fontWeight: "500 !important",
                color: "#666666",
                border: "1px solid #DADADA",
                borderRadius: "5px",
                py: 1,
                display: "flex",
                justifyContent: "center",
              }}
            >
              {totalBilangan} Bilangan Ahli Jawatankuasa
            </Typography>
          </Grid>
          <Grid item />
        </Grid>
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>
      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            //const tempArrayAJK: AJKState[] = [];
            const errors = validateForm();
            if (Object.keys(errors).length > 0) {
              setFormErrors(errors);
              return;
            }
            if (totalBilangan < 7) {
              setShowErrorAjk(true);
              return;
            }
            const designationCode = [];
            for (let i = 0; i < jumlahPengerusi; i++) {
              designationCode.push(
                CommiteeEnum[pengerusi as keyof typeof CommiteeEnum]
              );
            }
            for (let i = 0; i < jumlahTimbalan; i++) {
              designationCode.push(
                CommiteeEnum[timbalan as keyof typeof CommiteeEnum]
              );
            }
            for (let i = 0; i < jumlahNaib; i++) {
              designationCode.push(
                CommiteeEnum[naib as keyof typeof CommiteeEnum]
              );
            }
            for (let i = 0; i < jumlahSetiaUsaha; i++) {
              designationCode.push(
                CommiteeEnum[setiaUsaha as keyof typeof CommiteeEnum]
              );
            }
            for (let i = 0; i < jumlahPenolongSetiaUsaha; i++) {
              designationCode.push(
                CommiteeEnum[penolongSetiaUsaha as keyof typeof CommiteeEnum]
              );
            }
            for (let i = 0; i < jumlahBendahari; i++) {
              designationCode.push(
                CommiteeEnum[bendahari as keyof typeof CommiteeEnum]
              );
            }
            for (let i = 0; i < jumlahPenolongBendahari; i++) {
              designationCode.push(
                CommiteeEnum[penolongBendahari as keyof typeof CommiteeEnum]
              );
            }
            for (let i = 0; i < jumlahAhliBiasa; i++) {
              designationCode.push(CommiteeEnum["Ahli Jawatankuasa Biasa"]);
            }
            //dispatch(setAJK(tempArrayAJK));*/
            if (isEdit || committeeData.length > 0) {
            } else {
              createClauseContent(
                {
                  resource: "society/committee/createForRegistration",
                  values: {
                    societyId,
                    designationCode,
                  },
                  meta: {
                    headers: {
                      portal: localStorage.getItem("portal"),
                      authorization: `Bearer ${localStorage.getItem(
                        "refine-auth"
                      )}`,
                    },
                  },
                  successNotification: false,
                },
                {
                  onSuccess(data: any, variables: any, context: any) {},
                }
              );
            }

            if (isEdit) {
              setDialogSaveOpen(true);
            } else {
              handleSaveContent({
                i18n,
                societyId,
                societyName: namaPertubuhan,
                dataId,
                isEdit,
                clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tempohJawatan,
                    titleName: "Tempoh Pelantikan Jawatankuasa",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: pemilihanAjk,
                    titleName: "Jenis Mesyuarat Agung",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: kekerapan,
                    titleName: "Kekerapan Mesyuarat Jawatankuasa",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tempohPelucutan,
                    titleName:
                      "Bilangan tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tempohPelucutanWaktu,
                    titleName:
                      "Tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: pengerusi,
                    titleName: "Pengerusi",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahPengerusi,
                    titleName: "Bilangan Pengerusi",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: timbalan,
                    titleName: "Timbalan Pengerusi",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahTimbalan,
                    titleName: "Bilangan Timbalan Pengerusi",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: naib,
                    titleName: "Naib Pengerusi",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahNaib,
                    titleName: "Bilangan Naib Pengerusi",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: setiaUsaha,
                    titleName: "Setiausaha",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahSetiaUsaha,
                    titleName: "Bilangan Setiausaha",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: penolongSetiaUsaha,
                    titleName: "Penolong Setiausaha",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahPenolongSetiaUsaha,
                    titleName: "Bilangan Penolong Setiausaha",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bendahari,
                    titleName: "Bendahari",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahBendahari,
                    titleName: "Bilangan Bendahari",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: penolongBendahari,
                    titleName: "Penolong Bendahari",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahPenolongBendahari,
                    titleName: "Bilangan Penolong Bendahari",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: ahliBiasa,
                    titleName: "Ahli Jawatankuasa Biasa",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahAhliBiasa,
                    titleName: "Bilangan Ahli Jawatankuasa Biasa",
                  },
                ],
                clause: "clause6",
                clauseCount: 6,
                clauseContentId,
              });
            }
          }}
          disabled={
            isCreatingContent ||
            isEditingContent ||
            !checked ||
            !pengerusi ||
            (!timbalan && isRequiredConstitution) ||
            (!naib && isRequiredConstitution) ||
            !setiaUsaha ||
            (!penolongSetiaUsaha && isRequiredConstitution) ||
            !bendahari ||
            (!penolongBendahari && isRequiredConstitution) ||
            (!ahliBiasa && isRequiredConstitution) ||
            !tempohJawatan ||
            !pemilihanAjk ||
            !tempohPelucutanWaktu
          }
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
      <MessageDialog
        open={showErrorAjk}
        onClose={() => setShowErrorAjk(false)}
        message={t("moreThan7ajk")}
      />
      <DialogConfirmation
        open={dialogSaveOpen}
        // onClose={() => setDialogSaveOpen(false)}
        onConfirmationText={t("AJKNoUpdateConfirmation")}
        onAction={handleConfirm}
        onClose={() => setDialogSaveOpen(false)}
        isMutating={isLoadingUpdate || isEditingContent}
      />
    </>
  );
};

export default FasalContentEnam;
