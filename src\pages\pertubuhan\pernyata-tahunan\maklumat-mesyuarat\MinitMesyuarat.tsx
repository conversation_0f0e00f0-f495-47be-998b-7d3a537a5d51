import {
  <PERSON>,
  Icon<PERSON>utton,
  Grid,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { useQuery } from "@/helpers";
import { Meeting } from "../interface";
import { EyeIcon } from "../../../../components/icons";

type props = {
  sectionStyle: any;
  meeting: Meeting | undefined;
};

const MinitMesyuarat: React.FC<props> = ({ sectionStyle, meeting }) => {
  const { t } = useTranslation();
  const { id } = useParams();
  const [fileName, setFileName] = useState<string>("");
  const [fileUrl, setFileUrl] = useState<string>("");

  const { refetch: fetchDocuments, isLoading: fetchDocumentsIsLoading } =
    useQuery({
      url: `society/document/documentByParam`,
      filters: [
        { field: "societyId", operator: "eq", value: id },
        { field: "meetingId", operator: "eq", value: meeting?.id },
      ],
      onSuccess: (data) => {
        const fileInfo = data?.data?.data?.[0];
        if (fileInfo) {
          setFileName(fileInfo?.name);
          setFileUrl(fileInfo?.url);
        }
      },
    });

  const downloadFile = (filePath: string) => {
    window.open(filePath, "_blank", "noreferrer,noopener");
  };

  return (
    <Box
      sx={{
        p: 3,
        border: "1px solid #D9D9D9",
        borderRadius: "14px",
      }}
    >
      <Typography variant="subtitle1" sx={sectionStyle}>
        {t("meetingMinutes")}
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={4}>
          <Typography
            variant="body1"
            sx={{
              color: "#666666",
              fontWeight: "400 !important",
              fontSize: "14px",
            }}
          >
            {t("minitMesyuarat")} <span style={{ color: "red" }}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <Grid item xs={12}>
            <Box
              sx={{
                border: "1px solid #DADADA",
                borderRadius: "8px",
                p: 3,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: 1,
                backgroundColor: "#FFFFFF",
                "&:hover": {
                  backgroundColor: "#F9FAFB",
                },
              }}
            >
              <Box
                sx={{
                  width: "100%",
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  gap: 1,
                }}
              >
                <Box
                  sx={{
                    width: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Box>
                    <Typography
                      sx={{
                        color: "#222222",
                        fontSize: "14px",
                        textAlign: "center",
                      }}
                    >
                      {fileName ? fileName : "-"}
                    </Typography>
                  </Box>
                  <IconButton
                    onClick={() => downloadFile(fileUrl)}
                    sx={{
                      padding: 0,
                    }}
                  >
                    <EyeIcon color="#666666" />
                  </IconButton>
                </Box>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MinitMesyuarat;
