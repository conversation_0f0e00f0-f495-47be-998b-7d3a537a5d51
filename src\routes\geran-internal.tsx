/* eslint-disable react-refresh/only-export-components */
import { Navigate, Route } from "react-router-dom";
import { lazy, Suspense } from "react";
import { useSelector } from "react-redux";
import { getUserPortal } from "@/redux/userReducer";
import { PORTAL_INTERNAL } from "@/helpers";
import { registerRoutes } from "@/helpers/routeDetector";
import { RouteGuard } from "@/components/RouteGuard";

import { PageLoader } from "@/components";
import SenaraiPermohonanKueri from "@/pages/geran-internal/semakan-permohonan/senarai-permohonan-kueri";
import GarisPanduan from "@/pages/geran-internal/pengurusan-borang/GarisPanduan";
import MensyuaratPenilaian from "@/pages/geran-internal/semakan-permohonan/MensyuaratPenilaian";
import LaporanPelaksanaanDetail from "@/pages/geran-internal/laporan-pelaksanaan/LaporanPelaksanaanDetail";
import LaporanPermohonanDetail from "@/pages/geran-internal/laporan-permohonan/LaporanPermohonanDetail";
const GeranInternalLayout = lazy(() => import("@/pages/geran-internal/Layout"));
const CarianPermohonan = lazy(
  () => import("@/pages/geran-internal/carian-permohonan")
);
const LaporanPelaksanaan = lazy(
  () => import("@/pages/geran-internal/laporan-pelaksanaan")
);
const LaporanPermohonan = lazy(
  () => import("@/pages/geran-internal/laporan-permohonan")
);
const SemakanPermohonan = lazy(
  () => import("@/pages/geran-internal/semakan-permohonan")
);
const Pembayaran = lazy(() => import("@/pages/geran-internal/pembayaran"));
const PermohonanLanjutan = lazy(
  () => import("@/pages/geran-internal/permohonan-lanjutan")
);
const PengurusanBorang = lazy(
  () => import("@/pages/geran-internal/pengurusan-borang")
);

// Layout component to wrap all geran-internal routes with protection
const GeranInternalGuardedLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Suspense fallback={<PageLoader />}>
      <GeranInternalLayout />
    </Suspense>
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example route registrations:
  // '/geran-internal': 'internal',
  // '/geran-internal/carian-permohonan': 'internal',
  // '/geran-internal/laporan-pelaksanaan': 'internal',
  // '/geran-internal/laporan-permohonan': 'internal',
  // '/geran-internal/semakan-permohonan': 'internal',
  // '/geran-internal/pembayaran': 'internal',
  // '/geran-internal/permohonan-lanjutan': 'internal',
  // '/geran-internal/pengurusan-borang': 'internal',
  // Add your route registrations here
});

const routeComponents = (
  <Route
    path="geran-internal"
    element={<GeranInternalGuardedLayout />}
  >
    <Route index element={<Navigate to="carian-permohonan" />} />
    <Route path="carian-permohonan" element={<CarianPermohonan />} />
    <Route path="laporan-pelaksanaan" element={<LaporanPelaksanaan />} />
    <Route
      path="laporan-pelaksanaan/mensyuarat-penilaian/:applicationGeranId"
      element={<LaporanPelaksanaanDetail />}
    />
    <Route path="laporan-permohonan" element={<LaporanPermohonan />} />
    <Route
      path="laporan-permohonan/mensyuarat-penilaian/:applicationGeranId"
      element={<LaporanPermohonanDetail />}
    />
    <Route path="semakan-permohonan" element={<SemakanPermohonan />}></Route>
    <Route
      path="semakan-permohonan/mensyuarat-penilaian/:applicationGeranId"
      element={<SenaraiPermohonanKueri />}
    />
    <Route
      path="semakan-permohonan/mensyuarat-penilaian"
      element={<MensyuaratPenilaian />}
    />
    <Route path="pembayaran" element={<Pembayaran />} />
    <Route path="permohonan-lanjutan" element={<PermohonanLanjutan />} />
    <Route path="pengurusan-borang" element={<PengurusanBorang />} />
    <Route path="pengurusan-borang/garis-panduan" element={<GarisPanduan />} />
  </Route>
);

export const geranInternal = {
  routes:
    localStorage.getItem("portal") === PORTAL_INTERNAL &&
    import.meta.env.VITE_APP_ENV !== "production" ? (
      <></>
    ) : null,
};

export const useGeranInternalRoutes = () => {
  const userPortal = useSelector(getUserPortal);
  const routes =
    userPortal === parseInt(PORTAL_INTERNAL) &&
    import.meta.env.VITE_APP_ENV !== "production" &&
    routeComponents;

  return {
    routes,
  };
};
