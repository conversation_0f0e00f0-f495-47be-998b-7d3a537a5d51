/* eslint-disable react-refresh/only-export-components */
import { Navigate, Route } from "react-router-dom";
import { lazy, Suspense } from "react";
import { useSelector } from "react-redux";
import { getUserPortal } from "@/redux/userReducer";
import { PORTAL_INTERNAL } from "@/helpers";
import { registerRoutes } from "@/helpers/routeDetector";
import { RouteGuard } from "@/components/RouteGuard";

import { PageLoader } from "@/components";
import SenaraiPermohonanKueri from "@/pages/geran-internal/semakan-permohonan/senarai-permohonan-negeri";
import GarisPanduan from "@/pages/geran-internal/pengurusan-borang/GarisPanduan";
import MensyuaratPenilaian from "@/pages/geran-internal/semakan-permohonan/MensyuaratPenilaian";
import LaporanPelaksanaanDetail from "@/pages/geran-internal/laporan-pelaksanaan/LaporanPelaksanaanDetail";
import LaporanPermohonanDetail from "@/pages/geran-internal/laporan-permohonan/LaporanPermohonanDetail";
import SyaratGeran from "@/pages/geran-internal/pengurusan-borang/SyaratGeran";
import CiptaBorangDetail from "@/pages/geran-internal/pengurusan-borang/CiptaBorangDetail";
import SyaratGeranUpdate from "@/pages/geran-internal/pengurusan-borang/SyaratGeranUpdate";
import CairanPermohonanDetail from "@/pages/geran-internal/carian-permohonan/CarianPermohonanDetail";
import CarianPermohonanDetail from "@/pages/geran-internal/carian-permohonan/CarianPermohonanDetail";
import SenaraiPermohonanNegeri from "@/pages/geran-internal/semakan-permohonan/senarai-permohonan-negeri";
import SenaraiPermohonanIbuPejabat from "@/pages/geran-internal/semakan-permohonan/senarai-permohonan-ibu-pejabat";
const GeranInternalLayout = lazy(() => import("@/pages/geran-internal/Layout"));
const CarianPermohonan = lazy(
  () => import("@/pages/geran-internal/carian-permohonan")
);
const LaporanPelaksanaan = lazy(
  () => import("@/pages/geran-internal/laporan-pelaksanaan")
);
const LaporanPermohonan = lazy(
  () => import("@/pages/geran-internal/laporan-permohonan")
);
const SemakanPermohonan = lazy(
  () => import("@/pages/geran-internal/semakan-permohonan")
);
const Pembayaran = lazy(() => import("@/pages/geran-internal/pembayaran"));
const PermohonanLanjutan = lazy(
  () => import("@/pages/geran-internal/permohonan-lanjutan")
);
const PengurusanBorang = lazy(
  () => import("@/pages/geran-internal/pengurusan-borang")
);

// Layout component to wrap all geran-internal routes with protection
const GeranInternalGuardedLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === "development"}
  >
    <Suspense fallback={<PageLoader />}>
      <GeranInternalLayout />
    </Suspense>
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Base
  "/geran-internal": "internal",

  // Carian Permohonan
  "/geran-internal/carian-permohonan": "internal",
  "/geran-internal/carian-permohonan/:grantApplicationId": "internal",

  // Laporan Pelaksanaan
  "/geran-internal/laporan-pelaksanaan": "internal",
  "/geran-internal/laporan-pelaksanaan/mensyuarat-penilaian/:applicationGeranId/:societyId": "internal",

  // Laporan Permohonan
  "/geran-internal/laporan-permohonan": "internal",
  "/geran-internal/laporan-permohonan/mensyuarat-penilaian/:applicationGeranId/:societyId": "internal",

  // Semakan Permohonan
  "/geran-internal/semakan-permohonan": "internal",
  "/geran-internal/semakan-permohonan/mensyuarat-penilaian": "internal",
  "/geran-internal/semakan-permohonan/mensyuarat-penilaian/:applicationGeranId/:societyId": "internal",
  "/geran-internal/semakan-permohonan/mensyuarat-penilaian/negeri/:applicationGeranId/:societyId": "internal",

  // Pembayaran
  "/geran-internal/pembayaran": "internal",

  // Permohonan Lanjutan
  "/geran-internal/permohonan-lanjutan": "internal",

  // Pengurusan Borang
  "/geran-internal/pengurusan-borang": "internal",
  "/geran-internal/pengurusan-borang/:grantTemplateId": "internal",
  "/geran-internal/pengurusan-borang/geran/garis-panduan": "internal",
  "/geran-internal/pengurusan-borang/syarat-geran": "internal",
  "/geran-internal/pengurusan-borang/:grantTemplateId/syarat-geran": "internal",
  // Add your route registrations here
});

const routeComponents = (
  <Route path="geran-internal" element={<GeranInternalGuardedLayout />}>
    <Route index element={<Navigate to="carian-permohonan" />} />
    <Route path="carian-permohonan" element={<CarianPermohonan />} />
    <Route path="carian-permohonan/:grantApplicationId" element={<CarianPermohonanDetail />} />
    <Route path="laporan-pelaksanaan" element={<LaporanPelaksanaan />} />
    <Route
      path="laporan-pelaksanaan/mensyuarat-penilaian/:applicationGeranId/:societyId"
      element={<LaporanPelaksanaanDetail />}
    />
    <Route path="laporan-permohonan" element={<LaporanPermohonan />} />
    <Route
      path="laporan-permohonan/mensyuarat-penilaian/:applicationGeranId/:societyId"
      element={<LaporanPermohonanDetail />}
    />
    <Route path="semakan-permohonan" element={<SemakanPermohonan />}></Route>
    <Route
      path="semakan-permohonan/mensyuarat-penilaian/:applicationGeranId/:societyId"
      element={<SenaraiPermohonanIbuPejabat />}
    />
    <Route
      path="semakan-permohonan/mensyuarat-penilaian/negeri/:applicationGeranId/:societyId"
      element={<SenaraiPermohonanNegeri />}
    />
    <Route
      path="semakan-permohonan/mensyuarat-penilaian"
      element={<MensyuaratPenilaian />}
    />
    <Route path="pembayaran" element={<Pembayaran />} />
    <Route path="permohonan-lanjutan" element={<PermohonanLanjutan />} />
    <Route path="pengurusan-borang" element={<PengurusanBorang />} />
    <Route
      path="pengurusan-borang/:grantTemplateId"
      element={<CiptaBorangDetail />}
    />
    <Route
      path="pengurusan-borang/geran/garis-panduan"
      element={<GarisPanduan />}
    />
    <Route path="pengurusan-borang/syarat-geran" element={<SyaratGeran />} />
     <Route path="pengurusan-borang/:grantTemplateId/syarat-geran" element={<SyaratGeranUpdate />} />
  </Route>
);

export const geranInternal = {
  routes:
    localStorage.getItem("portal") === PORTAL_INTERNAL &&
    import.meta.env.VITE_APP_ENV !== "production" ? (
      <></>
    ) : null,
};

export const useGeranInternalRoutes = () => {
  const userPortal = useSelector(getUserPortal);
  const routes =
    userPortal === parseInt(PORTAL_INTERNAL) &&
    import.meta.env.VITE_APP_ENV !== "production" &&
    routeComponents;

  return {
    routes,
  };
};
