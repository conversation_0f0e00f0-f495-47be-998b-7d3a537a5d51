import React, { useCallback, useEffect, useState } from "react";
import Stack from "@mui/material/Stack";
import IconButton from "@mui/material/IconButton";
import Divider from "@mui/material/Divider";
import Box from "@mui/material/Box";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { ApplicationStatusList } from "../../../../helpers/enums";
import {
  Button,
  Grid,
  InputAdornment,
  SvgIcon,
  debounce,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  Typography,
  DialogActions,
  CircularProgress,
} from "@mui/material";
import FilterListIcon from "@mui/icons-material/FilterList";
import { Search } from "@mui/icons-material";
import {
  formatDateToDDMMYYYY,
  removeLocalStorage,
} from "../../../../helpers/utils";
import useMutation from "../../../../helpers/hooks/useMutation";
import DataTable, { IColumn } from "../../../../components/datatable";
import { useForm } from "@refinedev/react-hook-form";
import useQuery from "../../../../helpers/hooks/useQuery";
import { FieldValues } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { setIsKuiri, setIsViewPindaan } from "@/redux/fasalReducer";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { ButtonPrimary, ButtonText } from "@/components";
import dayjs from "dayjs";
import { EditIcon, EyeIcon, TrashIcon } from "@/components/icons";
import { DeleteIcon } from "@/components/icons/delete";
export interface SenaraiPindaan {
  id: number;
  jenisPindaan: string;
  statusPermohonan: "Lulus" | "Belum dihantar";
  tarikhMohon: string;
  tarikhHantar: string | null;
  tarikhKeputusan: string | null;
}

export const ListSenaraiPindaan = ({
  isBlackListed,
}: {
  isBlackListed?: boolean;
}) => {
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [allAmendmentList, setAllAmendmentList] = useState<any>([]);
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");
  const [selectDeleteInfo, setSelectDeleteInfo] = useState<any>(null);
  const [deletePopup, setDeletePopup] = useState(false);

  const { watch, setValue } = useForm<FieldValues>({
    defaultValues: {
      searchQuery: "",
      page: 1,
      rowsPerPage: 10,
    },
  });

  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);
  const isAccessible = !isBlackListed && (isManager || isAliranTugasAccess);

  const {
    data: amendmentList,
    refetch: refetchAmendmentList,
    isLoading: fetchAmendmentListIsLoading,
  } = useQuery({
    url: `society/amendment/getAmendmentByParam`,
    filters: [
      { field: "pageSize", value: watch("rowsPerPage"), operator: "eq" },
      { field: "pageNo", value: watch("page"), operator: "eq" },
      { field: "societyId", value: id, operator: "eq" },
      {
        field: "searchQuery",
        value: debouncedSearchQuery,
        operator: "eq",
      },
    ],
    autoFetch: false,
  });

  const {
    data: updatedDate,
    fetch: updateAmendmentId,
    isLoading: updateAmendmentIdIsLoading,
  } = useMutation({
    url: "society/amendment/update/",
    method: "put",
    onSuccess: (data) => {
      refetchAmendmentList();
      setDeletePopup(false);
    },
  });

  const columns: IColumn[] = [
    {
      field: "goal",
      headerName: t("amendmentType"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return row.goal ? row.goal : "-";
      },
      cellClassName: "custom-cell",
    },
    {
      field: "createdDate",
      headerName: t("applicationDate"),
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box sx={{ textAlign: "center" }}>
            {row.createdDate
              ? dayjs(row.createdDate).format("DD-MM-YYYY")
              : "-"}
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "submissionDate",
      headerName: t("submissionDate"),
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box sx={{ textAlign: "center" }}>
            {row.submissionDate
              ? formatDateToDDMMYYYY(row.submissionDate)
              : "-"}
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "approvedDate",
      headerName: t("decisionDate"),
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box sx={{ textAlign: "center" }}>
            {row.approvedDate ? formatDateToDDMMYYYY(row.approvedDate) : "-"}
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "applicationStatusCode",
      headerName: t("status"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const status = ApplicationStatusList.find(
          (item) => item.id === Number(params?.row?.applicationStatusCode)
        );
        return status ? t(status.value) : t("-");
      },
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: (params: any) => {
        const row = params?.row;
        const status = Number(row?.applicationStatusCode);
        const pendingResult = status == 2;
        const belumDihantar = status == 1;
        const tolakLulus = status == 3 || status == 4;
        const isKuiri = status == 36;
        return (
          <Box
            sx={{
              display: "flex",
              alignItems: "right",
              justifyContent: "center",
            }}
          >
            {isAccessible ? (
              <>
                {pendingResult || tolakLulus ? (
                  <IconButton
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      alignContent: "center",
                    }}
                    onClick={() => {
                      clearStateData();
                      handleViewRow(row);
                    }}
                  >
                    <EyeIcon />
                  </IconButton>
                ) : (
                  <>
                    <IconButton
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignContent: "center",
                      }}
                      onClick={() => handleEditwRow(row)}
                    >
                      <EditIcon color="#1DC1C1" />
                    </IconButton>

                    {!isKuiri ? (
                      <IconButton
                        onClick={() => handleDeleteRow(row)}
                        sx={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <TrashIcon
                          sx={{
                            color: "#FF0000",
                          }}
                        />
                      </IconButton>
                    ) : (
                      " "
                    )}
                  </>
                )}
              </>
            ) : (
              <IconButton
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignContent: "center",
                }}
                onClick={() => {
                  clearStateData();
                  handleViewRow(row);
                }}
              >
                <EyeIcon />
              </IconButton>
            )}
          </Box>
        );
      },
    },
  ];

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchQuery(watch("searchQuery"));
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [watch("searchQuery")]);

  const clearStateData = () => {
    dispatch(setIsViewPindaan(null));
    dispatch(setIsKuiri(false));
    removeLocalStorage("amendmentId");
    removeLocalStorage("isViewMode");
  };

  const handleViewRow = (rowData: any) => {
    const { id, applicationStatusCode } = rowData;
    dispatch(setIsViewPindaan(id));
    navigate(`pindaan-perlembagaan`);
  };

  const handleDeleteRow = async (rowData: any) => {
    dispatch(setIsViewPindaan(null));
    const data = {
      id: rowData.id,
      meetingType: rowData.meetingType,
      meetingDate: rowData.meetingDate,
      meetingId: rowData.meetingId,
      societyId: id,
      applicationStatusCode: -1,
      status: "-001",
    };
    setSelectDeleteInfo(data);
    setDeletePopup(true);
  };

  const handleEditwRow = (rowData: any) => {
    const { id, applicationStatusCode } = rowData;
    dispatch(setIsViewPindaan(null));
    if (applicationStatusCode == 36) {
      dispatch(setIsKuiri(true));
    } else {
      dispatch(setIsKuiri(false));
    }
    navigate(`pindaan-perlembagaan?isEdit=${id}`);
  };

  const handleChangePage = (newPage: number) => {
    setValue("page", newPage);
  };

  useEffect(() => {
    refetchAmendmentList();
  }, []);

  useEffect(() => {
    refetchAmendmentList();
  }, [watch("rowsPerPage"), watch("page"), debouncedSearchQuery]);

  useEffect(() => {
    setAllAmendmentList(amendmentList?.data?.data?.data || []);
  }, [amendmentList]);

  const totalList = amendmentList?.data?.data?.total ?? 0;

  const onConfirmDelete = () => {
    updateAmendmentId(selectDeleteInfo);
  };

  return (
    <>
      <Stack gap={2}>
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: { xs: "column", sm: "row" },
              justifyContent: "space-between",
              alignItems: { xs: "stretch", sm: "center" },
              gap: 2,
            }}
          >
            <Box
              sx={{
                display: "flex",
                flexDirection: { xs: "column", sm: "row" },
                alignItems: { xs: "stretch", sm: "center", md: "center" },
                flex: 1,
                gap: 2,
                justifyContent: "center",
              }}
            >
              <TextField
                placeholder={t("amendmentType")}
                variant="outlined"
                size="small"
                onChange={(e) => setValue("searchQuery", e.target.value)}
                fullWidth
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  backgroundColor: "#E8E9E8",
                  "& .MuiOutlinedInput-root": {
                    "& fieldset": {
                      borderColor: "#E8E9E8",
                    },
                    "&:hover fieldset": {
                      borderColor: "#E8E9E8",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "#E8E9E8",
                    },
                  },
                  padding: "2px",
                  maxWidth: { sm: "500px" },
                  borderRadius: "10px",
                  border: "1px solid rgba(102, 102, 102, 0.8)",
                }}
              />
            </Box>
          </Box>
          <Box
            sx={{
              maxWidth: "650px",
              display: "flex",
              alignItems: "center",
              borderRadius: "8px",
              backgroundColor: "#FFFFFF",
              marginInline: "auto",
              marginTop: "12px",
              marginBottom: "12px",
              boxShadow: "0px 1px 3px rgba(0, 0, 0, 0.1)",
              border: "1px solid #E5E7EB",
              padding: "4px",
              gap: "4px",
            }}
          >
            <Button
              variant="text"
              startIcon={<FilterListIcon sx={{ color: "#6B7280" }} />}
              sx={{
                height: "36px",
                color: "#6B7280",
                textTransform: "none",
                "&:hover": {
                  backgroundColor: "#F9FAFB",
                },
                width: "120px",
              }}
            >
              {t("filterBy")}
            </Button>

            {[t("amendmentType"), t("decisionDate"), t("status")].map(
              (text, index) => (
                <React.Fragment key={text}>
                  <Divider
                    orientation="vertical"
                    flexItem
                    sx={{ backgroundColor: "#E5E7EB" }}
                  />
                  <Button
                    variant="text"
                    endIcon={
                      <SvgIcon sx={{ fontSize: 16, color: "#6B7280" }}>
                        <svg
                          width="9"
                          height="7"
                          viewBox="0 0 9 7"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M1 1L4.5 6L8 1"
                            stroke="currentColor"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </SvgIcon>
                    }
                    sx={{
                      height: "36px",
                      color: "#6B7280",
                      textTransform: "none",
                      width: "200px",
                      justifyContent: "space-between",
                      "&:hover": {
                        backgroundColor: "#F9FAFB",
                      },
                    }}
                  >
                    {text}
                  </Button>
                </React.Fragment>
              )
            )}
          </Box>
          <DataTable
            columns={columns}
            isLoading={fetchAmendmentListIsLoading}
            rows={allAmendmentList}
            page={watch("page")}
            rowsPerPage={watch("rowsPerPage")}
            totalCount={totalList}
            onPageChange={handleChangePage}
          />
        </Box>
      </Stack>

      <Dialog
        open={deletePopup}
        onClose={() => setDeletePopup(false)}
        fullScreen={false}
        PaperProps={{
          style: {
            borderRadius: "8px",
            backgroundColor: "#fff",
            color: "#666666",
            maxWidth: "500px",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogContent sx={{ py: 2, pb: 1, px: 2.5, textAlign: "center" }}>
          <Typography variant="body1" sx={{ fontSize: 14 }}>
            {t("confirmDeletePerlembagaan")}
          </Typography>
        </DialogContent>
        <DialogActions
          sx={{
            py: 2,
            px: 3,
            justifyContent: "center",
            flexDirection: "column",
            gap: 1,
          }}
        >
          {updateAmendmentIdIsLoading ? (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
              }}
            >
              <CircularProgress />
            </Box>
          ) : (
            <>
              <ButtonPrimary onClick={() => setDeletePopup(false)}>
                {t("no")}
              </ButtonPrimary>
              <ButtonText onClick={onConfirmDelete}>{t("yes")}</ButtonText>
            </>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ListSenaraiPindaan;
