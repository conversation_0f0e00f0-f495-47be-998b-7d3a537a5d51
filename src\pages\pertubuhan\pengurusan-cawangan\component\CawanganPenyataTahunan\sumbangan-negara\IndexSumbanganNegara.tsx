import React, { useState } from "react";
import { PernyataStepper } from "../pernyata-stepper";
import ListSumbanganDariLuarNegara from "./ListSumbanganDariLuarNegara";
import { Box, Stack } from "@mui/material";
import ListSumbanganKeLuarNegara from "./ListSumbanganKeLuarNegara";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import { useSenaraiContext } from "@/pages/pertubuhan/SenaraiContext";
import useQuery from "@/helpers/hooks/useQuery";
import { AddressList } from "../interface";
import { useSelector } from "react-redux";

export const CawanganIndexSumbanganNegara = () => {
  const { t } = useTranslation();

  const navigate = useNavigate();

  const { handleNextPenyataTahunan: handleNext, handleBackPenyataTahunan: handleBack } =
    useSenaraiContext();

  const handleBackActions = () => {
    handleBack()
    navigate(-1)
  }

  //@ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);
  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;

  const handleNextActions = () => {
    handleNext()
    navigate(`../cawangan-penyata-tahunan-display`, {
      state: {
        societyId: societyId,
        statementId: statementId,
        year: year
      }
    });
  }
  return (
    <Box>
      <Box mt={3}>
        <ListSumbanganDariLuarNegara />
      </Box>
      <Box mt={3}>
        <ListSumbanganKeLuarNegara />
      </Box>
      <Box mt={3} sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
        justifyContent: "flex-end", display: "flex"
      }}>
        <Stack direction="row" spacing={2} sx={{ pl: 1 }} justifyContent="flex-end">
          <ButtonOutline onClick={handleBackActions}>{t('back')}</ButtonOutline>
          <ButtonPrimary onClick={handleNextActions}>{t('next')}</ButtonPrimary>
        </Stack>
      </Box>
    </Box>
  );
};

export default CawanganIndexSumbanganNegara;
