import { TrashIcon } from "@/components/icons";
import { removeFieldFromSection, setFieldField } from "@/redux/geranReducer";
import { RootState } from "@/redux/store";
import {
  Box,
  Grid,
  IconButton,
  Switch,
  TextField,
  Checkbox,
  useTheme,
} from "@mui/material";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
type CardFormProps = {
  order: number;
  sectionId: string;
  fieldId: string;
};
const KonfirmasiCardForm: React.FC<CardFormProps> = ({
  order,
  fieldId,
  sectionId,
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { control } = useForm<FieldValues>({
    defaultValues: {
      sectionName: "",
      fieldName: "",
      required: false,
      textareaContent: "",
    },
  });
  const field = useSelector((state: RootState) =>
    state.geran.sections
      .find((s: any) => s.id === sectionId)
      ?.fields.find((f: any) => f.id === fieldId)
  );
  const { fieldName, isRequired, sectionName } = field;
  const options = field?.options || ["", ""];

  const handleOptionChange = (index: number, value: string) => {
    const updated = [...options];
    updated[index] = value;
    dispatch(
      setFieldField({
        sectionId,
        fieldId,
        options: updated,
      })
    );
  };
  const primary = theme.palette.primary.main;

  return (
    <Box
      mt={2}
      sx={{
        borderRadius: 3,
        border: `0.5px solid ${primary}`,
        padding: "22px 34px",
      }}
    >
      <Grid container spacing={2} mb={2}>
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "stretch",
              borderRadius: "12px",

              backgroundColor: "#fff",
            }}
          >
            {/* Left side */}
            <Box
              display="flex"
              flexDirection="column"
              gap={2}
              flex={1}
              justifyContent="center"
            >
              {order === 1 && (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <Controller
                      control={control}
                      name="sectionName"
                      render={({ field }) => (
                        <TextField
                          {...field}
                          placeholder="Tulis Tajuk Disini"
                          variant="outlined"
                          value={sectionName || ""}
                          size="small"
                          onChange={(e) =>
                            dispatch(
                              setFieldField({
                                sectionId,
                                fieldId,
                                sectionName: e.target.value,
                              })
                            )
                          }
                          fullWidth
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                borderColor: "primary.main",
                              },
                              "&:hover fieldset": {
                                borderColor: "primary.dark",
                              },
                              "&.Mui-focused fieldset": {
                                borderColor: "primary.main",
                              },
                            },
                            borderRadius: "3px",
                          }}
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              )}
              <Box>
                <Box sx={{ flex: 1 }}>
                  <Controller
                    control={control}
                    name="description"
                    render={({ field }) => (
                      <TextField
                        {...field}
                        placeholder="Tulis Disini"
                        variant="outlined"
                        size="medium"
                        fullWidth
                        value={options[0] ?? ""}
                        onChange={(e) => handleOptionChange(0, e.target.value)}
                        multiline
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            "& fieldset": {
                              borderColor: "primary.main",
                            },
                            "&:hover fieldset": {
                              borderColor: "primary.dark",
                            },
                            "&.Mui-focused fieldset": {
                              borderColor: "primary.main",
                            },
                          },
                          borderRadius: "3px",
                        }}
                      />
                    )}
                  />
                </Box>
                <Box>
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "flex-end",
                      gap: 3,
                      mt: 3,
                      width: "50%",
                    }}
                  >
                    <Checkbox
                      checked
                      disabled
                      color="primary"
                      sx={{
                        width: 21,
                        height: 21,
                        padding: 0,

                        "&.MuiCheckbox-root": {
                          borderRadius: "3px",
                          border: "0.5px solid #979797",
                        },
                        "&.Mui-checked": {
                          backgroundColor: primary,
                          borderColor: primary,
                        },
                        "& .MuiSvgIcon-root": {
                          display: "none",
                        },
                      }}
                    />
                    <Controller
                      control={control}
                      name="confirmationtext"
                      render={({ field }) => (
                        <TextField
                          {...field}
                          placeholder="Tulis Disini"
                          variant="outlined"
                          value={options[1] ?? ""}
                          onChange={(e) =>
                            handleOptionChange(1, e.target.value)
                          }
                          size="small"
                          fullWidth
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                borderColor: "primary.main",
                              },
                              "&:hover fieldset": {
                                borderColor: "primary.dark",
                              },
                              "&.Mui-focused fieldset": {
                                borderColor: "primary.main",
                              },
                            },
                            borderRadius: "3px",
                          }}
                        />
                      )}
                    />
                  </Box>
                </Box>
              </Box>
            </Box>

            {/* Right side */}
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                ml: 2,
              }}
            >
              {/* Switch */}
              <Box
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "12px",
                  padding: "8px 16px",
                  marginBottom: "12px",
                  backgroundColor: "#fff",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Controller
                  control={control}
                  name="required"
                  render={({ field }) => (
                    <Switch
                      {...field}
                      checked={isRequired || false}
                      onChange={(e) =>
                        dispatch(
                          setFieldField({
                            sectionId,
                            fieldId,
                            isRequired: e.target.checked,
                          })
                        )
                      }
                    />
                  )}
                />
              </Box>

              {/* Delete Button */}
              <Box
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "12px",
                  padding: "8px 16px",
                  backgroundColor: "#fff",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <IconButton
                  sx={{
                    color: "#FF0000",
                    p: 1,
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    dispatch(
                      removeFieldFromSection({
                        sectionId: sectionId,
                        fieldId: fieldId,
                      })
                    );
                  }}
                >
                  <TrashIcon sx={{ width: 20, height: 20 }} />
                </IconButton>
              </Box>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default KonfirmasiCardForm;
