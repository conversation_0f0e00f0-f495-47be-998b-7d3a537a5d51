import {
  Box,
  Typography,
  Dialog,
  DialogContent,
  useMediaQuery,
  useTheme,
  Card,
  CircularProgress,
} from "@mui/material";
import { t } from "i18next";
import React, { useCallback, useEffect, useState } from "react";

import { useCustomMutation } from "@refinedev/core";
import { useNavigate, useParams } from "react-router-dom";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import MaklumatPermohonanSection from "./MaklumatPermohonanSection";
import DokumenSokonganSection from "./DokumenSokonganSection";
import PerlembagaanSection from "./PerlembagaanSection";
import PenyataTahuanSection from "./PenyataTahuanSection";

import {
  DecisionOptionsCodeRayuan,
  DocumentUploadType,
  filterEmptyValuesOnObject,
  KelulusanKuiri,
  PermissionNames,
  pageAccessEnum,
  ROApprovalType,
  useMutation,
} from "@/helpers";
import FileUploader from "@/components/input/fileUpload";
import { ButtonOutline, ButtonPrimary, Label } from "@/components";
import { useSelector } from "react-redux";
import { AppDispatch } from "@/redux/store";
import { useDispatch } from "react-redux";
import { fetchAppealByIdData } from "@/redux/APIcalls/appealByIdThunks";
import { fetchSocietyByIdData } from "@/redux/APIcalls/societyByIdThunks";
import { API_URL } from "@/api";
import KelulusanAccordion from "./KelulusanAccordion";
import Input from "@/components/input/Input";
import { CheckedIcon } from "@/components/input/customRadio";
import AuthHelper from "@/helpers/authHelper";

type ReadStatusType = {
  [key: number]: Boolean;
};

interface ROApproval {
  createdDate: string;
  note: string;
  [key: string]: any;
}

interface FormValuesD {
  appealId?: any | undefined;
  societyId?: any | undefined;
  societyNo?: any | undefined;
  rejectReason?: any;
  decisionNotes?: any;
  approvedBy?: any;
  applicationStatusCode?: any;
  letterNo?: any;
}

interface FormValuesK {
  appealId?: any | undefined;
  societyId?: any | undefined;
  societyNo?: any | undefined;
  rejectReason?: any;
  approvedBy?: any;
  applicationStatusCode?: any;
  notesQuery?: any;
  queryReceiver?: any;
  queryAnswer?: any;
}

function KuiriRayuanKelulusanSemak() {
  const keputusan_induk__kelulusan_items = [
    {
      subTitle: t("maklumatPermohonan"),
      component: <MaklumatPermohonanSection />,
    },
    {
      subTitle: t("supportingDocuments"),
      component: <DokumenSokonganSection />,
    },
    {
      subTitle: t("constitution"),
      component: <PerlembagaanSection />,
    },
    {
      subTitle: t("annualStatement"),
      component: <PenyataTahuanSection />,
    },
    // {
    //   subTitle: t("idk2"),
    //   component: <KeputusanPerm />,
    // },
  ];

  const navigate = useNavigate();

  const hasKelulusanUpdatePermission = AuthHelper.hasPageAccess(
    PermissionNames.KELULUSAN.label,
    pageAccessEnum.Update
  );

  const { id } = useParams();
  const decisionOptions = DecisionOptionsCodeRayuan(t);
  useEffect(() => {
    if (!hasKelulusanUpdatePermission) {
      navigate("/internal-user");
    }
  }, [hasKelulusanUpdatePermission, navigate]);

  const theme = useTheme();
  const [dialogSejarahKuiriSaveOpen, setDialogSejarahKuiriSaveOpen] =
    useState(false);

  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);

  const [readStatus, setReadStatus] = useState<ReadStatusType>({});

  const [ROApprovalHistory, setROApprovalHistory] =
    useState<ROApproval | null>();

  const handleChangeCurrentExpandSection =
    (item: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);

      if (isExpanded) {
        // setReadStatus((prevState) => ({ ...prevState, [item]: true }));
        setReadStatus((prevState) => {
          const updatedStatus = keputusan_induk__kelulusan_items.reduce(
            (acc, _, i) => {
              if (i + 1 <= item) {
                acc[i + 1] = true;
              } else {
                acc[i + 1] = !!prevState[i + 1] || false;
              }
              return acc;
            },
            {} as Record<number, boolean>
          );
          return updatedStatus;
        });
      }
    };

  const {
    data: societyDataByIdIndex,
    loading: loadingSociety,
    error: errorSociety,
  } = useSelector((state: any) => state.societyByIdData);

  const {
    data: appealDataById,
    loading: loadingAppeal,
    error: errorAppeal,
  } = useSelector((state: any) => state.appealByIdData);
  const dispatch: AppDispatch = useDispatch();
  useEffect(() => {
    if (id) {
      dispatch(fetchAppealByIdData({ id: id }));
      fetchUserRoles();
    }
  }, [id]);

  const [reloadQuery, setReloadQuery] = useState(1);
  useEffect(() => {
    if (appealDataById?.societyId) {
      // @ts-ignore
      const payload = {
        societyId: appealDataById?.societyId,
        appealId: appealDataById?.id,
        roApprovalType: ROApprovalType.SOCIETY_APPEAL.code,
        // queryReceiver: "JPPM NEGERI",
      };

      getQuery(payload);
      dispatch(fetchSocietyByIdData({ id: appealDataById.societyId }));
    }
  }, [appealDataById, reloadQuery]);

  const [hasPPKDNRole, setHasPPKDNRole] = useState(false);
  const fetchUserRoles = async () => {
    try {
      const portal = localStorage.getItem("portal") || "";
      const authToken = localStorage.getItem("refine-auth");

      if (!authToken) {
        throw new Error("Authorization token is missing");
      }

      const response = await fetch(
        `${API_URL}/user/profile/getUserInternalDetails`,
        {
          headers: {
            portal,
            authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      if (
        data?.data?.userRole?.length > 0 &&
        data.data.userRole.includes("PP KDN")
      ) {
        setHasPPKDNRole(true);
      } else {
        setHasPPKDNRole(false);
      }
    } catch (error) {
      console.error("Failed to fetch user data");
    }
  };

  const [formValuesD, setFormValuesD] = useState<FormValuesD>({});
  const [formValuesK, setFormValuesK] = useState<FormValuesK>({});

  const handleChangeDecision = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setFormValuesD((prevValues) => ({
        ...prevValues,
        [name]: value,
      }));
    },
    []
  );

  const handleChangeKuiri = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setFormValuesK((prevValues) => ({
        ...prevValues,
        [name]: value,
      }));
    },
    []
  );

  const handleQueryOpen = async () => {
    setDialogSejarahKuiriSaveOpen(!dialogSejarahKuiriSaveOpen);
  };

  const [ROQuery, setROQuery] = useState(null);

  //JPPM NEGERI
  const { fetch: getQuery, isLoading: isLoadingQuery } = useMutation({
    url: "society/roQuery/getQuery",
    method: "post",
    onSuccess: (data) => {
      const queryData = data?.data?.data;
      if (queryData?.length > 0) {
        setROQuery(queryData);
      }
    },
    onSuccessNotification: () => {},
  });

  //RO APPROVAL SUBMIT
  const [isSuccess, setIsSuccess] = useState(false);

  const { mutate: updateDecision, isLoading: isLoadingUpdateDecision } =
    useCustomMutation();

  const UpdateDecision = (values: any) => {
    updateDecision(
      {
        url: `${API_URL}/society/roDecision/updateApprovalStatus`,
        method: "patch",
        values: values,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status !== "ERROR") {
            if (values.applicationStatusCode !== 36) {
              navigate("../rayuan");
            }
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else if (data?.data?.msg) {
            return {
              message: t(data?.data?.msg),
              type: "error",
            };
          } else {
            return {
              message: t("error"),
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: t(data?.data?.msg),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          setIsSuccess(true);
          setReloadQuery((prev) => prev + 1);
        },
      }
    );
  };

  //KUIRI SUBMIT

  const handleSubmitKuiri = async (event: React.FormEvent) => {
    event.preventDefault();
    if (appealDataById) {
      const payload = {
        societyId: societyDataByIdIndex?.id,
        appealId: appealDataById?.id,
        applicationStatusCode: 36,
        note: formValuesK.notesQuery,
        rejectReason: "",
        roApprovalType: ROApprovalType.SOCIETY_APPEAL.code,
        queryReceiver: formValuesK.queryReceiver,
      };
      const filterPayload = filterEmptyValuesOnObject(payload);
      UpdateDecision(filterPayload);
    }
  };

  //RO APPROVAL SUBMIT
  const handleSubmitDescision = async (event: React.FormEvent) => {
    event.preventDefault();
    if (appealDataById) {
      const payload = {
        societyId: societyDataByIdIndex?.id,
        appealId: appealDataById?.id,
        applicationStatusCode: formValuesD.applicationStatusCode,
        rejectReason:
          formValuesD.applicationStatusCode === 4
            ? formValuesD.decisionNotes
            : "",
        note:
          formValuesD.applicationStatusCode === 4
            ? ""
            : formValuesD.decisionNotes,
        roApprovalType: ROApprovalType.SOCIETY_APPEAL.code,
      };

      const filterPayload = filterEmptyValuesOnObject(payload);
      UpdateDecision(filterPayload);
    }
  };

  //ANSWER SUBMIT

  const { mutate: updateAnswer, isLoading: isLoadingUpdateAnswer } =
    useCustomMutation();

  const UpdateAnswer = (values: any) => {
    updateAnswer(
      {
        url: `${API_URL}/society/roDecision/updateQuery`,
        method: "patch",
        values: values,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status !== "ERROR") {
            setReloadQuery((prev) => prev + 1);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else if (data?.data?.msg) {
            return {
              message: t(data?.data?.msg),
              type: "error",
            };
          } else {
            return {
              message: t("error"),
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: t(data?.data?.msg),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          setIsSuccess(true);
        },
      }
    );
  };

  const handleSubmitAnswer = async (event: React.FormEvent) => {
    event.preventDefault();
    if (appealDataById) {
      const payload = {
        societyId: societyDataByIdIndex?.id,
        appealId: appealDataById?.id,
        // note: formValuesK?.queryAnswer,
        roApprovalType: ROApprovalType.SOCIETY_APPEAL.code,
        queryReceiver: "JPPM NEGERI",
      };

      const filterPayload = filterEmptyValuesOnObject(payload);
      UpdateAnswer(filterPayload);
    }
  };

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Card
            sx={{
              p: 3,
              backgroundColor: "var(--primary-color)",
              borderRadius: "15px",
              boxShadow: "none",
              height: "100px",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "flex-start",
              position: "relative",
              overflow: "hidden",
              "&::before": {
                content: '""',
                position: "absolute",
                top: 0,
                right: 0,
                width: "150px",
                height: "100%",
                // backgroundImage: `url("/pattern.svg")`,
                backgroundSize: "contain",
                backgroundRepeat: "no-repeat",
                backgroundPosition: "right center",
                opacity: 0.2,
              },
            }}
          >
            <Box
              sx={{ display: "flex", alignItems: "center", gap: 2, zIndex: 1 }}
            >
              <Typography
                sx={{
                  fontFamily: "Poppins",
                  fontSize: "20px",
                  fontWeight: 600,
                  color: "white",
                }}
              >
                {societyDataByIdIndex?.societyName}
              </Typography>
            </Box>
            <Typography
              sx={{
                fontFamily: "Poppins",
                fontSize: "14px",
                color: "white",
                mt: 1,
                zIndex: 1,
                opacity: 0.8,
              }}
            >
              {societyDataByIdIndex?.applicationNo}
            </Typography>
          </Card>
        </Box>
        <Box sx={{ mt: 4 }}>
          {keputusan_induk__kelulusan_items.map((item, index) => {
            return (
              <KelulusanAccordion
                key={index}
                subTitle={item.subTitle}
                currentIndex={index + 1}
                currentExpand={currentExpandSection}
                readStatus={readStatus}
                onChangeFunc={handleChangeCurrentExpandSection}
              >
                {item.component}
              </KelulusanAccordion>
            );
          })}
        </Box>
        {hasPPKDNRole ? (
          <>
            <Box
              sx={{
                backgroundColor: "white",
                p: 3,
                borderRadius: "15px",
                mt: 2,
              }}
              component="form"
              onSubmit={handleSubmitKuiri}
            >
              <Box
                sx={{
                  pl: 2,
                  p: 3,
                  mt: 1,
                  borderRadius: "10px",
                  border: "0.5px solid #dfdfdf",
                }}
              >
                <Box
                  sx={{
                    mb: 5,
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Typography color={"primary"}>{t("kuiri")}</Typography>
                  <ButtonOutline
                    sx={{
                      width: "20%",
                      gap: 2,
                    }}
                    //@ts-ignore
                    disabled={ROQuery?.length > 0 ? false : true}
                    onClick={() => handleQueryOpen()}
                  >
                    <img height={16} width={15} src="/addDocument.png" />
                    {t("queryHistory")}
                  </ButtonOutline>
                </Box>
                {/* <Input
                  label={t("queryTo")}
                  //@ts-ignore
                  disabled={ROQuery?.[0]?.finished === false}
                  name="queryReceiver"
                  value={
                    formValuesK?.queryReceiver ? formValuesK.queryReceiver : ""
                  }
                  type="select"
                  options={KelulusanKuiri}
                  onChange={handleChangeKuiri}
                /> */}

                <Input
                  label={t("catatanKuiri")}
                  name="notesQuery"
                  value={formValuesK?.notesQuery}
                  //@ts-ignore
                  disabled={ROQuery?.[0]?.finished === false}
                  multiline
                  rows={4}
                  onChange={handleChangeKuiri}
                />

                <Box
                  sx={{
                    mt: 5,
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: 2,
                  }}
                >
                  <ButtonOutline
                    sx={{ py: 1 }}
                    disabled={
                      isLoadingUpdateDecision ||
                      //@ts-ignore
                      ROQuery?.[0]?.finished === false
                    }
                  >
                    {t("back2")}
                  </ButtonOutline>
                  <ButtonPrimary
                    type="submit"
                    disabled={
                      isLoadingUpdateDecision ||
                      //@ts-ignore
                      ROQuery?.[0]?.finished === false
                    }
                  >
                    {isLoadingUpdateDecision ? (
                      <CircularProgress size={15} />
                    ) : (
                      t("hantar")
                    )}
                  </ButtonPrimary>
                </Box>
              </Box>
            </Box>
            <Box
              sx={{
                backgroundColor: "white",
                p: 3,
                borderRadius: "15px",
                mt: 2,
              }}
              component="form"
              onSubmit={handleSubmitDescision}
            >
              <Box
                sx={{
                  pl: 2,
                  p: 3,
                  mt: 1,
                  borderRadius: "10px",
                  border: "0.5px solid #dfdfdf",
                }}
              >
                <Box
                  sx={{
                    mb: 5,
                  }}
                >
                  <Typography color={"primary"}>{t("keputusan")}</Typography>
                </Box>
                <Input
                  label={t("statusPermohonan")}
                  name="applicationStatusCode"
                  type="select"
                  options={decisionOptions}
                  onChange={handleChangeDecision}
                  value={
                    formValuesD?.applicationStatusCode
                      ? formValuesD.applicationStatusCode
                      : ""
                  }
                />

                <Input
                  name="letterNo"
                  label={t("letterNo")}
                  onChange={handleChangeDecision}
                  value={formValuesD?.letterNo ? formValuesD.letterNo : ""}
                />
                <Input
                  label={t("remarks")}
                  name="decisionNotes"
                  multiline
                  rows={4}
                  onChange={handleChangeDecision}
                  value={
                    formValuesD?.decisionNotes ? formValuesD.decisionNotes : ""
                  }
                />

                <Box
                  sx={{
                    mt: 5,
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: 2,
                  }}
                >
                  <ButtonOutline sx={{ py: 1 }}>{t("back2")}</ButtonOutline>
                  <ButtonPrimary type="submit">{t("hantar")}</ButtonPrimary>
                </Box>
              </Box>
            </Box>
          </>
        ) : (
          <>
            <Box
              sx={{
                backgroundColor: "white",
                p: 3,
                borderRadius: "15px",
                mt: 2,
              }}
              component="form"
              onSubmit={handleSubmitAnswer}
            >
              <Box
                sx={{
                  pl: 2,
                  p: 3,
                  mt: 1,
                  borderRadius: "10px",
                  border: "0.5px solid #dfdfdf",
                  display: "grid",
                }}
              >
                <Box
                  sx={{
                    mb: 5,
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Typography color={"primary"}>{t("kuiri")}</Typography>
                  <ButtonOutline
                    sx={{
                      width: "20%",
                      gap: 2,
                    }}
                    //@ts-ignore
                    disabled={ROQuery?.length > 0 ? false : true}
                    onClick={() => handleQueryOpen()}
                  >
                    <img height={16} width={15} src="/addDocument.png" />
                    {t("queryHistory")}
                  </ButtonOutline>
                </Box>

                <Box sx={{ display: "flex", minWidth: "100%", mb: 1 }}>
                  <Box sx={{ flex: 3.2 }}>
                    <Label text={t("catatanKuiri")} />
                  </Box>
                  <Box sx={{ flex: 6.3 }}>
                    <FileUploader
                      // disabled
                      type={DocumentUploadType.APPEAL}
                      societyId={appealDataById?.societyId}
                      societyNo={appealDataById?.societyNo}
                      appealId={appealDataById?.id ? appealDataById?.id : null}
                      validTypes={[
                        "text/plain",
                        // "application/rtf",
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                        // "application/msword",
                        // "application/vnd.oasis.opendocument.text",
                        "application/pdf",
                      ]}
                    />
                  </Box>
                </Box>
                <Input
                  label={t("JPPMComments")}
                  name="queryAnswer"
                  //@ts-ignore
                  value={ROQuery?.[0]?.note}
                  disabled
                  multiline
                  rows={4}
                  onChange={handleChangeKuiri}
                />

                <Box
                  sx={{
                    mt: 5,
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: 2,
                  }}
                >
                  <ButtonOutline sx={{ py: 1 }}>{t("back2")}</ButtonOutline>
                  <ButtonPrimary type="submit" disabled={isLoadingUpdateAnswer}>
                    {isLoadingUpdateAnswer ? (
                      <CircularProgress size={15} />
                    ) : (
                      t("hantar")
                    )}
                  </ButtonPrimary>
                </Box>
              </Box>
            </Box>
            <Box
              sx={{
                backgroundColor: "white",
                p: 3,
                borderRadius: "15px",
                mt: 2,
              }}
            >
              <Box
                sx={{
                  pl: 2,
                  p: 3,
                  mt: 1,
                  borderRadius: "10px",
                  border: "0.5px solid #dfdfdf",
                }}
              >
                <Box
                  sx={{
                    mb: 5,
                  }}
                >
                  <Typography color={"primary"}>{t("keputusan")}</Typography>
                </Box>
                <Input
                  label={t("statusPermohonan")}
                  name="letterNo"
                  type="select"
                  options={decisionOptions}
                  disabled
                />

                <Input label={t("letterNo")} disabled />
                <Input
                  label={t("remarks")}
                  name="note"
                  multiline
                  rows={4}
                  disabled
                />

                <Box
                  sx={{
                    mt: 5,
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: 2,
                  }}
                >
                  <ButtonOutline disabled sx={{ py: 1 }}>
                    {t("back2")}
                  </ButtonOutline>
                  <ButtonPrimary disabled>{t("hantar")}</ButtonPrimary>
                </Box>
              </Box>
            </Box>
          </>
        )}
      </Box>

      <Dialog
        open={dialogSejarahKuiriSaveOpen}
        onClose={() => setDialogSejarahKuiriSaveOpen(false)}
        fullScreen={fullScreen}
        PaperProps={{
          style: {
            borderRadius: "8px",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogContent sx={{ py: 4 }}>
          <Box
            sx={{
              p: 3,
              mt: 1,
              borderRadius: "10px",
              border: "0.5px solid #dfdfdf",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                pb: 4,
                color: "var(--primary-color)",
              }}
              gap={2}
            >
              <ChevronLeftIcon
                sx={{ cursor: "pointer" }}
                onClick={() =>
                  setDialogSejarahKuiriSaveOpen(!dialogSejarahKuiriSaveOpen)
                }
              />
              <Typography sx={{ fontWeight: "4001important" }}>
                {t("queryHistory")}
              </Typography>
            </Box>
            {/* @ts-ignore */}
            {ROQuery?.length ? (
              // @ts-ignore
              ROQuery?.map((item, index) => (
                <Box sx={{ display: "flex", mb: 4 }} key={item.id}>
                  <Box sx={{ mr: 2 }}>
                    <Box
                      sx={{
                        width: 35,
                        height: 35,
                        borderRadius: "50%",
                        border: `1px solid ${
                          item.finished ? "var(--primary-color)" : "#FF0000"
                        }`,
                        backgroundColor: item.finished
                          ? "var(--primary-color)80"
                          : "#FF000080",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      {item.finished ? <CheckedIcon /> : null}
                    </Box>
                    {/* @ts-ignore */}
                    {index !== ROQuery?.length - 1 && !item?.finished && (
                      <Box
                        sx={{
                          width: 2,
                          height: "100%",
                          backgroundColor: "#DADADA",
                          ml: 2,
                        }}
                      />
                    )}
                  </Box>
                  <Box sx={{ width: "100%" }}>
                    <Box sx={{ display: "flex", gap: 3 }}>
                      <Typography
                        sx={{
                          fontWeight: "300",
                          border: "1px solid #5088FF",
                          background: "#5088FF80",
                          borderRadius: "9px",
                          color: "#fff",
                          px: 2,
                          py: 1,
                          fontSize: "12px",
                        }}
                      >
                        Kuiri #{index + 1}
                      </Typography>
                      <Typography
                        sx={{
                          fontWeight: "300",
                          border: "1px solid #DADADA",
                          background: "transparent",
                          borderRadius: "9px",
                          color: "#666666",
                          px: 2,
                          py: 1,
                          fontSize: "12px",
                        }}
                      >
                        {item?.createdDate}
                      </Typography>
                      <Typography
                        sx={{
                          fontWeight: "300",
                          border: "1px solid #DADADA",
                          background: "transparent",
                          borderRadius: "9px",
                          color: "#666666",
                          px: 2,
                          py: 1,
                          fontSize: "12px",
                        }}
                      >
                        Pegawai: {item?.queryReceiver}
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        p: 3,
                        border: "1px solid #D9D9D9",
                        borderRadius: "14px",
                        width: "100%",
                        mt: 2,
                        minHeight: "150px",
                        position: "relative",
                      }}
                    >
                      <Typography sx={{ mb: 3, color: "#666666" }}>
                        {item?.notesQuery || item?.note}
                      </Typography>
                      <Box
                        sx={{
                          fontFamily: '"Poppins", sans-serif',
                          backgroundColor: item.finished
                            ? "var(--primary-color)80"
                            : "#FF000080",
                          border: `1px solid ${
                            item.finished ? "var(--primary-color)" : "#FF0000"
                          }`,
                          padding: "6px 20px",
                          borderRadius: "18px",
                          color: "#fff",
                          fontSize: "14px",
                          fontWeight: "400",
                          position: "absolute",
                          bottom: "20px",
                          right: "20px",
                        }}
                      >
                        {item.finished ? t("SELESAI") : t("belumselesai")}
                      </Box>
                    </Box>
                  </Box>
                </Box>
              ))
            ) : (
              <Typography className="label">{t("noData")}</Typography>
            )}
          </Box>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default KuiriRayuanKelulusanSemak;
