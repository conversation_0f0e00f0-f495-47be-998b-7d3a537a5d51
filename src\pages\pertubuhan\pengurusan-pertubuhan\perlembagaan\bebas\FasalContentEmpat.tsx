import {
  <PERSON>,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
  FormControl,
  Select,
  MenuItem,
  FormHelperText,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { handleSaveContent } from "../helper/handleSaveContent";
import { useSelector } from "react-redux";
import { capitalizeWords, getLocalStorage } from "@/helpers/utils";
import { FasalBebasProps } from "../Fasal";
import EditableFasalTextArea from "@/components/FasalBebasComponent/EditableFasalTextArea";
import FasalNameCom from "@/components/FasalBebasComponent/FasalNameCom";
import ReminderEditable from "@/components/FasalBebasComponent/ReminderEditable";
import ContentBox from "@/components/FasalBebasComponent/ContentBox";
import CheckContent from "@/components/FasalBebasComponent/CheckContent";
import { API_URL } from "@/api";
import { CommiteeEnum } from "@/helpers";
import MessageDialog from "@/components/dialog/message";
import { DialogConfirmation } from "@/components";
import { addJawatanTitleProps } from "@/pages/pertubuhan/pengurusan-perlembagaan/faedah/FasalContentLima";
import {
  AddAhliJawatanComponent,
  addJawatanBilanganeProps,
  ExistingAhliJawatanComponent,
} from "@/components/FasalComponent/AhliJawatanComponent";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

export const FasalContentEmpatBebas: React.FC<FasalBebasProps> = ({
  activeStep,
  setActiveStep,
  clause,
  asalData,
  name,
}) => {
  const requiredText = [
    "<<tempoh pelantikan jawatankuasa>>",
    "<<jenis mesyuarat agung>>",
    "<<jawatan Pengerusi>>",
    "<<bilangan Timbalan Pengerusi>>",
    "<<jawatan Timbalan Pengerusi>>",
    "<<bilangan Naib Pengerusi>>",
    "<<jawatan Naib Pengerusi>>",
    "<<jawatan Setiausaha Agung>>",
    "<<bilangan pen. SU>>",
    "<<jawatan Penolong Setiausaha>>",
    "<<jawatan Bendahari Agung>>",
    "<<bilangan pen. bendahari>>",
    "<<jawatan Penolong Bendahari>>",
    "<<bilangan ajk>>",
    "<<jawatan Ahli Jawatankuasa Biasa>>",
    "<<kekerapan mesyuarat>>",
    "<<tempoh membela diri pelucutan>>",
  ];

  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [tempohJawatan, setTempohJawatan] = useState(t("setahun"));
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [kekerapan, setKekerapan] = useState("4");
  const [tempohPelucutan, setTempohPelucutan] = useState("0");
  const [isLoadingUpdate, setIsLoadingUpdate] = useState(false);
  const [tempohPelucutanWaktu, setTempohPelucutanWaktu] = useState(t("day"));
  const [pengerusi, setPengerusi] = useState(t("chairman"));
  const [jumlahPengerusi, setJumlahPengerusi] = useState<any>("1");
  const [timbalan, setTimbalan] = useState(t("timbalanPengerusi"));
  const [jumlahTimbalan, setJumlahTimbalan] = useState<any>("0");
  const [naib, setNaib] = useState(t("naibPengerusi"));
  const [jumlahNaib, setJumlahNaib] = useState<any>("0");
  const [setiaUsaha, setSetiaUsaha] = useState(t("secretary"));
  const [jumlahSetiaUsaha, setJumlahSetiaUsaha] = useState<any>("1");
  const [penolongSetiaUsaha, setPenolongSetiaUsaha] = useState(
    t("asistantSecretary")
  );
  const [jumlahPenolongSetiaUsaha, setJumlahPenolongSetiaUsaha] =
    useState<any>("0");
  const [bendahari, setBendahari] = useState(t("treasurer"));
  const [jumlahBendahari, setJumlahBendahari] = useState<any>("1");
  const [penolongBendahari, setPenolongBendahari] = useState(
    t("asistantTreasurer")
  );
  const [showErrorAjk, setShowErrorAjk] = useState(false);
  const [jumlahPenolongBendahari, setJumlahPenolongBendahari] =
    useState<any>("0");
  const [ahliBiasa, setAhliBiasa] = useState(t("ordinaryCommitteeMember"));
  const [jumlahAhliBiasa, setJumlahAhliBiasa] = useState<any>("0");
  const [clauseContentId, setClauseContentId] = useState<string | number>("");
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [clauseContentEditable, setClauseContentEditable] = useState(
    clause.clauseContent
  );
  const [checked, setChecked] = useState(false);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [isRequiredConstitution, setIsRequiredConstitution] = useState(true);
  const [dialogSaveOpen, setDialogSaveOpen] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");

  const [selectedDeleteId, setSelectedDeleteId] = useState<number | null>(null);
  const [selectedMatchId, setSelectedMatchId] = useState<
    string | number | null
  >(null);

  const [showConfirmDeleteDialog, setShowConfirmDeleteDialog] = useState(false);

  const [showIsRestrictedWord, setShowIsRestrictedWord] = useState(false);

  const restrictedTitles = [
    t("chairman"),
    t("presiden"),
    t("pengarah"),
    t("thepresident"),
    t("timbalanPengerusi"),
    t("vicePresident"),
    t("timbalanPengarah"),
    t("naibPengerusi"),
    t("naibPresiden"),
    t("naibPengarah"),
    // t("secretary"),
    // t("generalSecretary"),
    // t("asistantSecretary"),
    // t("generalAssistantSecretary"),
    t("treasurer"),
    t("chiefTreasurer"),
    t("asistantTreasurer"),
    t("chiefAssistantTreasurer"),
    t("ordinaryCommitteeMember"),
    "Juruaudit",
    "Juru audit",
    "Auditor",
    "Pemegang amanah",
    "Pegawai Awam",
    "Pegawai Harta",
    "Pentadbir Harta",
    "Pemeriksa Kira-kira",
    "Penaung",
    "Penasihat",
  ];

  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { id, clauseId } = useParams();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };
  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();
  const { mutate: preCreateAjk } = useCustomMutation();
  const handleChange = (e: any) => {
    setClauseContentEditable(e.target.value);
  };

  // FOR CUSTOM AHLI JAWATAN
  const [existingCustomJawatanBilangan, setExistingCustomJawatanBilangan] =
    useState<addJawatanTitleProps[]>([]);
  const [existingCustomJawatanAhli, setExistingCustomJawatanAhli] = useState<
    addJawatanBilanganeProps[]
  >([]);
  const [numberAddAhliBilangan, setNumberAddAhliBilangan] = useState<
    addJawatanBilanganeProps[]
  >([]);
  const [numberAddAhli, setNumberAddAhli] = useState<addJawatanTitleProps[]>(
    []
  );

  useEffect(() => {
    if (clause) {
      //const clause = JSON.parse(clauseValue);
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }

      const fieldMappings: Record<string, (value: string) => void> = {
        "Tempoh Pelantikan Jawatankuasa": setTempohJawatan,
        "Jenis Mesyuarat Agung": setPemilihanAjk,
        "Kekerapan Mesyuarat Jawatankuasa": setKekerapan,
        "Bilangan tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa":
          setTempohPelucutan,
        "Tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa":
          setTempohPelucutanWaktu,
        Pengerusi: setPengerusi,
        "Bilangan Pengerusi": setJumlahPengerusi,
        "Timbalan Pengerusi": setTimbalan,
        "Bilangan Timbalan Pengerusi": setJumlahTimbalan,
        "Naib Pengerusi": setNaib,
        "Bilangan Naib Pengerusi": setJumlahNaib,
        Setiausaha: setSetiaUsaha,
        "Bilangan Setiausaha": setJumlahSetiaUsaha,
        "Penolong Setiausaha": setPenolongSetiaUsaha,
        "Bilangan Penolong Setiausaha": setJumlahPenolongSetiaUsaha,
        Bendahari: setBendahari,
        "Bilangan Bendahari": setJumlahBendahari,
        "Penolong Bendahari": setPenolongBendahari,
        "Bilangan Penolong Bendahari": setJumlahPenolongBendahari,
        "Ahli Jawatankuasa Biasa": setAhliBiasa,
        "Bilangan Ahli Jawatankuasa Biasa": setJumlahAhliBiasa,
      };
      if (clause.constitutionValues) {
        clause.constitutionValues.forEach((item: any) => {
          const setter = fieldMappings[item.titleName];
          if (setter && item.definitionName) {
            setter(item.definitionName);
          }
        });
      }
      setClauseContentEditable(
        clause.clauseModifyContent
          ? clause.clauseModifyContent
          : clause.clauseContent
      );
      setIsEdit(clause.edit);
    }
  }, [clause]);

  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);

  let clauseContent = clauseContentEditable;
  clauseContent = clauseContent.replaceAll(
    /<<tempoh pelantikan jawatankuasa>>/gi,
    `<b>${tempohJawatan || "<<tempoh pelantikan jawatankuasa>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${pemilihanAjk || "<<jenis mesyuarat agung>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kekerapan mesyuarat>>/gi,
    `<b>${kekerapan || "<<kekerapan mesyuarat>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh membela diri pelucutan>>/gi,
    `<b>${tempohPelucutan || "<<tempoh membela diri pelucutan>>"}</b>`
  );

  clauseContent = clauseContent.replaceAll(
    /<<jawatan Pengerusi>>/gi,
    `<b>${pengerusi || "<<jawatan Pengerusi>>"}</b>`
  );

  if (jumlahTimbalan > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Timbalan Pengerusi>>/gi,
      `<b>${timbalan || "<<jawatan Timbalan Pengerusi>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan Timbalan Pengerusi>>/gi,
      `<b>${
        Number(jumlahTimbalan) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahTimbalan} orang` || "<<bilangan Timbalan Pengerusi>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*ii\.\s*<<bilangan Timbalan Pengerusi>> <<jawatan Timbalan Pengerusi>>\s*[\r\n\t]?/gim,
      "    "
    );
  }

  if (jumlahNaib > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Naib Pengerusi>>/gi,
      `<b>${naib || "<<jawatan Naib Pengerusi>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan Naib Pengerusi>>/gi,
      `<b>${
        Number(jumlahNaib) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahNaib} orang` || "<<bilangan Naib Pengerusi>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*iii\.\s*<<bilangan Naib Pengerusi>> <<jawatan Naib Pengerusi>>\s*[\r\n\t]?/gim,
      "    "
    );
  }

  clauseContent = clauseContent.replaceAll(
    /<<jawatan Bendahari Agung>>/gi,
    `<b>${bendahari || "<<jawatan Bendahari Agung>>"}</b>`
  );

  if (jumlahPenolongBendahari > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Penolong Bendahari>>/gi,
      `<b>${penolongBendahari || "<<jawatan Penolong Bendahari>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan pen. bendahari>>/gi,
      `<b>${
        Number(jumlahPenolongBendahari) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahPenolongBendahari} orang` || "<<bilangan pen. bendahari>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*vii\.\s*<<bilangan pen. bendahari>> <<jawatan Penolong Bendahari>>\s*[\r\n]?/gim,
      "    "
    );
  }

  clauseContent = clauseContent.replaceAll(
    /<<jawatan Setiausaha Agung>>/gi,
    `<b>${setiaUsaha || "<<jawatan Setiausaha Agung>>"}</b>`
  );

  if (jumlahPenolongSetiaUsaha > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Penolong Setiausaha>>/gi,
      `<b>${penolongSetiaUsaha || "<<jawatan Penolong Setiausaha>>"}</b>`
    );

    clauseContent = clauseContent.replaceAll(
      /<<bilangan pen. SU>>/gi,
      `<b>${
        Number(jumlahPenolongSetiaUsaha) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahPenolongSetiaUsaha} orang` || "<<bilangan pen. SU>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*v\.\s*<<bilangan pen. SU>> <<jawatan Penolong Setiausaha>>\s*[\r\n]?/gim,
      "    "
    );
  }

  if (jumlahAhliBiasa > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Ahli Jawatankuasa Biasa>>/gi,
      `<b>${ahliBiasa || "<<jawatan Ahli Jawatankuasa Biasa>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan ajk>>/gi,
      `<b>${
        Number(jumlahAhliBiasa) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahAhliBiasa} orang` || "<<bilangan ajk>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*viii\.\s*<<bilangan ajk>> <<jawatan Ahli Jawatankuasa Biasa>>\s*[\r\n]?/gim,
      ""
    );
  }

  clauseContent = clauseContent.replace(
    /<<jawatan telah diubahsuai>>\s*/gi,
    ""
  );
  clauseContent = clauseContent.replace(
    /\s*<<penambahan jawatan diubahsuai>>/gi,
    ``
  );

  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  clauseContent = renumberRomanList(clauseContent);

  function renumberRomanList(content: any) {
    const romanNumerals = [
      "i",
      "ii",
      "iii",
      "iv",
      "v",
      "vi",
      "vii",
      "viii",
      "ix",
      "x",
    ];
    let index = 0;

    return content.replace(
      /^(\s*)(?:i{1,3}|iv|v?i{0,3}|ix|x)\.\s/gim,
      (_: any, indent: any) => {
        const roman = romanNumerals[index++] || `${index}.`;
        return `${indent}${roman}. `;
      }
    );
  }

  const handlerChangeClauseContentEditable = (val: string) => {
    setClauseContentEditable(val);
  };

  const { data: listCommittee } = useCustom({
    url: `${API_URL}/society/committee/getAll`,
    method: "get",
    config: {
      filters: [
        {
          field: "pageNo",
          operator: "eq",
          value: 1,
        },
        {
          field: "pageSize",
          operator: "eq",
          value: 1000,
        },
        {
          field: "societyId",
          operator: "eq",
          value: societyDataRedux.id,
        },
      ],
      // query: {
      //   // societyId: id,
      //   pageNo: 1,
      //   // pageSize: 10,
      // },

      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !isEdit,
    },
  });

  const committeeData = listCommittee?.data?.data?.data || [];

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!tempohJawatan) {
      errors.tempohJawatan = t("fieldRequired");
    }
    if (!pemilihanAjk) {
      errors.pemilihanAjk = t("fieldRequired");
    }
    if (!kekerapan) {
      errors.kekerapan = t("fieldRequired");
    }
    if (!tempohPelucutan || Number(tempohPelucutan) == 0) {
      errors.tempohPelucutan = t("fieldRequired");
    }
    if (!tempohPelucutanWaktu) {
      errors.tempohPelucutanWaktu = t("fieldRequired");
    }
    if (!jumlahPengerusi) {
      errors.jumlahPengerusi = t("fieldRequired");
    }
    if (!pengerusi) {
      errors.pengerusi = t("fieldRequired");
    }
    if (!jumlahTimbalan && isRequiredConstitution) {
      errors.jumlahTimbalan = t("fieldRequired");
    }
    if (!timbalan && isRequiredConstitution) {
      errors.timbalan = t("fieldRequired");
    }
    if (!jumlahNaib && isRequiredConstitution) {
      errors.jumlahNaib = t("fieldRequired");
    }
    if (!naib && isRequiredConstitution) {
      errors.naib = t("fieldRequired");
    }
    if (!jumlahSetiaUsaha) {
      errors.jumlahSetiaUsaha = t("fieldRequired");
    }
    if (!setiaUsaha) {
      errors.setiaUsaha = t("fieldRequired");
    }
    if (!jumlahPenolongSetiaUsaha && isRequiredConstitution) {
      errors.jumlahPenolongSetiaUsaha = t("fieldRequired");
    }
    if (!penolongSetiaUsaha && isRequiredConstitution) {
      errors.penolongSetiaUsaha = t("fieldRequired");
    }
    if (!jumlahBendahari) {
      errors.jumlahBendahari = t("fieldRequired");
    }
    if (!bendahari) {
      errors.bendahari = t("fieldRequired");
    }
    if (!jumlahPenolongBendahari && isRequiredConstitution) {
      errors.jumlahPenolongBendahari = t("fieldRequired");
    }
    if (!penolongBendahari && isRequiredConstitution) {
      errors.penolongBendahari = t("fieldRequired");
    }
    if (!jumlahAhliBiasa && isRequiredConstitution) {
      errors.jumlahAhliBiasa = t("fieldRequired");
    }
    if (!ahliBiasa && isRequiredConstitution) {
      errors.ahliBiasa = t("fieldRequired");
    }

    return errors;
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  const handleConfirm = () => {
    const designationCode = [];
    for (let i = 0; i < jumlahPengerusi; i++) {
      designationCode.push({
        designationCode: CommiteeEnum[pengerusi as keyof typeof CommiteeEnum],
        name: pengerusi,
      });
    }
    for (let i = 0; i < jumlahTimbalan; i++) {
      designationCode.push({
        designationCode: CommiteeEnum[timbalan as keyof typeof CommiteeEnum],
        name: timbalan,
      });
    }
    for (let i = 0; i < jumlahNaib; i++) {
      designationCode.push({
        designationCode: CommiteeEnum[naib as keyof typeof CommiteeEnum],
        name: naib,
      });
    }
    for (let i = 0; i < jumlahSetiaUsaha; i++) {
      designationCode.push({
        designationCode: CommiteeEnum[setiaUsaha as keyof typeof CommiteeEnum],
        name: setiaUsaha,
      });
    }
    for (let i = 0; i < jumlahPenolongSetiaUsaha; i++) {
      designationCode.push({
        designationCode:
          CommiteeEnum[penolongSetiaUsaha as keyof typeof CommiteeEnum],
        name: penolongSetiaUsaha,
      });
    }
    for (let i = 0; i < jumlahBendahari; i++) {
      designationCode.push({
        designationCode: CommiteeEnum[bendahari as keyof typeof CommiteeEnum],
        name: bendahari,
      });
    }
    for (let i = 0; i < jumlahPenolongBendahari; i++) {
      designationCode.push({
        designationCode:
          CommiteeEnum[penolongBendahari as keyof typeof CommiteeEnum],
        name: penolongBendahari,
      });
    }
    for (let i = 0; i < jumlahAhliBiasa; i++) {
      designationCode.push({
        designationCode: CommiteeEnum["Ahli Jawatankuasa Biasa"],
        name: "Ahli Jawatankuasa Biasa",
      });
    }
    // check existing custom jawatan
    for (let i = 0; i < existingCustomJawatanAhli.length; i++) {
      for (let j = 0; j < existingCustomJawatanBilangan.length; j++) {
        if (
          existingCustomJawatanAhli[i].matchId ===
          existingCustomJawatanBilangan[j].matchId
        ) {
          if (
            existingCustomJawatanAhli[i].definitionName
              .toLowerCase()
              .includes("setiausaha") ||
            existingCustomJawatanAhli[i].definitionName
              .toLowerCase()
              .includes("secretary")
          ) {
            for (
              let o = 0;
              o < Number(existingCustomJawatanBilangan[j].definitionName);
              o++
            ) {
              designationCode.push({
                designationCode: CommiteeEnum["Lain Lain Setiausaha"],
                name: "Lain Lain Setiausaha",
              });
            }
          } else {
            for (
              let h = 0;
              h < Number(existingCustomJawatanBilangan[j].definitionName);
              h++
            ) {
              designationCode.push({
                designationCode: "12",
                name: existingCustomJawatanAhli[i].definitionName,
              });
            }
          }
        }
      }
    }
    // custome ahli
    for (let i = 0; i < numberAddAhli.length; i++) {
      for (let j = 0; j < numberAddAhliBilangan.length; j++) {
        if (numberAddAhli[i].tempId === numberAddAhliBilangan[j].tempId) {
          if (
            numberAddAhli[i].definitionName
              .toLowerCase()
              .includes("setiausaha") ||
            numberAddAhli[i].definitionName.toLowerCase().includes("secretary")
          ) {
            for (
              let o = 0;
              o < Number(numberAddAhliBilangan[j].definitionName);
              o++
            ) {
              designationCode.push({
                designationCode: CommiteeEnum["Lain Lain Setiausaha"],
                name: "Lain Lain Setiausaha",
              });
            }
          } else {
            for (
              let h = 0;
              h < Number(numberAddAhliBilangan[j].definitionName);
              h++
            ) {
              designationCode.push({
                designationCode: "12",
                name: numberAddAhli[i].definitionName,
              });
            }
          }
        }
      }
    }

    setIsLoadingUpdate(true);
    preCreateAjk(
      {
        url: `${API_URL}/society/committee/updateForRegistration`,
        method: "put",
        values: {
          societyId,
          designationCode,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: false,
      },
      {
        onSuccess(data: any, variables: any, context: any) {
          handleSaveContent({
            i18n,
            societyId: societyDataRedux.id,
            societyName: societyDataRedux.societyName,
            clauseContentId,
            dataId,
            isEdit,
            createClauseContent,
            editClauseContent,
            description: clauseContent,
            modifiedTemplate: clauseContentEditable,
            constitutionValues: [
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: tempohJawatan,
                titleName: "Tempoh Pelantikan Jawatankuasa",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: pemilihanAjk,
                titleName: "Jenis Mesyuarat Agung",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: kekerapan,
                titleName: "Kekerapan Mesyuarat Jawatankuasa",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: tempohPelucutan,
                titleName:
                  "Bilangan tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: tempohPelucutanWaktu,
                titleName:
                  "Tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: pengerusi,
                titleName: "Pengerusi",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahPengerusi,
                titleName: "Bilangan Pengerusi",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: timbalan,
                titleName: "Timbalan Pengerusi",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahTimbalan,
                titleName: "Bilangan Timbalan Pengerusi",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: naib,
                titleName: "Naib Pengerusi",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahNaib,
                titleName: "Bilangan Naib Pengerusi",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: setiaUsaha,
                titleName: "Setiausaha",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahSetiaUsaha,
                titleName: "Bilangan Setiausaha",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: penolongSetiaUsaha,
                titleName: "Penolong Setiausaha",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahPenolongSetiaUsaha,
                titleName: "Bilangan Penolong Setiausaha",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: bendahari,
                titleName: "Bendahari",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahBendahari,
                titleName: "Bilangan Bendahari",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: penolongBendahari,
                titleName: "Penolong Bendahari",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahPenolongBendahari,
                titleName: "Bilangan Penolong Bendahari",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: ahliBiasa,
                titleName: "Ahli Jawatankuasa Biasa",
              },
              {
                constitutionContentId: null,
                societyName: namaPertubuhan,
                definitionName: jumlahAhliBiasa,
                titleName: "Bilangan Ahli Jawatankuasa Biasa",
              },
              ...numberAddAhliBilangan,
              ...numberAddAhli,
              ...existingCustomJawatanAhli,
              ...existingCustomJawatanBilangan,
            ],
            clause: "clause4",
            clauseCount: 4,
          });

          if (!isEditingContent) {
            setIsLoadingUpdate(false);
            setDialogSaveOpen(false);
          }
        },
      }
    );
  };

  const totalFixJawatan =
    parseInt(jumlahPengerusi || 0) +
    parseInt(jumlahTimbalan || 0) +
    parseInt(jumlahNaib || 0) +
    parseInt(jumlahSetiaUsaha || 0) +
    parseInt(jumlahPenolongSetiaUsaha || 0) +
    parseInt(jumlahBendahari || 0) +
    parseInt(jumlahPenolongBendahari || 0) +
    parseInt(jumlahAhliBiasa || 0);

  //
  const totalExistingCustomeJawatan = existingCustomJawatanBilangan.reduce(
    (sum, item) => {
      return sum + Number(item.definitionName);
    },
    0
  );

  const totalCustomeJawatan = numberAddAhliBilangan.reduce((sum, item) => {
    return sum + Number(item.definitionName);
  }, 0);

  const addAhliJawatanHandle = () => {
    const uniqueId = Math.random().toString(36).substring(2, 15);
    setNumberAddAhliBilangan([
      ...numberAddAhliBilangan,
      {
        tempId: uniqueId,
        constitutionContentId: null,
        societyName: namaPertubuhan,
        definitionName: "",
        titleName: "",
      },
    ]);

    setNumberAddAhli([
      ...numberAddAhli,
      {
        tempId: uniqueId,
        constitutionContentId: null,
        societyName: namaPertubuhan,
        definitionName: "",
        titleName: "",
        customJawatan: true,
      },
    ]);
  };

  const {
    data: customJawatanData,
    isLoading: customJawatanListIsLoading,
    refetch: refetchCustomJawatan,
  } = useCustom({
    url: `${API_URL}/society/constitutionvalue/callCustomJawatan`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: id,
        constitutionContentId: dataId,
        amendmentId: amendmentId,
      },
    },
    queryOptions: {
      enabled: !!amendmentId && !!dataId,
      cacheTime: 0,
      onSuccess: (data) => {
        const responseData = data?.data?.data || [];

        if (responseData && responseData.length > 0) {
          const newAhliBilanganItems = responseData.map(
            (data: any, index: number) => ({
              id: data?.countId,
              constitutionContentId: null,
              matchId: `${index + data?.id}`,
              societyName: namaPertubuhan,
              definitionName: data.countDefinitionName || "",
              titleName: data.countTitleName || "",
              amendmentId: data?.amendmentId,
            })
          );

          const newAhliItems = responseData.map((data: any, index: number) => ({
            id: data?.id,
            constitutionContentId: null,
            matchId: `${index + data?.id}`,
            societyName: namaPertubuhan,
            definitionName: data.definitionName || "",
            titleName: data.definitionName || "",
            customJawatan: true,
            amendmentId: data?.amendmentId,
          }));

          setExistingCustomJawatanBilangan((prev) => [
            ...prev,
            ...newAhliBilanganItems,
          ]);
          setExistingCustomJawatanAhli((prev) => [...prev, ...newAhliItems]);
        }
      },
    },
  });

  const { mutate: deleteCustomJawatan, isLoading: isDeleteCustomJawatan } =
    useCustomMutation();

  const deleteCustomJawatanFunc = () => {
    deleteCustomJawatan({
      url: `${API_URL}/society/constitutionvalue/deleteCustomJawatan?id=${selectedDeleteId}`,
      method: "delete",
      values: {},
      config: {
        headers: {
          "Content-Type": "application/json",
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
      successNotification: (data: any) => {
        setExistingCustomJawatanAhli(
          existingCustomJawatanAhli.filter(
            (items: any) => items.matchId !== selectedMatchId
          )
        );
        setExistingCustomJawatanBilangan(
          existingCustomJawatanBilangan.filter(
            (items: any) => items.matchId !== selectedMatchId
          )
        );
        setSelectedDeleteId(null);
        setSelectedMatchId(null);
        setShowConfirmDeleteDialog(false);
        return {
          message: data?.data?.msg,
          type: "success",
        };
      },
    });
  };

  const removeExistingAhliJawatanHandle = (
    matchId: string | number,
    id: number
  ) => {
    if (!id || !matchId) {
      return;
    }
    setSelectedDeleteId(id);
    setSelectedMatchId(matchId);
    setShowConfirmDeleteDialog(true);
  };

  const removeAhliJawatanHandle = (tempId: number) => {
    if (!tempId) {
      return;
    }

    setNumberAddAhliBilangan(
      numberAddAhliBilangan.filter((items: any) => items.tempId !== tempId)
    );
    setNumberAddAhli(
      numberAddAhli.filter((items: any) => items.tempId !== tempId)
    );
  };

  const handleOnchangeJawatanTitleChange = (
    tempId: number,
    field: keyof addJawatanTitleProps,
    value: string
  ) => {
    const normalizedInput = value.toLowerCase().trim();
    const isRestricted = restrictedTitles.some((term) =>
      normalizedInput.includes(term.toLowerCase())
    );

    if (isRestricted) {
      setShowIsRestrictedWord(true);
      return;
    }

    setNumberAddAhli(
      numberAddAhli.map((items: any) =>
        items.tempId === tempId
          ? { ...items, definitionName: value, titleName: value }
          : items
      )
    );
    setNumberAddAhliBilangan(
      numberAddAhliBilangan.map((items: any) =>
        items.tempId === tempId
          ? { ...items, titleName: `Bilangan ${value}` }
          : items
      )
    );
  };

  const handleOnchangeJawatanBilanganChange = (
    tempId: number,
    field: keyof addJawatanTitleProps,
    value: string
  ) => {
    let title = "";
    const findAhliId = numberAddAhli.filter(
      (items) => tempId?.toString() === items?.tempId?.toString()
    );
    if (findAhliId && findAhliId.length > 0) {
      title = findAhliId?.[0]?.definitionName;
    }
    setNumberAddAhliBilangan(
      numberAddAhliBilangan.map((items: any) =>
        items.tempId === tempId
          ? { ...items, definitionName: value, titleName: `Bilangan ${title}` }
          : items
      )
    );
  };

  // ======================================================

  const handleOnchangeExistingJawatanTitleChange = (
    id: number,
    field: keyof addJawatanTitleProps,
    value: string
  ) => {
    const normalizedInput = value.toLowerCase().trim();
    const isRestricted = restrictedTitles.some((term) =>
      normalizedInput.includes(term.toLowerCase())
    );

    if (isRestricted) {
      setShowIsRestrictedWord(true);
      return;
    }

    setExistingCustomJawatanAhli(
      existingCustomJawatanAhli.map((items: any) =>
        items.matchId === id
          ? { ...items, definitionName: value, titleName: value }
          : items
      )
    );
    setExistingCustomJawatanBilangan(
      existingCustomJawatanBilangan.map((items: any) =>
        items.matchId === id
          ? { ...items, titleName: `Bilangan ${value}` }
          : items
      )
    );
  };

  const handleOnchangeExistingJawatanBilanganChange = (
    id: number,
    field: keyof addJawatanTitleProps,
    value: string
  ) => {
    let title = "";
    const findAhliId = existingCustomJawatanAhli.filter(
      (items) => items?.matchId?.toString() === id.toString()
    );
    if (findAhliId && findAhliId.length > 0) {
      title = findAhliId?.[0]?.definitionName;
    }
    setExistingCustomJawatanBilangan(
      existingCustomJawatanBilangan.map((items: any) =>
        items.matchId === id
          ? { ...items, definitionName: value, titleName: `Bilangan ${title}` }
          : items
      )
    );
  };

  return (
    <>
      <Grid container>
        {/* name section */}
        <FasalNameCom clauseId={id} name={name} />
        <Grid item xs={12}>
          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
                mb: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("positionOfAuthority")}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Typography sx={labelStyle}>
                    {t("jenisMesyuaratAgung")}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControl
                    fullWidth
                    required
                    error={!!formErrors.pemilihanAjk}
                  >
                    <Select
                      size="small"
                      value={pemilihanAjk}
                      displayEmpty
                      onChange={(e) => {
                        setPemilihanAjk(e.target.value as string);
                        if ((e.target.value as string) == t("annual")) {
                          setTempohJawatan(t("setahun"));
                        } else if (
                          (e.target.value as string) == t("biennial")
                        ) {
                          setTempohJawatan(t("duaTahun"));
                        }
                        setFormErrors((prevErrors) => ({
                          ...prevErrors,
                          pemilihanAjk: "",
                        }));
                      }}
                    >
                      <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                      <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
                    </Select>
                  </FormControl>
                  {formErrors.pemilihanAjk && (
                    <FormHelperText sx={{ color: "red" }}>
                      {formErrors.pemilihanAjk}
                    </FormHelperText>
                  )}
                </Grid>
              </Grid>

              <Grid container spacing={2} sx={{ mt: 0 }}>
                <Grid item xs={12} md={4}>
                  <Typography sx={labelStyle}>
                    {t("electionPeriod")}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControl
                    fullWidth
                    required
                    error={!!formErrors.tempohJawatan}
                  >
                    <Select
                      size="small"
                      value={tempohJawatan}
                      disabled={pemilihanAjk == t("biennial")}
                      displayEmpty
                      onChange={(e) => {
                        setTempohJawatan(e.target.value as string);
                        setFormErrors((prevErrors) => ({
                          ...prevErrors,
                          tempohJawatan: "",
                        }));
                      }}
                    >
                      <MenuItem value={t("setahun")}>{t("setahun")}</MenuItem>
                      <MenuItem value={t("duaTahun")}>{t("duaTahun")}</MenuItem>
                    </Select>
                  </FormControl>
                  {formErrors.tempohJawatan && (
                    <FormHelperText sx={{ color: "red" }}>
                      {formErrors.tempohJawatan}
                    </FormHelperText>
                  )}
                </Grid>
              </Grid>

              <Grid container spacing={2} sx={{ mt: 0 }}>
                <Grid item xs={12} md={4}>
                  <Typography sx={labelStyle}>
                    {t("meetingFrequency")}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    type="number"
                    onKeyDown={(e) => {
                      if (
                        e.key.toLowerCase() === "e" ||
                        e.key === "E" ||
                        e.key === "+" ||
                        e.key === "-"
                      ) {
                        e.preventDefault();
                      }
                    }}
                    size="small"
                    placeholder="4"
                    fullWidth
                    required
                    value={kekerapan}
                    onChange={(e) => {
                      setKekerapan(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        kekerapan: "",
                      }));
                    }}
                    error={!!formErrors.kekerapan}
                    helperText={formErrors.kekerapan}
                    InputProps={{
                      endAdornment: (
                        <Typography sx={{ ...labelStyle, mt: 1 }}>
                          {t("times")}
                        </Typography>
                      ),
                      inputProps: {
                        inputMode: "numeric",
                        pattern: "[0-9]*",
                        min: 0,
                      },
                    }}
                  />
                </Grid>
                <Grid item />
              </Grid>
              <Grid container spacing={2} sx={{ mt: 0 }}>
                <Grid item xs={12} md={4}>
                  <Typography sx={labelStyle}>
                    {t("periodOfDefendingOneselfTwo")}
                    <Typography sx={{ display: "inline", color: "red" }}>
                      *
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} md={2}>
                  <TextField
                    type="number"
                    onKeyDown={(e) => {
                      if (
                        e.key.toLowerCase() === "e" ||
                        e.key === "E" ||
                        e.key === "+" ||
                        e.key === "-"
                      ) {
                        e.preventDefault();
                      }
                    }}
                    size="small"
                    placeholder="0"
                    fullWidth
                    required
                    value={tempohPelucutan}
                    onChange={(e) => {
                      setTempohPelucutan(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        tempohPelucutan: "",
                      }));
                    }}
                    error={!!formErrors.tempohPelucutan}
                    helperText={formErrors.tempohPelucutan}
                    InputProps={{
                      inputProps: {
                        inputMode: "numeric",
                        pattern: "[0-9]*",
                        min: 0,
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <FormControl
                    fullWidth
                    required
                    error={!!formErrors.tempohPelucutanWaktu}
                  >
                    <Select
                      size="small"
                      value={tempohPelucutanWaktu}
                      displayEmpty
                      onChange={(e) => {
                        setTempohPelucutanWaktu(e.target.value as string);
                        setFormErrors((prevErrors) => ({
                          ...prevErrors,
                          tempohPelucutanWaktu: "",
                        }));
                      }}
                      disabled
                    >
                      <MenuItem value={t("day")}>{t("day")}</MenuItem>
                      <MenuItem value={t("week")}>{t("week")}</MenuItem>
                      <MenuItem value={t("month")}>{t("month")}</MenuItem>
                      <MenuItem value={t("year")}>{t("year")}</MenuItem>
                    </Select>
                  </FormControl>
                  {formErrors.tempohPelucutanWaktu && (
                    <FormHelperText sx={{ color: "red" }}>
                      {formErrors.tempohPelucutanWaktu}
                    </FormHelperText>
                  )}
                </Grid>
              </Grid>
            </Box>

            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("ahliJawatanKuasa")}
              </Typography>
              <Grid
                container
                spacing={2}
                sx={{ mt: 0, display: "flex", justifyContent: "flex-end" }}
              >
                <Grid
                  item
                  xs={12}
                  md={6}
                  gap={1}
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                    alignItems: "center",
                  }}
                >
                  <ButtonOutline onClick={addAhliJawatanHandle}>
                    Tambah Ahli Jawatankuasa
                  </ButtonOutline>
                </Grid>
                <Grid item xs={12} md={4} />
              </Grid>

              <Grid container spacing={2} sx={{ mt: 0 }}>
                <Grid item xs={12} md={2}>
                  <Typography variant="subtitle1" sx={sectionStyle}>
                    {t("NumberofPositions")}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" sx={sectionStyle}>
                    {t("JobTitle")}
                  </Typography>
                </Grid>
              </Grid>
              <Grid container spacing={2}>
                <Grid item xs={12} md={2}>
                  <TextField
                    type="number"
                    onKeyDown={(e) => {
                      if (
                        e.key.toLowerCase() === "e" ||
                        e.key === "E" ||
                        e.key === "+" ||
                        e.key === "-"
                      ) {
                        e.preventDefault();
                      }
                    }}
                    size="small"
                    placeholder="0"
                    fullWidth
                    disabled
                    sx={{ background: "#E8E9E8" }}
                    required
                    value={jumlahPengerusi}
                    onChange={(e) => {
                      setJumlahPengerusi(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        jumlahPengerusi: "",
                      }));
                    }}
                    error={!!formErrors.jumlahPengerusi}
                    helperText={formErrors.jumlahPengerusi}
                    InputProps={{
                      inputProps: {
                        inputMode: "numeric",
                        pattern: "[0-9]*",
                        min: 0,
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl
                    fullWidth
                    required
                    error={!!formErrors.pengerusi}
                  >
                    <Select
                      size="small"
                      value={pengerusi}
                      displayEmpty
                      onChange={(e) => {
                        setPengerusi(e.target.value as string);
                        setFormErrors((prevErrors) => ({
                          ...prevErrors,
                          pengerusi: "",
                        }));
                      }}
                    >
                      <MenuItem value={t("chairman")}>
                        {t("chairman")}{" "}
                        <Typography sx={{ display: "inline", color: "red" }}>
                          *
                        </Typography>
                      </MenuItem>
                      <MenuItem value={t("presiden")}>
                        {t("presiden")}{" "}
                        <Typography sx={{ display: "inline", color: "red" }}>
                          *
                        </Typography>
                      </MenuItem>
                      <MenuItem value={t("pengarah")}>
                        {t("pengarah")}{" "}
                        <Typography sx={{ display: "inline", color: "red" }}>
                          *
                        </Typography>
                      </MenuItem>
                      <MenuItem value={t("thepresident")}>
                        {t("thepresident")}
                      </MenuItem>
                    </Select>
                  </FormControl>
                  {formErrors.pengerusi && (
                    <FormHelperText sx={{ color: "red" }}>
                      {formErrors.pengerusi}
                    </FormHelperText>
                  )}
                </Grid>
                <Grid item />
              </Grid>

              <Grid container spacing={2} sx={{ mt: 0 }}>
                <Grid item xs={12} md={2}>
                  <TextField
                    type="number"
                    onKeyDown={(e) => {
                      if (
                        e.key.toLowerCase() === "e" ||
                        e.key === "E" ||
                        e.key === "+" ||
                        e.key === "-"
                      ) {
                        e.preventDefault();
                      }
                    }}
                    size="small"
                    placeholder="0"
                    fullWidth
                    required={isRequiredConstitution}
                    value={jumlahTimbalan}
                    onChange={(e) => {
                      console.log("e.target.value", e.target.value);
                      setJumlahTimbalan(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        jumlahTimbalan: "",
                      }));
                    }}
                    error={!!formErrors.jumlahTimbalan}
                    helperText={formErrors.jumlahTimbalan}
                    InputProps={{
                      inputProps: {
                        inputMode: "numeric",
                        pattern: "[0-9]*",
                        min: 0,
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  {isRequiredConstitution ? (
                    <FormControl
                      fullWidth
                      required
                      error={!!formErrors.timbalan}
                    >
                      <Select
                        size="small"
                        value={timbalan}
                        displayEmpty
                        onChange={(e) => {
                          setTimbalan(e.target.value as string);
                          setFormErrors((prevErrors) => ({
                            ...prevErrors,
                            timbalan: "",
                          }));
                        }}
                      >
                        <MenuItem value={t("timbalanPengerusi")}>
                          {t("timbalanPengerusi")}
                        </MenuItem>
                        <MenuItem value={t("vicePresident")}>
                          {t("vicePresident")}
                        </MenuItem>
                        <MenuItem value={t("timbalanPengarah")}>
                          {t("timbalanPengarah")}
                        </MenuItem>
                      </Select>
                    </FormControl>
                  ) : (
                    <FormControl fullWidth error={!!formErrors.timbalan}>
                      <Select
                        size="small"
                        value={timbalan}
                        displayEmpty
                        onChange={(e) => {
                          setTimbalan(e.target.value as string);
                          setFormErrors((prevErrors) => ({
                            ...prevErrors,
                            timbalan: "",
                          }));
                        }}
                      >
                        <MenuItem value={t("timbalanPengerusi")}>
                          {t("timbalanPengerusi")}
                        </MenuItem>
                        <MenuItem value={t("vicePresident")}>
                          {t("vicePresident")}
                        </MenuItem>
                        <MenuItem value={t("timbalanPengarah")}>
                          {t("timbalanPengarah")}
                        </MenuItem>
                      </Select>
                    </FormControl>
                  )}

                  {formErrors.timbalan && (
                    <FormHelperText sx={{ color: "red" }}>
                      {formErrors.timbalan}
                    </FormHelperText>
                  )}
                </Grid>

                <Grid item />
              </Grid>

              <Grid container spacing={2} sx={{ mt: 0 }}>
                <Grid item xs={12} md={2}>
                  <TextField
                    type="number"
                    onKeyDown={(e) => {
                      if (
                        e.key.toLowerCase() === "e" ||
                        e.key === "E" ||
                        e.key === "+" ||
                        e.key === "-"
                      ) {
                        e.preventDefault();
                      }
                    }}
                    size="small"
                    placeholder="0"
                    fullWidth
                    required={isRequiredConstitution}
                    value={jumlahNaib}
                    onChange={(e) => {
                      setJumlahNaib(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        jumlahNaib: "",
                      }));
                    }}
                    error={!!formErrors.jumlahNaib}
                    helperText={formErrors.jumlahNaib}
                    InputProps={{
                      inputProps: {
                        inputMode: "numeric",
                        pattern: "[0-9]*",
                        min: 0,
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  {isRequiredConstitution ? (
                    <FormControl fullWidth required error={!!formErrors.naib}>
                      <Select
                        size="small"
                        value={naib}
                        displayEmpty
                        onChange={(e) => {
                          setNaib(e.target.value as string);
                          setFormErrors((prevErrors) => ({
                            ...prevErrors,
                            naib: "",
                          }));
                        }}
                      >
                        <MenuItem value={t("naibPengerusi")}>
                          {t("naibPengerusi")}
                        </MenuItem>
                        <MenuItem value={t("naibPresiden")}>
                          {t("naibPresiden")}
                        </MenuItem>
                        <MenuItem value={t("naibPengarah")}>
                          {t("naibPengarah")}
                        </MenuItem>
                      </Select>
                    </FormControl>
                  ) : (
                    <FormControl fullWidth error={!!formErrors.naib}>
                      <Select
                        size="small"
                        value={naib}
                        displayEmpty
                        onChange={(e) => {
                          setNaib(e.target.value as string);
                          setFormErrors((prevErrors) => ({
                            ...prevErrors,
                            naib: "",
                          }));
                        }}
                      >
                        <MenuItem value={t("naibPengerusi")}>
                          {t("naibPengerusi")}
                        </MenuItem>
                        <MenuItem value={t("naibPresiden")}>
                          {t("naibPresiden")}
                        </MenuItem>
                        <MenuItem value={t("naibPengarah")}>
                          {t("naibPengarah")}
                        </MenuItem>
                      </Select>
                    </FormControl>
                  )}

                  {formErrors.naib && (
                    <FormHelperText sx={{ color: "red" }}>
                      {formErrors.naib}
                    </FormHelperText>
                  )}
                </Grid>

                <Grid item />
              </Grid>

              <Grid container spacing={2} sx={{ mt: 0 }}>
                <Grid item xs={12} md={2}>
                  <TextField
                    type="number"
                    onKeyDown={(e) => {
                      if (
                        e.key.toLowerCase() === "e" ||
                        e.key === "E" ||
                        e.key === "+" ||
                        e.key === "-"
                      ) {
                        e.preventDefault();
                      }
                    }}
                    size="small"
                    placeholder="0"
                    fullWidth
                    required
                    disabled
                    sx={{ background: "#E8E9E8" }}
                    value={jumlahSetiaUsaha}
                    onChange={(e) => {
                      setJumlahSetiaUsaha(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        jumlahSetiaUsaha: "",
                      }));
                    }}
                    error={!!formErrors.jumlahSetiaUsaha}
                    helperText={formErrors.jumlahSetiaUsaha}
                    InputProps={{
                      inputProps: {
                        inputMode: "numeric",
                        pattern: "[0-9]*",
                        min: 0,
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl
                    fullWidth
                    required
                    error={!!formErrors.setiaUsaha}
                  >
                    <Select
                      size="small"
                      value={setiaUsaha}
                      displayEmpty
                      onChange={(e) => {
                        setSetiaUsaha(e.target.value as string);
                        setFormErrors((prevErrors) => ({
                          ...prevErrors,
                          setiaUsaha: "",
                        }));
                      }}
                    >
                      <MenuItem value={t("secretary")}>
                        {t("secretary")}{" "}
                        <Typography sx={{ display: "inline", color: "red" }}>
                          *
                        </Typography>
                      </MenuItem>
                      <MenuItem value={t("generalSecretary")}>
                        {t("generalSecretary")}{" "}
                        <Typography sx={{ display: "inline", color: "red" }}>
                          *
                        </Typography>
                      </MenuItem>
                    </Select>
                  </FormControl>
                  {formErrors.setiaUsaha && (
                    <FormHelperText sx={{ color: "red" }}>
                      {formErrors.setiaUsaha}
                    </FormHelperText>
                  )}
                </Grid>

                <Grid item />
              </Grid>

              <Grid container spacing={2} sx={{ mt: 0 }}>
                <Grid item xs={12} md={2}>
                  <TextField
                    type="number"
                    onKeyDown={(e) => {
                      if (
                        e.key.toLowerCase() === "e" ||
                        e.key === "E" ||
                        e.key === "+" ||
                        e.key === "-"
                      ) {
                        e.preventDefault();
                      }
                    }}
                    size="small"
                    placeholder="0"
                    fullWidth
                    required={isRequiredConstitution}
                    value={jumlahPenolongSetiaUsaha}
                    onChange={(e) => {
                      setJumlahPenolongSetiaUsaha(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        jumlahPenolongSetiaUsaha: "",
                      }));
                    }}
                    error={!!formErrors.jumlahPenolongSetiaUsaha}
                    helperText={formErrors.jumlahPenolongSetiaUsaha}
                    InputProps={{
                      inputProps: {
                        inputMode: "numeric",
                        pattern: "[0-9]*",
                        min: 0,
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  {isRequiredConstitution ? (
                    <FormControl
                      fullWidth
                      required
                      error={!!formErrors.penolongSetiaUsaha}
                    >
                      <Select
                        size="small"
                        value={penolongSetiaUsaha}
                        displayEmpty
                        onChange={(e) => {
                          setPenolongSetiaUsaha(e.target.value as string);
                          setFormErrors((prevErrors) => ({
                            ...prevErrors,
                            penolongSetiaUsaha: "",
                          }));
                        }}
                      >
                        <MenuItem value={t("asistantSecretary")}>
                          {t("asistantSecretary")}
                        </MenuItem>
                        <MenuItem value={t("generalAssistantSecretary")}>
                          {t("generalAssistantSecretary")}
                        </MenuItem>
                      </Select>
                    </FormControl>
                  ) : (
                    <FormControl
                      fullWidth
                      error={!!formErrors.penolongSetiaUsaha}
                    >
                      <Select
                        size="small"
                        value={penolongSetiaUsaha}
                        displayEmpty
                        onChange={(e) => {
                          setPenolongSetiaUsaha(e.target.value as string);
                          setFormErrors((prevErrors) => ({
                            ...prevErrors,
                            penolongSetiaUsaha: "",
                          }));
                        }}
                      >
                        <MenuItem value={t("asistantSecretary")}>
                          {t("asistantSecretary")}
                        </MenuItem>
                        <MenuItem value={t("generalAssistantSecretary")}>
                          {t("generalAssistantSecretary")}
                        </MenuItem>
                      </Select>
                    </FormControl>
                  )}

                  {formErrors.penolongSetiaUsaha && (
                    <FormHelperText sx={{ color: "red" }}>
                      {formErrors.penolongSetiaUsaha}
                    </FormHelperText>
                  )}
                </Grid>

                <Grid item />
              </Grid>

              <Grid container spacing={2} sx={{ mt: 0 }}>
                <Grid item xs={12} md={2}>
                  <TextField
                    type="number"
                    onKeyDown={(e) => {
                      if (
                        e.key.toLowerCase() === "e" ||
                        e.key === "E" ||
                        e.key === "+" ||
                        e.key === "-"
                      ) {
                        e.preventDefault();
                      }
                    }}
                    size="small"
                    placeholder="0"
                    fullWidth
                    required
                    disabled
                    sx={{ background: "#E8E9E8" }}
                    value={jumlahBendahari}
                    onChange={(e) => {
                      setJumlahBendahari(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        jumlahBendahari: "",
                      }));
                    }}
                    error={!!formErrors.jumlahBendahari}
                    helperText={formErrors.jumlahBendahari}
                    InputProps={{
                      inputProps: {
                        inputMode: "numeric",
                        pattern: "[0-9]*",
                        min: 0,
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl
                    fullWidth
                    required
                    error={!!formErrors.bendahari}
                  >
                    <Select
                      size="small"
                      value={bendahari}
                      displayEmpty
                      onChange={(e) => {
                        setBendahari(e.target.value as string);
                        setFormErrors((prevErrors) => ({
                          ...prevErrors,
                          bendahari: "",
                        }));
                      }}
                    >
                      <MenuItem value={t("treasurer")}>
                        {t("bendahari")}{" "}
                        <Typography sx={{ display: "inline", color: "red" }}>
                          *
                        </Typography>
                      </MenuItem>
                      <MenuItem value={t("chiefTreasurer")}>
                        {t("chiefTreasurer")}{" "}
                        <Typography sx={{ display: "inline", color: "red" }}>
                          *
                        </Typography>
                      </MenuItem>
                      {/* <MenuItem value={t("honoraryTreasurer")}>
                      {t("honoraryTreasurer")}{" "}
                      <Typography sx={{ display: "inline", color: "red" }}>
                        *
                      </Typography>
                    </MenuItem> */}
                    </Select>
                  </FormControl>
                  {formErrors.bendahari && (
                    <FormHelperText sx={{ color: "red" }}>
                      {formErrors.bendahari}
                    </FormHelperText>
                  )}
                </Grid>

                <Grid item />
              </Grid>

              <Grid container spacing={2} sx={{ mt: 0 }}>
                <Grid item xs={12} md={2}>
                  <TextField
                    type="number"
                    onKeyDown={(e) => {
                      if (
                        e.key.toLowerCase() === "e" ||
                        e.key === "E" ||
                        e.key === "+" ||
                        e.key === "-"
                      ) {
                        e.preventDefault();
                      }
                    }}
                    size="small"
                    placeholder="0"
                    fullWidth
                    required={isRequiredConstitution}
                    value={jumlahPenolongBendahari}
                    onChange={(e) => {
                      setJumlahPenolongBendahari(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        jumlahPenolongBendahari: "",
                      }));
                    }}
                    error={!!formErrors.jumlahPenolongBendahari}
                    helperText={formErrors.jumlahPenolongBendahari}
                    InputProps={{
                      inputProps: {
                        inputMode: "numeric",
                        pattern: "[0-9]*",
                        min: 0,
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  {isRequiredConstitution ? (
                    <FormControl
                      fullWidth
                      required
                      error={!!formErrors.penolongBendahari}
                    >
                      <Select
                        size="small"
                        value={penolongBendahari}
                        displayEmpty
                        onChange={(e) => {
                          setPenolongBendahari(e.target.value as string);
                          setFormErrors((prevErrors) => ({
                            ...prevErrors,
                            penolongBendahari: "",
                          }));
                        }}
                      >
                        <MenuItem value={t("asistantTreasurer")}>
                          {t("asistantTreasurer")}
                        </MenuItem>
                        <MenuItem value={t("chiefAssistantTreasurer")}>
                          {t("chiefAssistantTreasurer")}
                        </MenuItem>
                        {/* <MenuItem value={t("honoraryAssistantTreasurer")}>
                        {t("honoraryAssistantTreasurer")}
                      </MenuItem> */}
                      </Select>
                    </FormControl>
                  ) : (
                    <FormControl
                      fullWidth
                      error={!!formErrors.penolongBendahari}
                    >
                      <Select
                        size="small"
                        value={penolongBendahari}
                        displayEmpty
                        onChange={(e) => {
                          setPenolongBendahari(e.target.value as string);
                          setFormErrors((prevErrors) => ({
                            ...prevErrors,
                            penolongBendahari: "",
                          }));
                        }}
                      >
                        <MenuItem value={t("asistantTreasurer")}>
                          {t("asistantTreasurer")}
                        </MenuItem>
                        <MenuItem value={t("chiefAssistantTreasurer")}>
                          {t("chiefAssistantTreasurer")}
                        </MenuItem>
                        {/* <MenuItem value={t("honoraryAssistantTreasurer")}>
                        {t("honoraryAssistantTreasurer")}
                      </MenuItem> */}
                      </Select>
                    </FormControl>
                  )}

                  {formErrors.penolongBendahari && (
                    <FormHelperText sx={{ color: "red" }}>
                      {formErrors.penolongBendahari}
                    </FormHelperText>
                  )}
                </Grid>

                <Grid item />
              </Grid>

              <Grid container spacing={2} sx={{ mt: 0 }}>
                <Grid item xs={12} md={2}>
                  <TextField
                    type="number"
                    onKeyDown={(e) => {
                      if (
                        e.key.toLowerCase() === "e" ||
                        e.key === "E" ||
                        e.key === "+" ||
                        e.key === "-"
                      ) {
                        e.preventDefault();
                      }
                    }}
                    size="small"
                    placeholder="0"
                    fullWidth
                    required={isRequiredConstitution}
                    value={jumlahAhliBiasa}
                    onChange={(e) => {
                      setJumlahAhliBiasa(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        jumlahAhliBiasa: "",
                      }));
                    }}
                    error={!!formErrors.jumlahAhliBiasa}
                    helperText={formErrors.jumlahAhliBiasa}
                    InputProps={{
                      inputProps: {
                        inputMode: "numeric",
                        pattern: "[0-9]*",
                        min: 0,
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  {isRequiredConstitution ? (
                    <FormControl
                      fullWidth
                      required
                      error={!!formErrors.ahliBiasa}
                    >
                      <Select
                        size="small"
                        value={ahliBiasa}
                        displayEmpty
                        onChange={(e) => {
                          setAhliBiasa(e.target.value as string);
                          setFormErrors((prevErrors) => ({
                            ...prevErrors,
                            ahliBiasa: "",
                          }));
                        }}
                      >
                        <MenuItem value={t("ordinaryCommitteeMember")}>
                          {t("ordinaryCommitteeMember")}
                        </MenuItem>
                      </Select>
                    </FormControl>
                  ) : (
                    <FormControl fullWidth error={!!formErrors.ahliBiasa}>
                      <Select
                        size="small"
                        value={ahliBiasa}
                        displayEmpty
                        onChange={(e) => {
                          setAhliBiasa(e.target.value as string);
                          setFormErrors((prevErrors) => ({
                            ...prevErrors,
                            ahliBiasa: "",
                          }));
                        }}
                      >
                        <MenuItem value={t("ordinaryCommitteeMember")}>
                          {t("ordinaryCommitteeMember")}
                        </MenuItem>
                      </Select>
                    </FormControl>
                  )}

                  {formErrors.ahliBiasa && (
                    <FormHelperText sx={{ color: "red" }}>
                      {formErrors.ahliBiasa}
                    </FormHelperText>
                  )}
                </Grid>

                <Grid item />
              </Grid>

              {existingCustomJawatanAhli && existingCustomJawatanAhli.length > 0
                ? existingCustomJawatanAhli.map((data: any, index) => {
                    return (
                      <ExistingAhliJawatanComponent
                        key={index}
                        allBilanganData={existingCustomJawatanBilangan}
                        ahliData={data}
                        removeHandler={removeExistingAhliJawatanHandle}
                        onChangeTitleHandler={
                          handleOnchangeExistingJawatanTitleChange
                        }
                        onChangeBilanganHandler={
                          handleOnchangeExistingJawatanBilanganChange
                        }
                      />
                    );
                  })
                : null}

              {/* add ahli jawantan */}
              {numberAddAhli && numberAddAhli.length > 0
                ? numberAddAhli.map((data: any, index) => {
                    return (
                      <AddAhliJawatanComponent
                        key={`${index}${data.tempId}`}
                        allBilanganData={numberAddAhliBilangan}
                        data={data}
                        removeHandler={removeAhliJawatanHandle}
                        onChangeTitleHandler={handleOnchangeJawatanTitleChange}
                        onChangeBilanganHandler={
                          handleOnchangeJawatanBilanganChange
                        }
                      />
                    );
                  })
                : null}
              {/*  */}

              <Grid container spacing={2} sx={{ mt: 0 }}>
                <Grid item xs={12} md={8}>
                  <Typography
                    sx={{
                      fontWeight: "500 !important",
                      color: "#666666",
                      border: "1px solid #DADADA",
                      borderRadius: "5px",
                      py: 1,
                      display: "flex",
                      justifyContent: "center",
                    }}
                  >
                    {totalFixJawatan} Bilangan Ahli Jawatankuasa
                  </Typography>
                </Grid>
                <Grid item />
              </Grid>
            </Box>
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <ReminderEditable />
            <EditableFasalTextArea
              requiredFieldText={requiredText}
              clauseContentEditable={clauseContentEditable}
              setClauseContentEditable={handlerChangeClauseContentEditable}
            />
          </Box>

          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
            }}
          >
            <ContentBox clauseContent={clauseContent} />
            <CheckContent checked={checked} onChange={handleChangeCheckbox} />
            {/* submit */}
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                variant="contained"
                sx={{
                  width: isMobile ? "100%" : "auto",
                  display: isViewMode ? "none" : "block",
                }}
                onClick={() => {
                  //const tempArrayAJK: AJKState[] = [];
                  const errors = validateForm();
                  if (Object.keys(errors).length > 0) {
                    setFormErrors(errors);
                    return;
                  }
                  const totalBilangan =
                    totalFixJawatan +
                    totalCustomeJawatan +
                    totalExistingCustomeJawatan;

                  if (totalBilangan < 7) {
                    setShowErrorAjk(true);
                    return;
                  }
                  const designationCode = [];
                  for (let i = 0; i < jumlahPengerusi; i++) {
                    designationCode.push({
                      designationCode:
                        CommiteeEnum[pengerusi as keyof typeof CommiteeEnum],
                      name: pengerusi,
                    });
                  }
                  for (let i = 0; i < jumlahTimbalan; i++) {
                    designationCode.push({
                      designationCode:
                        CommiteeEnum[timbalan as keyof typeof CommiteeEnum],
                      name: timbalan,
                    });
                  }
                  for (let i = 0; i < jumlahNaib; i++) {
                    designationCode.push({
                      designationCode:
                        CommiteeEnum[naib as keyof typeof CommiteeEnum],
                      name: naib,
                    });
                  }
                  for (let i = 0; i < jumlahSetiaUsaha; i++) {
                    designationCode.push({
                      designationCode:
                        CommiteeEnum[setiaUsaha as keyof typeof CommiteeEnum],
                      name: setiaUsaha,
                    });
                  }
                  for (let i = 0; i < jumlahPenolongSetiaUsaha; i++) {
                    designationCode.push({
                      designationCode:
                        CommiteeEnum[
                          penolongSetiaUsaha as keyof typeof CommiteeEnum
                        ],
                      name: penolongSetiaUsaha,
                    });
                  }
                  for (let i = 0; i < jumlahBendahari; i++) {
                    designationCode.push({
                      designationCode:
                        CommiteeEnum[bendahari as keyof typeof CommiteeEnum],
                      name: bendahari,
                    });
                  }
                  for (let i = 0; i < jumlahPenolongBendahari; i++) {
                    designationCode.push({
                      designationCode:
                        CommiteeEnum[
                          penolongBendahari as keyof typeof CommiteeEnum
                        ],
                      name: penolongBendahari,
                    });
                  }
                  for (let i = 0; i < jumlahAhliBiasa; i++) {
                    designationCode.push({
                      designationCode: CommiteeEnum["Ahli Jawatankuasa Biasa"],
                      name: "Ahli Jawatankuasa Biasa",
                    });
                  }
                  console.log(existingCustomJawatanAhli);
                  // check existing custom jawatan
                  for (let i = 0; i < existingCustomJawatanAhli.length; i++) {
                    for (
                      let j = 0;
                      j < existingCustomJawatanBilangan.length;
                      j++
                    ) {
                      if (
                        existingCustomJawatanAhli[i].matchId ===
                        existingCustomJawatanBilangan[j].matchId
                      ) {
                        if (
                          existingCustomJawatanAhli[i].definitionName
                            .toLowerCase()
                            .includes("setiausaha") ||
                          existingCustomJawatanAhli[i].definitionName
                            .toLowerCase()
                            .includes("secretary")
                        ) {
                          for (
                            let o = 0;
                            o <
                            Number(
                              existingCustomJawatanBilangan[j].definitionName
                            );
                            o++
                          ) {
                            designationCode.push({
                              designationCode:
                                CommiteeEnum["Lain Lain Setiausaha"],
                              name: "Lain Lain Setiausaha",
                            });
                          }
                        } else {
                          for (
                            let h = 0;
                            h <
                            Number(
                              existingCustomJawatanBilangan[j].definitionName
                            );
                            h++
                          ) {
                            designationCode.push({
                              designationCode: "12",
                              name: existingCustomJawatanAhli[i].definitionName,
                            });
                          }
                        }
                      }
                    }
                  }
                  // custome ahli
                  for (let i = 0; i < numberAddAhli.length; i++) {
                    for (let j = 0; j < numberAddAhliBilangan.length; j++) {
                      if (
                        numberAddAhli[i].tempId ===
                        numberAddAhliBilangan[j].tempId
                      ) {
                        if (
                          numberAddAhli[i].definitionName
                            .toLowerCase()
                            .includes("setiausaha") ||
                          numberAddAhli[i].definitionName
                            .toLowerCase()
                            .includes("secretary")
                        ) {
                          for (
                            let o = 0;
                            o < Number(numberAddAhliBilangan[j].definitionName);
                            o++
                          ) {
                            designationCode.push({
                              designationCode:
                                CommiteeEnum["Lain Lain Setiausaha"],
                              name: "Lain Lain Setiausaha",
                            });
                          }
                        } else {
                          for (
                            let h = 0;
                            h < Number(numberAddAhliBilangan[j].definitionName);
                            h++
                          ) {
                            designationCode.push({
                              designationCode: "12",
                              name: numberAddAhli[i].definitionName,
                            });
                          }
                        }
                      }
                    }
                  }
                  //dispatch(setAJK(tempArrayAJK));*/
                  if (isEdit || committeeData.length > 0) {
                  } else {
                    createClauseContent(
                      {
                        resource: "society/committee/createForRegistration",
                        values: {
                          societyId,
                          designationCode,
                        },
                        meta: {
                          headers: {
                            portal: localStorage.getItem("portal"),
                            authorization: `Bearer ${localStorage.getItem(
                              "refine-auth"
                            )}`,
                          },
                        },
                        successNotification: false,
                      },
                      {
                        onSuccess(data: any, variables: any, context: any) {},
                      }
                    );
                  }

                  handleSaveContent({
                    i18n,
                    societyId: societyDataRedux.id,
                    societyName: societyDataRedux.societyName,
                    clauseContentId,
                    dataId,
                    isEdit,
                    clauseNo: clauseNo,
                    clauseName: clauseName,
                    createClauseContent,
                    editClauseContent,
                    description: clauseContent,
                    modifiedTemplate: clauseContentEditable,
                    constitutionValues: [
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: tempohJawatan,
                        titleName: "Tempoh Pelantikan Jawatankuasa",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: pemilihanAjk,
                        titleName: "Jenis Mesyuarat Agung",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: kekerapan,
                        titleName: "Kekerapan Mesyuarat Jawatankuasa",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: tempohPelucutan,
                        titleName:
                          "Bilangan tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: tempohPelucutanWaktu,
                        titleName:
                          "Tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: pengerusi,
                        titleName: "Pengerusi",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahPengerusi,
                        titleName: "Bilangan Pengerusi",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: timbalan,
                        titleName: "Timbalan Pengerusi",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahTimbalan,
                        titleName: "Bilangan Timbalan Pengerusi",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: naib,
                        titleName: "Naib Pengerusi",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahNaib,
                        titleName: "Bilangan Naib Pengerusi",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: setiaUsaha,
                        titleName: "Setiausaha",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahSetiaUsaha,
                        titleName: "Bilangan Setiausaha",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: penolongSetiaUsaha,
                        titleName: "Penolong Setiausaha",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahPenolongSetiaUsaha,
                        titleName: "Bilangan Penolong Setiausaha",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: bendahari,
                        titleName: "Bendahari",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahBendahari,
                        titleName: "Bilangan Bendahari",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: penolongBendahari,
                        titleName: "Penolong Bendahari",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahPenolongBendahari,
                        titleName: "Bilangan Penolong Bendahari",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: ahliBiasa,
                        titleName: "Ahli Jawatankuasa Biasa",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahAhliBiasa,
                        titleName: "Bilangan Ahli Jawatankuasa Biasa",
                      },
                      ...numberAddAhliBilangan,
                      ...numberAddAhli,
                      ...existingCustomJawatanAhli,
                      ...existingCustomJawatanBilangan,
                    ],
                    clause: "clause4",
                    clauseCount: 4,
                  });
                }}
                disabled={isCreatingContent || isEditingContent || !checked}
              >
                {isCreatingContent || isEditingContent
                  ? t("saving")
                  : t("save")}
              </ButtonPrimary>
            </Grid>
          </Box>
        </Grid>
      </Grid>
      <MessageDialog
        open={showErrorAjk}
        onClose={() => setShowErrorAjk(false)}
        message={t("moreThan7ajk")}
      />
      <DialogConfirmation
        open={dialogSaveOpen}
        // onClose={() => setDialogSaveOpen(false)}
        onConfirmationText={t("AJKNoUpdateConfirmation")}
        onAction={handleConfirm}
        onClose={() => setDialogSaveOpen(false)}
        isMutating={isLoadingUpdate || isEditingContent}
      />
      <MessageDialog
        open={showIsRestrictedWord}
        onClose={() => setShowIsRestrictedWord(false)}
        message={"Gelaran jawatan ini tidak dibenarkan di bawah AJK"}
      />
      {/* delete comfirmation */}
      <DialogConfirmation
        open={showConfirmDeleteDialog}
        onClose={() => {
          setShowConfirmDeleteDialog(false);
          setSelectedDeleteId(null);
          setSelectedMatchId(null);
        }}
        onAction={deleteCustomJawatanFunc}
        isMutating={isDeleteCustomJawatan}
        onConfirmationText={"Adakah anda pasti untuk padam jawatan ini?"}
      />
    </>
  );
};

export default FasalContentEmpatBebas;
