import { <PERSON>, Grid, Icon<PERSON>utton, Typography } from "@mui/material";
import { t } from "i18next";
import { useEffect, useState } from "react";
import { formatDate } from "../../../../helpers/utils";
import { useNavigate } from "react-router-dom";
import {
  ApplicationStatusEnum,
  OrganisationPositions,
  StatusPermohonan,
} from "../../../../helpers/enums";
import ButtonPrevious from "../../../../components/button/ButtonPrevious";
import { ButtonPrimary } from "../../../../components/button";
import { EyeIcon } from "../../../../components/icons";
import DataTable, { IColumn } from "@/components/datatable";
import { useQuery } from "@/helpers";
import Input from "@/components/input/Input";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { CrudFilter } from "@refinedev/core";
import { MaklumatTabProps } from "../maklumatSelectionTabs";
import ForbiddenPage from "@/pages/forbidden";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "30px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

function AhliBukanWarganegaraTab({ disabled }: MaklumatTabProps) {
  if (disabled) {
    return <ForbiddenPage internal />;
  } else {
    const navigate = useNavigate();
    const { control, setValue, watch, getValues, reset, handleSubmit } =
      useForm<FieldValues>({
        defaultValues: {
          pageNo: 1,
          pageSize: 10,
          searchQuery: "",
          applicationStatusCode: "",
        },
      });

    const [displaySenaraiAjk, setDisplaySenaraiAjk] = useState<any[]>([]);
    const [total, setTotal] = useState<number>(0);
    const statusPermohonan = Object.entries(StatusPermohonan)
      .filter(([key, value]) => Number(key) !== 0)
      .map(([key, value]) => ({
        value: key,
        label: t(value),
      }));

    const { refetch: fetchNonCitizenCommittee, isLoading } = useQuery({
      url: `society/admin/branchNonCitizen/findAllByParam`,
      filters: [
        { field: "pageNo", operator: "eq", value: watch("pageNo") },
        { field: "pageSize", operator: "eq", value: watch("pageSize") },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
        {
          field: "applicationStatusCode",
          operator: "eq",
          value: getValues("applicationStatusCode"),
        },
      ],
      autoFetch: false,
      onSuccess: (data) => {
        const list = data?.data?.data;
        setTotal(list.total);
        setDisplaySenaraiAjk(list.data);
      },
    });

    const columns: IColumn[] = [
      {
        field: "name",
        headerName: t("applicationName"),
        align: "center",
        renderCell: (params) => <Box>{params.row.name ?? "-"}</Box>,
      },
      {
        field: "identificationNo",
        headerName: t("idNumberPlaceholder"),
        align: "center",
        renderCell: (params) => <Box>{params.row.identificationNo ?? "-"}</Box>,
      },
      {
        field: "designationCode",
        headerName: t("jawatan"),
        align: "center",
        renderCell: ({ row }: any) => {
          return t(
            `${
              t(
                OrganisationPositions.find(
                  (item) => item?.value === Number(row?.designationCode)
                )?.label ?? "-"
              ) || "-"
            }`
          );
        },
      },
      {
        field: "societyName",
        headerName: t("organizationName"),
        align: "center",
        renderCell: (params) => <Box>{params.row.societyName ?? "-"}</Box>,
      },
      {
        field: "societyNo",
        headerName: t("ppmCawangan"),
        align: "center",
        renderCell: (params) => (
          <Box>
            {params.row.branchNo
              ? params.row.branchNo
              : params.row.branchApplicationNo ?? "-"}
          </Box>
        ),
      },
      {
        field: "applicationStatusCode",
        headerName: t("statusPermohonan"),
        align: "center",
        renderCell: (params) => {
          return (
            <Box>
              {params.row.applicationStatusCode
                ? t(ApplicationStatusEnum[params.row.applicationStatusCode]) ??
                  "-"
                : "-"}
            </Box>
          );
        },
      },
      {
        field: "createdDate",
        headerName: t("tarikhCipta"),
        align: "center",
        renderCell: (params) => (
          <Box>{formatDate(params.row.createdDate) ?? "-"}</Box>
        ),
      },
      {
        field: "actions",
        headerName: "",
        align: "right",
        renderCell: (params: any) => {
          const ajkId = btoa(params.row.id);
          const societyId = btoa(params.row.societyId);

          return (
            <>
              <IconButton
                sx={{ color: "black" }}
                onClick={() =>
                  navigate(`ahli-bukan-warganegara/${societyId}/${ajkId}`, {
                    state: {
                      nonCitizenCommittee: params.row,
                    },
                  })
                }
              >
                <EyeIcon />
              </IconButton>
            </>
          );
        },
      },
    ];

    useEffect(() => {
      const filters: CrudFilter[] = [
        {
          field: "pageSize",
          value: watch("pageSize"),
          operator: "eq",
        },
        {
          field: "pageNo",
          value: watch("pageNo"),
          operator: "eq",
        },
      ];
      fetchNonCitizenCommittee({ filters });
    }, []);

    const handleSearch = () => {
      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: getValues("pageSize") },
        { field: "pageNo", operator: "eq", value: getValues("pageNo") },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
        {
          field: "applicationStatusCode",
          operator: "eq",
          value: getValues("applicationStatusCode"),
        },
      ];
      fetchNonCitizenCommittee({ filters });
    };

    const handleClearSearch = () => {
      reset();
      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: 10 },
        { field: "pageNo", operator: "eq", value: 1 },
        {
          field: "applicationStatusCode",
          operator: "eq",
          value: null,
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: null,
        },
      ];
      fetchNonCitizenCommittee({ filters });
    };

    const handleChangePage = (newPage: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: watch("pageSize"), operator: "eq" },
        { field: "pageNo", value: newPage, operator: "eq" },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
        {
          field: "applicationStatusCode",
          operator: "eq",
          value: getValues("applicationStatusCode"),
        },
      ];
      setValue("pageNo", newPage);
      fetchNonCitizenCommittee({ filters });
    };

    const handleChangePageSize = (newPageSize: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: newPageSize, operator: "eq" },
        { field: "pageNo", value: 1, operator: "eq" },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
        {
          field: "applicationStatusCode",
          operator: "eq",
          value: getValues("applicationStatusCode"),
        },
      ];
      setValue("pageNo", 1);
      setValue("pageSize", newPageSize);
      fetchNonCitizenCommittee({ filters });
    };

    return (
      <>
        <form onSubmit={handleSubmit(handleSearch)}>
          <Box
            sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
          >
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("senaraiPertubuhan")}
              </Typography>
              {/* organization category */}

              <Controller
                name="applicationStatusCode"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="select"
                    label={t("statusPermohonan")}
                    options={statusPermohonan}
                  />
                )}
              />
              {/* finding/carian */}
              <Controller
                name="searchQuery"
                control={control}
                render={({ field }) => <Input {...field} label={t("search")} />}
              />
              <Grid container mt={3} spacing={2}>
                <Grid
                  item
                  xs={12}
                  sx={{
                    mt: 2,
                    display: "flex",
                    // flexDirection: isMobile ? "column" : "row",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <ButtonPrevious onClick={handleClearSearch}>
                    {t("previous")}
                  </ButtonPrevious>
                  <ButtonPrimary type="submit">{t("search")}</ButtonPrimary>
                </Grid>
              </Grid>
            </Box>
          </Box>
          {/* ============= */}
          <Box
            sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
          >
            <Box
              sx={{
                textAlign: "center",
                color: "#fff",
                borderRadius: "13px",
                backgroundColor: "var(--primary-color)",
                py: 2,
              }}
            >
              <Typography variant="h5" gutterBottom>
                {total}
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
                {t("rekodBukanWarganegaraDijumpai")}
              </Typography>
            </Box>

            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mt: 2,
              }}
            >
              <DataTable
                columns={columns as any[]}
                rows={displaySenaraiAjk} // Correctly slice data
                page={watch("pageNo")}
                rowsPerPage={watch("pageSize")}
                totalCount={total}
                isLoading={isLoading}
                onPageChange={handleChangePage}
                onPageSizeChange={handleChangePageSize}
                customNoDataText={t("noRecordForStatusBranch")}
              />
            </Box>
          </Box>
        </form>
      </>
    );
  }
}

export default AhliBukanWarganegaraTab;
