import { Navigate, Outlet, Route } from "react-router-dom";
import { RouteGuard } from "../../components/RouteGuard";
import { registerRoutes } from "../../helpers/routeDetector";
import Layout from "../../pages/pertubuhan/pengurusan-pertubuhan/Layout";
import ListDokumenSokongan from "../../pages/pertubuhan/pengurusan-pertubuhan/dokumen-sokongan";
import ListBayaran from "../../pages/pertubuhan/pengurusan-pertubuhan/bayaran";
import ListSenaraiAjk from "../../pages/pertubuhan/pengurusan-pertubuhan/senarai-ajk";
import CreateAjk from "../../pages/pertubuhan/pengurusan-pertubuhan/senarai-ajk/CreateAjk";
import CreateAjkBukanWn from "../../pages/pertubuhan/pengurusan-pertubuhan/senarai-ajk/CreateAjkBukanWn";
import CreateMam from "../../pages/pertubuhan/pengurusan-pertubuhan/maklumat-am/CreateMam";
import CreateMesyuarat from "../../pages/pertubuhan/pengurusan-pertubuhan/mesyuarat-penubuhan";
import UpdateMam from "../../pages/pertubuhan/pengurusan-pertubuhan/maklumat-am/UpdateMam";
import CreatePerlembagaan from "../../pages/pertubuhan/pengurusan-pertubuhan/perlembagaan";
import SemakPerlembagaan from "../../pages/pertubuhan/pengurusan-pertubuhan/perlembagaan/SemakPerlembagaan";
import Fasal from "../../pages/pertubuhan/pengurusan-pertubuhan/perlembagaan/Fasal";
import Kaunter from "../../pages/pertubuhan/pengurusan-pertubuhan/bayaran/Kaunter";
import Online from "../../pages/pertubuhan/pengurusan-pertubuhan/bayaran/Online";
import Term from "../../pages/pertubuhan/pengurusan-pertubuhan/bayaran/Term";
import Butiran from "../../pages/pertubuhan/pengurusan-pertubuhan/bayaran/Butiran";
import FasalContentAdd from "@/pages/pertubuhan/pengurusan-pertubuhan/perlembagaan/addFasal";

// Layout component to wrap all pengurusan pertubuhan routes with protection
const PengurusanPertubuhanLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Layout>
      <Outlet />
    </Layout>
  </RouteGuard>
);

// Register routes with their portal types
registerRoutes({
  // Add your route registrations here
});

export const pengurusanPertubuhan = {
  resources: [
    {
      name: "society/register",
      list: "/society/register",
    },
    {
      name: "society/committee/getAll",
      list: "/society/committee/getAll",
    },
    {
      name: "society/nonCitizenCommittee/getAll",
      list: "/society/nonCitizenCommittee/getAll",
    },
    {
      name: "society/organization/register",
      list: "/society/organization/register",
    },
    {
      name: "society/admin/constitutionTypeWithClauseContent/list",
      list: "/society/admin/constitutionTypeWithClauseContent/list",
    },
    {
      name: "society/constitution/clausevalue/create",
      list: "/society/constitution/clausevalue/create",
    },
    {
      name: "society/constitution/clausecontent/create",
      list: "/society/constitution/clausecontent/create",
    },
    {
      name: "user/auth/validateId" ,
      list: "/user/auth/validateId",
    },
  ],
  routes: (
    <Route path="pengurusan-pertubuhan" element={<PengurusanPertubuhanLayout />}>
      <Route index element={<Navigate to="pendaftaran/maklumat-am" />} />

      <Route path="pendaftaran">
        <Route path="maklumat-am">
          <Route index element={<CreateMam />} />
        </Route>

        <Route path="mesyuarat-penubuhan">
          <Route index element={<CreateMesyuarat />} />
        </Route>

        <Route path="perlembagaan">
          <Route index element={<CreatePerlembagaan />} />
        </Route>

        <Route path="add/:id">
          <Route index element={ <FasalContentAdd/>} />
        </Route>

        <Route path="fasal/:id">
          <Route index element={<Fasal />} />
        </Route>

        <Route path="semak-perlembagaan">
          <Route index element={<SemakPerlembagaan />} />
        </Route>

        <Route path="senarai-ajk">
          <Route index element={<ListSenaraiAjk />} />
        </Route>

        <Route path="create-ajk">
          <Route index element={<CreateAjk />} />
        </Route>

        <Route path="create-ajk-bukanwn">
          <Route index element={<CreateAjkBukanWn />} />
        </Route>

        <Route path="dokumen-sokongan">
          <Route index element={<ListDokumenSokongan />} />
        </Route>

        <Route path="bayaran">
          <Route index element={<ListBayaran />} />

          <Route path="kaunter" element={<Kaunter />} />

          <Route path="online">
            <Route index element={<Online />} />

            <Route path="term" element={<Term />} />

            <Route path="butiran" element={<Butiran />} />
          </Route>
        </Route>
      </Route>
    </Route>
  ),
};
