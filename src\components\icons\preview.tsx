import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  sx?: React.CSSProperties;
}

export const PreviewIcon: React.FC<IconProps> = React.forwardRef<
  SVGSVGElement,
  IconProps
>(({ sx, color = "inherit", ...props }, ref) => {
  return (
    <svg
      ref={ref}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ color, ...sx }}
      {...props}
    >
      <path
        d="M3.33333 14C2.96667 14 2.65289 13.8696 2.392 13.6087C2.13111 13.3478 2.00044 13.0338 2 12.6667V3.33333C2 2.96667 2.13067 2.65289 2.392 2.392C2.65333 2.13111 2.96711 2.00044 3.33333 2H12.6667C13.0333 2 13.3473 2.13067 13.6087 2.392C13.87 2.65333 14.0004 2.96711 14 3.33333V12.6667C14 13.0333 13.8696 13.3473 13.6087 13.6087C13.3478 13.87 13.0338 14.0004 12.6667 14H3.33333ZM3.33333 12.6667H12.6667V4.66667H3.33333V12.6667ZM8 11.3333C7.08889 11.3333 6.27511 11.0862 5.55867 10.592C4.84222 10.0978 4.32267 9.456 4 8.66667C4.32222 7.87778 4.84178 7.23622 5.55867 6.742C6.27556 6.24778 7.08933 6.00044 8 6C8.91067 5.99956 9.72467 6.24689 10.442 6.742C11.1593 7.23711 11.6787 7.87867 12 8.66667C11.6778 9.45555 11.1584 10.0973 10.442 10.592C9.72556 11.0867 8.91156 11.3338 8 11.3333ZM8 10.3333C8.62222 10.3333 9.18889 10.186 9.7 9.89133C10.2111 9.59667 10.6111 9.18844 10.9 8.66667C10.6111 8.14444 10.2111 7.736 9.7 7.44133C9.18889 7.14667 8.62222 6.99956 8 7C7.37778 7.00044 6.81111 7.14778 6.3 7.442C5.78889 7.73622 5.38889 8.14444 5.1 8.66667C5.38889 9.18889 5.78889 9.59733 6.3 9.892C6.81111 10.1867 7.37778 10.3338 8 10.3333ZM8 9.66667C8.27778 9.66667 8.514 9.56955 8.70867 9.37533C8.90333 9.18111 9.00044 8.94489 9 8.66667C8.99956 8.38844 8.90244 8.15244 8.70867 7.95867C8.51489 7.76489 8.27867 7.66756 8 7.66667C7.72133 7.66578 7.48533 7.76311 7.292 7.95867C7.09867 8.15422 7.00133 8.39022 7 8.66667C6.99867 8.94311 7.096 9.17933 7.292 9.37533C7.488 9.57133 7.724 9.66844 8 9.66667Z"
        fill="currentColor"
      />
    </svg>
  );
});
