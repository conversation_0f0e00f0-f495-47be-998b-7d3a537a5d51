import { <PERSON>, Grid, SelectChangeEvent, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import Input from "@/components/input/Input";
import { useTranslation } from "react-i18next";
import { AddressList, Meeting } from "../interface";
import useQuery from "@/helpers/hooks/useQuery";
import { MALAYSIA, MeetingMethods } from "@/helpers/enums";
import { useSelector } from "react-redux";
import { isOptionValueNotIncluded } from "@/helpers";
import AWSLocationMap, {
  AWSLocationSearchMap,
} from "@/components/geocoder/geocoder";
type props = {
  sectionStyle: any;
  meeting: Meeting | undefined;
};
const AlamatTempatMesyuarat: React.FC<props> = ({ sectionStyle, meeting }) => {
  const { t } = useTranslation();
  const [organizationCoords, setOrganizationCoords] = useState<
    [number, number]
  >([2.745564, 101.707021]);
  const [addressList, setAddressList] = useState<AddressList[]>([]);
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  useQuery({
    url: `society/admin/address/list`,
    onSuccess: (data) => {
      const list = data?.data?.data || [];
      setAddressList(list);
    },
  });

  const currentMeetingState = meeting ? Number(meeting?.state) : 0;
  const currentMeetingDistrict = meeting ? Number(meeting?.district) : 0;

  const addressDataArray = addressList
    .filter((item: any) => item.pid === MALAYSIA)
    .map((item: any) => ({
      label: item.name,
      value: item.id,
    }));

  const addressDataArrayDaerah = addressList
    .filter((items: any) => items.id === Number(meeting?.district))
    .map((item: any) => ({ value: item.id, label: item.name }));

  if (meeting?.meetingMethod == MeetingMethods.ATAS_TALIAN) {
    return;
  }

  useEffect(() => {
    if (meeting) {
      const coordinateArray = meeting?.GISInformation?.split(", ").map(
        (coord) => parseFloat(coord)
      );
      setOrganizationCoords([coordinateArray?.[0], coordinateArray?.[1]]);
    }
  }, [meeting]);

  return (
    <Box
      sx={{
        background: "white",
        border: "1px solid rgba(0, 0, 0, 0.12)",
        borderRadius: "14px",
        p: 3,
        mb: 2,
      }}
    >
      <Typography variant="subtitle1" sx={sectionStyle}>
        {t("alamatTempatMesyuarat")}
      </Typography>
      <Grid item xs={12}>
        {/** @ts-expect-error */}
        {meeting?.meetingMethod == MeetingMethods.ATAS_TALIAN ? null : (
          <>
            <Input
              label={t("namaTempatMesyuarat")}
              value={meeting?.meetingPlace ? meeting?.meetingPlace : "-"}
              disabled
            />
            <Grid container spacing={2} alignItems="flex-start" sx={{ mb: 1 }}>
              <Grid item xs={12} sm={4}>
                <Typography
                  variant="body1"
                  sx={{
                    color: "#666666",
                    fontWeight: "400 !important",
                    fontSize: "14px",
                  }}
                >
                  {t("meetingLocation")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <div
                  style={{
                    height: "150px",
                    width: "100%",
                    borderRadius: "8px",
                  }}
                >
                  <AWSLocationMap
                    longitude={organizationCoords[1]}
                    latitude={organizationCoords[0]}
                    // onLocationSelected={handleLocationSelected}
                  />
                </div>
              </Grid>
            </Grid>
          </>
        )}
        <Input
          label={t("alamatTempatMesyuarat")}
          disabled
          value={meeting?.meetingAddress ? meeting?.meetingAddress : "-"}
        />
        <Input
          label={t("negeri")}
          type="select"
          value={meeting ? parseInt(meeting?.state) : 0}
          disabled
          options={
            isOptionValueNotIncluded(currentMeetingState, addressDataArray)
              ? []
              : addressDataArray
          }
        />
        <Input
          label={t("daerah")}
          type="select"
          value={meeting ? parseInt(meeting?.district) : 0}
          disabled
          options={
            isOptionValueNotIncluded(
              currentMeetingDistrict,
              addressDataArrayDaerah
            )
              ? []
              : addressDataArrayDaerah
          }
        />
        <Input
          label={t("bandar")}
          disabled
          value={meeting?.city ? meeting?.city : "-"}
        />
        <Input
          label={t("poskod")}
          disabled
          value={meeting?.postcode ? meeting?.postcode : "-"}
        />
      </Grid>
    </Box>
  );
};

export default AlamatTempatMesyuarat;
