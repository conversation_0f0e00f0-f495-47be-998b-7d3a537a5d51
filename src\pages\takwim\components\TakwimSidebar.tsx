import React, { useState, useMemo } from "react";
import { Box, Typography, Button, Grid } from "@mui/material";
import { useTranslation } from "react-i18next";
import { Link, useNavigate } from "react-router-dom";
import { Calendar } from "../../../components/calendar/Main";
import { useTakwim } from "../../../contexts/takwimProvider";
import dayjs from "dayjs";
import { TakwimPaper } from "@/components/paper";
import { IEvent } from "@/types/event";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import { formatTimeWithPeriod } from "@/helpers/timeUtils";
import AddIcon from "@mui/icons-material/Add";
import AuthHelper from "@/helpers/authHelper";
import { PermissionNames, pageAccessEnum } from "@/helpers";

interface TakwimSidebarProps {
  events?: IEvent[];
}

interface GroupedEvents {
  [key: string]: IEvent[];
}

export const TakwimSidebar: React.FC<TakwimSidebarProps> = ({
  events = [],
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [selectedDay, setSelectedDay] = useState(dayjs());
  const { selectedMonthYear, setSelectedMonthYear } = useTakwim();
  const navigate = useNavigate();

  const hasCreateEventPermission = checkPermissionAndUserGroup(
    pageAccessEnum.Create
  );
  const hasEditEventPermission = checkPermissionAndUserGroup(
    pageAccessEnum.Update
  );
  function checkPermissionAndUserGroup(accessType: number) {
    const hasPermission: boolean = AuthHelper.hasPageAccess(
      PermissionNames.TAKWIM?.label || "TAKWIM-AKT",
      accessType
    );

    // 2 is internal user
    if (localStorage.getItem("portal") === "2") {
      return hasPermission && true;
    } else {
      return hasPermission && false;
    }
  }

  const hasEditOrCreateEventPermission =
    hasCreateEventPermission || hasEditEventPermission;

  // Group events by date and get only future events
  const groupedUpcomingEvents = useMemo(() => {
    const today = dayjs().startOf("day");

    // Filter future events and sort them
    const futureEvents = events
      .filter((event) => {
        const eventDate = dayjs(event.eventStartDate);
        return (
          eventDate.isSameOrAfter(today, "day") &&
          eventDate.isSame(selectedMonthYear, "month") &&
          eventDate.isSame(selectedMonthYear, "year")
        );
      })
      .sort((a, b) => dayjs(a.eventStartDate).diff(dayjs(b.eventStartDate)))
      .slice(0, 3); // Limit to first 3 events

    // Group by date
    const grouped = futureEvents.reduce((acc: GroupedEvents, event) => {
      const dateKey = dayjs(event.eventStartDate).format("YYYY-MM-DD");
      if (!acc[dateKey]) {
        acc[dateKey] = [];
      }
      acc[dateKey].push(event);
      return acc;
    }, {});

    return grouped;
  }, [events, selectedMonthYear]);

  const formatDateHeader = (dateStr: string) => {
    const date = dayjs(dateStr);
    const today = dayjs().startOf("day");
    const endOfWeek = dayjs().endOf("week");

    if (date.isSame(today, "day")) {
      return "Hari ini";
    } else if (date.isAfter(endOfWeek)) {
      return date.locale("ms").format("dddd, DD MMMM YYYY");
    }

    return date.locale("en").format("dddd"); // Returns day name in Malay
  };

  const handleMonthChange = (date: dayjs.Dayjs) => {
    if (date && date.isValid()) {
      setSelectedMonthYear(date);
    }
  };

  return (
    <TakwimPaper>
      {hasCreateEventPermission && (
        <Box sx={{ mb: 2 }}>
          <Button
            variant="contained"
            fullWidth
            sx={{
              textTransform: "none",
              color: "#fff",
            }}
            onClick={() => navigate("/takwim/create-event")}
          >
            <AddIcon sx={{ mr: 1 }} />
            {t("CreateEvent")}
          </Button>
        </Box>
      )}
      <Calendar
        value={selectedDay}
        onChange={(newValue) => {
          if (newValue) {
            // setSelectedDay(newValue);
            handleMonthChange(newValue);
          }
        }}
        onMonthChange={(newMonth) => {
          if (newMonth) {
            setSelectedMonthYear(dayjs(newMonth));
          }
        }}
        sx={{
          margin: "0",
          border: "1px solid #dadada99",
          width: "100%",
          borderRadius: "0.5rem",
          marginBottom: "1.5rem",
        }}
        views={["day"]}
        showDaysOutsideCurrentMonth
      />

      <Typography
        sx={{
          color: "#0CA6A6",
          marginBottom: "1rem",
          fontWeight: 500,
        }}
      >
        {t("takwim_sidebar_nextEvent")}
      </Typography>

      {/* Event List */}
      {Object.entries(groupedUpcomingEvents).map(([dateKey, dateEvents]) => {
        // Only show events for the selected month and year
        const eventDate = dayjs(dateKey);
        const isSelectedMonth = eventDate.isSame(selectedMonthYear, "month");
        const isSelectedYear = eventDate.isSame(selectedMonthYear, "year");

        if (!isSelectedMonth || !isSelectedYear) return null;

        return (
          <Box key={dateKey} sx={{ mb: 3 }}>
            <Typography
              sx={{
                borderRadius: "0.5rem",
              }}
            >
              {t(formatDateHeader(dateKey)?.toLocaleLowerCase())}
            </Typography>

            {dateEvents.map((event) => (
              <Link
                to={`activity/${event.eventNo}`}
                key={event.id}
                style={{ textDecoration: "none", color: "inherit" }}
              >
                <Box
                  key={event.id}
                  sx={{
                    mb: 2,
                    p: 2,
                    border: "1px solid #dadada99",
                    backgroundColor: "#fff",
                    borderRadius: "1rem",
                    boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.05)",
                  }}
                >
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Typography
                        sx={{ fontSize: "0.9rem", color: "text.secondary" }}
                      >
                        {formatTimeWithPeriod(event.startTime)}
                      </Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Box
                        sx={{
                          display: "-webkit-box",
                          WebkitBoxOrient: "vertical",
                          WebkitLineClamp: 4,
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          maxHeight: "4.8em", // Adjust as needed for your font size/line height
                        }}
                      >
                        <Typography
                          sx={{ fontWeight: 500, mb: 0.5, lineHeight: 1.2 }}
                        >
                          {event.eventName}
                        </Typography>
                        <Typography
                          sx={{
                            color: "text.secondary",
                            fontSize: "0.85rem",
                            lineHeight: 1.2,
                            display: "flex",
                            alignItems: "center",
                          }}
                        >
                          <LocationOnIcon
                            sx={{
                              fontSize: "0.85rem",
                              color: "text.secondary",
                              marginRight: 0.5,
                            }}
                          />
                          {event.venue}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </Link>
            ))}
          </Box>
        );
      })}

      <Link to={`activity`} style={{ textDecoration: "none" }}>
        <Button
          variant="outlined"
          size="large"
          fullWidth
          sx={{
            marginTop: "0.75rem",
            textTransform: "capitalize",
            borderColor: "#97979733",
            borderRadius: "10px",
            color: "#0CA6A6",
            "&:hover": {
              borderColor: "#28a7a7",
              backgroundColor: "rgba(65, 195, 195, 0.04)",
            },
          }}
        >
          {t("takwim_sidebar_viewFull")}
        </Button>
      </Link>
    </TakwimPaper>
  );
};
