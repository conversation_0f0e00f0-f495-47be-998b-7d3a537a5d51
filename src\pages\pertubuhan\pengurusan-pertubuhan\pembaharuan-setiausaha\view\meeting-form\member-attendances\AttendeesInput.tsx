import { memo } from "react";
import { useFormContext } from "react-hook-form";

import { TextFieldController } from "@/components";

export interface AttendeesInputProps {
  /**
   * @default false
   */
  disabledState?: boolean
}

const AttendeesInput = memo(
  ({ disabledState = false }: AttendeesInputProps) => {
    const { control } = useFormContext();

    return (
      <TextFieldController
        name="totalAttendees"
        control={control}
        isNumber={true}
        disabled={disabledState}
        fullWidth
      />
    )
  }
);

export default AttendeesInput;
