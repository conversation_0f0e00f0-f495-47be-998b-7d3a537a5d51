import React, { useState } from "react";
import {
  Box,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  Button,
  Paper,
  Grid,
  IconButton,
  Menu,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Divider,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { useNavigate } from "react-router-dom";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DeleteIcon from "@/assets/svg/icon-trash.svg?react";
import { Add } from "@mui/icons-material";
import ButtonPrimary from "../../../../components/button/ButtonPrimary";
import { HideOrDisplayInherit } from "../../../../helpers/enums";

export const CreateAjk: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openMenuId, setOpenMenuId] = useState<string | number | null>(null);
  const [SemakanKhalayakAjk, setSemakanKhalayakAjk] = useState([
    {
      id: 1,
      perkara: t("bankruptcy"),
      namaDokumen: "Rashid_bankrupsi_pengesahan",
    },
    {
      id: 2,
      perkara: "PDRM",
      namaDokumen: "Rashid_rekod_pdrm",
    },
    {
      id: 3,
      perkara: "PDRM",
      namaDokumen: "Rashid_rekod_pdrm",
    },
  ]);

  const handleMenuOpen = (
    event: React.MouseEvent<HTMLElement>,
    id: string | number
  ) => {
    setAnchorEl(event.currentTarget);
    setOpenMenuId(id);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setOpenMenuId(null);
  };

  const handleViewDocument = () => {
    // Implement view document logic
    handleMenuClose();
  };

  const handleDeleteDocument = () => {
    // Implement delete document logic
    handleMenuClose();
  };

  const handleBack = () => {
    navigate("/pertubuhan/pengurusan-individu/maklumat-ajk");
  };

  const renderFormField = (
    label: string,
    component: React.ReactNode,
    required = false
  ) => (
    <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
      <Grid item xs={12} sm={3}>
        <Typography variant="body1" sx={{ color: "black" }}>
          {label}
          {required && <span style={{ color: "red" }}>*</span>}
        </Typography>
      </Grid>
      <Grid item xs={12} sm={9}>
        {component}
      </Grid>
    </Grid>
  );

  const CustomSelect = (props: any) => (
    <Select
      {...props}
      IconComponent={(iconProps) => (
        <KeyboardArrowDownIcon {...iconProps} style={{ color: "black" }} />
      )}
    />
  );

  return (
    <Box sx={{ p: { xs: 2, sm: 3 }, mt: 3, ml: 7, mr: 7 }}>
      <Box
        sx={{
          backgroundColor: "#e0f2f1",
          px: 2,
          py: 1,
          mb: 3,
          borderRadius: 2.5,
        }}
      >
        <Typography
          variant="h6"
          component="h2"
          sx={{ fontWeight: "bold", color: "black", fontSize: 14 }}
        >
          {t("personalInfo")}
        </Typography>
      </Box>
      <Paper
        sx={{ p: 3, mb: 3, backgroundColor: "white", borderRadius: "16px" }}
      >
        {renderFormField(
          t("position"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />
        )}
        {renderFormField(
          t("title"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("fullName"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("gender"),
          <FormControl fullWidth variant="outlined">
            <CustomSelect
              displayEmpty
              sx={{
                backgroundColor: "#e0e0e0",
                color: "black",
                borderRadius: "12px",
                "& .MuiTextField-notchedOutline": {
                  borderRadius: "12px",
                },
                "& .MuiInputBase-input": { color: "black" },
              }}
              defaultValue=""
              placeholder={t("selectPlaceholder")}
            >
              <MenuItem value="" disabled>
                <em>{t("selectPlaceholder")}</em>
              </MenuItem>
              <MenuItem value="L">{t("male")}</MenuItem>
              <MenuItem value="P">{t("female")}</MenuItem>
            </CustomSelect>
          </FormControl>,
          true
        )}
        {renderFormField(
          t("citizenship"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("idType"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("idNumber"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("dateOfBirth"),
          <TextField
            fullWidth
            variant="outlined"
            type="date"
            InputLabelProps={{
              shrink: true,
              sx: { color: "black" },
            }}
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& .MuiInputBase-input": { color: "black" },
              "& .MuiSvgIcon-root": { color: "black" },
              "& fieldset": { borderRadius: "12px" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("placeOfBirth"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("occupation"),
          <FormControl fullWidth variant="outlined">
            <CustomSelect
              displayEmpty
              sx={{
                backgroundColor: "#e0e0e0",
                color: "black",
                borderRadius: "12px",
                "& .MuiTextField-notchedOutline": {
                  borderRadius: "12px",
                },
                "& .MuiInputBase-input": { color: "black" },
              }}
              defaultValue=""
              placeholder={t("selectPlaceholder")}
            >
              <MenuItem value="" disabled>
                <em>{t("selectPlaceholder")}</em>
              </MenuItem>
              <MenuItem value="Swasta">{t("private")}</MenuItem>
              <MenuItem value="Kerajaan">{t("government")}</MenuItem>
              <MenuItem value="Bekerja Sendiri">{t("selfEmployed")}</MenuItem>
            </CustomSelect>
          </FormControl>,
          true
        )}
        {renderFormField(
          t("residentialAddress"),
          <TextField
            fullWidth
            variant="outlined"
            multiline
            rows={3}
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("state"),
          <FormControl fullWidth variant="outlined">
            <CustomSelect
              displayEmpty
              sx={{
                backgroundColor: "#e0e0e0",
                color: "black",
                borderRadius: "12px",
                "& .MuiTextField-notchedOutline": {
                  borderRadius: "12px",
                },
                "& .MuiInputBase-input": { color: "black" },
              }}
              defaultValue=""
              placeholder={t("selectPlaceholder")}
            >
              <MenuItem value="" disabled>
                <em>{t("selectPlaceholder")}</em>
              </MenuItem>
              <MenuItem value="Selangor">Selangor</MenuItem>
              <MenuItem value="Kuala Lumpur">Kuala Lumpur</MenuItem>
              <MenuItem value="Johor">Johor</MenuItem>
            </CustomSelect>
          </FormControl>,
          true
        )}
        {renderFormField(
          t("district"),
          <FormControl fullWidth variant="outlined">
            <CustomSelect
              displayEmpty
              sx={{
                backgroundColor: "#e0e0e0",
                color: "black",
                borderRadius: "12px",
                "& .MuiTextField-notchedOutline": {
                  borderRadius: "12px",
                },
                "& .MuiInputBase-input": { color: "black" },
              }}
              defaultValue=""
              placeholder={t("selectPlaceholder")}
            >
              <MenuItem value="" disabled>
                <em>{t("selectPlaceholder")}</em>
              </MenuItem>
              <MenuItem value="Petaling">Petaling</MenuItem>
              <MenuItem value="Klang">Klang</MenuItem>
              <MenuItem value="Hulu Langat">Hulu Langat</MenuItem>
            </CustomSelect>
          </FormControl>,
          true
        )}
        {renderFormField(
          t("city"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />
        )}
        {renderFormField(
          t("postcode"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("email"),
          <TextField
            fullWidth
            variant="outlined"
            type="email"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("phoneNumber"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />,
          true
        )}
        {renderFormField(
          t("homeNumber"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />
        )}
        {renderFormField(
          t("officeNumber"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />
        )}
      </Paper>

      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          backgroundColor: "#e0f2f1",
          px: 2,
          py: 1,
          mb: 3,
          borderRadius: 2.5,
        }}
      >
        <Typography
          variant="h6"
          component="h2"
          sx={{ fontWeight: "bold", color: "black", fontSize: 14 }}
        >
          {t("employerInfo")}
        </Typography>
        <Typography variant="body2" sx={{ color: "red", fontWeight: "bold" }}>
          {t("jawatanKuasaNotes")}
        </Typography>
      </Box>
      <Paper
        sx={{ p: 3, mb: 3, backgroundColor: "white", borderRadius: "16px" }}
      >
        {renderFormField(
          t("employerName"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />
        )}
        {renderFormField(
          t("employerAddress"),
          <TextField
            fullWidth
            variant="outlined"
            multiline
            rows={3}
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />
        )}
        {renderFormField(
          t("country"),
          <FormControl fullWidth variant="outlined">
            <CustomSelect
              displayEmpty
              sx={{
                backgroundColor: "#e0e0e0",
                color: "black",
                borderRadius: "12px",
                "& .MuiTextField-notchedOutline": {
                  borderRadius: "12px",
                },
                "& .MuiInputBase-input": { color: "black" },
              }}
              defaultValue=""
              placeholder={t("selectPlaceholder")}
            >
              <MenuItem value="" disabled>
                <em>{t("selectPlaceholder")}</em>
              </MenuItem>
              <MenuItem value="Malaysia">Malaysia</MenuItem>
              <MenuItem value="Singapura">Singapura</MenuItem>
              <MenuItem value="Indonesia">Indonesia</MenuItem>
            </CustomSelect>
          </FormControl>
        )}
        {renderFormField(
          t("state"),
          <FormControl fullWidth variant="outlined">
            <CustomSelect
              displayEmpty
              sx={{
                backgroundColor: "#e0e0e0",
                color: "black",
                borderRadius: "12px",
                "& .MuiTextField-notchedOutline": {
                  borderRadius: "12px",
                },
                "& .MuiInputBase-input": { color: "black" },
              }}
              defaultValue=""
              placeholder={t("selectPlaceholder")}
            >
              <MenuItem value="" disabled>
                <em>{t("selectPlaceholder")}</em>
              </MenuItem>
              <MenuItem value="Selangor">Selangor</MenuItem>
              <MenuItem value="Kuala Lumpur">Kuala Lumpur</MenuItem>
              <MenuItem value="Johor">Johor</MenuItem>
            </CustomSelect>
          </FormControl>
        )}
        {renderFormField(
          t("district"),
          <FormControl fullWidth variant="outlined">
            <CustomSelect
              displayEmpty
              sx={{
                backgroundColor: "#e0e0e0",
                color: "black",
                borderRadius: "12px",
                "& .MuiTextField-notchedOutline": {
                  borderRadius: "12px",
                },
                "& .MuiInputBase-input": { color: "black" },
              }}
              defaultValue=""
              placeholder={t("selectPlaceholder")}
            >
              <MenuItem value="" disabled>
                <em>{t("selectPlaceholder")}</em>
              </MenuItem>
              <MenuItem value="Petaling">Petaling</MenuItem>
              <MenuItem value="Klang">Klang</MenuItem>
              <MenuItem value="Hulu Langat">Hulu Langat</MenuItem>
            </CustomSelect>
          </FormControl>
        )}
        {renderFormField(
          t("city"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />
        )}
        {renderFormField(
          t("postcode"),
          <TextField
            fullWidth
            variant="outlined"
            sx={{
              backgroundColor: "#e0e0e0",
              borderRadius: "12px",
              "& fieldset": { borderRadius: "12px" },
              "& .MuiInputBase-input": { color: "black" },
            }}
          />
        )}
      </Paper>

      <Box sx={{ display: "flex", flexDirection: "column", mb: 2, gap: 2 }}>
        <Box
          sx={{
            backgroundColor: "#e0f2f1",
            px: 2,
            py: 1,
            borderRadius: 2.5,
          }}
        >
          <Typography
            variant="h6"
            component="h2"
            sx={{ fontWeight: "bold", fontSize: 14 }}
          >
            {t("ajkEligibilityCheck")}
          </Typography>
        </Box>
        {/* <ButtonPrimary
          startIcon={<Add sx={{ color: "white" }} />}
          sx={{ alignSelf: "flex-end", px: 2, py: 1 }}
          // onClick={handleDaftarAJK}
        >
          {t("addDocument")}
        </ButtonPrimary> */}
      </Box>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: "none",
          border: "1px solid #e0e0e0",
          backgroundColor: "white",
          borderRadius: 2.5 * 1.5,
          p: 1,
          mb: 3,
        }}
      >
        <Table>
          <TableHead>
            <TableRow>
              <TableCell
                sx={{
                  fontWeight: "bold",
                  borderBottom: "1px solid #e0e0e0",
                  color: "black",
                  p: 1,
                }}
              >
                {t("matter")}
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: "bold",
                  borderBottom: "1px solid #e0e0e0",
                  color: "black",
                  p: 1,
                }}
              >
                {t("documentName")}
              </TableCell>
              <TableCell
                align="right"
                sx={{
                  fontWeight: "bold",
                  borderBottom: "1px solid #e0e0e0",
                  color: "black",
                  p: 1,
                }}
              >
                {t("activity")}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {SemakanKhalayakAjk.map((row, index) => (
              <TableRow key={row.id}>
                <TableCell
                  sx={{
                    color: "black",
                    borderBottom: "1px solid #e0e0e0",
                    p: 1,
                  }}
                >
                  {row.perkara}
                </TableCell>
                <TableCell
                  sx={{
                    color: "black",
                    borderBottom: "1px solid #e0e0e0",
                    p: 1,
                  }}
                >
                  {row.namaDokumen}
                </TableCell>
                <TableCell
                  align="right"
                  sx={{ borderBottom: "1px solid #e0e0e0", p: 1 }}
                >
                  <IconButton
                    onClick={(event) => handleMenuOpen(event, row.id)}
                  >
                    <MoreVertIcon sx={{ color: "black" }} />
                  </IconButton>
                  <Menu
                    anchorEl={anchorEl}
                    open={openMenuId === row.id}
                    onClose={handleMenuClose}
                    slotProps={{
                      paper: {
                        elevation: 0,
                        sx: {
                          backgroundColor: "white",
                          color: "black",
                          boxShadow: "0 4px 4px rgba(0, 0, 0, 0.25)",
                          "& .MuiMenuItem-root": {
                            color: "black",
                            "& .MuiSvgIcon-root": {
                              color: "black",
                            },
                          },
                          "& .MuiDivider-root": {
                            my: 0.5,
                          },
                        },
                      },
                    }}
                    transformOrigin={{ horizontal: "right", vertical: "top" }}
                    anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
                  >
                    <MenuItem onClick={handleViewDocument}>
                      <VisibilityIcon sx={{ mr: 2 }} />
                      {t("viewDocument")}
                    </MenuItem>
                    <Divider
                      variant="middle"
                      component="li"
                      sx={{ borderColor: "#e0e0e0" }}
                    />
                    <MenuItem
                      onClick={handleDeleteDocument}
                      sx={{ display: HideOrDisplayInherit }}
                    >
                      <DeleteIcon style={{ marginRight: "0.5rem" }} />
                      {t("deleteDocument")}
                    </MenuItem>
                  </Menu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 2 }}>
        <Button
          variant="outlined"
          sx={{ borderColor: "#4db6ac", color: "#4db6ac" }}
          onClick={handleBack}
        >
          {t("semula")}
        </Button>
        <Button
          variant="contained"
          sx={{
            backgroundColor: "#4db6ac",
            "&:hover": { backgroundColor: "#009688" },
          }}
        >
          {t("update")}
        </Button>
      </Box>
    </Box>
  );
};

export default CreateAjk;
