import React, { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  DecisionOptionsCode,
  NEW_PermissionNames,
  pageAccessEnum,
} from "../../../helpers/enums";

import {
  Box,
  Grid,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Typography,
  useMediaQuery,
  Theme,
} from "@mui/material";
import MaklumatSetiausahaSection from "./views/MaklumatSetiausahaSection";
import KeputusanPermohonanSection from "./views/KeputusanPermohonanSection";
import AccordionComp from "../View/Accordion";
// import DialogConfirmation from "./views/DialogConfirmation";
import { ButtonOutline, ButtonPrimary } from "../../../components/button";

import { AddIcon } from "../../../components/icons";
import { useForm, FieldValues } from "react-hook-form";
import { API_URL } from "@/api";
import { useCustom, useCustomMutation } from "@refinedev/core";
import {
  DialogConfirmation,
  SelectFieldController,
  TextFieldController,
} from "@/components";
import AuthHelper from "@/helpers/authHelper";

const labelStyle = {
  fontSize: "16px",
  color: "#666666",
  fontWeight: "400 !important",
};

const subTitleStyle = {
  color: "#0CA6A6",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

type ReadStatusType = {
  [key: number]: boolean;
};

function KeputusanIndukPembaharuanSetiausahaMigrasi() {
  const hasUpdatePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_INDUK.children.PEMBAHARUAN_SETIAUSAHA_MIGRASI.label,
    pageAccessEnum.Update
  );
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language;
  const decisionOptions = DecisionOptionsCode(t);
  const { id } = useParams();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const [readStatus, setReadStatus] = useState<ReadStatusType>({});

  const sectionItems = [
    {
      subTitle: t("secretaryInformation"),
      component: <MaklumatSetiausahaSection />,
    },
    {
      subTitle: t("keputusanPermohonan"),
      component: <KeputusanPermohonanSection />,
    },
  ];

  const handleChangeCurrentExpandSection =
    (item: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);

      if (isExpanded) {
        setReadStatus((prevState) => {
          const updatedStatus = sectionItems.reduce((acc, _, i) => {
            if (i + 1 <= item) {
              acc[i + 1] = true;
            } else {
              acc[i + 1] = !!prevState[i + 1] || false;
            }
            return acc;
          }, {} as Record<number, boolean>);
          return updatedStatus;
        });
      }
    };

  const handleDialogClose = () => setIsDialogOpen(false);
  const handleFormSubmit = () => {};

  const methodsRoAction = useForm();

  const {
    handleSubmit: handleSubmitRoAction,
    control: controlRoAction,
    setValue: setValueRoAction,
    getValues: getValuesRoAction,
    watch: watchRoAction,
  } = methodsRoAction;

  const { data: roListData, isLoading: isRoListLoading } = useCustom({
    url: `${API_URL}/society/user/getRoList`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: id,
      },
    },
  });

  const roList = roListData?.data?.data ?? [];

  const roListOptions =
    roList.map((item: any) => ({
      value: item.id,
      label: item.name,
    })) || [];

  const { mutate: updateRo, isLoading } = useCustomMutation();

  const onSubmitRoAction = (data: FieldValues) => {
    console.log(data);
    // updateRo(
    //   {
    //     url: `${API_URL}/society/roDecision/updateRo`,
    //     method: "patch",
    //     values: {
    //       roId: data.ro,
    //       noteRo: data.noteRo,
    //       amendmentId: data.amendmentId,
    //       roApprovalType: ROApprovalType.SOCIETY_AMENDMENT.code,
    //     },
    //     config: {
    //       headers: {
    //         portal: localStorage.getItem("portal"),
    //         authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
    //       },
    //     },
    //     successNotification: (data) => {
    //       return {
    //         message: data?.data?.msg,
    //         type: "success",
    //       };
    //     },
    //     errorNotification: (data) => {
    //       return {
    //         message: data?.response?.data?.msg,
    //         type: "error",
    //       };
    //     },
    //   },
    //   {
    //     onError(error, variables, context) {
    //       console.log(error);
    //     },
    //   }
    // );
  };
  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            {currentLanguage === "my"
              ? "Reformis Muda Perak"
              : " Young Reformist Perak"}
          </Box>
        </Box>

        <Box sx={{ mt: 4 }}>
          {sectionItems.map((item, index) => {
            return (
              <AccordionComp
                key={index}
                subTitle={item.subTitle}
                currentIndex={index + 1}
                currentExpand={currentExpandSection}
                readStatus={readStatus}
                onChangeFunc={handleChangeCurrentExpandSection}
              >
                {item.component}
              </AccordionComp>
            );
          })}
        </Box>

        <Box
          sx={{
            backgroundColor: "white",
            p: 3,
            borderRadius: "15px",
            mt: 2,
          }}
        >
          <form>
            <Box
              sx={{
                pl: 2,
                p: 3,
                mt: 1,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
              }}
            >
              <Box
                sx={{
                  mb: 3,
                }}
              >
                <Typography color={"primary"}>
                  {currentLanguage === "my"
                    ? "Maklumat Perlembagaan (semakan RO)"
                    : "Constitutional Information (RO review)"}
                </Typography>
              </Box>
              <Grid container>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("frequencyOrdinaryGeneralMeetings")}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                  <Select
                    fullWidth
                    displayEmpty
                    defaultValue=""
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          "& .MuiMenuItem-root": {
                            fontSize: "14px",
                          },
                        },
                      },
                    }}
                    sx={{ height: "37px", fontSize: "14px" }}
                  >
                    {/* <MenuItem key={item.value} value={item.value}>
                        {item.label}
                      </MenuItem> */}
                  </Select>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("deadlineForOrdinaryGeneralMeeting")}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <TextField
                      sx={{
                        width: "100px",
                        backgroundColor: "#FFF",

                        "& .MuiInputBase-input": {
                          fontSize: "14px",
                        },
                      }}
                      size="small"
                    />

                    <Select
                      displayEmpty
                      defaultValue=""
                      MenuProps={{
                        PaperProps: {
                          sx: {
                            "& .MuiMenuItem-root": {
                              fontSize: "14px",
                            },
                          },
                        },
                      }}
                      sx={{ height: "37px", fontSize: "14px", flexGrow: 1 }}
                    >
                      {/* <MenuItem key={item.value} value={item.value}>
                        {item.label}
                      </MenuItem> */}
                    </Select>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("financialYearStartsOn")}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                  <Select
                    fullWidth
                    displayEmpty
                    defaultValue=""
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          "& .MuiMenuItem-root": {
                            fontSize: "14px",
                          },
                        },
                      },
                    }}
                    sx={{ height: "37px", fontSize: "14px" }}
                  >
                    {/* <MenuItem key={item.value} value={item.value}>
                        {item.label}
                      </MenuItem> */}
                  </Select>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("numberOfAuditors")}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                  <Select
                    fullWidth
                    displayEmpty
                    defaultValue=""
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          "& .MuiMenuItem-root": {
                            fontSize: "14px",
                          },
                        },
                      },
                    }}
                    sx={{ height: "37px", fontSize: "14px" }}
                  >
                    {/* <MenuItem key={item.value} value={item.value}>
                        {item.label}
                      </MenuItem> */}
                  </Select>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("realEstateAdministration")}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                  <Select
                    fullWidth
                    displayEmpty
                    defaultValue=""
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          "& .MuiMenuItem-root": {
                            fontSize: "14px",
                          },
                        },
                      },
                    }}
                    sx={{ height: "37px", fontSize: "14px" }}
                  >
                    {/* <MenuItem key={item.value} value={item.value}>
                        {item.label}
                      </MenuItem> */}
                  </Select>
                </Grid>
              </Grid>
            </Box>

            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                marginTop: "34px",
                gap: "10px",
              }}
            >
              <ButtonPrimary disabled={!hasUpdatePermission}>
                {t("update")}
              </ButtonPrimary>
            </Box>
          </form>
        </Box>

        <Box
          sx={{
            backgroundColor: "white",
            p: 3,
            borderRadius: "15px",
            mt: 2,
          }}
        >
          <Box
            sx={{
              pl: 2,
              p: 3,
              mt: 1,
              borderRadius: "10px",
              border: "0.5px solid #dfdfdf",
            }}
          >
            <Box
              sx={{
                mb: 3,
              }}
            >
              <Typography color={"primary"}>{t("kuiri")}</Typography>
            </Box>
            <Box
              sx={{
                display: "flex",
                gap: "4px",
                justifyContent: "flex-end",
                marginBottom: "27px",
              }}
            >
              <IconButton
                aria-label="close"
                sx={{
                  padding: 2,
                  borderRadius: "5px",
                  backgroundColor: "var(--primary-color)",
                }}
              >
                <AddIcon color="#fff" />
              </IconButton>

              <ButtonPrimary>{t("historyInquiry")}</ButtonPrimary>
            </Box>

            <Grid container>
              <Grid item xs={12} sm={4}>
                <Typography variant="body1" sx={labelStyle}>
                  {t("remarks")} {t("kuiri")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  fullWidth
                  disabled={!hasUpdatePermission}
                  sx={{
                    backgroundColor: "#FFF",
                    minHeight: "59px",
                    "& .MuiInputBase-input": {
                      fontSize: "14px",
                      minHeight: "59px",
                    },
                  }}
                  size="small"
                  multiline
                />
              </Grid>
            </Grid>
          </Box>
        </Box>

        <Box
          sx={{
            backgroundColor: "white",
            p: 3,
            borderRadius: "15px",
            mt: 2,
          }}
        >
          <form onSubmit={handleSubmitRoAction(onSubmitRoAction)}>
            <Box
              sx={{
                p: 3,
                mb: 3,
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography variant="h6" component="h2" sx={subTitleStyle}>
                {t("ROAction")}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("responsibleRO")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <SelectFieldController
                    name="ro"
                    control={controlRoAction}
                    options={roListOptions}
                    disabled={!hasUpdatePermission}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("remarks")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={controlRoAction}
                    name="noteRo"
                    multiline
                    disabled={!hasUpdatePermission}
                    defaultValue={getValuesRoAction("noteRo")}
                    sx={{
                      minHeight: "92px",
                    }}
                    sxInput={{
                      minHeight: "92px",
                    }}
                  />
                </Grid>
              </Grid>
            </Box>

            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonPrimary
                variant="contained"
                type="submit"
                disabled={!hasUpdatePermission}
                sx={{ width: isMobile ? "100%" : "auto", mb: 3 }}
              >
                {t("update")}
              </ButtonPrimary>
            </Grid>
          </form>

          <form onSubmit={() => setIsDialogOpen(true)}>
            <Box
              sx={{
                pl: 2,
                p: 3,
                mt: 1,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
              }}
            >
              <Box
                sx={{
                  mb: 3,
                }}
              >
                <Typography color={"primary"}>{t("keputusan")}</Typography>
              </Box>
              <Grid container>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("statusPermohonan")}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                  <Select
                    fullWidth
                    displayEmpty
                    defaultValue=""
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          "& .MuiMenuItem-root": {
                            fontSize: "14px",
                          },
                        },
                      },
                    }}
                    disabled={!hasUpdatePermission}
                    sx={{ height: "37px", fontSize: "14px" }}
                  >
                    {decisionOptions?.map((item: any) => (
                      <MenuItem key={item.value} value={item.value}>
                        {item.label}
                      </MenuItem>
                    ))}
                  </Select>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("remarks")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    sx={{
                      backgroundColor: "#FFF",
                      minHeight: "92px",
                      "& .MuiInputBase-input": {
                        fontSize: "14px",
                        minHeight: "92px",
                      },
                    }}
                    disabled={!hasUpdatePermission}
                    size="small"
                    multiline
                  />
                </Grid>
              </Grid>
            </Box>
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                marginTop: "34px",
                gap: "10px",
              }}
            >
              <ButtonOutline
                onClick={() =>
                  navigate(
                    "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk"
                  )
                }
              >
                {t("back")}
              </ButtonOutline>

              <ButtonPrimary disabled={!hasUpdatePermission}>
                {t("update")}
              </ButtonPrimary>
            </Box>
          </form>
        </Box>
      </Box>

      <DialogConfirmation
        open={isDialogOpen}
        onClose={handleDialogClose}
        onAction={handleFormSubmit}
        isMutating={false}
        onConfirmationText={t("permohonanConfirmation")}
        onSuccessText={t("applicationSuccessSubmited")}
        isSuccess={isSuccess}
        decisionLabel={"Lulus"}
      />
    </>
  );
}

export default KeputusanIndukPembaharuanSetiausahaMigrasi;
