import { useTranslation } from "react-i18next";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { useEffect, useState, useRef } from "react";
import {
  Box,
  Container,
  Grid,
  Typography,
  Button,
  Paper,
  Avatar,
  Link as MuiLink,
  Chip,
  CircularProgress,
  Alert,
  Tooltip,
} from "@mui/material";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import GroupsIcon from "@mui/icons-material/Groups";
import { CalendarEvent } from "calendar-link";
import { useTakwim } from "../../contexts/takwimProvider";
import "./ActivityDetails.css";
import { ModelCalendarActivityResponseBodyGet } from "../../models";
import CustomAvatarGroup from "@/components/AvatarGroup";
import "./takwim-landing.css";
import { useGetIdentity } from "@refinedev/core";
import { IUser } from "@/types/user";
import ButiranContent from "./components/ButiranContent";
import PesertaContent from "./components/PesertaContent";
import MaklumBalasContent from "./components/MaklumBalasContent";
import { DialogActionFlow } from "@/components/dialog/confirm/DialogActionFlow";
import { useCustomMutation } from "@refinedev/core";
import { TakwimPaper } from "@/components/paper";
import { TakwimContainer } from "@/components/container/TakwimContainer";
import { attendParticipants } from "./dummy/attendParticipants";
import { useDispatch } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { fetchEventByEventNo } from "@/redux/APIcalls/takwimThunks";
import { useSelector } from "react-redux";
import { ApiResponse, IEvent, IEventOrganiser } from "@/types/event";
import { eventService } from "@/services/eventService";
import {
  AttendeesCancelledName,
  AttendeesName,
  IAttendeeCancelRequest,
  IAttendeeRequest,
  IEventAttendee,
} from "@/types/eventAttendee";
import { useNotification } from "@refinedev/core";
import { TAB_MAPPING, TAB_NAMES, TabNames } from "./constants";
import AuthHelper from "@/helpers/authHelper";
import {
  NEW_PermissionNames,
  pageAccessEnum,
} from "@/helpers";
import dayjs from "dayjs";
import BackButton from "@/components/BackButton";
import { format } from "date-fns";
import DetailedTooltip from "@/components/tooltip/DetailedTooltip";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";

interface TakwimContextType {
  isAuthenticated: boolean;
}

const statusColors = {
  CONFIRMED: "#4CAF50",
  PENDING: "#FFA726",
  CANCELLED: "#EF5350",
};

const statusLabels = {
  CONFIRMED: "Disahkan",
  PENDING: "Menunggu",
  CANCELLED: "Dibatalkan",
};

const TakwimActivityDetailsPage = () => {
  const { t } = useTranslation();
  const { eventNo } = useParams<{ eventNo: string }>();
  const [searchParams] = useSearchParams();
  const { data: user, isLoading: isUserLoading } = useGetIdentity<IUser>();
  const dispatch = useDispatch<AppDispatch>();
  const { open: openNotification } = useNotification();
  const navigate = useNavigate();
  const tittleColor = "#41C3C3";

  // Initialize tab state based on named parameter
  const [detailActiveTab, setDetailActiveTab] = useState<number>(() => {
    const tabParam = searchParams.get("tab") as TabNames;
    return tabParam ? TAB_MAPPING[tabParam] : 0;
  });
  const [isWalkinParticipant, setIswalkinParticipant] = useState<boolean>(
    () => {
      return searchParams.get("isWalkinParticipant") === "true";
    }
  );
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState<"join" | "cancel">("join");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [event, setEvent] = useState<IEvent | null>(null);
  const [organizer, setOrganizer] = useState<IEventOrganiser | null>(null);
  const [eventAttedees, setEventAttedees] = useState<IEventAttendee[]>([]);
  const { mutate: joinActivity, isLoading: joinLoading } = useCustomMutation();
  const {
    isEventAdmin,
    setIsEventAdmin,
    setUserAttendance,
    userAttendance,
    setIsEventEnded,
    isEventEnded,
  } = useTakwim();
  const [attendees, setAttendees] = useState<AttendeesName[]>([]);
  const [canceledAttendees, setCanceledAttendees] = useState<
    AttendeesCancelledName[]
  >([]);
  const [totalSlots, setTotalSlots] = useState<number>(0);
  const [isErrorSetAttendance, setIsErrorSetAttendance] = useState(false);
  const [isLoadingAttendees, setIsLoadingAttendees] = useState(false);
  const [isLoadingCanceledAttendees, setIsLoadingCanceledAttendees] =
    useState(false);
  const [isPageLoading, setIsPageLoading] = useState(true);
  const [customDialogMessage, setCustomDialogMessage] = useState<string | null>(
    null
  );
  const [responseData, setResponseData] = useState<ApiResponse<any> | null>(
    null
  );
  const [showFeedbackTab, setShowFeedbackTab] = useState(false);

  useEffect(() => {
    // Update active tab when query param changes

    const tabParam = searchParams.get("tab") as TabNames;
    if (user) {
      if (tabParam && TAB_MAPPING[tabParam] !== undefined) {
        setDetailActiveTab(TAB_MAPPING[tabParam]);
      }
    }
  }, [searchParams]);

  useEffect(() => {
    const tabParam = searchParams.get("tab") as TabNames;
    if (isUserLoading) return;
    if (user == undefined) {
      console.log("user only if not login", user === undefined, user);

      if (
        tabParam &&
        TAB_MAPPING[tabParam] !== undefined &&
        TAB_MAPPING[tabParam] == 2
      ) {
        const encodedPath = encodeURIComponent(
          `/takwim/activity/${eventNo}?tab=${tabParam}`
        );
        navigate(`/login`);
      }
    }
  }, [user]);


  function checkPermissionAndUserGroup(accessType: number) {
    const hasPermission: boolean = AuthHelper.hasAuthority([
      NEW_PermissionNames.TAKWIM.label,
    ]);

    // 2 is internal user
    if (localStorage.getItem("portal") === "2") {
      return hasPermission && true;
    } else {
      return hasPermission && false;
    }
  }
  const hasEditEventPermission = checkPermissionAndUserGroup(
    pageAccessEnum.Update
  );

  const hasCreateEventPermission = checkPermissionAndUserGroup(
    pageAccessEnum.Create
  );

  const hasEventManagementPermission =
    hasCreateEventPermission && hasEditEventPermission;

  const fetchEvent = async (eventNo: string) => {
    try {
      setIsPageLoading(true);
      const resEvent = await eventService.getEventByEventNo(eventNo);
      // const data = await response.json();

      setEvent(resEvent.data);
      if (resEvent.code === 200) {
        const today = dayjs();
        const eventEndDate = dayjs(resEvent.data?.eventEndDate, "YYYY-MM-DD");
        const eventEndDateTime = dayjs(
          `${resEvent.data?.eventEndDate} ${resEvent.data?.endTime}`,
          "YYYY-MM-DD HH:mm:ss"
        );

        if (eventEndDate.isBefore(today, "day")) {
          // End date is before today: event ended
          setIsEventEnded(true);
        } else if (eventEndDate.isSame(today, "day")) {
          // End date is today: check endTime
          setIsEventEnded(eventEndDateTime.isBefore(today));
        } else {
          // End date is after today: event not ended
          setIsEventEnded(false);
        }
      }
      setIsPageLoading(false);
      setIsLoading(false);
    } catch (error) {
      setError("Failed to fetch event details");
      setIsLoading(false);
      setIsPageLoading(false);
    }
  };
  useEffect(() => {
    if (eventNo) {
      fetchEvent(eventNo);
      fetchEventAttendees(eventNo);
      fetchEventAttendeesDetails(eventNo);
      if (user) {
        fetchAttendeeDetail(user?.identificationNo, eventNo);
        fetchEventAttendeesCanceledDetails(eventNo);
      }
      // if(!isEventAdmin){
      //   fetchEventAdmin(user?.identificationNo);
      // }
    }
  }, [eventNo]);
  const fetchEventAdmin = async (identificationNo: string | undefined) => {
    try {
      // setIsLoading(true);
      const eAdmin = await eventService.getEventAdmin(identificationNo);

      if (eAdmin.id) {
        setIsEventAdmin(true);
      } else {
        setIsEventAdmin(false);
      }
    } catch (error) {
      console.log("");
    } finally {
      // setIsLoading(false);
    }
  };

  const fetchAttendeeDetail = async (
    identificationNo: string | undefined,
    eventNo: string | undefined
  ) => {
    if (!user) return;

    try {
      // setIsLoading(true);
      const attendeeDetail: ApiResponse<IEventAttendee> =
        await eventService.getAttendeesDetailByIdentificationNo(
          identificationNo,
          eventNo
        );
      if (attendeeDetail?.data?.present) {
        setShowFeedbackTab(true);
      }
      setUserAttendance(attendeeDetail.data);

      // setEventAttedees(attendees); // Set the organizer state with the returned data
    } catch (error) {
      console.log(
        error instanceof Error
          ? error.message
          : "Failed to fetch feedback questions"
      );
    } finally {
      // setIsLoading(false);
    }
  };

  const fetchEventAttendees = async (eventNo: string) => {
    try {
      // setIsLoading(true);
      const attendees = await eventService.getAttendeesByEventNo(eventNo);

      setEventAttedees(attendees); // Set the organizer state with the returned data
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch event details"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const fetchEventAttendeesDetails = async (eventNo: string) => {
    try {
      setIsLoadingAttendees(true);
      const resAttendees: ApiResponse<AttendeesName[]> =
        await eventService.getAttendeesDetailByEventNo(eventNo);
      setAttendees(resAttendees.data);
      // setEventAttedees(attendees); // Set the organizer state with the returned data
    } catch (error) {
      console.log(error);
    } finally {
      // setIsLoading(false);
      setIsLoadingAttendees(false);
    }
  };
  const fetchEventAttendeesCanceledDetails = async (eventNo: string) => {
    try {
      setIsLoadingCanceledAttendees(true);
      const resAttendees: ApiResponse<AttendeesCancelledName[]> =
        await eventService.getCanceledAttendeesDetailByEventNo(eventNo);

      setCanceledAttendees(resAttendees.data);

      // setEventAttedees(attendees); // Set the organizer state with the returned data
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoadingCanceledAttendees(false);
    }
  };

  // }, []);
  // console.log(data, "events details")
  const handleTabClick = (tabIndex: number) => {
    const tabName = TAB_NAMES[tabIndex as keyof typeof TAB_NAMES];
    navigate(`/takwim/activity/${eventNo}?tab=${tabName}`);
    setDetailActiveTab(tabIndex);
  };

  const organiser = {
    name: "Tuan Azwan bin Ali",
    avatar: "/path/to/main-avatar.jpg",
  };

  const renderContent = () => {
    switch (detailActiveTab) {
      case 0:
        return (
          <ButiranContent
            data={event ?? undefined}
            mainUser={organizer}
            members={eventAttedees}
            setEvent={setEvent}
          />
        );
      case 1:
        return (
          <PesertaContent
            mainUser={organiser}
            attendees={attendees}
            totalSlots={event?.maxParticipants}
            isLoadingAttendees={isLoadingAttendees}
            renderNoParticipant={renderNoParticipant}
            hasEventManagementPermission={hasEventManagementPermission}
          />
        );
      case 2:
        return showFeedbackTab || hasEventManagementPermission ? (
          <MaklumBalasContent
            feedbackName={event?.feedbackName}
            eventNo={eventNo}
            eventEndDate={
              event?.eventEndDate ? new Date(event.eventEndDate) : null
            }
            isEventEnded={isEventEnded}
          />
        ) : (
          <ButiranContent
            data={event ?? undefined}
            mainUser={organizer}
            members={eventAttedees}
            setEvent={setEvent}
          />
        );
      default:
        return (
          <ButiranContent
            data={event ?? undefined}
            mainUser={organizer}
            members={eventAttedees}
            setEvent={setEvent}
          />
        );
    }
  };

  const createAttendance = async () => {};

  const handleJoinRequest = () => {
    if (!user) {
      navigate(`/login?to=/takwim/activity/${eventNo}`);
      openNotification?.({
        message: "Sila log masuk/daftar akaun untuk sertai acara.",
        type: "error",
      });

      return;
    }
    setDialogType("join");
    setOpenDialog(true);
  };

  const handleCancelRequest = () => {
    setDialogType("cancel");
    setOpenDialog(true);
  };

  const renderNoParticipant = () => {
    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          m: "auto",
          // mr: "auto",
          height: "150px",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <AccountCircleIcon
          sx={{
            fontSize: 60,
            color: "#DADADA",
            // m: "auto",
          }}
        />
        <Typography
          variant="body1"
          sx={{ color: "#666666", fontWeight: "bold" }}
        >
          Tiada peserta
        </Typography>
      </Box>
    );
  };

  // add conditio if user not login
  const handleConfirm = async () => {
    // Simulate API call
    if (dialogType == "join") {
      try {
        const attendeeData: IAttendeeRequest = {
          eventNo: eventNo ?? "",
          fullName: user?.name ?? "",
          email: user?.email ?? "",
          phoneNumber: user?.mobilePhone ?? "",
          identificationNo: user?.identificationNo ?? "",
          societyId: "",
          // ... other attendee data
        };
        const response: ApiResponse<IEventAttendee> =
          await eventService.createEventAttendee(attendeeData);
        setUserAttendance(response.data);

        // Handle success
        if (response.code === 201) {
          setUserAttendance(response.data);

          openNotification?.({
            message: response.msg || t("Penyertaan acara berjaya"),
            type: "success",
          });

          if (isWalkinParticipant) {
            navigate(`/takwim/update-attendance?eventNo=${eventNo}`);
          }

          (eventNo ? fetchEventAttendees(eventNo) : null) ?? null;
          return; // Success case
        } else {
          // If response code is not 201, throw an error with the API message
          throw new Error(response.msg || "Failed to join event");
        }
      } catch (error) {
        // Handle error
        if (error instanceof Error) {
          setCustomDialogMessage(error.message || null);
        } else {
          setCustomDialogMessage("An unknown error occurred");
        }
        openNotification?.({
          message:
            error instanceof Error
              ? error.message
              : "An unknown error occurred",
          type: "error",
        });
        throw error; // Re-throw to be caught by DialogActionFlow
      } finally {
        fetchEventAttendeesDetails(eventNo ?? "");
      }
    } else {
      try {
        const attendeeData: IAttendeeCancelRequest = {
          eventNo: eventNo ?? "",
          attendanceNo: userAttendance?.attendanceNo ?? "",
        };
        const response: ApiResponse<IEventAttendee> =
          await eventService.cancelEventAttendance(attendeeData);
        setUserAttendance(null);

        // Handle success
        if (response.code === 200) {
          openNotification?.({
            message: t("Penyertaan acara dibatalkan."),
            type: "success",
          });
          (eventNo ? fetchEventAttendees(eventNo) : null) ?? null;
          return; // Success case
        } else {
          console.log("not catch");

          // If response code is not 200, throw an error
          // throw new Error(response.msg || "Failed to cancel event attendance");
        }
      } catch (error) {
        setCustomDialogMessage(
          error instanceof Error ? error.message : "An unknown error occurred"
        );

        // Handle error
        openNotification?.({
          message:
            error instanceof Error
              ? error.message
              : "An unknown error occurred",
          type: "error",
        });
        throw error; // Re-throw to be caught by DialogActionFlow
      } finally {
        fetchEventAttendeesDetails(eventNo ?? "");
      }
    }
  };
  if (isPageLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "80vh",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <>
      <Box
        minWidth="lg"
        sx={{
          display: "flex",
          flexDirection: "column",
          // gap: 2,
          mb: 2,
          ml: "auto",
          mr: "auto",
        }}
      >
        <BackButton label="Takwim" to="" />
        {/* Header Image Box */}

        <TakwimContainer>
          <Box
            sx={{
              display: "grid",
              gap: 2,
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 2,
                backgroundColor: "#333333", // Default dark background when no banner
                backgroundImage: event?.bannerUrl
                  ? `url(${event.bannerUrl})`
                  : "none",
                backgroundSize: "cover",
                backgroundPosition: "center",
                backgroundRepeat: "no-repeat",
                height: "100px",
                width: "100%",
                borderRadius: "10px",
                padding: "16px",
                position: "relative",
                "&::before": {
                  // Add overlay to ensure text readability
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: "rgba(0, 0, 0, 0.5)",
                  borderRadius: "inherit",
                },
              }}
            >
              <Avatar
                src="/path-to-avatar.jpg"
                sx={{
                  width: "40px",
                  height: "40px",
                  position: "relative",
                  zIndex: 1,
                  border: "2px solid white",
                }}
              />
              <Typography
                variant="h6"
                sx={{
                  color: "white",
                  position: "relative",
                  zIndex: 1,
                  textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
                }}
              >
                {event?.eventName}
              </Typography>
            </Box>
            <Box
              sx={{
                width: "100%",
                display: "flex",
                gap: 2,
                border: "0.2px solid #DADADA",
                p: 0.5,
                borderRadius: "10px",
                boxShadow: "0px 12px 24px #EAE8E866",
              }}
            >
              <Button
                className="tab-btn"
                fullWidth
                variant={detailActiveTab === 0 ? "contained" : "text"}
                onClick={() => handleTabClick(0)}
              >
                Butiran
              </Button>
              {user?.id && (
                <>
                  {event?.hasMax && (
                    <Button
                      className="tab-btn"
                      fullWidth
                      variant={detailActiveTab === 1 ? "contained" : "text"}
                      onClick={() => handleTabClick(1)}
                    >
                      Peserta
                    </Button>
                  )}

                  {/* hide when event if user not for this event not finish yet */}
                  {(showFeedbackTab || hasEventManagementPermission) &&
                    event?.hasMax && (
                      <Button
                        className="tab-btn"
                        fullWidth
                        variant={detailActiveTab === 2 ? "contained" : "text"}
                        onClick={() => handleTabClick(2)}
                      >
                        Maklum Balas
                      </Button>
                    )}
                </>
              )}
            </Box>
          </Box>
        </TakwimContainer>

        {/* Main Content */}
        <TakwimContainer>
          {renderContent()}
          {detailActiveTab !== 2 && (
            // TO DO Hide button if admin
            <Box sx={{ display: "grid", justifyContent: "center", gap: 2 }}>
              {!hasEventManagementPermission && (
                <>
                  {event?.hasMax && (
                    <>
                      {/* hide when event finish */}
                      {!userAttendance &&
                        event?.eventEndDate &&
                        // If end date is after today, show button
                        (dayjs(event.eventEndDate).isAfter(dayjs(), "day") ||
                          // If end date is today, check end time is after now
                          (dayjs(event.eventEndDate).isSame(dayjs(), "day") &&
                            dayjs(
                              `${event.eventEndDate} ${event.endTime}`
                            ).isAfter(dayjs()))) && (
                          <Button
                            variant={user?.id ? "outlined" : "contained"}
                            size="large"
                            onClick={handleJoinRequest}
                            sx={{
                              width: "322px",
                              textTransform: "none",
                              borderRadius: "10px",
                              px: 4,
                            }}
                          >
                            Permintaan untuk Sertai
                          </Button>
                        )}

                      {/* hide when event start */}
                      {userAttendance && event?.eventStartDate && (
                        <Box sx={{ display: "flex", justifyContent: "center" }}>
                          <MuiLink
                            className="link"
                            onClick={handleCancelRequest}
                            sx={{ cursor: "pointer" }}
                          >
                            Batal permintaan sertai
                          </MuiLink>
                        </Box>
                      )}
                    </>
                  )}
                  {!event?.hasMax && (
                    <Typography variant="body1" sx={{ color: "#666666" }}>
                      Acara ini tidak memerlukan pendaftaran
                    </Typography>
                  )}
                </>
              )}
            </Box>
          )}
        </TakwimContainer>
        {detailActiveTab === 1 && hasEventManagementPermission && (
          <TakwimContainer>
            <TakwimPaper>
              <Grid item xs={12}>
                <Typography variant="subtitle1" color="primary" sx={{ mb: 5 }}>
                  {t("Kedatangan Peserta")}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography color={"primary"} sx={{ mb: 2 }}>
                  Peserta telah mengesahkan kehadiran dalam acara:{" "}
                  <Box component="span" sx={{ color: "primary.main" }}>
                    {attendees.filter((member) => member.present).length}/
                    {attendees.length}
                  </Box>
                </Typography>
              </Grid>

              {/* Attend Participants Grid Section */}
              <Grid item xs={12}>
                <Grid container spacing={2}>
                  {isLoadingAttendees ? (
                    <Box
                      sx={{
                        ml: "auto",
                        mr: "auto",
                      }}
                    >
                      <CircularProgress />
                    </Box>
                  ) : (
                    <>
                      {attendees.filter((member) => member.present).length > 0
                        ? attendees
                            .filter((member) => member.present)
                            .map((participant, idx) => (
                              <Grid
                                item
                                xs={6}
                                sm={4}
                                md={2}
                                key={participant.fullName}
                              >
                                <DetailedTooltip
                                  fields={[
                                    {
                                      label: "Nama",
                                      value: participant.fullName,
                                    },
                                    {
                                      label: "Nama Pertubuhan",
                                      values:
                                        participant.societyNameList &&
                                        participant.societyNameList.length > 0
                                          ? participant.societyNameList
                                          : ["Tiada"],
                                    },
                                    {
                                      label: "Jawatan",
                                      values:
                                        participant.positionList &&
                                        participant.positionList.length > 0
                                          ? participant.positionList
                                          : ["Tiada"],
                                    },
                                    {
                                      label: "Tarikh Daftar Masuk",
                                      value: participant.dateCheckedIn,
                                      isDate: true,
                                    },
                                    {
                                      label: "Masa Daftar Masuk",
                                      value: participant.timeCheckedIn,
                                      isTime: true,
                                    },
                                  ]}
                                >
                                  <Box
                                    sx={{
                                      display: "flex",
                                      flexDirection: "column",
                                      alignItems: "center",
                                      textAlign: "center",
                                      gap: 1,
                                    }}
                                  >
                                    <Avatar
                                      src={participant.fullName}
                                      alt={participant.fullName}
                                      sx={{
                                        width: 45,
                                        height: 45,
                                        mb: 1,
                                        border: "2px solid white",
                                        boxShadow:
                                          "0px 2px 4px rgba(0, 0, 0, 0.1)",
                                        bgcolor: "#0CA6A6",
                                      }}
                                    />

                                    <Typography
                                      variant="body2"
                                      sx={{
                                        fontSize: "0.875rem",
                                        maxWidth: "100%",
                                        overflow: "hidden",
                                        textOverflow: "ellipsis",
                                        whiteSpace: "nowrap",
                                      }}
                                    >
                                      {participant.fullName}
                                    </Typography>
                                  </Box>
                                </DetailedTooltip>
                              </Grid>
                            ))
                        : renderNoParticipant()}
                    </>
                  )}
                </Grid>
              </Grid>
            </TakwimPaper>
          </TakwimContainer>
        )}
        {detailActiveTab === 1 && hasEventManagementPermission && (
          <TakwimContainer>
            <TakwimPaper>
              <Grid item xs={12}>
                <Typography variant="subtitle1" color="primary" sx={{ mb: 5 }}>
                  {t("Batal Penyertaan")}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography color={"primary"} sx={{ mb: 2 }}>
                  Peserta telah membatalkan penyertaan dalam acara:{" "}
                  <Box component="span" sx={{ color: "primary.main" }}>
                    {canceledAttendees?.length}
                  </Box>
                </Typography>
              </Grid>

              {/* Attend Participants Grid Section */}
              <Grid item xs={12}>
                <Grid container spacing={2}>
                  {isLoadingCanceledAttendees ? (
                    <Box
                      sx={{
                        ml: "auto",
                        mr: "auto",
                      }}
                    >
                      <CircularProgress />
                    </Box>
                  ) : (
                    <>
                      {canceledAttendees.length > 0
                        ? canceledAttendees.map((participant) => (
                            <Grid
                              item
                              xs={6}
                              sm={4}
                              md={2}
                              key={participant.fullName}
                            >
                              <DetailedTooltip
                                fields={[
                                  {
                                    label: "Nama",
                                    value: participant.fullName,
                                  },
                                  {
                                    label: "Nama Pertubuhan",
                                    values:
                                      participant.societyNameList &&
                                      participant.societyNameList.length > 0
                                        ? participant.societyNameList
                                        : ["Tiada"],
                                  },
                                  {
                                    label: "Jawatan",
                                    values:
                                      participant.positionList &&
                                      participant.positionList.length > 0
                                        ? participant.positionList
                                        : ["Tiada"],
                                  },
                                  {
                                    label: "Tarikh Pembatalan",
                                    value: participant.dateCancelled,
                                    isDate: true,
                                  },
                                  {
                                    label: "Masa Pembatalan",
                                    value: participant.timeCancelled,
                                    isTime: true,
                                  },
                                ]}
                              >
                                <Box
                                  sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "center",
                                    textAlign: "center",
                                    gap: 1,
                                    cursor: "pointer", // Add cursor pointer to indicate it's hoverable
                                  }}
                                >
                                  <Avatar
                                    src={participant.fullName}
                                    alt={participant.fullName}
                                    sx={{
                                      width: 45,
                                      height: 45,
                                      mb: 1,
                                      border: "2px solid white",
                                      boxShadow:
                                        "0px 2px 4px rgba(0, 0, 0, 0.1)",
                                      bgcolor: "#0CA6A6",
                                    }}
                                  />

                                  <Typography
                                    variant="body2"
                                    sx={{
                                      fontSize: "0.875rem",
                                      maxWidth: "100%",
                                      overflow: "hidden",
                                      textOverflow: "ellipsis",
                                      whiteSpace: "nowrap",
                                    }}
                                  >
                                    {participant.fullName}
                                  </Typography>
                                </Box>
                              </DetailedTooltip>
                            </Grid>
                          ))
                        : renderNoParticipant()}
                    </>
                  )}
                </Grid>
              </Grid>
            </TakwimPaper>
          </TakwimContainer>
        )}
      </Box>
      <DialogActionFlow
        open={openDialog}
        onClose={() => {
          setOpenDialog(false);
          setCustomDialogMessage(null);
        }}
        onConfirm={customDialogMessage ? async () => {} : handleConfirm}
        hideOnError={false}
        confirmationText={
          customDialogMessage
            ? customDialogMessage
            : dialogType === "join"
            ? "Adakah anda pasti untuk menyertai acara ini?"
            : "Adakah anda pasti untuk batal menyertai acara ini?"
        }
        successMessage={
          dialogType === "join"
            ? "Permohonan sertai acara telah berjaya"
            : customDialogMessage
            ? customDialogMessage
            : "Permohonan batal sertai acara telah berjaya"
        }
        errorMessage={customDialogMessage}
      />
    </>
  );
};

export default TakwimActivityDetailsPage;
