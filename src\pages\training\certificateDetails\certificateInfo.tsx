import React, {useEffect, useRef} from "react";
import {Typography} from "@mui/material";
import Box from "@mui/material/Box";
import ERosesLogoWithName from "@/components/eroses-log-with-name";
import {ButtonPrimary} from "@/components";
import {useTranslation} from "react-i18next";
import {useCustom} from "@refinedev/core";
import {API_URL} from "@/api";
import {formatDate} from "@/helpers";

interface CertificateInfoProps {
  enrollmentData: any
  trainingCertificate: any,
  downloadable?: boolean;
  width?: string;
  handleCallback?: React.Dispatch<React.SetStateAction<string>>
}

const CertificateInfo: React.FC<CertificateInfoProps> = ({
                                                           enrollmentData,
                                                           trainingCertificate,
                                                           downloadable = false,
                                                           width = "80%",
                                                           handleCallback
                                                         }) => {
  const {t, i18n} = useTranslation();

  const svgRef = useRef<SVGSVGElement | null>(null);


  const handleDownload = () => {
    //const filePath = `${API_URL}/${trainingCertificate.certificatePath}`;
    //console.log("handleDownload",filePath);
    //window.open(filePath, "_blank");
  };

  return (
    <>
      <Box
        sx={{
          width: width,
          //flex: 5,
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //flex: 5,
            px: 5,
            py: 2,
            mb: 1,
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <ERosesLogoWithName textColor="#666666"/>
            <Box>
              <Typography
                sx={{
                  color: "#666666",
                  fontWeight: "400",
                  fontSize: 12,
                  textAlign: "right",
                }}
              >
                {`${t("certificateNo")}:`}
              </Typography>
              <Typography
                sx={{
                  color: "#666666",
                  fontWeight: "400",
                  fontSize: 12,
                  textAlign: "right",
                }}
              >
                {`${t("certificateUrl")}: ${API_URL}/${trainingCertificate.certificatePath}`}
              </Typography>
              <Typography
                sx={{
                  color: "#666666",
                  fontWeight: "400",
                  fontSize: 12,
                  textAlign: "right",
                }}
              >
                {`${t("referenceNo")}:`}
              </Typography>
            </Box>
          </Box>
          <Box>
            <Typography
              sx={{
                color: "#666666",
                pt: 3,
                fontWeight: "400",
                fontSize: 14,
              }}
            >
              {t("achievementCertificate")}
            </Typography>
          </Box>
          <Box>
            <Typography
              sx={{
                color: "#000000",
                pt: 3,
                fontWeight: "600",
                fontSize: 40,
              }}
            >
              {enrollmentData.title ?? ""}
            </Typography>
          </Box>
          <Box>
            <Typography
              sx={{
                color: "#666666",
                pt: 3,
                fontWeight: "400",
                fontSize: 14,
              }}
            >

            </Typography>
          </Box>
          <Box
            sx={{
              mt: 5,
              pt: 5,
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <Box>
              <Typography
                sx={{
                  color: "var(--primary-color)",
                  pt: 3,
                  fontWeight: "600",
                  fontSize: 30,
                }}
              >
                {trainingCertificate.user?.name ?? ""}
              </Typography>
              <Typography
                sx={{
                  color: "#666666",
                  pt: 3,
                  fontWeight: "400",
                  fontSize: 14,
                }}
              >
                {`${t("date")}: ${formatDate(trainingCertificate.issueDate)}`}
              </Typography>
              <Typography
                sx={{
                  color: "#666666",
                  pt: 3,
                  fontWeight: "400",
                  fontSize: 14,
                }}
              >
                {`${t("time")}: ${formatDate(trainingCertificate.issueDate, "hh:mm:ss")}`}
              </Typography>
            </Box>
          </Box>
        </Box>
        {downloadable ? (
          <Box sx={{display: "flex", mt: 1, justifyContent: "flex-end"}}>
            <ButtonPrimary
              variant="outlined"
              sx={{
                borderColor: "#666666",
                bgcolor: "#fff",
                "&:hover": {bgcolor: "#fff", borderColor: "#666666"},
                color: "#666666",
                fontWeight: "400",
              }}
              onClick={handleDownload}
            >
              {t("download")}
            </ButtonPrimary>
          </Box>
        ) : (
          <></>
        )}
      </Box>
    </>
  );
};

export default CertificateInfo;
