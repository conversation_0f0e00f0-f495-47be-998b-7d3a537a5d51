import { CircularProgress, I<PERSON><PERSON><PERSON>on, Pagination } from "@mui/material";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  Typography,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  TableFooter,
  Paper,
  Fade,
} from "@mui/material";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DeleteIcon from "@/assets/svg/icon-trash.svg?react";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { OrganizationStepper } from "../organization-stepper";
import SwapVertIcon from "@mui/icons-material/SwapVert";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";

import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { useSelector } from "react-redux";
import { selectAJK } from "../../../../redux/ajkReducer";
import {
  ApplicationStatus,
  MALAYSIA,
  ApplicationStatusEnum,
  OrganisationPositions,
} from "../../../../helpers/enums";
import InfoQACard from "../InfoQACard";
import { EditIcon, EyeIcon } from "@/components/icons";
import { capitalizeWords, useQuery } from "@/helpers";
import ConfirmationDialog from "@/components/dialog/confirm";
import { useCheckAndUpdateRegistration } from "@/helpers/hooks/useCheckAndupdateRegister";
import { formatArrayDateField } from "@/helpers/timeUtils";

export const ListSenaraiAjk: React.FC = () => {
  const navigate = useNavigate();
  const [allow, setAllow] = useState(true);
  const [activeStep, setActiveStep] = useState(3);
  const { t } = useTranslation();
  const [params] = useSearchParams();
  const encodedId = params.get("id");
  const id = atob(encodedId || "");
  const [pageListSenarai, setPageListSenarai] = useState(1);
  const [selectedCommitteeToDelete, setSelectedCommitteeToDelete] = useState<
    any | null
  >(null);
  const [nonCitizenAjkList, setNonCitizenAjkList] = useState<any[]>();
  const [societyAjkType, setSocietyAjkType] = useState("");

  const { isLoading: isLoadingSocietyAjkType } = useCustom({
    url: `${API_URL}/society/constitutioncontent/getAjkType`,
    method: "get",
    config: {
      filters: [{ field: "societyId", operator: "eq", value: id }],
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (data) => {
        const response = data?.data?.data?.societyAjkType;
        setSocietyAjkType(response);
      },
    },
  });

  const { data: listCommittee, isLoading: isLoadingCommittee } = useCustom({
    url: `${API_URL}/society/committee/getAll`,
    method: "get",
    config: {
      filters: [
        {
          field: "pageNo",
          operator: "eq",
          value: 1,
        },
        {
          field: "pageSize",
          operator: "eq",
          value: 1000,
        },
        {
          field: "societyId",
          operator: "eq",
          value: id,
        },
      ],
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !!encodedId,
    },
  });

  const committeeData = listCommittee?.data?.data?.data || [];

  const [pageNonListSenarai, setPageNonListSenarai] = useState(1);
  const {
    data: listNonCitizenCommittee,
    isLoading: isLoadingNonCitizenCommittee,
    refetch,
  } = useCustom({
    url: `${API_URL}/society/nonCitizenCommittee/getAll`,
    method: "get",
    config: {
      filters: [
        {
          field: "societyId",
          operator: "eq",
          value: id,
        },
        {
          field: "applicationStatusCode",
          operator: "ne",
          value: -1,
        },
      ],
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !!encodedId,
      onSuccess: (data) => {
        setNonCitizenAjkList(data?.data?.data?.data || []);
      },
    },
  });

  const nonCitizenCommitteeData =
    listNonCitizenCommittee?.data?.data?.data || [];

  const handleDaftarAJKBukanWn = () => {
    navigate(
      `/pertubuhan/pengurusan-pertubuhan/pendaftaran/create-ajk-bukanwn?id=${params.get(
        "id"
      )}`
    );
  };

  const [orderBy, setOrderBy] = useState("");
  const [orderDirection, setOrderDirection] = useState("asc");

  const handleSort = (column: any) => {
    const isAsc = orderBy === column && orderDirection === "asc";
    setOrderDirection(isAsc ? "desc" : "asc");
    setOrderBy(column);
  };

  const { mutate: editNonCitizenIds, isLoading: isLoadingEdit } =
    useCustomMutation();
  const { mutateAsync: deleteAJK, isLoading: isLoadingDeleteAJK } =
    useCustomMutation();

  const EditNonCitizenIds: (ids: any) => void = (ids) => {
    editNonCitizenIds(
      {
        url: `${API_URL}/society/nonCitizenCommittee/update-by-list`,
        method: "put",
        values: { ids },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          refetch();
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const hantarNonCitizen = () => {
    const ids = nonCitizenAjkList
      ?.filter((item) => Number(item.applicationStatusCode) === 1)
      .map((item) => item.id);
    EditNonCitizenIds(ids);
  };

  const ajk = useSelector(selectAJK);
  const itemsPerPage = 10; // Set how many items per page
  //const combinedData = [...committeeData, ...ajk]; // Combine the two arrays
  //const combinedNonData =
  // Calculate the start and end index for the current page
  const [currentPageData, setCurrentPageData] = useState([]);
  const [currentNonPageData, setCurrentNonPageData] = useState([]);
  const [combinedData, setCombinedData] = useState([]);
  // Helper function to sort data
  const sortData = (data: any[], orderBy: string, orderDirection: string) => {
    if (!orderBy) return data;

    return [...data].sort((a, b) => {
      let aValue, bValue;

      switch (orderBy) {
        case "jawatan":
          aValue = a?.designationCode || 0;
          bValue = b?.designationCode || 0;
          break;
        case "namaAJK":
          aValue = a?.name || "";
          bValue = b?.name || "";
          break;
        case "emel":
          aValue = a?.email || "";
          bValue = b?.email || "";
          break;
        case "negeri":
          aValue = a?.residentialStateCode || 0;
          bValue = b?.residentialStateCode || 0;
          break;
        default:
          return 0;
      }

      // Handle string comparison
      if (typeof aValue === "string" && typeof bValue === "string") {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return orderDirection === "asc" ? -1 : 1;
      }
      if (aValue > bValue) {
        return orderDirection === "asc" ? 1 : -1;
      }
      return 0;
    });
  };

  useEffect(() => {
    if (committeeData.length > 0) {
      const temp = [...committeeData, ...ajk];
      // @ts-ignore
      setCombinedData(temp);

      // Apply sorting first
      const sortedData = sortData(temp, orderBy, orderDirection);

      const startIndex = (pageListSenarai - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;

      // Slice the sorted data based on the page and items per page
      // @ts-ignore
      setCurrentPageData(sortedData.slice(startIndex, endIndex));
    }
  }, [pageListSenarai, committeeData, orderBy, orderDirection]);

  useEffect(() => {
    const startIndex = (pageNonListSenarai - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;

    // Slice the data based on the page and items per page
    //const currentPageData = combinedData.slice(startIndex, endIndex);
    // @ts-ignore
    setCurrentNonPageData(nonCitizenCommitteeData.slice(startIndex, endIndex));
  }, [pageNonListSenarai, nonCitizenCommitteeData]);

  const [isNext, setIsNext] = useState(true);
  const [showConfirmDeleteDialog, setShowConfirmDeleteDialog] = useState(false);

  // @ts-ignore
  const createAjk = (row) => {
    const linkData = JSON.stringify(row);
    const encodedData = encodeURIComponent(linkData);
    navigate(`../create-ajk?id=${params.get("id")}`, {
      state: encodedData,
    });
  };

  // @ts-ignore
  const createNonCitizenAjk = (row) => {
    const linkData = JSON.stringify(row);
    const encodedData = encodeURIComponent(linkData);
    navigate(
      "../create-ajk-bukanwn?id=" + params.get("id") + "&value=" + encodedData
    );
  };

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  //const addressList = getLocalStorage("address_list", null);
  // @ts-ignore
  const addressData = useSelector((state) => state.addressData.data);
  const StateList = addressData
    .filter((item: any) => item.pid === MALAYSIA)
    .map((item: any) => ({ value: item.id, label: item.name }));
  //console.log(StateList);
  const confirmDeleteNonCitizenCommittee = (row: any) => {
    setShowConfirmDeleteDialog(true);
    setSelectedCommitteeToDelete(row);
  };
  const handleDeleteNonCitizenCommittee = async () => {
    try {
      const values = {
        ...selectedCommitteeToDelete,
        applicationStatusCode: -1,
        // Convert date fields that might be in array format
        ...(selectedCommitteeToDelete.visaExpirationDate && {
          visaExpirationDate: formatArrayDateField(
            selectedCommitteeToDelete.visaExpirationDate
          ),
        }),
        ...(selectedCommitteeToDelete.permitExpirationDate && {
          permitExpirationDate: formatArrayDateField(
            selectedCommitteeToDelete.permitExpirationDate
          ),
        }),
        ...(selectedCommitteeToDelete.transferDate && {
          transferDate: formatArrayDateField(
            selectedCommitteeToDelete.transferDate
          ),
        }),
      };
      await deleteAJK({
        url: `${API_URL}/society/nonCitizenCommittee/${values.id}/edit`,
        method: "put",
        values,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      });
      await refetch();
    } finally {
      setShowConfirmDeleteDialog(false);
      setSelectedCommitteeToDelete(null);
    }
  };

  const { data: checkAndUpdateRegistrationData, fetch: fetchCheck } =
    useCheckAndUpdateRegistration({
      id: societyDataRedux?.id,
      pageNo: 4,
      enabled: false,
      onSuccessNotification: (data) => {
        const responseData = data?.data?.data;
        //  const message = data?.data?.msg
        //  if(!responseData){
        //   return {
        //     message: message,
        //     type: "error",
        //   };
        //  }
      },
      onSuccess: (data) => {
        const responseData = data?.data?.data;
        if (responseData) {
          navigate("../dokumen-sokongan?id=" + encodedId);
        }
      },
    });

  const handleGoNext = () => {
    fetchCheck();
  };

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box
        sx={{
          width: "55vw",
          background: "#fff",
          padding: 3,
          borderRadius: "10px",
        }}
      >
        <Fade in={true} timeout={500}>
          <Box>
            <Box
              sx={{
                padding: 3,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
              }}
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  fontWeight: "bold",
                  fontSize: 14,
                  color: "var(--primary-color)",
                }}
              >
                {t("ajkList")}
              </Typography>
              <Box>
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    mb: 2,
                    gap: 2,
                  }}
                >
                  {/* <ButtonOutline
                    sx={{ alignSelf: "flex-end", px: 2, py: 1 }}
                    onClick={handleDaftarAJK}
                    disabled
                  >
                    {t("registerAJK")}
                  </ButtonOutline> */}
                </Box>
                <Typography
                  variant="body2"
                  sx={{ textAlign: "right", mb: 1, color: "blue" }}
                ></Typography>

                <TableContainer
                  component={Paper}
                  sx={{
                    boxShadow: "none",
                    backgroundColor: "white",
                    borderRadius: 2.5 * 1.5,
                    p: 1,
                    mb: 3,
                  }}
                >
                  <Table>
                    <TableHead>
                      <TableRow>
                        {/* Table Header remains the same */}
                        <TableCell
                          onClick={() => handleSort("jawatan")}
                          sx={{ cursor: "pointer", userSelect: "none" }}
                        >
                          {t("position")}
                          {orderBy === "jawatan" ? (
                            orderDirection === "asc" ? (
                              <ArrowUpwardIcon sx={{ ml: 1, fontSize: 16 }} />
                            ) : (
                              <ArrowDownwardIcon sx={{ ml: 1, fontSize: 16 }} />
                            )
                          ) : (
                            <SwapVertIcon
                              sx={{ ml: 1, fontSize: 16, opacity: 0.5 }}
                            />
                          )}
                        </TableCell>
                        <TableCell
                          onClick={() => handleSort("namaAJK")}
                          sx={{ cursor: "pointer", userSelect: "none" }}
                        >
                          {t("ajkName")}
                          {orderBy === "namaAJK" ? (
                            orderDirection === "asc" ? (
                              <ArrowUpwardIcon sx={{ ml: 1, fontSize: 16 }} />
                            ) : (
                              <ArrowDownwardIcon sx={{ ml: 1, fontSize: 16 }} />
                            )
                          ) : (
                            <SwapVertIcon
                              sx={{ ml: 1, fontSize: 16, opacity: 0.5 }}
                            />
                          )}
                        </TableCell>
                        <TableCell
                          onClick={() => handleSort("emel")}
                          sx={{ cursor: "pointer", userSelect: "none" }}
                        >
                          {t("email")}
                          {orderBy === "emel" ? (
                            orderDirection === "asc" ? (
                              <ArrowUpwardIcon sx={{ ml: 1, fontSize: 16 }} />
                            ) : (
                              <ArrowDownwardIcon sx={{ ml: 1, fontSize: 16 }} />
                            )
                          ) : (
                            <SwapVertIcon
                              sx={{ ml: 1, fontSize: 16, opacity: 0.5 }}
                            />
                          )}
                        </TableCell>
                        <TableCell
                          onClick={() => handleSort("negeri")}
                          sx={{ cursor: "pointer", userSelect: "none" }}
                        >
                          {t("state")}
                          {orderBy === "negeri" ? (
                            orderDirection === "asc" ? (
                              <ArrowUpwardIcon sx={{ ml: 1, fontSize: 16 }} />
                            ) : (
                              <ArrowDownwardIcon sx={{ ml: 1, fontSize: 16 }} />
                            )
                          ) : (
                            <SwapVertIcon
                              sx={{ ml: 1, fontSize: 16, opacity: 0.5 }}
                            />
                          )}
                        </TableCell>
                        <TableCell align="right"></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {isLoadingCommittee ? (
                        <TableRow>
                          <TableCell align="center" colSpan={5}>
                            <CircularProgress />
                          </TableCell>
                        </TableRow>
                      ) : combinedData.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} align="center">
                            <Box>Data kosong</Box>
                          </TableCell>
                        </TableRow>
                      ) : (
                        currentPageData.map((row: any) => {
                          return (
                            <TableRow key={row?.id}>
                              <TableCell>
                                {t(
                                  OrganisationPositions.find(
                                    (item) =>
                                      item?.value ===
                                      Number(row?.designationCode)
                                  )?.label || "-"
                                )}
                              </TableCell>
                              <TableCell>{row?.name}</TableCell>
                              <TableCell>{row?.email ?? "-"}</TableCell>
                              <TableCell>
                                {row?.residentialStateCode && StateList != null
                                  ? capitalizeWords(
                                      StateList.find(
                                        (item: any) =>
                                          item?.value ===
                                          Number(row?.residentialStateCode)
                                      )?.label ?? "-"
                                    )
                                  : "-"}
                              </TableCell>

                              <TableCell align="right">
                                {row.jawatan === "Setiausaha" ? (
                                  <IconButton>
                                    {/* Empty button if Setiausaha */}
                                  </IconButton>
                                ) : (
                                  <IconButton
                                    sx={{
                                      border: "1px solid #DADADA",
                                      padding: "8px 24px",
                                      borderRadius: 1,
                                    }}
                                    onClick={() => createAjk(row)}
                                  >
                                    <EditIcon color="var(--primary-color)" />
                                  </IconButton>
                                )}
                              </TableCell>
                            </TableRow>
                          );
                        })
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>

                {/* Pagination */}
                <Pagination
                  count={
                    isNaN(Math.ceil(combinedData?.length / itemsPerPage)) ||
                    combinedData?.length == 0
                      ? 1
                      : Math.ceil(combinedData?.length / itemsPerPage)
                  } // Calculate total pages
                  page={pageListSenarai}
                  onChange={(_, newPage) => {
                    setPageListSenarai(newPage);
                  }} // Update page state
                />
              </Box>
            </Box>

            <Box
              sx={{
                padding: 3,
                border: "0.5px solid #dfdfdf",
                borderRadius: "10px",
                mt: 3,
              }}
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  fontWeight: "bold",
                  fontSize: 14,
                  color: "var(--primary-color)",
                }}
              >
                {t("nonCitizenAJK")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  mb: 2,
                  gap: 2,
                }}
              >
                <ButtonOutline
                  sx={{ alignSelf: "flex-end", px: 2, py: 1 }}
                  onClick={handleDaftarAJKBukanWn}
                  disabled={
                    isLoadingSocietyAjkType || societyAjkType === "Warganegara"
                  }
                >
                  {t("registerNonCitizenAJK")}
                </ButtonOutline>
              </Box>

              <TableContainer
                component={Paper}
                sx={{
                  boxShadow: "none",
                  border: "1px solid #e0e0e0",
                  backgroundColor: "white",
                  borderRadius: 2.5 * 1.5,
                  p: 1,
                  mb: 3,
                }}
              >
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell
                        sx={{
                          borderBottom: "1px solid #e0e0e0",
                          color: "black",
                          p: 1,
                        }}
                      >
                        {t("position")}
                      </TableCell>
                      <TableCell
                        sx={{
                          borderBottom: "1px solid #e0e0e0",
                          color: "black",
                          p: 1,
                        }}
                      >
                        {t("ajkName")}
                      </TableCell>
                      <TableCell
                        sx={{
                          borderBottom: "1px solid #e0e0e0",
                          color: "black",
                          p: 1,
                        }}
                      >
                        {t("applicationStatus")}
                      </TableCell>
                      <TableCell
                        align="right"
                        sx={{
                          borderBottom: "1px solid #e0e0e0",
                          color: "black",
                          p: 1,
                        }}
                      >
                        {t("action")}
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {isLoadingNonCitizenCommittee && (
                      <TableRow>
                        <TableCell align="center" colSpan={4}>
                          <CircularProgress />
                        </TableCell>
                      </TableRow>
                    )}
                    {nonCitizenCommitteeData?.length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={4}
                          align="center"
                          sx={{ border: 0, mt: 10 }}
                        >
                          <Box>Data kosong</Box>
                        </TableCell>
                      </TableRow>
                    ) : null}
                    {currentNonPageData?.map((row: any) => {
                      const applicationStatusCode = row?.applicationStatusCode
                        ? capitalizeWords(
                            Object.entries(ApplicationStatusEnum).find(
                              ([key]) =>
                                parseInt(key) === row?.applicationStatusCode
                            )?.[1]!
                          ) ?? "UNKNOWN"
                        : "UNKNOWN";
                      return (
                        <TableRow key={row?.id}>
                          <TableCell
                            sx={{
                              color: "black",
                              borderBottom: "1px solid #e0e0e0",
                              p: 1,
                            }}
                          >
                            {t(
                              OrganisationPositions.find(
                                (item) =>
                                  item?.value === Number(row?.designationCode)
                              )?.label || "-"
                            )}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "black",
                              borderBottom: "1px solid #e0e0e0",
                              p: 1,
                            }}
                          >
                            {row?.name}
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "black",
                              borderBottom: "1px solid #e0e0e0",
                              p: 1,
                            }}
                          >
                            {applicationStatusCode}
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{ borderBottom: "1px solid #e0e0e0", p: 1 }}
                          >
                            {row?.applicationStatusCode === 2 && (
                              <Box
                                sx={{
                                  display: "flex",
                                  justifyContent: "flex-end",
                                  gap: 1,
                                }}
                              >
                                <IconButton
                                  color="primary"
                                  sx={{ height: "2rem", width: "2rem" }}
                                  onClick={() => createNonCitizenAjk(row)}
                                >
                                  <EyeIcon />
                                </IconButton>
                              </Box>
                            )}

                            {row?.applicationStatusCode === 1 && (
                              <Box
                                sx={{
                                  display: "flex",
                                  justifyContent: "flex-end",
                                  gap: 1,
                                }}
                              >
                                <IconButton
                                  color="primary"
                                  sx={{
                                    minWidth: "2rem",
                                    minHeight: "2rem",
                                  }}
                                  onClick={() => createNonCitizenAjk(row)}
                                >
                                  <EditIcon />
                                </IconButton>
                                <IconButton
                                  color="error"
                                  sx={{
                                    minWidth: "2rem",
                                    minHeight: "2rem",
                                  }}
                                  onClick={() =>
                                    confirmDeleteNonCitizenCommittee(row)
                                  }
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Box>
                            )}
                          </TableCell>
                        </TableRow>
                      );
                    }) ?? false}
                  </TableBody>
                  <Pagination
                    page={pageNonListSenarai}
                    onChange={(_, page) => {
                      setPageNonListSenarai(page);
                    }}
                    sx={{
                      mt: 3,
                    }}
                    count={
                      isNaN(
                        Math.ceil(
                          listNonCitizenCommittee?.data?.data?.total / 10
                        )
                      ) || listNonCitizenCommittee?.data?.data?.total == 0
                        ? 1
                        : Math.ceil(
                            listNonCitizenCommittee?.data?.data?.total / 10
                          )
                    }
                  />
                  <TableFooter>
                    <TableRow>
                      <TableCell colSpan={4} sx={{ borderBottom: "none" }}>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "flex-end",
                            mt: 2,
                            gap: 2,
                          }}
                        >
                          <ButtonPrimary
                            disabled={
                              !nonCitizenAjkList ||
                              nonCitizenAjkList.length === 0 ||
                              isLoadingEdit ||
                              nonCitizenAjkList?.every(
                                (item) =>
                                  Number(item.applicationStatusCode) !== 1
                              )
                            }
                            onClick={hantarNonCitizen}
                          >
                            {t("hantar")}
                          </ButtonPrimary>
                        </Box>
                      </TableCell>
                    </TableRow>
                  </TableFooter>
                </Table>
              </TableContainer>
            </Box>
          </Box>
        </Fade>

        <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
          <ButtonPrimary
            onClick={() => {
              handleGoNext();
            }}
            disabled={!isNext || !allow}
          >
            {t("next")}
          </ButtonPrimary>
        </Box>
      </Box>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <OrganizationStepper
          // ajkBukanWn={ajkBukanWarganegara}
          activeStep={activeStep}
          hidePayment={
            societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI
          }
        />

        {societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI ? (
          <Box
            sx={{
              padding: 3,
              backgroundColor: "white",
              borderRadius: "15px",
              maxHeight: "60vh",
              maxWidth: "18vw",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("kuiri")}
            </Typography>
            <Box
              sx={{
                padding: 3,
                backgroundColor: "#DADADA",
                borderRadius: "15px",
                maxHeight: "60vh",
                maxWidth: "18vw",
              }}
            >
              <Typography
                sx={{
                  mb: 8,
                  fontSize: "12px",
                  color: "#666666",
                  fontWeight: "500 !important",
                }}
              >
                {societyDataRedux.queryText}
              </Typography>
            </Box>
          </Box>
        ) : null}

        <InfoQACard />
      </Box>
      <ConfirmationDialog
        open={showConfirmDeleteDialog}
        onClose={() => {
          setShowConfirmDeleteDialog(false);
          setSelectedCommitteeToDelete(null);
        }}
        onCancel={() => {
          setShowConfirmDeleteDialog(false);
          setSelectedCommitteeToDelete(null);
        }}
        onConfirm={handleDeleteNonCitizenCommittee}
        title=""
        message={t("deleteConfirmationMessage")}
      />
    </Box>
  );
};

export default ListSenaraiAjk;
