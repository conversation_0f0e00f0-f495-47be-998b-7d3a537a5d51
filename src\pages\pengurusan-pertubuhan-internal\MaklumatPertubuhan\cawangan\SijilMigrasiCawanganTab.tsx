import { Box, Grid, Typography } from "@mui/material";
import { t } from "i18next";
import { useEffect, useMemo, useState } from "react";
import {
  formatDate,
  getMalaysiaAddressList,
  getStateNameById,
} from "../../../../helpers/utils";
import ButtonPrevious from "../../../../components/button/ButtonPrevious";
import { ButtonPrimary } from "../../../../components/button";
import DataTable, { IColumn } from "@/components/datatable";
import Input from "@/components/input/Input";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { CrudFilter } from "@refinedev/core";
import { useQuery } from "@/helpers";
import { MaklumatTabProps } from "../maklumatSelectionTabs";
import ForbiddenPage from "@/pages/forbidden";

function SijilMigrasiCawanganTab({ disabled }: MaklumatTabProps) {
  if (disabled) {
    return <ForbiddenPage internal />;
  } else {
    const sectionStyle = {
      color: "var(--primary-color)",
      marginBottom: "30px",
      borderRadius: "16px",
      fontSize: "14px",
      fontWeight: "500 !important",
    };

    const [displaySenaraiAjk, setDisplaySenaraiAjk] = useState<any[]>([]);
    const [total, setTotal] = useState<number>(0);

    const { control, setValue, watch, getValues, reset, handleSubmit } =
      useForm<FieldValues>({
        defaultValues: {
          pageNo: 1,
          pageSize: 10,
          state: "",
          searchQuery: "",
        },
      });

    const handleClearSearch = () => {
      reset();
      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: 10 },
        { field: "pageNo", operator: "eq", value: 1 },
        {
          field: "state",
          operator: "eq",
          value: null,
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: null,
        },
      ];
      refetch({ filters });
    };

    const malaysiaAddressList = getMalaysiaAddressList() ?? [];
    const stateOptions = useMemo(() => {
      return malaysiaAddressList.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
    }, []);

    const handleSearch = () => {
      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: getValues("pageSize") },
        { field: "pageNo", operator: "eq", value: getValues("pageNo") },
        {
          field: "state",
          operator: "eq",
          value: getValues("state"),
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      refetch({ filters });
    };

    const handleChangePage = (newPage: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: watch("pageSize"), operator: "eq" },
        { field: "pageNo", value: newPage, operator: "eq" },
        {
          field: "state",
          operator: "eq",
          value: getValues("state"),
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      setValue("pageNo", newPage);
      refetch({ filters });
    };

    const handleChangePageSize = (newPageSize: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: newPageSize, operator: "eq" },
        { field: "pageNo", value: 1, operator: "eq" },
        {
          field: "isBranch",
          value: true,
          operator: "eq",
        },
        {
          field: "state",
          operator: "eq",
          value: getValues("state"),
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: getValues("searchQuery"),
        },
      ];
      setValue("pageNo", 1);
      setValue("pageSize", newPageSize);
      refetch({ filters });
    };

    const columns: IColumn[] = [
      {
        field: "name",
        headerName: t("secretaryName"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.name ?? "-"}</Box>;
        },
      },
      {
        field: "societyName",
        headerName: t("organizationName"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.societyName ?? "-"}</Box>;
        },
      },
      {
        field: "societyNo",
        headerName: t("orgNumber"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.societyNo ?? "-"}</Box>;
        },
      },
      {
        field: "branchName",
        headerName: t("branchNameDetails"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.branchName ?? "-"}</Box>;
        },
      },
      {
        field: "branchNo",
        headerName: t("branchNumber"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{row?.branchNo ?? "-"}</Box>;
        },
      },
      {
        field: "replacementDate",
        headerName: t("secretaryChangeDate"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{formatDate(row?.replacementDate) ?? "-"}</Box>;
        },
      },
      {
        field: "stateCode",
        headerName: t("state"),
        align: "center",
        renderCell: ({ row }: any) => {
          return <Box>{getStateNameById(row?.stateCode)}</Box>;
        },
      },
    ];

    const { data, isLoading, refetch } = useQuery({
      url: `society/branch/secretary/findByParams`,
      filters: [
        {
          field: "pageSize",
          value: watch("pageSize"),
          operator: "eq",
        },
        {
          field: "pageNo",
          value: watch("pageNo"),
          operator: "eq",
        },
      ],
      autoFetch: false,
      onSuccess: (data) => {
        const AJKdata = data?.data?.data?.data || [];
        const total = data?.data?.data?.total;
        setTotal(total);
        setDisplaySenaraiAjk(AJKdata);
      },
    });

    useEffect(() => {
      const filters: CrudFilter[] = [
        {
          field: "pageSize",
          value: 10,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: 1,
          operator: "eq",
        },
      ];
      refetch({ filters });
    }, []);

    return (
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <form onSubmit={handleSubmit(handleSearch)}>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("listOfSecretaries")}
            </Typography>

            {/* state */}
            <Controller
              name="state"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="select"
                  label={t("state")}
                  options={stateOptions}
                />
              )}
            />

            {/* finding/carian */}
            <Controller
              name="searchQuery"
              control={control}
              render={({ field }) => <Input {...field} label={t("search")} />}
            />
            <Grid container mt={3} spacing={2}>
              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  // flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrevious onClick={handleClearSearch}>
                  {t("previous")}
                </ButtonPrevious>
                <ButtonPrimary type="submit">{t("search")}</ButtonPrimary>
              </Grid>
            </Grid>
          </Box>
        </form>
        {/* ============= */}
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              textAlign: "center",
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              py: 2,
            }}
          >
            <Typography variant="h5" gutterBottom>
              {total}
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
              {t("rekodDijumpai")}
            </Typography>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mt: 2,
            }}
          >
            <DataTable
              columns={columns}
              rows={displaySenaraiAjk}
              page={watch("pageNo")}
              rowsPerPage={watch("pageSize")}
              totalCount={total}
              onPageChange={handleChangePage}
              onPageSizeChange={handleChangePageSize}
              isLoading={isLoading}
              customNoDataText={t("noRecordForStatusBranch")}
            />
          </Box>
        </Box>
      </Box>
    );
  }
}
export default SijilMigrasiCawanganTab;
