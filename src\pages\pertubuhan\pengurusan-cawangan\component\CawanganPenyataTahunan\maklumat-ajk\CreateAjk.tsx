/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useMemo, useState } from "react";
import { Box, Typography, styled, Grid } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { ButtonOutline } from "@/components/button";
import { useCustom } from "@refinedev/core";
import { API_URL } from "@/api";
import Input from "@/components/input/Input";
import { Controller, useForm, FieldValues } from "react-hook-form";
import useMutation from "@/helpers/hooks/useMutation";
import { useDispatch, useSelector } from "react-redux";
import { removeAjk } from "@/redux/ajkReducer";
import {
  CitizenshipStatus,
  IdTypes,
  ListGelaran,
  ListGender,
  MALAYSIA,
  OrganisationPositions,
} from "@/helpers/enums";
import { capitalizeWords, getLocalStorage } from "@/helpers";
import dayjs from "dayjs";

interface ICommitte {
  jobCode: string;
  branchId: number;
  branchNo: string;
  titleCode: string;
  committeeName: string;
  gender: string;
  nationalityStatus: string;
  identityType: string;
  committeeIcNo: string;
  dateOfBirth: string; // Format: YYYY-MM-DD
  placeOfBirth: string;
  designationCode: string;
  otherDesignationCode: string;
  committeeEmployerAddressStatus: string;
  committeeEmployerName: string;
  committeeEmployerAddress: string;
  committeeEmployerPostcode: string;
  committeeEmployerCountryCode: string;
  committeeEmployerStateCode: string;
  committeeEmployerCity: string;
  committeeEmployerDistrict: string;
  committeeAddress: string;
  residentialPostcode: string;
  residentialAddressStatus: string;
  residentialCountryCode: string;
  committeeStateCode: string;
  committeeDistrict: string;
  committeeCity: string;
  email: string;
  telephoneNumber: string;
  phoneNumber: string;
  noTelP: string;
  status: number;
  applicationStatusCode: string;
  pegHarta: string;
  tarikhTukarSu: string; // Format: YYYY-MM-DD
  otherPosition: string;
  batalFlat: boolean;
  blacklistNotice: boolean;
  benarAjk: boolean;
  id: string;
}

export const CreateAjk: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const params = new URLSearchParams(window.location.search);
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const { id } = useParams();
  const [ajkId, setAjkId] = useState<number>();
  const occupationList = getLocalStorage("occupation_list", []);
  const value = params.get("value")
    ? JSON.parse(params.get("value") || "")
    : {};

  const { data: addressList, isLoading: isAddressLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData = addressList?.data?.data || [];
  const location = useLocation();
  const ajk: ICommitte = location.state?.ajk;

  console.log(ajk);

  const allCountryOptions = useMemo(
    () =>
      addressData
        ?.filter((i: any) => i.level === 0)
        ?.map((item: any) => ({
          value: item.id,
          label: item.name,
        }))
        ?.filter((i: any) => i.value !== MALAYSIA),
    [addressData]
  );

  const mutate = useMutation({
    url: "society/committee/create",
    onSuccess: () => {
      if (value?.no) {
        dispatch(removeAjk(value?.no));
      }
    },
  });
  const dispatch = useDispatch();

  const defaultValue = {
    jobCode: "",
    branchId: branchDataRedux.id,
    branchNo: branchDataRedux.branchNo,
    titleCode: "",
    committeeName: "",
    gender: "",
    nationalityStatus: "",
    identityType: "",
    committeeIcNo: "",
    dateOfBirth: "",
    placeOfBirth: "",
    designationCode: "",
    otherDesignationCode: "",
    committeeEmployerAddressStatus: "",
    committeeEmployerName: "",
    committeeEmployerAddress: "",
    committeeEmployerCountryCode: "",
    committeeEmployerStateCode: "",
    committeeEmployerCity: "",
    committeeEmployerDistrict: "",
    committeeAddress: "",
    residentialPostcode: "",
    residentialAddressStatus: "",
    residentialCountryCode: "",
    committeeStateCode: "",
    committeeDistrict: "",
    committeeCity: "",
    email: "",
    telephoneNumber: "",
    phoneNumber: "",
    noTelP: "",
    status: "",
    applicationStatusCode: "",
    pegHarta: "",
    tarikhTukarSu: "",
    otherPosition: "",
    batalFlat: false,
    blacklistNotice: false,
    benarAjk: true,
  };

  const form = useForm<FieldValues>({
    defaultValues: { defaultValue },
  });

  const {
    register,
    formState: { errors },
    handleSubmit,
    control,
    watch,
    setValue,
    getValues,
    reset,
  } = form;

  useEffect(() => {
    if (ajk) {
      setAjkId(parseInt(ajk.id));
      Object.entries(ajk).forEach(([key, value]) => {
        setValue(key, value);
      });
    }
  }, []);

  const view = location.state?.view;

  return (
    <Box sx={{ display: "flex", gap: 3 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 2 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#fff",
          borderRadius: "14px",
          width: "100%",
        }}
      >
        <form
          style={{
            // border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
            width: "100%",
          }}
        >
          <Box
            sx={{
              mb: 3,
              p: 3,
              borderRadius: "16px",
              border: "1px solid #D9D9D9",
            }}
          >
            <Typography
              variant="h6"
              component="h2"
              sx={{
                fontWeight: "bold",
                fontSize: 14,
                color: "var(--primary-color)",
                pl: 2,
              }}
            >
              {t("positionInfo")}
            </Typography>

            <Box sx={{ pl: 2 }}>
              <Grid container>
                <Controller
                  name="designationCode"
                  rules={{
                    required: "Medan ini diperlukan",
                  }}
                  defaultValue={parseInt(getValues("designationCode"))}
                  control={control}
                  render={({ field }) => {
                    return (
                      <Input
                        disabled
                        required
                        {...field}
                        type="select"
                        label={t("position")}
                        error={!!errors.designationCode?.message}
                        value={parseInt(getValues("designationCode"))}
                        options={OrganisationPositions.map((position) => ({
                          ...position,
                          label: t(position.label),
                        }))}
                      />
                    );
                  }}
                />
              </Grid>
            </Box>
          </Box>

          <Box
            sx={{
              p: 3,
              borderRadius: "16px",
              border: "1px solid #D9D9D9",
            }}
          >
            <Box
              sx={{
                px: 2,
                py: 1,
                mb: 3,
                borderRadius: "16px",
              }}
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  fontWeight: "bold",
                  fontSize: 14,
                  color: "var(--primary-color)",
                }}
              >
                {t("personalInfo")}
              </Typography>
            </Box>

            <Box sx={{ pl: 2 }}>
              <Controller
                control={control}
                name="identityType"
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("idType")}
                      // value={ajk.identificationType}
                      value={t(
                        `${
                          IdTypes.find(
                            (item) => item.value === ajk?.identityType
                          )?.label || ""
                        }`
                      )}
                      disabled
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="committeeIcNo"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      disabled
                      value={ajk.committeeIcNo}
                      label={t("idNumber")}
                      error={!!errors.committeeIcNo?.message}
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="titleCode"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      required
                      label={t("title")}
                      type="select"
                      options={ListGelaran}
                      value={ajk.titleCode}
                      disabled
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="committeeName"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("fullName")}
                      value={ajk.committeeName}
                      error={!!errors.committeeName?.message}
                      disabled
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="gender"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      type="select"
                      options={ListGender.map((option) => ({
                        ...option,
                        label: t(option.label),
                      }))}
                      label={t("gender")}
                      value={ajk.gender}
                      disabled
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="nationalityStatus"
                render={({ field }) => {
                  return (
                    <Input
                      disabled
                      value={
                        getValues("citizenshipStatus")
                          ? Number(getValues("citizenshipStatus"))
                          : 2
                      }
                      name="citizenshipStatus"
                      label={t("citizenship")}
                      type="select"
                      options={CitizenshipStatus.map((item) => ({
                        ...item,
                        label: t(item.label),
                      }))}
                      required
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="dateOfBirth"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      disabled
                      required
                      {...field}
                      label={t("dateOfBirth")}
                      type="date"
                      onChange={(newValue) =>
                        setValue("dateOfBirth", newValue.target.value)
                      }
                      value={
                        getValues("dateOfBirth")
                          ? dayjs(getValues("dateOfBirth")).format("DD-MM-YYYY")
                          : ""
                      }
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="placeOfBirth"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      disabled
                      label={t("placeOfBirth")}
                      value={ajk.placeOfBirth}
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="jobCode"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("occupation")}
                      type="select"
                      disabled
                      options={occupationList}
                      value={ajk.jobCode}
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="committeeAddress"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("residentialAddress")}
                      disabled
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="committeeStateCode"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("state")}
                      type="select"
                      fullWidth
                      disabled
                      options={addressData
                        ?.filter((item: any) => item.pid == MALAYSIA)
                        .map((item: any) => ({
                          label: item.name,
                          value: "" + item.id,
                        }))}
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="committeeDistrict"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("district")}
                      type="select"
                      fullWidth
                      options={addressData
                        ?.filter(
                          (item: any) =>
                            item.id == Number(ajk.committeeDistrict)
                        )
                        .map((item: any) => ({
                          label: item.name,
                          value: Number(item.id),
                        }))}
                      disabled
                      value={
                        ajk.committeeDistrict
                          ? Number(ajk.committeeDistrict)
                          : ""
                      }
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="committeeCity"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("city")}
                      disabled
                      value={ajk.committeeCity}
                    />
                  );
                }}
              />

              <Controller
                name="postcode"
                rules={{
                  required: t("idNumberRequired"),
                  maxLength: {
                    value: 5,
                    message: t("postcodeHelper"),
                  },
                  pattern: {
                    value: /^\d+$/,
                    message: t("numbersOnly"),
                  },
                }}
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled
                      required
                      {...field}
                      onChange={(e) => {
                        if (/^\d{0,5}$/.test(e.target.value)) {
                          field?.onChange(e.target.value);
                        }
                      }}
                      error={!!errors.postcode?.message}
                      label={t("postcode")}
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="email"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("email")}
                      type="email"
                      disabled
                      value={ajk.email}
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="phoneNumber"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      required
                      label={t("phoneNumber")}
                      type="number"
                      disabled
                      value={ajk.phoneNumber}
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="telephoneNumber"
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      label={t("homeNumber")}
                      type="number"
                      disabled
                      value={ajk.telephoneNumber}
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="noTelP"
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      label={t("officeNumber")}
                      type="number"
                      disabled
                      value={ajk.phoneNumber}
                    />
                  );
                }}
              />
            </Box>
          </Box>

          <Box
            sx={{
              mt: 3,
              p: 3,
              borderRadius: "16px",
              border: "1px solid #D9D9D9",
            }}
          >
            <Box
              sx={{
                display: "flex",
                // justifyContent: "space-between",
                // backgroundColor: "#e0f2f1",
                px: 2,
                py: 1,
                mb: 3,
                borderRadius: "16px",
              }}
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  fontWeight: "bold",
                  fontSize: 14,
                  color: "var(--primary-color)",
                }}
              >
                {t("employerInfo")}
              </Typography>
            </Box>

            <Box sx={{ pl: 2 }}>
              <Controller
                control={control}
                name="committeeEmployerName"
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("employerName")}
                      disabled
                      value={ajk.committeeEmployerName}
                    />
                  );
                }}
              />

              {ajk.committeeEmployerAddressStatus !== "dalam" ? (
                <Controller
                  control={control}
                  name="committeeEmployerCountryCode"
                  render={({ field }) => {
                    return (
                      <Input
                        required
                        {...field}
                        label={t("country")}
                        type={"select"}
                        options={allCountryOptions}
                        disabled
                        value={Number(ajk.committeeEmployerCountryCode)}
                      />
                    );
                  }}
                />
              ) : null}

              <Controller
                control={control}
                name="committeeEmployerAddress"
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      label={t("employerAddress")}
                      multiline
                      rows={4}
                      disabled
                      value={ajk.committeeEmployerAddress}
                    />
                  );
                }}
              />

              {ajk.committeeEmployerAddress === "dalam" && (
                <>
                  <Controller
                    control={control}
                    name="committeeEmployerStateCode"
                    render={({ field }) => {
                      return (
                        <Input
                          required
                          {...field}
                          label={t("state")}
                          type="select"
                          fullWidth
                          multiline
                          rows={3}
                          options={addressData
                            ?.filter((item: any) => item.pid == MALAYSIA)
                            .map((item: any) => ({
                              label: capitalizeWords(item.name, null, true),
                              value: item.id,
                            }))}
                          disabled
                          value={ajk.committeeEmployerStateCode}
                        />
                      );
                    }}
                  />

                  <Controller
                    control={control}
                    name="committeeEmployerDistrict"
                    render={({ field }) => {
                      return (
                        <Input
                          required
                          {...field}
                          label={t("district")}
                          type="select"
                          fullWidth
                          multiline
                          rows={3}
                          options={addressData
                            ?.filter(
                              (item: any) =>
                                item.id ==
                                Number(ajk.committeeEmployerStateCode)
                            )
                            .map((item: any) => ({
                              label: capitalizeWords(item.name, null, true),
                              value: Number(item.id),
                            }))}
                          disabled
                          value={Number(ajk.committeeEmployerDistrict)}
                        />
                      );
                    }}
                  />

                  <Controller
                    control={control}
                    name="committeeEmployerCity"
                    render={({ field }) => {
                      return (
                        <Input
                          required
                          {...field}
                          label={t("city")}
                          disabled
                          value={ajk.committeeEmployerCity}
                        />
                      );
                    }}
                  />

                  <Controller
                    control={control}
                    name="committeeEmployerPostcode"
                    rules={{
                      required: t("idNumberRequired"),
                      maxLength: {
                        value: 5,
                        message: t("postcodeHelper"),
                      },
                      pattern: {
                        value: /^\d+$/,
                        message: t("numbersOnly"),
                      },
                    }}
                    render={({ field }) => {
                      return (
                        <Input
                          required
                          {...field}
                          onChange={(e) => {
                            if (/^\d{0,5}$/.test(e.target.value)) {
                              field?.onChange(e.target.value);
                            }
                          }}
                          label={t("postcode")}
                          disabled
                          value={ajk.committeeEmployerPostcode}
                        />
                      );
                    }}
                  />
                </>
              )}
            </Box>
          </Box>

          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              mt: 2,
              gap: 2,
            }}
          >
            <ButtonOutline onClick={() => navigate(-1)}>
              {t("back")}
            </ButtonOutline>
          </Box>
        </form>
      </Box>
    </Box>
  );
};

export default CreateAjk;
