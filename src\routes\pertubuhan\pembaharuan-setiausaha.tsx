import { Outlet, Route } from "react-router-dom";
import { RouteGuard } from "../../components/RouteGuard";
import { registerRoutes } from "../../helpers/routeDetector";
import PembaharuanSetiausaha from "../../pages/pertubuhan/pengurusan-pertubuhan/pembaharuan-setiausaha";
import PembaharuanSetiausahaLayout from "../../pages/pertubuhan/pengurusan-pertubuhan/pembaharuan-setiausaha/Layout";
import Tambah from "../../pages/pertubuhan/pengurusan-pertubuhan/pembaharuan-setiausaha/Tambah";
import Papar from "../../pages/pertubuhan/pengurusan-pertubuhan/pembaharuan-setiausaha/Papar";
import MaklumatPermohonan from "../../pages/pertubuhan/pengurusan-pertubuhan/pembaharuan-setiausaha/MaklumatPermohonan";
import KeputusanJawatankuasa from "../../pages/pertubuhan/pengurusan-pertubuhan/pembaharuan-setiausaha/KeputusanJawatankuasa";
import FormPembaharuanSetiaUsaha from "@/pages/pertubuhan/pengurusan-pertubuhan/pembaharuan-setiausaha/FormPembaharuanSetiausaha";
import FeedbackPembaharuanSetiaUsaha from "@/pages/pertubuhan/pengurusan-pertubuhan/pembaharuan-setiausaha/FeedbackPembaharuanSetiausaha";

// Layout component to wrap all pembaharuan setiausaha routes with protection
const PembaharuanSetiausahaGuardedLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <PembaharuanSetiausahaLayout>
      <Outlet />
    </PembaharuanSetiausahaLayout>
  </RouteGuard>
);

// Register routes with their portal types
registerRoutes({
 "/pertubuhan/pembaharuan-setiausaha": "external",
 "/pertubuhan/pembaharuan-setiausaha/:secretaryId": "external",
 "/pertubuhan/pembaharuan-setiausaha/secretaryId/feedback": "external",
 "/pertubuhan/pembaharuan-setiausaha/tambah": "external",
 "/pertubuhan/pembaharuan-setiausaha/tambah/:societyId": "external",
 "/pertubuhan/pembaharuan-setiausaha/tambah/:societyId/:secretaryId": "external",
});

export const pembaharuanSetiausaha = {
  routes: (
    <Route element={<PembaharuanSetiausahaGuardedLayout />}>
      <Route path="pembaharuan-setiausaha">
        <Route index element={<PembaharuanSetiausaha />} />

        <Route path=":secretaryId" element={<FormPembaharuanSetiaUsaha />} />

        <Route
          path=":secretaryId/feedback"
          element={<FeedbackPembaharuanSetiaUsaha />}
        />

        <Route path="tambah">
          <Route index element={<Tambah />} />

          <Route path=":societyId/" element={<FormPembaharuanSetiaUsaha />} />
          <Route
            path=":societyId/:secretaryId"
            element={<FormPembaharuanSetiaUsaha />}
          />
        </Route>

        {/* <Route path="papar" element={<Papar />} />

        <Route
          path="maklumat-permohonan-pembaharuan-setiausaha"
          element={<MaklumatPermohonan />}
        />

        <Route
          path="keputusan-jawatankuasa"
          element={<KeputusanJawatankuasa />}
        /> */}
      </Route>
    </Route>
  ),
};
