import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Box, Checkbox, Grid, TextField, Typography } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { ButtonPrimary, ButtonText } from "../../../../components/button";
import Input from "../../../../components/input/Input";
import { useCustom } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { CitizenshipStatus } from "@/helpers";

export const Papar: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [pek<PERSON><PERSON><PERSON>, set<PERSON><PERSON><PERSON><PERSON><PERSON>] = useState("yes");
  const [employerAddress, setEmployerAddress] = useState("in");
  const [masaMula, setMasaMula] = useState("");
  const [masaTamat, setMasaTamat] = useState("");
  const [members, setMembers] = useState([
    { id: 1, name: "<PERSON>", role: "<PERSON><PERSON><PERSON><PERSON>", status: "Present" },
    { id: 2, name: "Mizz Nina", role: "Setiausaha", status: "Present" },
  ]);

  const calculateDuration = () => {
    if (masaMula && masaTamat) {
      const start = new Date(`2000-01-01T${masaMula}`);
      const end = new Date(`2000-01-01T${masaTamat}`);
      const diff = (end.getTime() - start.getTime()) / (1000 * 60 * 60);
      return diff.toFixed(2);
    }
    return "";
  };

  const { data: addressList, isLoading: isLoadingAddress } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData = addressList?.data?.data || [];

  const { data: meetingList, isLoading: isMeetingLoading } = useCustom({
    url: `${API_URL}/society/admin/meeting/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const meetingData = meetingList?.data?.data || [];

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          borderRadius: "14px",
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{
            backgroundColor: "#e0f2f1",
            px: 2,
            py: 1,
            mb: 3,
            borderRadius: "16px",
          }}
        >
          {t("personalInfoNewSec")}
        </Typography>

        <Grid container spacing={2} pl={4} pt={2}>
          <Input disabled label={t("position")} value="Setiausaha" />
          <Input disabled label={t("gelaran")} value="Dato'" />
          <Input
            disabled
            label={t("fullName")}
            value="Ahmad Daud bin Sulaiman"
          />
          <Input disabled label={t("gender")} value="Lelaki" />
          <Input
            disabled
            label={t("citizenship")}
            type="select"
            options={CitizenshipStatus.map((item) => ({
              ...item,
              label: t(item.label),
            }))}
            value={1}
          />
          <Input disabled label={t("idType")} value="MyKad" />
          <Input disabled label={t("idNumber")} value="901011101191" />
          <Input disabled label={t("dateOfBirth")} value="11/09/1990" />
          <Input
            required
            disabled
            value="Hospital Kuala Lumpur"
            label={t("placeOfBirth")}
          />
          <Input
            disabled
            required
            type="select"
            value={pekerjaan}
            onChange={(e: React.ChangeEvent<{ value: unknown }>) =>
              setPekerjaan(e.target.value as string)
            }
            options={[
              { value: "no", label: "Tidak Bekerja" },
              {
                value: "yes",
                label: "Bekerja",
              },
            ]}
            label={t("pekerjaan")}
          />
          <Input
            disabled
            value="Lot 10, Jalan Mont Kiara II"
            required
            label={t("residentialAddress")}
          />
          <Input disabled label={t("country")} value="Malaysia" />
          <Input disabled label={t("state")} value="Selangor" />
          <Input
            required
            disabled
            type="select"
            dynamicData={addressData}
            isLoadingData={isLoadingAddress}
            label={t("district")}
            placeholder={t("selectPlaceholder")}
            useDynamicData
          />
          <Input disabled label={t("city")} />
          <Input disabled value="42100" required label={t("postcode")} />
          <Input
            disabled
            value="<EMAIL>"
            required
            label={t("email")}
          />
          <Input
            disabled
            value="0127891101"
            required
            label={`${t("phoneNumber")} ${t("mobileAddition")}`}
          />
          <Input disabled label={`${t("phoneNumber")} ${t("houseAddition")}`} />
          <Input
            disabled
            label={`${t("phoneNumber")} ${t("officeAddition")}`}
          />
        </Grid>

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            backgroundColor: "#e0f2f1",
            px: 2,
            py: 1,
            mb: 3,
            mt: 6,
            borderRadius: "16px",
          }}
        >
          <Typography
            variant="h6"
            component="h2"
            sx={{ fontWeight: "bold", color: "black", fontSize: "16px" }}
          >
            {t("employerInfo")}
          </Typography>
          <Typography variant="body2" sx={{ color: "red", fontWeight: "bold" }}>
            {t("maklumatMajikanWarning")}
          </Typography>
        </Box>

        <Grid container spacing={2} pl={4} pt={2}>
          <Input disabled value="Maybank Berhad" label={t("employerName")} />
          <Input
            disabled
            type="select"
            value={employerAddress}
            onChange={(e: React.ChangeEvent<{ value: unknown }>) =>
              setEmployerAddress(e.target.value as string)
            }
            options={[
              { value: "out", label: "Luar Negara" },
              {
                value: "in",
                label: "Dalam Negara",
              },
            ]}
            label={t("employerAddress")}
          />
          <Input
            isLabel={false}
            disabled
            value="Menara Maybank, Jalan Bangsar"
            label=""
          />
          <Input
            disabled
            required
            type="select"
            options={[]}
            label={t("country")}
          />

          <Input
            disabled
            type="select"
            dynamicData={addressData}
            isLoadingData={isLoadingAddress}
            label={t("state")}
            placeholder={t("selectPlaceholder")}
            useDynamicData
          />
          <Input
            disabled
            type="select"
            dynamicData={addressData}
            isLoadingData={isLoadingAddress}
            label={t("district")}
            placeholder={t("selectPlaceholder")}
            useDynamicData
          />
          <Input disabled label={t("city")} />
          <Input disabled label={t("postcode")} />
        </Grid>

        <Typography
          variant="subtitle1"
          sx={{
            backgroundColor: "#e0f2f1",
            px: 2,
            py: 1,
            mb: 3,
            mt: 6,
            borderRadius: "16px",
          }}
        >
          {t("pelantikanSetiausaha")}
        </Typography>

        <Grid container spacing={2} pl={4} pt={2}>
          <Input
            disabled
            required
            type="select"
            dynamicData={meetingData}
            isLoadingData={isMeetingLoading}
            label={t("meetingType")}
            placeholder={t("selectPlaceholder")}
            useDynamicData
          />
          <Input
            disabled
            required
            type="select"
            dynamicData={meetingData}
            isLoadingData={isMeetingLoading}
            label={t("meetingMethod")}
            placeholder={t("selectPlaceholder")}
            useDynamicData
            pid={2}
          />
          <Input
            disabled
            required
            type="select"
            dynamicData={meetingData}
            isLoadingData={isMeetingLoading}
            label={t("platformType")}
            placeholder={t("selectPlaceholder")}
            useDynamicData
            pid={3}
          />
          <Input
            disabled
            value="Setiausaha lama tidak aktif"
            required
            label={t("sebabTukarSetiausaha")}
          />
          <Grid item xs={12} sm={4} sx={{ "&.MuiGrid-root": { p: 0 } }}>
            <Typography>
              {t("tarikhMesyuarat")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid
            item
            xs={12}
            sm={8}
            sx={{ "&.MuiGrid-root": { py: 0, pr: 0, pl: 1 } }}
          >
            <TextField
              fullWidth
              required
              disabled
              type="date"
              inputProps={{
                min: new Date().toISOString().split("T")[0],
              }}
              sx={{
                "& .MuiOutlinedInput-input": {
                  padding: "8px 14px",
                },
                background: "#E8E9E8",
              }}
            />
          </Grid>
          <Grid
            item
            xs={12}
            sm={4}
            sx={{ "&.MuiGrid-root": { p: 0 }, mt: 1, mb: 1 }}
          >
            <Typography>
              {t("masa")}
              <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid
            item
            xs={12}
            sm={8}
            sx={{ "&.MuiGrid-root": { py: 0, pr: 0, pl: 1 }, mt: 1, mb: 1 }}
          >
            <Box display="flex" alignItems="center">
              <TextField
                disabled
                value={masaMula}
                onChange={(e) => setMasaMula(e.target.value)}
                type="time"
                sx={{
                  width: "150px",
                  "& .MuiOutlinedInput-input": {
                    padding: "8px 14px",
                  },
                  background: "#E8E9E8",
                }}
              />
              <Box sx={{ mx: 1 }}>—</Box>
              <TextField
                disabled
                value={masaTamat}
                onChange={(e) => setMasaTamat(e.target.value)}
                type="time"
                sx={{
                  width: "150px",
                  "& .MuiOutlinedInput-input": {
                    padding: "8px 14px",
                  },
                  background: "#E8E9E8",
                }}
              />
              <Typography sx={{ ml: 2 }}>
                {calculateDuration() && `${calculateDuration()} ${t("jam")}`}
              </Typography>
            </Box>
          </Grid>
          <Input disabled value="KL" required label={t("tempatMesyuarat")} />
          <Grid item xs={12} sm={4} sx={{ "&.MuiGrid-root": { p: 0 }, mb: 1 }}>
            <Typography>
              {t("petaLokasi")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid
            item
            xs={12}
            sm={8}
            sx={{ "&.MuiGrid-root": { py: 0, pr: 0, pl: 1 }, mb: 1 }}
          >
            <Box>
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d721169.4567849896!2d102.08600387522937!3d3.111625028262089!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sid!2sid!4v1727317814713!5m2!1sid!2sid"
                width="100%"
                height="250"
                loading="lazy"
              ></iframe>
            </Box>
          </Grid>
          <Input disabled required label={t("alamatTempatMesyuarat")} />
          <Input
            disabled
            required
            type="select"
            dynamicData={addressData}
            isLoadingData={isLoadingAddress}
            label={t("state")}
            placeholder={t("selectPlaceholder")}
            useDynamicData
          />
          <Input
            disabled
            required
            type="select"
            dynamicData={addressData}
            isLoadingData={isLoadingAddress}
            label={t("district")}
            placeholder={t("selectPlaceholder")}
            useDynamicData
          />
          <Input disabled label={t("city")} />
          <Input disabled required label={t("postcode")} />
          <Input
            disabled
            label={t("jumlahKehadiranAhliMesyuarat")}
            type="number"
          />
          <Input
            disabled
            type="select"
            value={employerAddress}
            onChange={(e: React.ChangeEvent<{ value: unknown }>) =>
              setEmployerAddress(e.target.value as string)
            }
            options={[
              { value: "no", label: "Tidak" },
              {
                value: "yes",
                label: "Ya",
              },
            ]}
            label={t("isiMaklumatMesyuarat")}
          />
          <Grid item xs={12} sm={4} sx={{ "&.MuiGrid-root": { p: 0 }, mb: 1 }}>
            <Typography>{t("senaraiAJKYangHadir")}</Typography>
          </Grid>

          {/* Members */}
          <Grid
            item
            xs={12}
            sm={8}
            sx={{ "&.MuiGrid-root": { py: 0, pr: 0, pl: 1 }, mb: 1 }}
          >
            {members.map((member) => (
              <Box key={member.id}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Typography
                    sx={{ color: "#7C7E9D" }}
                  >{`${member.id}. ${member.name}`}</Typography>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 12,
                    }}
                  >
                    <Typography sx={{ color: "#7C7E9D" }}>
                      {member.role}
                    </Typography>
                  </Box>
                </Box>
                <hr />
              </Box>
            ))}
          </Grid>
          <Input
            disabled
            label={t("ucapanAluanPengerusi")}
            multiline
            rows={3}
          />
          <Input disabled label={t("perkaraPerkara")} multiline rows={3} />
          <Input disabled label={t("halHalLain")} multiline rows={3} />
          <Input disabled label={t("penutup")} multiline rows={3} />
          <Input disabled label={t("disediakanOlehSetiausaha")} />
          <Input disabled label={t("disahkanOlehPengerusi")} />
          <Grid item xs={12} sm={4} sx={{ "&.MuiGrid-root": { p: 0 }, mb: 1 }}>
            <Typography>
              {t("minitMesyuarat")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid
            item
            xs={12}
            sm={8}
            sx={{
              "&.MuiGrid-root": { py: 0, pr: 0, pl: 1 },
              mb: 1,
              display: "flex",
              gap: 8,
            }}
          >
            <ButtonText>{t("downloadTemplate")}</ButtonText>
            <ButtonText>{t("papar")}</ButtonText>
          </Grid>
        </Grid>

        <Box sx={{ display: "flex", justifyContent: "flex-end", px: 1, mt: 5 }}>
          <ButtonPrimary>{t("janaMinit")}</ButtonPrimary>
        </Box>

        <Typography
          variant="subtitle1"
          sx={{
            backgroundColor: "#e0f2f1",
            px: 2,
            py: 1,
            mb: 3,
            mt: 6,
            borderRadius: "16px",
          }}
        >
          {t("personalInfoOldSecretary")}
        </Typography>

        <Grid container spacing={2} pl={4} pt={2}>
          <Input disabled required label={t("name")} />
          <Input disabled required label={t("idNumber")} />

          <Box sx={{ mt: 5, fontSize: "14px" }}>
            <Checkbox checked disabled sx={{ p: 0, mr: 1 }} />
            {t("confirmOldSecretary")}
          </Box>
        </Grid>
      </Box>

      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          mt: 3,
        }}
      >
        <ButtonPrimary onClick={() => navigate(-1)}>{t("back")}</ButtonPrimary>
      </Box>
    </>
  );
};

export default Papar;
