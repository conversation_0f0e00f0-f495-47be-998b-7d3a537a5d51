import { use<PERSON>allback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Box, Typography, debounce, IconButton } from "@mui/material";
import { API_URL } from "../../../api";
import { NEW_PermissionNames } from "../../../helpers/enums";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { t } from "i18next";
import { getLocalStorage } from "../../../helpers/utils";
import { EditIcon } from "../../../components/icons";
import DataTable, { IColumn } from "../../../components/datatable";
import { CrudFilter } from "@refinedev/core";
import useQuery from "../../../helpers/hooks/useQuery";
import { ExternalIcon } from "@/components/icons/external";
import { pageAccessEnum } from "@/helpers";
import { useForm, FieldValues } from "react-hook-form";
import {
  Dialog<PERSON>onfirmation,
  FormFieldRow,
  Label,
  SelectFieldController,
  TextFieldController,
} from "@/components";
import AuthHelper from "@/helpers/authHelper";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "30px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

type Props = {
  number?: number;
};

const KeputusanIndukKelulusan = ({ number }: Props) => {
  const navigate = useNavigate();

  const categories = getLocalStorage("category_list", []);
  const mainCategories = categories.filter((cat: any) => cat.level === 1);

  const subCategories = categories.filter((cat: any) => cat.level === 2);

  const [isOpenDialog, setIsOpenDialog] = useState(false);
  const [currentSelectedSocietyData, setCurrentSelectedSocietyData] =
    useState<any>(null);

  const mainCategoriesOptions = mainCategories?.map((category: any) => ({
    value: category.id.toString(),
    label: category.categoryNameEn,
  }));

  // {
  //   subCategories
  //     .filter(
  //       (subCat: any) => subCat.pid === parseInt(formData.organizationCategory)
  //     )
  //     .map((category: any) => (
  //       <MenuItem key={category.id} value={category.id.toString()}>
  //         {category.categoryNameEn}
  //       </MenuItem>
  //     ));
  // }

  const { getValues, setValue, watch, control } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      searchQuery: undefined,
      isQuery: 0,
      organizationCategory: "",
      subOrganizationCategory: "",
    },
  });

  const subCategoriesOptions = subCategories
    ?.filter((items: any) => {
      return items.pid === parseInt(watch("organizationCategory"));
    })
    .map((category: any) => ({
      value: category.id.toString(),
      label: category.categoryNameEn,
    }));

  const hasReadPermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_INDUK.children.PENDAFTARAN_PERTUBUHAN_INDUK.label,
    pageAccessEnum.Read
  );
  const hasUpdatePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_INDUK.children.PENDAFTARAN_PERTUBUHAN_INDUK.label,
    pageAccessEnum.Update
  );

  const page = watch("page");
  const pageSize = watch("pageSize");
  const { mutate: markSocietyAsRead } = useCreate();

  const handleEdit = (selectedSocietyId: any, read: boolean) => {
    const id = btoa(selectedSocietyId);
    if (selectedSocietyId && !read) {
      markSocietyAsRead(
        {
          resource: `society/${selectedSocietyId}/edit`,
          values: {
            approvalReviewed: true,
          },
          meta: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
            method: "put",
          },
          successNotification: false,
        },
        {
          onSuccess: () => {
            navigate(
              `/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/kelulusan/${id}`
            );
          },
        }
      );
    } else if (selectedSocietyId) {
      navigate(
        `/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/kelulusan/${id}`
      );
    }
  };

  const columns: IColumn[] = [
    {
      field: "applicationNo",
      headerName: "No. PPM/NSID",
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return (
          <Typography
            sx={{
              fontWeight: !row.approvalReviewed
                ? "700!important"
                : "400!important",
            }}
          >
            {row?.applicationNo ? row?.applicationNo : row?.societyNo ?? "-"}
          </Typography>
        );
      },
    },
    {
      field: "societyName",
      headerName: t("namaPertubuhan"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return (
          <Typography
            sx={{
              fontWeight: !row.approvalReviewed
                ? "700!important"
                : "400!important",
            }}
          >
            {row?.societyName ?? "-"}
          </Typography>
        );
      },
    },
    {
      field: "tarikhPermohonan",
      headerName: t("tarikhPermohonan"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return (
          <Typography
            sx={{
              fontWeight: !row.approvalReviewed
                ? "700!important"
                : "400!important",
            }}
          >
            {row?.registeredDate ? row?.registeredDate : "-"}
          </Typography>
        );
      },
    },
    {
      field: "paymentDate",
      headerName: t("paymentDate"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return (
          <Typography
            sx={{
              fontWeight: !row.approvalReviewed
                ? "700!important"
                : "400!important",
            }}
          >
            {row?.paymentDate ? row?.paymentDate : "-"}
          </Typography>
        );
      },
    },
    {
      field: "roName",
      headerName: "RO",
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return (
          <Typography
            sx={{
              fontWeight: !row.approvalReviewed
                ? "700!important"
                : "400!important",
            }}
          >
            {row?.roName ?? "-"}
          </Typography>
        );
      },
    },
    {
      field: "stateName",
      headerName: t("negeri"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return (
          <Typography
            sx={{
              fontWeight: !row.approvalReviewed
                ? "700!important"
                : "400!important",
            }}
          >
            {row?.stateName ?? "-"}
          </Typography>
        );
      },
    },
    {
      field: "actions",
      headerName: t("action"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const row = params.row;
        return (
          <Box sx={{ display: "flex", justifyContent: "center" }}>
            <IconButton
              onClick={() => {
                handleEdit(row.id, row.approvalReviewed);
              }}
              sx={{ color: "black", minWidth: 0, p: 0.5 }}
              disabled={!hasReadPermission}
            >
              <EditIcon
                sx={{
                  color: hasReadPermission
                    ? "var(--primary-color)"
                    : "var(--text-grey-disabled)",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>
            <IconButton
              onClick={() => handlePopupAddExternal(row)}
              disabled={!hasUpdatePermission}
            >
              <ExternalIcon
                sx={{
                  color: hasReadPermission
                    ? "var(--primary-color)"
                    : "var(--text-grey-disabled)",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  const {
    data: searchResult,
    isLoading: isSearchLoading,
    refetch: fetchPendingResult,
  } = useQuery({
    url: `society/roDecision/getAllPending/society/registration`,
    autoFetch: false,
  });

  let dataRo = [];
  if (!isSearchLoading) {
    dataRo = searchResult?.data?.data?.data || [];
  }

  const handleChangePage = (newPage: number) => {
    const filters: CrudFilter[] = [
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: newPage, operator: "eq" },
      {
        field: "isQuery",
        value: 0,
        operator: "eq",
      },
    ];

    if (watch("societyName")) {
      filters.push({
        field: "societyName",
        value: watch("societyName"),
        operator: "eq",
      });
    }

    setValue("page", newPage);
    fetchPendingResult({ filters });
  };

  const handleChangePageSize = (newPage: number) => {
    const filters: CrudFilter[] = [
      { field: "pageSize", value: newPage, operator: "eq" },
      { field: "pageNo", value: 1, operator: "eq" },
      {
        field: "isQuery",
        value: 0,
        operator: "eq",
      },
    ];

    if (watch("societyName")) {
      filters.push({
        field: "societyName",
        value: watch("societyName"),
        operator: "eq",
      });
    }

    setValue("page", 1);
    setValue("pageSize", newPage);
    fetchPendingResult({ filters });
  };

  const handleSearchSocietyName = useCallback(
    debounce((e) => {
      const value = e.target.value.trim();

      const filters: CrudFilter[] = [
        { field: "pageSize", value: pageSize, operator: "eq" },
        { field: "pageNo", value: 1, operator: "eq" },
        { field: "isQuery", value: 0, operator: "eq" },
      ];

      if (value) filters.push({ field: "societyName", value, operator: "eq" });

      fetchPendingResult({ filters });
    }, 2000),
    [pageSize, fetchPendingResult]
  );

  const totalList = searchResult?.data?.data?.total ?? 0;

  const {
    mutate: statusExternalAgency,
    isLoading: isLoadingStatusExternalAgency,
  } = useCustomMutation();

  const {
    mutate: createExternalAgencyReview,
    isLoading: isLoadingcreateExternalAgencyReview,
  } = useCustomMutation();

  const changeStatusAgency = (id: string | number | null) => {
    if (!id) {
      return;
    }
    statusExternalAgency(
      {
        url: `${API_URL}/society/roDecision/external-agency-review`,
        method: "patch",
        values: { societyId: id },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        errorNotification: (data) => {
          return {
            message: t("messageKeputusanPermohonanError"),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          setIsOpenDialog(false);
          createExternalAgency(id);
        },
      }
    );
  };

  const createExternalAgency = (id: string | number) => {
    createExternalAgencyReview(
      {
        url: `${API_URL}/society/internal/externalAgencyReview/create`,
        method: "post",
        values: { societyId: id },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: t("messageKeputusanPermohonanError"),
            type: "error",
          };
        },
      },
      {
        onSuccess(data, variables, context) {
          fetchPendingResult({
            filters: [
              {
                field: "pageSize",
                value: pageSize,
                operator: "eq",
              },
              {
                field: "pageNo",
                value: 1,
                operator: "eq",
              },
              {
                field: "isQuery",
                value: 0,
                operator: "eq",
              },
            ],
          });
        },
      }
    );
  };

  const addToExternalHandler = () => {
    changeStatusAgency(currentSelectedSocietyData?.id);
  };

  const handlePopupAddExternal = (data: any) => {
    setCurrentSelectedSocietyData(data);
    setIsOpenDialog(true);
  };

  const DIALOGTEXT = `Adakah anda pasti untuk menunggu ulasan agensi luar bagi ${currentSelectedSocietyData?.societyName} ${currentSelectedSocietyData?.applicationNo} ?`;

  useEffect(() => {
    fetchPendingResult({
      filters: [
        {
          field: "pageSize",
          value: 10,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: 1,
          operator: "eq",
        },
        {
          field: "isQuery",
          value: 0,
          operator: "eq",
        },
        {
          field: "organizationCategory",
          value: watch("organizationCategory"),
          operator: "eq",
        },
        {
          field: "subOrganizationCategory",
          value: watch("subOrganizationCategory"),
          operator: "eq",
        },
      ],
    });
  }, [watch("organizationCategory"), watch("subOrganizationCategory")]);

  return (
    <>
      <Box>
        <Box
          sx={{
            padding: "22px 16px",
            background: "#FFF",
            borderRadius: "15px",
            boxShadow: "0px 12px 12px 0px #EAE8E866",
            marginBottom: 1,
          }}
        >
          <Box
            sx={{
              width: "100%",
              border: "0.5px solid #DADADA",
              borderRadius: "10px",
              padding: "22px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("approval_waiting_wlist")}
            </Typography>
            <FormFieldRow
              label={<Label text={t("organization_category")} />}
              value={
                <SelectFieldController
                  name="organizationCategory"
                  control={control}
                  options={mainCategoriesOptions}
                  placeholder={t("selectPlaceholder")}
                  onChange={(e) => setValue("subOrganizationCategory", "")}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("organizationSubCategory2")} />}
              value={
                <SelectFieldController
                  name="subOrganizationCategory"
                  control={control}
                  options={subCategoriesOptions}
                  placeholder={t("selectPlaceholder")}
                  disabled={
                    !watch("organizationCategory") ||
                    subCategoriesOptions.length === 0
                  }
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("namaPertubuhan")} />}
              value={
                <TextFieldController
                  name="societyName"
                  control={control}
                  onChange={handleSearchSocietyName}
                />
              }
            />
          </Box>
        </Box>
        {/* ============= */}
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              textAlign: "center",
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              py: 2,
            }}
          >
            <Typography variant="h5" gutterBottom>
              {totalList}
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
              {t("associationAwaitingApproval")}
            </Typography>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mt: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("approval_waiting_wlist")}
            </Typography>

            <DataTable
              columns={columns}
              rows={dataRo}
              page={page}
              rowsPerPage={pageSize}
              totalCount={totalList}
              isLoading={isSearchLoading}
              onPageChange={handleChangePage}
              onPageSizeChange={handleChangePageSize}
            />
          </Box>
        </Box>
      </Box>

      <DialogConfirmation
        open={isOpenDialog}
        onClose={() => {
          setIsOpenDialog(false);
          setCurrentSelectedSocietyData(null);
        }}
        onAction={addToExternalHandler}
        isMutating={isLoadingStatusExternalAgency}
        onConfirmationText={DIALOGTEXT}
      />
    </>
  );
};

export default KeputusanIndukKelulusan;
