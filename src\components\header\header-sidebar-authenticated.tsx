import React, { useState, useEffect, PropsWithChildren, useMemo } from "react";
import { Link as RouterLink, useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  useCustomMutation,
  useGetIdentity,
  useLogout,
  useWarnAboutChange,
} from "@refinedev/core";
import useQuery from "../../helpers/hooks/useQuery";
import { useUserRedux } from "@/helpers/hooks";
import { convertDate } from "../../helpers/utils";

import {
  Box,
  Typography,
  MenuItem,
  IconButton,
  Menu,
  Avatar,
  Drawer,
  List,
  ListItem,
  ListItemText,
  Button,
  Collapse,
  Select,
  Popover,
  CircularProgress,
  Badge,
} from "@mui/material";
// import { Switch } from "../switch";
import { ComingSoon } from "./comingSoonAuth";
import LogoutIcon from "@mui/icons-material/Logout";
import PersonOutlineIcon from "@mui/icons-material/PersonOutline";
// import MenuBookIcon from "@mui/icons-material/MenuBook";
import MenuOpenIcon from "@mui/icons-material/MenuOpen";
import NotificationsIcon from "@mui/icons-material/Notifications";
import DoneIcon from "@mui/icons-material/Done";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import InboxIcon from "@mui/icons-material/Inbox";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import { GeranIcon } from "../icons";
import { API_URL } from "../../api";
import { PORTAL_INTERNAL, PORTAL_EXTERNAL, USER_DETAILS_KEY } from "@/helpers";
import {
  MENU_INTERNAL,
  MENU_ORGANIZATION,
  NavItem,
} from "@/helpers/menuConfig";
import AuthHelper from "@/helpers/authHelper";
import { NotificationColors } from "@/helpers";
import { FieldValues, useForm } from "react-hook-form";
import { MoreHoriz } from "@mui/icons-material";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { Notification } from "@/pages/dashboard/notification/MainNotificationPage";
import { useDispatch } from "react-redux";

import {
  logoutRedux,
  setUserDataRedux,
  setUserPortalRedux,
} from "@/redux/userReducer";

import { setGroupedNotifications } from "@/redux/notificationReducer";
import ERosesLogoWithName from "../eroses-log-with-name";
import { ArrowBackground3SVG } from "../icons/arrowBackground3";

export type IIdentity = {
  id: number;
  name: string;
  email: string;
  userGroup: number;
  identificationNo: string;
};

export type Permission = {
  createdDate: string;
  createdBy: number;
  modifiedDate: string;
  modifiedBy: number;
  id: number;
  pid: number;
  name?: string;
  userRole: string;
  description: string;
  status: number;
};

export type PermissionsResponse = {
  data: Permission[];
};

dayjs.extend(relativeTime);

export const HeaderSidebarAuthenticated: React.FC<PropsWithChildren> = ({
  children,
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { mutate: logout } = useLogout();
  const { data: user } = useGetIdentity<IIdentity>();
  const { warnWhen, setWarnWhen } = useWarnAboutChange();
  const dispatch = useDispatch();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(
    null
  );

  const [showComingSoon, setShowComingSoon] = useState(false);
  const [group, setGroup] = useState<number[] | null>(null);

  const isMalay = ["bm", "my"].includes(i18n.language);
  const localeAnnoDomini = `${isMalay ? "ms" : "en"}-MY`;
  const localeHijri = `${localeAnnoDomini}-u-ca-islamic`;
  const userDetail = localStorage.getItem("user-details");
  const identificationNo = userDetail
    ? JSON.parse(userDetail).identificationNo
    : "";
  const userName = userDetail ? JSON.parse(userDetail).name : "";

  // MyDigitalId logout API mutation
  const { mutateAsync: callMyDigitalIdLogout } = useCustomMutation();

  const executeLogout = async () => {
    // First call MyDigitalId logout API
    const token = localStorage.getItem("refine-auth");

    //resetting the popup view history
    sessionStorage.setItem("seenPopup", "false");

    if (token) {
      try {
        const data = await callMyDigitalIdLogout({
          url: `${API_URL}/user/auth/myDigitalId/logout`,
          method: "post",
          values: {},
          config: {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
              portal: localStorage.getItem("portal") || "",
            },
          },
          successNotification: false,
          errorNotification: false,
        });

        // Check if response contains logoutUrl
        const logoutUrl = data?.data?.data?.logoutUrl;

        if (logoutUrl) {
          // First do the local cleanup (localStorage, Redux, etc.)
          proceedWithLogout();
          // Clear all cookies
          clearCookies();
          // Then redirect to the provided logout URL
          window.location.href = logoutUrl;
        } else {
          // Proceed with normal logout after successful API call
          proceedWithLogout();
          // Clear all cookies
          clearCookies();
        }
      } catch (error) {
        // Even if MyDigitalId logout fails, proceed with normal logout
        console.warn("MyDigitalId logout API failed:", error);
        proceedWithLogout();
        // Clear all cookies
        clearCookies();
      }
    } else {
      // No token available, proceed with normal logout
      proceedWithLogout();
      // Clear all cookies
      clearCookies();
    }
  };

  const clearCookies = () => {
    // Get all cookies and clear them
    document.cookie.split(";").forEach((cookie) => {
      const eqPos = cookie.indexOf("=");
      const name =
        eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();
      // Clear cookie for current domain
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
      // Clear cookie for parent domain (if subdomain)
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
      // Clear cookie for root domain
      const rootDomain = window.location.hostname
        .split(".")
        .slice(-2)
        .join(".");
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${rootDomain}`;
    });
  };

  const proceedWithLogout = () => {
    dispatch(logoutRedux());
    logout();
    localStorage.setItem("isLogout", "1");
    localStorage.setItem("tmp-identificationNo", identificationNo);
  };
  const handleLogout = () => {
    if (warnWhen) {
      const confirm = window.confirm(
        t(
          "warnWhenUnsavedChanges",
          "Are you sure you want to leave? You have unsaved changes."
        )
      );

      if (confirm) {
        setWarnWhen(false);
        executeLogout();
      }
    } else {
      executeLogout();
    }
  };

  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
    handleMenuClose();
  };

  const handleMenuClick = (item: NavItem, index: number) => {
    if (item.action) {
      item.action();
    } else {
      navigate(item.path);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
    setOpenDropdownIndex(null);
  };

  const administrationMenuItems: NavItem[] =
    localStorage.getItem("portal") === PORTAL_INTERNAL
      ? MENU_INTERNAL(setShowComingSoon)
      : MENU_ORGANIZATION(setShowComingSoon);

  const tetapanMenuItems: NavItem[] = [
    {
      id: "profile",
      label: t("profile"),
      path: "/profile",
      icon: (active: boolean) => (
        <AccountCircleIcon sx={{ color: active ? "#fff" : "#55556D" }} />
      ),
      permissions: [],
      keyword: ["/profile"],
    },
    {
      id: "profile",
      label: t("takwim"),
      path: "/takwim",
      icon: (active: boolean) => (
        <CalendarMonthIcon sx={{ color: active ? "#fff" : "#55556D" }} />
      ),
      permissions: [],
      keyword: ["/takwim"],
    },
    {
      id: "geran",
      label: t("geran"),
      path:
        localStorage.getItem("portal") === PORTAL_EXTERNAL
          ? "/geran-external"
          : "/geran-internal",
      icon: (active: boolean) => (
        <GeranIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "24px",
            height: "24px",
          }}
        />
      ),
      permissions: [],
      keyword: ["/geran"],
      // action: () => setShowComingSoon(true),
    },
    {
      id: "pembubaran",
      label: t("petiMasuk"),
      path: "pengurus-pertubuhan/pembubaran",
      icon: (active: boolean) => (
        <InboxIcon sx={{ color: active ? "#fff" : "#55556D" }} />
      ),
      // action: () => setShowComingSoon(true),
      permissions: [],
    },
    {
      id: "keluar",
      label: t("keluar"),
      path: "#",
      icon: (active: boolean) => (
        <LogoutIcon sx={{ color: active ? "#fff" : "#55556D" }} />
      ),
      action: () => handleLogout(),
      permissions: [],
    },
  ];

  useQuery({
    url: "user/auth/me/group",
    onSuccess: (data) => {
      const groupData = data?.data?.data || [];

      setGroup(groupData);
    },
  });

  useUserRedux();

  const drawer = (
    <Box
      sx={{
        textAlign: "center",
      }}
    >
      <List>
        {administrationMenuItems.map((item, index) => {
          const isDropdownOpen = openDropdownIndex === index;

          return (
            <React.Fragment key={item.label}>
              {item.subItems ? (
                <ListItem
                  button
                  onClick={() =>
                    setOpenDropdownIndex(
                      openDropdownIndex === index ? null : index
                    )
                  }
                >
                  <ListItemText primary={t(item.label)} />
                  <ArrowDropDownIcon />
                </ListItem>
              ) : (
                <ListItem
                  onClick={handleDrawerToggle}
                  button
                  component={RouterLink}
                  to={item.path}
                >
                  <ListItemText primary={t(item.label)} />
                </ListItem>
              )}

              {item.subItems && (
                <Collapse in={isDropdownOpen} timeout="auto" unmountOnExit>
                  <Box
                    sx={{
                      paddingLeft: 3,
                      display: "flex",
                      flexDirection: "column",
                      gap: 2,
                      marginTop: 1,
                    }}
                  >
                    {item.subItems.map((subItem) => (
                      <Box
                        key={subItem.label}
                        onClick={() => {
                          navigate(subItem.path);
                          handleDrawerToggle();
                        }}
                        sx={{
                          textAlign: "left",
                          fontSize: "14px",
                        }}
                      >
                        {subItem.label}
                      </Box>
                    ))}
                  </Box>
                </Collapse>
              )}
            </React.Fragment>
          );
        })}
        <ListItem>
          <Button
            onClick={() => changeLanguage(i18n.language === "my" ? "en" : "my")}
            fullWidth
          >
            {i18n.language === "my" ? "English" : "Bahasa Melayu"}
          </Button>
        </ListItem>
      </List>
    </Box>
  );

  const handleComingSoonBack = () => {
    setShowComingSoon(false);
  };

  const {
    data: userData,
    isLoading: isLoadingUserData,
    refetch: fetchUserData,
  } = useQuery({
    url: "user/auth/getCurrentUserDetailsByCriteria",
    autoFetch: false,
    onSuccess: (data) => {
      const response = data?.data?.data;
      localStorage.setItem(USER_DETAILS_KEY, JSON.stringify(response));
      dispatch(setUserDataRedux(response));
      handleMenuClose();
    },
  });

  const handleSwitchGroup = (type: string) => {
    const portalValue = type === "internal-user" ? "2" : "1";

    // Update portal in localStorage and Redux
    dispatch(setUserPortalRedux(parseInt(portalValue)));
    localStorage.setItem("portal", portalValue);

    // Fetch user data for the new portal
    fetchUserData({
      filters: [
        {
          field: "identificationNo",
          value: identificationNo,
          operator: "eq",
        },
        {
          field: "userGroup",
          value: portalValue,
          operator: "eq",
        },
      ],
    });

    // Navigate to the appropriate default route for the selected portal
    // Let RouteGuard handle any portal updates based on the route
    if (type === "internal-user") {
      navigate("/internal-user");
    } else {
      navigate("/pertubuhan");
    }
  };

  const currentPort = localStorage.getItem("portal");

  const [allowedMenuItems, setAllowedMenuItems] = useState<any>(
    administrationMenuItems
  );

  const stableAdministrationMenuItems = useMemo(
    () => administrationMenuItems,
    [administrationMenuItems]
  );

  useEffect(() => {
    if (localStorage.getItem("portal") === PORTAL_INTERNAL) {
      const finalMenuItems = stableAdministrationMenuItems.filter((item) => {
        if (item.permissions.length == 0) {
          // If the permissions (length = 0), it means no permission is required.
          return true;
        } else {
          return AuthHelper.hasAuthority(item.permissions);
        }
      });

      if (JSON.stringify(finalMenuItems) !== JSON.stringify(allowedMenuItems)) {
        setAllowedMenuItems(finalMenuItems);
      }
    } else if (
      JSON.stringify(stableAdministrationMenuItems) !==
      JSON.stringify(allowedMenuItems)
    ) {
      setAllowedMenuItems(stableAdministrationMenuItems);
    }
  }, [stableAdministrationMenuItems]);

  useEffect(() => {
    const normalizePath = (path: string) => {
      return path.startsWith("/") ? path : `/${path}`;
    };

    // const isPathMatch = (menuPath: string, currentPath: string) => {
    //   const normalizedMenuPath = normalizePath(menuPath).replace("{:id}", ".*");
    //   const regex = new RegExp(`^${normalizedMenuPath}$`);
    //   return regex.test(normalizePath(currentPath));
    // };

    const currentPath = location.pathname;

    const allMenuItems = [
      ...allowedMenuItems.map((item: any) => ({
        ...item,
        menuType: "administration",
      })),
      ...tetapanMenuItems.map((item) => ({
        ...item,
        menuType: "tetapan",
      })),
    ];

    const currentMenuItem = allMenuItems.find(
      (item) =>
        normalizePath(item.path) === currentPath ||
        item.subItems?.some(
          (subItem: any) => normalizePath(subItem.path) === currentPath
        ) ||
        item?.keyword?.some((keyword: string) => currentPath.includes(keyword))
    );

    if (currentMenuItem) {
      const index = allMenuItems.indexOf(currentMenuItem);

      setActiveTab(index);
    } else {
      setActiveTab(0);
    }
  }, [location, allowedMenuItems, tetapanMenuItems]);

  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [anchorNoti, setAnchorNoti] = useState<null | HTMLElement>(null);
  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
    },
  });
  const page = watch("page");
  const pageSize = watch("pageSize");

  const {
    data: notificationData,
    isLoading: isLoadingNotificationData,
    refetch,
  } = useQuery({
    url: "notification/getAll/me",
    filters: [
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: page, operator: "eq" },
    ],
  });

  useEffect(() => {
    if (localStorage.getItem("portal")) {
      refetch();
    }
  }, [localStorage.getItem("portal")]);

  const {
    data: unseenCount,
    isLoading: isLoadingUnseenCount,
    refetch: refetchUnseenCount,
  } = useQuery({
    url: "notification/getAllUnseenCount",
  });

  const notifications: Notification[] =
    notificationData?.data?.data?.data || [];

  const unreadCount = unseenCount?.data?.data || [];

  const mainGroupedNotifications = notifications.reduce(
    (acc: Record<string, Notification[]>, noti: Notification) => {
      const createdDate = dayjs(noti?.createdDate, "DD-MM-YYYY HH:mm:ss");
      const today = dayjs().startOf("day");
      const yesterday = dayjs().subtract(1, "day").startOf("day");
      const sevenDaysAgo = dayjs().subtract(7, "day").startOf("day");
      const oneMonthAgo = dayjs().subtract(1, "month").startOf("day");

      if (createdDate.isSame(today, "day")) {
        acc["hariIni"] = [...(acc["hariIni"] || []), noti];
      } else if (createdDate.isSame(yesterday, "day")) {
        acc["semalam"] = [...(acc["semalam"] || []), noti];
      } else if (createdDate.isAfter(sevenDaysAgo)) {
        acc["7HariTerakhir"] = [...(acc["7HariTerakhir"] || []), noti];
      } else if (createdDate.isAfter(oneMonthAgo)) {
        acc["sebulanTerakhir"] = [...(acc["sebulanTerakhir"] || []), noti];
      } else {
        acc["sebelumnya"] = [...(acc["sebelumnya"] || []), noti];
      }

      return acc;
    },
    {}
  );

  useEffect(() => {
    if (mainGroupedNotifications) {
      dispatch(setGroupedNotifications(mainGroupedNotifications));
    }
  }, [mainGroupedNotifications, dispatch]);

  const getAvatarColor = (letter: string) => {
    const index =
      letter.toUpperCase().charCodeAt(0) % NotificationColors.length;
    return NotificationColors[index];
  };

  const handleGoToNotifications = () => {
    setAnchorNoti(null);
    navigate("/notifikasi");
  };

  const handleMarkAsRead = () => {
    const notificationTrackerIds = Array.from(notifications.values()).map(
      (noti) => noti?.notificationTrackerId
    );
    MarkLoadedAsRead(notificationTrackerIds);
  };

  const handleNotificationOpen = (e: any) => {
    refetchUnseenCount();
    refetch();
    setAnchorNoti(anchorNoti ? null : e.currentTarget);
  };

  const { mutate: markLoadedAsRead, isLoading: isLoadingMarkLoadedAsRead } =
    useCustomMutation();

  const MarkLoadedAsRead: (filteredData: any) => void = (filteredData) => {
    markLoadedAsRead(
      {
        url: `${API_URL}/notification/markAsSeen`,
        method: "put",
        values: {
          notificationTrackerIds: filteredData,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          setMenuAnchorEl(null);
          setValue("page", 1);
          setGroupedNotifications({});
          refetchUnseenCount();
          refetch();
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  return (
    <>
      {showComingSoon ? (
        <ComingSoon onBack={handleComingSoonBack} />
      ) : (
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            gap: "41px",
            minHeight: "100vh",
            padding: {
              xs: "15px",
              md: "30px",
            },
          }}
        >
          {/* SIDEBAR */}
          <Box
            sx={{
              overflow: "hidden",
              flexShrink: 0,
              height: "calc(100vh - 60px)",
              bgcolor: "#ffffff",
              boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
              borderRadius: "25px",
              padding: "37px 27px",
              position: "sticky",
              top: "30px",
              display: { xs: "none", md: "block" },
            }}
          >
            <Box
              sx={{
                display: "flex",
                width: "fit-content",
                marginInline: "auto",
                alignItems: "center",
                gap: "8px",
                marginBottom: 2,
              }}
            >
              {/* <img
                src="/logo.png"
                alt="eRoses Logo"
                style={{ width: "35px" }}
              />
              <Typography
                variant="h6"
                sx={{ color: "#000", fontWeight: "bold", fontSize: "16px" }}
              >
                {t("eRoses")}
              </Typography> */}
              <Box
                sx={{
                  display: "flex",
                  justifyContent: { xs: "center", md: "flex-start" },
                }}
              >
                <ERosesLogoWithName textColor="#666666" />
              </Box>
            </Box>

            <Box
              sx={{
                display: { xs: "none", md: "flex" },
                flexDirection: "column",
                marginBottom: "47px",
                height: "calc(100% - (2px* 2) - 35px - 47px)",
                overflowY: "scroll",
                scrollbarWidth: "none",
              }}
            >
              <Typography
                fontSize="8px"
                color={"#55556DB2"}
                fontWeight={600}
                marginBottom="3px"
                marginLeft={2}
              ></Typography>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "10px",
                  marginBottom: "42px",
                }}
              >
                {allowedMenuItems.map((item: any, index: number) => {
                  const isActive = activeTab === index;
                  const isDropdownOpen = openDropdownIndex === index;

                  return (
                    <React.Fragment key={item.label}>
                      <Box
                        onClick={() => handleMenuClick(item, index)}
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "flex-start",
                          gap: "10px",
                          padding: "11px 19px",
                          borderRadius: "20px",
                          backgroundColor: isActive
                            ? "var(--primary-color)"
                            : "transparent",
                          color: isActive ? "#fff" : "#55556D",
                          fontWeight: 400,
                          fontSize: "14px",
                          cursor: "pointer",
                        }}
                      >
                        <Typography
                          sx={{
                            display: "flex",
                            gap: 1,
                            fontSize: 14,
                            justifyContent: "center",
                            alignItems: "center",
                            textShadow: isActive
                              ? "2px 2px 4px rgba(0, 0, 0, 0.2)"
                              : "none",
                          }}
                        >
                          {item.icon && item.icon(isActive)}
                          {item.label}
                        </Typography>
                      </Box>

                      {item.subItems && (
                        <Collapse
                          in={isDropdownOpen}
                          timeout="auto"
                          unmountOnExit
                        >
                          <Box
                            sx={{
                              paddingLeft: 7,
                              display: "flex",
                              flexDirection: "column",
                              gap: 2,
                            }}
                          >
                            {item.subItems.map((subItem: any) => (
                              <Box
                                key={subItem.label}
                                onClick={() => {
                                  navigate(subItem.path);
                                  setOpenDropdownIndex(null);
                                }}
                                sx={{
                                  fontSize: "14px",
                                  color: "#55556D",
                                  cursor: "pointer",
                                  "&:hover": {
                                    color: "var(--primary-color)",
                                  },
                                }}
                              >
                                <Typography
                                  sx={{
                                    display: "flex",
                                    gap: 1,
                                    fontSize: 14,
                                    justifyContent: "center",
                                    textShadow: isActive
                                      ? "2px 2px 4px rgba(0, 0, 0, 0.2)"
                                      : "none",
                                  }}
                                >
                                  {subItem.label}
                                </Typography>
                              </Box>
                            ))}
                          </Box>
                        </Collapse>
                      )}
                    </React.Fragment>
                  );
                })}
              </Box>

              <Typography
                fontSize="8px"
                color={"#55556DB2"}
                fontWeight={600}
                marginBottom="3px"
                marginLeft={2}
              ></Typography>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "10px",
                }}
              >
                {tetapanMenuItems.map((item, index) => {
                  const isActive =
                    activeTab === index + allowedMenuItems.length;
                  return (
                    <Box
                      key={item.label}
                      onClick={() =>
                        handleMenuClick(item, index + allowedMenuItems.length)
                      }
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "flex-start",
                        gap: "10px",
                        padding: "11px 19px",
                        borderRadius: "20px",
                        backgroundColor: isActive
                          ? "var(--primary-color)"
                          : "transparent",
                        color: isActive ? "#fff" : "#55556D",
                        fontWeight: 400,
                        fontSize: "14px",
                        cursor: "pointer",
                      }}
                    >
                      <Typography
                        sx={{
                          display: "flex",
                          gap: 1,
                          fontSize: 14,
                          justifyContent: "center",
                          textShadow: isActive
                            ? "2px 2px 4px rgba(0, 0, 0, 0.2)"
                            : "none",
                        }}
                      >
                        {item.icon && item.icon(isActive)}
                        {item.label}
                      </Typography>
                    </Box>
                  );
                })}
              </Box>
            </Box>

            {/* <Box marginInline="auto" width="fit-content">
              <Switch />
            </Box> */}
          </Box>
          {/* END SIDEBAR */}

          {/* DRAWER MOBILE*/}
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{
              keepMounted: true,
            }}
            sx={{
              display: { xs: "block", md: "none" },
              "& .MuiDrawer-paper": { boxSizing: "border-box", width: 240 },
            }}
          >
            {drawer}
          </Drawer>
          {/* END DRAWER MOBILE */}

          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              flexGrow: 1,
              height: "100%",
              maxWidth: {
                md: "calc(100% - 270px)",
              },
            }}
          >
            {/* HEADER */}
            <Box
              sx={{
                marginBottom: "35px",
                display: "flex",
              }}
            >
              {/* MOBILE MENU */}
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{
                  display: { md: "none" },
                  backgroundColor: "#fff",
                  borderRadius: "15px",
                  padding: "0 17px",
                  margin: 0,
                }}
              >
                <MenuOpenIcon />
              </IconButton>
              {/* END MOBILE MENU */}

              <Box
                sx={{
                  display: "flex",
                  gap: "14px",
                  width: "fit-content",
                  marginLeft: "auto",
                }}
              >
                <Box
                  sx={{
                    background: "#fff",
                    borderRadius: "15px",
                    padding: "15px 17px",
                    display: {
                      xs: "none",
                      sm: "block",
                    },
                  }}
                >
                  <Typography
                    fontSize={"12px"}
                    fontWeight={600}
                    lineHeight="18px"
                  >
                    {convertDate(new Date(), localeAnnoDomini)} ,{" "}
                    {convertDate(new Date(), localeHijri)}
                  </Typography>
                </Box>

                {/* LANGUAGE SELECT */}
                <Select
                  value={i18n.language}
                  onChange={(event) =>
                    changeLanguage(event.target.value as string)
                  }
                  sx={{
                    width: "80px",
                    height: "48px",
                    borderRadius: "15px",
                    background: "#fff",
                    "& .MuiPaper-root": {
                      boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
                    },
                    "& .MuiSelect-select": {
                      padding: "4px 28px 4px 12px",
                      fontSize: "14px",
                      fontWeight: 700,
                    },
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#fff",
                    },
                    display: { xs: "none", md: "flex" },
                  }}
                >
                  <MenuItem
                    value="my"
                    sx={{ fontSize: "14px", fontWeight: 700 }}
                  >
                    BM
                  </MenuItem>
                  <MenuItem
                    value="en"
                    sx={{ fontSize: "14px", fontWeight: 700 }}
                  >
                    EN
                  </MenuItem>
                </Select>
                {/* END LANGUAGE SELECT */}

                {/* NOTIFICATIONS BTN */}

                <Button
                  sx={{
                    background: "#fff",
                    borderRadius: "15px",
                    color: "var(--primary-color)",
                    minWidth: "unset",
                    padding: "0 17px",
                  }}
                  onClick={(e) => handleNotificationOpen(e)}
                >
                  <Badge
                    badgeContent={
                      unreadCount > 0
                        ? unreadCount > 99
                          ? "99+"
                          : unreadCount
                        : 0
                    }
                    color="error"
                  >
                    <NotificationsIcon sx={{ fontSize: "22px" }} />
                  </Badge>
                </Button>

                <Popover
                  open={Boolean(anchorNoti)}
                  anchorEl={anchorNoti}
                  onClose={() => setAnchorNoti(null)}
                  anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "right",
                  }}
                  transformOrigin={{ vertical: "top", horizontal: "right" }}
                  sx={{ mt: 1 }}
                >
                  <Box sx={{ width: "65vh", maxHeight: "70vh" }}>
                    {isLoadingNotificationData ? (
                      <Box
                        sx={{
                          display: "grid",
                          gap: 1,
                          justifyContent: "center",
                          alignItems: "center",
                          textAlign: "center",
                          px: 3,
                          py: 2,
                        }}
                      >
                        <CircularProgress
                          size={24}
                          sx={{
                            display: "block",
                            mx: 3,
                            mb: 2,
                          }}
                        />
                      </Box>
                    ) : notifications.length === 0 ? (
                      <Box
                        sx={{
                          display: "grid",
                          gap: 1,
                          justifyContent: "center",
                          alignItems: "center",
                          textAlign: "center",
                          px: 3,
                          py: 2,
                        }}
                      >
                        <Typography className="label-dashboard">
                          {t("noMoreNotification")}
                        </Typography>
                        <Typography className="sub-title-notification">
                          {t("noMoreNotification2")}
                        </Typography>
                      </Box>
                    ) : (
                      <>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            mx: 3,
                            my: 2,
                            gap: 2,
                          }}
                        >
                          <Typography variant="h6" className="mainTitle">
                            {t("notifikasi")}
                          </Typography>
                          <IconButton
                            className="mainTitle"
                            onClick={(e) => setMenuAnchorEl(e.currentTarget)}
                          >
                            <MoreHoriz />
                          </IconButton>
                        </Box>
                        {Object.entries(mainGroupedNotifications).map(
                          ([label, notis]) => (
                            <Box key={label}>
                              <Typography
                                className="title-no-height"
                                sx={{ fontWeight: "bold", mx: 3, my: 2 }}
                              >
                                {t(`${label}`)}
                              </Typography>
                              <Box
                                sx={{
                                  display: "flex",
                                  flexDirection: "column",
                                }}
                              >
                                {notis.map((noti: any) => {
                                  const createdDate = dayjs(
                                    noti?.createdDate,
                                    "DD-MM-YYYY HH:mm:ss"
                                  );
                                  const diffMinutes = dayjs().diff(
                                    createdDate,
                                    "minute"
                                  );
                                  const timeAgo =
                                    diffMinutes < 60
                                      ? `${diffMinutes}m`
                                      : diffMinutes < 1440
                                      ? `${Math.floor(diffMinutes / 60)}j`
                                      : `${Math.floor(diffMinutes / 1440)}h`;

                                  return (
                                    <Box
                                      sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 2,
                                        p: 0,
                                        m: 0,
                                        minHeight: 72,
                                        backgroundColor:
                                          noti?.seen === false
                                            ? "var(--primary-color)40"
                                            : "transparent",
                                        px: 3,
                                        py: 2,
                                      }}
                                      key={noti?.notificationTrackerId}
                                    >
                                      <Avatar
                                        sx={{
                                          bgcolor: getAvatarColor(
                                            noti?.subject.charAt(0)
                                          ),
                                          width: 55, // Adjust size
                                          height: 55,
                                          fontSize: 18, // Adjust text size
                                        }}
                                      >
                                        {noti?.subject.charAt(0).toUpperCase()}
                                      </Avatar>
                                      <Box sx={{ flex: 1 }}>
                                        <Box
                                          sx={{
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "space-between",
                                          }}
                                        >
                                          <Typography
                                            variant="body1"
                                            className="label-dashboard"
                                            sx={{
                                              display: "-webkit-box",
                                              WebkitBoxOrient: "vertical",
                                              WebkitLineClamp: 1,
                                              overflow: "hidden",
                                            }}
                                          >
                                            {noti?.subject}
                                          </Typography>
                                          <Typography
                                            variant="caption"
                                            sx={{ color: "gray" }}
                                          >
                                            {timeAgo}
                                          </Typography>
                                        </Box>
                                        <Typography
                                          variant="body2"
                                          className="label-login"
                                          sx={{
                                            display: "-webkit-box",
                                            WebkitBoxOrient: "vertical",
                                            WebkitLineClamp: 2,
                                            overflow: "hidden",
                                            pr: 3,
                                          }}
                                          dangerouslySetInnerHTML={{
                                            __html: noti?.content.replace(
                                              /\r\n/g,
                                              " "
                                            ),
                                          }}
                                        />
                                      </Box>
                                    </Box>
                                  );
                                })}
                              </Box>
                            </Box>
                          )
                        )}
                      </>
                    )}

                    <Box
                      sx={{
                        borderTop: "1px solid #ddd",
                        p: 1,
                        textAlign: "center",
                        background: "#fff",
                        position: "sticky",
                        bgcolor: "var(--light-btn)",
                        bottom: 0,
                      }}
                    >
                      <Button
                        fullWidth
                        sx={{ textTransform: "none" }}
                        onClick={() => handleGoToNotifications()}
                      >
                        {t("seeAllNotifications")}
                      </Button>
                    </Box>
                  </Box>
                </Popover>

                <Popover
                  open={Boolean(menuAnchorEl)}
                  anchorEl={menuAnchorEl}
                  onClose={() => setMenuAnchorEl(null)}
                  anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "right",
                  }}
                  transformOrigin={{ vertical: "top", horizontal: "right" }}
                  sx={{ mt: 1 }}
                >
                  <Typography
                    className="label"
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      width: 270,
                    }}
                  >
                    <Button
                      fullWidth
                      sx={{ textTransform: "none", p: 2 }}
                      onClick={() => handleMarkAsRead()}
                    >
                      {t("markAsRead")}
                    </Button>
                  </Typography>
                </Popover>

                {/* PROFILE BTN */}
                <Avatar
                  sx={{
                    width: "47px",
                    height: "48px",
                    borderRadius: "15px",
                    bgcolor: "#fff",
                    color: "#000000",
                    cursor: "pointer",
                  }}
                  onClick={handleMenuOpen}
                >
                  <PersonOutlineIcon sx={{ fontSize: 30 }} />
                </Avatar>

                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleMenuClose}
                  anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "right",
                  }}
                  transformOrigin={{
                    vertical: "top",
                    horizontal: "right",
                  }}
                  sx={{
                    "& .MuiPaper-root": {
                      backgroundColor: "#D8D8D8",
                      borderRadius: "7px",
                      px: 2,
                      py: 3,
                    },
                    "& .MuiMenu-list": {
                      padding: 0,
                      display: "flex",
                      flexDirection: "column",
                      gap: 1,
                    },
                  }}
                >
                  <Typography sx={{ textAlign: "center", mb: 1 }}>
                    Hi, {userName}!
                  </Typography>
                  {group && group.length > 0 && group.includes(1) ? (
                    <MenuItem
                      onClick={(event) => {
                        event.preventDefault();
                        handleSwitchGroup("pertubuhan");
                      }}
                      sx={{
                        p: 2,
                        background: "#fff",
                        borderRadius: "7px",
                        display: "flex",
                        alignItems: "center",
                        gap: "17px",
                      }}
                    >
                      <PersonOutlineIcon />
                      <Typography>{t("organizationUser")}</Typography>
                      {isLoadingUserData && currentPort == "1" ? (
                        <CircularProgress size={20} />
                      ) : currentPort == "1" ? (
                        <Box
                          sx={{
                            width: "16px",
                            height: "16px",
                            background: "#1BD5D2",
                            borderRadius: "50%",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <DoneIcon
                            sx={{
                              fontSize: "10px",
                              color: "#fff",
                            }}
                          />
                        </Box>
                      ) : (
                        ""
                      )}
                    </MenuItem>
                  ) : null}

                  {group && group.length > 0 && group.includes(2) ? (
                    <MenuItem
                      onClick={(event) => {
                        event.preventDefault();
                        handleSwitchGroup("internal-user");
                      }}
                      sx={{
                        p: 2,
                        background: "#fff",
                        borderRadius: "7px",
                        display: "flex",
                        alignItems: "center",
                        gap: "17px",
                      }}
                    >
                      <PersonOutlineIcon />
                      <Typography>{t("internalUser")}</Typography>
                      {isLoadingUserData && currentPort == "2" ? (
                        <CircularProgress size={20} />
                      ) : currentPort == "2" ? (
                        <Box
                          sx={{
                            width: "16px",
                            height: "16px",
                            background: "#1BD5D2",
                            borderRadius: "50%",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <DoneIcon
                            sx={{
                              fontSize: "10px",
                              color: "#fff",
                            }}
                          />
                        </Box>
                      ) : (
                        ""
                      )}
                    </MenuItem>
                  ) : null}
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: "7px",
                      backgroundColor: "var(--primary-color)",
                      cursor: "pointer",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      gap: 3,

                      marginTop: 5,
                    }}
                    // onClick={() =>
                    //   navigate("/pertubuhan/pembaharuan-setiausaha")
                    // }
                    onClick={(event) => {
                      event.preventDefault();
                      setShowComingSoon(true);
                      handleMenuClose();
                      handleMenuClose();
                    }}
                  >
                    <img
                      src={`data:image/svg+xml;utf8,${encodeURIComponent(
                        ArrowBackground3SVG
                      )}`}
                      width={50}
                      alt="Arrow Background"
                    />
                    <Typography
                      sx={{
                        fontSize: "16px",
                        textShadow: "2px 2px 4px rgba(0, 0, 0, 0.2)",
                        color: "white",
                      }}
                    >
                      {t("userGuide")}
                    </Typography>
                  </Box>
                  <Button
                    sx={{
                      width: "100%",
                      borderRadius: "7px",
                      background: "transparent",
                      color: "var(--error)",
                      textTransform: "none",
                      border: "1px solid var(--error)",
                    }}
                    onClick={handleLogout}
                  >
                    Log Keluar
                  </Button>
                </Menu>
              </Box>
            </Box>
            {/* END HEADER */}

            {/* MAIN */}
            <Box
              sx={{
                flexGrow: 1,
              }}
            >
              {children}
            </Box>
            {/* END MAIN */}
          </Box>
        </Box>
      )}
    </>
  );
};
