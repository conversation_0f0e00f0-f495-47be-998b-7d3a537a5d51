import React from "react";
import type { TFunction } from "i18next";
import Box from "@mui/material/Box";
import { Grid, TextField, Typography } from "@mui/material";
import {
  Control,
  Controller,
  FieldValues,
  UseFormGetValues,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { formatAndValidateNumber } from "@/helpers";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

export const CreatePernyataanPendapatanPembelajaanMigrate: React.FC<{
  t: TFunction;
  control: Control<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  checked: boolean;
  isDisabled: boolean;
}> = ({ t, control, setValue, getValues, checked, isDisabled }) => {
  function formatNumber(num: string) {
    return Number(num.replace(/,/g, ""));
  }
  const renderInputGroup = (
    title: string,
    items: { label: string; variable: string }[]
  ) => (
    <>
      <Box
        sx={{
          background: "white",
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={4}>
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontSize: "14px",
                fontWeight: "500 !important",
              }}
            >
              {t(title)}
            </Typography>
          </Grid>
          <Grid item xs={12}>
            {items.map((item, index) => {
              return (
                <Grid container spacing={2} sx={{ mb: 1 }}>
                  <Grid item xs={4}>
                    <Typography sx={labelStyle}>
                      {t(item.label)} (RM)
                    </Typography>
                  </Grid>
                  <Grid item xs={8}>
                    <Controller
                      name={item.variable}
                      key={index}
                      control={control}
                      defaultValue={getValues(item.variable)}
                      render={({ field }) => {
                        return (
                          <TextField
                            size="small"
                            fullWidth
                            disabled={checked || isDisabled}
                            value={getValues(item.variable)}
                            placeholder="0"
                            onChange={(e) => {
                              const formattedValue = formatAndValidateNumber(
                                e.target.value
                              );
                              const currentValue = formatNumber(
                                getValues(item.variable)
                              );
                              const newValue = formatNumber(e.target.value);
                              const totalIncome = formatNumber(
                                getValues("totalExpense")
                              );

                              const updatedTotalIncome =
                                totalIncome - currentValue + newValue;

                              setValue(
                                "totalExpense",
                                formatAndValidateNumber(
                                  updatedTotalIncome.toString()
                                )
                              );

                              // console.log(typeof currentValue);
                              if (formattedValue !== null) {
                                field.onChange(formattedValue);
                                // setValue("totalIncome", formattedValue);
                              }
                            }}
                          />
                        );
                      }}
                    />{" "}
                  </Grid>
                </Grid>
              );
            })}
          </Grid>
        </Grid>
      </Box>
    </>
  );

  return (
    <>
      {renderInputGroup("operatingExpenses", [
        { label: "fundraisingExpenses", variable: "taxExpense" },
        { label: "tax", variable: "taxExpense" },
        { label: "others", variable: "sumbExpense" },
        { label: "welfareExpenses", variable: "zakatWelfare" },
        { label: "generalCharity", variable: "generalWelfare" },
        { label: "deathCharity", variable: "deathWelfare" },
        { label: "giftsPresents", variable: "giftWelfare" },
        { label: "scholarship", variable: "scholarshipWelfare" },
      ])}

      {renderInputGroup("activityFundraisingExpenses", [
        {
          label: "activityOrganizationExpenses",
          variable: "organizedActivity",
        },
        { label: "organizationActivityPromotion", variable: "promoActivity" },
        { label: "entertainment", variable: "banquetActivity" },
        { label: "visitTourFamilyDay", variable: "tourActivity" },
        { label: "investmentExpenses", variable: "investmentActivity" },
        { label: "participationFees", variable: "feeActivity" },
        { label: "others", variable: "otherActivity" },
      ])}

      {renderInputGroup("administrativeCosts", [
        { label: "allowancesSalariesWages", variable: "salaryCost" },
        { label: "rental", variable: "rentalCost" },
        { label: "utilities", variable: "utilityCost" },
        { label: "officeSupplies", variable: "supplyCost" },
        { label: "membershipCard", variable: "cardCost" },
        { label: "bonus", variable: "bonusCost" },
        { label: "epfSocso", variable: "kwspCost" },
        { label: "insurance", variable: "insuranceCost" },
        { label: "uniformClothes", variable: "uniformCost" },
        { label: "maintenance", variable: "maintenanceCost" },
        { label: "modification", variable: "renovationCost" },
        { label: "transportation", variable: "transportationCost" },
        { label: "photocopy", variable: "photocopyCost" },
        { label: "bankCharges", variable: "bankChargeCost" },
        { label: "others", variable: "otherCost" },
      ])}

      {renderInputGroup("otherExpenses", [
        { label: "otherExpenses", variable: "otherExpense" },
      ])}

      {renderInputGroup("", [
        { label: "jumlahPerbelanjaan", variable: "totalExpense" },
      ])}
    </>
  );
};

export default CreatePernyataanPendapatanPembelajaanMigrate;
