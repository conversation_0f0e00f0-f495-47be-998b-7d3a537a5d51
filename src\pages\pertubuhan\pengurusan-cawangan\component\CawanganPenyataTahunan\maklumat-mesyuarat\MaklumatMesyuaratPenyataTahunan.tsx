import { Box, Grid, Typography } from "@mui/material";
import React, { useEffect } from "react";
import Input from "@/components/input/Input";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, TileLayer, useMap } from "react-leaflet";
import { Meeting } from "../interface";
import { MeetingMethods } from "@/helpers/enums";
import dayjs from "dayjs";
import {
  getLocalStorage,
  getMeetingType,
  useBackendLocalization,
} from "@/helpers";
import { FormMeetingDateTime } from "@/components/form/meeting";

type props = {
  isDisabled: boolean;
  sectionStyle: any;
  businessCoords: [number, number];
  meetings: Meeting[];
  meeting: Meeting | undefined | null;
  handleSetMeeting: (selectedMeeting: Meeting | null) => void;
  handleSetFilteredMeeting: (filteredMeeting: Meeting[]) => void;
  setSelectedDate: (date: string) => void;
  selectedDate: string;
  filteredMeetings: Meeting[];
  meetingId: string | number | null;
  availableDateList: string[];
  setSavedMeetingDate: any;
  savedMeetingDate: string;
  resetChanges: () => void;
};
const MaklumatMesyuaratPenyataTahunan: React.FC<props> = ({
  isDisabled,
  meetingId,
  selectedDate,
  filteredMeetings,
  sectionStyle,
  businessCoords,
  meetings,
  meeting,
  setSelectedDate,
  handleSetMeeting,
  handleSetFilteredMeeting,
  availableDateList = [],
  setSavedMeetingDate,
  savedMeetingDate,
  resetChanges,
}) => {
  const { t, i18n } = useTranslation();
  const meetingList = getLocalStorage("meeting_list", null);
  const RecenterAutomatically = ({
    lat,
    lng,
  }: {
    lat: number;
    lng: number;
  }) => {
    const map = useMap();
    useEffect(() => {
      map.setView([lat, lng]);
    }, [lat, lng]);
    return null;
  };

  useEffect(() => {
    if (meetingId) {
      const selectedMeeting = meetings.find(
        (meeting) => meeting.id === meetingId
      );
      if (selectedMeeting) {
        handleSetMeeting(selectedMeeting);
      }
    }
  }, [meetingId, meetings]);

  useEffect(() => {
    if (selectedDate) {
      const filteredMeeting = meetings.filter(
        (meeting) =>
          meeting.meetingDate.toString() ===
          dayjs(selectedDate).format("YYYY-MM-DD")
      );
      handleSetFilteredMeeting(filteredMeeting);
    }
  }, [selectedDate]);

  const { getTranslation } = useBackendLocalization();

  const getPlatformType = (type: number) => {
    const platform =
      meetingList.filter((item: any) => item.id === type)?.[0] || {};
    return getTranslation(platform);
  };

  const getMeetingMethod = (meetingId: string | number | undefined) => {
    const meetingMethod = meetingList.find(
      (m: any) => m.id === Number(meetingId)
    );
    return meetingMethod ? getTranslation(meetingMethod) : "-";
  };

  const clearMeeting = () => handleSetMeeting(null);

  return (
    <Box
      sx={{
        background: "white",
        border: "1px solid rgba(0, 0, 0, 0.12)",
        borderRadius: "14px",
        p: 3,
        mb: 2,
      }}
    >
      <Typography variant="subtitle1" sx={sectionStyle}>
        {t("maklumatMesyuaratPenyataTahunan")}
      </Typography>
      <Grid item xs={12}>
        <Input
          required
          label={t("meetingDate")}
          type="date"
          availableDate={availableDateList}
          value={selectedDate ? selectedDate : savedMeetingDate}
          onChange={(e) => {
            clearMeeting();
            setSavedMeetingDate(e.target.value);
            setSelectedDate(e.target.value);
            resetChanges();
            const filteredMeeting = meetings.filter(
              (meeting) =>
                meeting.meetingDate.toString() ===
                dayjs(selectedDate ? selectedDate : e.target.value).format(
                  "YYYY-MM-DD"
                )
            );
            handleSetFilteredMeeting(filteredMeeting);
          }}
        />
        {meetingId || filteredMeetings.length > 0 ? (
          <>
            <Input
              label={t("meetingList")}
              type="select"
              required
              disabled={isDisabled}
              value={meetingId ? meetingId : meeting?.id}
              onChange={(e) => {
                // Find the selected meeting based on the value from the select input
                const selectedMeeting = meetings.find(
                  (meeting) => meeting.id === e.target.value
                );
                if (selectedMeeting) {
                  handleSetMeeting(selectedMeeting); // Pass selected meeting to parent
                }
              }}
              options={filteredMeetings.map((meeting) => ({
                value: meeting.id,
                label: `${
                  getMeetingType(Number(meeting.meetingType))?.label
                } (${meeting.meetingDate})`,
              }))}
            />
            {meeting ? (
              <>
                {" "}
                <Input
                  label={t("meetingMethod")}
                  disabled
                  value={getMeetingMethod(meeting?.meetingMethod)}
                  required
                />
                {meeting?.meetingMethod == MeetingMethods.BERSEMUKA ? null : (
                  <Input
                    label={t("platformType")}
                    disabled
                    value={getPlatformType(Number(meeting?.platformType))}
                    required
                  />
                )}
                <Input
                  label={t("meetingPurpose")}
                  disabled
                  value={meeting?.meetingPurpose}
                  required
                />
                <Grid container spacing={2} alignItems="center" sx={{ mb: 1 }}>
                  <Grid item xs={12}>
                    <FormMeetingDateTime
                      viewOnly
                      meetingTimeFromAttribute="meetingTime"
                      defaultValues={{
                        meetingDate: meeting?.meetingDate
                          ? dayjs(meeting.meetingDate)
                          : null,
                        meetingTime: meeting?.meetingDate
                          ? dayjs(
                              `${meeting.meetingDate} ${
                                meeting?.meetingTime ?? "00:00:00"
                              }`,
                              "YYYY-MM-DD HH:mm:[00]"
                            )
                          : null,
                        meetingTimeTo: meeting?.meetingDate
                          ? dayjs(
                              `${meeting.meetingDate} ${
                                meeting?.meetingTimeTo ?? "00:00:00"
                              }`,
                              "YYYY-MM-DD HH:mm:[00]"
                            )
                          : null,
                      }}
                    />
                  </Grid>
                </Grid>
                {meeting?.meetingMethod == MeetingMethods.ATAS_TALIAN ? null : (
                  <>
                    <Input
                      label={t("namaTempatMesyuarat")}
                      value={meeting?.meetingPlace}
                      disabled
                    />
                    <Grid
                      container
                      spacing={2}
                      alignItems="flex-start"
                      sx={{ mb: 1 }}
                    >
                      <Grid item xs={12} sm={4}>
                        <Typography
                          variant="body1"
                          sx={{
                            color: "#666666",
                            fontWeight: "400 !important",
                            fontSize: "14px",
                          }}
                        >
                          {t("meetingLocation")}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={8}>
                        <MapContainer
                          center={businessCoords}
                          zoom={13}
                          style={{
                            height: "150px",
                            width: "100%",
                            borderRadius: "8px",
                          }}
                        >
                          <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                          <Marker position={businessCoords} />
                          <RecenterAutomatically
                            lat={businessCoords[0]}
                            lng={businessCoords[1]}
                          />
                        </MapContainer>
                      </Grid>
                    </Grid>
                  </>
                )}
              </>
            ) : null}
          </>
        ) : null}
      </Grid>
    </Box>
  );
};

export default MaklumatMesyuaratPenyataTahunan;
