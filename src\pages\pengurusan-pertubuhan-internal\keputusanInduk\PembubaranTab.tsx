import { useCallback, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { CrudFilter } from "@refinedev/core";
import { FieldValues, useForm } from "react-hook-form";
import {
  getLocalStorage,
  debounce,
  getStateNameById,
  ApplicationStatusEnum,
  useQuery,
  NEW_PermissionNames,
  pageAccessEnum,
  formatDate,
} from "@/helpers";

import { Box, Typography, IconButton } from "@mui/material";
import DataTable, { IColumn } from "../../../components/datatable";
import FormFieldRow from "../../../components/form-field-row";
import Label from "../../../components/label/Label";
import TextFieldController from "../../../components/input/TextFieldController";
import SelectFieldController from "../../../components/input/select/SelectFieldController";

import { EditIcon } from "../../../components/icons";
import AuthHelper from "@/helpers/authHelper";

interface Props {
  number: number;
}

const PembubaranTab: React.FC<Props> = ({ number }) => {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language;

  const hasReadPermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_INDUK.children.PEMBUBARAN.label,
    pageAccessEnum.Read
  );

  const columns: IColumn[] = [
    {
      field: "societyName",
      headerName: t("pertubuhan"),
      flex: 1,
      align: "center",
    },
    {
      field: "roName",
      headerName: t("roBertanggungjawab"),
      flex: 1,
      align: "center",
    },
    {
      field: "transferDate",
      headerName: t("tarikhAlir"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => formatDate(row.transferDate),
    },
    {
      field: "submissionDate",
      headerName: t("submissionDate"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => formatDate(row.submissionDate),
    },
    {
      field: "societyNo",
      headerName: t("organizationNumber"),
      flex: 1,
      align: "center",
    },
    {
      field: "stateCode",
      headerName: t("negeri"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => getStateNameById(row.stateCode),
    },
    {
      field: "statusPermohonan",
      headerName: t("statusPermohonan"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => ApplicationStatusEnum[row.applicationStatusCode],
    },
    {
      field: "actions",
      headerName: t("action"),
      align: "center",
      renderCell: ({ row }) => (
        <IconButton
          disabled={!hasReadPermission}
          onClick={() => navigate(`pembubaran/${row.id}`)}
        >
          <EditIcon
            sx={{
              color: hasReadPermission
                ? "var(--primary-color)"
                : "var(--text-grey-disabled)",
              width: "1rem",
              height: "1rem",
            }}
          />
        </IconButton>
      ),
    },
  ];

  const { control, setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      categoryCode: "",
      subCategoryCode: "",
      societyName: "",
      page: 1,
      pageSize: 10,
    },
  });

  const {
    category,
    page,
    societyName,
    pageSize,
    categoryCode,
    subCategoryCode,
  } = watch();

  const {
    data: liquidationApprovalResponse,
    refetch: fetchLiquidationApprovalList,
    isLoading,
  } = useQuery({
    url: "society/roDecision/getAllPending/society/liquidation",
    autoFetch: false,
  });

  const totalList = liquidationApprovalResponse?.data?.data?.total ?? 0;
  const liquidationApprovalList =
    liquidationApprovalResponse?.data?.data?.data ?? [];

  const categories = getLocalStorage("category_list", []);
  const mainCategories = categories.filter((cat: any) => cat.level === 1) ?? [];
  const subCategories = categories.filter((cat: any) => cat.level === 2) ?? [];

  const mainCategoriesOptions = mainCategories?.map((category: any) => ({
    value: category.id.toString(),
    label: category.categoryNameEn,
  }));

  const subCategoriesOptions = subCategories?.map((category: any) => ({
    value: category.id.toString(),
    label: category.categoryNameEn,
  }));

  const getBaseFilters = (pageSize: number, pageNo: number): CrudFilter[] => [
    { field: "pageSize", value: pageSize, operator: "eq" },
    { field: "pageNo", value: pageNo, operator: "eq" },
  ];

  const buildFilters = (
    pageSize: number,
    pageNo: number,
    options: {
      societyName?: string;
      categoryCode?: string;
      subCategoryCode?: string;
    }
  ): CrudFilter[] => {
    const filters = getBaseFilters(pageSize, pageNo);

    if (options.societyName) {
      filters.push({
        field: "societyName",
        value: options.societyName,
        operator: "eq",
      });
    }

    if (options.categoryCode) {
      filters.push({
        field: "categoryCode",
        value: options.categoryCode,
        operator: "eq",
      });
    }

    if (options.subCategoryCode) {
      filters.push({
        field: "subCategoryCode",
        value: options.subCategoryCode,
        operator: "eq",
      });
    }

    return filters;
  };

  const handleSearchSocietyName = useCallback(
    debounce((e) => {
      const value = e.target.value.trim();
      const filters = buildFilters(pageSize, 1, {
        societyName: value,
        categoryCode,
        subCategoryCode,
      });
      setValue("page", 1);
      setValue("pageSize", pageSize);
      fetchLiquidationApprovalList({ filters });
    }, 2000),
    [pageSize]
  );

  const handleChangeCategories = () => {
    setValue("page", 1);
    setValue("pageSize", 10);
    const filters = buildFilters(10, 1, {
      categoryCode,
      subCategoryCode,
    });
    fetchLiquidationApprovalList({ filters });
  };

  const handleChangePage = (newPage: number) => {
    const filters = buildFilters(pageSize, newPage, {
      societyName,
      categoryCode,
      subCategoryCode,
    });
    setValue("page", newPage);
    fetchLiquidationApprovalList({ filters });
  };

  const handleChangePageSize = (newPageSize: number) => {
    const filters = buildFilters(newPageSize, 1, {
      societyName,
      categoryCode,
      subCategoryCode,
    });

    setValue("page", 1);
    setValue("pageSize", newPageSize);
    fetchLiquidationApprovalList({ filters });
  };

  useEffect(() => {
    fetchLiquidationApprovalList({
      filters: getBaseFilters(pageSize, 1),
    });
  }, []);

  useEffect(() => {
    handleChangeCategories();
  }, [categoryCode, subCategoryCode]);

  return (
    <>
      <Box
        sx={{
          padding: "22px 16px",
          background: "#FFF",
          borderRadius: "15px",
          boxShadow: "0px 12px 12px 0px #EAE8E866",
          marginBottom: 1,
        }}
      >
        <Box
          sx={{
            width: "100%",
            border: "0.5px solid #DADADA",
            borderRadius: "10px",
            padding: "22px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("parentLiquidationWaitingList")}
          </Typography>

          <FormFieldRow
            label={<Label text={t("organization_category")} />}
            value={
              <SelectFieldController
                name="categoryCode"
                control={control}
                options={mainCategoriesOptions}
                placeholder={t("selectPlaceholder")}
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("organizationSubCategory2")} />}
            value={
              <SelectFieldController
                name="subCategoryCode"
                control={control}
                options={subCategoriesOptions}
                placeholder={t("selectPlaceholder")}
                disabled={!categoryCode}
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("namaPertubuhan")} />}
            value={
              <TextFieldController
                name="societyName"
                control={control}
                onChange={handleSearchSocietyName}
              />
            }
          />
        </Box>
      </Box>

      <Box
        sx={{
          padding: "22px 16px",
          background: "#FFF",
          borderRadius: "15px",
          boxShadow: "0px 12px 12px 0px #EAE8E866",
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: "13px",
            padding: "15px 0",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            marginBottom: 1,
            backgroundColor: "var(--primary-color)",
          }}
        >
          <Typography
            fontWeight="500 !important"
            fontSize="36px"
            color="#FFF"
            textAlign="center"
            lineHeight="30px"
            sx={{
              "& span": {
                fontSize: "20px",
              },
            }}
          >
            {number}
            <br />
            <span>
              {currentLanguage === "my"
                ? "Persatuan menunggu keputusan pembubaran Induk"
                : "The association awaits the decision on the dissolution of the parent company."}
            </span>
          </Typography>
        </Box>
        <Box
          sx={{
            width: "100%",
            border: "0.5px solid #DADADA",
            borderRadius: "10px",
            padding: "22px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="37px"
          >
            {t("parentLiquidationWaitingList")}
          </Typography>

          <DataTable
            columns={columns}
            rows={liquidationApprovalList}
            page={page}
            rowsPerPage={pageSize}
            totalCount={totalList}
            isLoading={isLoading}
            onPageChange={handleChangePage}
            onPageSizeChange={handleChangePageSize}
          />
        </Box>
      </Box>
    </>
  );
};

export default PembubaranTab;
