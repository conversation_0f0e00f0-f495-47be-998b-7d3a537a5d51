import { useEffect, useState } from "react";
import { useCallback } from "react";
import { useForm, FieldValues, useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import useQuery from "../../../../helpers/hooks/useQuery";
import { debounce } from "../../../../helpers/utils";
import { useSecretaryReformContext } from "./Provider";

import { Box, Typography, CircularProgress } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { ButtonPrimary } from "../../../../components/button";
import InputAdornment from "@mui/material/InputAdornment";
import TextFieldController from "../../../../components/input/TextFieldController";
import DisabledTextField from "../../../../components/input/DisabledTextField";

import { SearchIcon } from "../../../../components/icons";
import { useGetIdentity } from "@refinedev/core";

import { IUser } from "@/types";
import MessageDialog from "@/components/dialog/message";

export const Tambah: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { reset, setValue: setSecretaryFormValue } = useFormContext();
  const { data: user } = useGetIdentity<IUser>();
  const { handleSetSocietyData } = useSecretaryReformContext();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSocietyBlackListed, setIsSocietyBlackListed] = useState(false);

  const isMyLanguage = i18n.language === "my";

  const { control, setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      search: "",
      result: "",
      error: "",
    },
  });

  const result = watch("result");
  const error = watch("error");

  const { isLoading: isFetchingSociety, refetch: fetchSociety } = useQuery({
    url: "society/search",
    autoFetch: false,
    onSuccess: (res) => {
      const data = res?.data;

      if (data.code === 200) {
        const societyData = data.data?.data[0];
        const blackListed = societyData?.subStatusCode === "003";
        if (blackListed) {
          setIsSocietyBlackListed(blackListed);
          const message = isMyLanguage
            ? "Pertubuhan ini telah disenaraihitamkan."
            : "This organization has been blacklisted.";

          setValue("error", message);
        } else {
          setValue("error", "");
        }
        setValue("result", societyData);
        handleSetSocietyData(societyData);
      }

      if (data.code === 404) {
        const message = isMyLanguage
          ? "Tiada rekod pertubuhan ditemui."
          : "No societies found with the given search criteria.";

        setValue("error", message);
        setValue("result", "");
        handleSetSocietyData(null);
        setIsSocietyBlackListed(false);
      }
    },
  });

  const handleSearchSociety = useCallback(
    debounce(
      ({
        target: { value },
      }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        if (!value) return;
        fetchSociety({
          filters: [
            {
              field: "societyNo",
              operator: "eq",
              value: value,
            },
          ],
        });
      },
      1000
    ),
    []
  );

  useEffect(() => {
    reset();
  }, []);

  useEffect(() => {
    if (!user) return;

    setSecretaryFormValue("identificationType", user?.identificationType ?? "");
    setSecretaryFormValue("identificationNo", user?.identificationNo ?? "");
  }, [user]);

  const {
    data: getBlacklistedData,
    isLoading: getBlacklistedDataIsLoading,
    refetch: fetchBlackListed,
  } = useQuery({
    url: `society/blacklist/isCurrentUserBlacklisted`,
    autoFetch: false,
    onSuccess: (data) => {
      const isBlacklisted = data?.data?.data;
      if (isBlacklisted) {
        setIsDialogOpen(true);
      } else {
        navigate(`${result.id}`);
      }
    },
  });

  const goto = () => {
    fetchBlackListed();
  };

  return (
    <>
      <Box>
        <Box
          sx={{
            backgroundColor: "white",
            borderRadius: "16px",
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.05)",
          }}
        >
          <Box
            sx={{
              backgroundColor: "transparent",
              padding: "30px",
              border: "1px solid #DADADA",
              marginBottom: "16px",
              borderRadius: "14px",
            }}
          >
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontSize: "16px",
                fontWeight: "bold",
                mb: 3,
              }}
            >
              {t("organizationInfo")}
            </Typography>

            <Box
              sx={{
                display: "flex",
                gap: 2,
                mx: { xs: 2, md: "auto" },
                // maxWidth: '900px',
                width: "80%",
                flexDirection: "column",
              }}
            >
              <TextFieldController
                control={control}
                name="search"
                size="medium"
                placeholder="Sila masukkan nombor Pendaftaran Pertubuhan (PPM-XXX-XX-XXXXXX)"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "10px",
                  },
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start" sx={{ mr: 4 }}>
                      {isFetchingSociety ? (
                        <CircularProgress size={18} />
                      ) : (
                        <SearchIcon />
                      )}
                    </InputAdornment>
                  ),
                }}
                onChange={handleSearchSociety}
              />

              <DisabledTextField
                size="medium"
                value={result ? result.societyName : "Pertubuhan XXXX"}
                sx={{
                  backgroundColor: "#F8F8F8",
                  "& .MuiOutlinedInput-root": {
                    paddingLeft: 6,
                    borderRadius: "10px",
                    "& fieldset": {
                      borderColor: "#EAEAEA",
                    },
                  },
                }}
              />
            </Box>

            {error && (
              <Typography
                fontSize="12px"
                fontWeight="400 !important"
                color="#FF0000"
                textAlign="center"
                mt={2}
              >
                {error}
              </Typography>
            )}

            <Box sx={{ display: "flex", justifyContent: "center", mt: 3 }}>
              <ButtonPrimary
                onClick={() => goto()}
                sx={{
                  backgroundColor: "var(--primary-color)",
                  color: "white",
                  textTransform: "none",
                  fontWeight: "bold",
                  minWidth: "120px",
                }}
                disabled={isFetchingSociety || isSocietyBlackListed || !result}
              >
                {t("next")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Box>
      </Box>
      <MessageDialog
        open={isDialogOpen}
        buttonText={t("back")}
        onClose={() => setIsDialogOpen(false)}
        message={t("blacklistedMsg")}
      />
    </>
  );
};

export default Tambah;
