import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { API_URL } from "../../../../../api";
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
interface FasalContentLimaBelasCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentLimaBelasCawangan: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));

  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  //const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!pemilihanAjk) {
      errors.pemilihanAjk = t("fieldRequired");
    }

    return errors;
  };

  /*const clauseContent = `
1. Mesyuarat Agung ${
    pemilihanAjk !== "" ? `${pemilihanAjk}` : "<<Jenis Mesyuarat Agung>>"
  } Cawangan hendaklah diadakan tidak lewat dari 30 hari selepas menerima pemberitahuan awal mengenai Mesyuarat Agung ${
    pemilihanAjk !== "" ? `${pemilihanAjk}` : "<<Jenis Mesyuarat Agung>>"
  }, supaya pemberitahuan mengenai masa, tarikh dan tempat Mesyuarat Agung ${
    pemilihanAjk !== "" ? `${pemilihanAjk}` : "<<Jenis Mesyuarat Agung>>"
  } Cawangan boleh dikeluarkan dengan sewajarnya.

2. Tugas-tugas Mesyuarat Agung ${
    pemilihanAjk !== "" ? `${pemilihanAjk}` : "<<Jenis Mesyuarat Agung>>"
  } Cawangan adalah:

   a. mempertimbangkan laporan Bendahari Cawangan dan penyata kewangan Cawangan yang telah diaudit;

   b. memilih wakil-wakil Mesyuarat Agung;

   c. memilih Jawatankuasa Cawangan dan Juruaudit Cawangan; dan

   d. membincangkan perkara-perkara lain yang dibentangkan.

3. Setiausaha Cawangan hendaklah menghantar kepada tiap-tiap ahli sekurang-kurangnya 21 hari sebelum tarikh yang ditetapkan untuk Mesyuarat Agung ${
    pemilihanAjk !== "" ? `${pemilihanAjk}` : "<<Jenis Mesyuarat Agung>>"
  } Cawangan pemberitahuan awal yang menyatakan tarikh, waktu dan tempat mesyuarat, panggilan mengemukakan cadangan untuk perbincangan dan pencalonan untuk pemilihan pegawai cawangan.

4. Pencalonan untuk pemilihan pegawai-pegawai dan cadangan untuk perbincangan dalam Mesyuarat Agung ${
    pemilihanAjk !== "" ? `${pemilihanAjk}` : "<<Jenis Mesyuarat Agung>>"
  } Cawangan hendaklah disampaikan kepada Setiausaha Cawangan tidak lewat dari tujuh (7) hari selepas penerimaan pemberitahuan awal mesyuarat.

5. Setiausaha Cawangan hendaklah menghantar kepada tiap-tiap ahli sekurang-kurangnya tujuh (7) hari sebelum diadakan Mesyuarat Agung ${
    pemilihanAjk !== "" ? `${pemilihanAjk}` : "<<Jenis Mesyuarat Agung>>"
  } Cawangan, satu (1) agenda mesyuarat, salinan minit mesyuarat, laporan aktiviti tahunan, penyata kewangan Cawangan bagi tahun lalu yang telah diaudit. Salinan dokumen itu juga boleh diperolehi untuk dibaca oleh ahli-ahli di alamat tempat urusan Cawangan.

6. Sekurang-kurangnya satu perdua (1/2) daripada jumlah ahli-ahli Cawangan yang berhak mengundi atau dua (2) kali jumlah ahli jawatankuasa Cawangan, mengikut mana-mana yang kurang, hendaklah hadir dalam Mesyuarat Agung ${
    pemilihanAjk !== "" ? `${pemilihanAjk}` : "<<Jenis Mesyuarat Agung>>"
  } Cawangan bagi mengesahkan perjalanan mesyuarat dan mencukupkan kuorum mesyuarat.

7. Jika kuorum tidak cukup selepas setengah jam daripada waktu yang telah ditetapkan untuk mesyuarat, maka mesyuarat itu hendaklah ditangguhkan kepada suatu tarikh (tidak lebih dari tujuh (7) hari) yang telah ditetapkan oleh Jawatankuasa Cawangan dan jika kuorum tidak cukup selepas setengah jam daripada waktu yang ditetapkan untuk mesyuarat yang ditangguhkan itu, maka ahli-ahli yang hadir berkuasa menjalankan mesyuarat pada hari itu tetapi tidak berkuasa membuat keputusan yang melibatkan semua ahli.

8. Suatu Mesyuarat Agung Khas Cawangan hendaklah diadakan:

   a. atas arahan Jawatankuasa Induk melalui mesyuarat; atau

   b. apabila difikirkan mustahak oleh Jawatankuasa Cawangan; atau

   c. atas permintaan bertulis kepada Setiausaha Cawangan oleh tidak kurang daripada satu perdua (1/2) ahli-ahli Cawangan yang berhak mengundi dengan menerangkan tujuan mengadakannya.

9. Mesyuarat Agung Khas Cawangan atas permintaan ahli-ahli hendaklah diadakan dalam tempoh 15 hari dari tarikh penerimaan permintaan mesyuarat tersebut.

10. Fasal 15(6) dan 15(7) dalam Perlembagaan ini berkenaan kuorum dan penangguhan Mesyuarat Agung ${
    pemilihanAjk !== "" ? `${pemilihanAjk}` : "<<Jenis Mesyuarat Agung>>"
  } Cawangan adalah terpakai untuk Mesyuarat Agung Khas Cawangan dengan syarat jika kuorum tidak mencukupi selepas setengah jam daripada waktu yang ditetapkan bagi Mesyuarat Agung Khas Cawangan atas permintaan ahli-ahli, maka mesyuarat itu hendaklah dibatalkan dan sekurang-kurangnya enam (6) bulan dari tarikh ini Mesyuarat Agung Khas Cawangan atas permintaan ahli-ahli dengan tujuan yang sama tidak boleh diadakan.

11. Setiausaha Cawangan hendaklah menghantar kepada tiap-tiap ahli dan Setiausaha Agung satu (1) salinan minit Mesyuarat Agung ${
    pemilihanAjk !== "" ? `${pemilihanAjk}` : "<<Jenis Mesyuarat Agung>>"
  } Cawangan atau Mesyuarat Agung Khas Cawangan dalam tempoh tiga (3) bulan setelah selesai mesyuarat tersebut.
`;*/

  //const clause15 = localStorage.getItem("clause15");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause = JSON.parse(clause15);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause.societyName);
      setPemilihanAjk(clause.constitutionValues[0]?.definitionName);
      setIsEdit(clause.edit);
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id } = useParams();

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${pemilihanAjk || "<<jenis mesyuarat agung>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");
  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("generalMeeting")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("jenisMesyuaratAgung")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required error={!!formErrors.pemilihanAjk}>
              <Select
                size="small"
                disabled
                value={pemilihanAjk}
                displayEmpty
                onChange={(e) => {
                  setPemilihanAjk(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    pemilihanAjk: "",
                  }));
                }}
              >
                <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
              </Select>
              {formErrors.pemilihanAjk && (
                <FormHelperText>{formErrors.pemilihanAjk}</FormHelperText>
              )}
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            const errors = validateForm();
            if (Object.keys(errors).length > 0) {
              setFormErrors(errors);
              return;
            }

            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: pemilihanAjk,
                  titleName: "Jenis Mesyuarat Agung",
                },
              ],
              clause: "clause15",
              clauseCount: 15,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentLimaBelasCawangan;
