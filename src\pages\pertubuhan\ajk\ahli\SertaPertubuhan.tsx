import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Typography, Checkbox, useTheme } from "@mui/material";
import { GridColDef } from "@mui/x-data-grid";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { useCustomMutation } from "@refinedev/core";
import ConfirmationDialog from "../../../../components/dialog/confirm";
import NewAlertDialog from "../../../../components/dialog/newAlert";
import { OrganisationPositions, useQuery } from "@/helpers";
import { FieldValues, useForm } from "react-hook-form";
import { DataTable } from "@/components";

interface JoinRequestResponseBody {
  identificationNo: string;
  name: string;
  phoneNumber: string;
  userId: string | number;
}

const AhliSertaPertubuhan = <
  JoinRequest extends JoinRequestResponseBody = JoinRequestResponseBody
>() => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [searchPertubuhan, setSearchPertubuhan] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [dialogTrueSaveOpen, setDialogTrueSaveOpen] = useState(false);
  const [dialogFalseSaveOpen, setDialogFalseSaveOpen] = useState(false);
  const [dialogAlertSaveOpen, setDialogAlertSaveOpen] = useState(false);
  const [accept, setAccept] = useState(false);

  const { id: societyId } = useParams();

  const primary = theme.palette.primary.main;

  const [checked, setChecked] = useState<
    Record<JoinRequest["identificationNo"], boolean>
  >({} as Record<JoinRequest["identificationNo"], boolean>);
  const [idArray, setIdArray] = useState<JoinRequest["identificationNo"][]>([]);

  const handleCheckboxChange = (idNo: JoinRequest["identificationNo"]) => {
    //console.log(prev);
    setChecked((prev) => {
      const newChecked = !(prev?.[idNo] ?? false);

      setIdArray((prevIdArray) => {
        if (newChecked) {
          return [...prevIdArray, idNo];
        }
        return prevIdArray.filter((val) => val !== idNo);
      });

      return {
        ...prev,
        [idNo]: newChecked,
      };
    });
  };

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(searchPertubuhan);
    }, 1000);

    return () => {
      clearTimeout(handler);
    };
  }, [searchPertubuhan]);

  const open = Boolean(anchorEl);
  const id = open ? "simple-menu" : undefined;

  const [refreshKey, setRefreshKey] = useState(0);

  const sectionStyle = {
    color: primary,
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { mutateAsync: approveMember, isLoading } = useCustomMutation();

  const handleConfirmation = (approve: boolean) => {
    setAccept(approve);
    if (approve) {
      setDialogTrueSaveOpen(true);
    } else {
      setDialogFalseSaveOpen(true);
    }
  };

  const handleApprove = async (approve: boolean) => {
    await approveMember(
      {
        url: `${API_URL}/society/${societyId}/members/requestApproval`,
        method: "put",
        values: {
          identificationNo: idArray,
          approve: approve,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },

        successNotification: (data) => {
          if (data?.data?.status === "SUCCESS") {
            refetchSocietyList();
            setDialogAlertSaveOpen(true);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
    setDialogTrueSaveOpen(false);
    setDialogFalseSaveOpen(false);
  };
  const [positionsTranslatedList, setPositionsTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);
  useEffect(() => {
    const newPList = OrganisationPositions.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setPositionsTranslatedList(newPList);
  }, [t]);

  const columns: GridColDef<JoinRequest>[] = [
    {
      field: "",
      flex: 1,
      align: "left",
      renderCell: ({ row }) => {
        return (
          <Checkbox
            sx={{
              width: 24,
              height: 24,
              padding: 0,
              "&.MuiCheckbox-root": {
                borderRadius: "6px",
                border: "2px solid #979797",
              },
              "&.Mui-checked": {
                backgroundColor: primary,
                borderColor: primary,
              },
              "& .MuiSvgIcon-root": {
                display: "none",
              },
            }}
            checked={(checked as any)?.[row.identificationNo] ?? false}
            onChange={() => handleCheckboxChange(row.identificationNo)}
          />
        );
      },
    },
    {
      field: "designationCode",
      headerName: t("skillType"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        const position = positionsTranslatedList?.find(
          (item: any) => Number(item.value) === Number(row.designationCode)
        );
        return (
          <Typography sx={{ fontWeight: "400!important", fontSize: 14 }}>
            {position?.label ? position?.label : "-"}
          </Typography>
        );
      },
    },
    {
      field: "name",
      headerName: t("name"),
      flex: 1,
      align: "center",
    },
    {
      field: "identificationNo",
      headerName: t("personalIdNo2"),
      flex: 1,
      align: "center",
    },
    {
      field: "phoneNumber",
      headerName: t("phoneNumber"),
      flex: 1,
      align: "center",
    },
  ];

  const hasData = idArray.length > 0;

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 3,
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");
  const {
    data: societyListDataResponse,
    isLoading: isLoadingSocietyListData,
    refetch: refetchSocietyList,
  } = useQuery({
    url: `society/${societyId}/listJoinRequests`,
    filters: [
      {
        field: "pageSize",
        value: 5,
        operator: "eq",
      },
      {
        field: "pageNo",
        value: page,
        operator: "eq",
      },
    ],
  });

  const totalList = societyListDataResponse?.data?.data?.total ?? 0;
  const rowData = societyListDataResponse?.data?.data?.data ?? [];
  const handleChangePage = (newPage: number) => setValue("page", newPage);

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("permintaanSertaPertubuhan")}
          </Typography>
          <Box sx={{ mb: 4 }}>
            <DataTable
              columns={columns as any}
              rows={rowData}
              page={page}
              rowsPerPage={pageSize}
              totalCount={totalList}
              onPageChange={handleChangePage}
              isLoading={isLoadingSocietyListData}
              pagination={false}
            />
          </Box>
          <Box
            sx={{
              marginTop: "10px",
              display: "flex",
              flexGrow: 1,
              flexDirection: "row",
              gap: 2,
              justifyContent: "end",
            }}
          >
            <ButtonOutline
              variant="outlined"
              sx={{
                color: "#FF0000",
                borderColor: "#FF0000",
                "&:hover": { backgroundColor: "#FF0000", color: "white" },
              }}
              onClick={() => handleConfirmation(false)}
              disabled={!hasData}
            >
              {t("tolak")}
            </ButtonOutline>
            <ButtonPrimary
              onClick={() => handleConfirmation(true)}
              disabled={!hasData}
            >
              {t("terima")}
            </ButtonPrimary>
          </Box>
        </Box>
      </Box>

      <ConfirmationDialog
        open={dialogTrueSaveOpen}
        onClose={() => setDialogTrueSaveOpen(false)}
        title={t("pembayaranKaunter")}
        message={t("acceptMemberApplication")}
        onConfirm={() => handleApprove(true)}
        onCancel={() => setDialogTrueSaveOpen(false)}
        turn={true}
      />
      <ConfirmationDialog
        open={dialogFalseSaveOpen}
        onClose={() => setDialogFalseSaveOpen(false)}
        title={t("pembayaranKaunter")}
        message={t("rejectMemberApplication")}
        onConfirm={() => handleApprove(false)}
        onCancel={() => setDialogFalseSaveOpen(false)}
        turn={true}
      />

      <NewAlertDialog
        open={dialogAlertSaveOpen}
        onClose={() => setDialogAlertSaveOpen(false)}
        message={
          accept
            ? t("membershipAccpetApplicationSuccessful")
            : t("membershipApplicationSuccessful")
        }
      />
    </>
  );
};

export default AhliSertaPertubuhan;
