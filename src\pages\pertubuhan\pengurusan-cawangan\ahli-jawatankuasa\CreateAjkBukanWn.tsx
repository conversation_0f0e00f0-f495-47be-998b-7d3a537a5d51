import React, { useEffect, useState } from "react";
import { Box, Typography, Grid, FormHelperText } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { API_URL } from "../../../../api";
import { useCustom, useCustomMutation } from "@refinedev/core";
import Input from "../../../../components/input/Input";
import {
  IdTypes,
  OrganisationPositions,
  DurationOptions,
  CitizenshipStatus,
  GenderType,
} from "../../../../helpers/enums";
import {
  autoGenderSetByIC,
  formatDateToDDMMYYYY,
  getLocalStorage,
} from "../../../../helpers/utils";
import FileUploader from "@/components/input/fileUpload";
import { DocumentUploadType, useQuery } from "@/helpers";
import { ButtonPrimary } from "@/components";
import { useICValidation } from "@/helpers/hooks/useICValidation.ts";

interface FormValues {
  name: any;
  citizenshipStatus: any;
  identificationType: any;
  identificationNo: any;
  applicantCountryCode: any;
  gender: any;
  visaNo: number | any;
  visaExpirationDate: any;
  permitNo: any;
  permitExpirationDate: any;
  tujuanDMalaysia: any;
  tempohDMalaysia: any;
  stayDurationDigit: number | any;
  stayDurationUnit: any;
  designationCode: any;
  summary: any;
  societyNo: string | null;
  societyName: null;
  activeCommitteeId: any;
}

export const CreateAjkBukanWn: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const { id: societyId } = useParams();
  const branchId = searchParams.get("id");
  const memberId = searchParams.get("mId")
    ? searchParams.get("mId")
    : undefined;
  const addressList = getLocalStorage("address_list", null);
  const CountryData = addressList
    ?.filter((item: any) => item.level === 0)
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));

  const [errors, setErrors] = useState<{ [key in keyof FormValues]?: string }>(
    {}
  );

  const [idTypeTranslatedList, setIdTypeTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  const [nonCitizenCommiteeInfo, setNonCitizenCommiteeInfo] = useState<any>({});
  const [ajkId, setAjkId] = useState<string | undefined>(undefined);

  const {
    data: nonCommitteeData,
    isLoading: nonCommitteeDataLoading,
    refetch: fetchNonCommitteeData,
  } = useQuery({
    url: `society/nonCitizenCommittee/${memberId}`,
    autoFetch: !!memberId,
    onSuccess: (data) => {
      setNonCitizenCommiteeInfo(data?.data?.data);
    },
  });

  useEffect(() => {
    if (nonCitizenCommiteeInfo) {
      setFormValues(nonCitizenCommiteeInfo);
    }
  }, [nonCitizenCommiteeInfo]);

  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${societyId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !!societyId,
    },
  });

  const [formValues, setFormValues] = useState<FormValues>({
    name: "",
    societyNo: null,
    societyName: null,
    citizenshipStatus: 2,
    identificationType: "",
    identificationNo: null,
    visaNo: "",
    visaExpirationDate: "",
    permitNo: "",
    permitExpirationDate: "",
    tujuanDMalaysia: "",
    tempohDMalaysia: "",
    stayDurationDigit: null,
    stayDurationUnit: "",
    designationCode: "",
    summary: "",
    applicantCountryCode: null,
    activeCommitteeId: null,
    gender: null,
  });

  useEffect(() => {
    if (societyData) {
      setFormValues((prevValues) => ({
        ...prevValues,
        societyNo: societyData?.data?.data?.societyNo
          ? societyData?.data?.data?.societyNo
          : societyData?.data?.data?.applicationNo,
        societyName: societyData?.data?.data?.societyName,
      }));
    }
  }, [societyData]);

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    if (name === "identificationType" && value === "4") {
      setFormValues({
        ...formValues,
        [name!]: value,
        identificationNo: "",
      });
    } else if (name === "identificationType") {
      resetICValidation();
      setFormValues({
        ...formValues,
        [name!]: value,
      });
    } else {
      setFormValues({
        ...formValues,
        [name!]: value,
      });
    }
  };

  const { mutate: edit, isLoading: isLoadingEdit } = useCustomMutation();
  const { mutate: create, isLoading: isLoadingCreate } = useCustomMutation();

  const CreateNonCitizenList: (filteredData: any) => void = (filteredData) => {
    create(
      {
        url: `${API_URL}/society/nonCitizenCommittee/branch/create`,
        method: "post",
        values: {
          ...filteredData,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          const ajkId = data?.data?.data?.id;
          setAjkId(ajkId);
          setTimeout(() => {
            setErrors({});
            navigate(`../ahlijawatankuasa?id=${branchId}`);
          }, 5000);
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const EditNonCitizenList: (filteredData: any) => void = (filteredData) => {
    edit(
      {
        url: `${API_URL}/society/nonCitizenCommittee/branch/${memberId}`,
        method: "put",
        values: {
          ...filteredData,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          navigate(`../ahlijawatankuasa?id=${branchId}`);
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    const newErrors: { [key in keyof FormValues]?: string } = {};
    const requiredFields = [
      "name",
      "identificationType",
      "identificationNo",
      "applicantCountryCode",
      "gender",
      "tujuanDMalaysia",
      "designationCode",
      "summary",
    ] as const;

    requiredFields.forEach((field) => {
      if (!formValues[field]) {
        newErrors[field] = t("fieldRequired");
      }
    });

    // Conditional validation for duration fields when identificationType is not 4 (MyPR)
    if (Number(formValues.identificationType) !== 4) {
      if (!formValues.stayDurationDigit) {
        newErrors.stayDurationDigit = t("fieldRequired");
      }
      if (!formValues.stayDurationUnit) {
        newErrors.stayDurationUnit = t("fieldRequired");
      }
    }

    if (Object.keys(newErrors).length === 0) {
      const filteredData = Object.fromEntries(
        Object.entries(formValues).filter(([key, value]) => value !== "")
      );
      filteredData.societyId = societyId;
      filteredData.societyNo = nonCitizenCommiteeInfo.societyNo;
      filteredData.branchId = branchId;
      filteredData.branchNo = nonCitizenCommiteeInfo.branchNo;

      if (memberId) {
        filteredData.id = memberId;
        EditNonCitizenList(filteredData);
      } else {
        filteredData.isFromRegistration = 1;
        CreateNonCitizenList(filteredData);
      }
    } else {
      setErrors(newErrors);
    }
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "32px",
    borderRadius: "16px",
    fontSize: "16px",
    fontWeight: "500 !important",
  };

  const handleSenaraiAjk = () => {
    navigate(-1);
  };

  const [positionsTranslatedList, setPositionsTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);

  const [durationOptionsTranslated, setDurationOptionsTranslated] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    const newPList = OrganisationPositions.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setPositionsTranslatedList(newPList);
  }, [t]);

  useEffect(() => {
    const filteredList = IdTypes.filter(
      (item) => Number(item.value) === 4 || Number(item.value) === 5
    );

    const newOList = filteredList.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    if (newOList) {
      setIdTypeTranslatedList(newOList);
    }
  }, [t]);

  useEffect(() => {
    const newDurationList = DurationOptions.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setDurationOptionsTranslated(newDurationList);
  }, [t]);

  const [JPNError, setJPNError] = useState(false);
  const {
    userICCorrect,
    userNameMatchIC,
    triggerICValidation,
    setUserICCorrect,
    setUserNameMatchIC,
    resetICValidation,
    integrationStatus,
  } = useICValidation({
    idType: formValues?.identificationType,
    idNumber: formValues?.identificationNo,
    fullName: formValues?.name,
  });

  useEffect(() => {
    const isMyKad =
      Number(formValues?.identificationType) === 1 ||
      Number(formValues?.identificationType) === 4;
    const nameReady = formValues?.name?.trim() !== "";
    const idReady = formValues?.identificationNo?.length === 12;
    if (integrationStatus === 0 && isMyKad) {
      setJPNError(true);
    } else {
      setJPNError(false);
    }
    if (integrationStatus === 1 && isMyKad && nameReady && idReady) {
      triggerICValidation();
    }
  }, [
    formValues?.identificationType,
    formValues?.identificationNo,
    formValues?.name,
    integrationStatus,
  ]);

  const [nameHelperText, setNameHelperText] = useState<string | undefined>(
    undefined
  );

  const [idNoHelperText, setIdNoHelperText] = useState<string | undefined>(
    undefined
  );
  useEffect(() => {
    if (formValues?.identificationType === "4") {
      if (formValues.identificationNo.toString().length < 12) {
        setIdNoHelperText(t("idNumberOnlyDigits"));
      } else {
        setIdNoHelperText(
          formValues?.identificationNo.toString().length === 12 &&
            !userICCorrect
            ? t("IcDoesNotExist")
            : undefined
        );
      }

      setNameHelperText(
        formValues?.identificationNo.toString()?.length === 12 &&
          formValues?.name?.trim() !== undefined &&
          !userNameMatchIC
          ? t("invalidName")
          : undefined
      );
    } else {
      setIdNoHelperText(undefined);
      setNameHelperText(undefined);
    }
  }, [
    formValues?.identificationType,
    formValues?.identificationNo,
    formValues?.name,
    userICCorrect,
    userNameMatchIC,
  ]);

  useEffect(() => {
    const type = Number(formValues?.identificationType);
    if (type === 1 || type === 4) {
      setFormValues({
        ...formValues,
        gender: autoGenderSetByIC(
          type,
          formValues?.gender,
          formValues?.identificationNo
        ),
      });
    }
  }, [formValues?.identificationNo]);

  const [positionList, setPositionList] = useState<
    { value: number; label: string; designationCode?: string }[]
  >([]);

  const { isLoading: isLoadingPositionListRes } = useQuery({
    url: "society/nonCitizenCommittee/getPositionsList",
    filters: [
      {
        field: "societyId",
        value: societyId,
        operator: "eq",
      },
      {
        field: "branchId",
        value: branchId,
        operator: "eq",
      },
    ],
    onSuccess: (data) => {
      if (data?.data?.status === "SUCCESS") {
        if (data?.data?.data === null && data?.data?.msg) {
        }
        const newList = data?.data?.data?.map((item: any) => {
          const position = OrganisationPositions.find(
            (p) => p.value === Number(item.designationCode)
          );
          const label = position?.label
            ? `${t(position.label)}${
                item.positionHolder ? ` - ${item.positionHolder}` : ""
              }`
            : item.designationCode;

          return {
            label,
            value: Number(item?.activeCommitteeId),
            designationCode: item.designationCode,
          };
        });

        setPositionList(newList);
      }
    },
  });

  return (
    <Box
      component="form"
      noValidate
      onSubmit={handleSubmit}
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
      }}
    >
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="h6" component="h2" sx={sectionStyle}>
          {t("nonCitizenAJK")}
        </Typography>
        {JPNError ? (
          <Box sx={{ mb: 4 }}>
            <FormHelperText sx={{ color: "var(--error)" }}>
              {t("JPNError")}
            </FormHelperText>
          </Box>
        ) : null}
        <Input
          value={
            societyData?.data?.data?.societyNo
              ? societyData?.data?.data?.societyNo
              : societyData?.data?.data?.applicationNo
          }
          name="societyNo"
          onChange={handleChange}
          disabled
          required
          label={t("organizationNumber2")}
        />
        <Input
          value={societyData?.data?.data?.societyName}
          name="societyName"
          onChange={handleChange}
          disabled
          required
          label={t("organization_name")}
        />
        <Input
          value={formValues?.name ?? ""}
          name="name"
          onChange={handleChange}
          label={t("fullNameCapitalizedOnlyFirstLetter")}
          error={!userNameMatchIC}
          helperText={nameHelperText}
          disabled={JPNError}
          required
        />
        <Input
          value={2}
          name="citizenshipStatus"
          onChange={handleChange}
          label={t("citizenship")}
          required
          type="select"
          options={CitizenshipStatus.map((item) => ({
            ...item,
            label: t(item.label),
          }))}
          disabled
        />
        <Input
          value={formValues?.identificationType ?? ""}
          name="identificationType"
          onChange={handleChange}
          required
          label={t("idTypeCapitalizedOnlyFirstLetter")}
          options={idTypeTranslatedList}
          type="select"
          error={!!errors.identificationType}
          helperText={errors.identificationType}
        />
        <Input
          value={formValues?.identificationNo ?? ""}
          name="identificationNo"
          onChange={handleChange}
          required
          label={t("idNumberCapitalizedOnlyFirstLetter")}
          disabled={JPNError}
          error={!!idNoHelperText}
          helperText={idNoHelperText}
          inputProps={
            formValues?.identificationType === "4"
              ? {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  maxLength: 12,
                  minLength: 12,
                }
              : undefined
          }
          onKeyDown={
            formValues?.identificationType === "4"
              ? (e) => {
                  const allowedKeys = [
                    "Backspace",
                    "ArrowLeft",
                    "ArrowRight",
                    "Tab",
                    "Delete",
                    "Control",
                    "Meta",
                    "v",
                  ];

                  // Allows copy paste
                  if (
                    (e.ctrlKey || e.metaKey) &&
                    (e.key === "v" || e.key === "V")
                  ) {
                    return;
                  }

                  if (!/^\d$/.test(e.key) && !allowedKeys.includes(e.key)) {
                    e.preventDefault();
                  }
                }
              : undefined
          }
        />
        <Input
          required
          name="gender"
          onChange={handleChange}
          value={formValues?.gender ?? ""}
          error={!!errors.gender}
          helperText={errors.gender}
          label={t("gender")}
          type="select"
          options={GenderType.map((item) => ({
            label: t(item.translateKey),
            value: item.code,
          }))}
        />
        <Input
          value={Number(formValues.applicantCountryCode) ?? ""}
          name="applicantCountryCode"
          onChange={handleChange}
          required
          label={t("originCountry")}
          options={CountryData}
          type="select"
          error={!!errors.applicantCountryCode}
          helperText={errors.applicantCountryCode}
        />
        <Input
          value={formValues?.visaNo ?? ""}
          name="visaNo"
          onChange={handleChange}
          label={t("nomborVisa")}
          error={!!errors.visaNo}
          helperText={errors.visaNo}
        />
        <Input
          value={formatDateToDDMMYYYY(formValues.visaExpirationDate)}
          name="visaExpirationDate"
          type="date"
          onChange={handleChange}
          label={t("visaExpiryDate")}
          error={!!errors.visaExpirationDate}
          helperText={errors.visaExpirationDate}
        />
        <Input
          value={formValues?.permitNo ?? ""}
          name="permitNo"
          onChange={handleChange}
          label={t("nomborPermit")}
          error={!!errors.permitNo}
          helperText={errors.permitNo}
        />
        <Input
          value={formatDateToDDMMYYYY(formValues.permitExpirationDate)}
          name="permitExpirationDate"
          type="date"
          onChange={handleChange}
          label={t("permitExpiryDate")}
          error={!!errors.permitExpirationDate}
          helperText={errors.permitExpirationDate}
        />
        <Input
          value={formValues?.tujuanDMalaysia ?? ""}
          name="tujuanDMalaysia"
          required
          onChange={handleChange}
          label={t("tujuanDiMalaysia")}
          error={!!errors.tujuanDMalaysia}
          helperText={errors.tujuanDMalaysia}
        />
        {Number(formValues.identificationType) !== 4 && (
          <Grid container spacing={2} alignItems="center" sx={{ mb: 1 }}>
            <Grid item xs={12} sm={4}>
              <Typography
                variant="body1"
                sx={{
                  color: "#666666",
                  fontWeight: "400 !important",
                  fontSize: "14px",
                }}
              >
                {t("tempohDiMalaysia")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Input
                    value={formValues?.stayDurationDigit ?? ""}
                    name="stayDurationDigit"
                    required
                    type="text"
                    inputMode="numeric"
                    onChange={(e) => {
                      const value = e.target.value;
                      if (/^\d*$/.test(value)) {
                        setFormValues({
                          ...formValues,
                          stayDurationDigit: parseInt(value) || null,
                        });
                      }
                    }}
                    error={!!errors.stayDurationDigit}
                    helperText={errors.stayDurationDigit}
                    isLabelNoSpace={false}
                    isLabel={false}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Input
                    value={formValues?.stayDurationUnit ?? ""}
                    name="stayDurationUnit"
                    required
                    type="select"
                    onChange={handleChange}
                    options={durationOptionsTranslated}
                    error={!!errors.stayDurationUnit}
                    helperText={errors.stayDurationUnit}
                    isLabelNoSpace={false}
                    isLabel={false}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        )}
        <Input
          value={formValues?.activeCommitteeId}
          name="activeCommitteeId"
          required
          options={positionList}
          isLoadingData={isLoadingPositionListRes}
          label={t("position")}
          type="select"
          error={!!errors.designationCode}
          helperText={errors.designationCode}
          onChange={(event) => {
            const { value } = event.target;
            const selectedOption =
              positionList?.find((opt) => opt.value === value) ?? null;

            if (selectedOption?.designationCode) {
              setFormValues((prevValues) => ({
                ...prevValues,
                designationCode: selectedOption.designationCode,
              }));
            }

            setFormValues((prevValues) => ({
              ...prevValues,
              activeCommitteeId: value,
            }));
          }}
        />
        <Input
          value={formValues?.summary ?? ""}
          name="summary"
          multiline
          rows={4}
          required
          onChange={handleChange}
          label={t("importanceOfPosition2")}
          error={!!errors.summary}
          helperText={errors.summary}
        />
      </Box>
      <FileUploader
        title="ajkEligibilityCheck"
        type={DocumentUploadType.NON_CITIZEN_COMMITTEE}
        uploadAfterSubmitIndicator={ajkId}
        uploadAfterSubmit={memberId ? false : true}
        validTypes={[
          "text/plain",
          "application/rtf",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/msword",
          "application/vnd.oasis.opendocument.text",
          "application/pdf",
        ]}
        maxFileSize={25 * 1024 * 1024}
        disabled={formValues?.identificationNo === null}
        societyId={ajkId || memberId ? societyId : undefined}
        branchId={ajkId || memberId ? branchId : undefined}
        branchCommitteeId={ajkId ? ajkId : memberId ? memberId : undefined}
        showSuccessUploadNotification={false}
        icNo={formValues?.identificationNo}
      />

      <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 2 }}>
        <ButtonPrimary
          sx={{
            background: "white",
            border: "1px solid #DDDDDD",
            color: "#666666",
            boxShadow: "none",
          }}
          onClick={handleSenaraiAjk}
        >
          {t("reset")}
        </ButtonPrimary>
        <ButtonPrimary
          sx={{
            boxShadow: "none",
          }}
          disabled={
            formValues?.identificationType === "4" &&
            (JPNError ||
              formValues?.identificationNo?.length < 12 ||
              !userICCorrect ||
              !userNameMatchIC)
          }
          type="submit"
        >
          {t("update")}
        </ButtonPrimary>
      </Box>
    </Box>
  );
};

export default CreateAjkBukanWn;
