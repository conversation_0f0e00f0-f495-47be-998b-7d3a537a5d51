import React from "react";
import {Route, Outlet} from "react-router-dom";
import ListArticle from "@/pages/hebahan/senaraiArtikel";
import CheckArticle from "@/pages/hebahan/semakArtikel";
import CreateArticle from "@/pages/hebahan/createArticle";
import InternalHebahanIndex from "@/pages/hebahan";
import { registerRoutes } from "../../helpers/routeDetector";
import { RouteGuard } from "../../components/RouteGuard";

// Layout component for internal hebahan routes
const InternalHebahanLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Outlet />
  </RouteGuard>
);

// Layout component for external hebahan routes
const ExternalHebahanLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Outlet />
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example: '/hebahan-internal': 'internal',
  // Example: '/hebahan': 'external',
  // Add your route registrations here
});

export const hebahan = {
  routes: (
    <>
      <Route path="/hebahan-internal" element={<InternalHebahanLayout />}>
        <Route index element={<InternalHebahanIndex/>}/>
        <Route path="list">
          <Route index element={<ListArticle />} />
          <Route path="create">
            <Route index element={<CreateArticle />} />
          </Route>
        </Route>
        <Route path="check">
          <Route index element={<CheckArticle />} />
        </Route>
      </Route>
      <Route path="/hebahan" element={<ExternalHebahanLayout />}>
        <Route index element={<ListArticle/>}/>
        <Route path="create">
          <Route index element={<CreateArticle />} />
        </Route>
      </Route>
    </>
  )
}
