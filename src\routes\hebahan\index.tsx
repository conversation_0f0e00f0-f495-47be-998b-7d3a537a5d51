import React from "react";
import {Outlet, Route} from "react-router-dom";
import ListArticle from "@/pages/hebahan/senaraiArtikel";
import CheckArticle from "@/pages/hebahan/semakArtikel";
import CreateArticle from "@/pages/hebahan/createArticle";
import InternalHebahanIndex from "@/pages/hebahan";
import PreviewArticle from "@/pages/hebahan/previewArticle";
import { registerRoutes } from "@/helpers";
import RouteGuard from "@/components/RouteGuard";

// Layout component to wrap all training routes with protection
const PostingArticleLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Outlet />
  </RouteGuard>
);

// Layout component for internal training routes
const InternalPostingArticleLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Outlet />
  </RouteGuard>
);

registerRoutes({
  '/hebahan-internal': 'internal',
  '/hebahan-internal/list': 'internal',
  '/hebahan-internal/list/create': 'internal',
  '/hebahan-internal/list/preview/:id': 'internal',
  '/hebahan-internal/list/update/:id': 'internal',
  '/hebahan-internal/check': 'internal',
  '/hebahan-internal/report': 'internal',
  '/hebahan/create': 'external',
  // Add your route registrations here
});

export const hebahan = {
  routes: (
    <>
      <Route path="/hebahan-internal" element={<InternalPostingArticleLayout />}>
        <Route index element={<InternalHebahanIndex/>}/>
        <Route path="list">
          <Route index element={<ListArticle />} />
          <Route path="create">
            <Route index element={<CreateArticle isUpdate={false} isAdmin={true} />} />
          </Route>
          <Route path="preview/:id">
            <Route index element={<PreviewArticle />} />
          </Route>
          <Route path="update/:id">
            <Route index element={<CreateArticle isUpdate={true} isAdmin={true} />} />
          </Route>
        </Route>
        <Route path="check">
          <Route index element={<CheckArticle />} />
        </Route>
         <Route path="report">
          <Route index element={<CheckArticle />} />
        </Route>
      </Route>
      <Route path="/hebahan" element={<PostingArticleLayout />}>
        <Route index element={<ListArticle/>}/>
        <Route path="create">
          <Route index element={<CreateArticle isUpdate={false} isAdmin={false} />} />
        </Route>
      </Route>
    </>
  )
}
