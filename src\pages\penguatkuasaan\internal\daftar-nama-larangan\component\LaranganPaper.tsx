import Box from "@mui/material/Box";
import type { BoxProps } from "@mui/material";
export const LaranganPaper: React.FC<BoxProps> = ({
  sx = [],
  children,
  ...props
}) => (
  <Box
    sx={[
      {
        backgroundColor: "white",
        borderRadius: "1rem",
        p: 2,
        boxShadow: "0 12px 12px 0 rgba(234, 232, 232, 0.4)",
      },
      ...(Array.isArray(sx) ? sx : [sx]),
    ]}
    {...props}
  >
    {children}
  </Box>
);
