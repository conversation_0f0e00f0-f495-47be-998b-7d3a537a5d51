import { <PERSON><PERSON>, Button, Grid, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Typography, Fade } from "@mui/material";
import { ButtonOutline } from "../../../../components/button";
import {
  ApplicationStatusEnum,
  OrganisationPositions,
} from "../../../../helpers/enums";
import RekodPertubuhanDialog from "../../../pertubuhan/kelulusan/penubuhan-induk/RekodPertubuhanDialog";
import PaparanSemakAjkDialog from "../../../pertubuhan/kelulusan/penubuhan-induk/PaparanSemakAjkDialog";
import SenaraiAjkDialogView from "./SenaraiAjkDialogView";
import DataTable, { IColumn } from "../../../../components/datatable";
import useQuery from "../../../../helpers/hooks/useQuery";
import { getLocalStorage } from "../../../../helpers/utils";
import { setAjkPaparanData, setAllAjkList } from "@/redux/ajkReducer";
import { useDispatch } from "react-redux";
import { EyeIcon } from "@/components/icons";
import { useForm } from "react-hook-form";

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

export const SenaraiAjk = () => {
  const navigate = useNavigate();
  const [dialogSaveOpen, setDialogSaveOpen] = useState(false);
  const [dialogSenaraiOpen, setDialogSenaraiOpen] = useState(false);

  const [dialogPaparanSaveOpen, setDialogPaparanSaveOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const { t } = useTranslation();
  const { id } = useParams();
  const decodedId = atob(id || "");

  const form = useForm<any>();
  const { setValue, watch } = form;

  const dispatch = useDispatch();

  const goAJKsemakPage = () => {
    navigate(
      `/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/${decodedId}/senarai-ajk-semak`
    );
  };

  // ===============================

  function goPaparanAjk(row: any) {
    dispatch(setAjkPaparanData(row));
    navigate(
      `/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/${decodedId}/paparan-ajk`
    );
  }

  const page = watch("page") || 1;
  const pageSize = watch("pageSize") || 10;

  const pageNonCitizen = watch("pageNonCitizen") || 1;
  const pageSizeNonCitizen = watch("pageSizeNonCitizen") || 10;

  const {
    data: listCommittee,
    isLoading: isLoadingCommittee,
    refetch: refetchCitizenCommittee,
  } = useQuery({
    url: `society/committee/getAll`,
    autoFetch: true,
    filters: [
      { field: "societyId", value: decodedId, operator: "eq" },
      {
        field: "pageNo",
        operator: "eq",
        value: page,
      },
      {
        field: "pageSize",
        operator: "eq",
        value: pageSize,
      },
    ],
    onSuccess: (data) => {
      const res = data?.data?.data?.data || [];
      dispatch(setAllAjkList(res));
    },
  });

  const {
    data: listNonCitizenCommittee,
    isLoading: isLoadingNonCitizenCommittee,
    refetch: refetchNonCitizenCommittee,
  } = useQuery({
    url: `society/nonCitizenCommittee/getAll`,
    autoFetch: true,
    filters: [
      { field: "societyId", value: decodedId, operator: "eq" },
      {
        field: "pageNo",
        operator: "eq",
        value: pageNonCitizen,
      },
      {
        field: "pageSize",
        operator: "eq",
        value: pageSizeNonCitizen,
      },
    ],
  });

  const addressList = getLocalStorage("address_list", null);

  const columnsCitizen: IColumn[] = [
    {
      field: "designationCode",
      headerName: t("position"),
      align: "center",
      flex: 1,
      renderCell: ({ row }: any) => {
        return t(
          `${
            OrganisationPositions.find(
              (item) => item?.value === Number(row?.designationCode)
            )?.label || "-"
          }`
        );
      },
    },
    {
      field: "name",
      headerName: t("name"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.name ? row?.name : "-";
      },
    },
    {
      field: "identificationNo",
      headerName: t("identificationNumber"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return row.identificationNo ? row.identificationNo : "-";
      },
    },
    {
      field: "email",
      headerName: t("email"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.email ? row?.email : "-";
      },
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      headerAlign: "right",
      align: "right",
      renderCell: (params: any) => {
        const row = params.row;
        return (
          <>
            <IconButton onClick={() => goPaparanAjk(row)}>
              <EyeIcon
                sx={{
                  fontSize: "2rem",
                  width: "1rem",
                  height: "1rem",
                  color: "var(--grey-text)",
                }}
              />
            </IconButton>
            {/* <IconButton onClick={(event) => handleMenuOpen(event, row.id)}>
              <Info sx={{ color: "black" }} />
            </IconButton> */}
          </>
        );
      },
    },
  ];

  const committeeData = listCommittee?.data?.data?.data || [];
  const totalListCitizen = listCommittee?.data?.data?.total ?? 0;

  //======================

  const columnsNonCitizen: IColumn[] = [
    {
      field: "positionCode",
      headerName: t("position"),
      align: "center",
      flex: 1,
      renderCell: ({ row }: any) => {
        return t(
          `${
            OrganisationPositions.find(
              (item) => item?.value === Number(row?.designationCode)
            )?.label || "-"
          }`
        );
      },
    },
    {
      field: "name",
      headerName: t("name"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return <Box sx={{ textAlign: "center" }}>{row?.name}</Box>;
      },
    },
    {
      field: "applicationStatusCode",
      headerName: t("statusPermohonan"),
      flex: 1,
      renderCell: (row: any) => {
        return (
          <Box sx={{ textAlign: "center" }}>
            {" "}
            {t(
              ApplicationStatusEnum[
                (row?.applicationStatusCode?.data
                  ?.statusCode as keyof typeof ApplicationStatusEnum) || "0"
              ]
            )}
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      headerAlign: "right",
      align: "right",
      renderCell: (params: any) => {
        const row = params.row;
        return (
          <>
            <IconButton onClick={() => goPaparanAjk(row)}>
              <EyeIcon
                sx={{
                  fontSize: "2rem",
                  width: "1rem",
                  height: "1rem",
                  color: "var(--grey-text)",
                }}
              />
            </IconButton>
            {/* <IconButton onClick={(event) => handleMenuOpen(event, row.id)}>
              <Info sx={{ color: "black" }} />
            </IconButton> */}
          </>
        );
      },
    },
  ];

  const nonCitizenCommitteeData =
    listNonCitizenCommittee?.data?.data?.data || [];
  const totalListNonCitizen = listNonCitizenCommittee?.data?.data?.total ?? 0;

  return (
    <Box>
      <Fade in={true} timeout={500}>
        <Box sx={{ mt: 2, mb: 2 }}>
          <Box
            sx={{
              p: 3,
              mb: 3,
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
            }}
          >
            <Typography sx={subTitleStyle}>{t("ajkList")}</Typography>
            <Box
              sx={{
                display: "flex",
                alignItems: "flex-start",
                justifyContent: "flex-end",
                gap: 2,
              }}
            >
              <Alert
                severity="info"
                sx={{
                  display: anchorEl ? "block" : "none",
                  mb: 2,
                  backgroundColor: "#FF000080",
                  py: 0,
                  color: "#fff",
                  fontSize: "12px",
                  fontWeight: "400",
                  borderRadius: "12px",
                  border: "2px solid #FF0000",
                }}
                icon={<Box sx={{ display: "none" }}></Box>}
              >
                <Grid container spacing={2} sx={{ alignItems: "center" }}>
                  <Grid item xs={12} sm={12}>
                    <li>
                      Individu ini terlibat dengan 2 pertubuhan:{" "}
                      <Button
                        sx={{
                          color: "#402DFF",
                          textDecoration: "underline",
                          p: 0,
                          fontSize: "12px",
                        }}
                        onClick={() => setDialogSaveOpen(true)}
                      >
                        {t("kliksemak")}
                      </Button>
                    </li>

                    <li>
                      {t("namaDisenaraiKelabu")} :{" "}
                      <Button
                        sx={{
                          color: "#402DFF",
                          textDecoration: "underline",
                          fontSize: "12px",
                        }}
                        onClick={() => setDialogSaveOpen(true)}
                      >
                        {t("kliksemak")}
                      </Button>
                    </li>
                  </Grid>
                </Grid>
              </Alert>

              <Box
                sx={{
                  background: "var(--primary-color)",
                  cursor: "pointer",
                  display: "flex",
                  justifyContent: "center",
                  alignContent: "center",
                  borderRadius: "10px",
                  p: 1,
                  flexShrink: 0,
                  height: "100%",
                }}
              >
                <img width={26} height={26} src="/addkuiri.svg" alt="" />
              </Box>
              <ButtonOutline
                startIcon={<></>}
                onClick={goAJKsemakPage}
                sx={{ alignSelf: "flex-start", px: 2, py: 1 }}
              >
                {t("viewAjk")}
              </ButtonOutline>
            </Box>
            <DataTable
              columns={columnsCitizen}
              rows={committeeData}
              page={page}
              rowsPerPage={pageSize}
              totalCount={totalListCitizen}
              onPageChange={(newPage) => setValue("page", newPage)}
              onPageSizeChange={(newPageSize) => {
                setValue("page", 1);
                setValue("pageSize", newPageSize);
              }}
              isLoading={isLoadingCommittee}
            />
          </Box>

          <Box sx={{ display: "flex", flexDirection: "column", mb: 2, gap: 2 }}>
            <Box
              sx={{
                p: 3,
                mb: 3,
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography sx={subTitleStyle}>{t("nonCitizenAJK")}</Typography>

              <DataTable
                columns={columnsNonCitizen}
                rows={nonCitizenCommitteeData}
                page={pageNonCitizen}
                rowsPerPage={pageSizeNonCitizen}
                totalCount={totalListNonCitizen}
                onPageChange={(newPage) => setValue("pageNonCitizen", newPage)}
                onPageSizeChange={(newPageSize) => {
                  setValue("pageNonCitizen", 1);
                  setValue("pageSizeNonCitizen", newPageSize);
                }}
                isLoading={isLoadingNonCitizenCommittee}
              />
            </Box>
          </Box>
        </Box>
      </Fade>
      {/* <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2, mr: 7 }}>
        <ButtonPrimary>{t("simpan")}</ButtonPrimary>
      </Box> */}
      <SenaraiAjkDialogView
        open={dialogSenaraiOpen}
        onClose={() => setDialogSenaraiOpen(false)}
      />
      <RekodPertubuhanDialog
        open={dialogSaveOpen}
        onClose={() => setDialogSaveOpen(false)}
      />
      <PaparanSemakAjkDialog
        open={dialogPaparanSaveOpen}
        onClose={() => setDialogPaparanSaveOpen(false)}
      />
    </Box>
  );
};

export default SenaraiAjk;
