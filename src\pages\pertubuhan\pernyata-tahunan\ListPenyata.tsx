import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  Typography,
  IconButton,
  Grid,
  FormControl,
  Select,
  Stack,
  MenuItem,
  CircularProgress,
} from "@mui/material";
import Searchbar from "../../../components/bar/Searchbar";
import { ButtonPrimary } from "../../../components/button";
import ALiranTugas from "../AliranTugas";
import useQuery from "../../../helpers/hooks/useQuery";
import {
  ApplicationStatusEnum,
  COMMITTEE_TASK_TYPE,
} from "../../../helpers/enums";
import { DokumenIcon, EditIcon, EyeIcon } from "../../../components/icons";
import { TrashIcon } from "../../../components/icons";
import ConfirmationDialog from "../../../components/dialog/confirm";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../api";
import { usejawatankuasaContext } from "../ajk/jawatankuasa/jawatankuasaProvider";
import {
  setIsViewStatement,
  setStatementDataRedux,
} from "@/redux/statementDataReducer";
import { useDispatch, useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import dayjs from "@/helpers/dayjs";
import { DataTable, IColumn } from "@/components";
import { CrudFilter } from "@refinedev/core";
import { debounce } from "lodash";
import { downloadFile } from "@/helpers";

const ListPenyata: React.FC = () => {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const renderFormField = (
    label: string,
    component: React.ReactNode,
    required = false
  ) => (
    <Grid container spacing={2} alignItems="center" sx={{ mb: 1 }}>
      <Grid item xs={12} sm={3}>
        <Typography
          variant="body1"
          sx={{ color: "#666666", fontWeight: "400 !important", fontSize: 14 }}
        >
          {label}
          {required && <span style={{ color: "red" }}>*</span>}
        </Typography>
      </Grid>
      <Grid item xs={12} sm={9}>
        {component}
      </Grid>
    </Grid>
  );

  const { id: societyId } = useParams();
  const { t } = useTranslation();
  const [isValid, setIsValid] = useState(true); // Track validation state

  const navigate = useNavigate();
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const [statementId, setStatementId] = useState<string | number>(0);

  type Penyata = {
    id: string | number;
    statementId: string | number;
    societyId: string | number;
    statementYear: number;
    applicationStatusCode: number;
    submissionDate?: string;
  };
  const [downloadingStatementId, setDownloadingStatementId] = useState<
    string | number | null
  >(null);
  const [ListPenyata, SetListPenyata] = useState<Penyata[]>([]);

  const [currentYearsList, setCurrentYearsList] = useState<number[]>([]);
  const [total, setTotal] = useState(0);
  const [isAjkMember, setIsAjkMember] = useState<boolean | undefined>(false);

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  const isBlackListed = societyDataRedux?.subStatusCode === "003";
  const isManager = useSelector(getUserPermission);
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  // const isAccessible = !isBlackListed && (isManager || isAliranTugasAccess);
  const isAccessible = !isBlackListed && (isManager || isAliranTugasAccess);

  const {
    society,
    module,
    setModule,
    fetchAliranTugas,
    fetchAliranTugasAccess,
    fetchAliranTugasStatus,
    fetchAjkList,
  } = usejawatankuasaContext();

  const [shouldFetch] = useState<boolean>(true);

  useEffect(() => {
    fetchAjkList();
    fetchAliranTugasAccess(module);
    fetchAliranTugasStatus(module);
    fetchAliranTugas(module);
  }, [module, shouldFetch]);

  useEffect(() => {
    setModule(COMMITTEE_TASK_TYPE.PENYATAAN_TAHUNAN);
  }, [module]);

  const { mutate: addPenyata } = useCustomMutation();

  const currentYear = new Date().getFullYear();
  const establishedYear = society?.registeredDate?.split("-")[0] || "2013";

  let years: (number | { label: number; value: number; color: string })[] = [];

  if (establishedYear) {
    const todayDate = new Date().toISOString().slice(0, 10);
    const est = parseInt(establishedYear);
    const endYear = currentYear - 1;
    years = Array.from({ length: endYear - est + 1 }, (_, i) => endYear - i);
    const systemYear = new Date().getFullYear();
    if (systemYear > currentYear) {
      years.unshift(currentYear);
    }

    //filter submitted year and add color indicator
    // @ts-expect-error
    years = years
      .filter((year) => !currentYearsList.includes(year as number))
      .map((year) => {
        const deadline = dayjs(`${Number(year) + 1}-03-01`);
        const color = dayjs(todayDate).isAfter(deadline)
          ? "#FF0000"
          : "#1fde15";
        return { label: year, value: year, color };
      });
  }

  const [yearSelected, setYearSelected] = useState<number>(0);

  const handleAktiviti = () => {
    if (yearSelected == 0) {
      setIsValid(false);
      return;
    }

    addPenyata(
      {
        url: `${API_URL}/society/statement/general/create`,
        method: "post",
        values: {
          statementYear: yearSelected,
          societyId: societyId,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: (data) => {
          refetchStatementList();
          const statementId = data?.data?.data?.statementId || null;
          setStatementId(statementId);
          dispatch(
            setStatementDataRedux({
              statementId: statementId,
              statementYear: yearSelected,
              societyId: societyId,
            })
          );
        },
      }
    );
  };

  const handleConfirmDeletePenyata = (statementId: string | number) => {
    setStatementId(statementId);
    setOpenConfirm(true);
  };

  const { mutate: deletePenyata, isLoading: isLoadingDeletePenyata } =
    useCustomMutation();

  const handleDeletePenyata = () => {
    deletePenyata(
      {
        url: `${API_URL}/society/statement/${statementId}/delete`,
        method: "delete",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          refetchStatementList();
          setOpenConfirm(false);
        },
      }
    );
  };
  const dispatch = useDispatch();

  const handleEditPenyata = (data: Penyata) => {
    dispatch(setStatementDataRedux(data));
    navigate("penyata-tahunan-agung");
  };

  const {
    data: statementListData,
    isLoading: statementListIsLoading,
    refetch: refetchStatementList,
  } = useQuery({
    url: "society/statement/general/list",
    filters: [{ field: "societyId", operator: "eq", value: societyId }],
    autoFetch: false,
    onSuccess: (data) => {
      const statementData = data?.data?.data?.data || [];
      const total = data?.data?.data?.total;
      setTotal(total);
      if (statementData) {
        SetListPenyata(statementData);
        if (statementData.length > 0) {
          //Get current year list
          const yearList = statementData.map((i: any) => i.statementYear);
          setCurrentYearsList(yearList);
        }
      }
    },
  });

  useEffect(() => {
    refetchStatementList();
  }, []);

  useEffect(() => {
    if (!societyId) {
      return;
    }
  }, []);

  const handleSearch = debounce((query: any) => {
    const filters: CrudFilter[] = [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "searchQuery", operator: "eq", value: query },
    ];
    refetchStatementList({ filters });
  }, 300);

  const handleChangePage = (newPage: number) => {
    const filters: CrudFilter[] = [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: newPage, operator: "eq" },
    ];
    setPage(newPage);
    refetchStatementList({ filters });
  };

  const handlePageSizeChange = (newPageSize: number) => {
    const filters: CrudFilter[] = [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "pageSize", value: newPageSize, operator: "eq" },
      { field: "pageNo", value: page, operator: "eq" },
    ];
    setPageSize(newPageSize);
    refetchStatementList({ filters });
  };

  const getStatusColor = (
    status: number,
    statementYear: number,
    submissionDate?: string
  ): string => {
    // 1: "BELUM DIHANTAR",
    // 17: "SELESAI",
    const todayDate = new Date().toISOString().slice(0, 10);
    const deadlineBelum = dayjs(`${Number(statementYear) + 1}-03-01`);
    if (Number(status) === 1) {
      return dayjs(todayDate).isAfter(deadlineBelum) ? "#FF0000" : "#1fde15";
    }
    return "000";
  };

  const {
    data: DownloadStatement,
    isLoading: isDownloadtatementLoading,
    refetch: fetchDownloadStatement,
  } = useQuery({
    url: `society/statement/exportPdfV2`,
    autoFetch: false,
    queryOptions: {
      onError(err) {
        setDownloadingStatementId(null);
      },
    },
    onSuccess: (data) => {
      const file = data?.data as unknown as string;
      downloadFile({
        data: file,
        name: `ANNUAL STATEMENT`,
      });
      setDownloadingStatementId(null);
    },
  });

  const handleCetak = ({ statementId }: { statementId: string | number }) => {
    setDownloadingStatementId(statementId);
    fetchDownloadStatement({
      filters: [
        { field: "societyId", value: societyId, operator: "eq" },
        { field: "statementId", value: statementId, operator: "eq" },
      ],
    });
  };

  const {
    data: isAjkMemeberData,
    isLoading: isAjkMemberDataisLoading,
    refetch: refetchCheckIsAjkMember,
  } = useQuery({
    url: "society/committee/isSocietyMember",
    filters: [{ field: "societyId", operator: "eq", value: societyId }],
    onSuccess: (data) => {
      setIsAjkMember(data?.data?.data);
    },
  });

  ///
  const columns: IColumn[] = [
    {
      field: `${t("tahunPenyata")}`,
      headerName: `${t("tahunPenyata")}`,
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        let checkStatusColor = getStatusColor(
          row.applicationStatusCode,
          row.statementYear,
          row.submissionDate
        );
        return (
          <Box
            {...(isAccessible
              ? {
                  sx: {
                    color: checkStatusColor,
                  },
                }
              : {})}
          >{`Penyata tahunan tahun ${row.statementYear}`}</Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: `${t("statusPermohonan")}`,
      headerName: `${t("statusPermohonan")}`,
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return <Box>{t(ApplicationStatusEnum[row.applicationStatusCode])}</Box>;
      },
      cellClassName: "custom-cell",
    },
    {
      field: `${t("submissionDate")}`,
      headerName: `${t("submissionDate")}`,
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return <Box>{row?.submissionDate ? row.submissionDate : "-"}</Box>;
      },
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      headerName: t("action"),
      align: "center",
      renderCell: ({ row }: any) => {
        const status = row.applicationStatusCode;
        const isSelesai = status === "17";
        const isDownloading = downloadingStatementId === row.statementId;
        return (
          <>
            {isAccessible ? (
              <>
                {isSelesai && isAjkMember ? (
                  <>
                    <IconButton
                      onClick={() => {
                        dispatch(setIsViewStatement(true));
                        handleEditPenyata(row);
                      }}
                    >
                      <EyeIcon sx={{ color: "var(--primary-color)" }} />
                    </IconButton>
                    <IconButton>
                      {isDownloading ? (
                        <CircularProgress size={12} />
                      ) : (
                        <DokumenIcon
                          sx={{
                            color: "var(--primary-color)",
                            width: "11px",
                            height: "12px",
                          }}
                          onClick={() =>
                            handleCetak({ statementId: row.statementId })
                          }
                        />
                      )}
                    </IconButton>
                  </>
                ) : (
                  <>
                    <IconButton
                      onClick={() => {
                        dispatch(setIsViewStatement(false));
                        handleEditPenyata(row);
                      }}
                    >
                      <EditIcon sx={{ color: "var(--primary-color)" }} />
                    </IconButton>
                    <IconButton
                      onClick={() =>
                        handleConfirmDeletePenyata(row.statementId)
                      }
                    >
                      <TrashIcon sx={{ color: "red" }} />
                    </IconButton>
                  </>
                )}
              </>
            ) : isSelesai && isAjkMember ? (
              <>
                <IconButton
                  onClick={() => {
                    dispatch(setIsViewStatement(true));
                    handleEditPenyata(row);
                  }}
                >
                  <EyeIcon sx={{ color: "var(--primary-color)" }} />
                </IconButton>
                <IconButton>
                  {isDownloading ? (
                    <CircularProgress size={12} />
                  ) : (
                    <DokumenIcon
                      sx={{
                        color: "var(--primary-color)",
                        width: "11px",
                        height: "12px",
                      }}
                      onClick={() =>
                        handleCetak({ statementId: row.statementId })
                      }
                    />
                  )}
                </IconButton>
              </>
            ) : (
              "-"
            )}
          </>
        );
      },
      cellClassName: "custom-cell",
    },
  ];

  const apiIsLoading = statementListIsLoading || isAjkMemberDataisLoading;

  if (apiIsLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "300px",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <>
      <Box
        mt={5}
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid container spacing={2}>
          <Searchbar
            xs={9}
            text={t("annualStatement")}
            title={false}
            filter={false}
            onSearchChange={(query) => handleSearch(query)} // Handle search input
          />
        </Grid>

        <DataTable
          columns={columns}
          rows={ListPenyata}
          isLoading={statementListIsLoading}
          page={page}
          rowsPerPage={pageSize}
          totalCount={total}
          onPageChange={handleChangePage}
          onPageSizeChange={handlePageSizeChange}
        />
      </Box>

      {isAccessible && (
        <>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                background: "white",
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
                mb: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("tambahPenyataTahunan")}
              </Typography>

              {years
                ? renderFormField(
                    t("tambahPenyataTahunan"),
                    <FormControl fullWidth required>
                      <Select
                        error={!isValid}
                        size="small"
                        displayEmpty
                        required
                        value={yearSelected}
                        onChange={(e) =>
                          setYearSelected(e.target.value as number)
                        }
                        renderValue={(selected) => {
                          return !selected
                            ? t("pleaseSelect")
                            : (selected as number);
                        }}
                      >
                        <MenuItem value="0" disabled>
                          {t("pleaseSelect")}
                        </MenuItem>
                        {years.map((data: any) => (
                          <MenuItem
                            sx={{ color: data.color }}
                            key={data.label}
                            value={data.value}
                          >
                            {data.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>,
                    true
                  )
                : null}

              <Stack
                direction="row"
                spacing={2}
                mt={2}
                sx={{ pl: 1, width: "100%" }}
                justifyContent="flex-end"
              >
                <ButtonPrimary onClick={handleAktiviti}>
                  {t("add")}
                </ButtonPrimary>
              </Stack>
            </Box>
          </Box>
          {isManager && (
            <ALiranTugas
              module={COMMITTEE_TASK_TYPE.PENYATAAN_TAHUNAN}
              confirmationMessage={(data) =>
                t(
                  `areYouSureFlowManagementTaskToAnotherCommittee_${data.module}`,
                  {
                    name: `<b>${data.ajkName}</b>`,
                  }
                )
              }
            />
          )}
        </>
      )}

      <ConfirmationDialog
        status={1}
        open={openConfirm}
        onClose={() => setOpenConfirm(false)}
        title={t("confirmDeletePenyata")}
        message={`${t("confirmDeletePenyata")}`}
        onConfirm={handleDeletePenyata}
        onCancel={() => setOpenConfirm(false)}
        isMutation={isLoadingDeletePenyata}
      />
    </>
  );
};

export default ListPenyata;
