import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>hapter } from "@/pages/internal-training/createStepTwo";
import { Box, Grid, IconButton, TextField, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import DurationComponent from "@/pages/internal-training/durationComponent";
import { DeleteIcon } from "@/components/icons/delete";
import { TrainingAddIcon } from "@/components/icons/trainingAdd";
import { ButtonOutline, ButtonPrimary, DialogConfirmation } from "@/components";
import ChapterFragment from "@/pages/internal-training/chapterFragment";
import QuestionFragment from "@/pages/internal-training/questionFragment";
import { useCustom, useCustomMutation, useNotification } from "@refinedev/core";
import { API_URL } from "@/api";
import ConfirmationDialog from "@/pages/internal-training/confirmDialog";
import { useNavigate } from "react-router-dom";
import { TrainingFormProps } from "./createStepOne";

export interface TrainingAnswer {
  answer: string,
  correct: boolean,
  sequenceOrder: number,
}

export interface TrainingQuestion {
  id: number,
  question: string,
  isUpdated: boolean,
  error: boolean,
  answers: TrainingAnswer[]
}

const CreateStepThree: React.FC<TrainingFormProps> = ({
  headerStyle,
  labelStyle,
  borderStyle,
  handleNext,
  courseId,
  isUpdate
}) => {

  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { open: openNotification } = useNotification();
  const [openModal, setOpenModal] = useState(false);
  const [totalQuestion, setTotalQuestion] = useState<TrainingQuestion[]>([{
    id: 0,
    question: "",
    isUpdated: false,
    error: false,
    answers: [],
  }])

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!formData.no) errors.no = t("fieldRequired");
    if (!formData.timeLimit) errors.timeLimit = t("fieldRequired");
    if (!formData.passingScore) errors.passingScore = t("fieldRequired");
    return errors;
  };

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState({
    no: 1,
    timeLimit: 0,
    passingScore: 0,
    isUpdated: false,
    error: false,
  });

  const [hour, setHour] = useState(0)
  const [minute, setMinute] = useState(0)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let { name, value } = e.target;
    if (name === "no" || name === "passingScore") {
      value = value.replace(/^0+/, '');
    }
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
      isUpdated: true,
    }));
    setFormErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const hourCallback = (e: number) => {
    setHour(e);
  }

  const minuteCallback = (e: number) => {
    setMinute(e);
  }

  useEffect(() => {
    setFormData((prevState) => ({
      ...prevState,
      timeLimit: hour * 60 + minute,
    }));
  }, [hour, minute]);

  const handleAddChapter = (e: React.MouseEvent<HTMLButtonElement>) => {
    const temp = {
      id: 0,
      question: "",
      isUpdated: false,
      error: false,
      answers: [],
    }
    totalQuestion.push(temp)
    const newArray = totalQuestion.slice();
    setTotalQuestion(newArray)
    setFormData((prevState) => ({
      ...prevState,
      quizNo: newArray.length,
    }));
  }

  const handleDeleteChapter = (i: number, id: number) => {
    totalQuestion.splice(i, 1);
    const newArray = totalQuestion.slice();
    setTotalQuestion(newArray);
    setFormData((prevState) => ({
      ...prevState,
      quizNo: newArray.length,
    }));
    if(id) DeleteQuestion(id);
  }

  const handleSaveDraft = (e: React.MouseEvent<HTMLButtonElement>) => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    console.log(trainingQuiz.id);
    if (trainingQuiz?.id && formData.isUpdated) EditQuiz();
    else if (trainingQuiz?.id && !formData.isUpdated) {
      totalQuestion.map((e, i) => {
        if (e.id && e.isUpdated) {
          EditQuestion(trainingQuiz?.id, i)
        } else if (!e.id) {
          CreateQuestion(trainingQuiz?.id, i)
        } else {
          if (i === totalQuestion.length - 1) {
            openNotification?.({
              type: "success",
              message: t("noChanges"),
              //description: "Failed to fetch feedback answers",
            });
          }
        }
      })
    }
    else if (!trainingQuiz?.id) CreateQuiz();
    else {
      openNotification?.({
        type: "success",
        message: t("noChanges"),
        //description: "Failed to fetch feedback answers",
      });
    }
  }

  const handleSave = () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    if (formData.error) {
      return;
    }
    if (trainingQuiz?.id && formData.isUpdated) EditQuiz();
    else if (trainingQuiz?.id && !formData.isUpdated) {
      totalQuestion.map((e, i) => {
        if (e.id && e.isUpdated) {
          EditQuestion(trainingQuiz?.id, i)
        } else if (!e.id) {
          CreateQuestion(trainingQuiz?.id, i)
        }
      })
    }
    else if (!trainingQuiz?.id) CreateQuiz();
    else {
      //navigate("/latihan-internal");
    }
  }

  const { mutate: createQuiz, isLoading: isQuizLoadingCreate } = useCustomMutation();
  const CreateQuiz = (): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    createQuiz(
      {
        url: `${API_URL}/society/admin/training/quiz`,
        method: "post",
        values: {
          trainingCourseId: courseId,
          title: "E-ROSES Basics Quiz",
          description: "Test your knowledge of E-ROSES basics",
          isMandatory: true,
          minScore: formData.passingScore,
          timeLimitMinutes: formData.timeLimit,
          sequenceOrder: 1,
          quizNo: formData.no
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            //handleNext(courseId!,"pelajaran");
            totalQuestion.map((e, i) => CreateQuestion(data?.data?.data, i))
            return {
              message: t("TRAINING_QUIZ_CREATED"),
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { mutate: editQuiz, isLoading: isQuizLoadingEdit } = useCustomMutation();
  const EditQuiz = (): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    editQuiz(
      {
        url: `${API_URL}/society/admin/training/quiz`,
        method: "put",
        values: {
          trainingCourseId: courseId,
          title: "E-ROSES Basics Quiz",
          description: "Test your knowledge of E-ROSES basics",
          isMandatory: true,
          minScore: formData.passingScore,
          timeLimitMinutes: formData.timeLimit,
          sequenceOrder: 1,
          quizNo: formData.no
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            //handleNext(courseId!,"pelajaran");
            totalQuestion.map((e, i) => {
              if (e.id != 0 && e.isUpdated) {
                EditQuestion(data?.data?.data, i)
              } else if (e.id == 0) {
                CreateQuestion(data?.data?.data, i)
              } else {
                //navigate("/latihan-internal");
              }
            })
            return {
              message: t("TRAINING_QUIZ_UPDATED"),
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { mutate: deleteQuestion, isLoading: isQuestionLoadingDelete } = useCustomMutation();
  const DeleteQuestion = (id: number): void => {
    deleteQuestion(
      {
        url: `${API_URL}/society/admin/training/quiz/questions/${id}`,
        method: "delete",
        values: {
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            return {
              message: t("TRAINING_QUIZ_QUESTION_DELETED"),
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { mutate: createQuestion, isLoading: isQuestionLoadingCreate } = useCustomMutation();
  const CreateQuestion = (quizId: number, i: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    const answers = totalQuestion[i].answers.map((e, i) => {
      return {
        optionText: e.answer,
        isCorrect: e.correct,
        sequenceOrder: e.sequenceOrder,
      }
    })
    createQuestion(
      {
        url: `${API_URL}/society/admin/training/quiz/questions`,
        method: "post",
        values: {
          trainingQuizId: quizId,
          questionText: totalQuestion[i].question,
          questionType: "MULTIPLE_CHOICE",
          points: 10,
          sequenceOrder: i + 1,
          options: answers
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            //navigate("/latihan-internal");
            return {
              message: t("TRAINING_QUIZ_QUESTION_CREATED"),
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { mutate: editQuestion, isLoading: isQuestionLoadingEdit } = useCustomMutation();
  const EditQuestion = (quizId: number, i: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    const answers = totalQuestion[i].answers.map((e, i) => {
      return {
        optionText: e.answer,
        isCorrect: e.correct,
        sequenceOrder: e.sequenceOrder
      }
    })
    editQuestion(
      {
        url: `${API_URL}/society/admin/training/quiz/questions`,
        method: "put",
        values: {
          id: totalQuestion[i].id,
          trainingQuizId: quizId,
          questionText: totalQuestion[i].question,
          questionType: "MULTIPLE_CHOICE",
          points: 10,
          sequenceOrder: i + 1,
          options: answers
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            //navigate("/latihan-internal");
            return {
              message: t("TRAINING_QUIZ_QUESTION_UPDATED"),
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { data: trainingData, isLoading: isTrainingLoading } = useCustom({
    url: `${API_URL}/society/admin/training/courses/${courseId}/quiz`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: isUpdate || courseId != 0,
      retry: false,
      cacheTime: 0,
    },
  });

  const trainingQuiz = trainingData?.data?.data || {};
  console.log("trainingQuiz", trainingData, isTrainingLoading)

  useEffect(() => {
    if ((isUpdate || courseId != 0) && Object.keys(trainingQuiz).length > 0) {
      const temp = {
        id: trainingQuiz.id ?? 0,
        no: trainingQuiz.quizNo,
        timeLimit: trainingQuiz.timeLimitMinutes,
        passingScore: trainingQuiz.minScore,
        isUpdated: false,
        error: false,
      }
      if (trainingQuiz.timeLimitMinutes > 59) {
        const tempH = Math.floor(trainingQuiz.timeLimitMinutes / 60);
        setHour(tempH)
      }
      setMinute(trainingQuiz.timeLimitMinutes % 60);
      setFormData(temp);
      const tempArr = trainingQuiz.questions.map((q: any) => {
        const tempQ = {
          id: q.id,
          question: q.questionText,
          isUpdated: false,
          answers: q.options.map((o: any) => {
            const tempA = {
              answer: o.optionText,
              correct: o.isCorrect,
              sequenceOrder: o.sequenceOrder,
            }
            return tempA
          }),
        }
        return tempQ;
      });
      console.log("tempArr", tempArr);
      setTotalQuestion(tempArr);
    }
  }, [trainingData]);

  const handleDataChange = (i: number, data: TrainingQuestion) => {
    //totalChapter[i].id = data.id;
    totalQuestion[i].question = data.question;
    totalQuestion[i].answers = data.answers;
    totalQuestion[i].isUpdated = data.isUpdated;
    setTotalQuestion(totalQuestion)
  }

  return (<>
    <Box sx={{ display: "flex" }}>
      <Box sx={{ width: "85%", }}>
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Box
            sx={borderStyle}
          >
            <Typography
              sx={headerStyle}
            >
              Ketetapan Quiz
            </Typography>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("trainingQuizNo")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size={"small"}
                  fullWidth
                  required
                  name="no"
                  value={formData.no}
                  error={!!formErrors.no}
                  helperText={formErrors.no}
                  type={"number"}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    if (/[1-9]/.test(e.target.value)) {
                      handleInputChange(e)
                    }
                  }}
                />
              </Grid>
              <DurationComponent setHour={setHour} setMinute={setMinute}
                labelStyle={labelStyle} hour={hour} minute={minute}
                minuteError={formErrors.timeLimit} />
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("trainingMinScore")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={6} sm={1}>
                <TextField
                  size={"small"}
                  fullWidth
                  required
                  type={"number"}
                  name="passingScore"
                  value={formData.passingScore}
                  error={!!formErrors.passingScore}
                  helperText={formErrors.passingScore}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    if (/[1-9]/.test(e.target.value) && parseInt(e.target.value) <= 100) {
                      handleInputChange(e)
                    }
                  }}
                />
              </Grid>
              <Grid item xs={6} sm={1}>
                <Typography sx={labelStyle}>
                  {`/ 100%`}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>

              </Grid>
            </Grid>
          </Box>
        </Box>
        {totalQuestion.map((e: TrainingQuestion, index) => {
          return (<Box
            key={index}
            sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              //display: "inline",
              px: 2,
              py: 2,
              mb: 1,
            }}
          >
            <QuestionFragment no={index + 1} headerStyle={headerStyle} borderStyle={borderStyle}
              labelStyle={labelStyle} handleDataChange={handleDataChange} data={e} />
            {index === totalQuestion.length - 1 ?
              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonOutline
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    width: "auto",
                  }}
                  onClick={handleSaveDraft}
                >
                  {t("save")}
                </ButtonOutline>
                <ButtonPrimary
                  variant="contained"
                  sx={{
                    width: "auto",
                  }}
                  onClick={(e) => setOpenModal(true)}
                //disabled={true}
                >
                  {t("next")}
                </ButtonPrimary>
              </Grid> : <></>}
          </Box>)
        })}
      </Box>
      <Box sx={{ width: "15%" }}>
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
            ml: 2,
          }}
        >
          {totalQuestion.map((e, i) => {
            return <Grid container spacing={1} key={i}>
              <Grid item xs={10}>
                <Box
                  sx={borderStyle}
                >
                  <Typography key={i} sx={{
                    fontSize: 14,
                    color: "#666666",
                    fontWeight: "400 !important",
                  }}>
                    {`${t("question")} ${i + 1}`}
                  </Typography>
                </Box></Grid>
              {i > 0 ?
                <Grid item xs={2}>
                  <IconButton
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      alignContent: "center",
                      mt: 1
                    }}
                    onClick={() => {
                      handleDeleteChapter(i, e.id)
                    }}
                  >
                    <DeleteIcon
                      sx={{
                        color: "#FF0000",
                      }}
                    />
                  </IconButton></Grid> : <Grid item xs={2}></Grid>}
            </Grid>
          })}
          <Box>
            <Grid container sx={{ mt: 1 }}>
              <Grid item xs={3}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    borderRadius: 2.5,
                    backgroundColor: "#0CA6A6",
                    width: 36,
                    height: 36,
                  }}
                >
                  <TrainingAddIcon sx={{ color: "#FFF", }} />
                </Box>
              </Grid>
              <Grid item xs={9}>
                <ButtonOutline
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    width: "75%",
                    minWidth: "75%"
                  }}
                  onClick={handleAddChapter}
                >
                  {t("addQuestion")}
                </ButtonOutline>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </Box>
    </Box>
    <DialogConfirmation
      open={openModal}
      onClose={() => {
        setOpenModal(false);
      }}
      onAction={handleSave}
      isMutating={false}
      onConfirmationText={"Adakah anda pasti untuk mencipta latihan ini?"}
    />
  </>
  );
}

export default CreateStepThree;
