import React from "react";
import Box from "@mui/material/Box/Box";
import {Typography} from "@mui/material";
import {ButtonPrimary} from "@/components";
import {useTranslation} from "react-i18next";
import {useNavigate} from "react-router-dom";



const InternalTrainingDashboardSidebar: React.FC = () => {
  const navigate = useNavigate();
  const {t, i18n} = useTranslation();


  return (<>
    <Box sx={{width: "15%", mt:4,pt:4}}>
      <Box>
      <Box
        sx={{
          //flex: 5,
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Typography
          sx={{
            color: "#1DC1C1",
            //pt: 3,
            mb:5,
            fontWeight: "500",
            fontSize: 14,
          }}
        >
          {t("createTraining")}
        </Typography>
        <ButtonPrimary
          variant="outlined"
          sx={{
            bgcolor: "#0CA6A6",
            "&:hover": {bgcolor: "#0CA6A6"},
            color: "#fff",
            fontWeight: "400",
          }}
          onClick={() => {
            navigate("/latihan-internal/create");
          }}
        >
          {t("create")}
        </ButtonPrimary>
      </Box>
        <Box
          sx={{
            //flex: 5,
            borderRadius: 2.5,
            backgroundColor: "#fff",
            //flex: 5,
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Typography
            sx={{
              color: "#1DC1C1",
              //pt: 3,
              mb:3,
              fontWeight: "500",
              fontSize: 14,
            }}
          >
            Peserta Latihan
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              //pt: 3,
              mb:1,
              fontWeight: "500",
              fontSize: 40,
              letterSpacing: -2,
            }}
          >
            0 %
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              //pt: 3,
              mb:3,
              fontWeight: "400",
              fontSize: 10,
            }}
          >
            Peningkatan berbanding minggu lalu
          </Typography>
        </Box>
        <Box
          sx={{
            //flex: 5,
            borderRadius: 2.5,
            backgroundColor: "#fff",
            //flex: 5,
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Typography
            sx={{
              color: "#1DC1C1",
              //pt: 3,
              mb:3,
              fontWeight: "500",
              fontSize: 14,
            }}
          >
            Peserta Latihan
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              //pt: 3,
              mb:1,
              fontWeight: "500",
              fontSize: 40,
              letterSpacing: -2,
            }}
          >
            0 %
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              //pt: 3,
              mb:3,
              fontWeight: "400",
              fontSize: 10,
            }}
          >
            Peningkatan berbanding minggu lalu
          </Typography>
        </Box>
        <Box
          sx={{
            //flex: 5,
            borderRadius: 2.5,
            backgroundColor: "#fff",
            //flex: 5,
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Typography
            sx={{
              color: "#1DC1C1",
              //pt: 3,
              mb:3,
              fontWeight: "500",
              fontSize: 14,
            }}
          >
            Peserta Latihan
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              //pt: 3,
              mb:1,
              fontWeight: "500",
              fontSize: 40,
              letterSpacing: -2,
            }}
          >
            0 %
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              //pt: 3,
              mb:3,
              fontWeight: "400",
              fontSize: 10,
            }}
          >
            Peningkatan berbanding minggu lalu
          </Typography>
        </Box>
      </Box>
    </Box>
  </>);
}

export default InternalTrainingDashboardSidebar;
