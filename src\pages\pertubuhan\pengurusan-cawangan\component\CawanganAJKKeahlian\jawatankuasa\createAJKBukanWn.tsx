import React, { useEffect, useState } from "react";
import { Box, FormHelperText, Grid, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import ButtonPrimary from "@/components/button/ButtonPrimary";
import { ButtonOutline } from "@/components/button";
import { Controller, useForm } from "react-hook-form";
import Input from "@/components/input/Input";
import useMutation from "@/helpers/hooks/useMutation";
import { API_URL } from "@/api";
import { useCustomMutation, useNotification } from "@refinedev/core";
import { usejawatankuasaContext } from "./jawatankuasaProvider";
import {
  autoGenderSetByIC,
  capitalizeWords,
  CitizenshipStatus,
  DocumentUploadType,
  DurationOptions,
  formatDate,
  GenderType,
  IdTypes,
  OrganisationPositions,
  useQuery,
} from "@/helpers";
import dayjs from "dayjs";
import FileUploader from "@/components/input/fileUpload";
import { AjkNonCiizen } from "../../CawanganPenyataTahunan/interface";
import { useSelector } from "react-redux";
import { useICValidation } from "@/helpers/hooks/useICValidation.ts";

export const CreateAjkBukanWn: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { open } = useNotification();

  const location = useLocation();
  const ajkNonCitizen: AjkNonCiizen = location.state?.ajkNonCitizen;
  const view = location.state?.view;
  const [ajkId, setAjkId] = useState<string | number | null>(null);
  const { id } = useParams();
  // const [userICCorrect, setUserICCorrect] = useState(false);
  // const [userNameMatchIC, setUserNameMatchIC] = useState(false);
  const [positionList, setPositionList] = useState<
    { value: number; label: string; designationCode?: string }[]
  >([]);

  useEffect(() => {
    if (ajkNonCitizen) {
      Object.entries(ajkNonCitizen).forEach(([key, value]) => {
        setValue(key, value);
      });
    }
  }, [ajkNonCitizen]);

  const {
    addressList,
    society,
    fetchAddressList,
    fetchSociety,
    meetingId,
    documentIds,
    appointmentDateG,
    savedMeetingDate,
  } = usejawatankuasaContext();

  const [shouldFetch, setShouldFetch] = useState<boolean>(true);

  useEffect(() => {
    fetchAddressList();
    fetchSociety();
    setShouldFetch(false);
  }, [shouldFetch]);

  const form = useForm<AjkNonCiizen | any>();
  const {
    control,
    formState: { errors },
    watch,
    handleSubmit,
    reset,
    setValue,
    getValues,
  } = form;

  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const CountryData = addressList
    ?.filter((item: any) => item.level === 0)
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));

  const { fetch: mutate, isLoading: isCreatingAJK } = useMutation({
    url: "society/nonCitizenCommittee/create",
    showSuccessNotification: false,
    onSuccess: (response) => {
      const ajkId = response.data.data.id;
      setAjkId(ajkId);
      navigate(-1);
    },
  });

  const { mutate: updateAJK, isLoading: isLoadingAJK } = useCustomMutation();

  const onSubmit = (value: AjkNonCiizen) => {
    value.visaExpirationDate = dayjs(getValues("visaExpirationDate")).format(
      "YYYY-MM-DD"
    );
    value.permitExpirationDate = dayjs(
      getValues("permitExpirationDate")
    ).format("YYYY-MM-DD");
    if (ajkNonCitizen?.id) {
      updateAJK(
        {
          url: `${API_URL}/society/nonCitizenCommittee/${ajkNonCitizen?.id}/edit`,
          method: "put",
          values: {
            ...value,
            branchId: branchDataRedux.id,
            branchNo: branchDataRedux.branchNo,
            meetingId: meetingId,
            documentId: documentIds,
            appointedDate: savedMeetingDate
              ? savedMeetingDate
              : appointmentDateG,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            navigate(-1);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          },
          errorNotification: (data) => {
            return {
              message: data?.response?.data?.msg,
              type: "error",
            };
          },
        },
        {
          onError(error, variables, context) {
            console.log(error);
          },
          onSuccess: () => {},
        }
      );
    } else {
      mutate({
        ...value,
        societyId: id,
        branchId: branchDataRedux.id,
        branchNo: branchDataRedux.branchNo,
        meetingId: meetingId,
        documentId: documentIds,
        appointedDate: savedMeetingDate ? savedMeetingDate : appointmentDateG,
      });
    }
  };

  const [JPNError, setJPNError] = useState(false);
  const {
    userICCorrect,
    userNameMatchIC,
    triggerICValidation,
    setUserICCorrect,
    setUserNameMatchIC,
    resetICValidation,
    integrationStatus,
  } = useICValidation({
    idType: watch("identificationType"),
    idNumber: watch("identificationNo"),
    fullName: watch("name"),
  });

  useEffect(() => {
    const isMyKad =
      Number(watch("identificationType")) === 1 ||
      Number(watch("identificationType")) === 4;
    const nameReady = watch("name")?.trim() !== "";
    const idReady = watch("identificationNo")?.length === 12;
    if (integrationStatus === 0 && isMyKad) {
      setJPNError(true);
    } else {
      setJPNError(false);
    }
    if (integrationStatus === 1 && isMyKad && nameReady && idReady) {
      triggerICValidation();
    }
  }, [
    watch("identificationType"),
    watch("identificationNo"),
    watch("name"),
    integrationStatus,
  ]);

  let identificationNoHelperText: string | undefined = undefined;
  if (watch("identificationType") === "4") {
    if (typeof errors.identificationNo?.message === "string") {
      identificationNoHelperText = errors.identificationNo.message;
    } else if (watch("identificationNo")?.length === 12 && !userICCorrect) {
      identificationNoHelperText = t("IcDoesNotExist");
    } else if (watch("identificationNo")?.length < 12) {
      identificationNoHelperText = t("idNumberOnlyDigits");
    }
  } else if (typeof errors.identificationNo?.message === "string") {
    identificationNoHelperText = errors.identificationNo.message;
  }

  let nameHelperText: string | undefined = undefined;
  if (watch("identificationType") === "4") {
    if (typeof errors.name?.message === "string") {
      nameHelperText = errors.name.message;
    } else if (
      watch("identificationNo")?.length === 12 &&
      watch("name")?.trim() !== undefined &&
      !userNameMatchIC
    ) {
      nameHelperText = t("invalidName");
    }
  } else if (typeof errors.name?.message === "string") {
    nameHelperText = errors.name.message;
  }

  const {
    data: positionListRes,
    isLoading: isLoadingPositionListRes,
    refetch: refetchSocietyList,
  } = useQuery({
    url: "society/nonCitizenCommittee/getPositionsList",
    filters: [
      {
        field: "societyId",
        value: id ? id : "",
        operator: "eq",
      },
      {
        field: "branchId",
        value: branchDataRedux?.id,
        operator: "eq",
      },
      {
        field: "appointedDate",
        value: savedMeetingDate ? savedMeetingDate : appointmentDateG,
        operator: "eq",
      },
    ],
    onSuccess: (data) => {
      if (data?.data?.status === "SUCCESS") {
        const newList = data?.data?.data?.map((item: any) => {
          const position = OrganisationPositions.find(
            (p) => p.value === Number(item.designationCode)
          );
          const label = position?.label
            ? `${t(position.label)}${
                item.positionHolder ? ` - ${item.positionHolder}` : ""
              }`
            : item.designationCode;

          return {
            label,
            value: Number(item?.activeCommitteeId),
            designationCode: item.designationCode,
          };
        });

        setPositionList(newList);
      }
    },
  });

  useEffect(() => {
    if (Number(getValues("identificationType")) === 4) {
      setValue(
        "gender",
        autoGenderSetByIC(
          Number(getValues("identificationType")),
          ajkNonCitizen?.gender,
          watch("identificationNo")
        )
      );
    }
  }, [watch("identificationNo")]);

  return (
    <Box sx={{ display: "flex", gap: 3 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 2 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#fff",
          borderRadius: "14px",
          width: "100%",
        }}
      >
        <form noValidate onSubmit={handleSubmit(onSubmit)}>
          <Box
            sx={{
              px: 2,
              py: 1,
              mb: 3,
              borderRadius: "14px",
            }}
          >
            <Typography
              variant="h6"
              component="h2"
              sx={{
                fontWeight: "bold",
                fontSize: 14,
                color: "var(--primary-color)",
              }}
            >
              {t("nonCitizenAJKInfo")}
            </Typography>
          </Box>

          {JPNError ? (
            <Box sx={{ pl: 2, mb: 4 }}>
              <FormHelperText sx={{ color: "var(--error)" }}>
                {t("JPNError")}
              </FormHelperText>
            </Box>
          ) : null}

          <Box sx={{ pl: 2 }}>
            <Controller
              name="societyNo"
              control={control}
              defaultValue={society?.societyNo}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    error={!!errors.societyNo?.message}
                    label={t("organizationNumber")}
                    disabled
                    value={society?.societyNo}
                  />
                );
              }}
            />
            <Controller
              name="societyName"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    error={!!errors.societyName?.message}
                    label={t("organizationName")}
                    disabled
                    value={society?.societyName}
                  />
                );
              }}
            />
            <Controller
              name="name"
              control={control}
              defaultValue={getValues("name")}
              rules={{
                required: t("fieldRequired"),
              }}
              render={({ field }) => {
                return (
                  <Input
                    required
                    disabled={view || JPNError}
                    {...field}
                    error={!!nameHelperText}
                    helperText={nameHelperText}
                    label={t("name")}
                    value={getValues("name")}
                  />
                );
              }}
            />
            <Controller
              name="citizenshipStatus"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    label={t("citizenship")}
                    disabled
                    value={2}
                    type="select"
                    options={CitizenshipStatus.map((item: any) => ({
                      ...item,
                      label: t(item.label),
                    }))}
                  />
                );
              }}
            />
            <Controller
              name="identificationType"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              defaultValue={getValues("identificationType")}
              render={({ field }) => {
                return (
                  <Input
                    required
                    disabled={view}
                    {...field}
                    error={!!errors.identificationType?.message}
                    label={t("idType")}
                    value={t(getValues("identificationType"))}
                    type="select"
                    onChange={(e) => {
                      const inputType = getValues("identificationType");
                      let value = e.target.value;
                      if (inputType !== "4" && value === "4") {
                        setValue("identificationNo", "");
                      }

                      setValue(field.name, value);
                    }}
                    options={IdTypes.filter(
                      (item) =>
                        Number(item.value) === 4 || Number(item.value) === 5
                    ).map((item) => ({
                      ...item,
                      label: t(item.label),
                    }))}
                  />
                );
              }}
            />
            <Controller
              name="identificationNo"
              rules={{
                required: t("fieldRequired"),
                validate: (value) => {
                  const type = getValues("identificationType");
                  if (type === "4" && value.length !== 12) {
                    return t("fieldRequired");
                  }
                  return true;
                },
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    disabled={view || JPNError}
                    {...field}
                    error={!!identificationNoHelperText}
                    helperText={identificationNoHelperText}
                    label={t("idNumber")}
                    inputProps={
                      getValues("identificationType") === "4"
                        ? {
                            inputMode: "numeric",
                            pattern: "[0-9]*",
                            maxLength: 12,
                            minLength: 12,
                          }
                        : undefined
                    }
                    onChange={(e) => {
                      const inputType = getValues("identificationType");
                      let value = e.target.value;

                      if (inputType === "4") {
                        value = value.replace(/\D/g, "").slice(0, 12);
                      }

                      setValue(field.name, value);
                    }}
                  />
                );
              }}
            />
            <Controller
              name="applicantCountryCode"
              control={control}
              defaultValue={getValues("applicantCountryCode")}
              rules={{
                required: t("fieldRequired"),
              }}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    disabled={view}
                    helperText={errors.applicantCountryCode?.message as string}
                    error={!!errors.applicantCountryCode?.message}
                    label={t("originCountry")}
                    type="select"
                    value={parseInt(getValues("applicantCountryCode"))}
                    options={CountryData}
                  />
                );
              }}
            />
            <Controller
              name="gender"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              defaultValue={getValues("gender")}
              render={({ field }) => {
                return (
                  <Input
                    required
                    disabled={view}
                    {...field}
                    error={!!errors.gender?.message}
                    label={t("gender")}
                    value={t(getValues("gender"))}
                    type="select"
                    options={GenderType.map((item) => ({
                      label: t(item.translateKey),
                      value: item.code,
                    }))}
                  />
                );
              }}
            />
            <Controller
              name="visaNo"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    disabled={view}
                    {...field}
                    error={!!errors.visaNo?.message}
                    label={t("nomborVisa")}
                  />
                );
              }}
            />
            <Controller
              name="visaExpirationDate"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    disabled={view}
                    {...field}
                    label={t("visaExpiryDate")}
                    type="date"
                    onChange={(newValue) =>
                      setValue("visaExpirationDate", newValue.target.value)
                    }
                    value={
                      getValues("visaExpirationDate")
                        ? formatDate(getValues("visaExpirationDate"))
                        : ""
                    }
                  />
                );
              }}
            />
            <Controller
              name="permitNo"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    disabled={view}
                    {...field}
                    error={!!errors.permitNo?.message}
                    label={t("permitNumber")}
                  />
                );
              }}
            />
            <Controller
              name="permitExpirationDate"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    disabled={view}
                    {...field}
                    label={t("permitExpiryDate")}
                    type="date"
                    onChange={(newValue) =>
                      setValue("permitExpirationDate", newValue.target.value)
                    }
                    value={
                      getValues("permitExpirationDate")
                        ? formatDate(getValues("permitExpirationDate"))
                        : ""
                    }
                  />
                );
              }}
            />
            <Controller
              name="tujuanDMalaysia"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    disabled={view}
                    {...field}
                    helperText={errors.tujuanDMalaysia?.message as string}
                    error={!!errors.tujuanDMalaysia?.message}
                    label={capitalizeWords(t("purposeInMalaysia"))}
                  />
                );
              }}
            />
            {Number(watch("identificationType")) !== 4 && (
              <Grid container spacing={2} alignItems="center" sx={{ mb: 1 }}>
                <Grid item xs={12} sm={4}>
                  <Typography
                    variant="body1"
                    sx={{
                      color: "#666666",
                      fontWeight: "400 !important",
                      fontSize: "14px",
                    }}
                  >
                    {capitalizeWords(t("durationInMalaysia"))}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Controller
                        name="stayDurationDigit"
                        control={control}
                        rules={{
                          validate: (value) => {
                            // Skip validation if identificationType is 4 (MyPR)
                            if (Number(watch("identificationType")) === 4)
                              return true;
                            return value ? true : t("fieldRequired");
                          },
                        }}
                        render={({ field }) => {
                          return (
                            <Input
                              required
                              {...field}
                              isLabelNoSpace={false}
                              isLabel={false}
                              type="text"
                              inputMode="numeric"
                              helperText={
                                errors.stayDurationDigit?.message as string
                              }
                              error={!!errors.stayDurationDigit?.message}
                              onChange={(newValue) => {
                                const value = newValue.target.value;
                                if (/^\d*$/.test(value)) {
                                  setValue(
                                    "stayDurationDigit",
                                    parseInt(value) || 0
                                  );
                                }
                              }}
                              disabled={view}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Controller
                        name="stayDurationUnit"
                        control={control}
                        rules={{
                          validate: (value) => {
                            // Skip validation if identificationType is 4 (MyPR)
                            if (Number(watch("identificationType")) === 4)
                              return true;
                            return value ? true : t("fieldRequired");
                          },
                        }}
                        render={({ field }) => {
                          return (
                            <Input
                              required
                              isLabelNoSpace={false}
                              {...field}
                              type="select"
                              helperText={
                                errors.stayDurationUnit?.message as string
                              }
                              error={!!errors.stayDurationUnit?.message}
                              onChange={(newValue) =>
                                setValue(
                                  "stayDurationUnit",
                                  newValue.target.value
                                )
                              }
                              isLabel={false}
                              value={getValues("stayDurationUnit")}
                              options={DurationOptions.map((duration) => ({
                                ...duration,
                                label: t(duration.label),
                              }))}
                              disabled={view}
                            />
                          );
                        }}
                      />
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            )}
            <Controller
              name="activeCommitteeId"
              rules={
                positionList?.length
                  ? {
                      required: t("fieldRequired"),
                      validate: (value) => value !== 0 || t("fieldRequired"),
                    }
                  : undefined
              }
              defaultValue={getValues("activeCommitteeId")}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    disabled={view}
                    required={positionList?.length ? true : false}
                    {...field}
                    type="select"
                    label={t("position")}
                    isLoadingData={isLoadingPositionListRes}
                    options={positionList}
                    helperText={errors.activeCommitteeId?.message as string}
                    error={!!errors.activeCommitteeId?.message}
                    onChange={(selectedValue) => {
                      field.onChange(selectedValue);
                      const selectedOption = positionList?.find(
                        (opt) => Number(opt.value) === Number(selectedValue)
                      );
                      if (selectedOption?.designationCode) {
                        setValue(
                          "designationCode",
                          selectedOption.designationCode
                        );
                      }
                    }}
                  />
                );
              }}
            />
            <Controller
              name="otherDesignationCode"
              rules={{
                required: t("fieldRequired"),
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    disabled={view}
                    {...field}
                    helperText={errors.tujuanDMalaysia?.message as string}
                    error={!!errors.otherDesignationCode?.message}
                    label={t("importanceOfPosition2")}
                  />
                );
              }}
            />
          </Box>

          <FileUploader
            key={branchDataRedux?.id}
            title="ajkEligibilityCheck"
            type={DocumentUploadType.NON_CITIZEN_COMMITTEE}
            uploadAfterSubmitIndicator={ajkId}
            uploadAfterSubmit={ajkNonCitizen?.id ? false : true}
            required
            validTypes={[
              "application/pdf",
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
              "application/msword",
              "text/plain",
            ]}
            maxFileSize={25 * 1024 * 1024}
            disabled={view}
            societyId={
              ajkId || ajkNonCitizen?.id ? (id ? id : undefined) : undefined
            }
            branchId={
              ajkId || ajkNonCitizen?.id
                ? branchDataRedux.id
                  ? branchDataRedux.id
                  : undefined
                : undefined
            }
            branchCommitteeId={
              ajkId ? ajkId : ajkNonCitizen?.id ? ajkNonCitizen?.id : undefined
            }
            icNo={watch("identificationNo")}
            showSuccessUploadNotification={false}
            onUploadAfterSubmitSuccessfully={() => {
              open?.({
                message: t("nonCitizenCommitteeSuccessfullyCreated"),
                type: "success",
              });
            }}
          />
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              mt: 2,
              gap: 2,
            }}
          >
            <ButtonOutline onClick={() => navigate(-1)}>
              {t("back")}
            </ButtonOutline>
            {!view ? (
              <>
                <ButtonPrimary
                  disabled={
                    JPNError ||
                    isCreatingAJK ||
                    isLoadingAJK ||
                    (watch("identificationType") === "4" &&
                      (watch("identificationNo")?.length < 12 ||
                        !userICCorrect ||
                        !userNameMatchIC))
                  }
                  type="submit"
                >
                  {t("update")}
                </ButtonPrimary>
              </>
            ) : null}
          </Box>
        </form>
      </Box>
    </Box>
  );
};

export default CreateAjkBukanWn;
