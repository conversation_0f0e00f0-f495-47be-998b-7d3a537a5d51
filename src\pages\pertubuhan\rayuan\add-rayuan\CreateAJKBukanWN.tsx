import React, { useEffect, useRef, useState } from "react";
import {
  Box,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  Button,
  Paper,
  Grid,
  IconButton,
  Menu,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Divider,
  <PERSON>ert,
  styled,
  LinearProgress,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { useNavigate } from "react-router-dom";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DeleteIcon from "@mui/icons-material/Delete";
import { Add } from "@mui/icons-material";
import ButtonPrimary from "../../../../components/button/ButtonPrimary";
import CampaignIcon from "@mui/icons-material/Campaign";
import CloseIcon from "@mui/icons-material/Close";
import { ButtonOutline } from "../../../../components/button";
import useUploadDocument from "../../../../helpers/hooks/useUploadDocument";
import { Controller, useForm } from "react-hook-form";
import Input from "../../../../components/input/Input";
import useMutation from "../../../../helpers/hooks/useMutation";
import { API_URL } from "../../../../api";
import { useCustomMutation } from "@refinedev/core";
import {
  CitizenshipStatus,
  HideOrDisplayInherit,
} from "../../../../helpers/enums";

interface IFormNonCommite {
  name: string;
  citizenshipStatus: string;
  identificationType: string;
  identificationNo: string;
  applicantCountryCode: string;
  visaExpirationDate: string; // ISO 8601 format
  permitExpirationDate: string; // ISO 8601 format
  visaNo: string;
  permitNo: string;
  positionCode: string;
  visaPermitNo: string;
  tujuanDMalaysia: string;
  tempohDMalaysia: string;
  durationType: string;
  summary: string;
  societyName: string;
  societyId: number;
  societyNo: string;
  branchId: number;
  branchNo: string;
  applicationStatusCode: string;
  status: number;
  ro: string; // Regional Office
  pembaharuanSu: string; // Renewal Required
  pemCaw: string; // Yes or No
  otherPosition: string;
  transferDate: string; // ISO 8601 format
  noteRo: string; // Note for Regional Office
}

const VisuallyHiddenInput = styled("input")({
  clip: "rect(0 0 0 0)",
  clipPath: "inset(50%)",
  height: 1,
  overflow: "hidden",
  position: "absolute",
  bottom: 0,
  left: 0,
  whiteSpace: "nowrap",
  width: 1,
});

interface UploadingFile {
  name: string;
  progress: number;
  size: string;
  isComplete?: boolean;
}

export const CreateAjkBukanWn: React.FC = () => {
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openMenuId, setOpenMenuId] = useState<number | null>(null);
  const { t } = useTranslation();
  const [activeStep, setActiveStep] = useState(3);

  const handleSenaraiAjk = () => {
    navigate(-1);
  };

  const [SemakanKhalayakAjk, setSemakanKhalayakAjk] = useState([
    // {
    //   id: 1,
    //   perkara: t("bankruptcy"),
    //   namaDokumen: "Rashid_bankrupsi_pengesahan",
    // },
  ]);

  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  const { upload } = useUploadDocument({
    type: "dokumen",
  });

  const handleMenuClose = () => {
    setAnchorEl(null);
    setOpenMenuId(null);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handlePreviewDocument = (fileUrl: string) => {
    window.open(fileUrl, "_blank");
  };

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files) return;

    Array.from(files).forEach((file) => {
      // Add to uploading state for UI feedback
      setUploadingFiles((prev) => [
        ...prev,
        {
          name: file.name,
          progress: 0,
          size: formatFileSize(file.size),
        },
      ]);

      // Upload using the hook
      upload({ file }, () => {
        // Update uploadingFiles to show completion
        setUploadingFiles((prev) =>
          prev.map((f) =>
            f.name === file.name ? { ...f, progress: 100, isComplete: true } : f
          )
        );
      });
    });
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = e.dataTransfer.files;
    if (!files) return;

    Array.from(files).forEach((file) => {
      setUploadingFiles((prev) => [
        ...prev,
        {
          name: file.name,
          progress: 0,
          size: formatFileSize(file.size),
        },
      ]);

      upload({ file }, () => {
        setUploadingFiles((prev) =>
          prev.map((f) =>
            f.name === file.name ? { ...f, progress: 100, isComplete: true } : f
          )
        );
      });
    });
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      const response = await fetch(
        `${API_URL}/society/document/${documentId}`,
        {
          method: "DELETE",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );

      if (response.ok) {
        // Remove from uploaded files list
        setUploadedFiles((prev) =>
          prev.filter((file) => file.id !== documentId)
        );
        handleMenuClose();
      }
    } catch (error) {
      console.error("Delete error:", error);
    }
  };

  const form = useForm<IFormNonCommite>();
  const {
    control,
    formState: { errors },
    watch,
    handleSubmit,
    reset,
    getValues,
  } = form;

  const mutate = useMutation({
    url: "society/nonCitizenCommittee/create",
    onSuccess: () => {
      reset({});
    },
  });

  const intialValue = {};

  const { mutate: updateAJK, isLoading: isLoadingAJK } = useCustomMutation();

  const onSubmit = (value: IFormNonCommite) => {
    if (isEdit) {
      const value = JSON.parse(params.get("value") || "");
      updateAJK(
        {
          url: `${API_URL}/society/nonCitizenCommittee/${value.id}/edit`,
          method: "put",
          values: {
            ...value,
            visaExpirationDate: value.visaExpirationDate.join("-"),
            permitExpirationDate: value.permitExpirationDate.join("-"),
            transferDate: value.transferDate.join("-"),
            modifiedDate: value.modifiedDate.join("-"),
            createdDate: value.createdDate.join("-"),
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            return {
              message: data?.data?.msg,
              type: "success",
            };
          },
          errorNotification: (data) => {
            return {
              message: data?.response?.data?.msg,
              type: "error",
            };
          },
        },
        {
          onError(error, variables, context) {
            console.log(error);
          },
        }
      );
    } else {
      mutate.fetch({ ...value });
    }
  };
  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    reset({});
    if (encodedId) {
      const decodedId = atob(encodedId);

      fetch(`${API_URL}/society/${decodedId}`, {
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      })
        .then((res) => res.json())
        .then((data) => {
          reset({
            ...getValues(),
            societyId: +decodedId,
            societyName: data.data.societyName,
            societyNo: data.data.societyNo || "GOS-2022",
          });
        })
        .catch((error) => {
          console.error("Error fetching society details:", error);
        });
    }
  }, [isEdit]);
  useEffect(() => {
    if (params.get("value")) {
      const value: string = params.get("value") || "";
      const parseValue = JSON.parse(value);
      console.log(parseValue);
      reset({
        ...getValues(),
        ...parseValue,
        visaExpirationDate: parseValue.visaExpirationDate.join("-"),
        permitExpirationDate: parseValue.permitExpirationDate.join("-"),
        transferDate: parseValue.transferDate.join("-"),
        modifiedDate: parseValue.modifiedDate.join("-"),
        createdDate: parseValue.createdDate.join("-"),
      });
      setIsEdit(true);
    }
  }, []);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  // @ts-ignore
  const addressData = useSelector((state) => state.addressData.data);
  const CountryData = addressData
    ?.filter((item: any) => item.level === 0)
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("maklumat")} {t("nonCitizenAJK")}
          </Typography>

          <Box sx={{ mt: 2 }}>
            <Controller
              name="societyNo"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.societyNo?.message}
                    helperText={errors.societyNo?.message}
                    label={t("organizationNumber")}
                    disabled
                  />
                );
              }}
            />
            <Controller
              name="societyName"
              rules={{
                required: "Medan ini diperlukan",
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.societyName?.message}
                    helperText={errors.societyName?.message}
                    label={t("organization_name")}
                    disabled
                  />
                );
              }}
            />
            <Controller
              name="name"
              rules={{
                required: "Medan ini diperlukan",
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.name?.message}
                    helperText={errors.name?.message}
                    label={t("fullNameCapitalizedOnlyFirstLetter")}
                  />
                );
              }}
            />
            <Controller
              name="citizenshipStatus"
              rules={{
                required: "Medan ini diperlukan",
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.citizenshipStatus?.message}
                    helperText={errors.citizenshipStatus?.message}
                    label={t("citizenship")}
                    value={2}
                    options={CitizenshipStatus.map((item) => ({
                      ...item,
                      label: t(item.label),
                    }))}
                    type="select"
                  />
                );
              }}
            />

            <Controller
              name="identificationType"
              rules={{
                required: "Medan ini diperlukan",
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.identificationType?.message}
                    helperText={errors.identificationType?.message}
                    label={t("idTypeCapitalizedOnlyFirstLetter")}
                    value={field.value}
                    type="select"
                    options={[
                      {
                        label: "MyPR",
                        value: "MyPR",
                      },
                      {
                        label: "Passport",
                        value: "Passport",
                      },
                    ]}
                  />
                );
              }}
            />
            <Controller
              name="identificationNo"
              rules={{
                required: "Medan ini diperlukan",
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.identificationNo?.message}
                    helperText={errors.identificationNo?.message}
                    label={t("idNumberCapitalizedOnlyFirstLetter")}
                  />
                );
              }}
            />

            <Controller
              name="applicantCountryCode"
              rules={{
                required: "Medan ini diperlukan",
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.applicantCountryCode?.message}
                    helperText={errors.applicantCountryCode?.message}
                    label={t("originCountry")}
                    type="select"
                    value={field.value}
                    options={CountryData}
                  />
                );
              }}
            />

            <Controller
              name="visaNo"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    error={!!errors.visaNo?.message}
                    helperText={errors.visaNo?.message}
                    label={t("visaNumber")}
                  />
                );
              }}
            />
            <Controller
              name="visaExpirationDate"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    error={!!errors.visaExpirationDate?.message}
                    helperText={errors.visaExpirationDate?.message}
                    label={t("visaExpiryDate")}
                    type="date"
                  />
                );
              }}
            />

            <Controller
              name="permitNo"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    error={!!errors.permitNo?.message}
                    helperText={errors.permitNo?.message}
                    label={t("permitNumber")}
                  />
                );
              }}
            />

            <Controller
              name="permitExpirationDate"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    error={!!errors.permitExpirationDate?.message}
                    helperText={errors.permitExpirationDate?.message}
                    type="date"
                    label={t("permitExpiryDate")}
                  />
                );
              }}
            />
            <Controller
              name="tujuanDMalaysia"
              rules={{
                required: "Medan ini diperlukan",
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.tujuanDMalaysia?.message}
                    helperText={errors.tujuanDMalaysia?.message}
                    label={t("purposeInMalaysia")}
                  />
                );
              }}
            />

            <Controller
              name="tempohDMalaysia"
              control={control}
              rules={{
                required: "Field in di perlukan",
              }}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.tempohDMalaysia?.message}
                    helperText={errors.tempohDMalaysia?.message}
                    label={t("durationInMalaysia")}
                    placeholder={t("exampleYearPlaceholder")}
                  />
                );
              }}
            />
            <Controller
              name="otherPosition"
              rules={{
                required: "Medan ini diperlukan",
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.otherPosition?.message}
                    helperText={errors.otherPosition?.message}
                    label={t("position")}
                  />
                );
              }}
            />
            <Controller
              name="positionCode"
              rules={{
                required: "Medan ini diperlukan",
              }}
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!errors.positionCode?.message}
                    helperText={errors.positionCode?.message}
                    label={t("importanceOfPosition")}
                  />
                );
              }}
            />
          </Box>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("ajkEligibilityCheck")}
          </Typography>

          <Box
            sx={{
              border: "2px dashed #e0e0e0",
              borderRadius: 1,
              p: 4,
              mb: 2,
              textAlign: "center",
              cursor: "pointer",
              "&:hover": {
                backgroundColor: "#f5f5f5",
              },
            }}
            onClick={() => fileInputRef.current?.click()}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <input
              type="file"
              ref={fileInputRef}
              style={{ display: "none" }}
              onChange={handleFileUpload}
              multiple
              accept=".pdf,.docx,.txt"
            />
            <svg
              width="28"
              height="29"
              viewBox="0 0 28 29"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              style={{ marginBottom: "8px" }}
            >
              <path
                d="M15.166 2.8335H15.4842C19.2889 2.8335 21.1913 2.8335 22.5124 3.7643C22.891 4.031 23.227 4.34727 23.5104 4.70353C24.4993 5.94695 24.4993 7.73741 24.4993 11.3183V14.288C24.4993 17.7451 24.4993 19.4736 23.9523 20.8541C23.0727 23.0735 21.2127 24.8241 18.8546 25.6519C17.3878 26.1668 15.5512 26.1668 11.8781 26.1668C9.77922 26.1668 8.72977 26.1668 7.89159 25.8726C6.54411 25.3996 5.48123 24.3992 4.97864 23.131C4.66602 22.3421 4.66602 21.3544 4.66602 19.3789V14.5002"
                stroke="var(--primary-color)"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M24.4987 14.5005C24.4987 16.6483 22.7576 18.3894 20.6098 18.3894C19.8331 18.3894 18.9173 18.2533 18.1621 18.4556C17.4911 18.6354 16.967 19.1596 16.7872 19.8306C16.5848 20.5858 16.7209 21.5015 16.7209 22.2783C16.7209 24.426 14.9798 26.1672 12.832 26.1672"
                stroke="var(--primary-color)"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M12.8333 7.50016L3.5 7.50016M8.16667 2.8335V12.1668"
                stroke="var(--primary-color)"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
            <Typography
              sx={{
                fontFamily: "DM Sans",
                fontSize: "18px",
                fontWeight: 500,
                lineHeight: "18.23px",
                textAlign: "center",
                textUnderlinePosition: "from-font",
                textDecorationSkipInk: "none",
                color: "#222222",
                marginBottom: "15px",
              }}
            >
              Click to upload
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: "var(--primary-color)",
                display: "flex",
                gap: 1,
                justifyContent: "center",
              }}
            >
              <Box
                component="span"
                sx={{ bgcolor: "#3276E81A", px: 1, py: 0.5, borderRadius: 1 }}
              >
                PDF
              </Box>
              <Box
                component="span"
                sx={{ bgcolor: "#3276E81A", px: 1, py: 0.5, borderRadius: 1 }}
              >
                DOCX
              </Box>
              <Box
                component="span"
                sx={{ bgcolor: "#3276E81A", px: 1, py: 0.5, borderRadius: 1 }}
              >
                TXT
              </Box>
              <Box
                component="span"
                sx={{ bgcolor: "#E3F2FD", px: 1, py: 0.5, borderRadius: 1 }}
              >
                &gt;25 MB
              </Box>
            </Typography>
          </Box>

          <Box sx={{ textAlign: "left", mt: 2 }}>
            {/* Show files being uploaded */}
            {uploadingFiles.map((file, index) => (
              <Box
                key={index}
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "8px",
                  backgroundColor: "#fff",
                  p: 2,
                  mb: 1,
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    mb: 1,
                  }}
                >
                  <Box>
                    <Typography>{file.name}</Typography>
                    {file.isComplete && (
                      <Typography
                        sx={{
                          color: "var(--primary-color)",
                          fontSize: "0.875rem",
                          mt: 0.5,
                        }}
                      >
                        Upload complete
                      </Typography>
                    )}
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "start", gap: 1 }}>
                    {file.isComplete ? (
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          color: "#4CAF50",
                          p: 1,
                          borderRadius: 1,
                        }}
                      >
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M23 12L20.56 9.21L20.9 5.52L17.29 4.7L15.4 1.5L12 2.96L8.6 1.5L6.71 4.69L3.1 5.5L3.44 9.2L1 12L3.44 14.79L3.1 18.49L6.71 19.31L8.6 22.5L12 21.03L15.4 22.49L17.29 19.3L20.9 18.48L20.56 14.79L23 12ZM9.38 16.01L7 13.61C6.9073 13.5175 6.83375 13.4076 6.78357 13.2866C6.73339 13.1657 6.70756 13.036 6.70756 12.905C6.70756 12.774 6.73339 12.6443 6.78357 12.5234C6.83375 12.4024 6.9073 12.2925 7 12.2L7.07 12.13C7.46 11.74 8.1 11.74 8.49 12.13L10.1 13.75L15.25 8.59C15.64 8.2 16.28 8.2 16.67 8.59L16.74 8.66C17.13 9.05 17.13 9.68 16.74 10.07L10.82 16.01C10.41 16.4 9.78 16.4 9.38 16.01Z"
                            fill="var(--primary-color)"
                          />
                        </svg>
                      </Box>
                    ) : (
                      <></>
                    )}
                  </Box>
                </Box>
                {!file.isComplete && (
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      gap: 2,
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{ color: "var(--primary-color)", minWidth: "180px" }}
                    >
                      {`${file.progress}%`} • Uploading • {file.size}
                    </Typography>

                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 2,
                        width: "300px",
                      }}
                    >
                      <LinearProgress
                        variant="determinate"
                        value={file.progress}
                        sx={{
                          flex: 1,
                          height: 6,
                          borderRadius: 3,
                          backgroundColor: "#E0E0E0",
                          "& .MuiLinearProgress-bar": {
                            backgroundColor: "#00BCD4",
                            borderRadius: 3,
                          },
                        }}
                      />
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteDocument(file.name)}
                      >
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M14 5L5 14M5 5L14 14"
                            stroke="#9E9E9E"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </IconButton>
                    </Box>
                  </Box>
                )}
              </Box>
            ))}

            {/* Show uploaded files */}
            {uploadedFiles.map((file, index) => (
              <Box
                key={file.id}
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "8px",
                  backgroundColor: "#fff",
                  p: 2,
                  mb: 1,
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    mb: 1,
                  }}
                >
                  <Box>
                    <Typography
                      onClick={() => handlePreviewDocument(file.url)}
                      sx={{
                        cursor: "pointer",
                        "&:hover": {
                          color: "var(--primary-color)",
                          textDecoration: "underline",
                        },
                      }}
                    >
                      {file.name}
                    </Typography>
                    <Typography
                      sx={{
                        color: "var(--primary-color)",
                        fontSize: "0.875rem",
                        mt: 0.5,
                      }}
                    >
                      Upload complete
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 1,
                    }}
                  >
                    {/* <IconButton
                          size="small"
                          onClick={() => handleDeleteDocument(file.id)}
                          sx={{ color: '#9E9E9E' }}
                        >
                          <DeleteIcon />
                        </IconButton> */}
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        color: "#4CAF50",
                        p: 1,
                        borderRadius: 1,
                      }}
                    >
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M23 12L20.56 9.21L20.9 5.52L17.29 4.7L15.4 1.5L12 2.96L8.6 1.5L6.71 4.69L3.1 5.5L3.44 9.2L1 12L3.44 14.79L3.1 18.49L6.71 19.31L8.6 22.5L12 21.03L15.4 22.49L17.29 19.3L20.9 18.48L20.56 14.79L23 12ZM9.38 16.01L7 13.61C6.9073 13.5175 6.83375 13.4076 6.78357 13.2866C6.73339 13.1657 6.70756 13.036 6.70756 12.905C6.70756 12.774 6.73339 12.6443 6.78357 12.5234C6.83375 12.4024 6.9073 12.2925 7 12.2L7.07 12.13C7.46 11.74 8.1 11.74 8.49 12.13L10.1 13.75L15.25 8.59C15.64 8.2 16.28 8.2 16.67 8.59L16.74 8.66C17.13 9.05 17.13 9.68 16.74 10.07L10.82 16.01C10.41 16.4 9.78 16.4 9.38 16.01Z"
                          fill="var(--primary-color)"
                        />
                      </svg>
                    </Box>
                  </Box>
                </Box>
              </Box>
            ))}
          </Box>
        </Box>

        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            mt: 2,
            gap: 2,
          }}
        >
          <ButtonOutline onClick={handleSenaraiAjk}>{t("back")}</ButtonOutline>
          <ButtonPrimary type="submit">{t("update")}</ButtonPrimary>
        </Box>
      </Box>
    </form>
  );
};

export default CreateAjkBukanWn;
