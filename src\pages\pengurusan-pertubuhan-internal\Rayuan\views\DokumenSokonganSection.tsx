import { useEffect } from "react";
import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { useDispatch } from "react-redux";
import { DocumentUploadType } from "@/helpers";
import { AppDispatch } from "@/redux/store";
import { fetchAppealByIdData } from "@/redux/APIcalls/appealByIdThunks";
import FileUploader from "@/components/input/fileUpload";

const DokumenSokonganSection = () => {
  const { id, type } = useParams();
  const decodedId = atob(id ?? "");
  const decodedType = atob(type ?? "");
  const dispatch: AppDispatch = useDispatch();
  const {
    data: appealData,
    loading: loadingAppealData,
    error: errorAppealData,
  } = useSelector((state: any) => state.appealByIdData);

  useEffect(() => {
    if (decodedId) {
      dispatch(fetchAppealByIdData({ id: decodedId }));
    }
  }, []);

  return (
    decodedId &&
    appealData?.societyId && (
      <FileUploader
        type={DocumentUploadType.APPEAL}
        appealId={decodedId}
        disabled={true}
        societyId={appealData?.societyId}
        validTypes={[
          "text/plain",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/msword",
          "application/pdf",
        ]}
      />
    )
  );
};

export default DokumenSokonganSection;
