import React, {useState} from "react";
import {Box, IconButton, InputAdornment, Stack, TextField, Typography} from "@mui/material";
import {Search} from "@mui/icons-material";
import {useNavigate} from "react-router-dom";
import TrainingBreadcrumb from "@/pages/training/breadcrumb";
import {useCustom} from "@refinedev/core";
import {API_URL} from "@/api";
interface TrainingHeaderProps {
  handleSearch?: (val : string) => void
}


const TrainingHeader: React.FC<TrainingHeaderProps> = ({handleSearch}) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  //const [score, setScore] = useState(0);


  const handleSearchChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const {value} = e.target;
    setSearchTerm(value);
    if(handleSearch) handleSearch(value);
  };

  const onSearchKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      const value = (event.target as HTMLInputElement).value;
      //console.log("onSearchKeyDown",value);
      if(handleSearch) handleSearch(value);
      //setValue("page", 1);
      //setSearchQuery(value);
    }
  };

  const { data: totalScoreData, isLoading: isTotalScoreLoading } = useCustom({
    url: `${API_URL}/society/training/score`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const totalScore = totalScoreData?.data?.data || 0;
  //console.log("totalScore", totalScore)

  return (
    <>
      <TrainingBreadcrumb isAdmin={false} />
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //display: "inline",
          px: 2,
          mb: 1,
        }}
      >
        <Stack
          sx={{py: 1}}
          direction="row"
          alignItems="center"
          justifyContent="space-between"
        >
          <Box sx={{display: "inline", justifyContent: "center"}}>
            <TextField
              placeholder="Latihan"
              variant="outlined"
              size="small"
              value={searchTerm}
              onKeyDown={onSearchKeyDown}
              onChange={(e) => handleSearchChange(e)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search/>
                  </InputAdornment>
                ),
              }}
              sx={{
                backgroundColor: "#E8E9E8",
                "& .MuiOutlinedInput-root": {
                  "& fieldset": {
                    borderColor: "#E8E9E8",
                  },
                  "&:hover fieldset": {
                    borderColor: "#E8E9E8",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "#E8E9E8",
                  },
                },
                padding: "2px",
                width: "800px",
                borderRadius: "10px",
                border: "1px solid rgba(102, 102, 102, 0.8)",
              }}
            />
          </Box>
          <Box sx={{display: "inline", justifyContent: "center"}}>
            <Box
              sx={{
                borderRadius: 2.5,
                backgroundColor: "#fff",
                display: "flex",
                p: 2,
                mb: 1,
              }}
            >
              <Typography
                sx={{
                  color: "#666666",
                  fontWeight: 600,
                  fontSize: "14px",
                  cursor: "pointer",
                  "&:hover": {
                    textDecoration: "underline",
                  },
                }}
                //onClick={() => navigate(item.path)}
              >
                {`${totalScore} pts`}
              </Typography>
            </Box>
          </Box>
        </Stack>
      </Box>
    </>
  );
}

export default TrainingHeader;
