import React, {useState} from "react";
import {Box, IconButton, InputAdornment, Stack, TextField, Typography} from "@mui/material";
import {Search} from "@mui/icons-material";
import {useNavigate} from "react-router-dom";
import TrainingBreadcrumb from "@/pages/training/breadcrumb";


const TrainingHeader: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");

  const handleSearchChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const {value} = e.target;
    setSearchTerm(value);
  };

  const onSearchKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      const value = (event.target as HTMLInputElement).value;
      //setValue("page", 1);
      //setSearchQuery(value);
    }
  };

  return (
    <>
      <TrainingBreadcrumb isAdmin={false} />
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //display: "inline",
          px: 2,
          mb: 1,
        }}
      >
        <Stack
          sx={{py: 1}}
          direction="row"
          alignItems="center"
          justifyContent="space-between"
        >
          <Box sx={{display: "inline", justifyContent: "center"}}>
            <TextField
              placeholder="Latihan"
              variant="outlined"
              size="small"
              value={searchTerm}
              onKeyDown={onSearchKeyDown}
              onChange={(e) => handleSearchChange(e)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search/>
                  </InputAdornment>
                ),
              }}
              sx={{
                backgroundColor: "#E8E9E8",
                "& .MuiOutlinedInput-root": {
                  "& fieldset": {
                    borderColor: "#E8E9E8",
                  },
                  "&:hover fieldset": {
                    borderColor: "#E8E9E8",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "#E8E9E8",
                  },
                },
                padding: "2px",
                width: "800px",
                borderRadius: "10px",
                border: "1px solid rgba(102, 102, 102, 0.8)",
              }}
            />
          </Box>
          <Box sx={{display: "inline", justifyContent: "center"}}>
            <Box
              sx={{
                borderRadius: 2.5,
                backgroundColor: "#fff",
                display: "flex",
                p: 2,
                mb: 1,
              }}
            >
              <Typography
                sx={{
                  color: "#666666",
                  fontWeight: 600,
                  fontSize: "14px",
                  cursor: "pointer",
                  "&:hover": {
                    textDecoration: "underline",
                  },
                }}
                //onClick={() => navigate(item.path)}
              >
                100 pts
              </Typography>
            </Box>
          </Box>
        </Stack>
      </Box>
    </>
  );
}

export default TrainingHeader;
