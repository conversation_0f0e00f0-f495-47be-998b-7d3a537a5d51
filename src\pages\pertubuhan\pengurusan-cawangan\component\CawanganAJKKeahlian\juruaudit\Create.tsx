import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { useEffect, useState } from "react";
import { API_URL } from "@/api";
import { useCustomMutation } from "@refinedev/core";
import {
  CitizenshipStatus,
  IdTypes,
  ListGelaran,
  ListGender,
  MALAYSIA,
} from "@/helpers/enums";
import Input from "@/components/input/Input";
import { usejawatankuasaContext } from "../jawatankuasa/jawatankuasaProvider";
import {
  Controller,
  FieldValues,
  SubmitHandler,
  useForm,
} from "react-hook-form";
import {
  autoDOBSetByIC,
  autoGenderSetByIC,
  capitalizeWords,
  filterEmptyValuesOnObject,
  getLocalStorage,
} from "@/helpers/utils";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import ConfirmationDialog from "@/components/dialog/confirm";
import NewAlertDialog from "@/components/dialog/newAlert";
import { useICValidation } from "@/helpers/hooks/useICValidation.ts";

export const CreateJuruAudit: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleBack = () => {
    navigate(-1);
  };

  const occupationList = getLocalStorage("occupation_list", []);

  const location = useLocation();
  const auditorId = location.state?.auditorId;

  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const [dialogAlertSaveOpen, setDialogAlertSaveOpen] = useState(false);

  const {
    addressList,
    fetchAddressList,
    auditor,
    fetchAuditor,
    members,
    fetchMembers,
    fetchBranchMembers,
    branchMembers,
  } = usejawatankuasaContext();

  useEffect(() => {
    fetchAddressList();
    fetchBranchMembers();
  }, [fetchAddressList, fetchBranchMembers]);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const defaultFormValues = {
    id: "",
    societyId: "",
    societyNo: "",
    branchNo: "",
    statementId: "",
    auditorType: "",
    titleCode: "",
    name: "",
    licenseNo: "",
    companyName: "",
    gender: "",
    nationalityStatus: "",
    identificationType: "",
    identificationNo: "",
    dateOfBirth: "",
    placeOfBirth: "",
    employmentCode: "",
    address: "",
    countryCode: "",
    stateCode: "",
    districtCode: "",
    smallDistrictCode: "",
    city: "",
    postcode: "",
    email: "",
    telephoneNo: "",
    phoneNo: "",
    appointmentDate: "",
    createdBy: "",
    createdDate: "",
    modifiedBy: "",
    modifiedDate: "",
    status: "",
    deleteStatus: "",
    pemCaw: "",
  };

  const {
    control,
    setValue,
    watch,
    getValues,
    handleSubmit,
    formState: { errors },
    reset: resetForm,
    setError,
    clearErrors,
  } = useForm<FieldValues>({
    defaultValues: defaultFormValues,
    mode: "onChange", // Enable real-time validation
  });

  useEffect(() => {
    if (auditorId) {
      fetchAuditor();
      if (auditor) {
        Object.entries(auditor).forEach(([key, value]) => {
          if (value == "" || value == null) {
            setValue(key, "-");
          } else {
            setValue(key, value);
          }
        });
      }
    }
  }, [fetchAuditor]);

  const { id: societyId } = useParams();

  const { mutate: saveAuditor, isLoading: isLoadingSaveAuditor } =
    useCustomMutation();
  const [JPNError, setJPNError] = useState(false);
  const {
    userICCorrect,
    userNameMatchIC,
    triggerICValidation,
    setUserICCorrect,
    setUserNameMatchIC,
    resetICValidation,
    integrationStatus,
  } = useICValidation({
    idType: watch("identificationType"),
    idNumber: watch("identificationNo"),
    fullName: watch("name"),
  });

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    const payload = filterEmptyValuesOnObject(data);
    if (errors.phoneNo) {
      return; // prevent submit on phone missmatching requirement
    }
    saveAuditor(
      {
        url: auditorId
          ? `${API_URL}/society/statement/auditor/${auditorId}/edit`
          : `${API_URL}/society/statement/auditor/create?societyId=${societyId}&branchId=${branchDataRedux.id}`,
        method: auditor ? "put" : "post",
        values: payload,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (
            data?.data?.status === "Success" ||
            data?.data?.status === "SUCCESS"
          ) {
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onSuccess: () => {
          setOpenConfirm(false);
          setDialogAlertSaveOpen(true);
        },
      }
    );
  };

  const closeSuccess = () => {
    setDialogAlertSaveOpen(false);
    navigate(-1);
  };

  // useEffect(() => {
  //   const type = getValues("identificationType");
  //   if (type === "1") {
  //     const idNoString =
  //       typeof watch("identificationNo") === "number"
  //         ? String(watch("identificationNo"))
  //         : watch("identificationNo") ?? ""; // Ensure string

  //     if (idNoString.length >= 6) {
  //       const parsedDate = dayjs(idNoString.substring(0, 6), "YYMMDD");

  //       if (parsedDate.isValid()) {
  //         setValue("dateOfBirth", parsedDate.toDate());
  //       }
  //     }
  //   }
  // }, [watch("identificationNo")]);
  useEffect(() => {
    const isMyKad =
      Number(watch("identificationType")) === 1 ||
      Number(watch("identificationType")) === 4;
    const nameReady = watch("name")?.trim() !== "";
    const idReady = watch("identificationNo")?.length === 12;
    if (integrationStatus === 0 && isMyKad) {
      setJPNError(true);
    } else {
      setJPNError(false);
    }
    if (integrationStatus === 1 && isMyKad && nameReady && idReady) {
      triggerICValidation();
    }
  }, [
    watch("identificationType"),
    watch("identificationNo"),
    watch("name"),
    integrationStatus,
  ]);

  useEffect(() => {
    const type = Number(getValues("identificationType"));
    if (type === 1 || type === 4) {
      setValue(
        "gender",
        autoGenderSetByIC(type, null, watch("identificationNo")?.toString())
      );

      setValue(
        "dateOfBirth",
        autoDOBSetByIC(type, null, watch("identificationNo")?.toString())
      );
    }
  }, [watch("identificationNo")]);

  useEffect(() => {
    const type = Number(getValues("identificationType"));
    if (type === 1 || !type) {
      setValue("nationalityStatus", 1);
    } else {
      setValue("nationalityStatus", 2);
    }
  }, [watch("identificationType")]);

  let identificationNoHelperText: string | undefined = undefined;
  if (
    watch("identificationType") === "1" ||
    watch("identificationType") === "4"
  ) {
    if (typeof errors.identificationNo?.message === "string") {
      identificationNoHelperText = errors.identificationNo.message;
    } else if (
      watch("identificationNo")?.length === 12 &&
      watch("name")?.trim() !== undefined &&
      !userICCorrect
    ) {
      identificationNoHelperText = t("IcDoesNotExist");
    } else if (watch("identificationNo")?.length < 12) {
      identificationNoHelperText = t("idNumberOnlyDigits");
    }
  } else if (typeof errors.identificationNo?.message === "string") {
    identificationNoHelperText = errors.identificationNo.message;
  }

  let nameHelperText: string | undefined = undefined;
  if (
    watch("identificationType") === "1" ||
    watch("identificationType") === "4"
  ) {
    if (typeof errors.name?.message === "string") {
      nameHelperText = errors.name.message;
    } else if (
      watch("identificationNo")?.length === 12 &&
      watch("name")?.trim() !== undefined &&
      !userNameMatchIC
    ) {
      nameHelperText = t("invalidName");
    }
  } else if (typeof errors.name?.message === "string") {
    nameHelperText = errors.name.message;
  }

  useEffect(() => {
    const values = getValues();
    if (watch("auditorType") === "D") {
      resetForm({
        ...defaultFormValues,
        auditorType: values.auditorType,
      });
    }
  }, [watch("auditorType")]);

  return (
    <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
        mb: 2,
      }}
    >
      <form onSubmit={(e) => e.preventDefault()}>
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("pilihanJuruaudit")}
          </Typography>

          <Controller
            name="auditorType"
            control={control}
            defaultValue={getValues("auditorType")}
            render={({ field }) => (
              <Input
                {...field}
                label={t("auditorType")}
                type="select"
                required
                onChange={(e) => setValue("auditorType", e.target.value)}
                options={[
                  { value: "L", label: "Bertauliah" },
                  { value: "D", label: "Dalaman" },
                ]}
              />
            )}
          />
          {watch("auditorType") === "D" && (
            <Controller
              name="committeeName"
              control={control}
              defaultValue={getValues("committeeName")}
              render={({ field }) => {
                const options = branchMembers
                  ? branchMembers?.data?.map(
                      (item: { id: any; committeeName: any }) => {
                        return {
                          value: item.committeeName, // Use name as value instead of id
                          label: item.committeeName || "-",
                        };
                      }
                    )
                  : [];

                // Find the current selected member by name
                const currentName = getValues("name");
                const selectedValue = currentName || "";
                return (
                  <Input
                    // required
                    {...field}
                    label={t("namaAhliPertubuhan")}
                    type="select"
                    value={selectedValue} // Use name as the selected value
                    onChange={(e) => {
                      const selectedName = e.target.value; // Get the selected name
                      const selectedOption = branchMembers?.data?.find(
                        (item: { id: any; committeeName: any }) =>
                          item.committeeName === selectedName
                      ); // Find the corresponding option by name
                      setValue(
                        "identificationType",
                        selectedOption?.identityType
                      );
                      setValue(
                        "identificationNo",
                        selectedOption?.committeeIcNo
                      );
                      setValue("titleCode", selectedOption?.titleCode);
                      setValue("email", selectedOption?.email);
                      setValue("phoneNo", selectedOption?.phoneNumber);
                      setValue("name", selectedOption?.committeeName ?? "-"); // Set the value as the label
                      let nationality = selectedOption?.nationalityStatus;
                      // Handle both string numbers and text values
                      if (
                        selectedOption?.nationalityStatus === "Warganegara" ||
                        selectedOption?.nationalityStatus === "1"
                      ) {
                        nationality = 1;
                      } else if (
                        selectedOption?.nationalityStatus ===
                          "Bukan Warganegara" ||
                        selectedOption?.nationalityStatus === "2"
                      ) {
                        nationality = 2;
                      }
                      if (nationality) {
                        setValue("nationalityStatus", Number(nationality));
                      }
                      setValue("gender", selectedOption?.gender ?? "-"); // Set the value as the label
                      setValue("placeOfBirth", selectedOption?.placeOfBirth);
                      setValue("committeeName", selectedOption?.id); // Store the ID in committeeName for backend
                    }}
                    options={options}
                  />
                );
              }}
            />
          )}
        </Box>

        {watch("auditorType") === "D" && (
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("maklumatJuruauditDalaman")}
            </Typography>

            <Controller
              name="appointmentDate"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    onChange={(newValue) =>
                      setValue("appointmentDate", newValue.target.value)
                    }
                    value={
                      getValues("appointmentDate")
                        ? dayjs(getValues("appointmentDate")).format(
                            "DD-MM-YYYY"
                          )
                        : ""
                    }
                    label={t("tarikhLantik")}
                    type="date"
                  />
                );
              }}
            />

            <Controller
              name="titleCode"
              control={control}
              defaultValue={getValues("titleCode")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("title")}
                  type="select"
                  options={ListGelaran}
                />
              )}
            />

            <Controller
              name="name"
              control={control}
              defaultValue={getValues("name")}
              render={({ field }) => (
                <Input
                  {...field}
                  disabled
                  required
                  label={t("fullName")}
                  type="text"
                />
              )}
            />

            <Controller
              name="gender"
              control={control}
              defaultValue={getValues("gender")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  disabled
                  label={t("gender")}
                  type="select"
                  value={getValues("gender")}
                  options={ListGender.map((item) => ({
                    ...item,
                    label: t(item.label),
                  }))}
                />
              )}
            />

            <Controller
              name="nationalityStatus"
              control={control}
              defaultValue={Number(getValues("nationalityStatus"))}
              render={({ field }) => (
                <Input
                  {...field}
                  disabled
                  required
                  label={t("citizenship")}
                  type="select"
                  value={Number(getValues("nationalityStatus"))}
                  options={CitizenshipStatus.map((item) => ({
                    ...item,
                    label: t(item.label),
                  }))}
                />
              )}
            />

            <Controller
              name="identificationType"
              control={control}
              defaultValue={getValues("identificationType")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  disabled
                  label={t("idType")}
                  type="select"
                  options={IdTypes.map((item) => ({
                    ...item,
                    label: t(item.label),
                  }))}
                />
              )}
            />

            <Controller
              name="identificationNo"
              rules={
                watch("auditorType") === "D"
                  ? {
                      required: t("fieldRequired"),
                      validate: (value) => {
                        const type = getValues("identificationType");
                        if (type === "1" || type === "4") {
                          if (value.length !== 12) {
                            return t("fieldRequired");
                          }
                        }
                        return true;
                      },
                    }
                  : {}
              }
              control={control}
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    required={watch("auditorType") === "D"}
                    disabled
                    {...field}
                    label={t("idNumber")}
                    error={!!identificationNoHelperText}
                    helperText={identificationNoHelperText}
                    inputProps={
                      getValues("identificationType") === "1" ||
                      getValues("identificationType") === "4"
                        ? {
                            inputMode: "numeric",
                            pattern: "[0-9]*",
                            maxLength: 12,
                            minLength: 12,
                          }
                        : undefined
                    }
                    onChange={(e) => {
                      const inputType = getValues("identificationType");
                      let value = e.target.value;

                      if (inputType === "1" || inputType === "4") {
                        value = value.replace(/\D/g, "").slice(0, 12);
                      }

                      setValue(field.name, value);
                    }}
                  />
                );
              }}
            />

            <Controller
              name="dateOfBirth"
              control={control}
              defaultValue={getValues("dateOfBirth")}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    label={t("dateOfBirth")}
                    type="date"
                    onChange={(newValue) =>
                      setValue("dateOfBirth", newValue.target.value)
                    }
                    value={
                      getValues("dateOfBirth")
                        ? dayjs(getValues("dateOfBirth")).format("DD-MM-YYYY")
                        : ""
                    }
                  />
                );
              }}
            />

            <Controller
              name="placeOfBirth"
              control={control}
              defaultValue={getValues("placeOfBirth")}
              rules={{
                required:
                  watch("auditorType") === "D"
                    ? t("validation.required")
                    : false,
              }}
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    {...field}
                    required={watch("auditorType") === "D"}
                    label={t("placeOfBirth")}
                    type="text"
                    error={!!error}
                    helperText={error?.message}
                  />
                );
              }}
            />

            <Controller
              name="employmentCode"
              control={control}
              defaultValue={getValues("employmentCode")}
              rules={{
                required:
                  watch("auditorType") === "D"
                    ? t("validation.required")
                    : false,
              }}
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    {...field}
                    required={watch("auditorType") === "D"}
                    // disabled
                    label={t("occupation")}
                    type="select"
                    options={occupationList}
                    error={!!error}
                    helperText={error?.message}
                  />
                );
              }}
            />

            <Controller
              name="address"
              control={control}
              defaultValue={getValues("address")}
              rules={{
                required:
                  watch("auditorType") === "D"
                    ? t("validation.required")
                    : false,
              }}
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    {...field}
                    // disabled
                    required={watch("auditorType") === "D"}
                    label={t("residentialAddress")}
                    type="text"
                    multiline
                    rows={3}
                    error={!!error}
                    helperText={error?.message}
                  />
                );
              }}
            />

            <Controller
              name="stateCode"
              control={control}
              defaultValue={getValues("stateCode")}
              rules={{
                required:
                  watch("auditorType") === "D"
                    ? t("validation.required")
                    : false,
              }}
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    {...field}
                    required={watch("auditorType") === "D"}
                    // disabled
                    label={t("state")}
                    type="select"
                    onChange={(e) => setValue("stateCode", e.target.value)}
                    value={parseInt(getValues("stateCode"))}
                    options={addressList
                      .filter((item: any) => item.pid === MALAYSIA)
                      .map((item: any) => ({
                        label: item.name,
                        value: item.id,
                      }))}
                    error={!!error}
                    helperText={error?.message}
                  />
                );
              }}
            />

            <Controller
              name="districtCode"
              control={control}
              defaultValue={getValues("districtCode")}
              rules={{
                required:
                  watch("auditorType") === "D"
                    ? t("validation.required")
                    : false,
              }}
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    {...field}
                    // disabled
                    required={watch("auditorType") === "D"}
                    label={t("district")}
                    type="select"
                    value={parseInt(getValues("districtCode"))}
                    onChange={(e) => setValue("districtCode", e.target.value)}
                    options={addressList
                      .filter(
                        (item: any) => item.pid === parseInt(watch("stateCode"))
                      )
                      .map((item: any) => ({
                        label: item.name,
                        value: item.id,
                      }))}
                    error={!!error}
                    helperText={error?.message}
                  />
                );
              }}
            />

            <Controller
              name="city"
              control={control}
              defaultValue={getValues("city")}
              render={({ field }) => (
                <Input {...field} label={t("city")} type="text" />
              )}
            />

            <Controller
              name="postcode"
              control={control}
              defaultValue={getValues("postcode")}
              rules={
                watch("auditorType") === "D"
                  ? {
                      required: t("validation.required"),
                      pattern: {
                        value: /^\d{5}$/, // ensures exactly 5 digits
                        message: t("validation.postcode"), // custom message
                      },
                    }
                  : {}
              }
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    {...field}
                    value={field.value || ""}
                    required={watch("auditorType") === "D"}
                    label={t("poskod")}
                    type="text"
                    inputMode="numeric"
                    onChange={(e) => {
                      const val = e.target.value.replace(/\D/g, ""); // keep digits only
                      if (val.length <= 5) {
                        field.onChange(val);
                      }
                    }}
                    error={!!error}
                    helperText={error?.message}
                  />
                );
              }}
            />

            <Controller
              name="email"
              control={control}
              defaultValue={getValues("email")}
              rules={
                watch("auditorType") === "D"
                  ? {
                      required: t("validation.required"),
                      pattern: {
                        value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                        message:
                          t("invalidEmail") ||
                          "Sila masukkan e-mel yang betul.",
                      },
                    }
                  : {}
              }
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    {...field}
                    required={watch("auditorType") === "D"}
                    label={t("email")}
                    type="email"
                    error={!!error}
                    helperText={error?.message}
                  />
                );
              }}
            />

           <Controller
              name="phoneNo"
              control={control}
              defaultValue={getValues("phoneNo")}
              rules={
                watch("auditorType") === "D"
                  ? {
                      required: t("requiredValidation"),
                      validate: (val) => {
                        if (!val) return true; // required will handle empties
                        const raw = val
                          .replace(/[^\d]/g, "")
                          .replace(/^60/, "");
                        return raw.length >= 8 || t("phoneDigitLimitWarning");
                      },
                    }
                  : {}
              }
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    {...field}
                    value={field.value || ""}
                    required={watch("auditorType") === "D"}
                    label={t("phoneNumber")}
                    type="text"
                    inputProps={{
                      inputMode: "numeric",
                    }}
                    onChange={(e) => {
                      const input = e.target as HTMLInputElement;
                      let raw = input.value.replace(/[^\d]/g, "");
                      // Allow empty input
                      if (!raw) {
                        setValue("phoneNo", null);
                        return;
                      }

                      // Remove leading 60 if present
                      if (raw.startsWith("60")) {
                        raw = raw.slice(2);
                      }

                      const limitedDigits = raw.slice(0, 10);
                      const formatted = "+60" + limitedDigits;

                      let error: any = "";
                      if (limitedDigits.length < 8) {
                        error = t("phoneDigitLimitWarning");
                      } else {
                        error = null;
                      }

                      setValue("phoneNo", formatted);

                      if (limitedDigits.length < 8) {
                        setError("phoneNo", {
                          type: "manual",
                          message: t("phoneDigitLimitWarning"),
                        });
                      } else {
                        clearErrors("phoneNo");
                      }
                    }}
                    onKeyDown={(e) => {
                      const input = e.target as HTMLInputElement;
                      const pos = input.selectionStart ?? 0;
                      const hasValue = input.value.length > 0;

                      // restrictions
                      if (hasValue) {
                        if (
                          (e.key.length === 1 && pos < 3) || // typing characters in +60
                          (e.key === "Backspace" && pos <= 3) || // backspacing into +60
                          (e.key === "Delete" && pos < 3) // deleting inside +60
                        ) {
                          e.preventDefault();
                        }
                      }
                    }}
                    onClick={(e) => {
                      const input = e.target as HTMLInputElement;
                      if (
                        input.value &&
                        input.selectionStart !== null &&
                        input.selectionStart < 3
                      ) {
                        // Move cursor to after +60 if user clicks inside prefix
                        setTimeout(() => {
                          input.setSelectionRange(3, 3);
                        }, 0);
                      }
                    }}
                    onFocus={(e) => {
                      const input = e.target as HTMLInputElement;
                      if (
                        input.value &&
                        input.selectionStart !== null &&
                        input.selectionStart < 3
                      ) {
                        // move cursor to after +60 on focus
                        setTimeout(() => {
                          input.setSelectionRange(3, 3);
                        }, 0);
                      }
                    }}
                    placeholder={
                      Number(getValues("identificationType")) === 1 ? "+60" : ""
                    }
                    // onChange={(e) => {
                    //   const numericValue = e.target.value.replace(
                    //     /[^0-9]/g,
                    //     ""
                    //   );
                    //   if (numericValue.length <= 11) {
                    //     field.onChange(numericValue);
                    //   }
                    // }}
                    error={!!error}
                    helperText={error?.message}
                  />
                );
              }}
            />

            <Controller
              name="telephoneNo"
              control={control}
              defaultValue={getValues("telephoneNo")}
              render={({ field }) => (
                <Input {...field} label={t("homeNumber")} type="text" />
              )}
            />
          </Box>
        )}

        {watch("auditorType") === "L" && (
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("maklumatJuruauditBertauliah")}
            </Typography>

           <Controller
              name="appointmentDate"
              control={control}
              rules={{
                required: t("validation.required"),
              }}
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    {...field}
                    required
                    onChange={(newValue) => {
                      setValue("appointmentDate", newValue.target.value);
                    }}
                    value={
                      getValues("appointmentDate")
                        ? dayjs(getValues("appointmentDate")).format(
                            "DD-MM-YYYY"
                          )
                        : ""
                    }
                    label={t("tarikhLantik")}
                    type="date"
                    error={!!error}
                    helperText={error?.message}
                  />
                );
              }}
            />
             <Controller
              name="name"
              rules={{
                required: t("validation.required"),
              }}
              control={control}
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!nameHelperText}
                    helperText={nameHelperText}
                    label={t("fullName")}
                  />
                );
              }}
            />
            {/*  ==========   */}
            <Controller
              name="gender"
              control={control}
              defaultValue={getValues("gender")}
              rules={{
                required: t("validation.required"),
              }}
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    {...field}
                    required
                    label={t("gender")}
                    type="select"
                    value={getValues("gender")}
                    options={ListGender.map((item) => ({
                      ...item,
                      label: t(item.label),
                    }))}
                    error={!!error}
                    helperText={error?.message}
                  />
                );
              }}
            />

            <Controller
              name="nationalityStatus"
              control={control}
              defaultValue={Number(getValues("nationalityStatus"))}
              rules={{
                required: t("validation.required"),
              }}
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    {...field}
                    disabled
                    required
                    label={t("citizenship")}
                    type="select"
                    value={Number(getValues("nationalityStatus"))}
                    options={CitizenshipStatus.map((item) => ({
                      ...item,
                      label: t(item.label),
                    }))}
                    error={!!error}
                    helperText={error?.message}
                  />
                );
              }}
            />

            <Controller
              name="identificationType"
              control={control}
              rules={{
                required: t("validation.required"),
              }}
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    required
                    {...field}
                    error={!!error}
                    helperText={error?.message}
                    label={t("idType")}
                    type="select"
                    options={IdTypes.filter(
                      (item) =>
                        Number(item.value) === 1 ||
                        Number(item.value) === 4 ||
                        Number(item.value) === 5
                    ).map((item) => ({
                      ...item,
                      label: t(item.label),
                    }))}
                    value={watch("identificationType")}
                    onChange={(e) => {
                      const inputType = getValues("identificationType");
                      let value = e.target.value;
                      if (
                        (inputType !== "1" && value === "1") ||
                        (inputType !== "4" && value === "4")
                      ) {
                        setValue("identificationNo", "");
                      }

                      setValue("identificationType", value);
                    }}
                  />
                );
              }}
            />

            <Controller
              name="identificationNo"
              rules={{
                required: t("fieldRequired"),
                validate: (value) => {
                  const type = getValues("identificationType");
                  if (type === "1" || type === "4") {
                    if (value.length !== 12) {
                      return t("fieldRequired");
                    }
                  }
                  return true;
                },
              }}
              control={control}
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    required
                    {...field}
                    label={t("idNumber")}
                    error={!!identificationNoHelperText}
                    helperText={identificationNoHelperText}
                    inputProps={
                      getValues("identificationType") === "1" ||
                      getValues("identificationType") === "4"
                        ? {
                            inputMode: "numeric",
                            pattern: "[0-9]*",
                            maxLength: 12,
                            minLength: 12,
                          }
                        : undefined
                    }
                    onChange={(e) => {
                      const inputType = getValues("identificationType");
                      let value = e.target.value;

                      if (inputType === "1" || inputType === "4") {
                        value = value.replace(/\D/g, "").slice(0, 12);
                      }

                      setValue(field.name, value);
                    }}
                  />
                );
              }}
            />

            {/*  ==========   */}
              <Controller
              name="licenseNo"
              control={control}
              defaultValue={getValues("licenseNo")}
              rules={{
                required: t("validation.required"),
              }}
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    {...field}
                    required
                    label={t("nomborLesen")}
                    type="text"
                    error={!!error}
                    helperText={error?.message}
                  />
                );
              }}
            />

            <Controller
              name="companyName"
              control={control}
              defaultValue={getValues("companyName")}
              render={({ field }) => (
                <Input
                  {...field}
                  // required
                  label={capitalizeWords(t("companyName"))}
                  type="text"
                />
              )}
            />
            <Controller
              name="companyNo"
              control={control}
              defaultValue={getValues("companyNo")}
              render={({ field }) => (
                <Input
                  {...field}
                  // required
                  label={t("noSyarikat")}
                  type="text"
                />
              )}
            />
            <Controller
              name="address"
              control={control}
              defaultValue={getValues("address")}
              render={({ field }) => (
                <Input
                  {...field}
                  label={t("companyAddress")}
                  type="text"
                  multiline
                  rows={3}
                />
              )}
            />
            <Controller
              name="stateCode"
              control={control}
              defaultValue={getValues("stateCode")}
              render={({ field }) => (
                <Input
                  {...field}
                  // required
                  label={t("state")}
                  type="select"
                  value={parseInt(getValues("stateCode"))}
                  onChange={(e) => setValue("stateCode", e.target.value)}
                  options={addressList
                    .filter((item: any) => item.pid === MALAYSIA)
                    .map((item: any) => ({
                      label: item.name,
                      value: item.id,
                    }))}
                />
              )}
            />

            <Controller
              name="districtCode"
              control={control}
              defaultValue={getValues("districtCode")}
              render={({ field }) => (
                <Input
                  {...field}
                  // required
                  label={t("district")}
                  type="select"
                  value={parseInt(getValues("districtCode"))}
                  onChange={(e) => setValue("districtCode", e.target.value)}
                  options={addressList
                    .filter(
                      (item: any) => item.pid === parseInt(watch("stateCode"))
                    )
                    .map((item: any) => ({ label: item.name, value: item.id }))}
                />
              )}
            />
            <Controller
              name="city"
              control={control}
              defaultValue={getValues("city")}
              render={({ field }) => (
                <Input {...field} label={t("city")} type="text" />
              )}
            />

            <Controller
              name="postcode"
              control={control}
              defaultValue={getValues("postcode")}
              render={({ field: { onChange, value, ...rest } }) => (
                <Input
                  {...rest}
                  value={value}
                  // required
                  label={t("poskod")}
                  type="text"
                  inputMode="numeric"
                  onChange={(e) => {
                    const onlyDigits = e.target.value
                      .replace(/\D/g, "")
                      .slice(0, 5);
                    onChange(onlyDigits);
                  }}
                />
              )}
            />

            <Controller
              name="email"
              control={control}
              defaultValue={getValues("email")}
              rules={{
                required: t("validation.required"),
                pattern: {
                  value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  message: t("invalidEmail"),
                },
              }}
              render={({ field, fieldState: { error } }) => (
                <Input
                  {...field}
                  required
                  label={t("emelSyarikat")}
                  type="email"
                  error={!!error}
                  helperText={error?.message}
                />
              )}
            />

           <Controller
              name="phoneNo"
              control={control}
              defaultValue={getValues("phoneNo")}
              rules={{
                required: t("validation.required"),
                validate: (val) => {
                  if (!val) return true; // required will handle empties
                  const raw = val.replace(/[^\d]/g, "").replace(/^60/, "");
                  return raw.length >= 8 || t("phoneDigitLimitWarning");
                },
              }}
              render={({
                field: { onChange, value, ...rest },
                fieldState: { error },
              }) => (
                <Input
                  {...rest}
                  value={value}
                  required
                  label={t("phoneNumber")}
                  type="text"
                  inputProps={{
                    inputMode: "numeric",
                  }}
                  onChange={(e) => {
                    const input = e.target as HTMLInputElement;
                    let raw = input.value.replace(/[^\d]/g, "");
                    // Allow empty input
                    if (!raw) {
                      setValue("phoneNo", null);
                      return;
                    }

                    // Remove leading 60 if present
                    if (raw.startsWith("60")) {
                      raw = raw.slice(2);
                    }

                    const limitedDigits = raw.slice(0, 10);
                    const formatted = "+60" + limitedDigits;

                    let error: any = "";
                    if (limitedDigits.length < 8) {
                      error = t("phoneDigitLimitWarning");
                    } else {
                      error = null;
                    }

                    setValue("phoneNo", formatted);

                    if (limitedDigits.length < 8) {
                      setError("phoneNo", {
                        type: "manual",
                        message: t("phoneDigitLimitWarning"),
                      });
                    } else {
                      clearErrors("phoneNo");
                    }
                  }}
                  onKeyDown={(e) => {
                    const input = e.target as HTMLInputElement;
                    const pos = input.selectionStart ?? 0;
                    const hasValue = input.value.length > 0;

                    // restrictions
                    if (hasValue) {
                      if (
                        (e.key.length === 1 && pos < 3) || // typing characters in +60
                        (e.key === "Backspace" && pos <= 3) || // backspacing into +60
                        (e.key === "Delete" && pos < 3) // deleting inside +60
                      ) {
                        e.preventDefault();
                      }
                    }
                  }}
                  onClick={(e) => {
                    const input = e.target as HTMLInputElement;
                    if (
                      input.value &&
                      input.selectionStart !== null &&
                      input.selectionStart < 3
                    ) {
                      // Move cursor to after +60 if user clicks inside prefix
                      setTimeout(() => {
                        input.setSelectionRange(3, 3);
                      }, 0);
                    }
                  }}
                  onFocus={(e) => {
                    const input = e.target as HTMLInputElement;
                    if (
                      input.value &&
                      input.selectionStart !== null &&
                      input.selectionStart < 3
                    ) {
                      // move cursor to after +60 on focus
                      setTimeout(() => {
                        input.setSelectionRange(3, 3);
                      }, 0);
                    }
                  }}
                  placeholder={
                    Number(getValues("identificationType")) === 1 ? "+60" : ""
                  }
                  error={!!error}
                  helperText={error?.message}
                />
              )}
            />
          </Box>
        )}

        <Box
          sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 2 }}
        >
          <ButtonOutline onClick={handleBack}>{t("back")}</ButtonOutline>
          <ButtonPrimary
            disabled={
              watch("identificationNo")?.length < 1 ||
              (watch("name") === "" && true) ||
              (watch("auditorType") !== "L" &&
                (watch("appointmentDate") === "" ||
                  watch("placeOfBirth") === "")) ||
              ((Number(watch("identificationType")) === 1 ||
                Number(watch("identificationType")) === 4) &&
                (watch("identificationNo")?.length < 12 ||
                  watch("name")?.trim === "" ||
                  !userICCorrect ||
                  !userNameMatchIC))
            }
            onClick={() => setOpenConfirm(true)}
          >
            {t("save")}
          </ButtonPrimary>
        </Box>
      </form>
      <ConfirmationDialog
        status={1}
        open={openConfirm}
        onClose={() => setOpenConfirm(false)}
        title={t("warning")}
        message={`${t("auditorAddConfirmation")}`}
        onConfirm={() => {
          handleSubmit(onSubmit)();
        }}
        onCancel={() => setOpenConfirm(false)}
        buttonPrimaryLabel="back"
        buttonTextLabel="ya"
        isMutation={isLoadingSaveAuditor}
      />
      <NewAlertDialog
        open={dialogAlertSaveOpen}
        onClose={() => closeSuccess()}
        message={t("auditorCreatedSuccessfully")}
      />
    </Box>
  );
};

export default CreateJuruAudit;
