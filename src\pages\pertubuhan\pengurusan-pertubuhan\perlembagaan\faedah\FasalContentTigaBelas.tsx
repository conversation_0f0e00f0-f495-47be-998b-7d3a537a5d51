import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import { months, RegExNumbers, RegExText } from "../../../../../helpers/enums";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import { formatAndValidateNumber } from "@/helpers";
import CustomPopover from "@/components/popover";
interface FasalContentTigaBelasFaedahProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentTigaBelasFaedah: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [kuasaPerbelanjaan, setKuasaPerbelanjaan] = useState("");
  const [kuasaPerbelanjaanKata, setKuasaPerbelanjaanKata] = useState("");
  const [kuasaPerbelanjaanJawatankuasa, setKuasaPerbelanjaanJawatankuasa] =
    useState("");
  const [
    kuasaPerbelanjaanJawatankuasaKata,
    setKuasaPerbelanjaanJawatankuasaKata,
  ] = useState("");
  const [perbelanjaanDibenarkan, setPerbelanjaanDibenarkan] = useState("");
  const [perbelanjaanDibenarkanKata, setPerbelanjaanDibenarkanKata] =
    useState("");
  const [tahunKewanganBermula, setTahunKewanganBermula] = useState("");

  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!kuasaPerbelanjaan) {
      errors.kuasaPerbelanjaan = t("fieldRequired");
    }

    if (!kuasaPerbelanjaanKata) {
      errors.kuasaPerbelanjaanKata = t("fieldRequired");
    }

    if (!kuasaPerbelanjaanJawatankuasa) {
      errors.kuasaPerbelanjaanJawatankuasa = t("fieldRequired");
    }

    if (!kuasaPerbelanjaanJawatankuasaKata) {
      errors.kuasaPerbelanjaanJawatankuasaKata = t("fieldRequired");
    }

    if (!perbelanjaanDibenarkan) {
      errors.perbelanjaanDibenarkan = t("fieldRequired");
    }

    if (!perbelanjaanDibenarkanKata) {
      errors.perbelanjaanDibenarkanKata = t("fieldRequired");
    }

    if (!tahunKewanganBermula) {
      errors.tahunKewanganBermula = t("fieldRequired");
    }

    return errors;
  };

  /*const clauseContent = `
1. Ahli boleh menamakan seseorang sebagai orang yang akan menerima wang bantuan daripada golongan yang disebutkan iaitu suami, isteri, anak (termasuk anak angkat), cucu, ibu, bapa, ibu mertua, bapa mertua, menantu, saudara seibu dan sebapa, saudara seibu atau sebapa, anak kepada saudara seibu dan sebapa, anak kepada saudara seibu atau sebapa, bapa atau ibu saudara seibu dan sebapa, atau anak kepada bapa atau ibu saudara seibu atau sebapa kepada ahli. Seseorang ahli boleh menamakan mana-mana orang selain daripada golongan tersebut di atas dengan syarat beliau menandatangani suatu perakuan bertulis yang disaksikan oleh seorang Jaksa Pendamai atau seorang Majistret atau Pendaftar/Penolong Pendaftar.

2. Tiap-tiap ahli hendaklah diberi satu (1) salinan Perlembagaan Pertubuhan apabila diterima menjadi ahli secara percuma. Mana-mana orang boleh diberi satu salinan Perlembagaan Pertubuhan apabila diminta dan perlu membayar wang sebanyak tidak lebih daripada RM5.00 (Ringgit Malaysia Lima Sahaja).

3. Tiap-tiap ahli hendaklah diberi satu (1) salinan kunci kira-kira, penyata penerimaan dan perbelanjaan atau penyata kewangan lain-lain yang sempurna diaudit apabila diminta secara percuma.

4. Buku-buku Pertubuhan ini dan daftar ahlinya boleh diperiksa oleh mana-mana ahli atau seseorang yang mempunyai kepentingan dalam kumpulan wang tabungan Pertubuhan dengan syarat memaklumkan kepada Setiausaha lima (5) hari terlebih dahulu.

5. Ahli-ahli yang ada kemusykilan terhadap pegawai-pegawai Pertubuhan atau Pertubuhan ini boleh mengemukakan kemusykilannya dalam Mesyuarat Agung dan keputusan Mesyuarat Agung adalah muktamad. Bekas ahli dan mereka yang bersangkutan dengan Pertubuhan ini boleh mengemukakan kemusykilan dalam Mesyuarat Agung melalui ahli Pertubuhan.

6. Sesuatu perbelanjaan yang lebih daripada RM ${
    kuasaPerbelanjaanJawatankuasa || "<<kuasa perbelanjaan jawatankuasa>>"
  } (Ringgit Malaysia ${
    kuasaPerbelanjaanJawatankuasaKata ||
    "<<kuasa perbelanjaan jawatankuasa-dalam perkataan>>"
  } Sahaja) tidak boleh dilakukan sebelum diluluskan terlebih dahulu oleh Mesyuarat Jawatankuasa dan sesuatu perbelanjaan yang lebih daripada RM ${
    kuasaPerbelanjaan || "<<kuasa perbelanjaan mesyuarat agung>>"
  } (Ringgit Malaysia ${
    kuasaPerbelanjaanKata ||
    "<<kuasa perbelanjaan mesyuarat agung-dalam perkataan>>"
  } Sahaja) tidak boleh dilakukan sebelum diluluskan terlebih dahulu oleh Mesyuarat Agung. Perbelanjaan RM ${
    perbelanjaanDibenarkan || "<<perbelanjaan yang dibenarkan>>"
  } (Ringgit Malaysia ${
    perbelanjaanDibenarkanKata ||
    "<<perbelanjaan yang dibenarkan-dalam perkataan>>"
  } Sahaja) dan ke bawah boleh dibenarkan oleh Bendahari bersama-sama dengan Pengerusi dan Setiausaha dan disahkan dalam Mesyuarat Jawatankuasa kemudiannya. Bagaimanapun pembayaran bantuan yang ditetapkan dalam Fasal 10 di atas tidak tertakluk kepada sekatan ini dan boleh dibayar jika syarat-syarat mendapatkan bantuan telah disempurnakan.

7. Ahli yang sengaja melanggar Perlembagaan Pertubuhan ini atau berkelakuan tidak baik sehingga mencemarkan nama baik atau mendatangkan kerugian kepada Pertubuhan ini boleh dipecat daripada menjadi ahli oleh Mesyuarat Jawatankuasa. Sebelum dipecat ahli tersebut hendaklah diberitahu secara bertulis sebab-sebab pemecatan dan ahli tersebut perlu diberi peluang untuk memberi apa-apa penjelasan secara bertulis atau dengan menghadiri Mesyuarat Jawatankuasa yang diadakan khas untuknya. Jika dua pertiga (2/3) Ahli-Ahli Jawatankuasa yang hadir dalam mesyuarat mengundi menyokong pemecatan, maka keputusan tersebut berkuatkuasa dengan serta-merta. Sekiranya ahli tidak berpuas hati rayuan boleh dikemukakan kepada Mesyuarat Agung selaras dengan peruntukan dalam perenggan 5 di atas.

8. Ahli yang hendak berhenti daripada menjadi ahli Pertubuhan hendaklah memberi kenyataan bertulis dua (2) minggu terlebih dahulu kepada Setiausaha dan menjelaskan segala hutangnya.

9. Tahun kewangan Pertubuhan ini hendaklah bagi tempoh 12 bulan iaitu bermula dari ${
    tahunKewanganBermula || "<<tahun kewangan bermula>>"
  }.
`;*/

  //const clause13 = localStorage.getItem("clause13");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause = JSON.parse(clause13);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause9Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause.societyName);
      if(clause.constitutionValues.length > 0){ 
      const fieldMappings: Record<string, (value: string) => void> = {
        "Kuasa Perbelanjaan Mesyuarat Agung": setKuasaPerbelanjaan,
        "Kuasa Perbelanjaan Mesyuarat Agung (Tulis Dalam Perkataan)": setKuasaPerbelanjaanKata,
        "Kuasa Perbelanjaan Jawatankuasa": setKuasaPerbelanjaanJawatankuasa,
        "Kuasa Perbelanjaan Jawatankuasa (Tulis Dalam Perkataan)": setKuasaPerbelanjaanJawatankuasaKata,
        "Perbelanjaan yang Dibenarkan": setPerbelanjaanDibenarkan,
        "Perbelanjaan yang Dibenarkan (Tulis Dalam Perkataan)": setPerbelanjaanDibenarkanKata,
        "Tahun Kewangan Bermula (Tahun Kewangan Bermula Dari Tahun Kewangan Yang Dibenarkan)": setTahunKewanganBermula
      };

      if(clause.constitutionValues){
        clause.constitutionValues.forEach((item:any) => {
          const setter = fieldMappings[item.titleName];
          if (setter && item.definitionName) {
            setter(item.definitionName);
          }
        });
      } 
     
      setIsEdit(clause.edit);
    }
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id } = useParams();

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  let clauseContent = clause.clauseContent;
  //clauseContent = clauseContent.replaceAll(/<<jumlah wang tangan yang dibenarkan dalam tangan>>/gi, `<b>${jumlahWangTangan || '<<jumlah wang tangan yang dibenarkan dalam tangan>>'}</b>`);
  //clauseContent = clauseContent.replaceAll(/<<jumlah wang dibenarkan-tulis dalam perkataan>>/gi, `<b>${jumlahWangTanganKata || '<<jumlah wang dibenarkan-tulis dalam perkataan>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<perbelanjaan yang dibenarkan>>/gi,
    `<b>${perbelanjaanDibenarkan || "<<perbelanjaan yang dibenarkan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<perbelanjaan yang dibenarkan -dalam perkataan>>/gi,
    `<b>${
      perbelanjaanDibenarkanKata ||
      "<<perbelanjaan yang dibenarkan -dalam perkataan>>"
    }</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${tempohPelucutanWaktu || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan mesyuarat agung>>/gi,
    `<b>${kuasaPerbelanjaan || "<<kuasa perbelanjaan mesyuarat agung>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahPengerusi || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan mesyuarat agung-dalam perkataan>>/gi,
    `<b>${
      kuasaPerbelanjaanKata ||
      "<<kuasa perbelanjaan mesyuarat agung-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan jawatankuasa>>/gi,
    `<b>${
      kuasaPerbelanjaanJawatankuasa || "<<kuasa perbelanjaan jawatankuasa>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kuasa perbelanjaan jawatankuasa-dalam perkataan>>/gi,
    `<b>${
      kuasaPerbelanjaanJawatankuasaKata ||
      "<<kuasa perbelanjaan jawatankuasa-dalam perkataan>>"
    }</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<tempoh yang dibenarkan utk masukkan wang ke dalam bank>>/gi, `<b>${tempohDibenarkan || '<<tempoh yang dibenarkan utk masukkan wang ke dalam bank>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<tahun kewangan bermula>>/gi,
    `<b>${tahunKewanganBermula || "<<tahun kewangan bermula>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("permulaanTahunKewangan")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography sx={labelStyle}>
                {t("tahunKewanganBermula")}{" "}
                <Typography sx={{ display: "inline", color: "red" }}>
                  *
                </Typography>
              </Typography>
              <CustomPopover
                customStyles={{ maxWidth: "250px" }}
                content={
                  <Typography sx={{ color: "#666666" }}>
                    {t("contohJan")}
                  </Typography>
                }
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required>
              <Select
                required
                size="small"
                value={tahunKewanganBermula}
                displayEmpty
                onChange={(e) => {
                  setTahunKewanganBermula(e.target.value);
                }}
              >
                {months.map((i) => {
                  return (
                    <MenuItem value={i.value} selected>
                      {i.label}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("kuasaPerbelanjaan")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("kuasaPerbelanjaan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={kuasaPerbelanjaan}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setKuasaPerbelanjaan(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaan: "",
                  }));
                } else {
                  e.preventDefault();
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaan}
              helperText={formErrors.kuasaPerbelanjaan}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="contoh : dua puluh "
              value={kuasaPerbelanjaanKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setKuasaPerbelanjaanKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanKata: "",
                  }));
                } else {
                  e.preventDefault();
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaanKata}
              helperText={formErrors.kuasaPerbelanjaanKata}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("kuasaPerbelanjaanJawatankuasa")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("kuasaPerbelanjaanJawatankuasa")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={kuasaPerbelanjaanJawatankuasa}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setKuasaPerbelanjaanJawatankuasa(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasa: "",
                  }));
                } else {
                  e.preventDefault();
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasa: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaanJawatankuasa}
              helperText={formErrors.kuasaPerbelanjaanJawatankuasa}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              placeholder="contoh : dua puluh "
              required
              value={kuasaPerbelanjaanJawatankuasaKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setKuasaPerbelanjaanJawatankuasaKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasaKata: "",
                  }));
                } else {
                  e.preventDefault();
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    kuasaPerbelanjaanJawatankuasaKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.kuasaPerbelanjaanJawatankuasaKata}
              helperText={formErrors.kuasaPerbelanjaanJawatankuasaKata}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("perbelanjaanDibenarkan")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("perbelanjaanDibenarkan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={perbelanjaanDibenarkan}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setPerbelanjaanDibenarkan(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    perbelanjaanDibenarkan: "",
                  }));
                } else {
                  setPerbelanjaanDibenarkan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    perbelanjaanDibenarkan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.perbelanjaanDibenarkan}
              helperText={formErrors.perbelanjaanDibenarkan}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              placeholder="contoh : dua puluh "
              required
              value={perbelanjaanDibenarkanKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setPerbelanjaanDibenarkanKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    perbelanjaanDibenarkanKata: "",
                  }));
                } else {
                  setPerbelanjaanDibenarkanKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    perbelanjaanDibenarkanKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.perbelanjaanDibenarkanKata}
              helperText={formErrors.perbelanjaanDibenarkanKata}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            const errors = validateForm();
            if (Object.keys(errors).length > 0) {
              setFormErrors(errors);
              return;
            }

            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kuasaPerbelanjaan,
                  titleName: "Kuasa Perbelanjaan Mesyuarat Agung",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kuasaPerbelanjaanKata,
                  titleName:
                    "Kuasa Perbelanjaan Mesyuarat Agung (Tulis Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kuasaPerbelanjaanJawatankuasa,
                  titleName: "Kuasa Perbelanjaan Jawatankuasa",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kuasaPerbelanjaanJawatankuasaKata,
                  titleName:
                    "Kuasa Perbelanjaan Jawatankuasa (Tulis Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: perbelanjaanDibenarkan,
                  titleName: "Perbelanjaan yang Dibenarkan",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: perbelanjaanDibenarkanKata,
                  titleName:
                    "Perbelanjaan yang Dibenarkan (Tulis Dalam Perkataan)",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tahunKewanganBermula,
                  titleName:
                    "Tahun Kewangan Bermula (Tahun Kewangan Bermula Dari Tahun Kewangan Yang Dibenarkan)",
                },
              ],
              clause: "clause13",
              clauseCount: 13,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentTigaBelasFaedah;
