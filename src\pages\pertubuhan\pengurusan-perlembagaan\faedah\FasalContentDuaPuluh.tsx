import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { SingleFileInput } from "../../../../components/input";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import FileInput from "../../../../components/input/FileInput";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import { DocumentUploadType, useUploadPresignedUrl } from "@/helpers";
import AsalContent from "@/components/asalFasalContent";
import CustomPopover from "@/components/popover";

interface FasalContentDuaPuluhFaedahProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: [];
  name: string;
}

export const FasalContentDuaPuluhFaedah: React.FC<
  FasalContentDuaPuluhFaedahProps
> = ({ activeStep, setActiveStep, clause, asalData, name }) => {
  const [asal, setAsal] = useState<any>(null);
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [keteranganBendera, setKeteranganBendera] = useState("");
  const [bendera, setBendera] = useState("");
  const [keteranganLambang, setKeteranganLambang] = useState("");
  const [lambang, setLambang] = useState("");
  const [keteranganLencana, setKeteranganLencana] = useState("");
  const [lencana, setLencana] = useState("");
  const [fileUrl, setFileUrl] = useState("");
  const [currentUpload, setCurrentUpload] = useState("");

  const [clauseContentId, setClauseContentId] = useState<string|number>("");;

  const [dataId, setDataId] = useState<number | null>(null);

  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  useEffect(() => {
    if (!fileUrl) return;

    if (currentUpload === "bendera") {
      setBendera(fileUrl);
    } else if (currentUpload === "lambang") {
      setLambang(fileUrl);
    } else if (currentUpload === "lencana") {
      setLencana(fileUrl);
    }
  }, [fileUrl]);

  const { id, clauseId } = useParams();
  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName, address, mailingAddress } =
          responseData?.data?.data;
        setNamaPertubuhan(societyName);
      },
    },
  });

  const { upload } = useUploadPresignedUrl({
    onSuccessUpload: (data) => {
      const fileUrl = data?.data?.data?.url;

      setFileUrl(fileUrl);
    },
  });

  const uploadedFilesRef = useRef<(File | null)[]>([null, null, null]);

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    if (isViewMode) return;
    const file = event.target.files?.[0];
    if (!file) return;
    const isDuplicate = uploadedFilesRef.current.some((existingFile, i) => {
      return (
        existingFile &&
        existingFile.name === file.name &&
        existingFile.size === file.size &&
        i !== index
      );
    });
    if (isDuplicate) {
      event.target.value = "";
      return;
    }
    uploadedFilesRef.current[index] = file;
    const name = event.target.name;
    setCurrentUpload(name);
    upload({
      params: {
        type: DocumentUploadType.SOCIETY,
        societyId,
      },
      file,
    });
  };

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause = JSON.parse(clause20);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause19Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause.societyName);
      setKeteranganBendera(clause.constitutionValues[0]?.definitionName);
      setBendera(clause.constitutionValues[1]?.definitionName);
      setKeteranganLambang(clause.constitutionValues[2]?.definitionName);
      setLambang(clause.constitutionValues[3]?.definitionName);
      setKeteranganLencana(clause.constitutionValues[4]?.definitionName);
      setLencana(clause.constitutionValues[5]?.definitionName);
      setIsEdit(clause.edit);
      setAsal(
        asalData.find((item: any) => item.clauseNo === clause.clauseNo
        ) || ""
      );
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  const benderaFile = `${
    bendera ? `<img width="200" src="${bendera}" alt="Bendera" />` : ""
  }`;
  const lambangFile = `${
    lambang ? `<img width="200" src="${lambang}" alt="Lambang" />` : ""
  }`;
  const lencanaFile = `${
    lencana ? `<img width="200" src="${lencana}" alt="Lencana" />` : ""
  }`;

  let clauseContent = clause.clauseContent;

  clauseContent = clauseContent.replaceAll(
    /<<file Bendera>>/gi,
    `<b>${benderaFile || "<<file Bendera>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<file Lambang>>/gi,
    `<b>${lambangFile || "<<file Lambang>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<file Lencana>>/gi,
    `<b>${lencanaFile || "<<file Lencana>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<Keterangan Bendera>>/gi,
    `<b>${keteranganBendera || "<<Keterangan Bendera>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<Keterangan Lambang>>/gi,
    `<b>${keteranganLambang || "<<Keterangan Lambang>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<Keterangan Lencana>>/gi,
    `<b>${keteranganLencana || "<<Keterangan Lencana>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  return (
    <>
      <AsalContent
        asalData={asal?.description || ""}
        clauseId={clauseId}
        name={name}
      />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bendera")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <Typography sx={labelStyle}>{t("bendera")}</Typography>
            <CustomPopover
                customStyles={{ maxWidth: "450px" }}
                content={
                  <>
                    <Typography
                      variant="body2"
                      sx={{ lineHeight: 1.5, color: "#666666" }}
                    >
                   Pertubuhan tidak dibenarkan untuk menggunakan lambang atau sebahagian daripada lambang berikut:
                    <ol>
                      <li>Jata Negara dan Negeri</li>
                      <li>Bendera Malaysia</li>
                      <li>Bendera Negeri-Negeri</li>
                      <li>Lambang Kebesaran Istana</li>
                      <li>Lambang Agensi</li>
                      <li>Kalimah Allah</li>
                      <li>Gangsterism</li>
                    </ol>
                    </Typography>
                  </>
                }
              />
           </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <FileInput
              acceptedFormats={[".jpg", ".jpeg", ".png"]}
              buttonText="Click to upload"
              id="bendera"
              name="bendera"
              value={bendera}
              currentIndex={0}
              uploadedFilesRef={uploadedFilesRef}
              onChange={(e) => handleFileChange(e, 0)}
              t={t}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography sx={labelStyle}>{t("keteranganBendera")}</Typography>
              <CustomPopover
                content={
                  <>
                    <Typography
                      variant="body2"
                      sx={{ lineHeight: 1.5, color: "#666666" }}
                    >
                      Memberi keterangan mengenai setiap reka bentuk dan warna
                      yang digunakan.
                    </Typography>
                  </>
                }
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              placeholder={`${t("keteranganBendera")}`}
              fullWidth
              required
              value={keteranganBendera}
              onChange={(e) => setKeteranganBendera(e.target.value)}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("lambang")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <Typography sx={labelStyle}>{t("lambang")}</Typography>
            <CustomPopover
                customStyles={{ maxWidth: "450px" }}
                content={
                  <>
                    <Typography
                      variant="body2"
                      sx={{ lineHeight: 1.5, color: "#666666" }}
                    >
                   Pertubuhan tidak dibenarkan untuk menggunakan lambang atau sebahagian daripada lambang berikut:
                    <ol>
                      <li>Jata Negara dan Negeri</li>
                      <li>Bendera Malaysia</li>
                      <li>Bendera Negeri-Negeri</li>
                      <li>Lambang Kebesaran Istana</li>
                      <li>Lambang Agensi</li>
                      <li>Kalimah Allah</li>
                      <li>Gangsterism</li>
                    </ol>
                    </Typography>
                  </>
                }
              />
           </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <FileInput
              acceptedFormats={[".jpg", ".jpeg", ".png"]}
              buttonText="Click to upload"
              id="lambang"
              name="lambang"
              value={lambang}
              currentIndex={1}
              uploadedFilesRef={uploadedFilesRef}
              onChange={(e) => handleFileChange(e, 1)}
              t={t}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography sx={labelStyle}>{t("keteranganLambang")}</Typography>
              <CustomPopover
                content={
                  <>
                    <Typography
                      variant="body2"
                      sx={{ lineHeight: 1.5, color: "#666666" }}
                    >
                      Memberi keterangan mengenai setiap reka bentuk dan warna
                      yang digunakan.
                    </Typography>
                  </>
                }
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              placeholder={`${t("keteranganLambang")}`}
              fullWidth
              required
              disabled={isViewMode}
              value={keteranganLambang}
              onChange={(e) => setKeteranganLambang(e.target.value)}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("lencana")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <Typography sx={labelStyle}>{t("lencana")}</Typography>
            <CustomPopover
                customStyles={{ maxWidth: "450px" }}
                content={
                  <>
                    <Typography
                      variant="body2"
                      sx={{ lineHeight: 1.5, color: "#666666" }}
                    >
                   Pertubuhan tidak dibenarkan untuk menggunakan lambang atau sebahagian daripada lambang berikut:
                    <ol>
                      <li>Jata Negara dan Negeri</li>
                      <li>Bendera Malaysia</li>
                      <li>Bendera Negeri-Negeri</li>
                      <li>Lambang Kebesaran Istana</li>
                      <li>Lambang Agensi</li>
                      <li>Kalimah Allah</li>
                      <li>Gangsterism</li>
                    </ol>
                    </Typography>
                  </>
                }
              />
           </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <FileInput
              acceptedFormats={[".jpg", ".jpeg", ".png"]}
              buttonText="Click to upload"
              id="lencana"
              name="lencana"
              value={lencana}
              currentIndex={2}
              uploadedFilesRef={uploadedFilesRef}
              onChange={(e) => handleFileChange(e, 2)}
              t={t}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography sx={labelStyle}>{t("keteranganLencana")}</Typography>
              <CustomPopover
                content={
                  <>
                    <Typography
                      variant="body2"
                      sx={{ lineHeight: 1.5, color: "#666666" }}
                    >
                      Memberi keterangan mengenai setiap reka bentuk dan warna
                      yang digunakan.
                    </Typography>
                  </>
                }
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              placeholder={`${t("keteranganLencana")}`}
              fullWidth
              required
              disabled={isViewMode}
              value={keteranganLencana}
              onChange={(e) => setKeteranganLencana(e.target.value)}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
                whiteSpace: "pre-wrap",
                wordWrap: "break-word",
              }}
            >
              <ol style={{ listStyle: "none", paddingLeft: 0 }}>
                {clauseContent.split(/\n\n+/).map((item, index) => (
                  <li key={index}>
                    {item
                      //.replace(/^\d+\.\s*/, "")
                      .split("\n")
                      .map((line, i) => {
                        if (line.includes("img")) {
                          return (
                            <Typography
                              component="p"
                              sx={{
                                marginBottom: 1,
                                color: "#666666",
                                fontWeight: "400 !important",
                                textAlign: "center",
                              }}
                              dangerouslySetInnerHTML={{ __html: line }}
                            />
                          );
                        } else {
                          return (
                            <Typography
                              key={i}
                              sx={{
                                marginBottom: 1,
                                color: "#666666",
                                fontWeight: "400 !important",
                              }}
                              dangerouslySetInnerHTML={{
                                __html: `${line.trim()}`,
                              }}
                            ></Typography>
                          );
                        }
                      })}
                  </li>
                ))}
              </ol>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              let checkAllImgUpload = 0;

              if (bendera && lambang && lencana) {
                checkAllImgUpload = 0;
              } else {
                checkAllImgUpload = 1;
              }
              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                hideConstitution: checkAllImgUpload,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: keteranganBendera,
                    titleName: "Keterangan Bendera",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bendera,
                    titleName: "Bendera",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: keteranganLambang,
                    titleName: "Keterangan Lambang",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: lambang,
                    titleName: "Lambang",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: keteranganLencana,
                    titleName: "Keterangan Lencana",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: lencana,
                    titleName: "Lencana",
                  },
                ],
                clause: "clause20",
                clauseCount: 20,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentDuaPuluhFaedah;
