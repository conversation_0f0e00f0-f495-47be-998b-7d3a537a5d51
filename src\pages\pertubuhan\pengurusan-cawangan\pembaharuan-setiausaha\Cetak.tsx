import React, { useRef, useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useQuery, downloadFile, useMutation } from "@/helpers";

import { Box, Typography, Button, CircularProgress } from "@mui/material";
import {
  ButtonOutline,
  ButtonPrimary,
  CustomSkeleton,
  DisabledTextField,
  FormFieldRow,
  Label,
} from "@/components";
import PrintPage from "./PrintPage";

import { ISecretaryBranchDetail, IApiResponse } from "@/types";

export const Cetak: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { secretaryBranchId } = useParams();
  const printRef = useRef<HTMLDivElement>(null);

  const [showPrintPage, setShowPrintPage] = useState(false);

  const { data: secretaryBranch<PERSON><PERSON>, isLoading: isLoadingSecretaryBranch } =
    useQuery<IApiResponse<ISecretaryBranchDetail>>({
      url: `society/branch/secretary/${secretaryBranchId}`,
    });

  const secretaryBranchDetail = secretaryBranchRes?.data.data ?? null;

  const { fetch: downloadSlip, isLoading: isDownloadingSlip } = useMutation({
    url: `society/document/exportPdf`,
    onSuccess: (data) => {
      if (data?.data?.status === "SUCCESS") {
        console.log(data);
        downloadFile({
          data: data?.data?.data?.pdfBytes,
          name: data?.data?.data?.fileName,
        });
      }
    },
  });

  const handleCetakClick = () => setShowPrintPage(true);

  const onPrint = () =>
    downloadSlip({
      documentTemplateCode: "SLIP_PENGESAHAN_PEMBAHARUAN_SETIAUSAHA_CAWANGAN",
      societyId: secretaryBranchDetail?.societyId,
      newSecretaryBranchId: secretaryBranchDetail?.id,
    });

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  useEffect(() => {
    if (!isLoadingSecretaryBranch && !secretaryBranchDetail) navigate(-1);
  }, [isLoadingSecretaryBranch, secretaryBranchDetail]);

  if (isLoadingSecretaryBranch || !secretaryBranchDetail)
    return <CustomSkeleton />;

  return (
    <Box mt={4} sx={{ backgroundColor: "white", p: 3, borderRadius: "15px" }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("maklumatPertubuhanDanCawangan")}
        </Typography>

        <FormFieldRow
          label={<Label text={t("organizationName")} />}
          value={
            <DisabledTextField
              value={secretaryBranchDetail?.societyName ?? "-"}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("organizationNumber")} />}
          value={
            <DisabledTextField
              value={secretaryBranchDetail?.societyNo ?? "-"}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("namaCawangan")} />}
          value={
            <DisabledTextField
              value={secretaryBranchDetail?.branchName ?? "-"}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("cawanganNumber")} />}
          value={
            <DisabledTextField value={secretaryBranchDetail?.branchNo ?? "-"} />
          }
        />
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("maklumatSetiausahaBaru")} {t("cawangan")}
        </Typography>

        <FormFieldRow
          label={<Label text={t("name")} />}
          value={
            <DisabledTextField
              value={secretaryBranchDetail?.secretaryName ?? "-"}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("idNumber")} />}
          value={
            <DisabledTextField
              value={secretaryBranchDetail?.secretaryIdNo ?? "-"}
            />
          }
        />
      </Box>

      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          mt: 3,
          gap: 2,
          mb: 3,
        }}
      >
        <ButtonOutline onClick={handleCetakClick}>{t("cetak")}</ButtonOutline>
        <ButtonPrimary onClick={() => navigate("..")} sx={{ color: "white" }}>
          {t("completed")}
        </ButtonPrimary>
      </Box>

      {showPrintPage && (
        <>
          <PrintPage
            secretaryBranchDetail={secretaryBranchDetail}
            ref={printRef}
          />

          <Box textAlign="center" mt={4}>
            <Button
              disabled={isDownloadingSlip}
              variant="contained"
              color="error"
              onClick={onPrint}
            >
              {isDownloadingSlip ? <CircularProgress size={15} /> : "Cetak"}
            </Button>
          </Box>
        </>
      )}
    </Box>
  );
};

export default Cetak;
