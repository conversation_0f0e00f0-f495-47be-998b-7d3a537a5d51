import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengu<PERSON>an-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
interface FasalContentTujuhBelasCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
}

export const FasalContentTujuhBelasCawangan: React.FC<
  FasalContentTujuhBelasCawanganProps
> = ({ activeStep, setActiveStep, clause }) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [clauseContentDescription, setClauseContentDescription] = useState<string|undefined>("");
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const { id, clauseId } = useParams();
  const [clauseContentId, setClauseContentId] = useState("");
  // const [clauseContent, setClauseContent] = useState("");
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName } = responseData?.data?.data;
        setNamaPertubuhan(societyName);
      },
    },
  });
  // const clauseContent = `
  //             1. Pengerusi Cawangan dalam tempoh memegang jawatannya hendaklah
  //             mempengerusikan semua Mesyuarat Agung Cawangan dan semua Mesyuarat
  //             Jawatankuasa Cawangan serta bertanggungjawab atas kesempurnaan
  //             perjalanan semua mesyuarat. Pengerusi Cawangan mempunyai undi
  //             pemutus dan beliau hendaklah menandatangani minit mesyuarat
  //             tersebut.

  //             2. Naib Pengerusi Cawangan hendaklah memangku jawatan Pengerusi
  //             Cawangan semasa ketiadaannya.

  //             3. Setiausaha Cawangan hendaklah menjalankan kerja pentadbiran
  //             cawangan mengikut Perlembagaan dan Setiausaha Cawangan hendaklah
  //             menjalankan arahan-arahan Mesyuarat Agung Cawangan dan
  //             Jawatankuasa Cawangan. Setiausaha Cawangan bertanggungjawab
  //             mengendalikan urusan surat-menyurat dan menyimpan semua rekod
  //             serta dokumen Cawangan, kecuali buku-buku akaun dan dokumen
  //             kewangan. Setiausaha Cawangan hendaklah menyimpan buku daftar ahli
  //             yang mengandungi maklumat terperinci ahli mengikut kategori
  //             seperti nama, tempat dan tarikh lahir, nombor kad pengenalan,
  //             pekerjaan, nama dan alamat majikan dan alamat rumah kediaman
  //             tiap-tiap ahli. Setiausaha Cawangan hendaklah hadir dalam semua
  //             mesyuarat dan membuat catatan mesyuarat kecuali atas sebab-sebab
  //             munasabah.

  //             4. Penolong Setiausaha Cawangan hendaklah membantu Setiausaha
  //             Cawangan menjalankan kerja-kerjanya dan memangku jawatan itu
  //             semasa ketiadaan Setiausaha Cawangan.

  //             5. Bendahari Cawangan adalah hendaklah bertanggungjawab dalam
  //             semua hal ehwal kewangan Cawangan dan memastikan penyata kewangan
  //             Cawangan yang terdiri daripada penyata penerimaan dan perbelanjaan
  //             serta kunci kira-kira adalah tepat dan teratur.

  //             a. Penolong Bendahari hendaklah membantu Bendahari menjalankan
  //             kerja-kerjanya dan memangku jawatan itu semasa ketiadaan
  //             Bendahari.

  //             6. Ahli Jawatankuasa Biasa Cawangan hendaklah membantu
  //             Jawatankuasa Cawangan menjalankan tugas yang diarahkan kepadanya.
  //           `;

  const clause17 = localStorage.getItem("clause17");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  useEffect(() => {
    if (clause) { 
      if(clause.clauseDescription){
        setClauseContentDescription(clause.clauseDescription);
      }else{
        setClauseContentDescription(undefined);
      }
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause17Data = JSON.parse(clause17);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        // setClauseContent(clause.clauseContent);
        setClauseContentId(clause.clauseContentId);
      }

      //setNamaPertubuhan(clause17Data.societyName);
      // setIsEdit(clause.edit);
      if(clause.editContentText){
        setIsEdit(clause.editContentText);
       }
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");
  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
               <FasalDisplayContent clauseContent={clauseContentDescription ? clauseContentDescription : clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContentDescription ? clauseContentDescription : clauseContent,
                constitutionValues: [],
                clause: "clause17",
                clauseCount: 17,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentTujuhBelasCawangan;
