import { Box, FormControl, Grid, IconButton, Typography } from "@mui/material";
import { t } from "i18next";
import { useEffect, useState } from "react";
import { formatDate } from "../../../../helpers/utils";
import { API_URL } from "../../../../api";
import { useNavigate } from "react-router-dom";
import { useCustom } from "@refinedev/core";
import {
  ApplicationStatusEnum,
  MALAYSIA,
  SocietyStatusList,
  StatusCodeList,
  StatusPermohonan,
} from "../../../../helpers/enums";
import ButtonPrevious from "../../../../components/button/ButtonPrevious";
import { ButtonPrimary } from "../../../../components/button";
import { EyeIcon } from "../../../../components/icons";
import DataTable, { IColumn } from "@/components/datatable";
import Input from "@/components/input/Input";
import { useQuery } from "@/helpers";

interface FormValues {
  userId?: string | number | null;
  negeri?: string | null;
  statusPertubuhan?: string | null;
  jenisPemegangJawatan?: string | null;
  carian?: string | null;
}

function PertubuhanTab() {
  const navigate = useNavigate();

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [displaySenaraiAjk, setDisplaySenaraiAjk] = useState<any[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [page, setPage] = useState(0);

  useEffect;
  const [formData, setFormData] = useState({
    statusPermohonan: "",
    negeri: "",
    statusPertubuhan: "",
    carian: "",
  });

  const statusPermohonan = Object.entries(StatusPermohonan)
    .filter(([key, value]) => Number(key) !== 0)
    .map(([key, value]) => ({
      value: key,
      label: t(value),
    }));

  const { data: addressList, isLoading: isAddressLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const negeri = addressList?.data?.data || [];

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "30px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { refetch: fetchSociety, isLoading } = useQuery({
    url: `society/admin/society/findAllByParam`,
    filters: [
      { field: "pageNo", operator: "eq", value: page + 1 },
      { field: "pageSize", operator: "eq", value: rowsPerPage },
      { field: "searchQuery", operator: "eq", value: formData.carian },
      {
        field: "applicationStatusCode",
        operator: "eq",
        value: formData.statusPermohonan,
      },
      { field: "state", operator: "eq", value: formData.negeri },
    ],
    autoFetch: false,
    onSuccess: (data) => {
      const list = data?.data?.data;
      setTotal(list.total);
      setDisplaySenaraiAjk(list.data);
    },
  });

  const handleClearSearch = () => {
    setFormData({
      statusPermohonan: "",
      negeri: "",
      statusPertubuhan: "",
      carian: "",
    });
    fetchSociety({
      filters: [
        { field: "pageNo", operator: "eq", value: 1 },
        { field: "pageSize", operator: "eq", value: rowsPerPage },
        { field: "searchQuery", operator: "eq", value: null },
        {
          field: "applicationStatusCode",
          operator: "eq",
          value: null,
        },
        { field: "state", operator: "eq", value: null },
      ],
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newErrors: { [key in keyof FormValues]?: string } = {};

    let data = displaySenaraiAjk;

    // if every parameter is empty
    if (
      formData.carian == "" &&
      formData.statusPermohonan == "" &&
      formData.negeri == "" &&
      formData.statusPertubuhan == ""
    ) {
      // data = localData;
    } else {
      // if there is input filled
      setFormData(formData);
      fetchSociety();
    }
    setTotal(data.length);
    setDisplaySenaraiAjk(data);

    // FetchUsers(false);
  };

  const columns: IColumn[] = [
    {
      field: "societyName",
      headerName: t("organizationName"),
      align: "center",
    },
    {
      field: "societyNo",
      headerName: t("noPPM"),
      align: "center",
      renderCell: (params) => (
        <Box className="custom-cell" style={{ textAlign: "center" }}>
          {params.row.societyNo
            ? params.row.societyNo
            : params.row.applicationNo ?? "-"}
        </Box>
      ),
    },
    {
      field: "applicationStatusCode",
      headerName: t("statusPermohonan"),
      headerAlign: "center",
      renderCell: (params) => (
        <div className="custom-cell" style={{ textAlign: "center" }}>
          <div>
            {params.row.applicationStatusCode
              ? t(ApplicationStatusEnum[params.row.applicationStatusCode])
              : "-"}
          </div>
        </div>
      ),
    },
    {
      field: "statusCode",
      headerName: t("statusPertubuhan"),
      align: "center",
      renderCell: (params) => (
        <Box className="custom-cell" style={{ textAlign: "center" }}>
          {params.row.statusCode
            ? t(
                StatusCodeList.find(
                  (status) => status.value === params.row.statusCode
                )?.label ?? "-"
              )
            : "-"}
        </Box>
      ),
    },
    {
      field: "paymentDate",
      headerName: t("tarikhBayar"),
      align: "center",
      renderCell: (params) => (
        <Box className="custom-cell" style={{ textAlign: "center" }}>
          {params.row.paymentDate
            ? formatDate(params.row.paymentDate, "D-M-YYYY", {
                parseFormat: "HH:mm DD:MM:YYYY",
              })
            : "-"}
        </Box>
      ),
    },
    {
      field: "stateCode",
      headerName: t("negeri"),
      align: "center",
      renderCell: (params) => (
        <div className="custom-cell" style={{ textAlign: "center" }}>
          <div>
            {params.row.stateCode
              ? negeri.find(
                  (id: any) => id.id === parseInt(params.row.stateCode)
                )?.name
              : "-"}
          </div>
        </div>
      ),
      // renderCell: (params: any) => params?.row?.roBertanggungjawab ?? "-",
    },
    {
      field: "migrateState",
      headerName: t("statusMigrasi"),
      align: "center",
      renderCell: (params) => (
        <Box className="custom-cell" style={{ textAlign: "center" }}>
          {params.row.migrateState ?? "-"}
        </Box>
      ),
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      align: "right",
      renderCell: (params: any) => {
        const id = btoa(params.row.id);
        return (
          <>
            {/* <IconButton
              sx={{ color: "black" }}
              onClick={() => navigate("pertubuhan/kemaskini")}
            >
              <EditIcon />
            </IconButton> */}
            <IconButton
              sx={{ color: "black" }}
              onClick={() =>
                navigate(`pertubuhan/view/${id}`, {
                  state: {
                    societyId: params.row.id,
                  },
                })
              }
            >
              <EyeIcon />
            </IconButton>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    fetchSociety();
  }, [page, rowsPerPage]);

  return (
    <>
      <Box component="form" onSubmit={handleSubmit}>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("senaraiPertubuhan")}
            </Typography>
            {/* organization category */}
            <FormControl fullWidth error={!!formErrors.statusPermohonan}>
              <Input
                value={formData.statusPermohonan}
                size="small"
                label={t("statusPermohonan")}
                options={statusPermohonan}
                onChange={(e) => {
                  setFormData((prevState) => ({
                    ...prevState,
                    statusPermohonan: e.target.value,
                  }));
                }}
                type="select"
                error={!!formErrors.statusPermohonan}
                helperText={formErrors.statusPermohonan}
              />
            </FormControl>

            {/* status pertubuhan */}
            <FormControl fullWidth error={!!formErrors.statusPertubuhan}>
              <Input
                value={formData.statusPertubuhan}
                size="small"
                label={t("statusPertubuhan")}
                options={StatusCodeList.map((item: any) => ({
                  label: t(item.label),
                  value: item.value,
                }))}
                type="select"
                onChange={(e) => {
                  setFormData((prevState) => ({
                    ...prevState,
                    statusPertubuhan: e.target.value,
                  }));
                }}
                error={!!formErrors.statusPertubuhan}
                helperText={formErrors.statusPertubuhan}
              />
            </FormControl>

            {/* state */}
            <FormControl fullWidth error={!!formErrors.subOrganizationCategory}>
              <Input
                value={formData.negeri}
                size="small"
                label={t("negeri")}
                options={negeri
                  .filter((item: any) => item.pid === MALAYSIA)
                  .map((item: any) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                type="select"
                onChange={(e) => {
                  setFormData((prevState) => ({
                    ...prevState,
                    negeri: e.target.value,
                  }));
                }}
                error={!!formErrors.negeri}
                helperText={formErrors.negeri}
              />
            </FormControl>

            {/* finding/carian */}
            <Input
              value={formData.carian}
              size="small"
              label={t("Carian")}
              onChange={(e) => {
                setFormData((prevState) => ({
                  ...prevState,
                  carian: e.target.value,
                }));
              }}
              error={!!formErrors.carian}
              helperText={formErrors.carian}
            />
            <Grid container mt={1} spacing={2}>
              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  // flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrevious onClick={handleClearSearch}>
                  {t("previous")}
                </ButtonPrevious>
                <ButtonPrimary type="submit">{t("search")}</ButtonPrimary>
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/* ============= */}
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              textAlign: "center",
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              py: 2,
            }}
          >
            <Typography variant="h5" gutterBottom>
              {total}
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
              {t("rekodDijumpai")}
            </Typography>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mt: 2,
            }}
          >
            <DataTable
              columns={columns as any[]}
              rows={displaySenaraiAjk} // Correctly slice data
              page={page + 1} // Page is 1-indexed for pagination
              rowsPerPage={rowsPerPage}
              isLoading={isLoading}
              totalCount={total}
              onPageChange={(newPage: number) => setPage(newPage - 1)}
              onPageSizeChange={(newRowsPerPage: number) =>
                setRowsPerPage(newRowsPerPage)
              }
            />
          </Box>
        </Box>
      </Box>
      <style>
        {`
      .custom-header {
        white-space: normal;  /* Allow wrapping text */
        overflow: hidden;
        /* text-overflow: ellipsis; */
        line-height: 19.5px;
        font-weight: 500;
      }
      .custom-cell {
        white-space: normal;  /* Allow wrapping text */
        overflow: hidden;
        /* text-overflow: ellipsis; */
        line-height: 19.5px;
      }
    `}
      </style>
    </>
  );
}

export default PertubuhanTab;
