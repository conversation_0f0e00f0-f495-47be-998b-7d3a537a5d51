import {
  Box,
  Chip,
  FormControlLabel,
  FormHelperText,
  Grid,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  styled,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonPrimary } from "../../../../../../components/button";
import { useEffect, useState } from "react";
import ButtonPrevious from "../../../../../../components/button/ButtonPrevious";
import { DecisionOptions, ListGelaran } from "../../../../../../helpers/enums";
import Input from "../../../../../../components/input/Input";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../../../api";
import { useNavigate, useSearchParams } from "react-router-dom";
import { capitalizeWords, formatDate, useQuery } from "@/helpers";

interface FormValues {
  userId?: number | null;
  title?: string | null;
  name?: string | null;
  identificationNo?: string | null;
  jppmBranch?: string | null;
  position?: string | null;
  userRole?: number[];
  email?: string | null;
  password1?: string | null;
  password?: string | null;
  status?: boolean | null;
  ic?: string | null;
  statusLog?: string;
  statusUpdate?: any;
  decision?: string | null;
  note?: string | null;
}

interface ListItem {
  value: any;
  label: any;
  id?: any | null;
}

interface UserData {
  id: number | null;
  title: string | null;
  name: string | null;
  identificationNo: string | null;
  position: number | null;
  userRole: string[] | null;
  email: string | null;
  status: number | null;
  lastUpdateBy: number | null;
  lastUpdateDate: number[] | null;
  lastPasswordChange: number[] | null;
  jppmBranch: number | null;
  decision?: string | null;
  remarks?: string | null;
}

function ROApprovalPenggunaJPM() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const userId = searchParams.get("id");
  const [decisionList, setDecisionList] = useState<
    { value: number; label: string }[]
  >([]);

  useEffect(() => {
    const newList = DecisionOptions.map((item) => ({
      value: item.value,
      label: t(item.label),
    }));
    //@ts-ignore
    setDecisionList(newList);
  }, [t]);
  const [userData, setUserData] = useState<UserData>();
  const [loading, setLoading] = useState(true);

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const [filteredBranchList, setFilteredBranchList] = useState<ListItem[]>([]);
  const [filteredPositionList, setFilteredPositionList] = useState<ListItem[]>(
    []
  );
  const [filteredUserRoleList, setFilteredUserRoleList] = useState<ListItem[]>(
    []
  );

  const [formValues, setFormValues] = useState<FormValues>({
    title: null,
    name: null,
    identificationNo: null,
    jppmBranch: null,
    position: null,
    userRole: [],
    email: null,
    password1: null,
    password: null,
    status: null,
    ic: null,
    statusLog: "-",
    decision: null,
    note: null,
  });
  const [errors, setErrors] = useState<{ [key in keyof FormValues]?: string }>(
    {}
  );

  const { data: listBranch, isLoading: isLoadingBranch } = useCustom({
    url: `${API_URL}/society/admin/branch/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const { data: listPosition, isLoading: isLoadingPosition } = useCustom({
    url: `${API_URL}/society/admin/jppmPostion/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const { data: listUserRoles, isLoading: isLoadingUserRoles } = useCustom({
    url: `${API_URL}/user/userRole`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const { mutate: editROApproval, isLoading: isLoadingEdit } =
    useCustomMutation();

  useEffect(() => {
    if (listBranch?.data?.data) {
      const transformedList = listBranch.data.data.map((item: any) => ({
        value: item.id,
        label: item.description,
      }));
      setFilteredBranchList(transformedList);
    }
  }, [listBranch]);

  useEffect(() => {
    if (listPosition?.data?.data) {
      const transformedList = listPosition.data.data.map((item: any) => ({
        value: item.id,
        label: `${item.name} ${item.grade}`,
      }));
      setFilteredPositionList(transformedList);
    }
  }, [listPosition]);

  useEffect(() => {
    if (listUserRoles?.data?.data) {
      const transformedList = listUserRoles?.data?.data?.data?.map(
        (item: any) => ({
          value: item.role,
          label: item.role,
          id: item.id,
        })
      );
      setFilteredUserRoleList(transformedList);
    }
  }, [listUserRoles]);

  const CustomRadio = styled(Radio)(({ theme }) => ({
    "& .MuiSvgIcon-root": {
      borderRadius: "2px",
      width: 16,
      height: 16,
    },
    // Styles for unchecked state
    "& .MuiSvgIcon-root:first-of-type": {
      border: "2px solid #979797",
      background: "white",
    },
    // Styles for checked state
    "& .MuiSvgIcon-root:last-child": {
      border: "2px solid #1976d2",
      background: "#1976d2",
      "&:before": {
        display: "block",
        width: 16,
        height: 16,
        backgroundImage: "radial-gradient(#fff,#fff 28%,transparent 32%)",
        content: '""',
      },
    },
  }));

  const CheckboxIcon = () => (
    <svg width="16" height="16" viewBox="0 0 16 16">
      <rect
        width="16"
        height="16"
        fill="none"
        stroke="#979797"
        strokeWidth="1"
        rx="3"
        ry="3"
      />

      <rect x="2" y="2" width="12" height="12" fill="#979797" />
    </svg>
  );

  const CheckedIcon = () => (
    <svg width="16" height="16" viewBox="0 0 16 16">
      <rect
        width="16"
        height="16"
        fill="none"
        stroke="#979797"
        strokeWidth="1"
        rx="3"
        ry="3"
      />
      <rect x="2" y="2" width="12" height="12" fill="currentColor" />
    </svg>
  );

  function FetchROApproval() {
    fetch(`${API_URL}/user/roApproval/getApproval`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        portal: localStorage.getItem("portal") || "",
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      body: JSON.stringify({
        userId: Number(userId),
      }),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Erorr when fetching ROApproval");
        }
        return response.json();
      })
      .then((data) => {
        console.log("Success:", data);
      })
      .catch((error) => {
        console.error("Error:", error);
      });
  }

  const {
    data: userUpdaterResponse,
    refetch: getUserUpdater,
    isLoading: isLoadingUserUpdater,
  } = useQuery<{ body: any[] }>({
    url: `society/user/getUserByCriteria`,
    autoFetch: false,
  });
  const userUpdater = userUpdaterResponse?.data?.body?.[0] ?? null;
  useEffect(() => {
    if (userId) {
      const fetchData = async () => {
        try {
          const response = await fetch(`${API_URL}/user/admin/${userId}`, {
            method: "GET",
            headers: {
              portal: localStorage.getItem("portal") || "",
              Authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
              "Content-Type": "application/json",
            },
          });
          if (!response.ok) {
            throw new Error("Failed to fetch data");
          }
          const result = await response.json();
          const data = result?.data;
          getUserUpdater({
            filters: [
              {
                field: "identificationNo",
                operator: "eq",
                value: result?.data?.identificationNo,
              },
            ],
          });
          setFormValues({
            title: result?.data.title,
            name: result?.data.name,
            identificationNo: result?.data.identificationNo,
            jppmBranch: result?.data.jppmBranch,
            position: result?.data.position,
            userRole:
              result?.data?.userRole?.length > 0 ? result.data.userRole : [],
            email: result?.data.email,
            status: result?.data.status,
            statusUpdate:
              result?.data.statusUpdate === 0
                ? result?.data.status === 2
                  ? 1
                  : result?.data.status
                : result?.data.statusUpdate,
            statusLog: "-",
          });
          setUserData(data);
        } catch (error) {
          console.error("Error fetching data:", error);
        } finally {
          setLoading(false);
        }
      };
      fetchData();
      FetchROApproval();
    }
  }, [userId]);

  const EditROApproval: () => void = () => {
    editROApproval(
      {
        url: `${API_URL}/user/roApproval`,
        method: "put",
        values: {
          note: formValues.note,
          decision: formValues.decision,
          userId: Number(userId),
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          setErrors({});
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onSuccess() {
          navigate(-1);
        },
        onError(error) {
          console.log(error);
        },
      }
    );
  };

  const handleChange = (e: any) => {
    const { name, value } = e.target;

    setFormValues({
      ...formValues,
      [name!]: value as string,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newErrors: { [key in keyof FormValues]?: string } = {};

    if (userId) {
      formValues.userId = Number(userId);
      if (!formValues.position) {
        newErrors.position = t("requiredField");
      }
      if (!formValues.decision) {
        newErrors.decision = t("requiredField");
      }
    }

    if (Object.keys(newErrors).length === 0) {
      if (userId) {
        EditROApproval();
      }
    } else {
      setErrors(newErrors);
    }
  };

  if (userData) {
    return (
      <Box
        component="form"
        onSubmit={handleSubmit}
        sx={{ display: "grid", gap: 2 }}
      >
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            backgroundColor: "white",
            borderRadius: "14px",
          }}
        >
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
              mb: 2,
            }}
          >
            <Box sx={{ mb: 6 }}>
              <Typography sx={{ mb: 4 }} className={"title"}>
                {t("maklumatPenggunaJPPM")}
              </Typography>

              <Input
                value={formValues.title ? formValues.title : ""}
                name="title"
                onChange={handleChange}
                disabled
                label={t("gelaran")}
                options={ListGelaran}
                type="select"
                error={!!errors.title}
                helperText={errors.title}
              />

              <Input
                value={formValues.name ? formValues.name : ""}
                name="name"
                onChange={handleChange}
                disabled
                label={t("fullName")}
                error={!!errors.name}
                helperText={errors.name}
              />

              <Input
                value={
                  formValues.identificationNo ? formValues.identificationNo : ""
                }
                name="identificationNo"
                onChange={handleChange}
                disabled
                inputProps={{
                  minLength: 12,
                  maxLength: 12,
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                }}
                type="text"
                label={t("idNumberPlaceholder")}
                error={!!errors.identificationNo}
                helperText={errors.identificationNo}
              />

              <Input
                value={formValues.jppmBranch ? formValues.jppmBranch : ""}
                name="jppmBranch"
                onChange={handleChange}
                disabled
                label={t("cawanganJPPM")}
                isLoadingData={isLoadingBranch}
                options={filteredBranchList}
                type="select"
                error={!!errors.jppmBranch}
                helperText={errors.jppmBranch}
              />

              <Input
                value={formValues.position ? formValues.position : ""}
                name="position"
                onChange={handleChange}
                disabled
                isLoadingData={isLoadingPosition}
                options={filteredPositionList}
                label={capitalizeWords(t("actualPosition"))}
                type="select"
                error={!!errors.position}
                helperText={errors.position}
              />

              <Grid container spacing={2} sx={{ pb: 1.5 }}>
                <Grid
                  item
                  xs={12}
                  sm={4}
                  sx={{ display: "flex", alignItems: "center" }}
                >
                  <Typography
                    sx={{
                      color: "#666666",
                      fontWeight: "400 !important",
                      fontSize: "14px",
                    }}
                  >
                    {t("peranan")}
                  </Typography>
                </Grid>
                {filteredUserRoleList && (
                  <Grid item xs={12} sm={8}>
                    <Select
                      disabled
                      multiple
                      value={formValues.userRole}
                      onChange={handleChange}
                      fullWidth
                      name="userRole"
                      sx={{
                        height: "40px",
                        borderRadius: "5px",
                        fontSize: "14px",
                        "& .MuiSelect-select": {
                          color: "black",
                          fontSize: "14px",
                        },
                        pt: 1,
                        pb: 1,
                      }}
                      renderValue={(selected) => (
                        <div
                          style={{ display: "flex", gap: 3, flexWrap: "wrap" }}
                        >
                          {selected.map((value) => {
                            const matchingItem = filteredUserRoleList.find(
                              (item) => item.value === value
                            );
                            return (
                              <div
                                key={value}
                                onClick={(e) => e.stopPropagation()}
                              >
                                <Chip
                                  sx={{
                                    border: "1px solid #147C7C",
                                    bgcolor: "#147C7C80",
                                    color: "white",
                                  }}
                                  label={
                                    matchingItem ? matchingItem.value : value
                                  }
                                />
                              </div>
                            );
                          })}
                        </div>
                      )}
                    >
                      <MenuItem key="" disabled value="">
                        <em>{t("selectPlaceholder")}</em>
                      </MenuItem>
                      {filteredUserRoleList.map((item: any) => (
                        <MenuItem key={item.value} value={item.value}>
                          {item.label}
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.userRole && (
                      <FormHelperText error>{errors.userRole}</FormHelperText>
                    )}
                  </Grid>
                )}
              </Grid>

              <Input
                value={formValues.email ? formValues.email : ""}
                name="email"
                onChange={handleChange}
                disabled
                label={t("email")}
                type="email"
                error={!!errors.email}
                helperText={errors.email}
              />

              <Grid container spacing={2} sx={{ pt: 2, pb: 2 }}>
                <Grid
                  item
                  xs={12}
                  sm={4}
                  sx={{ display: "flex", alignItems: "center" }}
                >
                  <Typography
                    variant="body1"
                    sx={{
                      color: "#666666",
                      fontWeight: "400 !important",
                      fontSize: "14px",
                    }}
                  >
                    {t("status")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Input
                    type="radio"
                    isLabel={false}
                    isLabelNoSpace={false}
                    name="type"
                    disabled
                    value={formValues.statusUpdate}
                    options={[
                      { value: 1, label: t("userStatusActive") },
                      { value: 3, label: t("userStatusInactive") },
                      { value: 4, label: t("userStatusDeactivated") },
                    ]}
                  />
                </Grid>
              </Grid>
              <Input
                value={
                  formatDate(userUpdater?.lastLogin, "DD-MM-YYYY HH:mm A") ?? ""
                }
                name="statusLog"
                onChange={handleChange}
                disabled
                required
                label={t("statusLogMasuk")}
                error={!!errors.statusLog}
                helperText={errors.statusLog}
              />
            </Box>
            {/* SECTION 2 */}

            {/* SECTION 3 */}
            <Box sx={{ mb: 6 }}>
              <Input
                value={userUpdater?.createdByName ?? "-"}
                name="createBy"
                label={t("recordedBy")}
                disabled
              />
              <Input
                value={
                  formatDate(userUpdater?.modifiedDate, "DD-MM-YYYY HH:mm A") ??
                  ""
                }
                name="lastUpdate"
                label={t("lastUpdate")}
                disabled
              />
              <Input
                value={
                  formatDate(
                    userUpdater?.passwordChange,
                    "DD-MM-YYYY  HH:mm A"
                  ) ?? ""
                }
                name="lastUpdateChangePassword"
                label={t("lastUpdateChangePassword")}
                disabled
              />
            </Box>
          </Box>
        </Box>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            backgroundColor: "white",
            borderRadius: "14px",
          }}
        >
          {decisionList ? (
            <Box
              component="form"
              onSubmit={handleSubmit}
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
                mb: 2,
              }}
            >
              <Input
                value={formValues.decision ? formValues.decision : ""}
                name="decision"
                onChange={handleChange}
                required
                label={t("decision")}
                options={decisionList}
                type="select"
                error={!!errors.decision}
                helperText={errors.decision}
              />

              <Input
                value={formValues.note ? formValues.note : ""}
                name="note"
                // required
                onChange={handleChange}
                label={t("remarks")}
                error={!!errors.note}
                helperText={errors.note}
              />
            </Box>
          ) : null}
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonPrevious
              variant="outlined"
              sx={{
                bgcolor: "white",
                "&:hover": { bgcolor: "white" },
                width: isMobile ? "100%" : "auto",
              }}
              onClick={() => navigate(-1)}
            >
              {t("previous")}
            </ButtonPrevious>
            <ButtonPrimary
              variant="contained"
              type="submit"
              disabled={errors.identificationNo ? true : false}
              sx={{
                width: isMobile ? "100%" : "auto",
                boxShadow: "none",
              }}
            >
              {t("hantar")}
            </ButtonPrimary>
          </Grid>
        </Box>
      </Box>
    );
  }
}

export default ROApprovalPenggunaJPM;
