import React, { useEffect, useState } from "react";
import {
  Stepper,
  Step,
  StepLabel,
  useMediaQuery,
  Theme,
  StepConnector,
  Box,
  Typography,
  useTheme,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import CheckIcon from "@mui/icons-material/Check";
import { API_URL } from "@/api";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { ApplicationStatus, ROApprovalType } from "@/helpers";

const steps = [
  "generalInformation",
  "minitMesyuarat",
  "ajkList",
  "supportingDocuments",
];

interface CawanganStepperProps {
  activeStep: number | null;
  kuiri: string | null;
}

interface StepperItem {
  label: string;
  path: string;
}

export const CawanganStepper: React.FC<CawanganStepperProps> = ({
  activeStep,
  kuiri,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const params = new URLSearchParams(window.location.search);
  const paramId = params.get("id");

  const [branchId, setBranchId] = useState(paramId);

  const [branchData, setBranchData] = useState<any>();
  const { data: branchDataById, isLoading: isLoadingBranch } = useCustom<any>({
    url: `${API_URL}/society/branch/getById/${branchId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: branchId !== null && branchId !== null,
      retry: false,
      cacheTime: 0,
      onSuccess: (responseData) => {
        if (responseData?.data?.data) {
          setBranchData(responseData?.data?.data);
        } else {
          setBranchData(null);
        }
      },
    },
  });

  const { mutate: queryData } = useCustomMutation();
  const [mianKuiri, setMainKuiri] = useState<string | null>(null);
  useEffect(() => {
    if (
      branchData?.applicationStatusCode == ApplicationStatus.KUIRI &&
      kuiri === null
    ) {
      queryData(
        {
          method: "post",
          url: `${API_URL}/society/roQuery/getQuery`,
          values: {
            roApprovalType: ROApprovalType.BRANCH_REGISTRATION.code,
            branchId: branchId,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
        },
        {
          onSuccess(data) {
            if (data?.data?.data[0]?.note) {
              setMainKuiri(data?.data?.data[0]?.note);
            }
          },
        }
      );
    } else {
      setMainKuiri(kuiri);
    }
  }, [branchData?.applicationStatusCode]);

  const handleStepClick = (step: number) => {
    const encodedId = paramId;

    switch (step) {
      case 0:
        navigate(`../senarai/cawangan/maklumat-am?id=${encodedId}`);
        break;
      case 1:
        navigate(`../senarai/cawangan/minute-mesyuarat?id=${encodedId}`);
        break;
      case 2:
        navigate(`../senarai/cawangan/ahlijawatankuasa?id=${encodedId}`);
        break;
      case 3:
        navigate(`../senarai/cawangan/dokumen-sokongan?id=${encodedId}`);
        break;
    }
  };

  const stepperItems: StepperItem[] = [
    {
      label: t("generalInformation"),
      path: `../maklumat-am?id=${paramId}`,
    },
    {
      label: t("minitMesyuarat"),
      path: `../minute-mesyuarat?id=${paramId}`,
    },
    {
      label: t("ajkList"),
      path: `../ahlijawatankuasa?id=${paramId}`,
    },
    {
      label: t("supportingDocuments"),
      path: `../dokumen-sokongan?id=${paramId}`,
    },
  ];

  const [open, setOpen] = useState(true);
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const location = useLocation();

  useEffect(() => {
    if (isSmallScreen) {
      setOpen(false);
    } else {
      setOpen(true);
    }
  }, [isSmallScreen]);

  const getActiveStep = () => {
    const currentPath = location.pathname;
    if (currentPath.includes("/maklumat-am")) {
      return 0; // First step active
    } else if (currentPath.includes("/minute-mesyuarat")) {
      return 1; // Both steps active (returns index of last active step)
    } else if (
      currentPath.includes("/ahlijawatankuasa") ||
      currentPath.includes("/create-ajk") ||
      currentPath.includes("/create-ajk-bukan-wn") ||
      currentPath.includes("/view-ajk-bukan-wn")
    ) {
      return 2;
    } else if (currentPath.includes("/dokumen-sokongan")) {
      return 3;
    }
    return -1; // No steps active
  };

  const showStepper = !location.pathname.includes("/list-data");
  const showCard =
    !location.pathname.includes("/maklumat-am") &&
    !location.pathname.includes("/minute-mesyuarat") &&
    !location.pathname.includes("/ahlijawatankuasa") &&
    !location.pathname.includes("/dokumen-sokongan");

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      <Box
        sx={{
          display: "grid",
        }}
      >
        {showStepper && (
          <Box
            sx={{
              padding: 3,
              backgroundColor: "white",
              borderRadius: "15px",
            }}
          >
            <Typography
              sx={{
                mb: 3,
                fontSize: "16px",
                color: "var(--primary-color)",
                fontWeight: "500 !important",
              }}
            >
              {t("kemaskiniCawangan")}
            </Typography>
            <Stepper
              activeStep={getActiveStep()}
              orientation="vertical"
              connector={null}
            >
              {stepperItems.map((step, index) => (
                <Step key={step.label} onClick={() => handleStepClick(index)}>
                  <StepLabel
                    icon={
                      <Box
                        sx={{
                          width: 20,
                          height: 20,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          borderRadius: "4px",
                          border: `2px solid ${
                            index <= getActiveStep()
                              ? "var(--primary-color)"
                              : "#DADADA"
                          }`,
                          backgroundColor:
                            index < getActiveStep()
                              ? "var(--primary-color)"
                              : "transparent",
                        }}
                      />
                    }
                  >
                    <Typography
                      sx={{
                        cursor: "pointer",
                        fontWeight: "500 !important",
                        color:
                          index <= getActiveStep()
                            ? "var(--primary-color)"
                            : "#DADADA",
                      }}
                    >
                      {step.label}
                    </Typography>
                  </StepLabel>
                </Step>
              ))}
            </Stepper>
          </Box>
        )}
      </Box>
      {branchData?.applicationStatusCode == ApplicationStatus.KUIRI &&
        mianKuiri && (
          <Box
            sx={{
              display: "grid",
              gap: 3,
              padding: 3,
              backgroundColor: "white",
              borderRadius: "15px",
            }}
          >
            <Typography className="title">{t("kuiri")}</Typography>
            <Box
              sx={{
                padding: 1.5,
                backgroundColor: "var(--border-grey)",
                borderRadius: "12px",
              }}
            >
              <Typography className="label-dashboard">{mianKuiri}</Typography>
            </Box>
          </Box>
        )}
    </Box>
  );
};
