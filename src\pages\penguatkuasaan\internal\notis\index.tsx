import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { CustomTabsPanel } from "@/components";
import NotisTab from "./NotisTab";
import TelahDihantarTab from "./TelahDihantarTab";

const Notis: React.FC = () => {
  const { i18n } = useTranslation();
  const location = useLocation();
  const isMyLanguage = i18n.language === "my";

  const [initialTab, setInitialTab] = useState(0);

  useEffect(() => {
    const tabMapping = {
      notis: 0,
      "telah-dihantar": 1,
    };

    if (location.state?.tab && location.state.tab in tabMapping) {
      setInitialTab(tabMapping[location.state.tab as keyof typeof tabMapping]);
    }
  }, [location.state]);

  const tabItems = [
    {
      label: isMyLanguage ? "Notis" : "Notis",
      content: <NotisTab />,
    },
    {
      label: isMyLanguage ? "Telah Dihantar" : "Telah Dihantar",
      content: <TelahDihantarTab />,
    },
  ];

  return <CustomTabsPanel tabs={tabItems} initialTab={initialTab} />;
};

export default Notis;
