import { useState, useEffect } from "react";
import { Typo<PERSON>, Box, Grid, CircularProgress } from "@mui/material";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { t } from "i18next";
import Input from "@/components/input/Input";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Controller,
  FieldValues,
  SubmitHandler,
  useForm,
} from "react-hook-form";
import { filterEmptyValuesOnObject } from "@/helpers/utils";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import useQuery from "@/helpers/hooks/useQuery";
import { BankType } from "../interface";
import { useSelector } from "react-redux";
import {
  FormFieldRow,
  Label,
  SelectFieldController,
  TextFieldController,
} from "@/components";

const CreateAccountBank = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [bankList, setBankList] = useState<BankType[]>([]);
  const statementId = location.state?.statementId;
  const societyId = location.state?.societyId;
  const bankId = location.state?.bankId;
  const branchId = location.state?.branchId;
  const { refetch: getBank } = useQuery({
    url: `society/statement/bankInfo/get`,
    filters: [
      { field: "id", operator: "eq", value: bankId },
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "branchId", operator: "eq", value: branchId },
    ],
    autoFetch: false,
    onSuccess: (data) => {
      const bankData = data?.data?.data?.[0] || [];
      setValue("accountNo", bankData?.accountNo);
      setValue("bankName", bankData?.bankName);
    },
  });

  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const { isLoading: getBankListIsLoading } = useQuery({
    url: `payment/getBankList`,
    onSuccess: (data) => {
      const bankList = data?.data?.data?.bankTypeList || [];
      setBankList(bankList);
    },
  });

  // Trigger the API call when bankId changes or when it's available
  useEffect(() => {
    if (bankId) {
      getBank(); // Call getBank() when bankId is not null
    }
  }, []); // Dependency on bankId to trigger the effect when it changes

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const defaultFormValues = {
    bankName: "",
    accountNo: "",
  };

  const {
    control,
    setValue,
    watch,
    getValues,
    handleSubmit,
    reset: resetForm,
  } = useForm<FieldValues>({
    defaultValues: defaultFormValues,
  });

  const { mutate: saveBank, isLoading: isLoadingSaveBank } =
    useCustomMutation();

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    const payload = filterEmptyValuesOnObject(data);

    saveBank(
      {
        url: bankId
          ? `${API_URL}/society/statement/bankInfo/update`
          : `${API_URL}/society/statement/bankInfo/create`,
        method: bankId ? "put" : "post",
        values: {
          ...payload,
          id: bankId,
          statementId: statementId,
          societyId: societyId,
          branchId: branchId,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
      },
      {
        onSuccess: () => {
          navigate(-1);
        },
      }
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("organizationBankAccountInfo")}
          </Typography>
          {getBankListIsLoading ? (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                minHeight: "100px",
              }}
            >
              <CircularProgress />
            </Box>
          ) : (
            <>
              <Grid>
                <FormFieldRow
                  label={<Label text={t("pilihanBank")} required />}
                  value={
                    <SelectFieldController
                      control={control}
                      name="bankName"
                      options={bankList.map((bank) => ({
                        value: bank.bankName,
                        label: bank.bankName,
                      }))}
                      required
                    />
                  }
                />
                <FormFieldRow
                  label={<Label text={t("accountNumber")} required />}
                  value={
                    <TextFieldController
                      name="accountNo"
                      control={control}
                      type="number"
                      required
                    />
                  }
                />
              </Grid>
            </>
          )}
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 2,
            mt: 2,
          }}
        >
          <ButtonOutline variant="outlined" onClick={() => navigate(-1)}>
            {t("back")}
          </ButtonOutline>
          <ButtonPrimary type="submit" disabled={isLoadingSaveBank}>
            {t("save")}
          </ButtonPrimary>
        </Box>
      </Box>
    </form>
  );
};

export default CreateAccountBank;
