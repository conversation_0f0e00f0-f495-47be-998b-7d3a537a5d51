import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useForm, FieldValues } from "react-hook-form";
import { CrudFilter, useGetIdentity } from "@refinedev/core";
import {
  globalStyles,
  useMutation,
  useQuery,
  DocumentCategoryOptions,
  filterEmptyValuesOnObject,
  omitKeysFromObject,
  useUploadPresignedUrl,
  DocumentUploadType,
  useGetDocument,
} from "@/helpers";

import {
  FormFieldRow,
  Label,
  TextFieldController,
  ButtonPrimary,
  CustomSkeleton,
  SelectFieldController,
  DialogConfirmation,
  DatePickerController,
  ButtonOutline,
  ToggleButtonController,
} from "@/components";
import Input from "@/components/input/Input";
import { Box, Typography, CircularProgress, IconButton } from "@mui/material";

import { PreviewIcon, ShareOutlinedIcon } from "@/components/icons";

import { IApiResponse, IDocumentGuidance, IUser } from "@/types";

const defaultText = `
ADALAH DENGAN INI DIPERINTAHKAN pertubuhan yang namanya tersebut di atas menghantarkan kepada Pendaftar Pertubuhan Malaysia dalam tempoh 30 HARI dari tarikh PERINTAH ini maklumat-maklumat berikut yang disahkan oleh dua (2) orang pemegang jawatan utama pertubuhan tersebut:<br/><br/>

(1) Alamat berdaftar/ tempat urusan: <strong style="color:red">&lt;&lt; ALAMAT PERTUBUHAN (1) &gt;&gt;</strong><br/>
(2) Jumlah anggota-anggota pada: <strong style="color:red">&lt;&lt; JUMLAH AHLI JAWATANKUASA (2) &gt;&gt;</strong><br/>
(3) Empat (4) salinan yang benar dan lengkap bagi tiap-tiap pindaan kepada undang undang berdaftar atau aturan-aturan pertubuhan ini yang dibuat dalam tahun <strong style="color:red">&lt;&lt; TAHUN PERLEMBAGAAN (3) &gt;&gt;</strong><br/>
(4) Tiga (3) salinan senarai yang benar, lengkap dan menunjukan jawatan, nama, nombor kad pengenalan, tarikh lahir, tempat lahir, pekerjaan, nama dan alamat majikan serta alamat tempat tinggal bagi semua pemegang jawatan dan penasihat pertubuhan ini bagi tahun <strong style="color:red">&lt;&lt; TAHUN SENARAI AHLI JAWATANKUASA (4) &gt;&gt;</strong><br/>
(5) Dua (2) salinan yang benar minit mesyuarat agung tahunan (termasuk notis panggilan mesyuarat agung, agenda dan senarai kehadiran bertandatangan) yang diadakan oleh pertubuhan ini di dalam tahun <strong style="color:red">&lt;&lt; TAHUN MINIT MESYUARAT AGUNG (5) &gt;&gt;</strong><br/>
(6) Dua (2) salinan yang benar minit mesyuarat jawatankuasa (termasuk notis panggilan mesyuarat agung, agenda dan senarai kehadiran bertandatangan) yang diadakan oleh pertubuhan ini di dalam tahun <strong style="color:red">&lt;&lt; TAHUN MINIT MESYUARAT JAWATANKUASA (6) &gt;&gt;</strong><br/>
(7) Dua (2) salinan penyata penerimaan dan pembayaran serta kunci kira-kira bagi tahun tutup kira-kira pada <strong style="color:red">&lt;&lt; JULAT TARIKH PENYATA KEWANGAN (7) &gt;&gt;</strong> yang telah sempurna diaudit oleh seorang juruaudit yang telah diluluskan oleh Pendaftar Pertubuhan terlebih dahulu;<br/>
(8) Dua (2) salinan keterangan yang menunjukkan butir-butir berkenaan geran atau surat hak milik segala harta yang dipunyai oleh pertubuhan pada tarikh perintah ini;<br/>
(9) Dua (2) salinan senarai nama dan alamat mana-mana pertubuhan, persatuan, kesatuan sekerja atau mana-mana kumpulan lain yang diperbadankan atau tak diperbadankan di luar persekutuan yang bergabung dengan pertubuhan ini pada tarikh perintah ini;<br/>
(10) Dua (2) salinan senarai perihal kewangan apa-apa wang atau harta apa-apa faedah atau keuntungan yang diterima oleh pertubuhan ini dari mana-mana orang yang lazimnya bermastautin di luar Malaysia atau suatu organisasi, pihak berkuasa, kerajaan, atau agensi mana-mana kerajaan, di luar Malaysia bagi tahun tutup kira-kira pada <strong style="color:red">&lt;&lt; JULAT TARIKH PENYATA ASET, WANG DAN KEUNTUNGAN (10) &gt;&gt;</strong><br/><br/>

PERINTAH ini adalah dibuat di bawah peruntukan Seksyen 14(2) Akta Pertubuhan, 1966.<br/><br/>

Diperbuat pada haribulan [<strong style="color:red">&lt;&lt; TARIKH PEMERIKSAAN &gt;&gt;</strong>]<br/><br/>

(<strong style="color:red">&lt;&lt; PEGAWAI BERTANGGUNGJAWAB &gt;&gt;</strong>)<br/>
b.p PENDAFTAR PERTUBUHAN MALAYSIA
`;

const DaftarPanduanForm: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const classes = globalStyles();
  const { t, i18n } = useTranslation();
  const { data: user } = useGetIdentity<IUser>();

  const isMyLanguage = i18n.language === "my";

  const [dialogOpen, setDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const { control, handleSubmit, getValues, setValue } = useForm<FieldValues>({
    defaultValues: {
      id: "",
      test: "",
      text: defaultText,
    },
  });

  const { isLoading: isLoadingDocumentDetail } = useQuery<
    IApiResponse<IDocumentGuidance>
  >({
    url: `document/external/${id}`,
    autoFetch: !!id,
    onSuccess: (res) => {
      const documentDetail = res?.data?.data;

      if (documentDetail) {
        setValue("id", id);
        setValue("name", documentDetail.name);
        setValue("referenceNumber", documentDetail.referenceNumber);
        setValue("category", documentDetail.category);
        setValue("effectiveDate", documentDetail.effectiveDate);
        setValue("yearOfRelease", documentDetail.yearOfRelease);
        setValue("summary", documentDetail.summary);
        setValue("uploadDate", documentDetail.uploadDate);
        setValue("uploadedBy", documentDetail.uploadedBy);
        setValue("activationStatus", documentDetail.activationStatus);
        setValue("visibilityStatus", documentDetail.visibilityStatus);

        const baseFilters: CrudFilter[] = [
          {
            field: "icNo",
            operator: "eq",
            value: user?.identificationNo,
          },
          {
            field: "externalDocumentId",
            operator: "eq",
            value: id,
          },
          {
            field: "type",
            operator: "eq",
            value: DocumentUploadType.EXTERNAL_DOCUMENT,
          },
        ];
      }
    },
  });

  const handleSaveDocument = () => {
    const keysToSkip = ["urlPdf", "urlEbook"];
    const formValues = getValues();
    const filterPayload = filterEmptyValuesOnObject(formValues);
    const payload = omitKeysFromObject(filterPayload, keysToSkip);
  };

  const onSubmit = () => setDialogOpen(true);

  if (isLoadingDocumentDetail) return <CustomSkeleton height={50} number={4} />;

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box className={classes.section} mb={2}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={1}>
            {isMyLanguage ? "Butiran Pertubuhan" : "Butiran Pertubuhan"}
          </Typography>

          <FormFieldRow
            label={<Label text="No Pertubuhan" />}
            value={
              <SelectFieldController
                control={control}
                options={[]}
                name="test"
                required
              />
            }
          />

          <FormFieldRow
            label={<Label text="Nama Pertubuhan" />}
            value={
              <TextFieldController control={control} name="test" disabled />
            }
          />
        </Box>
      </Box>
      <Box className={classes.section} mb={2}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={1}>
            {isMyLanguage ? "Butiran Notis" : "Butiran Notis"}
          </Typography>

          <FormFieldRow
            label={<Label text="Jenis Notis" />}
            value={
              <SelectFieldController
                control={control}
                options={[]}
                name="test"
                required
              />
            }
          />

          <FormFieldRow
            label={<Label text="Tarikh dan Masa Terbit" />}
            value={
              <TextFieldController control={control} name="test" disabled />
            }
          />

          <FormFieldRow
            label={<Label text="Status Penghatar Pos" />}
            value={
              <SelectFieldController
                control={control}
                options={[]}
                name="test"
              />
            }
          />

          <FormFieldRow
            align="flex-start"
            label={<Label text="Status Penerimaan Maklumbalas" />}
            value={
              <ToggleButtonController
                name="reason"
                control={control}
                options={[
                  {
                    value: 1,
                    label: "Ya",
                  },
                  {
                    value: 2,
                    label: "Tidak",
                  },
                ]}
                direction="row"
                toggleGap={4}
                sx={{
                  gap: 1,
                }}
              />
            }
          />

          <FormFieldRow
            label={<Label text="Tarikh dan Masa Terima" />}
            value={<DatePickerController control={control} name="test" />}
          />

          <FormFieldRow
            align="flex-start"
            label={<Label text="Catatan" />}
            value={
              <TextFieldController
                control={control}
                name="test"
                multiline
                rows={4}
              />
            }
          />
        </Box>
      </Box>

      <Box className={classes.section} mb={2}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 1,
            marginBottom: 1,
          }}
        >
          <ButtonPrimary
            sx={{
              backgroundColor: "#FFF",
              width: "133px",
              minWidth: "unset",
              height: "32px",
              color: "var(--primary-color)",
              textTransform: "none",
              fontWeight: 400,
              border: "1px solid var(--primary-color)",
            }}
          >
            Pratonton
          </ButtonPrimary>

          <IconButton
            aria-label="close"
            sx={{
              padding: "9px 10px",
              borderRadius: "5px",
              backgroundColor: "#9747FF80",
            }}
          >
            <PreviewIcon color="#9747FF" />
          </IconButton>

          <IconButton
            aria-label="close"
            sx={{
              padding: "9px 10px",
              borderRadius: "5px",
              backgroundColor: "#41C3C3",
            }}
          >
            <ShareOutlinedIcon color="#fff" />
          </IconButton>
        </Box>
        <Box className={classes.sectionBox} mb={2}>
          <Typography className="title" mb={2}>
            {isMyLanguage ? "Notis : Seksyen 14(2)" : "Notis : Seksyen 14(2)"}
          </Typography>

          <Box
            sx={{
              p: 4,
              border: "1px dashed #666666",
              borderRadius: "10px",
              background: "#9797974A",
            }}
          >
            <Typography color="#666666" mb={4} textAlign="center">
              АКTА PERTUBUHAN 1966 <br />
              PERINTAH MENGEMUKAKAN MAKLUMAT PERTUBUHAN <br />
              [Seksyen 14 (2)]
            </Typography>

            <Typography color="#666666" lineHeight={2}>
              NAMA PERTUBUHAN : <br />
              ALAMAT PERTUBUHAN : <br />
              NOMBOR PENDAFTARAN :
            </Typography>
          </Box>
        </Box>

        <Box className={classes.sectionBox}>
          <Input
            isLabel={false}
            // disabled={isViewMode}
            value={getValues().text}
            isLabelNoSpace={false}
            fullFeatureRichText={true}
            name="decisionNotes"
            type="richText"
            rows={4}
            // error={!!formErrors.matlamatPertubuhan}
            // helperText={formErrors.matlamatPertubuhan}
            // onChange={(e) => {
            //   setMatlamatPertubuhan(e.target.value);
            //   setFormErrors((prevErrors) => ({
            //     ...prevErrors,
            //     matlamatPertubuhan: "",
            //   }));
            // }}
          />
        </Box>
      </Box>
      <Box className={classes.section}>
        <Box className={classes.sectionBox}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonOutline
              sx={{
                color: "var(--primary-color)",
              }}
            >
              Simpan Draf
            </ButtonOutline>
            <ButtonPrimary
              type="submit"
              // disabled={
              //   isSubmittingDocument || isUploadingEbook || isUploadingEbook
              // }
            >
              {/* {isSubmittingDocument ||
            isUploadingEbook ||
            (isUploadingEbook && <CircularProgress size={15} />)} */}
              {t("hantar")}
            </ButtonPrimary>
          </Box>
        </Box>
      </Box>
      <DialogConfirmation
        isMutating={false}
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onAction={handleSaveDocument}
        onConfirmationText={
          isMyLanguage
            ? `Adakah Anda Pasti ${id ? "Kemaskini" : "Cipta"} Dokumen Ini ? ?`
            : `Are You Sure You ${id ? "Updated" : "Created"} This Document ?`
        }
        isSuccess={isSuccess}
        onSuccessText={
          isMyLanguage
            ? `Dokumen Anda Berjaya ${id ? "Dikemaskini" : "Dicipta"}`
            : `Your Document Was Successfully ${id ? "Updated" : "Created"}`
        }
      />
    </form>
  );
};

export default DaftarPanduanForm;
