import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import useQuery from "../../../helpers/hooks/useQuery";

import {
  Box,
  Typography,
  IconButton,
  TextField,
  InputAdornment,
  useTheme,
  SelectChangeEvent,
  Skeleton,
  Pagination,
  TablePagination,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";

interface IPerubuhanList {
  branchCount: number;
  societyId: string | number;
  societyName: string;
}

export const MainListCawangan: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const [searchText, setSearchText] = useState("");
  const [tempSearchText, setTempSearchText] = useState(searchText);

  // const filterOptions = [
  //   { value: "menunggu_keputusan", label: "Menunggu Keputusan" },
  //   { value: "belum_dihantar", label: "Belum dihantar" },
  //   { value: "lulus", label: "Lulus" },
  //   { value: "kuiri", label: "Kuiri" },
  //   { value: "tolak", label: "Tolak" },
  //   { value: "menunggu_maklumbalas", label: "Menunggu Maklumbalas" },
  // ];

  const { data, isLoading, refetch } = useQuery({
    url: "society/paging-branch-count",
    filters: [
      { field: "pageSize", operator: "eq" as const, value: pageSize },
      { field: "pageNo", operator: "eq" as const, value: page },
      ...(searchText
        ? [{ field: "name", operator: "eq" as const, value: searchText }]
        : []),
    ],
  });

  const pertubuhanList: IPerubuhanList[] = data?.data?.data?.data;
  const totalCount = data?.data?.data?.total || 0;

  const handlePageChange = (_: any, newPage: number) => {
    setPage(newPage + 1); // Convert zero-based to one-based
  };

  const handleSearch = () => {
    setSearchText(tempSearchText);
    setPage(1);
    refetch();
  };

  return (
    <Box sx={{ backgroundColor: "white", borderRadius: "16px", p: 8 }}>
      {/* Search Bar */}
      <TextField
        fullWidth
        placeholder={t("nameOfBranchOrganization")}
        variant="outlined"
        value={tempSearchText}
        onChange={(e) => setTempSearchText(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            e.preventDefault();
            handleSearch();
          }
        }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon
                sx={{
                  color: "var(--text-grey-disabled)",
                  marginLeft: "8px",
                }}
              />
            </InputAdornment>
          ),
        }}
        sx={{
          display: "block",
          boxSizing: "border-box",
          maxWidth: 570,
          marginInline: "auto",
          height: "40px",
          background: "var(--border-grey)",
          opacity: 0.5,
          border: "1px solid var(--text-grey)",
          borderRadius: "10px",
          "& .MuiOutlinedInput-root": {
            height: "40px",
            "& fieldset": {
              border: "none",
            },
          },
        }}
      />

      {/* Table Header */}
      <Box
        sx={{ display: "flex", justifyContent: "space-between", mb: 2, mt: 6 }}
      >
        <Typography sx={{ color: "#666666", fontWeight: 600 }}>
          {t("nameOfBranchOrganization")}
        </Typography>
        <Typography sx={{ color: "#666666", fontWeight: 600 }}>
          {t("numberOfBranches")}
        </Typography>
      </Box>

      {/* Data or Skeleton */}
      {isLoading ? (
        <Box sx={{ width: "100%" }}>
          <Skeleton height={100} />
          <Skeleton height={100} animation="wave" />
          <Skeleton height={100} />
        </Box>
      ) : pertubuhanList?.length > 0 ? (
        pertubuhanList?.map((item) => (
          <Box
            key={item.societyId}
            sx={{
              backgroundColor: "white",
              border: "1px solid #EAEAEA",
              borderRadius: "8px",
              mb: 2,
              "&:hover": { backgroundColor: "#F5F5F5", cursor: "pointer" },
            }}
            onClick={() =>
              navigate(`/pertubuhan/society/${item.societyId}/senarai/cawangan`)
            }
          >
            <Box
              sx={{ display: "flex", justifyContent: "space-between", p: 2 }}
            >
              <Typography sx={{ color: "#333333" }}>
                {item.societyName}
              </Typography>
              <Typography sx={{ color: "#666666" }}>
                {item.branchCount}
              </Typography>
            </Box>
          </Box>
        ))
      ) : (
        <Typography className="label" sx={{ mt: 3 }}>
          {t("noData")}
        </Typography>
      )}

      {/* Pagination */}
      <Box sx={{ display: "flex", justifyContent: "right", mt: 4 }}>
        <TablePagination
          component="div"
          count={totalCount}
          page={page - 1} // Convert one-based to zero-based
          onPageChange={handlePageChange}
          rowsPerPage={pageSize}
          onRowsPerPageChange={(e) => {
            setPageSize(parseInt(e.target.value, 10));
            setPage(1); // Reset to first page when changing page size
          }}
          rowsPerPageOptions={[5, 10, 25, 50]}
          labelRowsPerPage={t("rowsPerPage")}
          labelDisplayedRows={({ from, to, count, page }) =>
            `${from}-${to} ${t("of")} ${count}`
          }
        />
      </Box>
    </Box>
  );
};

export default MainListCawangan;
