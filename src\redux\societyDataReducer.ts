import { createSlice } from "@reduxjs/toolkit";

// Define a type for the slice state
export interface SocietyResponseBodyGetById {
  address: string | number
  akuiAjk: null
  appealStatement: null
  appealStatus: null
  applicationNo: string
  applicationStatusCode: number
  approvedDate: null
  bankName: null
  bankReferenceNo: null
  benarAjk: null
  bubar: null
  categoryCodeJppm: null
  city: string
  cityCode: null
  constitutionType: string
  countryCode: null
  createdBy: string
  /**
   * format: DD-MM-YYYY
   */
  createdDate: string
  designationCode: null
  districtCode: string
  email: string
  faxNumber: string
  flatPadam: null
  hasBranch: number
  id: string | number
  idOsol: null
  identificationNo: string
  inquire: null
  isManageAuthorized: null
  isQueried: null
  kodPtj: null
  mailingAddress: string
  mailingCity: string
  mailingCityCode: null
  mailingCountryCode: null
  mailingDistrictCode: string
  mailingPostcode: string
  mailingSmallDistrictCode: null
  mailingStateCode: string
  migrateAjk: null
  migrateStat: null
  migrateUndang: null
  modifiedBy: string
  /**
   * format DD-MM-YYYY
   */
  modifiedDate: string
  noPPMLama: null
  noPPPLama: null
  noteRo: null
  notis1: null
  originAddress: null
  originCity: null
  originDistrictCode: null
  originName: null
  originPostcode: null
  originSmallDistrictCode: null
  originStateCode: null
  /**
   * format DD-MM-YYYY
   */
  paymentDate: string
  paymentId: string | number
  /**
   * @description possible values:
   * - **ONLINE**
   */
  paymentMethod: string
  phoneNumber: string
  postcode: string
  receiptNo: null
  receiptStatus: null
  reconcileDate: null
  registeredDate: string
  replyNotis: null
  ro: null
  roBatal: null
  roUpdate: null
  rujuk: null
  shortName: null
  smallDistrictCode: null
  societyLevel: null
  societyName: string
  societyNo: null | string
  statBebas: null
  statPindaKecaw: null
  stateCode: string
  statement: null
  statementDate: null
  statusCode: string
  subCategoryCode: null
  tarikhAlih: null
  queryText: string
}

// Define the initial state using that type
const initialState: { data: SocietyResponseBodyGetById } = {
  data: {} as SocietyResponseBodyGetById,
};

export const societyDataSlice = createSlice({
  name: "societyData",
  initialState,
  reducers: {
    setSocietyDataRedux: (state, action) => {
      state.data = action.payload
    },
  },
  selectors: {
    getSocietyDataRedux: (state) =>
      Object.keys(state.data).length >= 0 ? state.data : null
  }
});

export const { setSocietyDataRedux } = societyDataSlice.actions;

export const { getSocietyDataRedux } = societyDataSlice.selectors

export default societyDataSlice.reducer;
