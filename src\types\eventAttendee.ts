export interface IEventAttendee {
  // id: string | number;
  fullName: string;
  email: string;
  phoneNumber: string;
  // identificationNo: string;
  societyId: string | number;
  applicationStatusCode: string;
  createdAt: string;
  updatedAt: string;
  present: boolean;
  attendanceNo: string;
  isFeedbackCompleted: boolean;
}

export interface IAttendeeRequest {
  eventNo: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  identificationNo: string;
  societyId: string | number;
}
export interface IAttendeeCancelRequest {
  eventNo: string;
  attendanceNo: string;
}

export interface AttendanceUpdateRequest {
  eventNo: string;
  identificationNo: string;
  present: boolean;

}

export interface AttendanceUpdateResponse {
  eventName: string;
  attendanceNo: string;
}

export interface AttendeesName extends AttendeesCancelledName {
  fullName: string;
  present: boolean;
  dateRegistered: string;
  timeRegistered: string;
  dateCheckedIn: string;
  timeCheckedIn: string;
}

export interface AttendeesCancelledName {
  fullName: string;
  present: boolean;
  societyNameList: string[];
  positionList: string[];
  dateCancelled: string;
  timeCancelled: string;
}
