import React, {useEffect, useRef, useState} from "react";
import {
  Box,
  FormControl,
  FormHelperText,
  Grid,
  MenuItem,
  Select,
  SxProps,
  TextField,
  Theme,
  Typography
} from "@mui/material";
import DurationComponent from "@/pages/internal-training/durationComponent";
import { ButtonOutline, ButtonPrimary, DialogConfirmation } from "@/components";
import { useTranslation } from "react-i18next";
import { useCustom, useCustomMutation, useNotification } from "@refinedev/core";
import { API_URL } from "@/api";
import { useNavigate } from "react-router-dom";
import ConfirmationDialog from "@/pages/internal-training/confirmDialog";
import { DocumentUploadType, useUploadPresignedUrl } from "@/helpers";
import FileInput from "@/components/input/FileInput";

export interface TrainingFormProps {
  headerStyle: SxProps<Theme>,
  labelStyle: SxProps<Theme>,
  borderStyle: SxProps<Theme>,
  handleNext: (id: string | number, page: string) => void,
  courseId: string | number,
  isUpdate: boolean,
  setCourseId?: React.Dispatch<React.SetStateAction<string | number>>
}

const CreateStepOne: React.FC<TrainingFormProps> = ({
  headerStyle,
  labelStyle,
  borderStyle,
  handleNext,
  courseId,
  isUpdate,
  setCourseId
}) => {

  const { t, i18n } = useTranslation();

  const [openModal, setOpenModal] = useState(false);
  const [level, setLevel] = useState("")
  const [hour, setHour] = useState(1)
  const [minute, setMinute] = useState(0)
  const { open: openNotification } = useNotification();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [tempCourseId, setTempCourseId] = useState<string | number>(0);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    level: "",
    duration: 0,
    status: 0,
    explanation: "",
    objective: "",
    thumbnailUrl: "",
    isUpdated: false
  });

  const uploadedFilesRef = useRef<(File | null)[]>([null, null, null]);

  useEffect(() => {
    if (courseId != 0) {
      setTempCourseId(courseId)
    }
  }, [courseId])

  const navigate = useNavigate();
  console.log("courseId", courseId);
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (!file) return;

    const isDuplicate = uploadedFilesRef.current.some((existingFile, i) => {
      return (
        existingFile &&
        existingFile.name === file.name &&
        existingFile.size === file.size &&
        i !== 0
      );
    });
    if (isDuplicate) {
      event.target.value = "";
      return;
    }
    setSelectedFile(file);
    setFormData((prevState) => ({
      ...prevState,
      thumbnailUrl: file.name,
    }));
  };

  const hourCallback = (e: number) => {
    setHour(e);
  }

  const minuteCallback = (e: number) => {
    setMinute(e);
  }

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!formData.title) errors.title = t("fieldRequired");
    if (!formData.description) errors.description = t("fieldRequired");
    if (!formData.level) errors.level = t("fieldRequired");
    if (!formData.duration) errors.duration = t("fieldRequired");
    //if (!formData.status) errors.status = t("requiredValidation");
    if (!formData.explanation) errors.explanation = t("fieldRequired");
    if (!formData.objective) errors.objective = t("fieldRequired");
    return errors;
  };


  useEffect(() => {
    setFormData((prevState) => ({
      ...prevState,
      duration: hour * 60 + minute,
    }));
  }, [hour, minute]);

  const handleSaveDraft = (e: React.MouseEvent<HTMLButtonElement>) => {
    if ((isUpdate || tempCourseId) && formData.isUpdated) Edit(0);
    else if ((isUpdate || tempCourseId) && !formData.isUpdated && selectedFile) {
      uploadFile({
        params: {
          type: DocumentUploadType.TRAINING_POSTER,
          trainingId: tempCourseId
        },
        file: selectedFile,
      });
    }
    else if (!tempCourseId) Create(0);
    else {
      openNotification?.({
        type: "success",
        message: t("noChanges"),
        //description: "Failed to fetch feedback answers",
      });
    }
  }

  const handleSave = () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    if ((isUpdate || tempCourseId) && formData.isUpdated) Edit(formData.status);
    else if ((isUpdate || tempCourseId) && !formData.isUpdated && selectedFile) {
      uploadFile({
        params: {
          type: DocumentUploadType.TRAINING_POSTER,
          trainingId: tempCourseId
        },
        file: selectedFile,
      });
    }
    else if (!tempCourseId) Create(formData.status);
    else if (tempCourseId) {
      handleNext(tempCourseId, "pelajaran");
    }
  }

  const trainingLevels = [
    {
      id: "Asas",
      name: "Asas",
    },
    {
      id: "Sederhana",
      name: "Sederhana",
    },
    {
      id: "Mahir",
      name: "Mahir",
    },
  ]

  const trainingStatuses = [
    {
      id: 1,
      name: "Publish",
    },
    {
      id: 2,
      name: "Hidden",
    },
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
      isUpdated: true
    }));
    setFormErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const { upload: uploadFile, isLoading: isUploading } = useUploadPresignedUrl({
    onSuccessUpload: (data) => {
      console.log("uploadFile", data)
      setSelectedFile(null);
      if (data?.data?.data?.trainingId) handleNext(data?.data?.data?.trainingId, "pelajaran");
    },
  });

  const { mutate: create, isLoading: isLoadingCreate } = useCustomMutation();
  const Create = (status: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    create(
      {
        url: `${API_URL}/society/admin/training/courses`,
        method: "post",
        values: {
          title: formData.title,
          description: formData.description,
          status: status,
          difficultyLevel: formData.level,
          startDate: formattedDate,
          endDate: formattedDate,
          maxParticipants: 50,
          passingCriteria: 70,
          duration: formData.duration,
          explanation: formData.explanation,
          objective: formData.objective,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            console.log("Create", data?.data?.data);
            setTempCourseId(data?.data?.data);
            if (setCourseId) setCourseId(data?.data?.data);
            setOpenModal(false)
            // Handle file upload
            if (selectedFile) {
              uploadFile({
                params: {
                  type: DocumentUploadType.TRAINING_POSTER,
                  trainingId: data?.data?.data
                },
                file: selectedFile,
              });
            }
            else {
              handleNext(data?.data?.data, "pelajaran");
            }
            return {
              message: t("TRAINING_CREATED"),
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { mutate: edit, isLoading: isLoadingEdit } = useCustomMutation();
  const Edit = (status: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    edit(
      {
        url: `${API_URL}/society/admin/training/courses`,
        method: "put",
        values: {
          id: tempCourseId,
          title: formData.title,
          description: formData.description,
          difficultyLevel: formData.level,
          status: status,
          startDate: formattedDate,
          endDate: formattedDate,
          maxParticipants: 50,
          passingCriteria: 70,
          duration: formData.duration,
          explanation: formData.explanation,
          objective: formData.objective,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            // Handle file upload
            if (selectedFile) {
              uploadFile({
                params: {
                  type: DocumentUploadType.TRAINING_POSTER,
                  trainingId: data?.data?.data
                },
                file: selectedFile,
              });
            } else {
              handleNext(tempCourseId, "pelajaran");
            }
            return {
              message: t("TRAINING_UPDATED"),
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { data: trainingData, isLoading: isTrainingLoading } = useCustom({
    url: `${API_URL}/society/admin/training/courses/${tempCourseId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: isUpdate || tempCourseId != 0,
      retry: false,
      cacheTime: 0,
    },
  });

  const trainingDetails = trainingData?.data?.data || {};
  console.log("trainingCourse", trainingData, isUpdate)

  const { data: trainingDocData, isLoading: isTrainingDocLoading } = useCustom({
    url: `${API_URL}/document/documentByParam`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        trainingId: tempCourseId,
        type: DocumentUploadType.TRAINING_POSTER
      },
    },
    queryOptions: {
      enabled: isUpdate || tempCourseId != 0,
      retry: false,
      cacheTime: 0,
    },
  });

  const trainingDoc = trainingDocData?.data?.data || [];
  console.log("trainingDoc", trainingDocData, isUpdate)

  useEffect(() => {
    if(trainingDoc.length > 0){
      setSelectedFile(trainingDoc[0])
    }
  },[trainingDocData])

  useEffect(() => {
    if ((isUpdate || tempCourseId != 0) && Object.keys(trainingDetails).length > 0) {
      const temp = {
        title: trainingDetails.title || "",
        description: trainingDetails.description || "",
        level: trainingDetails.difficultyLevel || "",
        duration: trainingDetails.duration,
        status: trainingDetails.status || "",
        explanation: trainingDetails.explanation || "",
        objective: trainingDetails.objective || "",
        thumbnailUrl: trainingDoc.url || "",
        isUpdated: false
      }
      setFormData(temp);
    }
  }, [trainingData]);

  return (<>
    <Box
      sx={{
        width: "85%",
        borderRadius: 2.5,
        backgroundColor: "#fff",
        //display: "inline",
        px: 2,
        py: 2,
        mb: 1,
      }}
    >
      <Box
        sx={borderStyle}
      >
        <Typography
          sx={headerStyle}
        >
          Nama Latihan
        </Typography>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("trainingName")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <TextField
              size={"small"}
              fullWidth
              required
              name="title"
              value={formData.title}
              error={!!formErrors.title}
              helperText={formErrors.title}
              onChange={handleInputChange}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("trainingDescription")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <TextField
              size={"small"}
              fullWidth
              required
              multiline
              rows={4}
              name="description"
              value={formData.description}
              error={!!formErrors.description}
              helperText={formErrors.description}
              onChange={handleInputChange}
            />
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={borderStyle}
      >
        <Typography
          sx={headerStyle}
        >
          Kategori Latihan
        </Typography>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("trainingLevel")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.level}
            >
              <Select
                value={formData.level}
                displayEmpty
                required
                name="level"
                size={"small"}
                //disabled={isLoadingAddress}
                onChange={(e) => {
                  setLevel(e.target.value);
                  setFormData((prevState) => ({
                    ...prevState,
                    level: e.target.value,
                  }));
                  setFormErrors((prev) => ({
                    ...prev,
                    level: "",
                  }));
                }}
                variant={'outlined'}>
                <MenuItem value="" disabled>
                  {t("pleaseSelect")}
                </MenuItem>
                {trainingLevels
                  .map((item: any) => (
                    <MenuItem key={item.id} value={item.id}>
                      {item.name}
                    </MenuItem>
                  ))}
              </Select>
              {formErrors.level && (
                <FormHelperText>{formErrors.level}</FormHelperText>
              )}
            </FormControl>
          </Grid>
          <DurationComponent setHour={setHour} setMinute={setMinute}
            labelStyle={labelStyle} hour={hour} minute={minute}
            minuteError={formErrors.duration} />
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("trainingStatus")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.status}
            >
              <Select
                value={formData.status}
                displayEmpty
                required
                name="status"
                size={"small"}
                variant={"outlined"}
                //disabled={isLoadingAddress}
                onChange={(e) => {
                  // @ts-ignore
                  setFormData((prevState) => ({
                    ...prevState,
                    status: e.target.value,
                  }));
                  setFormErrors((prev) => ({
                    ...prev,
                    status: "",
                  }));
                }}
              >
                <MenuItem value="" disabled>
                  {t("pleaseSelect")}
                </MenuItem>
                {trainingStatuses
                  .map((item: any) => (
                    <MenuItem key={item.id} value={item.id}>
                      {item.name}
                    </MenuItem>
                  ))}
              </Select>
              {formErrors.status && (
                <FormHelperText>{formErrors.status}</FormHelperText>
              )}
            </FormControl>
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={borderStyle}
      >
        <Typography
          sx={headerStyle}
        >
          Keterangan Latihan & Objektif
        </Typography>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("trainingExplanation")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <TextField
              size={"small"}
              fullWidth
              required
              multiline
              rows={4}
              name="explanation"
              value={formData.explanation}
              error={!!formErrors.explanation}
              helperText={formErrors.explanation}
              onChange={handleInputChange}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("trainingObjective")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <TextField
              size={"small"}
              fullWidth
              required
              multiline
              rows={4}
              name="objective"
              value={formData.objective}
              error={!!formErrors.objective}
              helperText={formErrors.objective}
              onChange={handleInputChange}
            />
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={borderStyle}
      >
        <Typography
          sx={headerStyle}
        >
          Poster Banner
        </Typography>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("trainingPoster")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            {/*<Box
              sx={{
                border: "2px solid #DADADA",
                borderRadius: "8px",
                p: 2,
                gap: 2,
                textAlign: "center",
                cursor: "pointer",
                height: "200px",
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
              }}
              onClick={() => {
                const element = document.getElementById("thumbnailBanner");
                if (element) {
                  element.click();
                }
              }}
            >
              {selectedFile || uploadedFiles?.length ? (
                <Typography sx={{ color: "#147C7C", mb: 1 }}>
                  {selectedFile
                    ? selectedFile.name
                    : uploadedFiles[0]?.name}
                </Typography>
              ) : (
                <>
                <Box
                    sx={{
                      width: 50,
                      aspectRatio: "1/1",
                      display: "flex",
                      justifyContent: "center",
                      alignContent: "center",
                      textAlign: "center",
                      borderRadius: 20,
                      mb: 2,
                      // p: 5,
                      bgColor: "#F2F4F7",
                    }}
                  >
                    <img
                      width={30}
                      src={"/uploadFileIcon.svg"}
                      alt={"view"}
                    />
                  </Box>

                  <Typography
                    sx={{
                      color: "var(--primary-color)",
                      fontWeight: "500",
                      fontSize: "14px",
                    }}
                  >
                    {t("muatNaik")}
                  </Typography>
                  <Typography
                    sx={{
                      color: "#667085",
                      fontWeight: "400",
                      fontSize: "12px",
                    }}
                  >
                    {`SVG, PNG, JPG or GIF (max. 800x400px)`}
                  </Typography>
                </>
              )}
              <input
                id="thumbnailBanner"
                type="file"
                hidden
                onChange={handleFileChange}
                accept=".svg,.png,.jpg,.jpeg,.gif"
              />
            </Box>*/}
            <FileInput
              acceptedFormats={[".jpg", ".jpeg", ".png"]}
              buttonText="Click to upload"
              id="thumbnailBanner"
              name="thumbnailBanner"
              value={selectedFile}
              currentIndex={0}
              uploadedFilesRef={uploadedFilesRef}
              onChange={handleFileChange}
              currentName={selectedFile?.name}
              t={t}
            />

            {(!selectedFile) ? (
              <FormHelperText sx={{ color: "red" }}>
                {t("fieldRequired")}
              </FormHelperText>
            ) : null}
          </Grid>
        </Grid>
      </Box>
      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline
          sx={{
            bgcolor: "white",
            "&:hover": { bgcolor: "white" },
            width: "auto",
          }}
          onClick={handleSaveDraft}
        >
          {t("save")}
        </ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{
            width: "auto",
          }}
          onClick={() => setOpenModal(true)}
        //disabled={true}
        >
          {t("next")}
        </ButtonPrimary>
      </Grid>
    </Box>
    <DialogConfirmation
      open={openModal}
      onClose={() => {
        setOpenModal(false);
      }}
      onAction={handleSave}
      isMutating={false}
      onConfirmationText={"Adakah anda pasti untuk mencipta latihan ini?"}
    />
  </>);
}

export default CreateStepOne;
