import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import {
  ApplicationStatusList,
  DecisionOptionsCode,
  NEW_PermissionNames,
  pageAccessEnum,
} from "../../../helpers/enums";

import {
  Box,
  CircularProgress,
  Grid,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import MaklumatAmSection from "./views/MaklumatAmSection";
import PengesahanSection from "./views/PengesahanSection";
import AccordionComp from "../View/Accordion";
import { ButtonOutline, ButtonPrimary } from "../../../components/button";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "@/redux/store";
import { fetchSocietyByIdData } from "@/redux/APIcalls/societyByIdThunks";
import { filterEmptyValuesOnObject, useMutation, useQuery } from "@/helpers";
import { FieldValues, useForm } from "react-hook-form";
import {
  DialogConfirmation,
  SelectFieldController,
  TextFieldController,
} from "@/components";
import { useCustom } from "@refinedev/core";
import { API_URL } from "@/api";
import AuthHelper from "@/helpers/authHelper";

const subTitleStyle = {
  color: "#0CA6A6",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

const labelStyle = {
  fontSize: "16px",
  color: "#666666",
  fontWeight: "400 !important",
};

type ReadStatusType = {
  [key: number]: boolean;
};

function KeputusanIndukPegawaiAwam() {
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const hasUpdatePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_INDUK.children.PEGAWAI_AWAM.label,
    pageAccessEnum.Update
  );

  const navigate = useNavigate();
  const { t } = useTranslation();
  const decisionOptions = DecisionOptionsCode(t).filter((i) => i.value !== 36);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [roExist, setRoExist] = useState(false);
  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);

  const { id } = useParams();
  const decodeID = atob(id || "");
  const dispatch: AppDispatch = useDispatch();

  const {
    data: societyDataById,
    loading: loadingSociety,
    error: errorSociety,
  } = useSelector((state: any) => state.societyByIdData);

  const {
    data: committeeData,
    isLoading: CommitteeDataLoading,
    refetch: fetchCommitteeData,
  } = useQuery({
    url: `society/public_officer/${decodeID}`,
    autoFetch: true,
    onSuccess: (data: any) => {
      const roId = data?.data?.data?.ro;
      const roNote = data?.data?.data?.roNote;
      if (roId) {
        setRoExist(true);
        setValueRoAction("ro", Number(roId));
        setValueRoAction("roNote", roNote);
      }
    },
  });

  useEffect(() => {
    if (committeeData?.data?.data?.societyId) {
      dispatch(
        fetchSocietyByIdData({
          id: committeeData?.data?.data?.societyId,
        })
      );
    }
  }, [committeeData?.data?.data]);

  const [readStatus, setReadStatus] = useState<ReadStatusType>({});

  const sectionItems = [
    {
      subTitle: t("generalInformation"),
      component: (
        <MaklumatAmSection
          societyDataById={societyDataById}
          PublicOfficerData={committeeData?.data?.data}
        />
      ),
    },
    {
      subTitle: t("pengesahan"),
      component: (
        <PengesahanSection
          societyDataById={societyDataById}
          PublicOfficerData={committeeData?.data?.data}
        />
      ),
    },
  ];

  const handleChangeCurrentExpandSection =
    (item: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);

      if (isExpanded) {
        setReadStatus((prevState) => {
          const updatedStatus = sectionItems.reduce((acc, _, i) => {
            if (i + 1 <= item) {
              acc[i + 1] = true;
            } else {
              acc[i + 1] = !!prevState[i + 1] || false;
            }
            return acc;
          }, {} as Record<number, boolean>);
          return updatedStatus;
        });
      }
    };
  const handleDialogClose = () => setIsDialogOpen(false);
  const handleFormSubmit = () => {
    const payload = getValues();
    if (committeeData?.data?.data?.societyId) {
      console.log("committeeData retreived", committeeData?.data?.data?.data);
      payload.approvalStatus = payload.applicationStatusCode;
      payload.type = "SOCIETY_PUBLIC_OFFICER";
      payload.rejectReason =
        payload.applicationStatusCode === 4 ? payload?.note : null;
      payload.note = payload.applicationStatusCode === 3 ? payload?.note : null;
      payload.finished = true;

      const filterPayload = filterEmptyValuesOnObject(payload);
      updateDecision(filterPayload);
    }
  };

  const { control, handleSubmit, getValues, watch, setValue } =
    useForm<FieldValues>({
      defaultValues: {
        approvalStatus: "",
        rejectReason: "",
        note: "",
      },
    });

  const { fetch: updateDecision, isLoading: isLoadingDecisionUpdate } =
    useMutation({
      url: `society/public_officer/${decodeID}/admin/updateApprovalStatus`,
      method: "put",
      config: {
        headers: {
          "Content-Type": "application/json",
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
      onSuccess: (data) => {
        setIsSuccess(true);
        navigate(-1);
      },
    });

  const onSubmit = () => setIsDialogOpen(true);

  //
  const methodsRoAction = useForm();
  const {
    handleSubmit: handleSubmitRoAction,
    control: controlRoAction,
    setValue: setValueRoAction,
    getValues: getValuesRoAction,
    watch: watchRoAction,
  } = methodsRoAction;

  const { data: roListData, isLoading: isRoListLoading } = useCustom({
    url: `${API_URL}/society/user/getRoList`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: societyDataById?.id,
      },
    },
  });

  const roList = roListData?.data?.data ?? [];

  const roListOptions =
    roList.map((item: any) => ({
      value: item.id,
      label: item.name,
    })) || [];

  const { fetch: updateRO, isLoading: isUpdatingRO } = useMutation({
    url: `society/public_officer/${decodeID}/admin/updateRo`,
    method: "put",
  });

  const onSubmitRoAction = (data: FieldValues) => {
    updateRO(data);
  };

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            {societyDataById?.societyName}
            <br />
            {societyDataById?.societyNo}
          </Box>
        </Box>

        <Box sx={{ mt: 4 }}>
          {sectionItems.map((item, index) => {
            return (
              <AccordionComp
                key={index}
                subTitle={item.subTitle}
                currentIndex={index + 1}
                currentExpand={currentExpandSection}
                readStatus={readStatus}
                onChangeFunc={handleChangeCurrentExpandSection}
              >
                {item.component}
              </AccordionComp>
            );
          })}
        </Box>

        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <form onSubmit={handleSubmitRoAction(onSubmitRoAction)}>
            <Box
              sx={{
                p: 3,
                mb: 3,
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography variant="h6" component="h2" sx={subTitleStyle}>
                {t("ROAction")}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("responsibleRO")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <SelectFieldController
                    name="ro"
                    disabled={!hasUpdatePermission || roExist}
                    control={controlRoAction}
                    options={roListOptions}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("remarks")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={controlRoAction}
                    name="roNote"
                    disabled={!hasUpdatePermission || roExist}
                    multiline
                    defaultValue={getValuesRoAction("roNote")}
                    sx={{
                      minHeight: "92px",
                    }}
                    sxInput={{
                      minHeight: "92px",
                    }}
                  />
                </Grid>
              </Grid>
            </Box>

            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                mb: 6,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonPrimary
                type="submit"
                disabled={!hasUpdatePermission || roExist}
              >
                {t("update")}
              </ButtonPrimary>
            </Grid>
          </form>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Box
              sx={{
                pl: 2,
                p: 3,
                mt: 1,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
              }}
            >
              <Box
                sx={{
                  mb: 3,
                }}
              >
                <Typography variant="h6" component="h2" sx={subTitleStyle}>
                  {t("keputusan")}
                </Typography>
              </Box>
              <Grid container>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("statusPermohonan")}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                  <SelectFieldController
                    control={control}
                    name="applicationStatusCode"
                    options={decisionOptions}
                    disabled={!hasUpdatePermission || isFormDisabled}
                    sx={{
                      background: isFormDisabled
                        ? "rgba(218, 218, 218, 0.5)"
                        : "",
                    }}
                    required
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("remarks")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={control}
                    name="note"
                    multiline
                    disabled={!hasUpdatePermission || isFormDisabled}
                    required={watch("applicationStatusCode") === 36}
                    sx={{
                      minHeight: "92px",
                      background: isFormDisabled
                        ? "rgba(218, 218, 218, 0.5)"
                        : "",
                    }}
                    sxInput={{
                      minHeight: "92px",
                    }}
                  />
                </Grid>
              </Grid>
            </Box>
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                marginTop: "34px",
                gap: "10px",
              }}
            >
              <ButtonOutline
                onClick={() =>
                  navigate(
                    "/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk"
                  )
                }
              >
                {t("back")}
              </ButtonOutline>

              <ButtonPrimary
                type="submit"
                disabled={
                  !hasUpdatePermission || isFormDisabled || CommitteeDataLoading
                }
              >
                {CommitteeDataLoading ? (
                  <CircularProgress
                    size="0.5rem"
                    sx={{
                      display: "block",
                    }}
                  />
                ) : (
                  t("hantar")
                )}
              </ButtonPrimary>
            </Box>
          </form>
        </Box>
      </Box>

      <DialogConfirmation
        open={isDialogOpen}
        onClose={handleDialogClose}
        onAction={handleFormSubmit}
        isMutating={isLoadingDecisionUpdate}
        onConfirmationText={t("permohonanConfirmation")}
        onSuccessText={t("applicationSuccessSubmited")}
        isSuccess={isSuccess}
        decisionLabel={
          ApplicationStatusList.find(
            (item) => item.id === getValues().applicationStatusCode
          )?.value || "-"
        }
      />
    </>
  );
}

export default KeputusanIndukPegawaiAwam;
