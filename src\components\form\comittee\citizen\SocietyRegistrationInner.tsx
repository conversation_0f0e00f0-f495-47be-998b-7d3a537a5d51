import { ButtonFormSubmitFormik, ButtonOutline } from "@/components/button";
import { DatePickerFormik } from "@/components/input/DatePickerFormik";
import FileUploader from "@/components/input/fileUpload";
import Input from "@/components/input/Input";
import { useFormikManualValidationHandler } from "@/contexts/formikManualValidation";
import { DefaultCommitteeCitizenSocietyRegistrationInitialValue } from "@/controllers";
import {
  autoDOBSetByIC,
  autoGenderSetByIC,
  capitalizeWords,
  CitizenshipStatus,
  DocumentUploadType,
  GenderType,
  getLocalStorage,
  IdTypes,
  MALAYSIA,
  OrganisationPositions,
} from "@/helpers";
import { useICValidation } from "@/helpers/hooks/useICValidation.ts";
import { SocietyAddressListResponseBodyGet } from "@/models";
import { gelaranList } from "@/pages/pertubuhan/pengurusan-pertubuhan/senarai-ajk/constanta";
import { Box, FormHelperText, Grid, Typography } from "@mui/material";
import { useBack } from "@refinedev/core";
import dayjs from "dayjs";
import { Field, FieldProps, useFormikContext } from "formik";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";

export const FormComitteeCitizenSocietyRegistrationInner = <
  Payload extends DefaultCommitteeCitizenSocietyRegistrationInitialValue = DefaultCommitteeCitizenSocietyRegistrationInitialValue,
  AddressList extends SocietyAddressListResponseBodyGet = SocietyAddressListResponseBodyGet
>({
  createdId,
}: any) => {
  const { t } = useTranslation();
  const { values, setFieldValue, initialValues, setFieldError } =
    useFormikContext<Payload>();
  const addressData = useSelector<any, AddressList[] | null>(
    (state) => state.addressData.data
  );
  const [params] = useSearchParams();
  const { handleSubmit } = useFormikManualValidationHandler<Payload>();
  const [ajkId, setAjkId] = useState<string | undefined>(undefined);
  const [JPNError, setJPNError] = useState(false);
  const encodedId = params.get("id");
  const id = atob(encodedId ?? "");
  const back = useBack();

  const {
    designationCode,
    employerAddressStatus,
    employerStateCode,
    identificationNo,
    jobCode,
    residentialStateCode,
    residentialPostcode,
    employerPostcode,
    id: committeeId,
  } = values;

  const organizationPosition =
    OrganisationPositions.find(
      (position) => position.value === Number(designationCode)
    )?.label ?? null;

  useEffect(() => {
    if (employerPostcode && employerPostcode.toString()?.length > 5) {
      setFieldValue(
        "employerPostcode",
        parseInt(employerPostcode.toString().slice(0, 5))
      );
    }
  }, [employerPostcode]);

  const {
    userICCorrect,
    userNameMatchIC,
    userExists,
    triggerICValidation,
    setUserICCorrect,
    setUserNameMatchIC,
    resetICValidation,
    integrationStatus,
  } = useICValidation({
    idType: values?.identificationType,
    idNumber: values?.identificationNo,
    fullName: values?.name,
  });

  useEffect(() => {
    const isMyKad =
      Number(values?.identificationType) === 1 ||
      Number(values?.identificationType) === 4;
    const nameReady = values?.name?.trim() !== "";
    const idReady = values?.identificationNo?.length === 12;
    if (integrationStatus === 0 && isMyKad) {
      setJPNError(true);
    } else {
      setJPNError(false);
    }
    if (integrationStatus === 1 && isMyKad && nameReady && idReady) {
      triggerICValidation();
    }
  }, [
    values?.identificationType,
    values?.identificationNo,
    values?.name,
    integrationStatus,
  ]);

  const occupationList = getLocalStorage("occupation_list", null);

  useEffect(() => {
    const type = Number(values?.identificationType);
    if (type === 1 || type === 4) {
      setFieldValue(
        "gender",
        autoGenderSetByIC(type, initialValues?.gender, values?.identificationNo)
      );
      setFieldValue(
        "dateOfBirth",
        autoDOBSetByIC(
          type,
          initialValues?.dateOfBirth,
          values?.identificationNo
        )
      );
    }
  }, [values?.identificationNo]);

  useEffect(() => {
    const type = Number(values?.identificationType);
    if (type === 1) {
      setFieldValue("nationalityStatus", 1);
    } else {
      setFieldValue("nationalityStatus", 2);
    }
  }, [values?.identificationType]);

  useEffect(() => {
    if (createdId) {
      setAjkId(createdId);
    }
  }, [createdId]);

  useEffect(() => {
    const type = Number(values?.identificationType);
    const raw = values?.phoneNumber;

    if (type === 1 && raw && !raw.startsWith("+60")) {
      let cleaned = raw.replace(/[^\d]/g, "");

      if (cleaned.startsWith("60")) {
        cleaned = cleaned.slice(2);
      }

      const limited = cleaned.slice(0, 10);
      const formatted = "+60" + limited;

      setFieldValue("phoneNumber", formatted);
    }
  }, []);

  return (
    <>
      <Box
        sx={{
          mb: 3,
          p: 3,
          borderRadius: "16px",
          border: "1px solid #D9D9D9",
        }}
      >
        <Typography sx={{ mb: 4 }} className="title">
          {t("positionInfo")}:{" "}
          {typeof organizationPosition === "string"
            ? t(organizationPosition)
            : ""}
        </Typography>
        <Box>
          <Grid container>
            <Field name="designationCode">
              {({
                field,
                meta,
              }: FieldProps<Payload["designationCode"], Payload>) => (
                <Input
                  required
                  {...field}
                  value={
                    typeof organizationPosition === "string"
                      ? t(organizationPosition)
                      : ""
                  }
                  disabled
                  label={t("position")}
                  error={!!meta.error}
                  helperText={meta.error}
                />
              )}
            </Field>
          </Grid>
        </Box>
      </Box>
      <Box
        sx={{
          p: 3,
          mb: 3,
          borderRadius: "16px",
          border: "1px solid #D9D9D9",
        }}
      >
        <Typography sx={{ mb: 4 }} className="title">
          {t("chairmanPersonalInfo")}:{" "}
          {typeof organizationPosition === "string"
            ? t(organizationPosition)
            : ""}
        </Typography>
        {JPNError ? (
          <Box sx={{ mb: 3 }}>
            <FormHelperText sx={{ color: "var(--error)" }}>
              {t("JPNError")}
            </FormHelperText>
          </Box>
        ) : null}
        <Box>
          <Field name="identificationType">
            {({ field, meta }: FieldProps) => (
              <Input
                required
                {...field}
                error={!!meta.error}
                label={t("idType")}
                disabled
                helperText={meta.error}
                value={t(
                  `${
                    IdTypes.find((item) => item.value === field.value)?.label ||
                    ""
                  }`
                )}
                options={IdTypes}
              />
            )}
          </Field>
          <Field name="identificationNo">
            {({ field, meta, form }: FieldProps) => (
              <Input
                required
                {...field}
                disabled={form.isSubmitting || JPNError}
                label={t("idNumber")}
                error={
                  !!meta.error ||
                  ((Number(values?.identificationType) === 1 ||
                    Number(values?.identificationType) === 4) &&
                    (values?.identificationNo?.length < 12 ||
                      (values?.identificationNo?.length === 12 &&
                        values?.name !== null &&
                        !userICCorrect) ||
                      (values?.identificationNo?.length === 12 && !userExists)))
                }
                helperText={
                  meta.error ||
                  ((Number(values?.identificationType) === 1 ||
                    Number(values?.identificationType) === 4) &&
                    (values?.identificationNo?.length === 12 &&
                    values?.name !== null &&
                    !userICCorrect
                      ? t("IcDoesNotExist")
                      : values?.identificationNo?.length === 12 && !userExists
                      ? t("userDoesntExist")
                      : values?.identificationNo?.length < 12
                      ? t("idNumberOnlyDigits")
                      : undefined))
                }
                type="text" // Use text to enforce length limit
                {...((values.identificationType === "1" ||
                  values.identificationType === "4") && {
                  digitsLimit: 12,
                  inputProps: {
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                    maxLength: 12,
                    minLength: 12,
                  },
                })}
                onChange={(e) => {
                  if (
                    values.identificationType === "1" ||
                    values.identificationType === "4"
                  ) {
                    const value = e.target.value
                      .replace(/\D/g, "")
                      .slice(0, 12); // Keep only digits & limit to 12
                    form.setFieldValue(field.name, value);
                  } else {
                    form.setFieldValue(field.name, e.target.value);
                  }
                }}
              />
            )}
          </Field>
          <Field name="otherPosition">
            {({ field, meta, form }: FieldProps) => (
              <Input
                required
                {...field}
                disabled={form.isSubmitting}
                error={!!meta.error}
                helperText={meta.error}
                label={t("title")}
                type="select"
                options={gelaranList}
              />
            )}
          </Field>
          <Field name="name">
            {({ field, meta, form }: FieldProps) => (
              <Input
                required
                {...field}
                disabled={form.isSubmitting || JPNError}
                error={
                  !!meta.error ||
                  (values?.identificationNo?.length === 12 &&
                    values?.name !== null &&
                    !userNameMatchIC)
                }
                helperText={
                  meta.error ||
                  (values?.identificationNo?.length === 12 &&
                    values?.name !== null &&
                    !userNameMatchIC)
                    ? t("invalidName")
                    : undefined
                }
                label={t("fullName")}
              />
            )}
          </Field>
          <Field name="gender">
            {({ field, meta, form }: FieldProps) => (
              <Input
                required
                {...field}
                type="select"
                options={GenderType.map((item) => ({
                  label: t(item.translateKey),
                  value: item.code,
                }))}
                disabled={form.isSubmitting}
                error={!!meta.error}
                helperText={meta.error}
                label={t("gender")}
              />
            )}
          </Field>
          <Field name="nationalityStatus">
            {({ field, meta }: FieldProps) => (
              <Input
                required
                {...field}
                error={!!meta.error}
                helperText={meta.error}
                label={t("citizenship")}
                value={initialValues?.identificationType === "1" ? 1 : 2}
                options={CitizenshipStatus.map((item) => ({
                  ...item,
                  label: t(item.label),
                }))}
                type="select"
                disabled
              />
            )}
          </Field>
          <Grid container spacing={2} alignItems="flex-start" sx={{ mb: 1 }}>
            <Grid item xs={12} sm={4}>
              <Typography
                variant="body1"
                sx={{
                  color: "#666666",
                  fontWeight: "400 !important",
                  fontSize: "14px",
                }}
              >
                {t("dateOfBirth")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <DatePickerFormik<Payload>
                name="dateOfBirth"
                disableFuture
                maxDate={new Date()}
                fontSize={16}
                helperText=""
                styleProfileID={2}
              />
            </Grid>
          </Grid>
          <Field name="placeOfBirth">
            {({ field, meta, form }: FieldProps) => (
              <Input
                required
                {...field}
                error={!!meta.error}
                helperText={meta.error}
                label={t("placeOfBirth")}
                type="text"
                fullWidth
                disabled={form.isSubmitting}
              />
            )}
          </Field>
          <Field name="jobCode">
            {({ field, form, meta }: FieldProps) => (
              <Input
                required
                {...field}
                error={!!meta.error}
                helperText={meta.error}
                disabled={form.isSubmitting}
                label={t("occupation")}
                type="select"
                options={occupationList}
              />
            )}
          </Field>
          <Field name="residentialAddress">
            {({ field, meta, form }: FieldProps) => (
              <Input
                required
                {...field}
                error={!!meta.error}
                helperText={meta.error}
                disabled={form.isSubmitting}
                label={t("residentialAddress")}
              />
            )}
          </Field>
          <Field name="residentialStateCode">
            {({ field, meta, form }: FieldProps) => (
              <Input
                required
                {...field}
                error={!!meta.error}
                helperText={meta.error}
                label={t("state")}
                type="select"
                fullWidth
                multiline
                disabled={form.isSubmitting}
                rows={3}
                options={addressData
                  ?.filter((item) => item.pid == MALAYSIA)
                  .map((item) => ({
                    label: capitalizeWords(item.name, null, true),
                    value: `${item.id}`,
                  }))}
              />
            )}
          </Field>
          <Field name="residentialDistrictCode">
            {({ field, meta, form }: FieldProps) => (
              <Input
                required
                {...field}
                error={!!meta.error}
                helperText={meta.error}
                label={t("district")}
                type="select"
                fullWidth
                multiline
                rows={3}
                disabled={form.isSubmitting}
                options={addressData
                  ?.filter(
                    (item) =>
                      // @ts-expect-error
                      item.pid == residentialStateCode
                  )
                  .map((item) => ({
                    label: capitalizeWords(item.name, null, true),
                    value: `${item.id}`,
                  }))}
              />
            )}
          </Field>
          <Field name="residentialCity">
            {({ field, meta, form }: FieldProps) => (
              <Input
                {...field}
                error={!!meta.error}
                helperText={meta.error}
                label={t("city")}
                disabled={form.isSubmitting}
              />
            )}
          </Field>
          <Field name="residentialPostcode">
            {({ field, meta, form }: FieldProps) => (
              <Input
                {...field}
                required
                disabled={form.isSubmitting}
                error={!!meta.error}
                helperText={meta.error}
                label={t("postcode")}
                inputProps={{ maxLength: 5 }}
                onChange={(e) => {
                  const onlyDigits = e.target.value
                    .replace(/\D/g, "")
                    .slice(0, 5);
                  form.setFieldValue("residentialPostcode", onlyDigits);
                }}
              />
            )}
          </Field>
          <Field name="email">
            {({ field, meta, form }: FieldProps) => (
              <Input
                required
                {...field}
                disabled={form.isSubmitting}
                error={!!meta.error}
                helperText={meta.error}
                label={t("emailWithoutDash")}
                type="email"
              />
            )}
          </Field>
          <Field name="phoneNumber">
            {({ field, meta, form }: FieldProps) => (
              <Input
                {...field}
                required
                error={!!meta.error}
                helperText={meta.error}
                disabled={form.isSubmitting}
                label={t("phoneNumber")}
                // inputMode={"numeric"}
                onInput={(e) => {
                  const input = e.target as HTMLInputElement;
                  const type = Number(values?.identificationType);
                  let value = input.value.replace(/\D/g, "");

                  if (type === 1) {
                    // Remove any existing +60 or 60 prefix
                    value = value.replace(/^\+?60/, "");
                    value = value.slice(0, 10);
                  } else {
                    value = value.slice(0, 12);
                  }

                  input.value = value;
                }}
                inputProps={{
                  inputMode: "numeric",
                }}
                onChange={(e) => {
                  const type = Number(values?.identificationType);
                  const input = e.target as HTMLInputElement;
                  let raw = input.value.replace(/[^\d]/g, "");
                  if (type === 1) {
                    // Allow empty input
                    if (!raw) {
                      form.setFieldValue("phoneNumber", null);
                      return;
                    }

                    // Remove leading 60 if present
                    raw = raw.replace(/^\+?60/, "");

                    const limitedDigits = raw.slice(0, 10);
                    const formatted = "+60" + limitedDigits;

                    let error = "";
                    if (limitedDigits.length < 8) {
                      error = t("phoneDigitLimitWarning");
                    }
                    form.setFieldValue("phoneNumber", formatted);

                    setFieldError("phoneNumber", error);
                  } else {
                    form.setFieldValue("phoneNumber", raw);
                  }
                }}
                onKeyDown={(e) => {
                  const type = Number(values?.identificationType);
                  if (type === 1) {
                    const input = e.target as HTMLInputElement;
                    const pos = input.selectionStart ?? 0;
                    const hasValue = input.value.length > 0;

                    // restrictions
                    if (hasValue) {
                      if (
                        (e.key.length === 1 && pos < 3) || // typing characters in +60
                        (e.key === "Backspace" && pos <= 3) || // backspacing into +60
                        (e.key === "Delete" && pos < 3) // deleting inside +60
                      ) {
                        e.preventDefault();
                      }
                    }
                  }
                }}
                onClick={(e) => {
                  const type = Number(values?.identificationType);
                  if (type === 1) {
                    const input = e.target as HTMLInputElement;
                    if (
                      input.value &&
                      input.selectionStart !== null &&
                      input.selectionStart < 3
                    ) {
                      // Move cursor to after +60 if user clicks inside prefix
                      setTimeout(() => {
                        input.setSelectionRange(3, 3);
                      }, 0);
                    }
                  }
                }}
                onFocus={(e) => {
                  const type = Number(values?.identificationType);
                  if (type === 1) {
                    const input = e.target as HTMLInputElement;
                    if (
                      input.value &&
                      input.selectionStart !== null &&
                      input.selectionStart < 3
                    ) {
                      // move cursor to after +60 on focus
                      setTimeout(() => {
                        input.setSelectionRange(3, 3);
                      }, 0);
                    }
                  }
                }}
                placeholder={
                  Number(values?.identificationType) === 1 ? "+60" : ""
                }
              />
            )}
          </Field>
          <Field name="telephoneNumber">
            {({ field, meta, form }: FieldProps) => (
              <Input
                {...field}
                error={!!meta.error}
                helperText={meta.error}
                label={capitalizeWords(t("nomborTelefonRumah"))}
                inputMode={"numeric"}
                type="number"
                disabled={form.isSubmitting}
                onInput={(e) => {
                  const input = e.target as HTMLInputElement;
                  input.value = input.value.replace(/\D/g, "").slice(0, 12);
                }}
              />
            )}
          </Field>
          <Field name="noTelP">
            {({ field, meta, form }: FieldProps) => (
              <Input
                {...field}
                error={!!meta.error}
                helperText={meta.error}
                label={capitalizeWords(t("nomborTelefonPejabat"))}
                inputMode={"numeric"}
                type="number"
                disabled={form.isSubmitting}
                onInput={(e) => {
                  const input = e.target as HTMLInputElement;
                  input.value = input.value.replace(/\D/g, "").slice(0, 12);
                }}
              />
            )}
          </Field>
        </Box>
      </Box>
      {jobCode !== "Tidak Bekerja" && jobCode !== "Pesara" && (
        <Box
          sx={{
            mb: 3,
            p: 3,
            borderRadius: "16px",
            border: "1px solid #D9D9D9",
          }}
        >
          <Typography sx={{ mb: 4 }} className="title">
            {t("employerInfo")}
          </Typography>

          <Box>
            <Field name="employerName">
              {({ field, meta, form }: FieldProps) => (
                <Input
                  {...field}
                  error={!!meta.error}
                  helperText={meta.error}
                  label={t("employerName")}
                  disabled={form.isSubmitting}
                />
              )}
            </Field>
            <Field name="employerAddressStatus">
              {({ field, meta, form }: FieldProps) => (
                <Input
                  {...field}
                  error={!!meta.error}
                  helperText={meta.error}
                  label={t("employerAddress")}
                  type="select"
                  disabled={form.isSubmitting}
                  options={[
                    {
                      value: "dalam",
                      label: "Dalam Negara",
                    },
                    {
                      value: "luar",
                      label: "Luar Negara",
                    },
                  ]}
                />
              )}
            </Field>
            <Field name="employerAddress">
              {({ field, meta, form }: FieldProps) => (
                <Input
                  {...field}
                  isLabel={false}
                  error={!!meta.error}
                  helperText={meta.error}
                  multiline
                  rows={3}
                  disabled={form.isSubmitting}
                />
              )}
            </Field>
            {employerAddressStatus !== "dalam" && (
              <Field name="employerCountryCode">
                {({ field, meta, form }: FieldProps) => (
                  <Input
                    {...field}
                    error={!!meta.error}
                    helperText={meta.error}
                    label={t("country")}
                    type="select"
                    disabled={form.isSubmitting}
                    options={addressData
                      ?.filter((item) => item.pid === 0)
                      .map((item) => ({
                        label: capitalizeWords(item.name),
                        value: item.id,
                      }))}
                  />
                )}
              </Field>
            )}
            {employerAddressStatus === "dalam" && (
              <>
                <Field name="employerStateCode">
                  {({ field, meta, form }: FieldProps) => (
                    <Input
                      {...field}
                      disabled={form.isSubmitting}
                      error={!!meta.error}
                      helperText={meta.error}
                      label={t("state")}
                      type="select"
                      fullWidth
                      options={
                        addressData
                          ?.filter((item) => item.pid == MALAYSIA)
                          .map((item) => ({
                            label: capitalizeWords(item.name, null, true),
                            value: `${item.id}`,
                          })) ?? []
                      }
                    />
                  )}
                </Field>
                <Field name="employerDistrict">
                  {({ field, meta, form }: FieldProps) => (
                    <Input
                      {...field}
                      error={!!meta.error}
                      helperText={meta.error}
                      label={t("district")}
                      disabled={form.isSubmitting}
                      type="select"
                      fullWidth
                      options={addressData
                        ?.filter(
                          (item) =>
                            // @ts-expect-error
                            item.pid == employerStateCode
                        )
                        .map((item) => ({
                          label: capitalizeWords(item.name, null, true),
                          value: `${item.id}`,
                        }))}
                    />
                  )}
                </Field>
                <Field name="employerCity">
                  {({ field, meta, form }: FieldProps) => (
                    <Input
                      {...field}
                      error={!!meta.error}
                      helperText={meta.error}
                      label={t("city")}
                      disabled={form.isSubmitting}
                    />
                  )}
                </Field>
                <Field name="employerPostcode">
                  {({ field, meta, form }: FieldProps) => (
                    <Input
                      {...field}
                      error={!!meta.error}
                      helperText={meta.error}
                      label={t("postcode")}
                      disabled={form.isSubmitting}
                      type="number"
                    />
                  )}
                </Field>
              </>
            )}
          </Box>
        </Box>
      )}
      <FileUploader
        key={values?.nationalityStatus}
        title="ajkEligibilityCheck"
        type={
          Number(values?.nationalityStatus) === 1
            ? DocumentUploadType.CITIZEN_COMMITTEE
            : DocumentUploadType.NON_CITIZEN_COMMITTEE
        }
        uploadAfterSubmitIndicator={ajkId}
        uploadAfterSubmit={committeeId ? false : true}
        validTypes={[
          "application/pdf",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/msword",
          "text/plain",
        ]}
        societyId={ajkId || committeeId ? parseInt(id) : undefined}
        icNo={identificationNo}
        societyCommitteeId={
          ajkId
            ? Number(ajkId)
            : Number(committeeId)
            ? Number(committeeId)
            : undefined
        }
      />
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          mt: 2,
          gap: 2,
        }}
      >
        <ButtonOutline onClick={back}>{t("back")}</ButtonOutline>
        <ButtonFormSubmitFormik
          // disabled={true}
          disabled={
            JPNError ||
            ((Number(values?.identificationType) === 1 ||
              Number(values?.identificationType) === 4) &&
              (values?.identificationNo?.length !== 12 ||
                userICCorrect === false ||
                userNameMatchIC === false ||
                !userExists))
          }
          label={t("update")}
          manualValidation={true}
          onClick={handleSubmit}
        />
      </Box>
    </>
  );
};
