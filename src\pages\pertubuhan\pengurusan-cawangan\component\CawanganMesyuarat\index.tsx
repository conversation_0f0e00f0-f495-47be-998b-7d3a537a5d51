import React, { useEffect, useState } from "react";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Typography, Button, Divider } from "@mui/material";
import { ButtonPrimary } from "@/components/button";
import { EyeIcon } from "@/components/icons";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import {
  ApplicationStatusEnum,
  COMMITTEE_TASK_TYPE,
  MeetingTypeOption,
} from "@/helpers/enums";
import ALiranTugas from "../../AliranTugas";
import { useBranchContext } from "@/pages/pertubuhan/BranchProvider";
import {
  formatDate,
  formatDateToDDMMYYYY,
  useDataGrid,
  useQuery,
} from "@/helpers";
import { DataGridUI } from "@/components/datagrid/UI";
import { GridColDef } from "@mui/x-data-grid";
import { DataTable, IColumn } from "@/components";
import FilterBar from "@/components/filter";
import { FieldValues, useForm } from "react-hook-form";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const CawanganMesyuarat: React.FC = () => {
  const { t } = useTranslation();
  const { id: societyId } = useParams();
  const navigate = useNavigate();

  const {
    fetchAliranTugasAccessHandle,
    isAliranModuleAccess,
    branchId,
    isBlackListed,
    isAuthorized,
    branchData,
  } = useBranchContext();

  const [filterStatus, setFilterStatus] = useState<string | number>("");
  const [filterYear, setFilterYear] = useState<string | number>("");

  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string>
  >({
    jenisMesyuarat: "jenisMesyuarat",
    status: "status",
    date: "date",
  });

  const filterOptions = {
    jenisMesyuarat: MeetingTypeOption,
    date: Array.from({ length: 50 }, (_, i) => ({
      label: `${new Date().getFullYear() - i}`,
      value: `${new Date().getFullYear() - i}`,
    })),
  };

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 5,
      meetingTypeString: undefined,
      meetingTypeOrder: null,
    },
  });

  const handlePageSizeChange = (newPageSize: number) => {
    setValue("page", 1);
    setValue("pageSize", newPageSize);
  };

  // const handleApplyFilter = () => {
  //   handleCloseFilterModal();
  // };

  const columns: IColumn[] = [
    {
      field: "meetingPurpose",
      headerName: t("meetingName"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: ({ row }) => row?.meetingPurpose ?? "-",
    },
    {
      field: "meetingType",
      headerName: t("meetingType"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (params: any) => {
        return params?.row?.meetingType
          ? getMeetingLabel(params?.row?.meetingType)
          : "-";
      },
    },
    {
      field: "meetingDate",
      headerName: t("meetingDate"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (params: any) => formatDateToDDMMYYYY(params.row.meetingDate),
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (params: any) => {
        const isActive =
          params.row.status === "11" || params.row.status === "001";
        const isInactive = params.row.status === "008";

        if (!isActive && !isInactive) return "";

        return (
          <Typography
            sx={{
              backgroundColor: "#fff",
              border: `2px solid ${
                isActive ? "var(--success)" : "var(--error)"
              }`,
              textAlign: "center",
              padding: "4px 8px",
              borderRadius: "20px",
              fontSize: "14px",
              color: "#666666",
              fontWeight: "normal",
            }}
          >
            {isActive ? "Selesai" : "Inaktif"}
          </Typography>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      headerAlign: "right",
      align: "right",
      renderCell: (params: any) => {
        const row = params.row;
        const canDeleteAndEdit: boolean = row.status === 11;
        return (
          <Link to={`${row.id}`}>
            <EyeIcon />
          </Link>
        );
      },
    },
  ];

  const { data, isLoading, refetch } = useQuery({
    url: `society/meeting/search`,
    filters: [
      ...(watch("meetingTypeString")
        ? [
            {
              field: "meetingTypeString",
              operator: "eq" as const,
              value: watch("meetingTypeString"),
            },
          ]
        : []),
      ...(filterYear
        ? [
            {
              field: "meetingYear",
              operator: "eq" as const,
              value: filterYear,
            },
          ]
        : []),
      ...(filterStatus
        ? [
            {
              field: "status",
              operator: "eq" as const,
              value: filterStatus,
            },
          ]
        : []),
      {
        field: "meetingType",
        operator: "eq",
        value: watch("meetingTypeOrder"),
      },
      {
        field: "societyId",
        operator: "eq",
        value: societyId,
      },
      {
        field: "branchId",
        operator: "eq",
        value: branchId,
      },
      {
        field: "pageSize",
        value: watch("pageSize"),
        operator: "eq",
      },
      {
        field: "pageNo",
        value: watch("page"),
        operator: "eq",
      },
    ],
  });

  const onFilterChange = (filter: string, value: string | number) => {
    setValue("page", 1);
    switch (filter.replace(/\s+/g, "")) {
      case "jenisMesyuarat":
        setValue("meetingTypeOrder", value);
        break;
      case "status":
        setFilterStatus(value);
        break;
      case "date":
        setFilterYear(value);
        break;
    }
  };

  const handleSelectedFiltersChange = (
    updatedFilters: Record<string, string>
  ) => {
    setSelectedFilters(updatedFilters);
  };

  const getMeetingLabel = (value: number): string => {
    const meeting = MeetingTypeOption.find(
      (option) => Number(option.value) === Number(value)
    );
    return meeting ? meeting.label : "Unknown Meeting Type";
  };

  useEffect(() => {
    refetch();
  }, []);

  useEffect(() => {
    refetch();
  }, [
    watch("meetingTypeString"),
    watch("meetingTypeOrder"),
    watch("pageSize"),
    watch("page"),
    filterStatus,
    filterYear,
  ]);

  const handleDaftarMesyuarat = () => {
    navigate("create");
  };

  const listData = data?.data?.data?.data || [];
  const totalList = data?.data?.data?.total ?? 0;

  const location = useLocation();
  const disabled = location.state?.disabled ?? false;

  useEffect(() => {
    fetchAliranTugasAccessHandle(COMMITTEE_TASK_TYPE.PENGURUSAN_MESYUARAT);
  }, []);

  const isPendingStatus =
    Number(branchData?.applicationStatusCode) === 2 &&
    branchData?.status === "008";

  const isAccessible =
    !isBlackListed &&
    (isAuthorized || isAliranModuleAccess) &&
    !isPendingStatus;

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <FilterBar
          filterOptions={filterOptions}
          onFilterChange={onFilterChange}
          selectedFilters={selectedFilters}
          onSelectedFiltersChange={handleSelectedFiltersChange}
        />

        <DataTable
          columns={columns}
          rows={listData}
          page={watch("page")}
          rowsPerPage={watch("pageSize")}
          totalCount={totalList}
          onPageChange={(newPage) => setValue("page", newPage)}
          onPageSizeChange={(newPageSize) => handlePageSizeChange(newPageSize)}
          isLoading={isLoading}
        />
      </Box>
      {isAccessible && (
        <>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("organizationMeeting")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{ color: "#666666", fontSize: 14 }}
                >
                  {t("tambahMesyuarat")}
                </Typography>

                <ButtonPrimary
                  disabled={disabled}
                  onClick={handleDaftarMesyuarat}
                >
                  {t("add")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>

          {isAccessible && isAuthorized ? (
            <ALiranTugas module={COMMITTEE_TASK_TYPE.PENGURUSAN_MESYUARAT} />
          ) : null}
        </>
      )}
    </>
  );
};

export default CawanganMesyuarat;
