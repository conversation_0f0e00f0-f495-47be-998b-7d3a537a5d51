import { Route, Outlet } from "react-router-dom";
import { BlogPostList, BlogPostCreate, BlogPostEdit, BlogPostShow } from "../pages/blog-posts";
import { registerRoutes } from "../helpers/routeDetector";
import { RouteGuard } from "../components/RouteGuard";

// Layout component to wrap all blog post routes with protection
const BlogPostLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Outlet />
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example: '/blog-posts': 'internal',
  // Example: '/blog-posts/create': 'internal',
  // Add your route registrations here
});

export const blogPosts = {
  resources: [
    {
      name: "blog_posts",
      list: "/blog-posts",
      create: "/blog-posts/create",
      edit: "/blog-posts/edit/:id",
      show: "/blog-posts/show/:id",
      meta: {
        canDelete: true,
      },
    },
  ],
  routes: (
    <Route path="/blog-posts" element={<BlogPostLayout />}>
      <Route index element={<BlogPostList />} />
      <Route path="create" element={<BlogPostCreate />} />
      <Route path="edit/:id" element={<BlogPostEdit />} />
      <Route path="show/:id" element={<BlogPostShow />} />
    </Route>
  ),
};
