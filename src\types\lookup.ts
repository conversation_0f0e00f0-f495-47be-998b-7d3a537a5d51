export interface ILookupList {
  id: string | number;
  name: string;
  code: string;
  grade: string;
  description: string;
  shortCode: string;
  status: boolean;
  createdBy: string;
  createdDate: string;
  modifiedBy: any;
  modifiedDate: string;
}

export interface ILookupDetail {
  id: string | number;
  code: string;
  name: string;
  description: string;
  address: string;
  cityCode: string;
  districtCode: string;
  stateCode: string;
  postcode: number;
  phoneNumber: string;
  faxNumber: string;
  emailAddress: string;
  status: number;
  createdBy: string;
  createdDate: string;
  modifiedBy: string;
  modifiedDate: string;
}

export interface ILookupEmail {
  recipientEmailAddress: string;
  notificationTrackerId: string | number;
  societyId: string | number;
  branchId: string;
  notificationType: number;
  subject: string;
  content: string;
  tags: string;
  recipientIdentificationNo: string;
  recipientUsername: string;
  recipientUserGroup: number;
  seen: boolean;
  seenAt: string;
  resendStatus: boolean;
  createdDate: string;
}

export interface ILookupCalendar {
  id: string | number;
  holidayName: string;
  startDate: string;
  endDate: string;
  description: string;
  stateIds: string[] | number[];
  status: boolean;
  createdBy: string;
  createdDate: string;
  modifiedBy: string;
  modifiedDate: string;
}
