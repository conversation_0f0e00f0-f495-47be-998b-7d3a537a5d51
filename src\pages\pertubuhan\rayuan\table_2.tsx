import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  TextField,
  InputAdornment,
  IconButton,
  SvgIcon,
  Grid,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import {
  ApplicationStatus<PERSON>num,
  ApplicationStatusList,
  SebabRyuanEnum,
  SocietyStatusList,
} from "../../../helpers/enums";
import { useTranslation } from "react-i18next";
import { useQuery } from "@/helpers";
import { FieldValues, useForm } from "react-hook-form";
import SearchIcon from "@mui/icons-material/Search";
import FilterBar from "@/components/filter";
import { DataTable } from "@/components";
import { EyeIcon } from "@/components/icons";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../api";
import DialogConfirmation from "@/components/dialog/confirm/dialog-confirmation";

const EditIcon = (props: any) => (
  <SvgIcon {...props}>
    <path
      d="M17,20H1c-0.6,0-1-0.4-1-1V3c0-0.6,0.4-1,1-1h9v2H2v14h14v-8h2v9C18,19.6,17.6,20,17,20z"
      fill="#147C7C"
    />
    <path
      d="M9.3,10.7c-0.4-0.4-0.4-1,0-1.4l9-9c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-9,9C10.3,11.1,9.7,11.1,9.3,10.7z"
      fill="#147C7C"
    />
  </SvgIcon>
);

const TrashIcon = (props: any) => (
  <SvgIcon {...props} viewBox="0 0 12 12">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.5261 2.46154L9.96071 10.8412C9.93964 11.1547 9.78193 11.4493 9.51988 11.6647C9.25782 11.8801 8.91124 12.0001 8.55106 12H3.44894C3.08876 12.0001 2.74218 11.8801 2.48012 11.6647C2.21807 11.4493 2.06036 11.1547 2.03929 10.8412L1.47529 2.46154H0V1.84615C0 1.76455 0.0371848 1.68629 0.103374 1.62858C0.169563 1.57088 0.259335 1.53846 0.352941 1.53846H11.6471C11.7407 1.53846 11.8304 1.57088 11.8966 1.62858C11.9628 1.68629 12 1.76455 12 1.84615V2.46154H10.5261ZM4.58824 0H7.41177C7.50537 0 7.59514 0.0324176 7.66133 0.0901211C7.72752 0.147825 7.76471 0.226087 7.76471 0.307692V0.923077H4.23529V0.307692C4.23529 0.226087 4.27248 0.147825 4.33867 0.0901211C4.40486 0.0324176 4.49463 0 4.58824 0ZM3.88235 4L4.23529 9.53846H5.29412L5.01177 4H3.88235ZM7.05882 4L6.70588 9.53846H7.76471L8.11765 4H7.05882Z"
      fill="#FF0000"
    />
  </SvgIcon>
);

interface RayuanTable2Props {
  refreshKey: number;
}

const RayuanTable2: React.FC<RayuanTable2Props> = ({ refreshKey }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [searchQuery, setSearchQuery] = useState("");
  const [applicationStatusCodeQuery, setApplicationStatusCodeQuery] = useState<
    string | number
  >("");
  const [statusCodeQuery, setStatusCodeQuery] = useState<string | number>("");
  const [registeredYearQuery, setRegisteredYearQuery] = useState<
    string | number
  >("");
  const [appealTypeQuery, setAppealTypeQuery] = useState<string | number>("");

  const [searchAppeal, setSearchAppeal] = useState<any>("");
  const [openConfirmDialog, setOpenConfirmDialog] = useState<boolean>(false);
  const [appealIdToDelete, setAppealIdToDelete] = useState<
    string | number | null
  >(null);

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 3,
      searchQuery: undefined,
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");
  const {
    data: appealListDataResponse,
    isLoading: isLoadingAppealListData,
    refetch,
  } = useQuery({
    url: "society/appeal/getByParam",
    filters: [
      {
        field: "pageSize",
        value: pageSize,
        operator: "eq",
      },
      {
        field: "pageNo",
        value: page,
        operator: "eq",
      },
      {
        field: "searchQuery",
        value: searchQuery,
        operator: "eq",
      },
      {
        field: "appealApplicationStatus",
        value: applicationStatusCodeQuery,
        operator: "eq",
      },
      {
        field: "societyApplicationStatus",
        value: statusCodeQuery,
        operator: "eq",
      },
      {
        field: "appealDate",
        value: registeredYearQuery,
        operator: "eq",
      },
      {
        field: "idSebab",
        value: appealTypeQuery,
        operator: "eq",
      },
    ],
  });

  useEffect(() => {
    console.log("refreshKey", refreshKey);
    refetch();
  }, [refreshKey]);

  const totalList = appealListDataResponse?.data?.data?.total ?? 0;
  const rowData = appealListDataResponse?.data?.data?.data ?? [];

  const { mutate: deleteAppeal, isLoading: isLoadingDeleteAppeal } =
    useCustomMutation();

  const handleConfirmDelete = (appealId: string | number) => {
    setAppealIdToDelete(appealId);
    setOpenConfirmDialog(true);
  };

  const handleDeleteAppeal = () => {
    if (appealIdToDelete) {
      deleteAppeal(
        {
          url: `${API_URL}/society/appeal/removeAppealApplication?id=${appealIdToDelete}`,
          method: "put",
          values: {},
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
        },
        {
          onSuccess: () => {
            refetch();
            setOpenConfirmDialog(false);
            setAppealIdToDelete(null);
          },
          onError: () => {
            setOpenConfirmDialog(false);
            setAppealIdToDelete(null);
          },
        }
      );
    }
  };

  const handleEdit = async (societyId: any, appealId: any) => {
    const queryParams = new URLSearchParams({
      appealId: appealId || "",
      societyId: societyId,
    }).toString();
    navigate(`/pertubuhan/paparan-pertubuhan/rayuan/add-rayuan?${queryParams}`);
  };

  const handleView = async (societyId: any, appealId: any) => {
    const queryParams = new URLSearchParams({
      appealId: appealId || "",
      societyId: societyId,
    }).toString();
    navigate(`/pertubuhan/paparan-pertubuhan/rayuan/view?${queryParams}`);
  };

  const filterOptions = {
    pertubuhan: SocietyStatusList,
    jenisRayuan: Object.entries(SebabRyuanEnum).map(([key, value]) => ({
      value: Number(key),
      label: value,
    })),
    tarikhMohon: Array.from({ length: 50 }, (_, i) => ({
      label: `${new Date().getFullYear() - i}`,
      value: `${new Date().getFullYear() - i}`,
    })),
    permohonan: ApplicationStatusList.map(({ id, value }) => ({
      value: id,
      label: value,
    })),
  };

  const onSearchKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      const value = (event.target as HTMLInputElement).value;
      setValue("page", 1);
      setSearchQuery(value);
    }
  };

  const columns = [
    {
      field: "societyName",
      headerName: t("organizationName"),
      flex: 1,
      align: "left",
      headerAlign: "left",
    },
    {
      field: "branchName",
      headerName: t("branchNameDetails"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return row?.branchName ? (
          <Typography sx={{ fontSize: "14px" }}>{row?.branchName}</Typography>
        ) : (
          "-"
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "idSebab",
      headerName: t("jenisRayuan"),
      width: 300,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return row?.idSebab ? (
          <Typography sx={{ fontSize: "14px" }}>
            {t(
              SebabRyuanEnum[
                (row?.idSebab as keyof typeof SebabRyuanEnum) || "0"
              ]
            )}
          </Typography>
        ) : (
          "-"
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "appealDate",
      headerName: t("tarikhPermohonan"),
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "societyApplicationStatusCode",
      headerName: t("organizationStatus"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        if (row?.branchApplicationStatusCode) {
          return t(
            ApplicationStatusEnum[
              (row?.branchApplicationStatusCode as keyof typeof ApplicationStatusEnum) ||
                "0"
            ]
          );
        } else {
          return t(
            ApplicationStatusEnum[
              (row?.societyApplicationStatusCode as keyof typeof ApplicationStatusEnum) ||
                "0"
            ]
          );
        }
      },
      cellClassName: "custom-cell",
    },
    {
      field: "appealApplicationStatusCode",
      headerName: t("statusPermohonan"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;

        return row?.appealApplicationStatusCode ? (
          <Typography sx={{ fontSize: "14px" }}>
            {t(
              ApplicationStatusList.find(
                (item) => item.id === row?.appealApplicationStatusCode
              )?.value || "-"
            )}
          </Typography>
        ) : (
          "-"
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      width: 100,
      sortable: false,
      disableColumnMenu: true,
      align: "right",
      headerAlign: "right",
      renderCell: (params: any) => {
        const row = params?.row;
        const status = ApplicationStatusList.find(
          (item) => item.id === row?.appealApplicationStatusCode
        );

        switch (status?.value) {
          case "BELUM_DIHANTAR":
          case "MENUNGGU_BAYARAN_KAUNTER":
          case "MENUNGGU_BAYARAN_ONLINE":
          case "BAYARAN_GAGAL":
            return (
              <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}>
                <IconButton
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignContent: "center",
                    aspectRatio: "1/1",
                  }}
                  onClick={() => handleEdit(row?.societyId, row?.id || "")}
                >
                  <EditIcon
                    sx={{
                      fontSize: "1rem",
                      width: "1rem",
                      height: "1rem",
                    }}
                  />
                </IconButton>
                <IconButton
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    aspectRatio: "1/1",
                  }}
                  onClick={() => handleConfirmDelete(row.id)}
                >
                  <TrashIcon
                    sx={{
                      fontSize: "0.9rem",
                      width: "0.9rem",
                      height: "0.9rem",
                    }}
                  />
                </IconButton>
              </Box>
            );
          case "LULUS":
          case "TOLAK":
          case "LULUS_BERSYARAT":
          case "MENUNGGU_KEPUTUSAN_MENTERI":
          case "MENUNGGU_KEPUTUSAN":
            return (
              <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}>
                <IconButton
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignContent: "center",
                    aspectRatio: "1/1",
                  }}
                  onClick={() => handleView(row?.societyId, row?.id || "")}
                >
                  <EyeIcon
                    sx={{
                      fontSize: "1rem",
                      width: "1rem",
                      height: "1rem",
                    }}
                  />
                </IconButton>
              </Box>
            );
          case "KUIRI":
            return (
              <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}>
                <IconButton
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignContent: "center",
                    aspectRatio: "1/1",
                  }}
                  onClick={() => handleEdit(row?.societyId, row?.id || "")}
                >
                  <EditIcon
                    sx={{
                      fontSize: "1rem",
                      width: "1rem",
                      height: "1rem",
                    }}
                  />
                </IconButton>
              </Box>
            );
        }
      },
    },
  ];

  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string>
  >({
    pertubuhan: "pertubuhan",
    permohonan: "permohonan",
    jenisRayuan: "jenisRayuan",
    tarikhMohon: "tarikhMohon",
  });

  const onFilterChange = (filter: string, value: string | number) => {
    setValue("page", 1);
    switch (filter) {
      case "jenisRayuan":
        setAppealTypeQuery(value);
        break;
      case "tarikhMohon":
        setRegisteredYearQuery(value);
        break;
      case "permohonan":
        setApplicationStatusCodeQuery(value);
        break;
      case "pertubuhan":
        setStatusCodeQuery(value);
        break;
    }
  };

  const handleSelectedFiltersChange = (
    updatedFilters: Record<string, string>
  ) => {
    setSelectedFilters(updatedFilters);
  };

  return (
    <Box
      sx={{
        border: "1px solid #DADADA",
        borderRadius: "10px",
        p: { xs: 1, sm: 2, md: 3 },
      }}
    >
      <Typography variant="h6" className="title" sx={{ mb: 4 }}>
        {t("organisationAppeal")}
      </Typography>
      <TextField
        fullWidth
        variant="outlined"
        placeholder={t("namaPertubuhan")}
        sx={{
          display: "block",
          boxSizing: "border-box",
          maxWidth: 570,
          marginInline: "auto",
          height: "40px",
          background: "var(--border-grey)",
          opacity: 0.5,
          border: "1px solid var(--text-grey)",
          borderRadius: "10px",
          "& .MuiOutlinedInput-root": {
            height: "40px",
            "& fieldset": {
              border: "none",
            },
          },
        }}
        onKeyDown={onSearchKeyDown}
        onChange={(e) => setSearchAppeal(e.target.value)}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon
                sx={{
                  color: "var(--text-grey-disabled)",
                  marginLeft: "8px",
                }}
              />
            </InputAdornment>
          ),
        }}
      />
      <FilterBar
        filterOptions={filterOptions}
        onFilterChange={onFilterChange}
        selectedFilters={selectedFilters}
        onSelectedFiltersChange={handleSelectedFiltersChange}
      />
      <Box sx={{ display: "flex", justifyContent: "center" }}></Box>

      <DataTable
        columns={columns as any}
        rows={rowData}
        page={page}
        rowsPerPage={pageSize}
        totalCount={totalList}
        onPageChange={(newPage) => setValue("page", newPage)}
        onPageSizeChange={(newPageSize) => {
          setValue("page", 1);
          setValue("pageSize", newPageSize);
        }}
        isLoading={isLoadingAppealListData}
      />

      <DialogConfirmation
        open={openConfirmDialog}
        onClose={() => {
          setOpenConfirmDialog(false);
          setAppealIdToDelete(null);
        }}
        onAction={handleDeleteAppeal}
        isMutating={isLoadingDeleteAppeal}
        onConfirmationText={t("confirmDeleteApplication")}
      />
    </Box>
  );
};

export default RayuanTable2;
