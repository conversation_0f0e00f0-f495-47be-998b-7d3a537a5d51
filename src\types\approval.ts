export interface IFeedback {
  name: string;
  identificationNo: string;
  id: string | number;
  icNo: string;
  feedback: string;
  nonCommittee: boolean | null;
  falseStatement: boolean | null;
  other: number;
  otherReason: any;
  positionCode: number;
  position: string;
}

export interface IReview {
  id: string | number;
  decision: string;
  applicationStatusCode: number;
  decisionDate: string | null;
  note: string;
  rejectReason: string | null;
  approvedBy: string;
  isPPP: boolean;
}

export type IQuery = Pick<IReview, 'id' | 'note'> & {
  date: string;
};
