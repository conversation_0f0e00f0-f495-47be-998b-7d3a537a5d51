import React, { useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Checkbox from "@mui/material/Checkbox";
import type { SelectChangeEvent } from "@mui/material/Select";
import InputLabel from "@mui/material/InputLabel";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { Select, Option } from "../../../../components/input";
import { OrganizationStepper } from "../organization-stepper";
import { Fade, Grid } from "@mui/material";
import { useNavigate, useSearchParams } from "react-router-dom";
import { ApplicationStatus, PaymentItemCode } from "../../../../helpers/enums";
import InfoQACard from "../InfoQACard";
import { DialogConfirmation } from "@/components";
import { useMutation, useQuery } from "@/helpers";
import usePaymentService from "@/helpers/hooks/usePaymentService";
import { useDispatch } from "react-redux";
import { setCalculatedPayment } from "@/redux/paymentReducer";

export const ListBayaran = () => {
  const [paymentMethod, setPaymentMethod] = useState("");
  const { t } = useTranslation();
  const [dialogOpen, setDialogOpen] = useState(false);
  const navigate = useNavigate();
  const [isChecked, setIsChecked] = useState(false);
  const [params] = useSearchParams();
  const dispatch = useDispatch();
  const { calculatePayment, processPayment, loading: paymentLoading } = usePaymentService();

  const encodedId = params.get("id");
  const decodedId = atob(encodedId || "");

  const activeStep = 5;

  const handleChange = (event: SelectChangeEvent) => {
    setPaymentMethod(event.target.value as string);
  };

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSuccessEditSocietyPayment(false);
  };

  const [successEditSocietyPayment, setSuccessEditSocietyPayment] = useState(false)
  const {
    fetchAsync: editSocietyPaymentMethodTwo,
    isLoading: isLoadingEditSocietyPaymentMethodTwo
  } = useMutation({
    method: "put",
    msgSuccess: "Permohonan berjaya dibayar.",
    onSuccessNotification: (data) => {
      if (data?.data?.code === 200) {
        return {
          message: data?.data?.msg,
          type: "success",
        };
      }
      return {
        message: data?.data?.msg,
        type: "error",
      };
    },
    async onSuccess(data) {
      if (data?.data?.code === 200) {
        // Call calculate API first
        const itemCode = paymentMethod === "kaunter"
          ? PaymentItemCode.PENDAFTARAN_PERTUBUHAN_BARU_KAUNTER
          : PaymentItemCode.PENDAFTARAN_PERTUBUHAN_BARU_ONLINE;

        const calculateRequest = {
          items: [
            {
              itemCode: itemCode,
              quantity: 1
            }
          ]
        };

        try {
          const calculateResponse = await calculatePayment(calculateRequest);

          if (calculateResponse?.data) {
            // Store calculated payment in Redux
            dispatch(setCalculatedPayment(calculateResponse.data));

            if (paymentMethod === "kaunter") {
              const paymentData = {
                societyId: parseInt(decodedId),
                amount: calculateResponse.data.totalAmount,
                email: "",
                signature: calculateResponse.data.signature,
              };
              const processPaymentResponse = await processPayment(paymentData);

              if (processPaymentResponse?.data) {
                navigate(`kaunter?id=${encodedId}`);
              }
            } else if (paymentMethod === "online") {
              navigate(`online?id=${encodedId}`);
            }
          }
        } catch (error) {
          console.error("Failed to calculate payment:", error);
          // You might want to show an error notification to the user
          // For now, we'll just log the error and prevent further execution
          throw error;
        }
      }
    },
  });

  const editSociety = async () => {
    setSuccessEditSocietyPayment(false)
    try {
      if (decodedId) {
        const data = {
          applicationStatusCode:
            paymentMethod === "kaunter"
              ? ApplicationStatus.MENUNGGU_BAYARAN_KAUNTER
              : ApplicationStatus.MENUNGGU_BAYARAN_ONLINE,
        };
        await editSocietyPaymentMethodTwo(data, () => `society/${decodedId}/checkAndUpdate`)
      }
    } finally {
      setSuccessEditSocietyPayment(true)
    }
  };

  const { data: paymentStatus } = useQuery({
    url: `society/admin/integration/payment/status`
  });

  const onlinePaymentEnabled: boolean =
    paymentStatus?.data?.data?.payment?.toLowerCase() == "enabled";
  const alert: boolean = paymentStatus?.data?.data?.alert;

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsChecked(event.target.checked);
  };

  const handleSubmit = () => {
    handleOpenDialog();
  };

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box sx={{ width: "55vw" }}>
        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3,
            }}
          >
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: "500 !important",
                  }}
                >
                  {t("payment")}
                </Typography>
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color: "#666666",
                  fontFamily: "Poppins",
                  fontSize: 14,
                  fontWeight: "400 !important",
                  lineHeight: "21px",
                  textAlign: "left",
                  textUnderlinePosition: "from-font",
                  textDecorationSkipInk: "none",
                  mb: 2,
                }}
              >
                {t("agreementText")}
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "flex-start",
                  gap: 1,
                }}
              >
                <Checkbox
                  id="akuan-setuju-terima"
                  checked={isChecked}
                  onChange={handleCheckboxChange}
                  sx={{
                    color: "#00A7A7",
                    "&.Mui-checked": {
                      color: "#00A7A7",
                    },
                    padding: "0",
                  }}
                />
                <InputLabel
                  htmlFor="akuan-setuju-terima"
                  required
                  sx={{
                    color: "#333333",
                    fontSize: 14,
                    fontWeight: 400,
                    lineHeight: 1.4,
                    "& .MuiFormLabel-asterisk": {
                      color: "#FF0000",
                    },
                  }}
                >
                  {t("agreementAcceptance")}
                </InputLabel>
              </Box>
            </Box>

            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 3,
              }}
            >
              <Box sx={{ mb: 2 }}>
                <Box sx={{ mb: 1 }}>
                  <Typography
                    variant="h6"
                    component="h2"
                    sx={{
                      color: "#00A7A7",
                      fontSize: 16,
                      fontWeight: "500 !important",
                    }}
                  >
                    {t("paymentMethod")}
                  </Typography>
                </Box>
                <Grid
                  container
                  rowSpacing={1}
                  columnSpacing={{ xs: 1, sm: 2, md: 3 }}
                >
                  <Grid item xs={4}>
                    {/* <Typography>1</Typography> */}
                  </Grid>
                  <Grid item xs={8}>
                    <Typography
                      sx={{
                        fontFamily: "Poppins, sans-serif",
                        fontSize: "12px",
                        fontWeight: 500,
                        lineHeight: "14px",
                        color: "#FF0000",
                        marginLeft: "15px",
                      }}
                    >
                      {alert}
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <InputLabel
                      htmlFor="cara-pembayaran"
                      required
                      sx={{
                        color: "#333333",
                        fontSize: 14,
                        fontWeight: 400,
                        minWidth: "150px",
                        "& .MuiFormLabel-asterisk": {
                          color: "#FF0000",
                        },
                      }}
                    >
                      {t("paymentMethod")}
                    </InputLabel>
                  </Grid>
                  <Grid item xs={8}>
                    <Select
                      value={paymentMethod}
                      onChange={handleChange}
                      id="cara-pembayaran"
                      t={t}
                      sx={{
                        width: "100%",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#E5E5E5",
                          borderRadius: 1,
                        },
                      }}
                    >
                      <Option value="kaunter">{t("pembayaranKaunter")}</Option>
                      {onlinePaymentEnabled ? (
                        <Option value="online">{t("pembayaranOnline")}</Option>
                      ) : (
                        <Option value="online" disabled>
                          {t("pembayaranOnline")}
                        </Option>
                      )}
                    </Select>
                  </Grid>
                </Grid>
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color: "#666666",
                  fontSize: 12,
                  marginTop: 10,
                  textAlign: "center",
                }}
              >
                {t("paymentNote")}
              </Typography>
            </Box>

            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
                marginTop: 3,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  borderRadius: 1,
                  textTransform: "none",
                }}
                disabled={!isChecked || !paymentMethod}
                onClick={handleSubmit}
              >
                {t("bayar")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>

        <DialogConfirmation
          open={dialogOpen}
          onClose={handleCloseDialog}
          onConfirmationText={t("confirmSubmitApplication")}
          onSuccessText="Permohonan berjaya dibayar."
          isSuccess={successEditSocietyPayment}
          isMutating={isLoadingEditSocietyPaymentMethodTwo || paymentLoading}
          onAction={async () => {
            try {
              await editSociety();
            } finally {
              handleCloseDialog();
            }
          }}
        />
      </Box>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <OrganizationStepper activeStep={activeStep} />
        <InfoQACard />
      </Box>
    </Box>
  );
};

export default ListBayaran;
