import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import { RegExNumbers, RegExText } from "../../../../../helpers/enums";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import { formatAndValidateNumber } from "@/helpers";
import { DialogConfirmation } from "@/components";
interface FasalContentEnamFaedahProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentEnamFaedah: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const [clauseContentDescription, setClauseContentDescription] = useState<string|undefined>("");
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [jumlahWangTangan, setJumlahWangTangan] = useState("");
  const [jumlahWangTanganKata, setJumlahWangTanganKata] = useState("");
  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [dialogSaveOpen, setDialogSaveOpen] = useState(false);
  const [isLoadingUpdate, setIsLoadingUpdate] = useState(false);
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!jumlahWangTangan) {
      errors.jumlahWangTangan = t("fieldRequired");
    }

    if (!jumlahWangTanganKata) {
      errors.jumlahWangTanganKata = t("fieldRequired");
    }

    return errors;
  };

  /*const clauseContent = `
              1. Pengerusi dalam tempoh memegang jawatannya hendaklah mempengerusikan semua Mesyuarat Agung dan Mesyuarat Jawatankuasa serta bertanggungjawab atas kesempurnaan perjalanan mesyuarat. Pengerusi mempunyai undi pemutus dan hendaklah menandatangani minit mesyuarat tersebut.

2. Timbalan Pengerusi hendaklah membantu Pengerusi dalam urusan pentadbiran Pertubuhan dan memangku tugas Pengerusi semasa ketiadaannya.
(Nota: selaraskan dengan pilihan di Fasal 5 Jawatankuasa)
Naib Pengerusi hendaklah memangku jawatan Pengerusi dan Timbalan Pengerusi semasa ketiadaannya. (Jika Ada)

3. Setiausaha hendaklah menjalankan kerja pentadbiran Pertubuhan mengikut Perlembagaan dan menjalankan arahan-arahan Mesyuarat Agung dan Jawatankuasa. Setiausaha bertanggungjawab mengendalikan urusan surat-menyurat dan menyimpan semua rekod serta dokumen Pertubuhan, kecuali buku-buku akaun dan dokumen kewangan. Setiausaha hendaklah menyimpan semua dokumen rasmi Pertubuhan termasuklah notis Mesyuarat Agung dan Mesyuarat Jawatankuasa, notis-notis dari Pendaftar Pertubuhan, sentiasa mengemaskini buku daftar ahli. Setiausaha hendaklah memastikan Buku Daftar Ahli mengandungi butir-butir seperti berikut:

i. bilangan siri;
ii. nama ahli;
iii. tarikh menjadi ahli;
iv. umur/ tarikh lahir;
v. nombor Kad Pengenalan;
vi. alamat;
vii. pekerjaan;
viii. nama-nama tanggungan; dan
ix. nama waris.

4. Penolong Setiausaha hendaklah membantu Setiausaha menjalankan kerja-kerjanya dan memangku jawatan itu semasa ketiadaan Setiausaha.

5. Bendahari hendaklah bertanggungjawab dalam semua hal ehwal kewangan Pertubuhan dan memastikan segala yuran Pertubuhan dikutip, mengemaskini buku yuran dan buku kira-kira wang masuk dan keluar Pertubuhan. Bendahari juga hendaklah bertanggungjawab di atas ketepatan:

a.Penyata kira-kira bulanan; dan

b. Penyata kira-kira tahunan yang diperiksa dan disahkan oleh Juruaudit yang dilantik dalam Mesyuarat Agung. Bendahari tidak dibenarkan menyimpan wang tunai di tangan melebihi RM ${
    jumlahWangTangan || "<<jumlah wang tangan yang dibenarkan>>"
  } (Ringgit Malaysia ${
    jumlahWangTanganKata ||
    "<<jumlah wang tangan yang dibenarkan-dalam perkataan>>"
  }  Sahaja) pada satu-satu masa dan wang yang lebih daripada jumlah itu hendaklah dimasukkan ke akaun bank yang diluluskan oleh Jawatankuasa. Bendahari hendaklah bertanggungjawab berkenaan dengan segala wang yang diterima dan dibayar serta diberi kuasa menandatangan cek-cek bagi pihak Pertubuhan bersama-sama dengan Pengerusi dan Setiausaha. Akaun bank itu hendaklah di atas nama Pertubuhan.
(Nota: selaraskan dengan pilihan di Fasal 5 Jawatankuasa)
Penolong Bendahari hendaklah menolong Bendahari menjalankan kerja-kerjanya dan memangku jawatan itu semasa ketiadaan Bendahari.

6. Ahli Jawatankuasa Biasa hendaklah hadir dalam mesyuarat dan membantu Jawatankuasa dalam menjalankan tugas yang diarahkan kepadanya.
            `;*/

  //const clause6 = localStorage.getItem("clause6");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => { 
    
    if (clause) { 
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause6Data = JSON.parse(clause6);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause3Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause6Data.societyName); 
      if(clause.clauseDescription){
        setClauseContentDescription(clause.clauseDescription);
      }else{
        setClauseContentDescription(undefined);
      }
      setJumlahWangTangan(clause.constitutionValues[0]?.definitionName);
      setJumlahWangTanganKata(clause.constitutionValues[1]?.definitionName);
      setIsEdit(clause.edit);
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id } = useParams();
  let clauseContent = clauseContentDescription ? clauseContentDescription : clause.clauseContent;

  clauseContent = clauseContent.replaceAll(
    /<<jumlah wang tangan yang dibenarkan>>/gi,
    `<b>${jumlahWangTangan || "<<jumlah wang tangan yang dibenarkan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jumlah wang tangan yang dibenarkan-dalam perkataan>>/gi,
    `<b>${
      jumlahWangTanganKata ||
      "<<jumlah wang tangan yang dibenarkan-dalam perkataan>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");
  
  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  const handleConfirm = () => {
    handleSaveContent({
      i18n,
      societyId,
      societyName: namaPertubuhan,
      dataId,
      isEdit,
      clauseNo: clauseNo,
      clauseName: clauseName,
      createClauseContent,
      editClauseContent,
      description: clauseContent,
      constitutionValues: [
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: jumlahWangTangan,
          titleName: "Jumlah Wang Tangan yang Dibenarkan Dalam Tangan",
        },
        {
          constitutionContentId: null,
          societyName: namaPertubuhan,
          definitionName: jumlahWangTanganKata,
          titleName:
            "Jumlah Wang Tangan yang Dibenarkan Dalam Tangan (Tulis Dalam Perkataan)",
        },
      ],
      clause: "clause6",
      clauseCount: 6,
      clauseContentId,
    });
    if (!isEditingContent) {
      setIsLoadingUpdate(false);
      setDialogSaveOpen(false);
    }
  };
  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("jumlahWangTangan")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("jumlahWangTangan")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              value={jumlahWangTangan}
              placeholder="RM 0.00"
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setJumlahWangTangan(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahWangTangan: "",
                  }));
                } else {
                  setJumlahWangTangan("");
                  e.preventDefault();
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahWangTangan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.jumlahWangTangan}
              helperText={formErrors.jumlahWangTangan}
            />
          </Grid>
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              value={jumlahWangTanganKata}
              placeholder="contoh : dua puluh "
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setJumlahWangTanganKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahWangTanganKata: "",
                  }));
                } else {
                  e.preventDefault();
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahWangTanganKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.jumlahWangTanganKata}
              helperText={formErrors.jumlahWangTanganKata}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
                <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            const errors = validateForm();
            if (Object.keys(errors).length > 0) {
              setFormErrors(errors);
              return;
            }

            if (clause.constitutionValues.length > 0) {
              setDialogSaveOpen(true);
            } else {
              handleConfirm();
            }
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
      <DialogConfirmation
        open={dialogSaveOpen}
        // onClose={() => setDialogSaveOpen(false)}
        onConfirmationText={t("AJKNoUpdateConfirmation")}
        onAction={handleConfirm}
        onClose={() => setDialogSaveOpen(false)}
        isMutating={isLoadingUpdate || isEditingContent}
      />
    </>
  );
};

export default FasalContentEnamFaedah;
