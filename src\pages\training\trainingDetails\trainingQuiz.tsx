import React, {useEffect, useState} from "react";
import {Typography, Box, Checkbox} from "@mui/material";
import TrainingBreadcrumb from "@/pages/training/breadcrumb";
import TrainingFragment from "@/pages/training/trainingFragment";
import {TrainingEnums} from "@/helpers";
import {useTranslation} from "react-i18next";
import {ButtonPrimary} from "@/components";
import {useCustom, useCustomMutation} from "@refinedev/core";
import {API_URL} from "@/api";
import {DurationIcon} from "@/components/icons/duration";
import Countdown from "@/pages/training/trainingDetails/countdown";

interface TrainingQuizFragmentProps {
  courseId: number,
  handleFinish: (val: any) => void,
  //quizFinished:boolean
}

interface AnswerProps {
  quizQuestionId: number,
  quizOptionId: number,
  isCorrect: boolean,
}

const TrainingQuiz: React.FC<TrainingQuizFragmentProps> = ({courseId, handleFinish,}) => {

  const {t, i18n} = useTranslation();
  const [step, setStep] = useState(1);
  const [answered, setAnswered] = useState(false);
  const [questions, setQuestions] = useState<any[]>([]);
  const [answers, setAnswers] = useState<AnswerProps[]>([]);
  const [disabled, setDisabled] = useState<boolean>(false);
  const [quizAttemptId, setQuizAttemptId] = useState<number>(0);
  const [timeLeft, setTimeLeft] = useState(-1);

  const {data: trainingQuizData, isLoading: isEnrolledTrainingLoading} = useCustom({
    url: `${API_URL}/society/training/courses/${courseId}/quiz`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const trainingQuiz = trainingQuizData?.data?.data || {};
  console.log("trainingQuiz", trainingQuiz)

  //const preAnswersArray = [1, 2, 3, 4];
  //let questionsArray: any[] = [];
  /*for (let i = 0; i < 10; ++i) {
    const question = ~~(Math.random() * 4);
    questionsArray.push(question);
    temp.push(0)
  }*/

  useEffect(() => {
    if (Object.keys(trainingQuiz).length > 0 && trainingQuiz.questions.length > 0) {
      setQuestions(trainingQuiz.questions);
      const temp = trainingQuiz.questions.map((e: any, i: number) => {
        return {
          quizQuestionId: e.id,
          quizOptionId: 0,
          isCorrect: false,
        }
      })
      setAnswers(temp);
      if(quizAttemptId === 0){
        StartQuiz(trainingQuiz.id);
      }
    }
  }, [trainingQuizData])


  const next = () => {
    if (step < Math.ceil(questions.length / 5)) setStep(step + 1);
    else if (!answered) {
      submit();
    } else if (answered) {
      checkScore();
    }
  }

  const back = () => {
    if (step > 1) setStep(step - 1);
  }

  const submit = () => {
    setDisabled(true);
    setAnswered(true);
    setStep(1);
  }

  const checkScore = () => {
    SubmitQuiz();
  }

  const answerQuestion = (i: number, val: any) => {
    answers[i] = {
      quizQuestionId: val.quizQuestionId,
      quizOptionId: val.id,
      isCorrect: val.isCorrect,
    };
    const temp = answers.slice();
    setAnswers(temp);
  }

  const handleStartQuiz = (quizData: any) => {
    console.log("quizData",quizData);
    setTimeLeft(quizData?.remainingTimeSeconds);
    setQuizAttemptId(quizData?.id);
  }

  const {mutate: startQuiz, isLoading: isLoadingStart} = useCustomMutation();
  const StartQuiz = (id: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    startQuiz(
      {
        url: `${API_URL}/society/training/quiz/attempt`,
        method: "post",
        values: {
          id: id
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            const quizData = data?.data?.data;
            handleStartQuiz(quizData);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const {mutate: submitQuiz, isLoading: isLoadingSubmit} = useCustomMutation();
  const SubmitQuiz = (): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    submitQuiz(
      {
        url: `${API_URL}/society/training/quiz/attempts/submit`,
        method: "put",
        values: {
          quizAttemptId: quizAttemptId,
          answers: answers,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            const submitData = data?.data?.data;
            handleFinish(submitData);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  return (
    <>
      <Box
        sx={{
          //flex: 5,
          width: "100%",
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box
          sx={{
            //height: "100%",
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //flex: 5,
            px: 5,
            py: 2,
            mb: 1,
          }}
        >
          <Box sx={{display: "flex", justifyContent: "space-between", gap: 1}}>
            <Typography
              sx={{
                color: "#0CA6A6",
                //pt: 3,
                fontWeight: "500",
                fontSize: 20,
              }}
            >
              {trainingQuiz.title}
            </Typography>
            {timeLeft > 0 ?
            <Countdown handleFinish={submit} initialTime={timeLeft} /> : <></> }
          </Box>
        </Box>
        <Box
          sx={{
            //height: "100%",
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //flex: 5,
            px: 5,
            py: 2,
            mb: 1,
          }}
        >
          {questions.map((item: any, index: number) => {
            if (index < step * 5 && index >= (step - 1) * 5) {
              console.log(answers[index]);
              return (
                <Box key={index} sx={{display: "flex", justifyContent: "space-between", gap: 1}}>
                  <Box>
                    <Typography
                      sx={{
                        color: "#666666",
                        pt: 3,
                        fontWeight: "400",
                        fontSize: 14,
                      }}
                    >
                      {`Question ${index + 1}`}
                    </Typography>
                    <Typography
                      sx={{
                        color: "#666666",
                        pt: 3,
                        fontWeight: "400",
                        fontSize: 14,
                      }}
                    >
                      {item.questionText}
                    </Typography>
                    <Box sx={{pl: 1}}>
                      {item.options.map((item2: any, index2: number) => {
                        return (<Typography key={index2}
                          sx={{fontWeight: "400", fontSize: 14}}
                        >
                          <Checkbox
                            disabled={disabled}
                            checked={answers[index].quizOptionId === item2.id}
                            onChange={(e) => answerQuestion(index, item2)}
                            sx={{p: 0, ml: 1, mr: 1}}
                          />
                          {`${item2.optionText}`}
                        </Typography>)
                      })}
                    </Box>
                  </Box>
                  {answered ?
                    <Box>
                      <Typography
                        sx={{
                          color: answers[index].isCorrect ? "#008000" : "#FF0000",
                          pt: 3,
                          fontWeight: "500",
                          fontSize: 14,
                        }}
                      >
                        {answers[index].isCorrect ? "BETUL" : "SALAH"}
                      </Typography>
                    </Box> : <></>}
                </Box>)}
            else {
              return <></>
            }
          })}
        </Box>
        <Box sx={{display: "flex", mt: 1, justifyContent: "flex-end", gap: 1}}>
          {step > 1 ?
            <ButtonPrimary
              variant="outlined"
              sx={{
                borderColor: "#0CA6A6",
                bgcolor: "#fff",
                "&:hover": {bgcolor: "#fff", borderColor: "#0CA6A6",},
                color: "#0CA6A6",
                fontWeight: "400",
              }}
              onClick={() => back()}
            >
              {t("back")}
            </ButtonPrimary> : <></>}
          <ButtonPrimary
            variant="outlined"
            sx={{
              borderColor: "#0CA6A6",
              bgcolor: "#0CA6A6",
              "&:hover": {bgcolor: "#0CA6A6", borderColor: "#0CA6A6",},
              color: "#fff",
              fontWeight: "400",
            }}
            onClick={() => next()}
          >
            {t("next")}
          </ButtonPrimary>
        </Box>
      </Box>
    </>
  );
}

export default TrainingQuiz;
