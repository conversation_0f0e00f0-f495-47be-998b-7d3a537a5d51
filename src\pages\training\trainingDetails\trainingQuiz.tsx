import React, {useCallback, useEffect, useState} from "react";
import {Typography, Box, Checkbox} from "@mui/material";
import TrainingBreadcrumb from "@/pages/training/breadcrumb";
import TrainingFragment from "@/pages/training/trainingFragment";
import {TrainingEnums} from "@/helpers";
import {useTranslation} from "react-i18next";
import {ButtonPrimary, DialogConfirmation} from "@/components";
import {useCustom, useCustomMutation} from "@refinedev/core";
import {API_URL} from "@/api";
import {DurationIcon} from "@/components/icons/duration";
import Countdown from "@/pages/training/trainingDetails/countdown";
import {useNavigate} from "react-router-dom";

interface TrainingQuizFragmentProps {
  courseId: number,
  handleFinish: (val: any) => void,
  submitted: boolean,
  attemptId: string,
  //quizFinished:boolean
}

interface AnswerProps {
  quizQuestionId: string,
  quizOptionId: string,
  isCorrect: boolean,
}

const TrainingQuiz: React.FC<TrainingQuizFragmentProps> = ({courseId, handleFinish, submitted, attemptId = "0"}) => {

  const {t, i18n} = useTranslation();
  const [openModal, setOpenModal] = useState(false);
  const [step, setStep] = useState(1);
  const [answered, setAnswered] = useState(false);
  //const [submitted, setSubmitted] = useState(false);
  const [result, setResult] = useState([]);
  const [questions, setQuestions] = useState<any[]>([]);
  const [answers, setAnswers] = useState<AnswerProps[]>([]);
  const [disabled, setDisabled] = useState<boolean>(false);
  const [quizAttemptId, setQuizAttemptId] = useState<string>("0");
  const [timeLeft, setTimeLeft] = useState(-1);
  const [currentTime, setCurrentTime] = useState(-1);
  const navigate = useNavigate();
  useEffect(() => {
    if (attemptId != "0") {
      setQuizAttemptId(attemptId);
    }
  }, [attemptId])

  const {data: trainingQuizData, isLoading: isEnrolledTrainingLoading} = useCustom({
    url: `${API_URL}/society/training/courses/${courseId}/quiz`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: (submitted) || (!answered && quizAttemptId === "0"),
      retry: false,
      cacheTime: 0,
    },
  });

  const trainingQuiz = trainingQuizData?.data?.data || {};
  //console.log("trainingQuiz", trainingQuiz)

  const {data: quizAttemptData, isLoading: isQuizAttemptLoading} = useCustom({
    url: `${API_URL}/society/training/quiz/attempts/${attemptId}/result`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: (submitted && attemptId != "0"),
      retry: false,
      cacheTime: 0,
    },
  });

  const quizAttempt = quizAttemptData?.data?.data || {};
  //console.log("quizAttempt", quizAttempt)

  useEffect(() => {
    if (Object.keys(quizAttempt).length > 0 && quizAttempt.results.length > 0) {
      setResult(quizAttempt.results);
    }
  }, [quizAttemptData])

  useEffect(() => {
    if (Object.keys(trainingQuiz).length > 0 && trainingQuiz.questions.length > 0) {
      setQuestions(trainingQuiz.questions);
      const temp = trainingQuiz.questions.map((e: any, i: number) => {
        return {
          quizQuestionId: e.id,
          quizOptionId: "0",
          isCorrect: false,
        }
      })
      //console.log("temp", temp);
      setAnswers(temp);
      if (quizAttemptId === "0" && !submitted) {
        StartQuiz(trainingQuiz.id);
      }
    }
  }, [trainingQuizData])


  const next = () => {
    //console.log("answered",answered);
    if (submitted) {
      navigate("/latihan");
      return;
    }
    if (step < Math.ceil(questions.length / 5)) setStep(step + 1);
    else if (!answered) {
      //console.log("currentTime", currentTime);
      setTimeLeft(currentTime);
      checkAnswer();
    } else if (answered && !submitted) {
      setOpenModal(true);
    }
  }

  const back = () => {
    if (step > 1) {
      //console.log("currentTime", currentTime);
      setTimeLeft(currentTime);
      setStep(step - 1)
    }
  }

  const submit = () => {
    setOpenModal(false);
    SubmitQuiz();
  }

  const checkAnswer = () => {
    setAnswered(true);
  }

  const checkScore = () => {
    //console.log("score", answered, submitted, quizAttemptId);
    handleFinish(result);
  }

  const answerQuestion = (i: number, val: any) => {
    answers[i] = {
      quizQuestionId: val.quizQuestionId,
      quizOptionId: val.id,
      isCorrect: val.isCorrect,
    };
    const temp = answers.slice();
    setAnswers(temp);
  }

  const handleStartQuiz = (quizData: any) => {
    //console.log("quizData", quizData);
    setTimeLeft(quizData?.remainingTimeSeconds);
    if (quizData.results.length > 0) {
      const previousAnswers = quizData.results.map((a: AnswerProps) => {
        return {
          quizQuestionId: a.quizQuestionId,
          quizOptionId: a.quizOptionId,
          isCorrect: false,
        }
      });
      setAnswers(prevAnswers => prevAnswers.map((answer) => {
          const t = previousAnswers.find((pa: AnswerProps) => pa.quizQuestionId === answer.quizQuestionId);
          if (t != null && t.quizOptionId != "0") {
            return {...answer, quizOptionId: t.quizOptionId}
          }
          return answer
        }
      ));
      /*answers.forEach((a) => {
        const t = previousAnswers.find((pa: AnswerProps) => pa.quizQuestionId === a.quizQuestionId);
        if (t != null && t.quizOptionId != "0") {
          a.quizOptionId = t.quizOptionId;
        }
      });
      console.log("answers", answers);
      setAnswers(answers);*/
    }
    setQuizAttemptId(quizData?.id);
  }

  const {mutate: startQuiz, isLoading: isLoadingStart} = useCustomMutation();
  const StartQuiz = (id: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    startQuiz(
      {
        url: `${API_URL}/society/training/quiz/attempt`,
        method: "post",
        values: {
          id: id
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            const quizData = data?.data?.data
            handleStartQuiz(quizData);
            return {
              message: data?.data?.msg.includes("successfully") ? t("TRAINING_QUIZ_START") : "",
              type: "success",
            };
          } else {
            back();
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const {mutate: submitQuiz, isLoading: isLoadingSubmit} = useCustomMutation();
  const SubmitQuiz = (): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    submitQuiz(
      {
        url: `${API_URL}/society/training/quiz/attempts/submit`,
        method: "put",
        values: {
          quizAttemptId: quizAttemptId,
          answers: (answers).map((a) => {
            return {
              quizQuestionId: a.quizQuestionId,
              quizOptionId: a.quizOptionId,
            }
          }),
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            const submitData = data?.data?.data;
            setDisabled(true);
            setStep(1);
            handleFinish(submitData);
            return {
              message: t("QUIZ_SUBMITTED"),
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  //let currentTime = timeLeft;
  //console.log("initcurrentTime",currentTime);

  const {mutate: updateQuizAttempt, isLoading: isUpdateQuizAttempt} = useCustomMutation();
  const UpdateQuizAttempt = (val: number): void => {
    const tempAnswers = (answers)
      .filter((a) => a.quizOptionId != "0")
      .map((a) => {
        return {
          quizQuestionId: a.quizQuestionId,
          quizOptionId: a.quizOptionId,
        }
      });
    updateQuizAttempt(
      {
        url: `${API_URL}/society/training/quiz/attempt/heartbeat`,
        method: "post",
        values: {
          quizAttemptId: quizAttemptId,
          timeLeft: val,
          answers: tempAnswers,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess(data) {
          const updateQuizAttemptData = data?.data?.data
          //console.log("updateQuizAttemptData", updateQuizAttemptData)
        },
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const handleTimeChange = (val: number) => {
    //currentTime = val;
    setCurrentTime(val);
    //console.log("currentTime",currentTime);
    UpdateQuizAttempt(val);
  }

  const counter = (submitted ? <></> :
    <Countdown handleFinish={submit} handleTimeChange={handleTimeChange} initialTime={timeLeft}
               attemptId={quizAttemptId}/>)

  return (
    <>
      {quizAttemptId === "0" && !submitted ? <Box
        sx={{
          //flex: 5,
          width: "100%",
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      /> : <></>}
      {answered && !submitted ?
        <Box
          sx={{
            //flex: 5,
            width: "100%",
            borderRadius: 2.5,
            backgroundColor: "#fff",
            //flex: 5,
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Box
            sx={{
              //height: "100%",
              borderRadius: 2.5,
              backgroundColor: "#fff",
              border: "1px solid #D9D9D9",
              //flex: 5,
              px: 5,
              py: 2,
              mb: 1,
            }}
          >
            <Box sx={{display: "flex", justifyContent: "center", gap: 1}}>
              <Typography
                sx={{
                  color: "#0CA6A6",
                  //pt: 3,
                  fontWeight: "500",
                  fontSize: 20,
                }}
              >
                {trainingQuiz.title}
              </Typography>
            </Box>
          </Box>
          <Box
            sx={{
              //height: "100%",
              borderRadius: 2.5,
              backgroundColor: "#fff",
              border: "1px solid #D9D9D9",
              //flex: 5,
              px: 5,
              py: 2,
              mb: 1,
            }}
          >
            <Box sx={{display: "flex", justifyContent: "space-between", gap: 1}}>
              <Typography
                sx={{
                  color: "#666666",
                  //pt: 3,
                  fontWeight: "500",
                  fontSize: 14,
                }}
              >
                {t("quizFinished")}
              </Typography>
              {timeLeft > 0 ?
                counter : <></>}
            </Box>
          </Box>
          {questions.map((q, i) => {
            return (
              <Box
                key={i}
                sx={{
                  //height: "100%",
                  borderRadius: 2.5,
                  backgroundColor: "#fff",
                  border: "1px solid #D9D9D9",
                  //flex: 5,
                  px: 5,
                  py: 2,
                  mb: 1,
                }}
              >
                <Box sx={{display: "flex", justifyContent: "space-between", gap: 1}}>
                  <Typography
                    sx={{
                      color: "#666666",
                      //pt: 3,
                      fontWeight: "400",
                      fontSize: 13,
                    }}
                  >
                    {`S${i + 1}`}
                  </Typography>
                  <Typography
                    sx={{
                      color: "#666666",
                      //pt: 3,
                      fontWeight: "400",
                      fontSize: 13,
                    }}
                  >
                    {answers[i].quizOptionId === "0" ? t("notAnswered") : t("answered")}
                  </Typography>
                </Box>
              </Box>);
          })}
          <Box sx={{position: "relative", bottom: 0}}>
            <Box sx={{display: "flex", justifyContent: "flex-end", gap: 1,}}>
              <ButtonPrimary
                variant="outlined"
                sx={{
                  borderColor: "#0CA6A6",
                  bgcolor: "#fff",
                  "&:hover": {bgcolor: "#fff", borderColor: "#0CA6A6",},
                  color: "#0CA6A6",
                  fontWeight: "400",
                }}
                onClick={() => {
                  //console.log("currentTime", currentTime);
                  setTimeLeft(currentTime);
                  setAnswered(false);
                }}
              >
                {t("back")}
              </ButtonPrimary>
              <ButtonPrimary
                variant="outlined"
                sx={{
                  borderColor: "#0CA6A6",
                  bgcolor: "#0CA6A6",
                  "&:hover": {bgcolor: "#0CA6A6", borderColor: "#0CA6A6",},
                  color: "#fff",
                  fontWeight: "400",
                }}
                onClick={next}
              >
                {t("next")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Box>
        : <></>}
      {(quizAttemptId != "0" && !answered) || (submitted) ?
        <>
          <Box
            sx={{
              //flex: 5,
              width: "100%",
              borderRadius: 2.5,
              backgroundColor: "#fff",
              //flex: 5,
              //display: "inline",
              px: 2,
              py: 2,
              mb: 1,
            }}
          >
            <Box
              sx={{
                //height: "100%",
                borderRadius: 2.5,
                backgroundColor: "#fff",
                border: "1px solid #D9D9D9",
                //flex: 5,
                px: 5,
                py: 2,
                mb: 1,
              }}
            >
              <Box sx={{display: "flex", justifyContent: "space-between", gap: 1}}>
                <Typography
                  sx={{
                    color: "#0CA6A6",
                    //pt: 3,
                    fontWeight: "500",
                    fontSize: 20,
                  }}
                >
                  {trainingQuiz.title}
                </Typography>
                {timeLeft > 0 ?
                  counter : <></>}
              </Box>
            </Box>
            <Box
              sx={{
                //height: "100%",
                borderRadius: 2.5,
                backgroundColor: "#fff",
                border: "1px solid #D9D9D9",
                //flex: 5,
                px: 5,
                py: 2,
                mb: 1,
              }}
            >
              {questions.map((item: any, index: number) => {
                if (index < step * 5 && index >= (step - 1) * 5) {
                  //console.log(answers[index]);
                  return (
                    <Box key={index} sx={{display: "flex", justifyContent: "space-between", gap: 1}}>
                      <Box>
                        <Typography
                          sx={{
                            color: "#666666",
                            pt: 3,
                            fontWeight: "400",
                            fontSize: 14,
                          }}
                        >
                          {`Question ${index + 1}`}
                        </Typography>
                        <Typography
                          sx={{
                            color: "#666666",
                            pt: 3,
                            fontWeight: "400",
                            fontSize: 14,
                          }}
                        >
                          {item.questionText}
                        </Typography>
                        <Box sx={{pl: 1}}>
                          {item.options.map((item2: any, index2: number) => {
                            return (<Typography key={index2}
                                                sx={{fontWeight: "400", fontSize: 14}}
                            >
                              <Checkbox
                                disabled={disabled || submitted}
                                checked={(answers[index] != null && answers[index].quizOptionId === item2.id)
                                  || (Object.keys(quizAttempt).length > 0 && quizAttempt.results.filter((r: any) => r.quizQuestionId === item.id)[0].quizOptionId === item2.id)
                                }
                                onChange={(e) => answerQuestion(index, item2)}
                                sx={{p: 0, ml: 1, mr: 1}}
                              />
                              {`${item2.optionText}`}
                            </Typography>)
                          })}
                        </Box>
                      </Box>
                      {submitted && (Object.keys(quizAttempt).length > 0 && quizAttempt.results.length > 0) ?
                        <Box>
                          <Typography
                            sx={{
                              color: quizAttempt.results.filter((r: any) => r.quizQuestionId === item.id)[0].isCorrect ? "#008000" : "#FF0000",
                              pt: 3,
                              fontWeight: "500",
                              fontSize: 14,
                            }}
                          >
                            {quizAttempt.results.filter((r: any) => r.quizQuestionId === item.id)[0].isCorrect ? t("CORRECT") : t("INCORRECT")}
                          </Typography>
                        </Box> : <></>}
                    </Box>)
                } else {
                  return <></>
                }
              })}
            </Box>
            <Box sx={{display: "flex", mt: 1, justifyContent: "flex-end", gap: 1}}>
              {step > 1 ?
                <ButtonPrimary
                  variant="outlined"
                  sx={{
                    borderColor: "#0CA6A6",
                    bgcolor: "#fff",
                    "&:hover": {bgcolor: "#fff", borderColor: "#0CA6A6",},
                    color: "#0CA6A6",
                    fontWeight: "400",
                  }}
                  disabled={isLoadingSubmit}
                  onClick={() => back()}
                >
                  {t("back")}
                </ButtonPrimary> : <></>}
              {!submitted ?
              <ButtonPrimary
                variant="outlined"
                sx={{
                  borderColor: "#0CA6A6",
                  bgcolor: "#0CA6A6",
                  "&:hover": {bgcolor: "#0CA6A6", borderColor: "#0CA6A6",},
                  color: "#fff",
                  fontWeight: "400",
                }}
                disabled={isLoadingSubmit}
                onClick={next}
              >
                {t("next")}
              </ButtonPrimary> : <></> }
            </Box>
          </Box>
        </> : <></>}
      <DialogConfirmation
        open={openModal}
        onClose={() => {
          setOpenModal(false);
        }}
        onAction={submit}
        isMutating={false}
        onConfirmationText={t("SUBMIT_TRAINING_QUIZ")}
      />
    </>
  );
}

export default TrainingQuiz;
