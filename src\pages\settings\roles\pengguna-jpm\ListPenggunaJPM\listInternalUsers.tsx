import { Box, IconButton, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
// import { Edit as EditIcon } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { useContext, useEffect, useState } from "react";
import { ButtonOutline } from "../../../../../components/button";
import { SearchContext } from "../../../../../contexts/searchProvider";
import { EditIcon } from "../../../../../components/icons";
import { ListUserStatus } from "../../../../../helpers/enums";
import { DataTable } from "@/components";

function ListPengguna() {
  const { t } = useTranslation();
  const [userList, setUserList] = useState<any>([]);
  const [pendingList, setPendingList] = useState([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [totalPendingUsers, setTotalPendingUsers] = useState(0);
  //   const [inactiveList, setInactiveList] = useState([]);
  // const [totalInactiveUsers, setTotalInactiveUsers] = useState(0);
  const {
    page,
    setPage,
    pageSize,
    setPageSize,
    // pageInactive,
    // setPageInactive,
    // pageSizeInactive,
    // setPageSizeInactive,
    pagePending,
    setPagePending,
    pageSizePending,
    setPageSizePending,
    searchResult,
    searchPendingResult,
    searchInactiveResult,
  } = useContext(SearchContext);

  useEffect(() => {
    const formatedList = searchResult?.data?.data?.map((item: any) => ({
      ...item?.user,
      roles: item?.roles,
      jppmBranchName: item?.jppmBranchName,
    }));
    setTotalUsers(searchResult?.data?.total);
    const users = formatedList?.filter((item: any) => item.status !== 2);

    setUserList(users);
    const formatedPendingList = searchPendingResult?.data?.data?.map(
      (item: any) => ({
        ...item.user,
        roles: item.roles,
        jppmBranchName: item.jppmBranchName,
      })
    );
    setTotalPendingUsers(searchPendingResult?.data?.total);
    setPendingList(formatedPendingList);

    // const formatedInactiveList = searchInactiveResult?.data?.data?.map(
    //   (item: any) => ({
    //     ...item.user,
    //     roles: item.roles,
    //     jppmBranchName: item.jppmBranchName,
    //   })
    // );
    // setTotalInactiveUsers(searchInactiveResult?.data?.total);
    // setInactiveList(formatedInactiveList);
  }, [searchResult, searchPendingResult, searchInactiveResult]);

  const navigate = useNavigate();

  const handleChangePage = (newPage: number) => {
    setPage(newPage);
  };

  const handleChangePageSize = (newPageSize: number) => {
    setPageSize(newPageSize);
  };

  const handleChangePagePending = (newPage: number) => {
    setPagePending(newPage);
  };

  const handleChangePageSizePending = (newPageSize: number) => {
    setPageSizePending(newPageSize);
  };

  // const handleChangePageInactive = (newPage: number) => {
  //   setPageInactive(newPage);
  // };

  // const handleChangePageSizeInactive = (newPageSize: number) => {
  //   setPageSizeInactive(newPageSize);
  // };

  const columns = [
    {
      field: "name",
      headerName: t("name"),
      width: 200,
      sortable: false,
      disableColumnMenu: true,
      align: "left",
      headerAlign: "left",
    },
    {
      field: "identificationNo",
      headerName: t("noKadPengenalan"),
      width: 30,
      sortable: false,
      disableColumnMenu: true,
      headerAlign: "center",
      align: "center",
    },
    {
      field: "userCategory",
      headerName: t("kategoriPengguna"),
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      headerAlign: "center",
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        const roles = row?.roles;
        if (roles?.length > 0) {
          const values = Array.from(new Set(roles)).join(", ");
          return (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                flexWrap: "wrap",
                gap: 1,
              }}
            >
              <Typography className="label">{values}</Typography>
            </Box>
          );
        }
        return null;
      },
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      headerAlign: "center",
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        const status = ListUserStatus.find(
          (item) => item.value === row?.status
        );
        const isActive = status?.value === 1;
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Typography
              className="status-pertubuhan-text"
              sx={{
                backgroundColor: "#fff",
                border: `2px solid ${
                  isActive ? "var(--success)" : "var(--error)"
                }`,
              }}
            >
              {status?.label ? t(status?.label) : "-"}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: "jppmBranchName",
      headerName: t("cawanganJPPM"),
      width: 300,
      sortable: false,
      disableColumnMenu: true,
      headerAlign: "center",
      align: "center",
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      sortable: false,
      disableColumnMenu: true,
      align: "right",
      renderCell: (params: any) => {
        const row = params?.row;
        const userId = row.id;
        const userStatus = row.status;

        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "end",
              alignItems: "end",
            }}
          >
            <IconButton
              onClick={() => {
                handleNavigation(userId, userStatus);
              }}
            >
              <EditIcon
                sx={{
                  // mt: "2px",
                  fontSize: "2rem",
                  width: "1rem",
                  height: "1rem",
                  color: "var(--primary-color)",
                }}
              />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  const handleNavigation = (userId: number, userStatus: number) => {
    if (userStatus === 2) {
      navigate(`../ro-approval-pengurusan-pengguna-jpm?id=${userId}`);
    } else {
      navigate(`../create-pengurusan-pengguna-jpm?id=${userId}`);
    }
  };

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography className={"title"}>
            {t("senaraiPenggunaMenungguKelulusan")}
          </Typography>

          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              mt: 3,
            }}
          >
            <ButtonOutline
              sx={{ p: "11px 32px" }}
              onClick={() => {
                navigate("../create-pengurusan-pengguna-jpm");
              }}
            >
              {t("daftarPenggunaJPPM")}
            </ButtonOutline>
          </Box>
          <DataTable
            rows={pendingList}
            columns={columns as any}
            page={pagePending}
            rowsPerPage={pageSizePending}
            totalCount={totalPendingUsers}
            onPageChange={handleChangePagePending}
            onPageSizeChange={handleChangePageSizePending}
            customNoDataText={t("noData")}
          />
        </Box>
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography className={"title"}>{t("senaraiPengguna")}</Typography>

          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              mt: 3,
            }}
          >
            <ButtonOutline
              sx={{ p: "11px 32px" }}
              onClick={() => {
                navigate("../create-pengurusan-pengguna-jpm");
              }}
            >
              {t("daftarPenggunaJPPM")}
            </ButtonOutline>
          </Box>

          <Box
            sx={{
              borderRadius: "14px",
              mt: 3,
              overflow: "scroll",
            }}
          >
            <DataTable
              rows={userList ? userList : []}
              columns={columns as any}
              page={page}
              rowsPerPage={pageSize}
              totalCount={totalUsers}
              onPageChange={handleChangePage}
              onPageSizeChange={handleChangePageSize}
              customNoDataText={t("noData")}
              rowHeight={90}
            />
          </Box>
        </Box>
      </Box>
      {/* <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography className={"title"}>{t("inactiveUserList")}</Typography>
          <DataTable
            rows={inactiveList}
            columns={columns as any}
            page={pageInactive}
            rowsPerPage={pageSizeInactive}
            totalCount={totalInactiveUsers}
            onPageChange={handleChangePageInactive}
            onPageSizeChange={handleChangePageSizeInactive}
            customNoDataText={t("noData")}
            rowHeight={3}
          />
        </Box>
      </Box> */}
    </Box>
  );
}

export default ListPengguna;
