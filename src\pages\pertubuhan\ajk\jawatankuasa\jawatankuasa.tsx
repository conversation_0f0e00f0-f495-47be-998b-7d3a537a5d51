import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Typography, IconButton, CircularProgress } from "@mui/material";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { Ajk } from "../../pernyata-tahunan/interface";
import { EyeIcon } from "../../../../components/icons";
import {
  COMMITTEE_TASK_TYPE,
  DocumentTemplates,
  OrganisationPositions,
} from "../../../../helpers/enums";
import { usejawatankuasaContext } from "./jawatankuasaProvider";
import { useSelector } from "react-redux";
import { getUserPermission } from "@/redux/userReducer";
import {
  downloadFile,
  getLocalStorage,
  useMutation,
  useQuery,
} from "@/helpers";
import { useDispatch } from "react-redux";
import { setJawatankuasaAJKFormValues } from "@/redux/slices/jawatankuasaAJKFormSlice";
import { DataTable, IColumn } from "@/components";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";

const JawatankuasaComp: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id: societyId } = useParams();
  const addressList = getLocalStorage("address_list", []);
  const location = useLocation();
  const disabled = location.state?.disabled ?? false;
  const dispatch = useDispatch();

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const {
    isAliranModuleAccess,
    module,
    society,
    fetchSociety,
    fetchAliranTugas,
    fetchAliranTugasAccess,
    fetchAliranTugasStatus,
    setModule,
  } = usejawatankuasaContext();

  const isManager = useSelector(getUserPermission);
  const [shouldFetch, setShouldFetch] = useState<boolean>(true);
  useEffect(() => {
    fetchAjkList();
    // fetchAddressList();
    fetchSociety();
    fetchAliranTugasAccess(module);
    fetchAliranTugasStatus(module);
    fetchAliranTugas(module);
  }, [module, shouldFetch]);

  useEffect(() => {
    setModule(COMMITTEE_TASK_TYPE.PENGURUSAN_AJK);
  }, []);

  const handleViewAjk = (ajk: Ajk) => {
    navigate(`../jawatankuasa/create-ajk`, {
      state: {
        ajk: ajk,
        view: true,
      },
    });
  };

  const { fetch: downloadReceipt, isLoading: isLoadingReceipt } = useMutation({
    url: "society/document/exportPdf",
    method: "post",
    onSuccess: (data) => {
      if (data?.data?.status === "SUCCESS") {
        console.log(data);
        downloadFile({
          data: data?.data?.data?.pdfBytes,
          name: data?.data?.data?.fileName,
        });
      }
    },
  });

  const handleCetak = async () => {
    const body = {
      // documentType: "SENARAI_AHLI_JAWATAN_KUASA",
      documentTemplateCode: DocumentTemplates.SENARAI_AHLI_JAWATAN_KUASA.value,
      societyId: societyId,
    };
    downloadReceipt(body);
  };

  useEffect(() => {
    dispatch(
      setJawatankuasaAJKFormValues({
        type: null,
        savedMeetingDate: null,
        savedMeetingDetail: null,
        appointmentDate: null,
        uploadedIds: [],
        citizenAJKs: [],
      })
    );
  }, []);

  const { mutate: updateList, isLoading: isUpdatingList } = useCustomMutation();

  const isBlacklisted = society?.subStatusCode === "003";
  const isAccessible = !isBlacklisted && (isManager || isAliranModuleAccess);
  const [isReordering, setIsReordering] = useState(false);
  const [saveReorder, setSaveReorder] = useState(false);
  const [pageSize, setPageSize] = useState(5);
  const [page, setPage] = useState(1);
  const {
    data: ajks,
    isLoading: isLoadingAJKs,
    refetch: fetchAjkList,
  } = useQuery({
    url: `society/committee/listAjk`,
    filters: [
      { field: "societyId", operator: "eq" as const, value: societyId },
      {
        field: "status",
        operator: "eq" as const,
        value: society?.applicationStatusCode === 2 ? "008" : "001",
      },
      ...(!isReordering
        ? [
            { field: "pageNo", operator: "eq" as const, value: page },
            { field: "pageSize", operator: "eq" as const, value: pageSize },
          ]
        : []),
    ],
    autoFetch: true,
  });

  const columns: IColumn[] = [
    {
      field: "designationCode",
      headerName: t("position"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box>
            {t(
              OrganisationPositions.find(
                (item) => item.value === Number(row.designationCode)
              )?.label || "-"
            )}
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "name",
      headerName: t("name"),
      align: "center",
      flex: 1,
      cellClassName: "custom-cell",
    },
    {
      flex: 1,
      field: "email",
      headerName: t("email"),
      align: "center",
      cellClassName: "custom-cell",
    },
    {
      flex: 1,
      field: "phoneNumber",
      headerName: t("phoneNumber"),
      align: "center",
      cellClassName: "custom-cell",
    },
    {
      field: "residentialStateCode",
      headerName: t("state"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box>
            {
              addressList.find(
                (item: any) => item.id.toString() === row.residentialStateCode
              )?.name
            }
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      flex: 1,
      field: "actions",
      headerName: "",
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <>
            <IconButton onClick={() => handleViewAjk(row)}>
              <EyeIcon />
            </IconButton>
          </>
        );
      },
    },
  ];

  const handleReorder = (newOrder: string[]) => {
    if (saveReorder) {
      updateList(
        {
          url: `${API_URL}/society/committee/arrangeCommittee`,
          method: "put",
          values: { societyId: societyId, committeeIdList: newOrder },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            if (data?.data?.status === "SUCCESS") {
              return {
                message: data?.data?.msg,
                type: "success",
              };
            } else {
              return {
                message: data?.data?.msg,
                type: "error",
              };
            }
          },
          errorNotification: (data) => {
            return {
              message: data?.response?.data?.msg,
              type: "error",
            };
          },
        },
        {
          onError(error, variables, context) {
            console.log(error);
          },
          onSuccess: () => {
            setIsReordering(false);
          },
        }
      );
    }
  };

  useEffect(() => {
    if (isReordering) {
      fetchAjkList();
    }
  }, [isReordering]);

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            textAlign: "center",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {t("bilanganAhliJawatankuasaTerkini")}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {ajks?.data?.data?.total} Orang
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("ajkList")}
          </Typography>
          <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 1 }}>
            {isReordering ? (
              <>
                <ButtonPrimary
                  onClick={() => {
                    setSaveReorder(true);
                  }}
                >
                  {t("save")}
                </ButtonPrimary>
                <ButtonOutline
                  onClick={() => {
                    setSaveReorder(false);
                    setIsReordering(false);
                  }}
                >
                  {t("cancel")}
                </ButtonOutline>
              </>
            ) : (
              <ButtonOutline
                onClick={() => {
                  setSaveReorder(false);
                  setIsReordering(true);
                }}
              >
                {t("reorder")}
              </ButtonOutline>
            )}
          </Box>
          <DataTable
            columns={columns}
            rows={ajks?.data?.data?.data || []}
            page={page}
            rowsPerPage={pageSize}
            isLoading={isLoadingAJKs}
            totalCount={ajks?.data?.data?.total}
            onPageChange={(newPage: number) => setPage(newPage)}
            onPageSizeChange={(newRowsPerPage: number) =>
              setPageSize(newRowsPerPage)
            }
            enableRowReordering={true}
            isReorderingMode={isReordering}
            onReorder={handleReorder}
            noPagination={isReordering}
            saveReorder={saveReorder}
          />

          {ajks?.data?.data?.total > 0 ? (
            <Box sx={{ mt: 3, display: "flex", justifyContent: "flex-end" }}>
              <ButtonPrimary
                onClick={handleCetak}
                sx={{
                  backgroundColor: "#00A7A7",
                  "&:hover": {
                    backgroundColor: "#008F8F",
                  },
                  borderRadius: 1,
                  textTransform: "none",
                }}
              >
                {isLoadingReceipt ? <CircularProgress size={24} /> : t("cetak")}
              </ButtonPrimary>
            </Box>
          ) : null}
        </Box>
      </Box>
      {isAccessible ? (
        <>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("update")} {t("ahliJawatanKuasa")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{ color: "#666666", fontSize: 14 }}
                >
                  {t("update")} {t("ahliJawatanKuasa")}
                </Typography>

                <ButtonPrimary
                  disabled={disabled}
                  onClick={() => {
                    navigate(`update-ajk`);
                  }}
                >
                  {t("update")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>{" "}
        </>
      ) : (
        <></>
      )}
    </>
    // :
    //  <></>
    // }
    // </>
  );
};

export default JawatankuasaComp;
