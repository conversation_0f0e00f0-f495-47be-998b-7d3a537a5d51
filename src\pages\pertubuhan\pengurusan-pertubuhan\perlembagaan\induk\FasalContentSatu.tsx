import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../../api";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import { setSocietyDataRedux } from "@/redux/societyDataReducer";
import { useDispatch } from "react-redux";
import { OrganizationLevelOption, useQuery } from "@/helpers";

interface FasalContentSatuProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentSatu: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [singkatanNama, setSingkatanNama] = useState("");
  const [takrifNama, setTakrifNama] = useState("");
  const [tarafPertubuhan, setTarafPertubuhan] = useState("");
  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState("");
  //const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [applicationStatusCode, setApplicationStatusCode] = useState(0);
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };
  const [isNameExist, setIsNameExist] = useState("");
  const [oldName, setOldName] = useState("");

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  /*useEffect(() => {
    if (encodedId) {
      const decodedId = atob(encodedId);
      setSocietyId(decodedId);
      fetch(`${API_URL}/society/${decodedId}`, {
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      })
        .then((res) => res.json())
        .then((data) => {
          setNamaPertubuhan(data?.data?.societyName);
        })
        .catch((error) => {
          console.error("Error fetching society details:", error);
        });
    }
  }, []);*/

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (namaPertubuhan?.length === 0) {
      errors.namaPertubuhan = t("fieldRequired");
    } else if (namaPertubuhan.length < 7) {
      errors.namaPertubuhan = "Sila masukkan sekurang-kurangnya 5 aksara";
    }

    return errors;
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  /*const clauseContent = `
    1. Pertubuhan ini dikenali dengan nama
    ${namaPertubuhan}
    selepas ini disebut "Pertubuhan".

    2. Singkatan Nama: ${singkatanNama || "<<Singkatan Nama>>"}

    3. Takrif Nama: ${takrifNama || "<<Takrif Nama>>"}

    4. Bertaraf: ${tarafPertubuhan}
  `;*/

  //const clause = localStorage.getItem("clause1");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<NAMA PERTUBUHAN>>/gi,
    `<b>${namaPertubuhan || "<<NAMA PERTUBUHAN>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<SINGKATAN NAMA>>/gi,
    `<b>${singkatanNama || "<<SINGKATAN NAMA>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<TAKRIF NAMA>>/gi,
    `<b>${takrifNama || "<<TAKRIF NAMA>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<TARAF PERTUBUHAN>>/gi,
    `<b>${tarafPertubuhan || "<<TARAF PERTUBUHAN>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      console.log(societyDataRedux);
      setTarafPertubuhan(societyDataRedux.societyLevel);
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
      setOldName(societyDataRedux.societyName);
      setApplicationStatusCode(societyDataRedux.applicationStatusCode);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      setDataId(clause.id);
      if (clause.constitutionValues.length > 0) {
        const fieldMappings: Record<string, (value: string) => void> = {
          // "Nama Pertubuhan": setNamaPertubuhan,
          "Singkatan Nama": setSingkatanNama,
          "Takrif Nama": setTakrifNama,
          "Taraf Pertubuhan": setTarafPertubuhan,
        };

        Object.values(fieldMappings).forEach((setter) => setter(""));

        if (clause.constitutionValues) {
          clause.constitutionValues.forEach((item: any) => {
            const setter = fieldMappings[item.titleName];
            if (setter && item.definitionName) {
              setter(item.definitionName);
            }
          });
        }
      }
      setIsEdit(clause.edit);
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent);
        setClauseContentId(clause.clauseContentId);
      }
    }
  }, [clause]);

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  const { id } = useParams();

  const [debouncedSocietyName, setDebouncedSocietyName] = useState("");
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSocietyName(namaPertubuhan);
    }, 500); // Adjust 500ms delay as needed

    return () => clearTimeout(timer);
  }, [namaPertubuhan]);

  useEffect(() => {
    if (debouncedSocietyName !== oldName && debouncedSocietyName?.length > 5) {
      refetch();
    }
  }, [debouncedSocietyName]);

  const {
    data: societyNameExist,
    isLoading: isLoadingSocietyNameExist,
    refetch,
  } = useQuery({
    autoFetch: false,
    url: `society/checkSocietyNameExists`,
    filters: [
      {
        field: "societyName",
        operator: "eq",
        value: debouncedSocietyName,
      },
    ],
    onSuccess: (data) => {
      const response = data?.data;

      const errors: { [key: string]: string } = {};

      if (response?.data === true) {
        errors.societyName = t(response?.msg);
        setIsNameExist(t(response?.msg));
      } else {
        setIsNameExist("");
      }

      if (!namaPertubuhan) {
        // Organization name validation
      } else {
        const namePattern = /^(pertubuhan|persatuan|kelab)\s/i;
        if (!namePattern.test(namaPertubuhan)) {
          errors.societyName =
            "Nama pertubuhan hendaklah bermula dengan Pertubuhan/ persatuan/ kelab";
          setIsNameExist(
            "Nama pertubuhan hendaklah bermula dengan Pertubuhan/ persatuan/ kelab"
          );
        }
      }
      setFormErrors(errors);
    },
  });

  const handleUpdateSociety = () => {
    Edit();
  };
  const dispatch = useDispatch();

  const { mutate: edit, isLoading: isLoadingEdit } = useCustomMutation();
  const Edit = (): void => {
    edit(
      {
        url: `society/${societyId}/edit`,
        method: "put",
        values: {
          societyName: namaPertubuhan,
          societyLevel: tarafPertubuhan,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status === "SUCCESS") {
            const temp = {
              ...societyDataRedux,
              societyName: namaPertubuhan,
              societyLevel: tarafPertubuhan,
            };
            dispatch(setSocietyDataRedux(temp));
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clause")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("namaPertubuhan")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              name="namaPertubuhan"
              size="small"
              value={namaPertubuhan}
              placeholder={`${t("namaPertubuhan")}`}
              fullWidth
              required
              disabled={applicationStatusCode !== 36}
              sx={applicationStatusCode !== 36 ? { background: "#E8E9E8" } : {}}
              error={!!isNameExist || !!formErrors.namaPertubuhan}
              helperText={isNameExist}
              onChange={(e) => {
                setNamaPertubuhan(e.target.value);

                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  namaPertubuhan: "",
                }));
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("organizationLevel")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <Select
              size="small"
              fullWidth
              required
              value={tarafPertubuhan}
              displayEmpty
              disabled
              sx={{ background: "#E8E9E8" }}
              onChange={(e) => {
                setTarafPertubuhan(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  tarafPertubuhan: "",
                }));
              }}
              renderValue={(selected) =>
                selected
                  ? OrganizationLevelOption.find(
                      (item) => item.value === selected
                    )?.value
                  : t("organizationLevel")
              }
            >
              {OrganizationLevelOption.map((item) => (
                <MenuItem key={item.value} value={item.value}>
                  {item.value}
                </MenuItem>
              ))}
            </Select>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("nameAbbreviation")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              value={singkatanNama}
              fullWidth
              onChange={(e) => {
                setSingkatanNama(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  singkatanNama: "",
                }));
              }}
              error={!!formErrors.singkatanNama}
              helperText={formErrors.singkatanNama}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("nameDefinition")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              value={takrifNama}
              placeholder={`${t("nameDefinitionPlaceholder")}`}
              fullWidth
              multiline
              rows={5}
              onChange={(e) => {
                setTakrifNama(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  takrifNama: "",
                }));
              }}
              error={!!formErrors.takrifNama}
              helperText={formErrors.takrifNama}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{ mt: 2, display: "flex", justifyContent: "flex-end", gap: 1 }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            const errors = validateForm();
            if (Object.keys(errors).length > 0) {
              setFormErrors(errors);
              return;
            }

            // handleUpdateSociety();
            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: namaPertubuhan,
                  titleName: "Nama Pertubuhan",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: singkatanNama,
                  titleName: "Singkatan Nama",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: takrifNama,
                  titleName: "Takrif Nama",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tarafPertubuhan,
                  titleName: "Taraf Pertubuhan",
                },
              ],
              clause: "clause1",
              clauseCount: 1,
              clauseContentId,
            });
          }}
          disabled={
            isCreatingContent ||
            isEditingContent ||
            !checked ||
            isNameExist?.length > 0 ||
            namaPertubuhan?.length < 7
          }
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentSatu;
