import { configureStore } from '@reduxjs/toolkit'
import { FLUSH, PAUSE, PERSIST, persistCombineReducers, persistStore, PURGE, REGISTER, REHYDRATE } from 'reduxjs-toolkit-persist';
import createIdbStorage from '@piotr-cz/redux-persist-idb-storage';

import { ajkSlice } from './ajkReducer'
import { societyDataSlice } from "./societyDataReducer";
import { addressDataSlice } from "./addressDataReducer";
import { geranSlice } from './geranReducer';
import { branchDataSlice } from "./branchDataReducer";
import { statementDataSlice } from "./statementDataReducer";
import { rayuanDataSlice } from './rayuanDataReducer';
import { fasalSlice } from './fasalReducer';
import { rolesDataSlice } from './rolesDataReducer';
import { organisationCategoriesDataSlice } from './organisationCategoriesDataReducer';
import { permissionsListDataSlice } from './permissionsListDataReducer';
import { appealDataSlice } from './appealDataReducer';
import { appealByIdDataSlice } from './appealByIdDataReducer';
import { societyByIdDataSlice } from './societyByIdDataReducer';
import { userCommitteeBySocietyIdDataSlice } from './userCommitteeBySocietyIdDataReducer';
import { branchByIdDataSlice } from './branchByIdDataReducer';
import { meetingByBranchIdDataSlice } from './meetingByBranchIdDataReducer';
import { positionListDataSlice } from './positionListDataReducer';
import { userByIcDataSlice } from './userByIcDataReducer';
import { carianSlice } from './carianReducer';
import { userSlice } from './userReducer';
import { societyBySearchDataSlice } from './societyBySearchDataReducer';
import { secretaryBranchReformSlice } from './secretaryBranchReformReducer';
import { extensionTimeSlice } from './extensionTimeDataReducer';
import { branchAmendSlice } from './branchAmendReducer';
import { notificationsSlice } from './notificationReducer';
import { chatbotReducer } from './chatbotReducer';
import { takwimSlice } from './takwimDataReducer';
import { jawatankuasaAJKFormSlice } from './slices/jawatankuasaAJKFormSlice';
import { paymentSlice } from './paymentReducer';
import { liquidationSlice } from './liquidationReducer';
import { pegawaiSlice } from './pegawaiReducer';

const reducer = persistCombineReducers(
  {
    key: 'eRoses',
    storage: createIdbStorage({ name: 'eRoses', storeName: 'values' }),
    blacklist: ['addressData', 'secretaryBranchReform', 'chatbot', 'liquidation'],
    debug: import.meta.env.DEV
  },
  {
    takwim: takwimSlice.reducer,
    branchAmendData: branchAmendSlice.reducer,
    carian: carianSlice.reducer,
    pegawai: pegawaiSlice.reducer,
    chatbot: chatbotReducer,
    fasal: fasalSlice.reducer,
    ajk: ajkSlice.reducer,
    societyData: societyDataSlice.reducer,
    addressData: addressDataSlice.reducer,
    branchData: branchDataSlice.reducer,
    statementData: statementDataSlice.reducer,
    geran: geranSlice.reducer,
    rayuanData: rayuanDataSlice.reducer,
    rolesData: rolesDataSlice.reducer,
    organisationCategoriesData: organisationCategoriesDataSlice.reducer,
    permissionsListData: permissionsListDataSlice.reducer,
    positionListData: positionListDataSlice.reducer,
    appealData: appealDataSlice.reducer,
    appealByIdData: appealByIdDataSlice.reducer,
    societyByIdData: societyByIdDataSlice.reducer,
    userByIcData: userByIcDataSlice.reducer,
    branchByIdData: branchByIdDataSlice.reducer,
    meetingByBranchIdData: meetingByBranchIdDataSlice.reducer,
    userCommitteeBySocietyIdData: userCommitteeBySocietyIdDataSlice.reducer,
    user: userSlice.reducer,
    societyBySearchData: societyBySearchDataSlice.reducer,
    secretaryBranchReform: secretaryBranchReformSlice.reducer,
    extensionTime: extensionTimeSlice.reducer,
    notifications: notificationsSlice.reducer,
    jawatankuasaAJKForm: jawatankuasaAJKFormSlice.reducer,
    payment: paymentSlice.reducer,
    liquidation: liquidationSlice.reducer
  }
)

export const store = configureStore({
  devTools: import.meta.env.DEV,
  reducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // @ts-expect-error
        ignoreActions: [
          FLUSH,
          REHYDRATE,
          PAUSE,
          PERSIST,
          PURGE,
          REGISTER
        ]
      }
    })
})

export const persistor = persistStore(store)

// Get the type of our store variable
export type AppStore = typeof store
// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<AppStore['getState']>
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = AppStore['dispatch']

