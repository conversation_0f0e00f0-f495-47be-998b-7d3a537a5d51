import Box from "@mui/material/Box";
import React, { useEffect, useState } from "react";
import { Stack, Typography } from "@mui/material";
import ALiranTugas from "../../../AliranTugas";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import FasalContent from "../../../../../components/FasalContent";
import { useTranslation } from "react-i18next";
import { API_URL } from "../../../../../api";
import { useCustom } from "@refinedev/core";
import { useDispatch } from "react-redux";
import { setIsDisplayConstituition } from "../../../../../redux/fasalReducer";
import { removeLocalStorage } from "../../../../../helpers/utils";
import { removeFromStorage } from "../../../pengurusan-pertubuhan/perlembagaan/removeFasal";
import {
  ApplicationStatus,
  ConstitutionType,
} from "../../../../../helpers/enums";
import JawatankuasaProvider from "@/pages/pertubuhan/ajk/jawatankuasa/jawatankuasaProvider";
import { useSelector } from "react-redux";
import { getUserPermission } from "@/redux/userReducer";
import { useDownloadAndExportConstitutions } from "@/helpers/hooks/useDownloadConstitutions";
import { useBranchContext } from "@/pages/pertubuhan/BranchProvider";
// import JawatankuasaProvider from "../CawanganAJKKeahlian/jawatankuasa/jawatankuasaProvider";

const Perlembagaan = () => {
  const [fasal, setFasal] = useState([]);
  const [generate, setGenerate] = useState(false);
  const { id } = useParams();

  const { data: clauseContentData, isLoading: loadingData } = useCustom({
    url: `${API_URL}/society/constitutioncontent/get`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: id,
        status: ApplicationStatus["AKTIF"],
      },
    },
    queryOptions: {
      enabled: !!id,
    },
  });
  const clauseContent = clauseContentData?.data?.data?.data || [];
  const { data: constitutionData, isLoading: isConstitutionLoading } =
    useCustom({
      url: `${API_URL}/society/admin/constitutionTypeWithClauseContent/list`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
    });

  const allConstitutions = constitutionData?.data?.data || [];
  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const constitutionType =
    societyData?.data?.data?.constitutionType || ConstitutionType.IndukNGO[1];

  const generatePerlembagaanAfter = () => {
    if (constitutionType) {
      const updatedConstitutions = allConstitutions?.map((item: any) => {
        const updatedClauseContents = item?.clauseContents?.map(
          (clause: any, index: any) => {
            const existingItem = clauseContent.find(
              (item: any) => item.clauseContentId === clause.id
            );
            if (existingItem) {
              return {
                ...clause,
                description: existingItem?.description,
              };
            }
            return { ...clause, description: clause?.content };
          }
        );
        return {
          ...item,
          clauseContents: updatedClauseContents,
        };
      });
      /*updatedConstitutions?.map((item: any) => {
        if (item.name === constitutionType) {
          const updatedItem = item?.clauseContents?.map(
            (clause: any, index: any) => {
              clause.content = clause.content.replaceAll(/<</gi,"&lt;&lt;");
              clause.content = clause.content.replaceAll(/>>/gi,"&gt;&gt;");
              clause.description = clause.description.replaceAll(/<</gi,"&lt;&lt;");
              clause.description = clause.description.replaceAll(/>>/gi,"&gt;&gt;");
              return { ...clause };
            }
          );
          setFasal(updatedItem);
        }
      });*/
      updatedConstitutions?.map((item: any) => {
        if (item.name === constitutionType) {
          setFasal(item.clauseContents);
        }
      });
    } else {
      setFasal([]);
    }
  };

  useEffect(() => {
    if (clauseContent && allConstitutions && constitutionType) {
      setGenerate(true);
    }
  }, [constitutionType, allConstitutions, clauseContent]);

  useEffect(() => {
    if (generate) {
      generatePerlembagaanAfter();
    }
  }, [generate]);

  const hideIndex = clauseContent.findIndex(
    (p: any) => p.hideConstitution == true
  );

  const { getConstitutionsFile, isLoadingDownloadConstitutions } =
    useDownloadAndExportConstitutions();

  const hideId = clauseContent?.[hideIndex]?.clauseContentId ?? null;

  const isLoading = loadingData || isConstitutionLoading;
  return (
    <>
      {!isLoading && fasal.length > 0 && (
        <FasalContent
          downloadButton
          fasalContent={fasal}
          isLoadingDownload={isLoading}
          downloadFunction={() => id && getConstitutionsFile(id, 1)}
          hideId={hideId}
        />
      )}
    </>
  );
};

export interface Organization {
  id: number;
  name: string;
  code: string;
}

export const CawanganPerlembagaan: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const navigate = useNavigate();
  const { id } = useParams();
  const { societyId } = useBranchContext();

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const goPage = (url: string) => {
    removeLocalStorage("amendmentId");
    removeFromStorage();
    dispatch(setIsDisplayConstituition(false));
    navigate(url);
  };

  const { data: clauseContentData, isLoading: isClauseContentDataLoading } =
    useCustom({
      url: `${API_URL}/society/constitutioncontent/get`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        query: {
          societyId: societyId,
          status: ApplicationStatus["AKTIF"],
        },
      },
      queryOptions: {
        enabled: !!societyId,
      },
    });

  let clauseContent;
  if (clauseContentData) {
    clauseContent = clauseContentData?.data?.data?.data || [];
  }

  const location = useLocation();
  const disabled = location.state?.disabled ?? false;

  const isManager = useSelector(getUserPermission);

  return (
    <>
      <Stack
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
        gap={2}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("perlembagaanPertubuhan")}
          </Typography>
          <Perlembagaan />
        </Box>
      </Stack>

      <Stack
        sx={{
          backgroundColor: "white",
          borderRadius: "14px",
        }}
        gap={2}
      >
        <JawatankuasaProvider>
          {/* {isManager ? <ALiranTugas disabled={disabled} /> : <></> } */}
        </JawatankuasaProvider>
      </Stack>
    </>
  );
};

export default CawanganPerlembagaan;
