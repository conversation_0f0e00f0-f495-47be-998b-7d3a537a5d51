import { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";
import { FieldValues, useForm } from "react-hook-form";
import {
  useMutation,
  getAddressList,
  getMalaysiaAddressList,
  omitKeysFromObject,
} from "@/helpers";

import {
  Box,
  Theme,
  Typography,
  useMediaQuery,
  CircularProgress,
} from "@mui/material";
import {
  FormFieldRow,
  Label,
  TextFieldController,
  ButtonPrimary,
  SelectFieldController,
} from "@/components";
import ButtonPrevious from "@/components/button/ButtonPrevious";

import { IApiResponse } from "@/types";

const Tambah = () => {
  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const addressList = getAddressList() ?? [];
  const malaysiaAddressList = getMalaysiaAddressList() ?? [];

  const { control, handleSubmit, watch, setValue } = useForm<FieldValues>({
    defaultValues: {
      description: "",
      code: "",
      address: "",
      cityCode: "",
      districtCode: "",
      stateCode: "",
      postcode: "",
      phoneCode: "",
      phoneNumber: "",
      faxCode: "",
      faxNumber: "",
      status: 1,
    },
  });

  const stateOptions = useMemo(() => {
    return malaysiaAddressList.map((item: any) => ({
      label: item.name,
      value: item.id,
    }));
  }, []);

  const districtOptions = useMemo(
    () =>
      addressList
        .filter((item: any) => item.pid === Number(watch("stateCode")))
        .map((item: any) => ({
          value: String(item.id),
          label: item.name,
        })),
    [addressList, watch("stateCode")]
  );

  const { fetch: createJppmBranch, isLoading: isCreatingJppmBranch } =
    useMutation<IApiResponse<string | number>>({
      url: "society/lookup/jppmBranch/create",
      onSuccess: (res) => {
        const resCode = res.data.code;

        if (resCode === 200) navigate("..");
      },
    });

  const onSubmit = (data: FieldValues) => {
    const { faxCode, phoneCode, phoneNumber, faxNumber } = data;
    const keysToSkip = ["faxCode", "phoneCode"];
    const filteredValues = omitKeysFromObject(data, keysToSkip);

    const payload = {
      ...filteredValues,
      phoneNumber: `${phoneCode}-${phoneNumber}`,
      faxNumber: `${faxCode}-${faxNumber}`,
    };

    createJppmBranch(payload);
  };

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography sx={{ mb: 2 }} className="title">
            {t("penambahanCawangan")}
          </Typography>

          <Box
            component="form"
            onSubmit={handleSubmit(onSubmit)}
            sx={{ display: "grid" }}
          >
            <FormFieldRow
              label={<Label text={t("kodCawangan")} required />}
              value={
                <TextFieldController control={control} name="code" required />
              }
            />

            <FormFieldRow
              label={<Label text={t("cawangan")} required />}
              value={
                <TextFieldController
                  control={control}
                  name="description"
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("address")} required />}
              value={
                <TextFieldController
                  name="address"
                  control={control}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("state")} required />}
              value={
                <SelectFieldController
                  name="stateCode"
                  control={control}
                  options={stateOptions}
                  placeholder={t("selectPlaceholder")}
                  onChange={() => setValue("districtCode", "")}
                  requiredCondition={() => true}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("district")} required />}
              value={
                <SelectFieldController
                  name="districtCode"
                  control={control}
                  options={districtOptions}
                  placeholder={t("selectPlaceholder")}
                  disabled={!watch("stateCode")}
                  requiredCondition={() => true}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("city")} required />}
              value={
                <TextFieldController
                  name="cityCode"
                  control={control}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("postcode")} required />}
              value={
                <TextFieldController
                  name="postcode"
                  control={control}
                  type="number"
                  isPostcode
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("phoneNumber")} required />}
              value={
                <Box sx={{ display: "flex", gap: 2 }}>
                  <TextFieldController
                    control={control}
                    name="phoneCode"
                    type="telCode"
                    sx={{ width: "80px" }}
                    inputProps={{ maxLength: 3 }}
                    sxControl={{ width: "80px" }}
                    required
                  />
                  <TextFieldController
                    control={control}
                    name="phoneNumber"
                    type="tel"
                    required
                  />
                </Box>
              }
            />

            <FormFieldRow
              label={<Label text={t("faxNumber")} required />}
              value={
                <Box sx={{ display: "flex", gap: 2 }}>
                  <TextFieldController
                    control={control}
                    name="faxCode"
                    type="telCode"
                    sx={{ width: "80px" }}
                    inputProps={{ maxLength: 3 }}
                    sxControl={{ width: "80px" }}
                    required
                  />
                  <TextFieldController
                    control={control}
                    name="faxNumber"
                    type="tel"
                    required
                  />
                </Box>
              }
            />

            <Box
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonPrevious
                variant="outlined"
                sx={{
                  bgcolor: "white",
                  "&:hover": { bgcolor: "white" },
                  width: isMobile ? "100%" : "auto",
                }}
                onClick={() => navigate(-1)}
              >
                {t("back")}
              </ButtonPrevious>
              <ButtonPrimary
                type="submit"
                disabled={isCreatingJppmBranch}
                variant="contained"
                sx={{
                  width: isMobile ? "100%" : "auto",
                }}
              >
                {isCreatingJppmBranch && <CircularProgress size={15} />}

                {t("add")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Tambah;
