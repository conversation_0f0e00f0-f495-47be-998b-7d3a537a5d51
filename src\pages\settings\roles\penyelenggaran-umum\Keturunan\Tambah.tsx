import { useNavigate } from "react-router-dom";
import { t } from "i18next";
import { FieldValues, useForm } from "react-hook-form";
import { useMutation } from "@/helpers";

import {
  Box,
  Theme,
  Typography,
  useMediaQuery,
  CircularProgress,
} from "@mui/material";
import {
  FormFieldRow,
  Label,
  TextFieldController,
  ButtonPrimary,
} from "@/components";
import ButtonPrevious from "@/components/button/ButtonPrevious";

import { IApiResponse } from "@/types";

const Tambah = () => {
  const navigate = useNavigate();

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const { fetch: createRace, isLoading: isCreatingRace } = useMutation<
    IApiResponse<string | number>
  >({
    url: "society/lookup/race/create",
    onSuccess: (res) => {
      const resCode = res.data.code;

      if (resCode === 200) navigate("..");
    },
  });

  const { control, handleSubmit } = useForm<FieldValues>({
    defaultValues: {
      name: "",
      code: "",
      status: 1,
    },
  });

  const onSubmit = (data: FieldValues) => createRace(data);

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography sx={{ mb: 2 }} className="title">
            {t("penambahanKeturunan")}
          </Typography>

          <Box
            component="form"
            onSubmit={handleSubmit(onSubmit)}
            sx={{ display: "grid" }}
          >
            <FormFieldRow
              label={<Label text={t("kodKeturunan")} />}
              value={<TextFieldController control={control} name="code" />}
            />

            <FormFieldRow
              label={<Label text={t("namaKeturunan")} />}
              value={<TextFieldController control={control} name="name" />}
            />

            <Box
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonPrevious
                variant="outlined"
                sx={{
                  bgcolor: "white",
                  "&:hover": { bgcolor: "white" },
                  width: isMobile ? "100%" : "auto",
                }}
                onClick={() => navigate(-1)}
              >
                {t("back")}
              </ButtonPrevious>
              <ButtonPrimary
                type="submit"
                disabled={isCreatingRace}
                variant="contained"
                sx={{
                  width: isMobile ? "100%" : "auto",
                }}
              >
                {isCreatingRace && <CircularProgress size={15} />}

                {t("add")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Tambah;
