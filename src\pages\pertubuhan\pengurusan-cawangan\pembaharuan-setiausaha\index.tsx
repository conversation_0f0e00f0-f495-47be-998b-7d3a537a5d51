import { useMemo, useCallback } from "react";
import { useForm, FieldValues } from "react-hook-form";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { useQuery, debounce } from "@/helpers";

import {
  Box,
  Typography,
  TextField,
  CircularProgress,
  IconButton,
} from "@mui/material";
import {
  DataTable,
  IColumn,
  ButtonPrimary,
  SelectFieldController,
} from "@/components";

import {
  IApiResponse,
  IApiPaginatedResponse,
  ISocietyBranchList,
  ISecretaryBranchList,
} from "@/types";

import { Search } from "@mui/icons-material";
import { EyeIcon } from "@/components/icons";

export const PembaharuanSetiausahaCawangan: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();

  const columns: IColumn<ISecretaryBranchList>[] = [
    { field: "secretaryName", headerName: t("secretaryName"), flex: 3 },
    { field: "branchName", headerName: t("namaCawangan"), flex: 3 },
    {
      field: "replacementDate",
      headerName: t("renewalDate"),
      flex: 2,
      align: "center",
    },
    {
      field: "id",
      headerName: "",
      renderCell: ({ row }) => (
        <IconButton sx={{ p: 0.5 }} onClick={() => navigate(`cetak/${row.id}`)}>
          <EyeIcon />
        </IconButton>
      ),
    },
  ];

  const { setValue, watch, control } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 99,
      secretaryName: undefined,
      selectedBranch: "",
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");
  const secretaryName = watch("secretaryName");
  const selectedBranch = watch("selectedBranch");

  const {
    data: branchSecretaryListRes,
    isLoading: isLoadingSecretaryBranchList,
  } = useQuery<IApiPaginatedResponse<ISecretaryBranchList>>({
    url: `society/${id}/branches/secretaries`,
    filters: [
      {
        field: "page",
        operator: "eq",
        value: page,
      },
      {
        field: "secretaryName",
        operator: "eq",
        value: secretaryName,
      },
    ],
  });

  const { data: branchListRes, isLoading: isLoadingBranchList } = useQuery<
    IApiResponse<ISocietyBranchList[]>
  >({
    url: `society/branch/getAllActiveBySocietyId`,
    filters: [
      {
        field: "societyId",
        operator: "eq",
        value: id,
      },
    ],
  });

  const branchSecretaryList = branchSecretaryListRes?.data?.data?.data ?? [];
  const totalCount = branchSecretaryListRes?.data?.data?.total ?? 0;
  const branchList = branchListRes?.data?.data ?? null;

  const branchListOptions = useMemo(
    () =>
      branchList?.map(({ id, name }) => ({
        value: String(id),
        label: name,
      })) || [],
    [branchList]
  );

  const getBranchNo = useCallback(
    (branchId: string | number) =>
      branchList?.find(({ id }) => id === branchId)?.branchNo ?? "",
    [branchList]
  );

  const handleChangePage = (newPage: number) => setValue("page", newPage);

  const handleSearchSecretaryName = useCallback(
    debounce(
      ({
        target: { value },
      }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setValue("secretaryName", value || undefined);
      },
      1000
    ),
    []
  );

  return (
    <Box>
      <Box
        sx={{
          backgroundColor: "white",
          borderRadius: "16px",
          p: { xs: 3, sm: 4 },
          boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.05)",
          marginTop: 2,
        }}
      >
        <Box
          sx={{
            display: "flex",
            gap: 2,
            mb: 3,
            mx: { xs: 2, md: "auto" },
            maxWidth: "600px",
            width: "100%",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Box
            sx={{
              display: "flex",
              flex: 1,
              backgroundColor: "#F8F8F8",
              borderRadius: "8px",
              alignItems: "center",
              px: 1,
              border: "1px solid #EAEAEA",
              width: "100%",
            }}
          >
            <Search sx={{ color: "#666666", mr: 1 }} />
            <TextField
              placeholder={t("secretaryName")}
              variant="standard"
              fullWidth
              InputProps={{
                disableUnderline: true,
              }}
              sx={{
                "& input": {
                  p: "8px 0",
                },
              }}
              onChange={handleSearchSecretaryName}
            />
          </Box>
        </Box>

        <Box
          sx={{
            maxWidth: 650,
            mx: "auto",
          }}
        >
          <DataTable
            columns={columns}
            rows={branchSecretaryList}
            page={page}
            rowsPerPage={pageSize}
            totalCount={totalCount}
            onPageChange={handleChangePage}
            pagination={false}
            isLoading={isLoadingSecretaryBranchList}
          />
        </Box>
      </Box>

      <Box
        sx={{
          backgroundColor: "white",
          borderRadius: "16px",
          p: { xs: 3, sm: 4 },
          boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.05)",
          marginTop: 2,
          display: "flex",
          justifyContent: "center",
        }}
      >
        <Box
          sx={{
            p: { xs: 2, sm: 3 },
            width: "100%",
            maxWidth: "900px",
            backgroundColor: "none",
            border: "1px solid #EAEAEA",
            borderRadius: "12px",
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            Maklumat cawangan
          </Typography>

          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              paddingLeft: 10,
              paddingRight: 10,
              paddingTop: 3,
              gap: 2,
            }}
          >
            {isLoadingBranchList ? (
              <CircularProgress sx={{ marginInline: "auto" }} />
            ) : (
              <>
                <SelectFieldController
                  control={control}
                  name="selectedBranch"
                  options={branchListOptions}
                  placeholder={t("namaCawangan")}
                  sx={{
                    height: "56px",
                    backgroundColor: "white",
                    borderRadius: "8px",
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#E0E0E0",
                    },
                    "& .MuiInputBase-input": {
                      fontSize: "1rem",
                    },
                  }}
                />

                <TextField
                  fullWidth
                  placeholder={t("branchNumber")}
                  value={getBranchNo(selectedBranch)}
                  disabled
                  sx={{
                    backgroundColor: "#F5F5F5",
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "8px",
                    },
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#E0E0E0",
                    },
                  }}
                />
              </>
            )}

            <Box sx={{ display: "flex", justifyContent: "center", mt: 6 }}>
              <ButtonPrimary
                sx={{
                  backgroundColor: "var(--primary-color)",
                  color: "white",
                  "&:hover": { backgroundColor: "var(--primary-color)" },
                  textTransform: "none",
                  minWidth: "200px",
                  borderRadius: "8px",
                }}
                onClick={() => {
                  navigate(
                    `/pertubuhan/society/${id}/senarai/cawangan/pembaharuan/pelantikan/${selectedBranch}`,
                    {
                      state: {
                        branchId: selectedBranch,
                      },
                    }
                  );
                }}
              >
                Seterusnya
              </ButtonPrimary>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default PembaharuanSetiausahaCawangan;
