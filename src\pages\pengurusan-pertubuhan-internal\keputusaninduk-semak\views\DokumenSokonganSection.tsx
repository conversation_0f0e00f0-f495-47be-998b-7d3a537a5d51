import { useParams } from "react-router-dom";

import FileUploader from "@/components/input/fileUpload";
import { DocumentUploadType } from "@/helpers";

export const DokumenSokonganSection = () => {
  const { id } = useParams();
  const decodedId = atob(id || "");

  return (
    decodedId && (
      <FileUploader
        type={DocumentUploadType.SUPPORTING_DOCUMENT}
        societyId={decodedId}
        disabled={true}
        validTypes={[
          "text/plain",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/msword",
          "application/pdf",
        ]}
      />
    )
  );
};

export default DokumenSokonganSection;
