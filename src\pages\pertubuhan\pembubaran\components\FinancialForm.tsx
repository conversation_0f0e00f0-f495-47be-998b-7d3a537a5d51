import { useState } from "react";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useSenaraiContext } from "../../SenaraiContext";
import { usePembubaranContext } from "../PembubaranProvider";
import { useLocation } from "react-router-dom";
import { FieldValues, useForm, useFormContext } from "react-hook-form";
import {
  globalStyles,
  DocumentUploadType,
  useQuery,
  useMutation,
} from "@/helpers";

import {
  Box,
  Typography,
  Grid,
  FormControlLabel,
  Checkbox,
  FormHelperText,
  CircularProgress,
} from "@mui/material";
import {
  FormFieldRow,
  DisabledTextField,
  Label,
  ButtonPrimary,
  FileUploader,
  CustomSkeleton,
  ButtonOutline,
} from "@/components";

import {
  IApiResponse,
  IStatementFinancial,
  ISubmittedStatement,
} from "@/types";

const FinancialForm: React.FC = () => {
  const location = useLocation();
  const { t, i18n } = useTranslation();
  const { id: societyId } = useParams();

  const isMyLanguage = i18n.language === "my";
  const branchId = location.state?.branchId ?? null;

  const { handleNextPembubaran: handleNext, handleBackPembubaran: handleBack } =
    useSenaraiContext();
  const { isViewOnly } = usePembubaranContext();
  const { getValues: liquidationFormValues } = useFormContext();

  const classes = globalStyles();
  const currentLanguage = i18n.language;

  const [statementId, setStatementId] = useState<string | number | undefined>(
    undefined
  );

  const {
    data: financialStatementRes,
    isLoading: isLoadingFinancialStatement,
    refetch: fetchStatementFinancial,
  } = useQuery<IApiResponse<IStatementFinancial>>({
    url: "society/statement/statement-financial/get",
    autoFetch: false,
  });

  const { fetch: updateLiquidation, isLoading: isUpdatingLiquidation } =
    useMutation({
      url: "society/liquidate/update",
      method: "put",
      onSuccess: () => {
        handleNext();
      },
      msgSuccess: isMyLanguage ? "Berjaya Dikemaskini" : "Successfully Updated",
    });

  const {
    data: submittedStatementRes,
    isLoading: isLoadingSubmittedStatement,
  } = useQuery<IApiResponse<ISubmittedStatement[]>>({
    url: "society/statement/submittedStatements",
    filters: [
      {
        field: "societyId",
        operator: "eq",
        value: liquidationFormValues().societyId,
      },
      {
        field: "branchId",
        operator: "eq",
        value: branchId ?? undefined,
      },
    ],
    onSuccess: (res) => {
      const data = res?.data?.data;

      const submittedItems = data?.filter((item) => item.submitted) ?? [];
      const latestSubmittedStatement =
        submittedItems.length > 0
          ? submittedItems.reduce((latest, current) =>
              current.year > latest.year ? current : latest
            )
          : null;

      if (latestSubmittedStatement) {
        fetchStatementFinancial({
          filters: [
            {
              field: "societyId",
              operator: "eq",
              value: latestSubmittedStatement.societyId,
            },
            {
              field: "branchId",
              operator: "eq",
              value: latestSubmittedStatement.branchId ?? undefined,
            },
            {
              field: "statementId",
              operator: "eq",
              value: latestSubmittedStatement.statementId,
            },
          ],
        });
        setStatementId(latestSubmittedStatement.statementId);
      }
    },
  });

  const listSubmittedStatement = submittedStatementRes?.data?.data ?? [];
  const statementFinancialData = financialStatementRes?.data?.data ?? null;

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<FieldValues>({
    defaultValues: {
      document: "",
    },
  });

  const onSubmit = () => {
    if (isViewOnly) return handleNext();

    const { id } = liquidationFormValues();

    const payload = {
      id,
      statementId,
    };

    updateLiquidation(payload);
  };

  if (isLoadingSubmittedStatement || isLoadingFinancialStatement)
    return <CustomSkeleton number={3} />;

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box className={classes.section} mb={2}>
        <Box className={classes.sectionBox} mb={1}>
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight={500}
            mb="20px"
          >
            {currentLanguage === "my"
              ? "Senarai Penyata dihantar"
              : "List of Statements sent"}
          </Typography>

          <Grid container spacing={2} sx={{ mt: 2, mb: 4 }}>
            {listSubmittedStatement?.map((year) => (
              <Grid item xs={3} key={year.year} sx={{ display: "flex" }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={year.submitted}
                      sx={{
                        color: "#00bcd4",
                        "&.Mui-checked": {
                          color: "#00bcd4",
                        },
                        p: 0.5,
                      }}
                    />
                  }
                  label={<Label text={year.year} />}
                  labelPlacement="start"
                  sx={{
                    flex: 1,
                    m: 0,
                    justifyContent: "center",
                  }}
                />
              </Grid>
            ))}
          </Grid>
        </Box>
      </Box>

      <Box className={classes.section}>
        <Box className={classes.sectionBox} mb={2}>
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight={500}
          >
            Maklumat Kewangan
          </Typography>
        </Box>

        <Box className={classes.sectionBox} mb={2}>
          <FormFieldRow
            label={
              <Typography
                fontSize="14px"
                color="var(--primary-color)"
                fontWeight={500}
              >
                {t("totalIncome")}
              </Typography>
            }
            value={
              <DisabledTextField
                value={statementFinancialData?.totalIncome || 0}
              />
            }
          />
        </Box>

        <Box className={classes.sectionBox} mb={2}>
          <FormFieldRow
            label={
              <Typography
                fontSize="14px"
                color="var(--primary-color)"
                fontWeight={500}
              >
                {t("jumlahPerbelanjaan")}
              </Typography>
            }
            value={
              <DisabledTextField
                value={statementFinancialData?.totalExpense || 0}
              />
            }
          />
        </Box>

        <Box className={classes.sectionBox} mb={2}>
          <FormFieldRow
            label={
              <Typography
                fontSize="14px"
                color="var(--primary-color)"
                fontWeight={500}
              >
                {t("totalAssets")}
              </Typography>
            }
            value={
              <DisabledTextField
                value={statementFinancialData?.totalAsset || 0}
              />
            }
          />
        </Box>

        <Box className={classes.sectionBox} mb={2}>
          <FormFieldRow
            label={
              <Typography
                fontSize="14px"
                color="var(--primary-color)"
                fontWeight={500}
              >
                {currentLanguage === "my"
                  ? "Jumlah libailiti"
                  : "Total Liability"}
              </Typography>
            }
            value={
              <DisabledTextField
                value={statementFinancialData?.totalLiability || 0}
              />
            }
          />
        </Box>

        <Box className={classes.sectionBox} mb={2}>
          <Box
            sx={{
              mb: "20px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight={500}
            >
              {currentLanguage === "my"
                ? "Muat Naik Penyata Kewangan"
                : "Upload Financial Statements"}
            </Typography>
            {errors && errors.document && (
              <FormHelperText sx={{ color: "red", fontSize: "12px" }}>
                {errors?.document?.message as string}
              </FormHelperText>
            )}
          </Box>

          {statementId && (
            <FormFieldRow
              align="flex-start"
              label={
                <Label
                  sx={{ marginTop: "16px" }}
                  text={
                    currentLanguage === "my"
                      ? "Penyata Kewangan yang telah selesai diaudit"
                      : "Completed audited financial statements"
                  }
                />
              }
              value={
                <FileUploader
                  // title="addSupportingDocument"
                  type={DocumentUploadType.SUPPORTING_DOCUMENT}
                  disabled
                  societyId={societyId}
                  statementId={statementId}
                  sxContainer={{
                    border: "2px dashed #ccc",
                    background: "#fff",
                    mb: 3,
                  }}
                  maxFileSize={25 * 1024 * 1024}
                  validTypes={[
                    // "text/plain",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "application/msword",
                    "application/pdf",
                  ]}
                />
              }
            />
          )}
        </Box>

        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 1,
          }}
        >
          <ButtonOutline onClick={handleBack}>{t("back")}</ButtonOutline>

          <ButtonPrimary type="submit" disabled={isUpdatingLiquidation}>
            {isUpdatingLiquidation && <CircularProgress size={10} />}
            {t("next")}
          </ButtonPrimary>
        </Box>
      </Box>
    </form>
  );
};

export default FinancialForm;
