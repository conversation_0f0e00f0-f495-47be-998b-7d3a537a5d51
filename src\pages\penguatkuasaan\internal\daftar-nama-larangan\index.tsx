import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Outlet, Route, Routes, useNavigate } from "react-router-dom";
import { PORTAL_INTERNAL, useQuery } from "@/helpers";
import { NavbarEnforcement } from "@/components/navbar/Enforcement";
import { Box, Container, Drawer, Grid } from "@mui/material";
import { LaranganTab } from "./laranganTab";
import { LaranganPaper } from "./component/LaranganPaper";
import { useLaranganContext } from "@/contexts/laranganProvider";
import { SenaraiNamaLarangan } from "./senaraiNamaLarangan";
import { SenaraiLogoLarangan } from "./senaraiLogoLarangan";
import { TambahRekodLarangan } from "./tambahRekodLarangan";

const DaftarNamaLarangan: React.FC = () => {
  const params = new URLSearchParams(window.location.search);
  const tab = params.get("tab");
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const {activeLaranganContent, setActiveLaranganContent} = useLaranganContext();
  const [page, setPage] = useState(1);


   // Map URL paths to tab indices
  useEffect(() => {
    const pathToTabMap: Record<string, number> = {
      '/penguatkuasaan/daftar_nama_larangan/senarai_nama_larangan': 0,
      '/penguatkuasaan/daftar_nama_larangan/senarai_logo_larangan': 1,
      '/penguatkuasaan/daftar_nama_larangan/tambah_rekod': 2,
    };

    const currentTab = pathToTabMap[location.pathname];
    if (currentTab !== undefined) {
      setActiveLaranganContent(currentTab);
    }
  }, [location.pathname, setActiveLaranganContent]);

  return (
    <>
    <LaranganTab />
      {/* <Grid
        sx={{
          display: "grid",
          gap: 2,
        }}
      >
        <Grid item md={12.5}>
          <LaranganPaper>{LaranganTab()}</LaranganPaper>
        </Grid>
        <Grid item md={12.5}>
          <Outlet />
            {renderContent()}
        </Grid>
      </Grid> */}
    </>
  );
};

export default DaftarNamaLarangan;
