import { Box, Switch, Typography } from "@mui/material";
import { LaranganBox } from "./component/LaranganBorder";
import { LaranganPaper } from "./component/LaranganPaper";
import TakwimInput from "@/components/input/TakwimInput";
import TakwimTextField from "@/components/input/TakwimTextField";
import TakwimSelect from "@/components/input/TakwimSelect";
import { ButtonPrimary } from "@/components";
import DialogActionFlow from "@/components/dialog/confirm/DialogActionFlow";
import { useEffect, useState } from "react";
import { NamaLarangan } from "@/types/larangan/namaLarangan";
import { Api } from "@mui/icons-material";
import TextareaWithLabel from "@/components/input/TextareaWithLabel";

interface TabNamaLaranganData {
  id: number;
  readAccess: boolean;
  createAccess: boolean;
  updateAccess: boolean;
  deleteAccess: boolean;
  mode: string | null;
  isEditMode: boolean;
  isDisabled: boolean;
}
export const TabNamaTerlarang = ({
  id,
  readAccess,
  createAccess,
  updateAccess,
  deleteAccess,
  mode,
  isEditMode,
  isDisabled,
}: TabNamaLaranganData) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [customDialogMessage, setCustomDialogMessage] = useState<string | null>(
    null
  );
  const [namaLaranganFormData, setNamaLaranganFormData] = useState<
    Partial<NamaLarangan>
  >({
    // activeRemarks: "",
    // remarks: "",
    // status: false,
    // forbiddenType: "",
    // keyword: "",
    // createdDate: "",
  });

  const handleConfirm = async () => {
    handleSave();
  };

  const handleSave = async () => {
    const namaLarangan: Partial<NamaLarangan> = {
      activeRemarks: namaLaranganFormData.activeRemarks,
      remarks: namaLaranganFormData.remarks,
      status: namaLaranganFormData.status,
      forbiddenType: namaLaranganFormData.forbiddenType,
      keyword: namaLaranganFormData.keyword,
      createdDate: namaLaranganFormData.createdDate,
    };
    console.log(namaLarangan, "NAMA LARANGAN DATA");

    if (isEditMode) {
      try {
        // call Api
      } catch {
        setCustomDialogMessage("Ralat semasa menyimpan data");
      }
    } else {
      // create new data
    }
  };
  const forbiddenType = [
    { value: "SENARAI_LARANGAN", label: "Senarai Larangan" },
    { value: "SENARAI_KELABU", label: "Senari Kelabu" },
  ];
  function handleFormChange<K extends keyof typeof namaLaranganFormData>(
    key: K,
    value: (typeof namaLaranganFormData)[K]
  ) {
    setNamaLaranganFormData((prev) => ({
      ...prev,
      [key]: value,
    }));
  }

  return (
    <>
      <LaranganPaper>
        <LaranganBox
          sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Box>
            <Typography
              sx={{
                color: "primary.main",
              }}
            >
              Aktivasi Kata Kunci Terlarang
            </Typography>
          </Box>
          <Box>
            <Switch
              onChange={(e) => handleFormChange("status", e.target.checked)}
              disabled={isDisabled}
            />
          </Box>
        </LaranganBox>

        <LaranganBox>
          <Typography
            sx={{
              color: "primary.main",
            }}
          >
            Butiran
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: 2,
            }}
          >
            <TakwimInput
              label="Kata Kunci"
              value={namaLaranganFormData?.keyword}
              disabled={isDisabled}
              onChange={(e) => {
                setNamaLaranganFormData((prev) => ({
                  ...prev,
                  keyword: e.target.value,
                }));
              }}
            />
            <TextareaWithLabel
              label="Catatan"
              value={namaLaranganFormData.remarks}
              disabled={isDisabled}
              onChange={(e) => {
                handleFormChange("remarks", e.target.value);
              }}
            />
            <TakwimSelect
              label="Jenis Larangan"
              required
              disabled={isDisabled}
              value={namaLaranganFormData?.forbiddenType}
              onChange={(e) => {
                handleFormChange("forbiddenType", e.target.value);
              }}
              options={forbiddenType}
            />
            <TakwimInput
              label="Tarikh dan Masa Terbit"
              value=""
              // onChange={() => {}}
              // disabled
            />
          </Box>
        </LaranganBox>
        <LaranganBox>
          <Typography
            sx={{
              color: "primary.main",
            }}
          >
            Catatan Status
          </Typography>
          <TextareaWithLabel
            disabled={isDisabled}
            label="Catatan Status"
            value={namaLaranganFormData.activeRemarks}
            onChange={(e) => {
              handleFormChange("activeRemarks", e.target.value);
            }}
          />
        </LaranganBox>
        <Box sx={{ p: "15px", textAlign: "right" }}>
          {!isDisabled && (
            <ButtonPrimary
              sx={{
                fontWeight: 300,
              }}
              onClick={() => setOpenDialog(true)}
            >
              Kemaskini
            </ButtonPrimary>
          )}
        </Box>
      </LaranganPaper>
      <DialogActionFlow
        open={openDialog}
        onClose={() => {
          setOpenDialog(false);
          setCustomDialogMessage(null);
        }}
        onConfirm={customDialogMessage ? async () => {} : handleConfirm}
        hideOnError={false}
        confirmationText={
          customDialogMessage
            ? customDialogMessage
            : "Adakah anda pasti untuk menyimpan nama larangan ini?"
        }
        successMessage={
          customDialogMessage
            ? customDialogMessage
            : "Nama larangan berjaya dipadam"
        }
      />
    </>
  );
};
