import { Box, Switch, Typography } from "@mui/material";
import { LaranganBox } from "./component/LaranganBorder";
import { LaranganPaper } from "./component/LaranganPaper";
import TakwimInput from "@/components/input/TakwimInput";
import TakwimTextField from "@/components/input/TakwimTextField";
import TakwimSelect from "@/components/input/TakwimSelect";
import { ButtonPrimary } from "@/components";
import DialogActionFlow from "@/components/dialog/confirm/DialogActionFlow";
import { useEffect, useState } from "react";
import { NamaLarangan } from "@/types/larangan/namaLarangan";
import { Api } from "@mui/icons-material";
import TextareaWithLabel from "@/components/input/TextareaWithLabel";
import { ApiResponse, laranganService } from "@/services/laranganService";
import { useNavigate } from "react-router-dom";
import dayjs from "dayjs";
import { useNotification } from "@refinedev/core";
import { LoadingOverlay } from "@/components/loading";

interface TabNamaLaranganData {
  id: number | string | null;
  readAccess: boolean;
  createAccess: boolean;
  updateAccess: boolean;
  deleteAccess: boolean;
  mode: string | null;
  isEditMode: boolean;
  isDisabled: boolean;
}
export const TabNamaTerlarang = ({
  id,
  readAccess,
  createAccess,
  updateAccess,
  deleteAccess,
  mode,
  isEditMode,
  isDisabled,
}: TabNamaLaranganData) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [errors, setErrors] = useState<any>({
    keyword: "",
    remarks: "",
    forbiddenType: "",
  });
  const [currentDateTime, setCurrentDateTime] = useState(
    dayjs().format("DD-MM-YYYY HH:mm:ss")
  );
  const { open: openNotification } = useNotification();
  const navigate = useNavigate();
  const [customDialogMessage, setCustomDialogMessage] = useState<
    string | null | undefined
  >(null);
  const [namaLaranganFormData, setNamaLaranganFormData] = useState<
    Partial<NamaLarangan>
  >({
    // activeRemarks: "",
    // remarks: "",
    // status: false,
    // forbiddenType: "",
    // keyword: "",
    // createdDate: "",
  });
  useEffect(() => {
    if (!id) {
      const interval = setInterval(() => {
        setCurrentDateTime(dayjs().format("DD-MM-YYYY HH:mm:ss"));
      }, 1000);

      return () => clearInterval(interval);
    }
  }, []);

  useEffect(() => {
    if (id) {
      fetchNamaLaaranganById(id);
    }
  }, [isEditMode]);

  const fetchNamaLaaranganById = async (id: number | string) => {
    try {
      setIsLoadingData(true);
      const response = await laranganService.getNamaLaranganById(id);
      if (response.code === 404) {
        openNotification?.({
          type: "error",
          message: "Error",
          description: response.msg,
        });
        setTimeout(() => {
          navigate(
            "/penguatkuasaan/daftar_nama_larangan/senarai_nama_larangan"
          );
        }, 5000);
      }
      setNamaLaranganFormData(response?.data);
      setCurrentDateTime(
        dayjs(response?.data?.createdDate).format("DD-MM-YYYY HH:mm")
      );
      // console.log(response?.data, "NAMA LARANGAN DATA");
    } catch (error) {
      openNotification?.({
        type: "error",
        message: "Error",
        description: "Failed to fetch nama larangan",
      });
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleConfirm = async () => {
    await handleSave();
  };

  const validateForm = () => {
    const valid = true;
    const errors: { [key: string]: string } = {};
    if (!namaLaranganFormData.keyword) {
      errors.keyword = "Sila isi kata kunci";
    }
    if (!namaLaranganFormData.forbiddenType) {
      errors.forbiddenType = "Sila pilih jenis larangan";
    }

    setErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const forbiddenType = [
    { value: "SENARAI_LARANGAN", label: "Senarai Larangan", tab: "larangan" },
    { value: "SENARAI_KELABU", label: "Senari Kelabu", tab: "kelabu" },
  ];
  const handleSave = async () => {
    const namaLarangan: Partial<NamaLarangan> = {
      activeRemarks: namaLaranganFormData.activeRemarks,
      remarks: namaLaranganFormData.remarks,
      forbiddenType: namaLaranganFormData.forbiddenType,
      keyword: namaLaranganFormData.keyword,
      createdDate: currentDateTime,
      active: namaLaranganFormData.active ?? false,
    };
    // return;

    if (!validateForm()) {
      setOpenDialog(false);
      openNotification?.({
        type: "error",
        message: "Sila lengkapkan maklumat",
        description: "Sila lengkapkan maklumat",
      });
      return;
    }

    if (isEditMode) {
      // edit
      try {
        const res: ApiResponse<any> = await laranganService.updateLarangan(
          namaLarangan,
          id
        );
        if (res.code === 200) {
          setCustomDialogMessage(res?.msg);
          // navigate(
          //   `/penguatkuasaan/daftar_nama_larangan/senarai_nama_larangan?tab=${
          //     forbiddenType.find(
          //       (item) => item.value === namaLaranganFormData.forbiddenType
          //     )?.tab
          //   }`
          // );
        } else {
          throw new Error(res.msg || "Failed to update larangan");
        }
      } catch {
        setCustomDialogMessage("Ralat semasa menyimpan data edit");
      }
    } else {
      // create new data
      try {
        // call Api
        const res: ApiResponse<any> = await laranganService.createLarangan(
          namaLarangan
        );
        if (res.code === 201) {
          setCustomDialogMessage(res?.msg);
          navigate(
            `/penguatkuasaan/daftar_nama_larangan/senarai_nama_larangan?tab=${
              forbiddenType.find(
                (item) => item.value === namaLaranganFormData.forbiddenType
              )?.tab
            }`
          );
        } else {
          throw new Error(res.msg || "Failed to create larangan");
        }
      } catch {
        setCustomDialogMessage("Ralat semasa menyimpan data create");
      }
    }
  };
  function handleFormChange<K extends keyof typeof namaLaranganFormData>(
    key: K,
    value: (typeof namaLaranganFormData)[K]
  ) {
    setNamaLaranganFormData((prev) => ({
      ...prev,
      [key]: value,
    }));
  }

  return (
    <>
      <LaranganPaper>
        {isLoadingData && <LoadingOverlay />}
        <LaranganBox
          sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Box>
            <Typography
              sx={{
                color: "primary.main",
              }}
            >
              Aktivasi Kata Kunci Terlarang
            </Typography>
          </Box>
          <Box>
            <Switch
              onChange={(e) => handleFormChange("active", e.target.checked)}
              disabled={isDisabled}
              checked={namaLaranganFormData.active}
            />
          </Box>
        </LaranganBox>

        <LaranganBox>
          <Typography
            sx={{
              color: "primary.main",
            }}
          >
            Butiran
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: 2,
            }}
          >
            <TakwimInput
              label="Kata Kunci"
              value={namaLaranganFormData?.keyword}
              disabled={isDisabled}
              onChange={(e) => {
                setNamaLaranganFormData((prev) => ({
                  ...prev,
                  keyword: e.target.value,
                }));
              }}
              error={!!errors.keyword}
            />
            <TextareaWithLabel
              label="Catatan"
              value={namaLaranganFormData?.remarks}
              disabled={isDisabled}
              onChange={(e) => {
                handleFormChange("remarks", e.target.value);
              }}
            />
            <TakwimSelect
              label="Jenis Larangan"
              required
              disabled={isDisabled}
              value={namaLaranganFormData?.forbiddenType}
              onChange={(e) => {
                handleFormChange("forbiddenType", e.target.value);
              }}
              error={!!errors.forbiddenType}
              options={forbiddenType}
            />
            <TakwimInput
              label="Tarikh dan Masa Terbit"
              value={currentDateTime}
              // onChange={() => {}}
              disabled
            />
          </Box>
        </LaranganBox>
        <LaranganBox>
          <Typography
            sx={{
              color: "primary.main",
            }}
          >
            Catatan Status
          </Typography>
          <TextareaWithLabel
            disabled={isDisabled}
            label="Catatan Status"
            value={namaLaranganFormData?.activeRemarks}
            onChange={(e) => {
              handleFormChange("activeRemarks", e.target.value);
            }}
          />
        </LaranganBox>
        <Box sx={{ p: "15px", textAlign: "right" }}>
          {!isDisabled && (
            <ButtonPrimary
              sx={{
                fontWeight: 300,
              }}
              onClick={() => setOpenDialog(true)}
            >
              Kemaskini
            </ButtonPrimary>
          )}
        </Box>
      </LaranganPaper>
      <DialogActionFlow
        open={openDialog}
        onClose={() => {
          setOpenDialog(false);
          setCustomDialogMessage(null);
        }}
        onConfirm={async () => await handleConfirm()}
        hideOnError={false}
        confirmationText={
          customDialogMessage
            ? customDialogMessage
            : "Adakah anda pasti untuk menyimpan nama larangan ini?"
        }
        successMessage={
          customDialogMessage
            ? customDialogMessage
            : "Nama larangan berjaya disimpan"
        }
      />
    </>
  );
};
