import React, { useEffect, useState } from "react";
import { Box, Typography, Fade } from "@mui/material";
import { useNavigate } from "react-router-dom";
import CreateDokumenSokonganDialog from "./CreateDokumenSokonganDialog";
import ButtonPrimary from "../../../../components/button/ButtonPrimary";
import { useTranslation } from "react-i18next";
import { OrganizationStepper } from "../organization-stepper";
import useQuery from "../../../../helpers/hooks/useQuery";
import { API_URL } from "../../../../api";
import { ApplicationStatus, ROApprovalType } from "../../../../helpers/enums";
import { DocumentUploadType } from "@/helpers";
import { useCustomMutation } from "@refinedev/core";
import ConfirmationDialog from "../../../../components/dialog/confirm";
import InfoQACard from "../InfoQACard";
import { useSelector } from "react-redux";
import FileUploader from "@/components/input/fileUpload";
import { useCheckAndUpdateRegistration } from "@/helpers/hooks/useCheckAndupdateRegister";

export const ListDokumenSokongan: React.FC = () => {
  const navigate = useNavigate();
  const [dialogSaveOpen, setDialogSaveOpen] = useState(false);
  const [activeStep, setActiveStep] = useState(4);
  const { t } = useTranslation();
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");
  const { data } = useQuery({
    url: "document/documentByParam",
    filters: [
      {
        field: "societyId",
        operator: "eq",
        value: atob(encodedId || ""),
      },
      {
        field: "type",
        operator: "eq",
        value: DocumentUploadType.SUPPORTING_DOCUMENT,
      },
    ],
  });
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const { mutate: editSocietyPaymentMethod } = useCustomMutation();
  const decodedId = atob(encodedId || "");
  const editSociety = () => {
    if (decodedId) {
      const data = {
        applicationStatusCode: ApplicationStatus.MENUNGGU_KEPUTUSAN,
      };
      editSocietyPaymentMethod(
        {
          method: "patch",
          url: `${API_URL}/society/roDecision/updateQuery`,
          values: {
            roApprovalType: ROApprovalType.SOCIETY_REGISTRATION.code,
            societyId: parseInt(decodedId),
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
        },
        {
          onSuccess() {},
        }
      );
      editSocietyPaymentMethod(
        {
          method: "put",
          url: `${API_URL}/society/${decodedId}/edit`,
          values: data,
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data: any) => {
            return {
              message: data?.data?.msg,
              type: "success",
            };
          },
          errorNotification: (data: any) => {
            return {
              message: data?.data?.msg,
              type: "error",
            };
          },
        },
        {
          onSuccess() {
            navigate(
              `/pertubuhan/society/${atob(encodedId || "")}/senarai/maklumat`
            );
          },
        }
      );
    }
  };

  useEffect(() => {
    if (data?.data?.data) {
      const documents = data.data.data.filter(
        (doc: any) => doc.type === "dokumen"
      );
      setUploadedFiles(documents);
    }
  }, [data]);

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { data: checkAndUpdateRegistrationData, fetch: fetchCheck } =
    useCheckAndUpdateRegistration({
      id: societyDataRedux?.id,
      pageNo: 5,
      enabled: false,
      onSuccessNotification: (data) => {
        const responseData = data?.data?.data;
        //  const message = data?.data?.msg
        //  if(!responseData){
        //   return {
        //     message: message,
        //     type: "error",
        //   };
        //  }
      },
      onSuccess: (data) => {
        const responseData = data?.data?.data;
        if (responseData) {
          navigate(`../bayaran?id=${encodedId}`);
        }
      },
    });

  const handleGoNext = () => {
    fetchCheck();
  };

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box sx={{ width: "55vw" }}>
        <Fade in={true} timeout={500}>
          <Box>
            <FileUploader
              title="addSupportingDocument"
              info="societyDocumentInfo"
              type={DocumentUploadType.SUPPORTING_DOCUMENT}
              societyId={parseInt(decodedId)}
              societyNo={societyDataRedux.societyNo}
              sxContainer={{
                border: "2px dashed #ccc",
                background: "#fff",
                mb: 3,
              }}
              maxFileSize={25 * 1024 * 1024}
              validTypes={[
                "text/plain",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/msword",
                "application/pdf",
              ]}
            />

            <ButtonPrimary
              sx={{ display: "block", ml: "auto" }}
              onClick={() => {
                if (
                  societyDataRedux.applicationStatusCode ==
                  ApplicationStatus.KUIRI
                ) {
                  setOpenConfirm(true);
                } else {
                  handleGoNext();
                }
              }}
            >
              {societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI
                ? t("hantar")
                : t("next")}
            </ButtonPrimary>
          </Box>
        </Fade>
        <ConfirmationDialog
          status={1}
          open={openConfirm}
          onClose={() => setOpenConfirm(false)}
          title="Hantar permohonan?"
          message="Adakah anda pasti untuk menghantar permohonan ini?"
          onConfirm={editSociety}
          onCancel={() => setOpenConfirm(false)}
        />
        <CreateDokumenSokonganDialog
          open={dialogSaveOpen}
          onClose={() => setDialogSaveOpen(false)}
        />
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <OrganizationStepper
          activeStep={activeStep}
          hidePayment={
            societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI
          }
        />
        {societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI ? (
          <Box
            sx={{
              padding: 3,
              backgroundColor: "white",
              borderRadius: "15px",
              maxHeight: "60vh",
              maxWidth: "18vw",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("kuiri")}
            </Typography>
            <Box
              sx={{
                padding: 3,
                backgroundColor: "#DADADA",
                borderRadius: "15px",
                maxHeight: "60vh",
                maxWidth: "18vw",
              }}
            >
              <Typography
                sx={{
                  mb: 8,
                  fontSize: "12px",
                  color: "#666666",
                  fontWeight: "500 !important",
                }}
              >
                {societyDataRedux.queryText}
              </Typography>
            </Box>
          </Box>
        ) : null}

        <InfoQACard />
      </Box>
    </Box>
  );
};

export default ListDokumenSokongan;
