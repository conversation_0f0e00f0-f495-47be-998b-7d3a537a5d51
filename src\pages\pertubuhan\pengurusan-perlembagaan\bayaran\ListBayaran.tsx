import React, { useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Checkbox from "@mui/material/Checkbox";
import type { SelectChangeEvent } from "@mui/material/Select";

import InputLabel from "@mui/material/InputLabel";
import { useTranslation } from "react-i18next";
import { ButtonPrimary } from "../../../../components/button";
import { Select, Option } from "../../../../components/input";
import { Fade, Grid } from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { ApplicationStatus, PaymentItemCode } from "../../../../helpers/enums";
import { getLocalStorage } from "../../../../helpers/utils";
import { DialogConfirmation } from "@/components";
import { useMutation, useQuery } from "@/helpers";
import usePaymentService from "@/helpers/hooks/usePaymentService";
import { useDispatch } from "react-redux";
import { setCalculatedPayment } from "@/redux/paymentReducer";
import { IPaymentCalculationRequest } from "@/services/paymentService";

function PembayaranCawangan() {
  const { t } = useTranslation();

  const [paymentMethod, setPaymentMethod] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const navigate = useNavigate();

  const [isChecked, setIsChecked] = useState(false);

  // Payment service hooks
  const { calculatePayment, processPayment } = usePaymentService();
  const dispatch = useDispatch();



  const handleChange = (event: SelectChangeEvent) => {
    setPaymentMethod(event.target.value as string);
  };

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsChecked(event.target.checked);
  };

  //const params = new URLSearchParams(window.location.search);
  //const societyId = params.get("id");
  const { id: societyId } = useParams();
  const amendmentId = getLocalStorage("amendmentId", null);

  const { fetchAsync: updateData, isLoading: isLoadingUpdateAmendment } =
    useMutation({
      url: `society/amendment/${amendmentId}/checkAndUpdate`,
      method: "put",
      // msgSuccess: "Permohonan berjaya dihantar.",
      onSuccessNotification: (data) => {
        return {
          message: data?.data?.msg,
          type: "success",
        };
      },
      onErrorNotification: () => {
        return {
          message: t("error"),
          type: "error",
        };
      },
      async onSuccess() {
        // First, call calculate API to get the latest payment calculation
        const calculateRequest: IPaymentCalculationRequest = {
          items: [
            {
              itemCode: paymentMethod === "online"
                ? PaymentItemCode.PINDAAN_UNDANG_UNDANG_ONLINE
                : PaymentItemCode.PINDAAN_UNDANG_UNDANG_KAUNTER,
              quantity: 1,
            },
          ],
        };

        try {
          const calculateResponse = await calculatePayment(calculateRequest);

          if (!calculateResponse?.data) {
            throw new Error("Failed to calculate payment");
          }

          // Store the latest calculated payment in Redux
          dispatch(setCalculatedPayment(calculateResponse.data));

          // Proceed with payment logic using the latest calculated payment
          if (paymentMethod === "online") {
            navigate(`online?societyId=${societyId}`);
          } else {
            await handleKaunterPayment(calculateResponse.data);
          }
        } catch (error) {
          console.error("Error calculating payment:", error);
          // Handle error appropriately - you might want to show an error message to the user
        }
      }
    });

  const updateAmendment = async () => {
    try {
      if (paymentMethod === "online") {
         await updateData(
          {
            applicationStatusCode: ApplicationStatus.MENUNGGU_BAYARAN_ONLINE,
          }
        );
      } else {
        await updateData(
          {
            applicationStatusCode: ApplicationStatus.MENUNGGU_BAYARAN_KAUNTER,
          }
        );
      }
    } finally {
      handleCloseDialog();
    }
  };

  const handleKaunterPayment = async (calculatedPayment: any) => {
    const getUserDetails = localStorage.getItem("user-details");
    const email = getUserDetails ? JSON.parse(getUserDetails).email : "";

    const processPaymentRequest = {
      societyId: parseInt(societyId as string),
      amendmentId: parseInt(amendmentId),
      amount: calculatedPayment.totalAmount,
      email: email,
      signature: calculatedPayment.signature,
    };

    await processPayment(processPaymentRequest);
    navigate(`kaunter?societyId=${societyId}`, {
      state: { amendmentId },
    });
  };

  const { data: paymentStatus } = useQuery<{ data: { payment: "string"; alert: boolean } }>({
    url: `society/admin/integration/payment/status`,
  });

  const onlinePaymentEnabled: boolean =
    paymentStatus?.data?.data?.payment?.toLowerCase() == "enabled";
  const alert: boolean = paymentStatus?.data?.data?.alert ?? false;

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const handleSubmit = () => {
    handleOpenDialog();
  };

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Fade in={true} timeout={500}>
        <Box
          sx={{
            backgroundColor: "white",
            border: 1,
            borderColor: "grey.300",
            borderRadius: 4,
            p: 3,
          }}
        >
          <Box
            sx={{
              border: 1,
              borderColor: "grey.300",
              borderRadius: 1,
              p: 2,
              mb: 2,
            }}
          >
            <Box sx={{ mb: 3 }}>
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  color: "#00A7A7",
                  fontSize: 16,
                  fontWeight: 600,
                }}
              >
                {t("payment")}
              </Typography>
            </Box>
            <Typography
              variant="body2"
              sx={{
                color: "#666666",
                fontFamily: "Poppins",
                fontSize: 14,
                fontWeight: 400,
                lineHeight: "21px",
                textAlign: "left",
                mb: 2,
              }}
            >
              {t("agreementText")}
            </Typography>
            <Box
              sx={{
                display: "flex",
                alignItems: "flex-start",
                gap: 1,
              }}
            >
              <Checkbox
                id="akuan-setuju-terima"
                checked={isChecked}
                onChange={handleCheckboxChange}
                sx={{
                  color: "#00A7A7",
                  "&.Mui-checked": {
                    color: "#00A7A7",
                  },
                  padding: "0",
                }}
              />
              <InputLabel
                htmlFor="akuan-setuju-terima"
                required
                sx={{
                  color: "#333333",
                  fontSize: 14,
                  fontWeight: 400,
                  lineHeight: 1.4,
                  "& .MuiFormLabel-asterisk": {
                    color: "#FF0000",
                  },
                }}
              >
                {t("agreementAcceptance")}
              </InputLabel>
            </Box>
          </Box>

          <Box
            sx={{
              border: 1,
              borderColor: "grey.300",
              borderRadius: 1,
              p: 2,
              mb: 3,
            }}
          >
            <Box sx={{ mb: 2 }}>
              <Box sx={{ mb: 1 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: 600,
                  }}
                >
                  {t("paymentMethod")}
                </Typography>
              </Box>
              <Grid
                container
                rowSpacing={1}
                columnSpacing={{ xs: 1, sm: 2, md: 3 }}
              >
                <Grid item xs={4}>
                  {/* <Typography>1</Typography> */}
                </Grid>
                <Grid item xs={8}>
                  <Typography
                    sx={{
                      fontFamily: "Poppins, sans-serif",
                      fontSize: "12px",
                      fontWeight: 500,
                      lineHeight: "14px",
                      color: "#FF0000",
                      marginLeft: "15px",
                    }}
                  >
                    {alert}
                  </Typography>
                </Grid>
                <Grid item xs={4}>
                  <InputLabel
                    htmlFor="cara-pembayaran"
                    required
                    sx={{
                      color: "#333333",
                      fontSize: 14,
                      fontWeight: 400,
                      minWidth: "150px",
                      "& .MuiFormLabel-asterisk": {
                        color: "#FF0000",
                      },
                    }}
                  >
                    {t("paymentMethod")}
                  </InputLabel>
                </Grid>
                <Grid item xs={8}>
                  <Select
                    value={paymentMethod}
                    onChange={handleChange}
                    id="cara-pembayaran"
                    t={t}
                    sx={{
                      width: "100%",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#E5E5E5",
                        borderRadius: 1,
                      },
                    }}
                  >
                    <Option value="kaunter">{t("pembayaranKaunter")}</Option>
                    <Option value="online" disabled={!onlinePaymentEnabled}>{t("pembayaranOnline")}</Option>
                  </Select>
                </Grid>
              </Grid>
            </Box>
            <Typography
              variant="body2"
              sx={{
                color: "#666666",
                fontSize: 12,
                marginTop: 10,
                textAlign: "center",
              }}
            >
              {t("paymentNote")}
            </Typography>
          </Box>

          <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
            <ButtonPrimary
              sx={{
                backgroundColor: "#00A7A7",
                "&:hover": {
                  backgroundColor: "#008F8F",
                },
                color: "white",
                borderRadius: 1,
                textTransform: "none",
              }}
              disabled={!isChecked || !paymentMethod}
              onClick={handleSubmit}
            >
              {t("hantar")}
            </ButtonPrimary>
          </Box>
        </Box>
      </Fade>

      <DialogConfirmation
        open={dialogOpen}
        onClose={handleCloseDialog}
        onConfirmationText={t("confirmSubmitApplication")}
        onAction={updateAmendment}
        onSuccessText="Permohonan berjaya dihantar."
        isMutating={paymentMethod === "online"
          ? isLoadingUpdateAmendment
          : false}
      />
    </Box>
  );
}

export default PembayaranCawangan;
