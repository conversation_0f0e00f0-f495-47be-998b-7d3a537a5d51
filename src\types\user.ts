export interface IUser {
  createdDate: string
  createdBy: number
  modifiedDate: string
  modifiedBy: number
  id: string | number
  name: string
  citizenshipTitle: string | null
  identificationType: number
  identificationNo: string
  profilePicture: string | null
  titleCode: string | null
  gender: string | null
  address: string | null
  cityCode: number | null
  city: string | null
  districtCode: number | null
  stateCode: string
  postcode: string | null
  housePhone: string | null
  mobilePhone: string
  email: string
  status: number
  userGroup: number
  password: string
  passwordChange: boolean | null
  passwordRenew: boolean
  renewDate: string | null
  count: number
  jppmPosition: string | null
  jppmPartCode: number | null
  jppmBranchId: string | number
  admBranch: string | null
  startServiceDate: string | null
  endServiceDate: string | null
  gradeCode: number | null
  roleCode: number | null
  roleId: string | number
  jppmPositionId: string | number
  accActivation: boolean
  accActivationTime: string | null
  flatCompliance: boolean
  lastLogin: string
  remarks: string | null
  newRegistration: string | null
  migrate: string
  notValidated: boolean | null
}
