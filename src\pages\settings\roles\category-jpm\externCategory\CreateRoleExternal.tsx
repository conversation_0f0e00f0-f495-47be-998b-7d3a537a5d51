import {
  Box,
  Checkbox,
  createTheme,
  FormControlLabel,
  FormHelperText,
  Grid,
  RadioGroup,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import SectionHeader from "../../../../../components/header/section/SectionHeader";
import { ButtonPrimary } from "../../../../../components/button";
import { useCallback, useEffect, useState } from "react";
import { GridCheckIcon } from "@mui/x-data-grid";
import Input from "../../../../../components/input/Input";
import {
  CheckboxIcon,
  CheckedIcon,
  CustomRadio,
} from "../../../../../components/input/customRadio";
import dayjs from "dayjs";

const listRoles = [
  {
    name: "<PERSON><PERSON>len<PERSON><PERSON><PERSON>",
    roles: [
      "<PERSON><PERSON><PERSON>",
      "<PERSON> <PERSON><PERSON>",
      "<PERSON> Pertubuhan",
      "Fasal 3 : <PERSON>dang-<PERSON><PERSON>",
      "<PERSON><PERSON>",
      "<PERSON><PERSON>",
    ],
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    roles: [
      "<PERSON>gguna Luar",
      "<PERSON><PERSON><PERSON>gguna Luar",
      "Pengguna JPPM",
      "Kategori Pengguna JPPM",
    ],
  },
  {
    name: "Penyelenggaraan Umum",
    roles: [
      "Email Queue",
      "SMTP Status",
      "Makluman",
      "Cawangan JPPM",
      "Jabatan Insolvensi",
      "Bahagian",
      "Jawatan",
      "Gred",
      "Pekerjaan",
      "Negara",
      "Daerah",
      "Bandar",
      "Agama",
      "Keturunan",
      "Tarikh Cuti Umum",
      "Senarai FAQ",
      "Maklum Balas Sistem",
      "Tetapan Bayaran Online",
    ],
  },
  {
    name: "Audit Trail",
    roles: ["Pengguna Luar", "Pengguna JPPM", "Myldentity"],
  },
  {
    name: "Pengurusan Pertubuhan",
    roles: [
      "Senarai Pertubuhan",
      "Senarai Pindaan",
      "Senarai Pembubaran",
      "Senarai Hitam/Pemutihan",
      "Senarai Rayan",
      "Penvata Tahunan",
      "Penukaran Status",
      "Semakan No Pendaftaran Lama",
    ],
  },
  {
    name: "Carian Berkaitan Pertubuhan",
    roles: [
      "Carian Pegawai Awam",
      "Carian Pegawai Harta",
      "Carian Juruaudit",
      "Carian Pertukaran SU",
      "Carian Bukan Warganegara",
      "Carian Senarai Kelabu",
    ],
  },
  {
    name: "Meja Bantuan",
    roles: [
      "Senarai aduan/maklumat/maklum balas",
      "Senarai aduan/maklumat/maklum balas baru",
      "Kemaskini aduan/maklumat/maklum balas",
      "Pembayaran Kaunter",
      "Rekod Pembayaran",
      "Semakan Kaunter Individu",
    ],
  },
  {
    name: "Kelulusan Pertubuhan",
    roles: [
      "Penubuhan Induk",
      "Penubuhan Menunggu Ulasan Agensi Luar",
      "Kuiri",
      "Penubuhan Cawangan",
      "Kuiri Cawangan",
      "Laniut Masa",
      "Pindaan Perlembagaan",
      "Kuiri Pindaan Undang-undang",
      "Pindaan Nama dan Alamat Cawangan",
      "Pembubaran",
      "Pembubaran Cawangan",
      "Rayuan",
      "Permohonan Bukan Warganegara",
      "Permohonan Bukan Warganegara Cawangan",
      "Pembaharuan SU",
      "Pembaharuan SU - Migrasi",
      "Pembaharuan SU - Kuiri",
      "Pegawai Awam",
      "Pegawai Harta",
      "Pegawai Awam Cawangan",
      "Pegawai Harta Cawangan",
      "Notis Senarai Hitam",
    ],
  },
  {
    name: "Kemaskini Maklumat Cawangan",
    roles: [
      "Senarai Cawangan",
      "Penyata Tahunan Cawangan",
      "Sijil Migrasi Cawangan/Penukaran IC SU Cawangan",
    ],
  },
  {
    name: "Carian Maklumat Cawangan",
    roles: [
      "Pindaan Nama dan Alamat Cawangan",
      "Pegawai Awam",
      "Pegawai Harta",
      "Juruaudit",
      "Pemegang Amanah",
      "Penukaran SU",
      "Bukan Warganegara",
      "Pemegang Jawatan",
    ],
  },
];

interface Role {
  role: string;
  description: string;
}

interface FormValues {
  id?: string | number | null;
  role: string | null;
  description: string | null;
  status: number | null;
  recordedBy: string | null;
  lastUpdated: string | null;
}

function CreateRoleExternal() {
  const { t } = useTranslation();
  const [formValues, setFormValues] = useState<FormValues>({
    id: null,
    role: null,
    description: null,
    status: null,
    recordedBy: null,
    lastUpdated: null,
  });
  const [errors, setErrors] = useState<{ [key in keyof FormValues]?: string }>(
    {}
  );
  const [savedRoles, setSavedRoles] = useState<Role[]>([]);

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  // if (memberId) {
  //   filteredData.id = memberId;
  // }
  // filteredData.branchId = branchId;
  // filteredData.branchNo = branchData.branchNo;
  // filteredData.societyId = branchData.societyId;
  // filteredData.societyNo = branchData.societyNo;
  // filteredData.id = memberId ? memberId : null;
  // filteredData.permissionList = savedRoles;
  // CreateUser(filteredData);

  const handleCheckboxChange = useCallback(
    (
      description: string,
      action: "VIEW" | "UPDATE" | "DELETE" | "CREATE",
      checked: boolean
    ) => {
      const role = `${description}:${action}`;
      setSavedRoles((prev) =>
        checked
          ? [...prev, { role, description }]
          : prev.filter((item) => item.role !== role)
      );
    },
    []
  );

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  }, []);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    const newErrors: { [key in keyof FormValues]?: string } = {};
    const requiredFields = [
      "role",
      "description",
      "status",
      "recordedBy",
      "lastUpdated",
    ] as const;

    requiredFields.forEach((field) => {
      if (!formValues[field]) {
        newErrors[field] = t("requiredField");
      }
    });
    console.log("flows", formValues, newErrors);
    if (Object.keys(newErrors).length === 0) {
      const filteredData = Object.fromEntries(
        Object.entries(formValues).filter(([key, value]) => value !== "")
      );

      console.log("filteredData:", filteredData);
    }
  };

  return (
    <Box
      sx={{ display: "grid", gap: 2 }}
      component="form"
      onSubmit={handleSubmit}
    >
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography className={"title"}>
            {t("addCategoryPenggunaJPPM")}
          </Typography>
          <Box
            sx={{
              mt: 3,
            }}
          >
            <Input
              required
              name="role"
              label={t("kategoriPengguna")}
              value={formValues?.role ? formValues.role : ""}
              onChange={handleChange}
              error={!!errors.role}
              helperText={errors.role}
            />
            <Input
              required
              name="description"
              label={t("descriptionCategory")}
              value={formValues?.description ? formValues.description : ""}
              onChange={handleChange}
              error={!!errors.description}
              helperText={errors.description}
            />
            <Grid container spacing={2} sx={{ pt: 2, pb: 2 }}>
              <Grid
                item
                xs={12}
                sm={4}
                sx={{ display: "flex", alignItems: "center" }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    color: "#666666",
                    fontWeight: "400 !important",
                    fontSize: "14px",
                  }}
                >
                  {t("status")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <RadioGroup
                  value={formValues.status}
                  onChange={handleChange}
                  name="status"
                  row
                >
                  <FormControlLabel
                    value={1}
                    className="label"
                    control={
                      <CustomRadio
                        icon={<CheckboxIcon />}
                        checkedIcon={<CheckedIcon />}
                      />
                    }
                    label={t("active")}
                  />
                  <FormControlLabel
                    value={3}
                    className="label"
                    control={
                      <CustomRadio
                        icon={<CheckboxIcon />}
                        checkedIcon={<CheckedIcon />}
                      />
                    }
                    label={t("inactive")}
                  />
                </RadioGroup>
                {errors.status && (
                  <FormHelperText error>{errors.status}</FormHelperText>
                )}
              </Grid>
            </Grid>
            <Input
              value={
                formValues?.recordedBy
                  ? dayjs(formValues.recordedBy).format("DD-MM-YYYY")
                  : ""
              }
              name="recordedBy"
              type="date"
              label={t("recordedBy")}
              onChange={handleChange}
              error={!!errors.recordedBy}
              helperText={errors.recordedBy}
            />
            <Input
              value={
                formValues?.lastUpdated
                  ? dayjs(formValues.lastUpdated).format("DD-MM-YYYY")
                  : ""
              }
              name="lastUpdated"
              type="date"
              label={t("lastUpdate")}
              onChange={handleChange}
              error={!!errors.lastUpdated}
              helperText={errors.lastUpdated}
            />
          </Box>
        </Box>
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography className={"title"}>
            {t("ketetapanKebenaranAkses")}
          </Typography>
          <Box
            sx={{
              mt: 3,
              display: "grid",
              gap: 2,
            }}
          >
            <Grid container>
              <Grid item xs={4}>
                <Typography
                  sx={{ fontWeight: "500!important" }}
                  className="label"
                >
                  {t("screenList")}
                </Typography>
              </Grid>
              <Grid item xs={2} sx={{ textAlign: "center" }}>
                <Typography
                  sx={{ fontWeight: "500!important" }}
                  className="label"
                >
                  {t("createAdd")}
                </Typography>
              </Grid>
              <Grid item xs={2} sx={{ textAlign: "center" }}>
                <Typography
                  sx={{ fontWeight: "500!important" }}
                  className="label"
                >
                  {t("read")}
                </Typography>
              </Grid>
              <Grid item xs={2} sx={{ textAlign: "center" }}>
                <Typography
                  sx={{ fontWeight: "500!important" }}
                  className="label"
                >
                  {t("update")}
                </Typography>
              </Grid>
              <Grid item xs={2} sx={{ textAlign: "center" }}>
                <Typography
                  sx={{ fontWeight: "500!important" }}
                  className="label"
                >
                  {t("delete")}
                </Typography>
              </Grid>
            </Grid>
            <Box sx={{ display: "grid", gap: 3 }}>
              {listRoles.map((item, index) => (
                <Box sx={{ display: "grid", gap: 1 }} key={index}>
                  <Typography fontWeight={"400!important"} className="title">
                    {item.name}
                  </Typography>
                  {item.roles.map((role) => (
                    <Grid container alignItems={"center"} key={role}>
                      <Grid item xs={4} sx={{ textAlign: "left" }}>
                        <Box sx={{ ml: 4 }}>
                          <Typography className="label">{role}</Typography>
                        </Box>
                      </Grid>
                      <Grid item sx={{ textAlign: "center" }} xs={2}>
                        <input
                          type="checkbox"
                          className="checkboxStyled"
                          onChange={(e) =>
                            handleCheckboxChange(
                              role,
                              "CREATE",
                              e.target.checked
                            )
                          }
                        />
                      </Grid>
                      <Grid item sx={{ textAlign: "center" }} xs={2}>
                        <input
                          type="checkbox"
                          onChange={(e) =>
                            handleCheckboxChange(role, "VIEW", e.target.checked)
                          }
                        />
                      </Grid>
                      <Grid item sx={{ textAlign: "center" }} xs={2}>
                        <input
                          type="checkbox"
                          onChange={(e) =>
                            handleCheckboxChange(
                              role,
                              "UPDATE",
                              e.target.checked
                            )
                          }
                        />
                      </Grid>
                      <Grid item sx={{ textAlign: "center" }} xs={2}>
                        <input
                          type="checkbox"
                          onChange={(e) =>
                            handleCheckboxChange(
                              role,
                              "DELETE",
                              e.target.checked
                            )
                          }
                        />
                      </Grid>
                    </Grid>
                  ))}
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
        <Grid
          item
          xs={12}
          sx={{
            mt: 2,
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            justifyContent: "flex-end",
            gap: 1,
          }}
        >
          <ButtonPrimary
            variant="outlined"
            sx={{
              bgcolor: "white",
              "&:hover": { bgcolor: "white" },
              width: isMobile ? "100%" : "auto",
            }}
          >
            {t("previous")}
          </ButtonPrimary>
          <ButtonPrimary
            type="submit"
            variant="contained"
            sx={{
              width: isMobile ? "100%" : "auto",
            }}
          >
            {t("hantar")}
          </ButtonPrimary>
        </Grid>
      </Box>
    </Box>
  );
}

export default CreateRoleExternal;
