import { <PERSON>, <PERSON>rid, <PERSON><PERSON>ield, Typography } from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { t } from "i18next";
import { ButtonPrimary } from "../../../../../../../components/button";
import {
  CitizenshipStatus,
  DocumentUploadType,
  getLocalStorage,
  IdTypes,
  ListGelaran,
  ListGender,
  MALAYSIA,
  OccupationList,
  OrganisationPositions,
  otherPositionSwitchList,
  useQuery,
} from "@/helpers";
import { useEffect, useState } from "react";
import { Committee } from "@/pages/pengurusan-pertubuhan-internal/KeputusanCawangan/Pendaftaran/views/BranchPaparanAjk";
import { useCustom } from "@refinedev/core";
import { API_URL } from "@/api";
import Input from "@/components/input/Input";
import { FileUploader } from "@/components";

const PaparAJK = () => {
  const subTitleStyle = {
    color: "var(--primary-color)",
    pb: "30px",
    fontSize: "16px",
    fontWeight: "500 !important",
  };

  const [committee, setCommittee] = useState<Committee>();

  const { societyId, branchId, ajkId } = useParams();
  const decodedSocietyId = atob(societyId || "");
  const decodedBranchId = atob(branchId || "");
  const decodedAjkId = atob(ajkId || "");

  const {
    data: branchCommittee,
    isLoading,
    refetch,
  } = useQuery({
    url: `society/branch/committee/${decodedAjkId}`,
    enabled: decodedAjkId !== null,
    onSuccess: (data) => {
      setCommittee(data?.data?.data);
    },
  });

  const { data: branchList, isLoading: isLoadingBranch } = useCustom<any>({
    url: `${API_URL}/society/branch/getById/${decodedBranchId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: decodedBranchId !== null,
      retry: false,
      cacheTime: 0,
    },
  });

  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${decodedSocietyId}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const navigate = useNavigate();
  const addressList = getLocalStorage("address_list", null);
  const CountryData = addressList
    ?.filter((item: any) => item.level === 0)
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));

  const getDistrict = (val = null) => {
    const address = addressList
      .filter((items: any) => items.id === Number(val))
      .map((item: any) => ({ value: item.id, label: item.name }));

    return address?.[0]?.label || "-";
  };

  const getStateName = (stateCode: any) => {
    const stateName = addressList.filter((i: any) => i.id == stateCode);
    return stateName[0]?.name;
  };

  const StateList = addressList
    ?.filter((item: any) => item.pid === MALAYSIA)
    ?.map((item: any) => ({ value: item.id, label: item.name }));

  const [formData, setFormData] = useState({
    position: "",
    idType: "",
    icNo: "",
    title: "",
    name: "",
    gender: "",
    citizen: "",
    dateOfBirth: "",
    placeOfBirth: "",
    pekerjaan: "",
    residentialAddress: "",
    committeeStateCode: "",
    committeeDistrict: "",
    committeeCity: "",
    postcode: "",
    email: "",
    phoneNumber: "",
    homePhoneNumber: "",
    officePhoneNumber: "",
    committeeEmployerName: "",
    committeeEmployerAddress: "",
    committeeEmployerStateCode: "",
    committeeEmployerDistrict: "",
    committeeEmployerCity: "",
    committeeEmployerCountryCode: null,
    committeeEmployerPostcode: "",
    pegHarta: "",
    designationCode: null,
    otherDesignationCode: null,
    otherPosition: null,
  });

  const [positionsTranslatedList, setPositionsTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);

  useEffect(() => {
    const newPList = OrganisationPositions.map((item) => ({
      ...item,
      label: t(item.label),
    }));

    setPositionsTranslatedList(newPList);
  }, [t]);

  useEffect(() => {
    console.log("committee", committee);
    if (committee) {
      setFormData({
        ...formData,
        position: positionsTranslatedList[committee.designationCode]?.label,
        idType: committee?.identityType,
        icNo: committee?.committeeIcNo,
        title: committee?.titleCode,
        name: committee?.committeeName,
        gender: committee?.gender,
        citizen: committee.citizenshipStatus,
        dateOfBirth: committee?.dateOfBirth ? committee?.dateOfBirth : "",
        placeOfBirth: committee?.placeOfBirth,
        pekerjaan: committee.jobCode,
        residentialAddress: committee?.committeeAddress,
        committeeStateCode: committee?.committeeStateCode
          ? getStateName(committee?.committeeStateCode)
          : "-",
        committeeDistrict: committee?.committeeDistrict
          ? getDistrict(committee.committeeDistrict)
          : "-",
        committeeCity: committee?.committeeCity,
        postcode: committee?.postcode,
        email: committee?.email,
        phoneNumber: committee?.phoneNumber,
        homePhoneNumber: committee?.homePhoneNumber,
        officePhoneNumber: committee?.officePhoneNumber,
        committeeEmployerName: committee?.committeeEmployerName,
        committeeEmployerAddress: committee?.committeeEmployerAddress,
        committeeEmployerStateCode: committee?.committeeEmployerStateCode
          ? getStateName(committee?.committeeEmployerStateCode)
          : "-",
        committeeEmployerDistrict: committee?.committeeEmployerDistrict
          ? getDistrict(committee?.committeeEmployerDistrict)
          : "-",
        committeeEmployerCity: committee?.committeeEmployerCity,
        committeeEmployerPostcode: committee?.committeeEmployerPostcode,
        committeeEmployerCountryCode: committee?.committeeEmployerCountryCode,
        pegHarta: committee?.pegHarta,
        designationCode: committee?.designationCode,
        otherDesignationCode: committee?.otherDesignationCode,
        otherPosition: committee?.otherPosition,
      });
    }
  }, [committee]);

  const goback = () => {
    navigate(-1);
  };

  const [employerDistrictList, setEmployerDistrictList] = useState<any[]>([]);

  useEffect(() => {
    if (formData.committeeEmployerStateCode) {
      setEmployerDistrictList(
        addressList
          ?.filter(
            (item: any) => item.pid === formData.committeeEmployerStateCode
          )
          ?.map((item: any) => ({ value: item.id, label: item.name }))
      );
    }
  }, [formData?.committeeEmployerStateCode]);

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            <Typography
              fontWeight="500 !important"
              fontSize="14px"
              lineHeight="21px"
            >
              {societyData?.data?.data?.societyName} <br />
              {branchList?.data?.data?.name}
            </Typography>
          </Box>
        </Box>
      </Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        {/*  */}
        <Box
          sx={{
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("chairman")}
          </Typography>

          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("jawatan")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  value={
                    !otherPositionSwitchList.some(
                      (item) => item.value === Number(formData.designationCode)
                    )
                      ? t(
                          OrganisationPositions.find(
                            (item) =>
                              item.value === Number(formData.designationCode)
                          )?.label || "-"
                        )
                      : t(
                          formData?.otherPosition ??
                            t(
                              OrganisationPositions.find(
                                (item) =>
                                  item.value ===
                                  Number(formData.designationCode)
                              )?.label || "-"
                            )
                        )
                  }
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      position: e.target.value,
                    })
                  }
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/*  */}
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("chairmanPersonalInfo")}
          </Typography>

          <Box>
            <Input
              value={formData.idType}
              name="idType"
              disabled
              required
              type="select"
              options={IdTypes.map((item) => ({
                ...item,
                label: t(item.label),
              }))}
              label={t("idTypeCapitalizedOnlyFirstLetter")}
            />
            <Input
              disabled
              value={formData.icNo}
              name="identificationNo"
              required
              label={t("idNumberCapitalizedOnlyFirstLetter")}
            />
            <Input
              disabled
              value={formData.title}
              name="title"
              required
              type="select"
              options={ListGelaran.map((item) => ({
                ...item,
                label: t(item.label),
              }))}
              label={t("title")}
            />
            <Input
              disabled
              value={formData.name}
              name="fullName"
              required
              label={t("fullName")}
            />

            <Input
              required
              type="select"
              value={formData.gender}
              options={ListGender.map((option) => ({
                ...option,
                label: t(option.label),
              }))}
              disabled
              label={t("gender")}
            />
            <Input
              required
              label={t("citizenship")}
              value={Number(formData.citizen)}
              options={CitizenshipStatus.map((item) => ({
                ...item,
                label: t(item.label),
              }))}
              type="select"
              disabled
            />
            <Input
              disabled
              value={formData.dateOfBirth}
              name="dateOfBirth"
              required
              label={t("dateOfBirth")}
              type="date"
            />
            <Input
              disabled
              value={formData.placeOfBirth}
              name="placeOfBirth"
              required
              label={t("placeOfBirth")}
            />
            <Input
              disabled
              value={formData.pegHarta}
              name="pekerjaan"
              options={OccupationList.map((item) => ({
                ...item,
                label: t(item.label),
              }))}
              type="select"
              required
              label={t("occupation")}
            />
            <Input
              disabled
              value={formData.residentialAddress}
              name="residentialAddress"
              required
              label={t("residentialAddress")}
            />

            {/*=========== committeeStateCode section ========*/}
            <Input
              disabled
              value={formData.committeeStateCode}
              name="committeeStateCode"
              required
              label={t("state")}
            />
            <Input
              disabled
              value={formData.committeeDistrict}
              name="committeeDistrict"
              required
              label={t("district")}
            />
            <Input
              disabled
              value={formData.committeeCity}
              name="committeeCity"
              required
              label={t("city")}
            />

            <Input
              disabled
              value={formData.postcode}
              name="postcode"
              required
              label={t("postcode")}
            />
            <Input
              disabled
              value={formData.email}
              name="email"
              required
              label={t("email")}
            />
            <Input
              disabled
              value={formData.phoneNumber}
              name="phoneNumber"
              required
              label={t("phoneNumber")}
            />
            <Input
              disabled
              value={formData.homePhoneNumber}
              name="homePhoneNumber"
              required
              label={t("homePhoneNumber")}
            />
            <Input
              disabled
              value={formData.officePhoneNumber}
              name="officePhoneNumber"
              required
              label={t("officePhoneNumber")}
            />
          </Box>
        </Box>
        {/*  */}
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("employerInfo")}
          </Typography>

          <Box>
            <Input
              disabled
              value={formData.committeeEmployerName}
              name="committeeEmployerName"
              required
              label={t("employerName")}
            />
            <Input
              disabled
              value={formData.committeeEmployerAddress}
              name="committeeEmployerAddress"
              required
              label={t("employerAddress")}
            />
            <Input
              value={
                formData.committeeEmployerCountryCode
                  ? Number(formData.committeeEmployerCountryCode)
                  : ""
              }
              disabled
              name="committeeEmployerCountryCode"
              label={t("originCountry")}
              options={CountryData}
              type="select"
            />
            {Number(formData.committeeEmployerCountryCode) === MALAYSIA ? (
              <>
                <Input
                  value={
                    formData.committeeEmployerStateCode
                      ? formData.committeeEmployerStateCode
                      : ""
                  }
                  name="committeeEmployerStateCode"
                  disabled
                  // options={StateList}
                  // type="select"
                  label={t("state")}
                />
                <Input
                  value={
                    formData.committeeEmployerDistrict
                      ? formData.committeeEmployerDistrict
                      : ""
                  }
                  name="committeeEmployerDistrict"
                  label={t("district")}
                  // options={employerDistrictList ? employerDistrictList : []}
                  // type="select"
                  disabled
                />
                <Input
                  value={
                    formData.committeeEmployerCity
                      ? formData.committeeEmployerCity
                      : ""
                  }
                  disabled
                  name="committeeEmployerCity"
                  label={t("city")}
                />
                <Input
                  value={
                    formData.committeeEmployerPostcode
                      ? formData.committeeEmployerPostcode
                      : ""
                  }
                  disabled
                  name="committeeEmployerPostcode"
                  label={t("postcode")}
                />
              </>
            ) : null}
          </Box>
        </Box>
        {/*  */}

        <Box>
          <Box sx={{ textAlign: "left", mt: 2 }}>
            {committee?.id && (
              <FileUploader
                key={committee?.id}
                title={t("ajkEligibilityCheck")}
                type={DocumentUploadType.CITIZEN_COMMITTEE}
                societyId={decodedSocietyId}
                branchId={decodedBranchId}
                icNo={committee?.committeeIcNo}
                disabled={true}
                branchCommitteeId={committee?.id?.toString()}
                validTypes={[
                  "text/plain",
                  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                  "application/msword",
                  "application/pdf",
                ]}
                // onUploadComplete={handleUploadComplete}
              />
            )}
          </Box>
        </Box>

        <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
          <ButtonPrimary onClick={goback}>{t("back")}</ButtonPrimary>
        </Box>
      </Box>
    </>
  );
};

export default PaparAJK;
