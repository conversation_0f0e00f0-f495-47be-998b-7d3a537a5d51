import React, { useEffect, useRef, useState } from "react";
import {
  Box,
  TextField,
  Typography,
  Grid,
  useMediaQuery,
  Theme,
  Select,
  MenuItem,
  FormControl,
} from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { MALAYSIA, MeetingMethods, MeetingTypeOption } from "@/helpers/enums";
import { Map<PERSON>ontaine<PERSON>, <PERSON><PERSON>, TileLayer, useMap } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import {
  DocumentUploadType,
  useMutation,
  useQuery,
  useUploadPresignedUrl,
} from "@/helpers";
import {
  FormMeetingAttendees,
  FormMeetingAttendeesBaseRef,
} from "@/components/form/meeting/Attendees";
import { OrganizationManagementMeetingRequestBodyCreateOnlyAttendees } from "@/types";
import {
  FormMeetingDateTime,
  FormMeetingDateTimeRef,
} from "@/components/form/meeting/DateTime";
import { useSelector } from "react-redux";
import dayjs from "@/helpers/dayjs";
import AWSLocationMap from "@/components/geocoder/geocoder";
import { withAPIKey } from "@aws/amazon-location-utilities-auth-helper";
import {
  LocationClient,
  SearchPlaceIndexForTextCommand,
} from "@aws-sdk/client-location";

const RecenterAutomatically = ({ lat, lng }: { lat: number; lng: number }) => {
  const map = useMap();
  useEffect(() => {
    map.setView([lat, lng]);
  }, [lat, lng]);
  return null;
};

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

export const CreateMesyuarat: React.FC = () => {
  ///
  // AWS Location configuration
  const PLACE_INDEX_NAME = import.meta.env.VITE_AWS_PLACE_INDEX;
  const AWS_REGION = import.meta.env.VITE_AWS_API_KEY_REGION;
  const API_KEY = import.meta.env.VITE_AWS_API_KEY;
  const [client, setClient] = useState<LocationClient | null>(null);
  const [locations, setLocations] = useState<string[]>([]);

  // Initialize the AWS Location Client
  useEffect(() => {
    const initializeClient = async () => {
      try {
        const authHelper = await withAPIKey(API_KEY, AWS_REGION);
        const locationClient = new LocationClient(authHelper.getClientConfig());
        setClient(locationClient);
      } catch (error) {
        console.error("❌ Failed to initialize AWS Location Client:", error);
      }
    };
    initializeClient();
  }, [API_KEY, AWS_REGION]);

  ///
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const params = useParams();
  const formMeetingAttendeesRef =
    useRef<
      FormMeetingAttendeesBaseRef<OrganizationManagementMeetingRequestBodyCreateOnlyAttendees>
    >();
  const formMeetingDateTimeRef = useRef<FormMeetingDateTimeRef>();

  const societyId = params.id || "";

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  // Use a single state for map coordinates (initially set to default values)
  const [mapCoords, setMapCoords] = useState<[number, number]>([
    101.707021, 2.745564,
  ]);
  const [meetingType, setMeetingType] = useState("");
  const [meetingMethod, setMeetingMethod] = useState("");
  const [platformType, setPlatformType] = useState("");

  const { fetchAsync: createMeeting, isLoading: isCreating } = useMutation<{
    data: number;
  }>({
    url: "society/meeting/create",
    onSuccess: async (data) => {
      setIsSaved(true);

      if (selectedFile) {
        await upload({
          file: selectedFile,
          params: {
            name: selectedFile.name,
            societyId: societyId,
            societyNo: branchDataRedux.societyNo,
            branchId: branchDataRedux.id,
            meetingId: data.data.data,
            type: DocumentUploadType.MEETING,
          },
        });
      }

      navigate(
        `/pertubuhan/society/${societyId}/senarai/cawangan/view/mesyuarat`
      );
    },
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isSaved, setIsSaved] = useState(false);

  const [formData, setFormData] = useState<any>({});

  const [meetingCoords, setMeetingCoords] = useState<[number, number]>([
    2.745564, 101.707021,
  ]);

  //@ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const { upload } = useUploadPresignedUrl({
    showSuccessNotification: false,
    onSuccessUpload: () => {
      setSelectedFile(null);
    },
  });

  const { data: addressList, isLoading: isLoadingAddress } = useQuery({
    url: "society/admin/address/list",
  });

  const { data: meetingList, isLoading } = useQuery({
    url: "society/admin/meeting/list",
  });

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prevState: any) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setFormData((prevState: any) => ({
        ...prevState,
        meetingMinute: file.name,
      }));
    }
  };
  const handleSave = async () => {
    if (
      formData.meetingMethod == MeetingMethods.BERSEMUKA ||
      formData.meetingMethod == MeetingMethods.HYBRID
    ) {
      if (formData.postcode.toString().length !== 5) {
        return {
          message: t("postcodeValidation"),
          type: "error",
        };
      }
    }

    // Validate FormMeetingDateTime
    const dateTimeErrors = formMeetingDateTimeRef.current?.getErrors();
    if (dateTimeErrors && Object.keys(dateTimeErrors).length > 0) {
      return {
        message: t("pleaseCompleteAllRequiredFields"),
        type: "error",
      };
    }

    const dateTimeValue = formMeetingDateTimeRef.current?.getValue();
    const formattedTime = dateTimeValue?.meetingTime ?? "";
    const formattedTime2 = dateTimeValue?.meetingTimeTo ?? "";

    const attendeesValue = formMeetingAttendeesRef.current?.getValue();
    const totalAttendees = attendeesValue?.totalAttendees ?? 7;
    const data = {
      societyId: societyId,
      branchId: branchDataRedux.id,
      society_no: branchDataRedux?.societyNo ?? 0,
      meetingType: formData.meetingType?.toString() ?? "",
      meetingPlace: formData.meetingPlace ?? "",
      meetingPurpose: formData.meetingPurpose ?? "",
      meetingMethod: formData.meetingMethod?.toString() ?? "",
      platformType: formData.platformType?.toString() ?? "",
      meetingDate: dateTimeValue?.meetingDate ?? "",
      meetingTime: formattedTime ?? "",
      meetingTimeTo: formattedTime2 ?? "",
      GISInformation: formData.GISInformation,
      meetingAddress: formData.meetingAddress ?? "",
      state: formData.state?.toString() ?? "",
      district: formData.district?.toString() ?? "",
      city: formData.city ?? "",
      postcode: formData.postcode ?? "",
      totalAttendees,
      providedBy: formData.providedBy ?? "",
      confirmBy: formData.confirmBy ?? "",
      meetingMinute: selectedFile ? selectedFile.name : "",
      status: 1,
      openingRemarks: formData.openingRemarks,
      mattersDiscussed: formData.mattersDiscussed,
      otherMatters: formData.othersDiscussionRemarks,
      closing: formData.closingRemarks,
    };
    await createMeeting(data);
  };

  const addressData = addressList?.data?.data || [];
  const meetingData = meetingList?.data?.data || [];
  const meetingTypeOptions = MeetingTypeOption.filter((option) =>
    [2, 3, 4].includes(option.value)
  );

  const searchLocation = async (query: string, type: "meetingAddress") => {
    if (!query) return;
    if (!client) {
      console.warn("❌ AWS Location Client has not been initialized");
      return;
    }
    try {
      const command = new SearchPlaceIndexForTextCommand({
        IndexName: PLACE_INDEX_NAME,
        Text: query,
        MaxResults: 1,
        FilterCountries: ["MYS"],
      });
      const response = await client.send(command);
      console.log("place", response);
      if (!response?.Results || response.Results.length === 0) {
        console.warn("⚠️ No locations found");
        setLocations([]);
        return;
      }
      const firstResult = response.Results[0];
      console.log("firstResult", firstResult);

      if (firstResult?.Place?.Geometry?.Point) {
        const [longitude, latitude] = firstResult.Place.Geometry.Point;
        console.log("📍 Coordinates:", longitude, latitude);

        const city = firstResult.Place.Municipality || "";
        const postcodeFound = firstResult.Place.PostalCode || "";
        const stateName = firstResult.Place.Region || "";
        const districtName = firstResult.Place.SubRegion || "";

        console.log("🏙️ City:", city);
        console.log("📮 Postcode:", postcodeFound);
        console.log("🗺️ State name:", stateName);
        console.log("🗺️ District name:", districtName);

        if (type === "meetingAddress") {
          // Update the map coordinates to the new location
          setMapCoords([longitude, latitude]);
          setFormData((prevState: any) => ({
            ...prevState,
            city: city,
          })); 
          setFormData((prevState: any) => ({
            ...prevState,
            postcode: postcodeFound,
          }));
          setFormData((prevState: any) => ({
            ...prevState,
            GISInformation: `${latitude}, ${longitude}`,
          }));
        }
      }

      setLocations(
        response.Results.map(
          (place) => place.Place?.Label ?? "Unknown Location"
        )
      );
    } catch (error) {
      console.error("Error searching location:", error);
    }
  };

  const handleLocationSelected = (location: {
    fullAddress: string;
    state: string;
    district: string;
    city: string;
    postcode: string;
  }) => {
    // Update form fields when a location is selected
    setFormData((prevState: any) => ({
      ...prevState,
      address: location.fullAddress,
    }));
    setFormData((prevState: any) => ({
      ...prevState,
      state: location.state,
    }));
    setFormData((prevState: any) => ({
      ...prevState,
      city: location.city,
    }));
    setFormData((prevState: any) => ({
      ...prevState,
      postcode: location.postcode,
    }));
  };

  return (
    <>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px" }}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("mesyuaratPenubuhan")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("meetingType")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FormControl fullWidth>
                <Select
                  size="small"
                  value={meetingType}
                  displayEmpty
                  required
                  onChange={(e) => {
                    setMeetingType(e.target.value);
                    setFormData((prevState: any) => ({
                      ...prevState,
                      meetingType: e.target.value,
                    }));
                  }}
                >
                  {meetingTypeOptions
                    .filter((item: any) => item.id !== 1)
                    .map((option, index: any) => (
                      <MenuItem
                        key={`meeting-type-option-${index}`}
                        value={option.value}
                      >
                        {t(option?.translateKey ?? "pleaseSelect")}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("meetingMethod")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FormControl fullWidth>
                <Select
                  size="small"
                  value={meetingMethod || ""}
                  displayEmpty
                  required
                  disabled={isLoading}
                  onChange={(e) => {
                    setMeetingMethod(e.target.value);
                    setFormData((prevState: any) => ({
                      ...prevState,
                      meetingMethod: e.target.value,
                    }));
                  }}
                >
                  <MenuItem value="" disabled>
                    {isLoading ? "Loading..." : t("pleaseSelect")}
                  </MenuItem>
                  {!isLoading &&
                    meetingData
                      .filter((item: any) => item.pid === 2)
                      .map((item: any) => (
                        <MenuItem key={item.id} value={item.id}>
                          {i18n.language === "en" ? item.nameEn : item.nameBm}
                        </MenuItem>
                      ))}
                </Select>
              </FormControl>
            </Grid>
            {meetingMethod == MeetingMethods.ATAS_TALIAN ||
            meetingMethod == MeetingMethods.HYBRID ? (
              <>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("platformType")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <FormControl fullWidth>
                    <Select
                      size="small"
                      value={platformType || ""}
                      displayEmpty
                      required
                      disabled={isLoading}
                      onChange={(e) => {
                        setPlatformType(e.target.value);
                        setFormData((prevState: any) => ({
                          ...prevState,
                          platformType: e.target.value,
                        }));
                      }}
                    >
                      <MenuItem value="" disabled>
                        {isLoading ? "Loading..." : t("pleaseSelect")}
                      </MenuItem>
                      {!isLoading &&
                        meetingData
                          .filter((item: any) => item.pid === 3)
                          .map((item: any) => (
                            <MenuItem key={item.id} value={item.id}>
                              {i18n.language === "en"
                                ? item.nameEn
                                : item.nameBm}
                            </MenuItem>
                          ))}
                    </Select>
                  </FormControl>
                </Grid>
              </>
            ) : (
              <></>
            )}
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("tujuanMesyuarat")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                fullWidth
                required
                name="meetingPurpose"
                placeholder={t("Thepurposeofthemeetingisto")}
                value={formData.meetingPurpose}
                onChange={handleInputChange}
              />
            </Grid>
          </Grid>
        </Box>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("masaDanTarikhMesyuarat")}
          </Typography>
          <FormMeetingDateTime
            // @ts-expect-error
            ref={formMeetingDateTimeRef}
            meetingTimeFromAttribute="meetingTime"
            defaultValues={{
              meetingDate: null,
              meetingTime: null,
              meetingTimeTo: null,
            }}
            branchRegistrationDate={
              branchDataRedux?.submissionDate
                ? dayjs(branchDataRedux.submissionDate)
                : undefined
            }
          />
        </Box>
        {meetingMethod == MeetingMethods.BERSEMUKA ||
        meetingMethod == MeetingMethods.HYBRID ? (
          <>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mb: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("alamatTempatMesyuarat")}
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("namaTempatMesyuarat")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    size="small"
                    fullWidth
                    required
                    name="meetingPlace"
                    value={formData.meetingPlace}
                    onChange={handleInputChange}
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("meetingLocation")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <div
                    style={{
                      height: "150px",
                      width: "100%",
                      borderRadius: "8px",
                      marginBottom: "30px",
                    }}
                  >
                    <AWSLocationMap
                      longitude={mapCoords[0]}
                      latitude={mapCoords[1]}
                      // zoom={20}
                      onLocationSelected={handleLocationSelected}
                    />
                  </div>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("meetingPlaceAddress")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    size="small"
                    fullWidth
                    required
                    name="meetingAddress"
                    value={formData.meetingAddress}
                    onChange={(e) => {
                      handleInputChange(e);
                      searchLocation(e.target.value, "meetingAddress");
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("state")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <FormControl fullWidth required>
                    <Select
                      size="small"
                      value={formData.state}
                      displayEmpty
                      required
                      disabled={isLoadingAddress}
                      onChange={(e) => {
                        setFormData((prevState: any) => ({
                          ...prevState,
                          state: e.target.value,
                        }));
                      }}
                    >
                      <MenuItem value="" disabled>
                        {isLoadingAddress ? "Loading..." : t("pleaseSelect")}
                      </MenuItem>
                      {!isLoadingAddress &&
                        addressData
                          ?.filter((item: any) => item.pid === MALAYSIA)
                          .map((item: any) => (
                            <MenuItem key={item.id} value={item.id}>
                              {item.name}
                            </MenuItem>
                          ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("district")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <FormControl fullWidth required>
                    <Select
                      size="small"
                      value={formData.district}
                      displayEmpty
                      required
                      disabled={isLoadingAddress || !formData.state}
                      onChange={(e) => {
                        setFormData((prevState: any) => ({
                          ...prevState,
                          district: e.target.value,
                        }));
                      }}
                    >
                      <MenuItem value="" disabled>
                        {isLoadingAddress ? "Loading..." : t("pleaseSelect")}
                      </MenuItem>
                      {!isLoadingAddress &&
                        addressData
                          ?.filter((item: any) => item.pid == formData.state)
                          .map((item: any) => (
                            <MenuItem key={item.id} value={item.id}>
                              {item.name}
                            </MenuItem>
                          ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("city")}</Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <TextField
                    size="small"
                    fullWidth
                    name="city"
                    value={formData.city}
                    onChange={handleInputChange}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("postcode")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    size="small"
                    fullWidth
                    required
                    name="postcode"
                    value={formData.postcode}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      const value = e.target.value;
                      if (/^\d{0,5}$/.test(value)) {
                        handleInputChange(e);
                      }
                    }}
                    type="text"
                    onKeyDown={(e) => {
                      if (
                        e.key.toLowerCase() === "e" ||
                        e.key === "E" ||
                        e.key === "+" ||
                        e.key === "-"
                      ) {
                        e.preventDefault();
                      }
                    }}
                  />
                </Grid>
              </Grid>
            </Box>
          </>
        ) : (
          <></>
        )}
        <FormMeetingAttendees
          // @ts-expect-error
          ref={formMeetingAttendeesRef}
          defaultValues={{
            totalAttendees: formData.totalAttendees,
          }}
          onSubmit={() => {}}
        />

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("minitMesyuarat")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("minitMesyuarat")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <Box
                sx={{
                  border: "2px solid #DADADA",
                  borderRadius: "8px",
                  p: 2,
                  gap: 2,
                  textAlign: "center",
                  cursor: "pointer",
                  height: "200px",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "center",
                }}
                onClick={() => {
                  const element = document.getElementById("meetingMinute");
                  if (element) {
                    element.click();
                  }
                }}
              >
                {selectedFile ? (
                  <Typography sx={{ color: "#147C7C", mb: 1 }}>
                    {selectedFile.name}
                  </Typography>
                ) : (
                  <>
                    <Box
                      sx={{
                        width: 50,
                        aspectRatio: "1/1",
                        display: "flex",
                        justifyContent: "center",
                        alignContent: "center",
                        textAlign: "center",
                        borderRadius: 20,
                        mb: 2,
                        // p: 5,
                        bgcolor: "#F2F4F7",
                      }}
                    >
                      <img
                        width={30}
                        src={"/uploadFileIcon.svg"}
                        alt={"view"}
                      />
                    </Box>

                    <Typography
                      sx={{
                        color: "var(--primary-color)",
                        fontWeight: "500",
                        fontSize: "14px",
                      }}
                    >
                      {t("muatNaik")}
                    </Typography>
                    <Typography
                      sx={{
                        color: "#667085",
                        fontWeight: "400",
                        fontSize: "12px",
                      }}
                    >
                      {"PDF <25 MB"}
                    </Typography>
                  </>
                )}
                <input
                  id="meetingMinute"
                  type="file"
                  hidden
                  onChange={handleFileChange}
                  accept=".pdf"
                />
              </Box>
            </Grid>
          </Grid>
        </Box>
        <Grid container spacing={2}>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonOutline
              sx={{
                width: isMobile ? "100%" : "auto",
              }}
              onClick={() => {
                navigate(-1);
              }}
            >
              {t("back")}
            </ButtonOutline>

            {!isSaved && (
              <ButtonPrimary
                variant="contained"
                sx={{ width: isMobile ? "100%" : "auto" }}
                onClick={handleSave}
                disabled={isCreating}
              >
                {isCreating ? t("saving") : t("update")}
              </ButtonPrimary>
            )}
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default CreateMesyuarat;
