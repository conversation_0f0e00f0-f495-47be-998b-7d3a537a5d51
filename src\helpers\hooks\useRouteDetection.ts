import React from 'react';
import { useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { getUserPortal, setUserPortalRedux } from '@/redux/userReducer';
import { PORTAL_INTERNAL, PORTAL_EXTERNAL } from '../constants';
import {
  getRoutePortalType,
  hasRouteAccess,
  getCurrentUserPortal
} from '../routeDetector';

/**
 * Hook to detect the current route's portal type and access permissions
 * @param autoUpdatePortal - Whether to automatically update user portal when route mismatch is detected
 */
export const useRouteDetection = (autoUpdatePortal: boolean = false) => {
  const location = useLocation();
  const dispatch = useDispatch();
  const userPortalFromRedux = useSelector(getUserPortal);

  // Get current route portal type
  const routePortalType = getRoutePortalType(location.pathname);

  // Get user portal (prefer Redux, fallback to localStorage)
  const userPortal = userPortalFromRedux?.toString() || getCurrentUserPortal();

  // Debug warning for unregistered routes when RouteGuard is enabled
  React.useEffect(() => {
    // Only show warning in development mode and when route is unknown
    if (process.env.NODE_ENV === 'development' && routePortalType === 'unknown') {
      console.warn(
        `🚨 RouteGuard Warning: Route "${location.pathname}" is protected by RouteGuard but not registered.\n` +
        `Please register this route using registerRoutes() in your route definition file.\n` +
        `Example: registerRoutes({ '${location.pathname}': 'internal' | 'external' | 'shared' })`
      );
    }
  }, [location.pathname, routePortalType]);

  // Check if user has access to current route
  const hasAccess = hasRouteAccess(location.pathname, userPortal || undefined);

  // Check if current route matches user's portal
  const isCorrectPortal = (() => {
    if (routePortalType === 'shared') return true;
    if (!userPortal) return false;

    return (
      (routePortalType === 'internal' && userPortal === PORTAL_INTERNAL) ||
      (routePortalType === 'external' && userPortal === PORTAL_EXTERNAL)
    );
  })();

  // Function to update user portal based on route
  const updatePortalBasedOnRoute = () => {
    if (routePortalType === 'shared' || routePortalType === 'unknown') {
      return; // Don't update for shared or unknown routes
    }

    const newPortal = routePortalType === 'internal' ? PORTAL_INTERNAL : PORTAL_EXTERNAL;

    if (userPortal !== newPortal) {
      console.log(`Updating user portal from ${userPortal} to ${newPortal} based on route: ${location.pathname}`);

      // Update localStorage
      localStorage.setItem('portal', newPortal);

      // Update Redux store
      dispatch(setUserPortalRedux(parseInt(newPortal)));
    }
  };

  // Auto-update portal if enabled and there's a mismatch (only for registered routes)
  if (autoUpdatePortal && !isCorrectPortal && routePortalType !== 'unknown') {
    updatePortalBasedOnRoute();
  }

  return {
    // Route information
    currentPath: location.pathname,
    routePortalType,

    // User information
    userPortal,
    userPortalType: userPortal === PORTAL_INTERNAL ? 'internal' as const : userPortal === PORTAL_EXTERNAL ? 'external' as const : 'unknown' as const,

    // Access control
    hasAccess,
    isCorrectPortal,
    shouldRedirect: !hasAccess && routePortalType !== 'unknown',

    // Portal management
    updatePortalBasedOnRoute,
    canUpdatePortal: routePortalType !== 'shared' && routePortalType !== 'unknown',
  };
};

/**
 * Hook to check if a specific route is accessible by current user
 */
export const useRouteAccess = (routePath: string) => {
  const userPortalFromRedux = useSelector(getUserPortal);
  const userPortal = userPortalFromRedux?.toString() || getCurrentUserPortal();

  const routePortalType = getRoutePortalType(routePath);
  const hasAccess = hasRouteAccess(routePath, userPortal || undefined);

  return {
    hasAccess,
    routePortalType,
  };
};

/**
 * Hook specifically for automatic portal updates based on route changes
 * @param enabled - Whether automatic updates are enabled
 */
export const useAutoPortalUpdate = (enabled: boolean = true) => {
  const location = useLocation();
  const dispatch = useDispatch();
  const userPortalFromRedux = useSelector(getUserPortal);

  const updatePortal = (routePath: string = location.pathname) => {
    const portalType = getRoutePortalType(routePath);

    // Only update for registered internal/external routes
    if (portalType === 'shared' || portalType === 'unknown') {
      return { updated: false, reason: 'Route not suitable for portal update' };
    }

    const currentPortal = userPortalFromRedux?.toString() || getCurrentUserPortal();
    const newPortal = portalType === 'internal' ? PORTAL_INTERNAL : PORTAL_EXTERNAL;

    if (currentPortal === newPortal) {
      return { updated: false, reason: 'Portal already matches' };
    }

    console.log(`Auto-updating portal from ${currentPortal} to ${newPortal} for route: ${routePath}`);

    // Update localStorage
    localStorage.setItem('portal', newPortal);

    // Update Redux store
    dispatch(setUserPortalRedux(parseInt(newPortal)));

    return { updated: true, newPortal, oldPortal: currentPortal };
  };

  // Auto-update on route change if enabled
  React.useEffect(() => {
    if (enabled) {
      updatePortal();
    }
  }, [location.pathname, enabled]);

  return {
    updatePortal,
    currentPath: location.pathname,
    enabled,
  };
};

/**
 * Hook to get navigation helpers based on user portal
 */
export const usePortalNavigation = () => {
  const userPortalFromRedux = useSelector(getUserPortal);
  const userPortal = userPortalFromRedux?.toString() || getCurrentUserPortal();

  const getDefaultRoute = () => {
    switch (userPortal) {
      case PORTAL_INTERNAL: return '/internal-user';
      case PORTAL_EXTERNAL: return '/pertubuhan';
      default: return '/';
    }
  };

  const isInternalUser = userPortal === PORTAL_INTERNAL;
  const isExternalUser = userPortal === PORTAL_EXTERNAL;

  return {
    userPortal,
    isInternalUser,
    isExternalUser,
    getDefaultRoute,
    defaultRoute: getDefaultRoute(),
  };
};
