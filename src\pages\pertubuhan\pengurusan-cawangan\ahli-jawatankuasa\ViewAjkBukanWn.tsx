import React, { useEffect, useState } from "react";
import { Box, Typography, Grid } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate, useSearchParams } from "react-router-dom";
import { API_URL } from "../../../../api";
import { useCustom } from "@refinedev/core";
import Input from "../../../../components/input/Input";
import {
  CitizenshipStatus,
  DurationOptions,
  IdTypes,
  OrganisationPositions,
} from "../../../../helpers/enums";
import { getLocalStorage } from "../../../../helpers/utils";
import FileUploader from "@/components/input/fileUpload";
import { DocumentUploadType, useQuery } from "@/helpers";
import { ButtonOutline } from "@/components";
import dayjs from "dayjs";

interface FormValues {
  name: any;
  citizenshipStatus: any;
  identificationType: any;
  identificationNo: any;
  applicantCountryCode: any;
  gender: any;
  visaNo: number | any;
  visaExpirationDate: any;
  permitNo: any;
  permitExpirationDate: any;
  tujuanDMalaysia: any;
  tempohDMalaysia: any;
  stayDurationDigit: number | any;
  stayDurationUnit: any;
  designationCode: any;
  summary: any;
  societyNo: string | null;
  societyName: null;
}

export const ViewAjkBukanWn: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [searchParams] = useSearchParams();
  const branchId = searchParams.get("id");
  const memberId = searchParams.get("mId");
  const addressList = getLocalStorage("address_list", null);
  const CountryData = addressList
    ?.filter((item: any) => item.level === 0)
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));
  const [durationOptionsTranslated, setDurationOptionsTranslated] = useState<
    { value: string; label: string }[]
  >([]);
  const [nonCommitteeAJK, setNonCommitteeAJK] = useState<any>({});

  useEffect(() => {
    const newDurationList = DurationOptions.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setDurationOptionsTranslated(newDurationList);
  }, [t]);

  const {
    data: nonCommitteeData,
    isLoading: nonCommitteeDataLoading,
    refetch: fetchNonCommitteeData,
  } = useQuery({
    url: `society/nonCitizenCommittee/${memberId}`,
    autoFetch: !!memberId,
    onSuccess: (data) => {
      setNonCommitteeAJK(data?.data?.data);
    },
  });

  useEffect(() => {
    if (nonCommitteeAJK) {
      setFormValues(nonCommitteeAJK);
    }
  }, [nonCommitteeAJK]);

  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${nonCommitteeAJK?.societyId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: !!nonCommitteeAJK?.societyId,
    },
  });

  const [formValues, setFormValues] = useState<FormValues>({
    name: "",
    societyNo: null,
    societyName: null,
    citizenshipStatus: 2,
    identificationType: "",
    identificationNo: null,
    visaNo: "",
    visaExpirationDate: "",
    permitNo: "",
    permitExpirationDate: "",
    tujuanDMalaysia: "",
    tempohDMalaysia: "",
    stayDurationDigit: null,
    stayDurationUnit: "",
    designationCode: "",
    summary: "",
    applicantCountryCode: null,
    gender: null,
  });

  useEffect(() => {
    if (societyData) {
      setFormValues((prevValues) => ({
        ...prevValues,
        societyNo: societyData?.data?.data?.societyNo,
        societyName: societyData?.data?.data?.societyName,
      }));
    }
  }, [societyData]);

  const [debouncedData, setDebouncedData] = useState<FormValues>(formValues);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedData(formValues);
    }, 300); // Adjust delay as needed

    return () => {
      clearTimeout(handler);
    };
  }, [formValues]);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "32px",
    borderRadius: "16px",
    fontSize: "16px",
    fontWeight: "500 !important",
  };

  const handleSenaraiAjk = () => {
    navigate(-1);
  };

  const [positionsTranslatedList, setPositionsTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);

  useEffect(() => {
    const newPList = OrganisationPositions.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setPositionsTranslatedList(newPList);
  }, [t]);

  const [idTypeTranslatedList, setIdTypeTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    const newOList = IdTypes.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setIdTypeTranslatedList(newOList);
  }, [t]);

  return (
    <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
      }}
    >
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="h6" component="h2" sx={sectionStyle}>
          {t("nonCitizenAJK")}
        </Typography>

        <Input
          value={
            societyData?.data?.data?.societyNo
              ? societyData?.data?.data?.societyNo
              : societyData?.data?.data?.applicationNo
          }
          name="societyNo"
          disabled
          required
          label={t("organizationNumber2")}
        />
        <Input
          value={societyData?.data?.data?.societyName}
          name="societyName"
          disabled
          required
          label={t("organization_name")}
        />
        <Input
          disabled
          value={formValues.name ? formValues.name : ""}
          name="name"
          label={t("fullNameCapitalizedOnlyFirstLetter")}
        />
        <Input
          disabled
          value={
            formValues.citizenshipStatus
              ? Number(formValues.citizenshipStatus)
              : 2
          }
          name="citizenshipStatus"
          label={t("citizenship")}
          type="select"
          options={CitizenshipStatus.map((item) => ({
            ...item,
            label: t(item.label),
          }))}
          required
        />

        <Input
          disabled
          value={
            formValues.identificationType ? formValues.identificationType : ""
          }
          name="identificationType"
          required
          label={t("idTypeCapitalizedOnlyFirstLetter")}
          options={idTypeTranslatedList}
          type="select"
        />
        <Input
          disabled
          value={formValues.identificationNo ? formValues.identificationNo : ""}
          name="identificationNo"
          required
          label={t("idNumberCapitalizedOnlyFirstLetter")}
        />
        <Input
          disabled
          value={Number(formValues.applicantCountryCode) ?? ""}
          name="applicantCountryCode"
          required
          label={t("originCountry")}
          options={CountryData}
          type="select"
        />
        <Input
          disabled
          value={formValues.visaNo ? formValues.visaNo : ""}
          name="visaNo"
          label={t("nomborVisa")}
        />
        <Input
          disabled
          value={dayjs(formValues.visaExpirationDate, "DD/MM/YY").format(
            "YYYY-MM-DD"
          )}
          name="visaExpirationDate"
          type="date"
          label={t("visaExpiryDate")}
        />
        <Input
          disabled
          value={formValues.permitNo ? formValues.permitNo : ""}
          name="permitNo"
          label={t("nomborPermit")}
        />
        <Input
          disabled
          value={dayjs(formValues.permitExpirationDate, "DD/MM/YY").format(
            "YYYY-MM-DD"
          )}
          name="permitExpirationDate"
          type="date"
          label={t("permitExpiryDate")}
        />
        <Input
          value={formValues?.tujuanDMalaysia ?? ""}
          name="tujuanDMalaysia"
          required
          label={t("tujuanDiMalaysia")}
          disabled
        />
        {Number(formValues.identificationType) !== 4 && (
          <Grid container spacing={2} alignItems="center" sx={{ mb: 1 }}>
            <Grid item xs={12} sm={4}>
              <Typography
                variant="body1"
                sx={{
                  color: "#666666",
                  fontWeight: "400 !important",
                  fontSize: "14px",
                }}
              >
                {t("tempohDiMalaysia")}
                <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Input
                    value={formValues?.stayDurationDigit ?? ""}
                    name="stayDurationDigit"
                    required
                    type="text"
                    inputMode="numeric"
                    onChange={(e) => {
                      const value = e.target.value;
                      if (/^\d*$/.test(value)) {
                        setFormValues({
                          ...formValues,
                          stayDurationDigit: parseInt(value) || null,
                        });
                      }
                    }}
                    disabled
                    isLabelNoSpace={false}
                    isLabel={false}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Input
                    value={formValues?.stayDurationUnit ?? ""}
                    name="stayDurationUnit"
                    required
                    type="select"
                    options={durationOptionsTranslated}
                    disabled
                    isLabelNoSpace={false}
                    isLabel={false}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        )}
        <Input
          disabled
          value={Number(formValues?.designationCode) ?? ""}
          name="designationCode"
          required
          options={positionsTranslatedList}
          label={t("position")}
          type="select"
        />
        <Input
          disabled
          value={formValues.summary ? formValues.summary : ""}
          name="summary"
          multiline
          rows={4}
          required
          label={t("importanceOfPosition2")}
        />
      </Box>
      <FileUploader
        title="ajkEligibilityCheck"
        type={DocumentUploadType.NON_CITIZEN_COMMITTEE}
        disabled
        validTypes={[
          "text/plain",
          "application/rtf",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/msword",
          "application/vnd.oasis.opendocument.text",
          "application/pdf",
        ]}
        societyId={nonCommitteeAJK?.societyId}
        branchId={branchId}
        branchCommitteeId={memberId?.toString()}
        icNo={formValues?.identificationNo}
      />

      <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 2 }}>
        <ButtonOutline onClick={handleSenaraiAjk}>{t("back")}</ButtonOutline>
      </Box>
    </Box>
  );
};

export default ViewAjkBukanWn;
