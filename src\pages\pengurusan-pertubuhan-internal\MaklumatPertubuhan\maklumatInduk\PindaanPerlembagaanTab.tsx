import { <PERSON>, Grid, Icon<PERSON>utton, Typography } from "@mui/material";
import { t } from "i18next";
import { useEffect, useState } from "react";
import { formatDate, getLocalStorage } from "../../../../helpers/utils";
import { useNavigate } from "react-router-dom";
import { ApplicationStatusEnum, MALAYSIA } from "../../../../helpers/enums";
import ButtonPrevious from "../../../../components/button/ButtonPrevious";
import { ButtonPrimary } from "../../../../components/button";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { EyeIcon } from "../../../../components/icons";
import DataTable, { IColumn } from "@/components/datatable";
import { CrudFilter } from "@refinedev/core";
import { PermissionNames, pageAccessEnum, useQuery } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";
import Input from "@/components/input/Input";
import ForbiddenPage from "@/pages/forbidden";
import { MaklumatTabProps } from "../maklumatSelectionTabs";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "30px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

function PindaanPerlembagaanTab({ disabled }: MaklumatTabProps) {
  if (disabled) {
    return <ForbiddenPage internal />;
  } else {
    const navigate = useNavigate();

    const [amendmentRecordList, setAmendmentRecordList] = useState([]);
    const [total, setTotal] = useState<number>(0);

    const statusPertubuhan = [
      { value: 2, label: t("MENUNGGU_KEPUTUSAN") },
      { value: 3, label: t("lulus") },
      { value: 4, label: t("tolak") },
      { value: 36, label: t("kuiri") },
    ];

    const addressList = getLocalStorage("address_list", null);

    const malaysiaList =
      addressList.filter((address: any) => address.pid === MALAYSIA) ?? [];

    const getStateName = (id: string) =>
      malaysiaList.find((state: any) => state.id === parseInt(id))?.name || "-";

    const stateList = malaysiaList.map((i: any) => ({
      value: i.id,
      label: i.name,
    }));

    const { getValues, setValue, watch, control, reset, handleSubmit } =
      useForm<FieldValues>({
        defaultValues: {
          pageNo: 1,
          pageSize: 10,
          searchQuery: undefined,
          isQuery: 0,
          state: "",
          status: "",
        },
      });

    const {
      data: searchResult,
      isLoading: isSearchLoading,
      refetch: fetchPendingResult,
    } = useQuery({
      url: `society/roDecision/getDecisionRecord/amendment`,
      autoFetch: false,
      filters: [
        { field: "pageNo", operator: "eq", value: watch("pageNo") },
        { field: "pageSize", operator: "eq", value: watch("pageSize") },
        { field: "isQuery", operator: "eq", value: 0 },
      ],
      onSuccess: (data) => {
        const responseData = data?.data?.data;
        setAmendmentRecordList(responseData?.data || []);
        setTotal(responseData?.total);
      },
    });

    const handleClearSearch = () => {
      reset();
      const filters: CrudFilter[] = [
        { field: "pageSize", operator: "eq", value: 10 },
        { field: "pageNo", operator: "eq", value: 1 },
        { field: "isQuery", operator: "eq", value: 0 },
        {
          field: "state",
          operator: "eq",
          value: null,
        },
        {
          field: "status",
          operator: "eq",
          value: null,
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: null,
        },
      ];
      fetchPendingResult({ filters });
    };

    const handleChangePage = (newPage: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: watch("pageSize"), operator: "eq" },
        { field: "pageNo", value: newPage, operator: "eq" },
        { field: "isQuery", operator: "eq", value: 0 },
        {
          field: "state",
          operator: "eq",
          value: watch("state"),
        },
        {
          field: "status",
          operator: "eq",
          value: watch("status"),
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: watch("searchQuery"),
        },
      ];
      setValue("pageNo", newPage);
      fetchPendingResult({ filters });
    };

    const handleChangePageSize = (newPageSize: number) => {
      const filters: CrudFilter[] = [
        { field: "pageSize", value: newPageSize, operator: "eq" },
        { field: "pageNo", value: 1, operator: "eq" },
        { field: "isQuery", operator: "eq", value: 0 },
        {
          field: "state",
          operator: "eq",
          value: watch("state"),
        },
        {
          field: "status",
          operator: "eq",
          value: watch("status"),
        },
        {
          field: "searchQuery",
          operator: "eq",
          value: watch("searchQuery"),
        },
      ];
      setValue("pageNo", 1);
      setValue("pageSize", newPageSize);
      fetchPendingResult({ filters });
    };

    const handleSearch = () => {
      fetchPendingResult({
        filters: [
          {
            field: "pageSize",
            value: getValues("pageSize"),
            operator: "eq",
          },
          {
            field: "pageNo",
            value: getValues("pageNo"),
            operator: "eq",
          },
          { field: "isQuery", operator: "eq", value: 0 },
          {
            field: "searchQuery",
            value: getValues("searchQuery"),
            operator: "eq",
          },
          {
            field: "state",
            value: getValues("state"),
            operator: "eq",
          },
          {
            field: "status",
            value: getValues("status"),
            operator: "eq",
          },
        ],
      });
    };

    const columns: IColumn[] = [
      {
        field: "societyName",
        headerName: t("namaPertubuhan"),
        flex: 1,
        align: "center",
        renderCell: ({ row }: any) => {
          return row?.societyName ?? "-";
        },
      },
      {
        field: "societyNo",
        headerName: t("noPertubuhan"),
        flex: 1,
        align: "center",
        renderCell: ({ row }: any) => {
          return row?.societyNo ?? "-";
        },
      },
      {
        field: "applicationStatusCode",
        headerName: t("statusPermohonan"),
        flex: 1,
        align: "center",
        renderCell: ({ row }: any) => {
          return t(
            ApplicationStatusEnum[
              (row?.applicationStatusCode as keyof typeof ApplicationStatusEnum) ||
                "-"
            ]
          );
        },
      },
      {
        field: "amendmentType",
        headerName: t("constitutionType"),
        flex: 1,
        align: "center",
        renderCell: ({ row }: any) => {
          return row?.amendmentType ? row?.amendmentType : "-";
        },
      },
      {
        field: "approvedDate",
        headerName: t("tarikhPermohonan"),
        flex: 1,
        align: "center",
        renderCell: (params: any) =>
          params?.row?.submissionDate
            ? formatDate(params?.row?.submissionDate)
            : "-",
      },
      {
        field: "stateCode",
        headerName: t("negeri"),
        flex: 1,
        align: "center",
        renderCell: (params: any) =>
          params?.row?.stateCode ? getStateName(params?.row?.stateCode) : "-",
      },
      {
        field: "actions",
        headerName: "",
        flex: 1,
        align: "center",
        renderCell: (params: any) => {
          const row = params.row;
          return (
            <Box sx={{ display: "flex" }}>
              <IconButton
                onClick={() => {
                  const amendmentId = btoa(row.id);
                  const societyId = btoa(row.societyId);
                  navigate(
                    `/pengurus-pertubuhan/maklumat-pertubuhan/induk/pertubuhan/pindaan-perlembagaan/${amendmentId}/${societyId}`
                  );
                }}
                sx={{ color: "black", minWidth: 0, p: 0.5 }}
              >
                <EyeIcon
                  sx={{
                    color: "var(--primary-color)",
                    width: "1rem",
                    height: "1rem",
                  }}
                />
              </IconButton>
            </Box>
          );
        },
      },
    ];

    useEffect(() => {
      fetchPendingResult({
        filters: [
          {
            field: "pageSize",
            value: watch("pageSize"),
            operator: "eq",
          },
          {
            field: "pageNo",
            value: watch("pageNo"),
            operator: "eq",
          },
          { field: "isQuery", operator: "eq", value: 0 },
        ],
      });
    }, []);

    return (
      <>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
            }}
          >
            <form onSubmit={handleSubmit(handleSearch)}>
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("amendmentList")}
              </Typography>
              <Controller
                name="state"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="select"
                    label={t("state")}
                    options={stateList}
                  />
                )}
              />
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="select"
                    label={t("applicationStatusCode")}
                    options={statusPertubuhan}
                  />
                )}
              />
              <Controller
                name="searchQuery"
                control={control}
                render={({ field }) => <Input {...field} label={t("search")} />}
              />

              <Grid container mt={3} spacing={2}>
                <Grid
                  item
                  xs={12}
                  sx={{
                    mt: 2,
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <ButtonPrevious
                    variant="outlined"
                    sx={{
                      bgcolor: "white",
                      "&:hover": { bgcolor: "white" },
                      // width: isMobile ? "100%" : "auto",
                    }}
                    onClick={handleClearSearch}
                  >
                    {t("previous")}
                  </ButtonPrevious>
                  <ButtonPrimary
                    onClick={handleSearch}
                    variant="contained"
                    sx={{
                      // width: isMobile ? "100%" : "auto",
                      boxShadow: "none",
                    }}
                  >
                    {t("search")}
                  </ButtonPrimary>
                </Grid>
              </Grid>
            </form>
          </Box>
        </Box>
        {/* ============= */}
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              textAlign: "center",
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              py: 2,
            }}
          >
            <Typography variant="h5" gutterBottom>
              {total}
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
              {t("rekodDijumpai")}
            </Typography>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mt: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("amendmentList")}
            </Typography>

            <DataTable
              columns={columns}
              rows={amendmentRecordList}
              page={watch("pageNo")}
              rowsPerPage={watch("pageSize")}
              totalCount={total}
              isLoading={isSearchLoading}
              onPageChange={handleChangePage}
              onPageSizeChange={handleChangePageSize}
              // paginationType="custom"
            />
          </Box>
        </Box>
      </>
    );
  }
}

export default PindaanPerlembagaanTab;
