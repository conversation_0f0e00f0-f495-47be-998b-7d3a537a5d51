import { Box } from "@mui/material";
import React from "react";

interface MaklumatBoxesProps {
  data: {
    name: string;
  };
  isActive: boolean;
  disabled: boolean;
  onClick: () => void;
}
export type MaklumatTabProps = {
  disabled: boolean;
};
const primary = "var(--primary-color)";

export const MaklumatBoxes: React.FC<MaklumatBoxesProps> = React.memo(
  ({ data, isActive, disabled, onClick }) => {
    return (
      <Box
        onClick={!disabled ? onClick : undefined}
        sx={{
          ...(!disabled && {
            "&:hover": {
              backgroundColor: isActive ? primary : "#f5f5f5",
              transform: "translateY(-2px)",
            },
          }),
          padding: "0.75rem",
          paddingTop: "0.5rem !important",
          borderRadius: "0.5rem",
          border: `1px solid ${!disabled ? primary : "var(--border-grey)"}`,
          backgroundColor: isActive
            ? primary
            : !disabled
            ? "white"
            : "var(--border-grey)",
          position: "relative",
          display: "grid",
          gap: 2,
          flexDirection: "column",
          justifyContent: "space-between",
          alignItems: "flex-start",
          height: "100%",
          minHeight: "80px",
          paddingBottom: 0,
          cursor: !disabled ? "pointer" : "default",
          transition: "all 0.2s ease-in-out",
          ...(isActive && {
            boxShadow: "4px 6px 12px 0 rgba(102, 102, 102, 0.3)",
          }),
        }}
      >
        <Box
          sx={{
            color: disabled
              ? "var(--text-grey-disabled)"
              : isActive
              ? "#fff"
              : "var(--primary-color)",
            fontWeight: 400,
            fontSize: "14px",
          }}
        >
          {data.name}
        </Box>
      </Box>
    );
  }
);
