import type { TFunction } from "i18next";
import Box from "@mui/material/Box";
import { Grid, TextField, Typography } from "@mui/material";
import Input from "@/components/input/Input";
import {
  Control,
  Controller,
  FieldValues,
  UseFormGetValues,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { formatAndValidateNumber } from "@/helpers";

export const CreateAset: React.FC<{
  t: TFunction;
  control?: Control<FieldValues>; // Made optional by adding `?`
  setValue?: UseFormSetValue<FieldValues>; // Made optional by adding `?`
  getValues?: UseFormGetValues<FieldValues>; // Made optional by adding `?`
  watch?: UseFormWatch<FieldValues>; // Made optional by adding `?`
  checked?: boolean;
  isAccessible?: boolean;
}> = ({ t, control, setValue, getValues, watch, checked, isAccessible }) => {
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);

  const renderInputGroup = (
    title: string,
    items: { label: string; variable: string }[]
  ) => (
    <>
      <Box
        sx={{
          background: "white",
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={4}>
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontSize: "14px",
                fontWeight: "500 !important",
              }}
            >
              {t("totalAssets")}
            </Typography>
          </Grid>

          <Grid item xs={8}>
            {items.map((item, index) =>
              control ? ( // Check if `control` is provided
                <Controller
                  name={item.variable}
                  control={control}
                  key={index}
                  defaultValue={
                    getValues ? getValues(item.variable) : undefined
                  } // Default value or empty string if `getValues` is undefined
                  render={({ field }) => (
                    <TextField
                      size="small"
                      fullWidth
                      required
                      disabled={checked || !isAccessible}
                      value={getValues ? getValues(item.variable) : undefined}
                      placeholder="0"
                      onChange={(e) => {
                        const formattedValue = formatAndValidateNumber(
                          e.target.value
                        );
                        if (formattedValue !== null) {
                          field.onChange(formattedValue);
                          if (setValue) {
                            setValue("totalAsset", formattedValue); // Update totalIncome if `setValue` is available
                          }
                        }
                      }}
                    />
                  )}
                />
              ) : (
                <Input
                  type="number"
                  key={index}
                  label={t(item.label)}
                  required
                  placeholder="0"
                  disabled={checked || !isAccessible}
                />
              )
            )}
          </Grid>
        </Grid>
      </Box>
    </>
  );

  return (
    <>
      {/* <Box
        sx={{
          background: "white",
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          py: 2,
          mb: 2,
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{
            color: "var(--primary-color)",
            borderRadius: "16px",
            fontSize: "14px",
            fontWeight: "500 !important",
          }}
        >
          {t("assets")}
        </Typography>
      </Box>

      {renderInputGroup("fixedAssets", [
        { label: "buildings", variable: "buildingAsset" },
        { label: "investmentAssets", variable: "investmentAsset" },
        { label: "intangibleAssets", variable: "intangibleAsset" },
        { label: "land", variable: "landAsset" },
        { label: "vehicles", variable: "vehicleAsset" },
        { label: "machineryEquipment", variable: "machineAsset" },
        { label: "furniture", variable: "furnitureAsset" },
        { label: "furnitofficeEquipmentSuppliesure", variable: "officeAsset" },
      ])}

      {renderInputGroup("totalCurrentAssets", [
        { label: "cashInHand", variable: "assetOnHand" },
        { label: "cashInBank", variable: "bankAsset" },
        { label: "accountsReceivable", variable: "accountAsset" },
        { label: "inventory", variable: "inventoryAsset" },
        { label: "currentInvestments", variable: "investAsset" },
        { label: "fixedDeposits", variable: "depositAsset" },
        { label: "accruedTaxes", variable: "taxAsset" },
      ])} */}

      {renderInputGroup("", [{ label: "totalAssets", variable: "totalAsset" }])}
    </>
  );
};

export default CreateAset;
