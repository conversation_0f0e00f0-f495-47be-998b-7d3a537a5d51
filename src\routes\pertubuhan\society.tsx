import { Navigate, Outlet, Route } from "react-router-dom";
import { RouteGuard } from "../../components/RouteGuard";
import { registerRoutes } from "../../helpers/routeDetector";
import DashboardPertubuhan from "../../pages/pertubuhan/pengurusan-pertubuhan/dashboard-pertubuhan";
import SenaraiLayout from "../../pages/pertubuhan/SenaraiLayout";
import MaklumatPertubuhan from "../../pages/pertubuhan/maklumat-pertubuhan";
import ListMesyuarat from "../../pages/pertubuhan/mesyuarat/ListMesyuarat";
import CreateMesyuarat from "../../pages/pertubuhan/mesyuarat/add-mesyuarat";
import Pembubaran from "../../pages/pertubuhan/pembubaran";
import CreatePembubaran from "../../pages/pertubuhan/pembubaran/add-pembubaran/CreatePembubaran";
import ListPenyata from "../../pages/pertubuhan/pernyata-tahunan/ListPenyata";
import CreateMam from "../../pages/pertubuhan/pernyata-tahunan/maklumat-am";
import CreateCawanganMam from "../../pages/pertubuhan/pengurusan-cawangan/maklumat-am";
import CreateMesyuaratAgung from "../../pages/pertubuhan/pernyata-tahunan/maklumat-mesyuarat";
import CreateAjk from "../../pages/pertubuhan/pernyata-tahunan/maklumat-ajk/CreateAjk";
import { CreatePernyataPendapatan } from "../../pages/pertubuhan/pernyata-tahunan/pernyata-pendapatan";
import { IndexSumbanganNegara } from "../../pages/pertubuhan/pernyata-tahunan/sumbangan-negara";
import ListCawangan from "../../pages/pertubuhan/pengurusan-cawangan/ListCawangan";
import RegisterCawangan from "../../pages/pertubuhan/pengurusan-cawangan/RegisterCawangan";
import DokumenSokongan from "../../pages/pertubuhan/pengurusan-cawangan/dokumen-sokongan";
import MinuteMesyuarat from "../../pages/pertubuhan/pengurusan-cawangan/minute-mesyuarat";
import AhliJawatanKuasa from "../../pages/pertubuhan/pengurusan-cawangan/ahli-jawatankuasa";
import CreateAjkCawangan from "../../pages/pertubuhan/pengurusan-cawangan/ahli-jawatankuasa/CreateAjk";
import CreateAjkBukanWnCawangan from "../../pages/pertubuhan/pengurusan-cawangan/ahli-jawatankuasa/CreateAjkBukanWn";
import CreateLanjutMasa from "../../pages/pertubuhan/pengurusan-cawangan/lanjut-masa/CreateLanjutMasa";
import UpdateMaklumatPerlembagaan from "../../pages/pertubuhan/pengurusan-perlembagaan/UpdateMaklumatPerlembagaan";
import ListPindaanPerlembagaan from "../../pages/pertubuhan/pengurusan-perlembagaan/ListPindaanPerlembagaan";
import UpdatePindaanPerlembagaan from "../../pages/pertubuhan/pengurusan-perlembagaan/UpdatePindaanPerlembagaan";
import SemakanPerlembagaan from "../../pages/pertubuhan/pengurusan-perlembagaan/SemakPerlembagaan";
import ListPindaanPerlembagaanNew from "../../pages/pertubuhan/pengurusan-perlembagaan/ListPindaanPerlembagaan/index";
import PembayaranCawangan from "../../pages/pertubuhan/pengurusan-cawangan/pembayaran-cawangan";
import MaklumatAsset from "../../pages/pertubuhan/pembubaran/add-pembubaran/MaklumatAsset";
import PembaharuanSetiausahaCawangan from "../../pages/pertubuhan/pengurusan-cawangan/pembaharuan-setiausaha";
import Pelantikan from "../../pages/pertubuhan/pengurusan-cawangan/pembaharuan-setiausaha/Pelantikan";
import Cetak from "../../pages/pertubuhan/pengurusan-cawangan/pembaharuan-setiausaha/Cetak";
import { Online } from "../../pages/pertubuhan/pengurusan-cawangan/pembayaran-cawangan/Online";
import Kaunter from "../../pages/pertubuhan/pengurusan-cawangan/pembayaran-cawangan/Kaunter";
import { Term } from "../../pages/pertubuhan/pengurusan-cawangan/pembayaran-cawangan/Term";
import Butiran from "../../pages/pertubuhan/pengurusan-cawangan/pembayaran-cawangan/Butiran";
import PaparCawangan from "../../pages/pertubuhan/pengurusan-cawangan/papar";
import ListDokumenSokongan from "../../pages/pertubuhan/dokumen";
import CreateAjkJawatankuasa from "../../pages/pertubuhan/ajk/jawatankuasa/createAJK";
import CreateAjkBknWnJawatankuasa from "../../pages/pertubuhan/ajk/jawatankuasa/createAJKBukanWn";
import AhliPertubuhan from "../../pages/pertubuhan/ajk/ahli";
import DaftarAhli from "../../pages/pertubuhan/ajk/ahli/DaftarAhli";
import AhliSertaPertubuhan from "../../pages/pertubuhan/ajk/ahli/SertaPertubuhan";
import Jawatankuasa from "../../pages/pertubuhan/ajk/jawatankuasa";
import ListMaklumatAjk from "../../pages/pertubuhan/pernyata-tahunan/maklumat-ajk";
import UpdateAJK from "../../pages/pertubuhan/ajk/jawatankuasa/updateAJK";
import ALiranTugas from "../../pages/pertubuhan/AliranTugas";
import MaklumatPemegangAmanah from "../../pages/pertubuhan/ajk/pemegang-amanah";
import CreatePemegangAmanah from "../../pages/pertubuhan/ajk/pemegang-amanah/CreatePemegangAmanah";
import JuruAudit from "../../pages/pertubuhan/ajk/juruaudit";
import CreateJuruAudit from "../../pages/pertubuhan/ajk/juruaudit/Create";
import ViewJuruAudit from "@/pages/pertubuhan/ajk/juruaudit/View";
import Pegawai from "../../pages/pertubuhan/ajk/pegawai";
import CreatePegawaiAwam from "../../pages/pertubuhan/ajk/pegawai/pegawai-awam/CreatePegawaiAwam";
import CreatePegawaiHarta from "../../pages/pertubuhan/ajk/pegawai/pegawai-harta/CreatePegawaiHarta";
import BayaranPegawaiList from "../../pages/pertubuhan/ajk/pegawai/bayaran/ListBayaran";
import BayaranPegawaiKaunter from "../../pages/pertubuhan/ajk/pegawai/bayaran/Kaunter";
import BayaranPegawaiOnline from "../../pages/pertubuhan/ajk/pegawai/bayaran/Online";
import BayaranPegawaiTerm from "../../pages/pertubuhan/ajk/pegawai/bayaran/Term";
import BayaranPegawaiButiran from "../../pages/pertubuhan/ajk/pegawai/bayaran/Butiran";
import JawatankuasaProvider from "../../pages/pertubuhan/ajk/jawatankuasa/jawatankuasaProvider";
import MigrasiPerlembagaan from "../../pages/pertubuhan/pengurusan-perlembagaan/MigrasiPerlembagaan";
import FormPembubaran from "../../pages/pertubuhan/pembubaran/FormPembubaran";
import CreateAccountBank from "../../pages/pertubuhan/pernyata-tahunan/maklumat-am/CreateAccountBank";
import PenyataTahunanJuruAudit from "../../pages/pertubuhan/pernyata-tahunan/juruaudit";
import PenyataTahunanCreateJuruAudit from "../../pages/pertubuhan/pernyata-tahunan/juruaudit/Create";
import PenyataTahunanCreateSumbangan from "../../pages/pertubuhan/pernyata-tahunan/sumbangan-negara/CreateSumbangan";
import PenyataTahunanPaparan from "../../pages/pertubuhan/pernyata-tahunan/paparan";
import PenyataTahunanPengakuan from "../../pages/pertubuhan/pernyata-tahunan/akuan";
import BayaranPerlembagaan from "../../pages/pertubuhan/pengurusan-perlembagaan/bayaran/ListBayaran";
import BayaranPerlembagaanKaunter from "../../pages/pertubuhan/pengurusan-perlembagaan/bayaran/Kaunter";
import BayaranPerlembagaanOnline from "../../pages/pertubuhan/pengurusan-perlembagaan/bayaran/Online";
import BayaranPerlembagaanTerm from "../../pages/pertubuhan/pengurusan-perlembagaan/bayaran/Term";
import BayaranPerlembagaanButiran from "../../pages/pertubuhan/pengurusan-perlembagaan/bayaran/Butiran";
import ViewMesyuarat from "../../pages/pertubuhan/mesyuarat/ViewMesyuarat";
import FasalContentAdd from "../../pages/pertubuhan/pengurusan-perlembagaan/addFasal";
import PembubaranProvider from "@/pages/pertubuhan/pembubaran/PembubaranProvider";
import CreateLaporanAktiviti from "@/pages/pertubuhan/pernyata-tahunan/laporan-aktiviti/CreateLaporanAktiviti";

// Cawangan components
import CawanganMaklumat from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganMaklumat/index";
import CawanganPerlembagaan from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPerlembagaan/index";
import CawanganMesyuarat from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganMesyuarat/index";
import CawanganViewMesyuarat from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganMesyuarat/component/ViewMesyuarat";
import CawanganCreateMesyuarat from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganMesyuarat/component/CreateMesyuarat";
import CawanganPembubaran from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPembubaran/index";
import CawanganCreatePembubaran from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPembubaran/component/CreatePembubaran";
import CawanganFormPembubaran from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPembubaran/component/FormPembubaran";
import CawanganMaklumatAsset from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPembubaran/component/MaklumatAsset";
import BranchProvider from "@/pages/pertubuhan/BranchProvider";

// Cawangan AJK components
import CawanganCreateAjkJawatankuasa from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/jawatankuasa/createAJK";
import CawanganKemasKiniAjkAjkJawatankuasa from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/jawatankuasa/kemasKiniAjk";
import CawanganCreateAjkBknWnJawatankuasa from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/jawatankuasa/createAJKBukanWn";
import CawanganAhliPertubuhan from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/ahli";
import CawanganDaftarAhli from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/ahli/DaftarAhli";
import CawanganAhliSertaPertubuhan from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/ahli/SertaPertubuhan";
import CawanganUpdateAJK from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/jawatankuasa/updateAJK";
import CawanganALiranTugas from "@/pages/pertubuhan/pengurusan-cawangan/AliranTugas";
import CawanganMaklumatPemegangAmanah from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/pemegang-amanah";
import CawanganCreatePemegangAmanah from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/pemegang-amanah/CreatePemegangAmanah";
import CawanganJuruAudit from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/juruaudit";
import CawanganCreateJuruAudit from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/juruaudit/Create";
import CawanganViewJuruAudit from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/juruaudit/View";
import CawanganPegawai from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/pegawai";
import CawanganCreatePegawaiAwam from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/pegawai/pegawai-awam/CreatePegawaiAwam";
import CawanganCreatePegawaiHarta from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/pegawai/pegawai-harta/CreatePegawaiHarta";
import CawanganBayaranPegawaiList from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/pegawai/bayaran/ListBayaran";
import CawanganBayaranPegawaiKaunter from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/pegawai/bayaran/Kaunter";
import CawanganBayaranPegawaiOnline from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/pegawai/bayaran/Online";
import CawanganBayaranPegawaiTerm from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/pegawai/bayaran/Term";
import CawanganBayaranPegawaiButiran from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/pegawai/bayaran/Butiran";
import CawanganJawatankuasaProvider from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/jawatankuasa/jawatankuasaProvider";
import JawatankuasaCawangan from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganAJKKeahlian/jawatankuasa/jawatankuasa";

// Cawangan pernyata tahunan components
import CawanganCreateAccountBank from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPenyataTahunan/maklumat-am/CreateAccountBank";
import CawanganPenyataTahunanJuruAudit from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPenyataTahunan/juruaudit";
import CawanganPenyataTahunanCreateJuruAudit from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPenyataTahunan/juruaudit/Create";
import CawanganPenyataTahunanCreateSumbangan from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPenyataTahunan/sumbangan-negara/CreateSumbangan";
import CawanganPenyataTahunanPaparan from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPenyataTahunan/paparan";
import CawanganPenyataTahunanPengakuan from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPenyataTahunan/akuan";
import CawanganListPenyata from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPenyataTahunan/ListPenyata";
import CawanganCreateMam from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPenyataTahunan/maklumat-am";
import CawanganCreateMesyuaratAgung from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPenyataTahunan/maklumat-mesyuarat";
import CawanganCreateAjk from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPenyataTahunan/maklumat-ajk/CreateAjk";
import { CawanganCreatePernyataPendapatan } from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPenyataTahunan/pernyata-pendapatan";
import CawanganListMaklumatAjk from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPenyataTahunan/maklumat-ajk";
import { CawanganIndexSumbanganNegara } from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPenyataTahunan/sumbangan-negara";
import CreateLaporanAktivitiCawangan from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPenyataTahunan/laporan-aktiviti/CreateLaporanAktiviti";
import CawanganDocument from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganDocument/index";
import MaklumatAmPaperExpired from "@/pages/pertubuhan/pengurusan-cawangan/paper-expired/components/maklumatAmPaperExpired";
import MinitPaperExpired from "@/pages/pertubuhan/pengurusan-cawangan/paper-expired/components/minitMesyuaratPaperExpired";
import AjkPaperExpired from "@/pages/pertubuhan/pengurusan-cawangan/paper-expired/components/ajkAndKeahlianPaperExpired";
import ViewAjkBukanWnCawangan from "@/pages/pertubuhan/pengurusan-cawangan/ahli-jawatankuasa/ViewAjkBukanWn";
import CawanganPindaan from "@/pages/pertubuhan/pengurusan-cawangan/component/CawanganPindaan";
import MeetingForm from "@/pages/pertubuhan/pengurusan-cawangan/amendment-cawangan/form/meetingForm";
import SelectPindaan from "@/pages/pertubuhan/pengurusan-cawangan/amendment-cawangan/selectPindaan";
import ListBayaranCawanganPindaan from "@/pages/pertubuhan/pengurusan-cawangan/amendment-cawangan/Bayaran/ListBayaran";
import KaunterCawanganPindaan from "@/pages/pertubuhan/pengurusan-cawangan/amendment-cawangan/Bayaran/Kaunter";
import ButiranCawanganPindaan from "@/pages/pertubuhan/pengurusan-cawangan/amendment-cawangan/Bayaran/Butiran";
import OnlineCawanganPindaan from "@/pages/pertubuhan/pengurusan-cawangan/amendment-cawangan/Bayaran/Online";
import TermCawanganPindaan from "@/pages/pertubuhan/pengurusan-cawangan/amendment-cawangan/Bayaran/Term";

// Layout component to wrap all society routes with protection
const SocietyLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === "development"}
  >
    <Outlet />
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example route registrations (uncomment and modify as needed):
  // PERTUBUHAN
  "/pertubuhan/society": "external",
  "/pertubuhan/society/:id": "external",
  "/pertubuhan/society/:id/senarai": "external",
  "/pertubuhan/society/:id/senarai/maklumat": "external",
  // '/pertubuhan/society/:id/dashboard': 'external',
  // '/pertubuhan/society/:id/senarai': 'external',
  // '/pertubuhan/society/:id/senarai/maklumat': 'external',
  // '/pertubuhan/society/:id/senarai/perlembagaan': 'external',

  // PENGURUSAN PERLEMBAGAAN
  "/pertubuhan/society/:id/senarai/perlembagaan/maklumat": "external",
  "/pertubuhan/society/:id/senarai/perlembagaan/maklumat/pindaan-perlembagaan":
    "external",
  "/pertubuhan/society/:id/senarai/perlembagaan/maklumat/pindaan-perlembagaan/update/:clauseId":
    "external",
  "/pertubuhan/society/:id/senarai/perlembagaan/maklumat/pindaan-perlembagaan/add/:currentNo":
    "external",
  "/pertubuhan/society/:id/senarai/perlembagaan/maklumat/pindaan-perlembagaan/bayaran":
    "external",
  "/pertubuhan/society/:id/senarai/perlembagaan/maklumat/pindaan-perlembagaan/bayaran/kaunter":
    "external",
  "/pertubuhan/society/:id/senarai/perlembagaan/maklumat/pindaan-perlembagaan/bayaran/online":
    "external",
  "/pertubuhan/society/:id/senarai/perlembagaan/maklumat/pindaan-perlembagaan/bayaran/online/term":
    "external",
  "/pertubuhan/society/:id/senarai/perlembagaan/maklumat/pindaan-perlembagaan/bayaran/online/butiran":
    "external",
  // PENGURUSAN MESYUARAT
  "/pertubuhan/society/:id/senarai/mesyuarat": "external",
  "/pertubuhan/society/:id/senarai/mesyuarat/:meetingId": "external",
  "/pertubuhan/society/:id/senarai/mesyuarat/createMeeting": "external",

  //PENYATA TAHUNAN
  "/pertubuhan/society/:id/senarai/penyataTahunan": "external",
  "/pertubuhan/society/:id/senarai/penyataTahunan/penyata-tahunan-info":
    "external",
  "/pertubuhan/society/:id/senarai/penyataTahunan/penyata-tahunan-bank":
    "external",
  "/pertubuhan/society/:id/senarai/penyataTahunan/penyata-tahunan-agung":
    "external",
  "/pertubuhan/society/:id/senarai/penyataTahunan/penyata-tahunan-ajk":
    "external",
  "/pertubuhan/society/:id/senarai/penyataTahunan/penyata-tahunan-ajk/view":
    "external",
  "/pertubuhan/society/:id/senarai/penyataTahunan/penyata-tahunan-juruaudit":
    "external",
  "/pertubuhan/society/:id/senarai/penyataTahunan/penyata-tahunan-juruaudit/create":
    "external",
  "/pertubuhan/society/:id/senarai/penyataTahunan/penyata-tahunan-pendapatan":
    "external",
  "/pertubuhan/society/:id/senarai/penyataTahunan/penyata-tahunan-laporan-aktiviti":
    "external",
  "/pertubuhan/society/:id/senarai/penyataTahunan/penyata-tahunan-sumbangan":
    "external",
  "/pertubuhan/society/:id/senarai/penyataTahunan/penyata-tahunan-sumbangan/createSumbangan":
    "external",
  "/pertubuhan/society/:id/senarai/penyataTahunan/penyata-tahunan-display":
    "external",
  "/pertubuhan/society/:id/senarai/penyataTahunan/penyata-tahunan-pengakuan":
    "external",

  // '/pertubuhan/society/:id/senarai/pembubaran': 'external',
  "/pertubuhan/society/:id/senarai/ajk": "external",
  "/pertubuhan/society/:id/senarai/ajk/jawatankuasa": "external",
  "/pertubuhan/society/:id/senarai/ajk/jawatankuasa/create-ajk": "external",
  "/pertubuhan/society/:id/senarai/ajk/jawatankuasa/update-ajk": "external",
  "/pertubuhan/society/:id/senarai/ajk/jawatankuasa/create-ajk-bukan-wn":
    "external",
  "/pertubuhan/society/:id/senarai/ajk/ahli": "external",
  "/pertubuhan/society/:id/senarai/ajk/ahli/serta-pertubuhan": "external",
  "/pertubuhan/society/:id/senarai/ajk/ahli/daftar": "external",
  "/pertubuhan/society/:id/senarai/ajk/ahli/edit/:id": "external",
  "/pertubuhan/society/:id/senarai/ajk/pemegang-amanah": "external",
  "/pertubuhan/society/:id/senarai/ajk/pemegang-amanah/create": "external",
  "/pertubuhan/society/:id/senarai/ajk/pegawai": "external",
  "/pertubuhan/society/:id/senarai/ajk/pegawai/create-awam": "external",
  "/pertubuhan/society/:id/senarai/ajk/pegawai/create-awam/bayaran": "external",
  "/pertubuhan/society/:id/senarai/ajk/pegawai/create-awam/bayaran/kaunter":
    "external",
  "/pertubuhan/society/:id/senarai/ajk/pegawai/create-awam/bayaran/online":
    "external",
  "/pertubuhan/society/:id/senarai/ajk/pegawai/create-awam/bayaran/online/term":
    "external",
  "/pertubuhan/society/:id/senarai/ajk/pegawai/create-awam/bayaran/online/butiran":
    "external",
  "/pertubuhan/society/:id/senarai/ajk/pegawai/create-harta": "external",
  "/pertubuhan/society/:id/senarai/ajk/pegawai/create-harta/bayaran":
    "external",
  "/pertubuhan/society/:id/senarai/ajk/pegawai/create-harta/bayaran/kaunter":
    "external",
  "/pertubuhan/society/:id/senarai/ajk/pegawai/create-harta/bayaran/online":
    "external",
  "/pertubuhan/society/:id/senarai/ajk/pegawai/create-harta/bayaran/online/term":
    "external",
  "/pertubuhan/society/:id/senarai/ajk/pegawai/create-harta/bayaran/online/butiran":
    "external",
  "/pertubuhan/society/:id/senarai/ajk/juruaudit": "external",
  "/pertubuhan/society/:id/senarai/ajk/juruaudit/create": "external",
  "/pertubuhan/society/:id/senarai/ajk/juruaudit/view": "external",
  "/pertubuhan/society/:id/senarai/ajk/aliran-tugas": "external",
  // '/pertubuhan/society/:id/senarai/cawangan': 'external',
  "/pertubuhan/society/:id/senarai/dokumen": "external",

  // CAWANGAN
  "/pertubuhan/society/:id/senarai/cawangan/ahlijawatankuasa": "external",

  "/pertubuhan/society/:id/senarai/cawangan/create-ajk-bukan-wn": "external",
  "/pertubuhan/society/:id/senarai/cawangan/create-ajk": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view-ajk-bukan-wn": "external",

  "/pertubuhan/society/:id/senarai/cawangan/dokumen-sokongan": "external",
  "/pertubuhan/society/:id/senarai/cawangan/register": "external",
  "/pertubuhan/society/:id/senarai/cawangan/pembayaran": "external",
  "/pertubuhan/society/:id/senarai/cawangan/pembayaran/kaunter": "external",
  "/pertubuhan/society/:id/senarai/cawangan/pembayaran/online": "external",
  "/pertubuhan/society/:id/senarai/cawangan/pembayaran/online/term": "external",
  "/pertubuhan/society/:id/senarai/cawangan/pembayaran/online/butiran":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/branch-info": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view": "external",

  //PINDAAN NAMA DAN ALAMAT
  "/pertubuhan/society/678/senarai/cawangan/branch-Info": "external",
  "/pertubuhan/society/678/senarai/cawangan/branch-Info/form/:amendId/:branchId":
    "external",
  "/pertubuhan/society/678/senarai/cawangan/branch-Info/amend/:amendId/:branchId":
    "external",
  "/pertubuhan/society/678/senarai/cawangan/branch-Info/bayaran": "external",
  "/pertubuhan/society/678/senarai/cawangan/branch-Info/bayaran/kaunter":
    "external",
  "/pertubuhan/society/678/senarai/cawangan/branch-Info/bayaran/online":
    "external",
  "/pertubuhan/society/678/senarai/cawangan/branch-Info/bayaran/online/term":
    "external",
  "/pertubuhan/society/678/senarai/cawangan/branch-Info/bayaran/online/butiran":
    "external",

  //Maklumat Petubuhan
  "/pertubuhan/society/:id/senarai/cawangan": "external",
  "/pertubuhan/society/:id/senarai/cawangan/maklumat-am": "external",

  // PENGURUSAN PERLEMBAGAAN
  "/pertubuhan/society/:id/senarai/cawangan/view/perlembagaan/maklumat":
    "external",

  //PENGURUSAN AJK
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/jawatankuasa": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/jawatankuasa/create-ajk":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/jawatankuasa/update-ajk":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/jawatankuasa/create-ajk-bukan-wn":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/jawatankuasa/kemaskini-ajk":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/ahli": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/ahli/daftar": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/ahli/edit/:id": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/ahli/serta-pertubuhan":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pemegang-amanah":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pemegang-amanah/create":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pegawai": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pegawai/create-awam":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pegawai/create-awam/bayaran":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pegawai/create-awam/bayaran/kaunter":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pegawai/create-awam/bayaran/online":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pegawai/create-awam/bayaran/online/term":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pegawai/create-awam/bayaran/online/butiran":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pegawai/create-harta":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pegawai/create-harta/bayaran":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pegawai/create-harta/bayaran/kaunter":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pegawai/create-harta/bayaran/online":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pegawai/create-harta/bayaran/online/term":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/pegawai/create-harta/bayaran/online/butiran":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/juruaudit": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/juruaudit/create":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/juruaudit/view":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/ajk/aliran-tugas": "external",

  //PENYATA TAHUNAN
  "/pertubuhan/society/:id/senarai/cawangan/view/penyataTahunan": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/penyataTahunan/cawangan-penyata-tahunan-info":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/penyataTahunan/cawangan-penyata-tahunan-bank":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/penyataTahunan/cawangan-penyata-tahunan-agung":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/penyataTahunan/cawangan-penyata-tahunan-ajk":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/penyataTahunan/cawangan-penyata-tahunan-ajk/view":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/penyataTahunan/cawangan-penyata-tahunan-juruaudit":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/penyataTahunan/cawangan-penyata-tahunan-juruaudit/create":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/penyataTahunan/cawangan-penyata-tahunan-pendapatan":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/penyataTahunan/cawangan-penyata-tahunan-laporan-aktiviti":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/penyataTahunan/cawangan-penyata-tahunan-sumbangan":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/penyataTahunan/cawangan-penyata-tahunan-sumbangan/createSumbangan":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/penyataTahunan/cawangan-penyata-tahunan-display":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/penyataTahunan/cawangan-penyata-tahunan-pengakuan":
    "external",

  // PENGURUSAN MESYUARAT
  "/pertubuhan/society/:id/senarai/cawangan/view/mesyuarat": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/minute-mesyuarat": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/:meetingId": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/create": "external",
  "/pertubuhan/society/:id/senarai/cawangan/minute-mesyuarat": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/mesyuarat/:id": "external",

  //PEMBUBARAN
  "/pertubuhan/society/:id/senarai/pembubaran": "external",
  "/pertubuhan/society/:id/senarai/pembubaran/create": "external",
  "/pertubuhan/society/:id/senarai/pembubaran/:liquidationId": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/pembubaran": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/pembubaran/create": "external",
  "/pertubuhan/society/:id/senarai/cawangan/view/pembubaran/:liquidationId":
    "external",

  //PEMBAHARUAN SETIAUSAHA CAWANGAN
  "/pertubuhan/society/:id/senarai/cawangan/pembaharuan": "external",
  "/pertubuhan/society/:id/senarai/cawangan/pembaharuan/pelantikan/:branchId":
    "external",
  "/pertubuhan/society/:id/senarai/cawangan/pembaharuan/cetak/:secretrayBranchId":
    "external",

  // Add your route registrations here
});

const RouteGuardedSocietyLayout = () => (
  <RouteGuard autoUpdatePortal={true} showDebugInfo={import.meta.env.DEV}>
    <SocietyLayout />
  </RouteGuard>
);

export const society = {
  routes: (
    <Route path="society/:id" element={<RouteGuardedSocietyLayout />}>
      <Route path="dashboard" element={<DashboardPertubuhan />} />

      <Route
        path="senarai"
        element={
          <SenaraiLayout>
            <Outlet />
          </SenaraiLayout>
        }
      >
        <Route index element={<Navigate to="maklumat" />} />

        <Route path="maklumat">
          <Route index element={<MaklumatPertubuhan />} />
        </Route>

        <Route path="perlembagaan">
          <Route path="maklumat">
            <Route
              index
              element={
                <JawatankuasaProvider>
                  <UpdateMaklumatPerlembagaan />
                </JawatankuasaProvider>
              }
            />

            <Route path="pindaan-perlembagaan">
              <Route index element={<ListPindaanPerlembagaanNew />} />

              <Route path="update/:clauseId">
                <Route
                  index
                  element={
                    <JawatankuasaProvider>
                      <UpdatePindaanPerlembagaan />
                    </JawatankuasaProvider>
                  }
                />
              </Route>
              <Route path="semakan">
                <Route index element={<SemakanPerlembagaan />} />
              </Route>

              <Route path="add/:currentNo">
                <Route index element={<FasalContentAdd />} />
              </Route>

              {/* <Route path="bayaran">
                <Route index element={<BayaranPerlembagaan />} />
              </Route> */}
              <Route path="bayaran">
                <Route index element={<BayaranPerlembagaan />} />

                <Route
                  path="kaunter"
                  element={<BayaranPerlembagaanKaunter />}
                />

                <Route path="online">
                  <Route index element={<BayaranPerlembagaanOnline />} />

                  <Route path="term" element={<BayaranPerlembagaanTerm />} />

                  <Route
                    path="butiran"
                    element={<BayaranPerlembagaanButiran />}
                  />
                </Route>
              </Route>
            </Route>
          </Route>

          <Route path="migrasi">
            <Route index element={<MigrasiPerlembagaan />} />
            {/* <Route index element={<ListSenaraiPindaan />} /> */}

            <Route path="pindaan-perlembagaan">
              <Route index element={<ListPindaanPerlembagaan />} />

              <Route path="update/:id">
                <Route index element={<UpdatePindaanPerlembagaan />} />
              </Route>

              <Route path="semakan">
                <Route index element={<SemakanPerlembagaan />} />
              </Route>

              <Route path="bayaran">
                <Route index element={<BayaranPerlembagaan />} />
              </Route>
            </Route>

            {/* <Route path="papar">
              <Route index element={<PaparMaklumatPerlembagaan />} />
            </Route> */}
          </Route>
        </Route>

        <Route path="mesyuarat">
          <Route
            index
            element={
              <JawatankuasaProvider>
                <ListMesyuarat />
              </JawatankuasaProvider>
            }
          />
          <Route path=":meetingId" element={<ViewMesyuarat />} />

          <Route path="createMeeting">
            <Route index element={<CreateMesyuarat />} />
          </Route>
        </Route>

        {/* <Route path="tugasan">
          <Route index element={<ListTugas />} />
        </Route> */}

        <Route path="pembubaran">
          <Route index element={<Pembubaran />} />

          <Route
            path="create"
            element={
              <PembubaranProvider>
                <FormPembubaran />
              </PembubaranProvider>
            }
          />

          <Route
            path=":liquidationId"
            element={
              <PembubaranProvider>
                <FormPembubaran />
              </PembubaranProvider>
            }
          />

          {/* <Route
            path=":liquidationId/feedback"
            element={<FeedbackPembubaran />}
          /> */}
        </Route>

        <Route path="ajk">
          <Route path="jawatankuasa">
            <Route
              index
              element={
                <JawatankuasaProvider>
                  <Jawatankuasa />
                </JawatankuasaProvider>
              }
            />

            <Route path="create-ajk">
              <Route
                index
                element={
                  <JawatankuasaProvider>
                    <CreateAjkJawatankuasa />
                  </JawatankuasaProvider>
                }
              />
            </Route>

            <Route path="create-ajk-bukan-wn">
              <Route
                index
                element={
                  <JawatankuasaProvider>
                    <CreateAjkBknWnJawatankuasa />
                  </JawatankuasaProvider>
                }
              />
            </Route>
            <Route path="update-ajk">
              <Route
                index
                element={
                  <JawatankuasaProvider>
                    <UpdateAJK />
                  </JawatankuasaProvider>
                }
              />

              {/* <Route path="update">
                <Route index element={<CreateRayuan />} />
              </Route>

              <Route path="updateBknWN">
                <Route index element={<CreateAjkBukanWn />} />
              </Route> */}
            </Route>
          </Route>

          <Route path="ahli">
            <Route
              index
              element={
                <JawatankuasaProvider>
                  <AhliPertubuhan />
                </JawatankuasaProvider>
              }
            />

            <Route path="daftar">
              <Route index element={<DaftarAhli />} />
            </Route>

            <Route path="edit/:memberId">
              <Route index element={<DaftarAhli />} />
            </Route>

            <Route path="serta-pertubuhan">
              <Route index element={<AhliSertaPertubuhan />} />
            </Route>
          </Route>

          <Route path="pemegang-amanah">
            <Route
              index
              element={
                <JawatankuasaProvider>
                  <MaklumatPemegangAmanah />
                </JawatankuasaProvider>
              }
            />

            <Route path="create">
              <Route
                index
                element={
                  <JawatankuasaProvider>
                    <CreatePemegangAmanah />
                  </JawatankuasaProvider>
                }
              />
            </Route>
          </Route>

          <Route path="pegawai">
            <Route
              index
              element={
                <JawatankuasaProvider>
                  <Pegawai />
                </JawatankuasaProvider>
              }
            />

            <Route path="create-awam">
              <Route
                index
                element={
                  <JawatankuasaProvider>
                    <CreatePegawaiAwam />
                  </JawatankuasaProvider>
                }
              />

              <Route path="bayaran">
                <Route index element={<BayaranPegawaiList />} />

                <Route path="kaunter" element={<BayaranPegawaiKaunter />} />

                <Route path="online">
                  <Route index element={<BayaranPegawaiOnline />} />

                  <Route path="term" element={<BayaranPegawaiTerm />} />

                  <Route path="butiran" element={<BayaranPegawaiButiran />} />
                </Route>
              </Route>
              {/* <Route path="bayaran" element={
                <JawatankuasaProvider>
                  <BayaranAwam />
                </JawatankuasaProvider>
              } /> */}
            </Route>

            <Route path="create-harta">
              <Route
                index
                element={
                  <JawatankuasaProvider>
                    <CreatePegawaiHarta />
                  </JawatankuasaProvider>
                }
              />

              <Route path="bayaran">
                <Route index element={<BayaranPegawaiList />} />

                <Route path="kaunter" element={<BayaranPegawaiKaunter />} />

                <Route path="online">
                  <Route index element={<BayaranPegawaiOnline />} />

                  <Route path="term" element={<BayaranPegawaiTerm />} />

                  <Route path="butiran" element={<BayaranPegawaiButiran />} />
                </Route>
              </Route>
            </Route>
          </Route>

          <Route path="juruaudit">
            <Route
              index
              element={
                <JawatankuasaProvider>
                  <JuruAudit />
                </JawatankuasaProvider>
              }
            />

            <Route path="create">
              <Route
                index
                element={
                  <JawatankuasaProvider>
                    <CreateJuruAudit />
                  </JawatankuasaProvider>
                }
              />
            </Route>
            <Route path="view">
              <Route
                index
                element={
                  <JawatankuasaProvider>
                    <ViewJuruAudit />
                  </JawatankuasaProvider>
                }
              />
            </Route>
          </Route>

          <Route path="aliran-tugas">
            <Route
              index
              element={
                <JawatankuasaProvider>
                  <ALiranTugas />
                </JawatankuasaProvider>
              }
            />
          </Route>
        </Route>

        <Route path="penyataTahunan">
          <Route
            index
            element={
              <JawatankuasaProvider>
                <ListPenyata />
              </JawatankuasaProvider>
            }
          />

          <Route path="penyata-tahunan-info">
            <Route index element={<CreateMam />} />
          </Route>

          <Route path="penyata-tahunan-bank">
            <Route index element={<CreateAccountBank />} />
          </Route>

          <Route path="penyata-tahunan-agung">
            <Route index element={<CreateMesyuaratAgung />} />
          </Route>

          <Route path="penyata-tahunan-ajk">
            <Route index element={<ListMaklumatAjk />} />

            <Route path="view" element={<CreateAjk />} />
          </Route>

          <Route path="penyata-tahunan-juruaudit">
            <Route index element={<PenyataTahunanJuruAudit />} />

            <Route path="create" element={<PenyataTahunanCreateJuruAudit />} />
          </Route>

          <Route path="penyata-tahunan-pendapatan">
            <Route index element={<CreatePernyataPendapatan />} />
          </Route>

          {/* <Route path="aset">
            <Route index element={<CreateAsetDanLiabiliti />} />
          </Route> */}

          <Route path="penyata-tahunan-laporan-aktiviti">
            <Route index element={<CreateLaporanAktiviti />} />
          </Route>

          <Route path="penyata-tahunan-sumbangan">
            <Route index element={<IndexSumbanganNegara />} />
            <Route
              path="createSumbangan"
              element={<PenyataTahunanCreateSumbangan />}
            />
          </Route>

          <Route path="penyata-tahunan-display">
            <Route index element={<PenyataTahunanPaparan />} />
          </Route>

          <Route path="penyata-tahunan-pengakuan">
            <Route index element={<PenyataTahunanPengakuan />} />
          </Route>
        </Route>

        <Route path="cawangan">
          <Route index element={<ListCawangan />} />

          <Route path="view" element={<BranchProvider />}>
            <Route index element={<CawanganMaklumat />} />

            <Route path="maklumat" element={<CawanganMaklumat />} />

            <Route path="perlembagaan">
              <Route path="maklumat">
                <Route index element={<CawanganPerlembagaan />} />
              </Route>
            </Route>

            <Route path="mesyuarat">
              <Route index element={<CawanganMesyuarat />} />
              <Route
                path=":meetingId"
                element={<CawanganViewMesyuarat viewOnly={true} />}
              />
              <Route path="create" element={<CawanganCreateMesyuarat />} />
            </Route>

            <Route path="pembubaran">
              <Route index element={<CawanganPembubaran />} />

              <Route
                path="create"
                element={
                  <PembubaranProvider>
                    <CawanganFormPembubaran />
                  </PembubaranProvider>
                }
              />

              <Route
                path=":liquidationId"
                element={
                  <PembubaranProvider>
                    <CawanganFormPembubaran />
                  </PembubaranProvider>
                }
              />

              {/* <Route
                path=":liquidationId/feedback"
                element={<CawanganFeedbackPembubaran />}
              /> */}
            </Route>

            <Route path="ajk">
              <Route path="jawatankuasa">
                <Route
                  index
                  element={
                    // <CawanganJawatankuasaProvider>
                    //   <CawanganJawatankuasa />
                    // </CawanganJawatankuasaProvider>
                    <JawatankuasaCawangan />
                  }
                />

                <Route path="create-ajk">
                  <Route
                    index
                    element={
                      <CawanganJawatankuasaProvider>
                        <CawanganCreateAjkJawatankuasa />
                      </CawanganJawatankuasaProvider>
                    }
                  />
                </Route>

                <Route path="kemaskini-ajk">
                  <Route
                    index
                    element={
                      <CawanganJawatankuasaProvider>
                        <CawanganKemasKiniAjkAjkJawatankuasa />
                      </CawanganJawatankuasaProvider>
                    }
                  />
                </Route>

                <Route path="create-ajk-bukan-wn">
                  <Route
                    index
                    element={
                      <CawanganJawatankuasaProvider>
                        <CawanganCreateAjkBknWnJawatankuasa />
                      </CawanganJawatankuasaProvider>
                    }
                  />
                </Route>
                <Route path="update-ajk">
                  <Route
                    index
                    element={
                      <CawanganJawatankuasaProvider>
                        <CawanganUpdateAJK />
                      </CawanganJawatankuasaProvider>
                    }
                  />
                </Route>
              </Route>

              <Route path="ahli">
                <Route index element={<CawanganAhliPertubuhan />} />

                <Route path="daftar">
                  <Route index element={<CawanganDaftarAhli />} />
                </Route>

                <Route path="edit/:memberId">
                  <Route index element={<CawanganDaftarAhli />} />
                </Route>

                <Route path="serta-pertubuhan">
                  <Route index element={<CawanganAhliSertaPertubuhan />} />
                </Route>
              </Route>

              <Route path="pemegang-amanah">
                <Route
                  index
                  element={
                    <CawanganJawatankuasaProvider>
                      <CawanganMaklumatPemegangAmanah />
                    </CawanganJawatankuasaProvider>
                  }
                />

                <Route path="create">
                  <Route
                    index
                    element={
                      <CawanganJawatankuasaProvider>
                        <CawanganCreatePemegangAmanah />
                      </CawanganJawatankuasaProvider>
                    }
                  />
                </Route>
              </Route>

              <Route path="pegawai">
                <Route
                  index
                  element={
                    <CawanganJawatankuasaProvider>
                      <CawanganPegawai />
                    </CawanganJawatankuasaProvider>
                  }
                />

                <Route path="create-awam">
                  <Route
                    index
                    element={
                      <CawanganJawatankuasaProvider>
                        <CawanganCreatePegawaiAwam />
                      </CawanganJawatankuasaProvider>
                    }
                  />

                  <Route path="bayaran">
                    <Route index element={<CawanganBayaranPegawaiList />} />

                    <Route
                      path="kaunter"
                      element={<CawanganBayaranPegawaiKaunter />}
                    />

                    <Route path="online">
                      <Route index element={<CawanganBayaranPegawaiOnline />} />

                      <Route
                        path="term"
                        element={<CawanganBayaranPegawaiTerm />}
                      />

                      <Route
                        path="butiran"
                        element={<CawanganBayaranPegawaiButiran />}
                      />
                    </Route>
                  </Route>
                  {/* <Route path="bayaran" element={
                <JawatankuasaProvider>
                  <BayaranAwam />
                </JawatankuasaProvider>
              } /> */}
                </Route>

                <Route path="create-harta">
                  <Route
                    index
                    element={
                      <CawanganJawatankuasaProvider>
                        <CawanganCreatePegawaiHarta />
                      </CawanganJawatankuasaProvider>
                    }
                  />

                  <Route path="bayaran">
                    <Route index element={<CawanganBayaranPegawaiList />} />

                    <Route
                      path="kaunter"
                      element={<CawanganBayaranPegawaiKaunter />}
                    />

                    <Route path="online">
                      <Route index element={<CawanganBayaranPegawaiOnline />} />

                      <Route
                        path="term"
                        element={<CawanganBayaranPegawaiTerm />}
                      />

                      <Route
                        path="butiran"
                        element={<CawanganBayaranPegawaiButiran />}
                      />
                    </Route>
                  </Route>
                </Route>
              </Route>

              <Route path="juruaudit">
                <Route
                  index
                  element={
                    <CawanganJawatankuasaProvider>
                      <CawanganJuruAudit />
                    </CawanganJawatankuasaProvider>
                  }
                />

                <Route path="create">
                  <Route
                    index
                    element={
                      <CawanganJawatankuasaProvider>
                        <CawanganCreateJuruAudit />
                      </CawanganJawatankuasaProvider>
                    }
                  />
                </Route>
                <Route path="view">
                  <Route
                    index
                    element={
                      <CawanganJawatankuasaProvider>
                        <CawanganViewJuruAudit />
                      </CawanganJawatankuasaProvider>
                    }
                  />
                </Route>
              </Route>

              <Route path="aliran-tugas">
                <Route
                  index
                  element={
                    <CawanganJawatankuasaProvider>
                      <CawanganALiranTugas />
                    </CawanganJawatankuasaProvider>
                  }
                />
              </Route>
            </Route>

            <Route path="penyataTahunan">
              <Route
                index
                element={
                  <CawanganJawatankuasaProvider>
                    <CawanganListPenyata />
                  </CawanganJawatankuasaProvider>
                }
              />

              <Route path="cawangan-penyata-tahunan-info">
                <Route index element={<CawanganCreateMam />} />
              </Route>

              <Route path="cawangan-penyata-tahunan-bank">
                <Route index element={<CawanganCreateAccountBank />} />
              </Route>

              <Route path="cawangan-penyata-tahunan-agung">
                <Route index element={<CawanganCreateMesyuaratAgung />} />
              </Route>

              <Route path="cawangan-penyata-tahunan-ajk">
                <Route index element={<CawanganListMaklumatAjk />} />

                <Route path="view" element={<CawanganCreateAjk />} />
              </Route>

              <Route path="cawangan-penyata-tahunan-juruaudit">
                <Route index element={<CawanganPenyataTahunanJuruAudit />} />

                <Route
                  path="create"
                  element={<CawanganPenyataTahunanCreateJuruAudit />}
                />
              </Route>

              <Route path="cawangan-penyata-tahunan-pendapatan">
                <Route index element={<CawanganCreatePernyataPendapatan />} />
              </Route>

              {/* <Route path="aset">
                <Route index element={<CawanganCreateAsetDanLiabiliti />} />
              </Route> */}

              <Route path="cawangan-penyata-tahunan-laporan-aktiviti">
                <Route index element={<CreateLaporanAktivitiCawangan />} />
              </Route>

              <Route path="cawangan-penyata-tahunan-pendapatan">
                <Route index element={<CawanganCreatePernyataPendapatan />} />
              </Route>

              <Route path="cawangan-penyata-tahunan-sumbangan">
                <Route index element={<CawanganIndexSumbanganNegara />} />
                <Route
                  path="createSumbangan"
                  element={<CawanganPenyataTahunanCreateSumbangan />}
                />
              </Route>

              <Route path="cawangan-penyata-tahunan-display">
                <Route index element={<CawanganPenyataTahunanPaparan />} />
              </Route>

              <Route path="cawangan-penyata-tahunan-pengakuan">
                <Route index element={<CawanganPenyataTahunanPengakuan />} />
              </Route>
            </Route>

            <Route path="dokumen">
              <Route index element={<CawanganDocument />} />
            </Route>
          </Route>

          <Route path="maklumat-am" element={<CreateCawanganMam />} />

          <Route path="expired">
            <Route path="maklumat" element={<MaklumatAmPaperExpired />} />

            <Route path="minit" element={<MinitPaperExpired />} />

            <Route path="ajk" element={<AjkPaperExpired />} />
          </Route>

          <Route path="minute-mesyuarat" element={<MinuteMesyuarat />} />

          <Route path="ahlijawatankuasa" element={<AhliJawatanKuasa />} />

          <Route path="create-ajk">
            <Route index element={<CreateAjkCawangan />} />
          </Route>

          <Route path="create-ajk-bukan-wn">
            <Route index element={<CreateAjkBukanWnCawangan />} />
          </Route>
          <Route path="view-ajk-bukan-wn">
            <Route index element={<ViewAjkBukanWnCawangan />} />
          </Route>

          <Route path="dokumen-sokongan" element={<DokumenSokongan />} />

          <Route path="lanjut-masa">
            <Route path="create">
              <Route index element={<CreateLanjutMasa />} />
            </Route>
          </Route>

          <Route path=":id/papar" element={<PaparCawangan />} />

          <Route path="register" element={<RegisterCawangan />} />

          <Route path="pembaharuan">
            <Route index element={<PembaharuanSetiausahaCawangan />} />

            <Route path="pelantikan/:branchId" element={<Pelantikan />} />

            <Route path="cetak/:secretaryBranchId" element={<Cetak />} />
          </Route>

          <Route path="branch-Info">
            <Route index element={<CawanganPindaan />} />

            <Route path="form" element={<MeetingForm />} />
            <Route
              path="amend"
              element={<SelectPindaan />}
            />

            {/* branch amend payment */}
            <Route path="bayaran">
              <Route index element={<ListBayaranCawanganPindaan />} />
              <Route path="kaunter" element={<KaunterCawanganPindaan />} />
              <Route path="online">
                <Route index element={<OnlineCawanganPindaan />} />
                <Route path="term" element={<TermCawanganPindaan />} />
                <Route path="butiran" element={<ButiranCawanganPindaan />} />
              </Route>
            </Route>
          </Route>

          <Route path="pembayaran">
            <Route index element={<PembayaranCawangan />} />

            <Route path="kaunter" element={<Kaunter />} />

            <Route path="online">
              <Route index element={<Online />} />

              <Route path="term" element={<Term />} />

              <Route path="butiran" element={<Butiran />} />
            </Route>
          </Route>
        </Route>

        <Route path="dokumen">
          <Route index element={<ListDokumenSokongan />} />
        </Route>
      </Route>
    </Route>
  ),
};
