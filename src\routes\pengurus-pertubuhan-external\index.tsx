import { Route, Outlet } from "react-router-dom";
import PertubuhanExternal from "../../pages/pengurus-pertubuhan-external/Pertubuhan";
import { registerRoutes } from "../../helpers/routeDetector";
import { RouteGuard } from "../../components/RouteGuard";

// Layout component to wrap all pengurus-pertubuhan-external routes with protection
const PengurusPertubuhanExternalLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === "development"}
  >
    <Outlet />
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example route registrations (uncomment and modify as needed):
  "/pengurus-pertubuhan": "shared",
  "/pengurus-pertubuhan/pertubuhan": "external",
  // '/pengurus-pertubuhan/pertubuhan/create': 'external',
  // '/pengurus-pertubuhan/pertubuhan/edit/:id': 'external',
  // '/pengurus-pertubuhan/pertubuhan/view/:id': 'external',
  // '/pengurus-pertubuhan/members': 'external',
  // '/pengurus-pertubuhan/members/add': 'external',
  // '/pengurus-pertubuhan/settings': 'external',
  // Add your route registrations here
});

const routes = [
  {
    path: "pertubuhan",
    page: <PertubuhanExternal />,
  },
];

export const pengurus_pertubuhan_external = {
  routes: (
    <Route
      path="pengurus-pertubuhan"
      element={<PengurusPertubuhanExternalLayout />}
    >
      {routes.map(({ path, page }) => (
        <Route key={path} path={path} element={page} />
      ))}
    </Route>
  ),
};
