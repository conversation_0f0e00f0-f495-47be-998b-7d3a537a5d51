import { ButtonPrimary } from "@/components";
import { TrashIcon } from "@/components/icons";
import { removeFieldFromSection, setFieldField } from "@/redux/geranReducer";
import { RootState } from "@/redux/store";
import DoNotDisturbOnIcon from "@mui/icons-material/DoNotDisturbOn";
import {
  Box,
  Checkbox,
  Grid,
  IconButton,
  Switch,
  TextField,
  useTheme,
} from "@mui/material";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { useSelector, useDispatch } from "react-redux";

type CardFormProps = {
  order: number;
  sectionId: string;
  fieldId: string;
};

const CheckBoxCardForm: React.FC<CardFormProps> = ({
  order,
  sectionId,
  fieldId,
}) => {
  const theme = useTheme();
  const primary = theme.palette.primary.main;
  const dispatch = useDispatch();

  const { control } = useForm<FieldValues>({
    defaultValues: {},
  });

  const field = useSelector((state: RootState) =>
    state.geran.sections
      .find((s: any) => s.id === sectionId)
      ?.fields.find((f: any) => f.id === fieldId)
  );

  if (!field) return null;

  const { fieldName, isRequired, options, sectionName = ["", ""] } = field;

  return (
    <Box
      mt={2}
      sx={{
        borderRadius: 3,
        border: `0.5px solid ${primary}`,
        padding: "22px 34px",
      }}
    >
      <Grid container spacing={2} mb={2}>
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "stretch",
              borderRadius: "12px",
              backgroundColor: "#fff",
            }}
          >
            {/* LEFT SIDE */}
            <Box flex={1} display="flex" flexDirection="column" gap={2}>
              {order === 1 && (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      value={sectionName ?? ""}
                      onChange={(e) =>
                        dispatch(
                          setFieldField({
                            sectionId,
                            fieldId,
                            sectionName: e.target.value,
                          })
                        )
                      }
                      placeholder="Tulis Tajuk Disini"
                      variant="outlined"
                      size="small"
                      fullWidth
                    />
                  </Grid>
                </Grid>
              )}

              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <TextField
                    placeholder="Deskripsi"
                    variant="outlined"
                    size="small"
                    fullWidth
                    value={fieldName ?? ""}
                    onChange={(e) =>
                      dispatch(
                        setFieldField({
                          sectionId,
                          fieldId,
                          fieldName: e.target.value,
                        })
                      )
                    }
                  />
                </Grid>

                <Grid item xs={12} sm={8}>
                  <Controller
                    control={control}
                    name={`fields.${fieldId}.options`}
                    defaultValue={options}
                    render={({ field: { value, onChange } }) => (
                      <Box display="flex" flexDirection="column" gap={1}>
                        {value.map((option: string, index: number) => (
                          <Box
                            key={index}
                            display="flex"
                            alignItems="center"
                            gap={1}
                          >
                            <Checkbox
                              checked
                              sx={{
                                width: 21,
                                height: 21,
                                padding: 0,
                                "&.MuiCheckbox-root": {
                                  borderRadius: "3px",
                                  border: "0.5px solid #979797",
                                },
                                "&.Mui-checked": {
                                  backgroundColor: primary,
                                  borderColor: primary,
                                },
                                "& .MuiSvgIcon-root": { display: "none" },
                              }}
                            />
                            <TextField
                              fullWidth
                              size="small"
                              placeholder={`Opsi ${index + 1}`}
                              value={option}
                              onChange={(e) => {
                                const newOptions = [...value];
                                newOptions[index] = e.target.value;
                                onChange(newOptions);
                                dispatch(
                                  setFieldField({
                                    sectionId,
                                    fieldId,
                                    options: newOptions,
                                  })
                                );
                              }}
                            />
                            {value.length > 1 && (
                              <IconButton
                                color="error"
                                onClick={() => {
                                  const newOptions = value.filter(
                                    (_: any, i: any) => i !== index
                                  );
                                  onChange(newOptions);
                                  dispatch(
                                    setFieldField({
                                      sectionId,
                                      fieldId,
                                      options: newOptions,
                                    })
                                  );
                                }}
                              >
                                <DoNotDisturbOnIcon fontSize="small" />
                              </IconButton>
                            )}
                          </Box>
                        ))}
                        <Box display="flex" justifyContent="flex-end">
                          <ButtonPrimary
                            type="button"
                            onClick={() => {
                              const newOptions = [...value, ""];
                              onChange(newOptions);
                              dispatch(
                                setFieldField({
                                  sectionId,
                                  fieldId,
                                  options: newOptions,
                                })
                              );
                            }}
                            sx={{ mt: 1, fontSize: "10px", minWidth: "100px" }}
                          >
                            Tambah Pilihan
                          </ButtonPrimary>
                        </Box>
                      </Box>
                    )}
                  />
                </Grid>
              </Grid>
            </Box>

            {/* RIGHT SIDE */}
            <Box
              display="flex"
              flexDirection="column"
              justifyContent="space-between"
              ml={2}
            >
              <Box
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "12px",
                  padding: "8px 16px",
                  marginBottom: "12px",
                  backgroundColor: "#fff",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Switch
                  checked={isRequired || false}
                  onChange={(e) =>
                    dispatch(
                      setFieldField({
                        sectionId,
                        fieldId,
                        isRequired: e.target.checked,
                      })
                    )
                  }
                />
              </Box>

              <Box
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "12px",
                  padding: "8px 16px",
                  backgroundColor: "#fff",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <IconButton
                  sx={{ color: "#FF0000", p: 1 }}
                  onClick={() =>
                    dispatch(removeFieldFromSection({ sectionId, fieldId }))
                  }
                >
                  <TrashIcon sx={{ width: 20, height: 20 }} />
                </IconButton>
              </Box>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CheckBoxCardForm;
