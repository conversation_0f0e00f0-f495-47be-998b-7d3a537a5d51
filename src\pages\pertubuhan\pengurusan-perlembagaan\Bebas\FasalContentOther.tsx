import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import CustomPopover from "../../../../components/popover";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import { DisabledTextField } from "@/components";

interface FasalContentTigaProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: string;
  name: string;
}

export const FasalContentOther: React.FC<FasalContentTigaProps> = ({
  activeStep,
  setActiveStep,
  clause,
  asalData,
  name,
}) => {
  const { id, clauseId } = useParams();
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [matlamatPertubuhan, setMatlamatPertubuhan] = useState("");

  const [clauseContentId, setClauseContentId] = useState("");
  const [clauseContent, setClauseContent] = useState("");
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [fasalNo, setFasalNo] = useState(clauseId);
  const [fasalTajuk, setFasalTajuk] = useState("");
  const [checked, setChecked] = useState(false);

  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  useEffect(() => {
    if (clause) {
      //const clause = JSON.parse(clause3);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        setClauseContent(clause.clauseContent);
        setClauseContentId(clause.clauseContentId);
      }
      if (clause.constitutionValues.length > 0) {
        setFasalTajuk(clause.clauseName || "");
        setMatlamatPertubuhan(
          clause.constitutionValues[0]?.definitionName ?? ""
        );
      }

      setIsEdit(clause.edit);
    }
  }, [clause]);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!fasalTajuk) {
      errors.fasalTajuk = t("fieldRequired");
    }
    if (!matlamatPertubuhan) {
      errors.matlamatPertubuhan = t("fieldRequired");
    }

    return errors;
  };

  const handleFasalNo = (e: any) => {
    const value = e.target.value;
    setFasalNo(value);
    setFormErrors((prevErrors) => ({
      ...prevErrors,
      fasalNo: "",
    }));
  };

  const handleTajukFasal = (e: any) => {
    const value = e.target.value;
    setFasalTajuk(value);
    setFormErrors((prevErrors) => ({
      ...prevErrors,
      fasalTajuk: "",
    }));
  };

  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography sx={{ mb: 1, ...sectionStyle }}>Maklumat Fasal</Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("NomborFasal")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              value={fasalNo}
              onKeyDown={(e) => {
                if (["e", "E", "+", "-", "."].includes(e.key)) {
                  e.preventDefault();
                }
              }}
              onChange={(e) => {
                handleFasalNo(e);
              }}
              type="number"
              inputProps={{ inputMode: "numeric", pattern: "[0-9]*" }}
              fullWidth
              sx={{ background: "#fff" }}
              error={!!formErrors.fasalNo}
              helperText={formErrors.fasalNo}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("NamaFasal")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              value={fasalTajuk}
              onChange={handleTajukFasal}
              placeholder={t("tajukfasal")}
              fullWidth
              sx={{ background: "#fff" }}
              error={!!formErrors.fasalTajuk}
              helperText={formErrors.fasalTajuk}
            />
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          background: "#fff",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clause")} {clauseId} : {name}
        </Typography>

        <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
          <Typography sx={labelStyle}>{t("organizationGoals")}</Typography>
          {/* <CustomPopover
            content={
              <>
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  sx={{ mb: 1, color: "#666666" }}
                >
                  Format
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ lineHeight: 1.5, color: "#666666" }}
                >
                  1. &lt;matlamat 1&gt;
                  <br />
                  2. &lt;matlamat 2&gt;
                </Typography>
              </>
            }
          /> */}
        </Box>
        <TextField
          fullWidth
          disabled={isViewMode}
          value={matlamatPertubuhan}
          variant="outlined"
          multiline
          rows={3}
          onChange={(e) => {
            setMatlamatPertubuhan(e.target.value);
            setFormErrors((prevErrors) => ({
              ...prevErrors,
              matlamatPertubuhan: "",
            }));
          }}
          error={!!formErrors.matlamatPertubuhan}
          helperText={formErrors.matlamatPertubuhan}
          sx={{
            "& fieldset": { borderRadius: "12px" },
            "& .MuiInputBase-input": { color: "black" },
          }}
        />
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <Box
                sx={{
                  whiteSpace: "pre-wrap",
                  wordWrap: "break-word",
                  py: 2,
                  height: "100%",
                }}
              >
                <Typography
                  sx={{ fontWeight: "400 !important" }}
                  dangerouslySetInnerHTML={{ __html: matlamatPertubuhan }}
                ></Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              const errors = validateForm();
              if (Object.keys(errors).length > 0) {
                setFormErrors(errors);
                return;
              }
              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                // clauseContentId,
                constitutionTypeId: 6,
                clauseNo: fasalNo,
                clauseName: fasalTajuk,
                dataId,
                isEdit,
                createClauseContent,
                editClauseContent,
                description: matlamatPertubuhan,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: matlamatPertubuhan,
                    titleName: "Matlamat Pertubuhan",
                  },
                ],
                clause: "clause3",
                clauseCount: 3,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentOther;
