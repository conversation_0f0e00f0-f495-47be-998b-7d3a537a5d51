import { Box, Grid, Icon<PERSON>utton, Typography } from "@mui/material";
import { t } from "i18next";
import { useNavigate } from "react-router-dom";
import { GridColDef } from "@mui/x-data-grid";

import Input from "../../../components/input/Input";
import { EditIcon } from "../../../components/icons";

import {
  SocietyCategoryResponseBodyGet,
  SocietyQueryRegistrationResponseBodyGet,
} from "../../../types";
import { ChangeEvent, useState } from "react";
import {
  capitalizeWords,
  getLocalStorage,
  toBase64,
  useBackendLocalization,
  useDataGrid,
  useQuery,
} from "@/helpers";
import { DataGridUI } from "@/components/datagrid/UI";
import { DataTable, IColumn } from "@/components";
import { FieldValues, useForm } from "react-hook-form";

const PendaftaranPertubuhanIndukTab = <
  SocietyData extends SocietyQueryRegistrationResponseBodyGet = SocietyQueryRegistrationResponseBodyGet,
  CategoryData extends SocietyCategoryResponseBodyGet = SocietyCategoryResponseBodyGet
>() => {
  const navigate = useNavigate();
  const { getTranslation } = useBackendLocalization<CategoryData>({
    enAttribute: "categoryNameEn",
    bmAttribute: "categoryNameBm",
  });
  const [searchParams, setSearchParams] = useState({
    categoryCode: "",
    subCategoryCode: "",
    societyName: "",
  });
  const [subCategoriesOptions, setSubCategoryOptions] = useState<
    { label: string; value: string | number }[]
  >([]);
  const { categoryCode, subCategoryCode, societyName } = searchParams;

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      searchQuery: undefined,
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");

  const {
    data: societyListData,
    isLoading: societyListDataIsLoading,
    refetch,
  } = useQuery({
    url: "society/roDecision/getAllPending/society/registration",
    filters: [
      {
        field: "pageSize",
        value: pageSize,
        operator: "eq",
      },
      {
        field: "pageNo",
        value: page,
        operator: "eq",
      },
      ...(typeof categoryCode === "number"
        ? [
            {
              field: "categoryCode",
              value: categoryCode,
              operator: "eq" as const,
            },
          ]
        : []),
      ...(typeof subCategoryCode === "number"
        ? [
            {
              field: "subCategoryCode",
              value: subCategoryCode,
              operator: "eq" as const,
            },
          ]
        : []),
      ...(societyName.length > 0
        ? [
            {
              field: "societyName",
              value: societyName,
              operator: "eq" as const,
            },
          ]
        : []),
      {
        field: "isQuery",
        value: 1,
        operator: "eq",
      },
    ],
  });

  const dataList = societyListData?.data?.data?.data || [];
  const societyRegistrationTotal = societyListData?.data?.data?.total || 0;

  const allCategoriesData = getLocalStorage(
    "category_list",
    []
  ) as CategoryData[];

  const categories = allCategoriesData.filter((item) => item.pid === 1);
  const categoriesOptions = categories.map(generateItemOptionFromData);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "30px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const columns: IColumn[] = [
    {
      field: "applicationNo",
      headerName: "No. PPM/NSID",
      flex: 1,
      align: "center",
    },
    {
      field: "creatorName",
      headerName: t("pemohon"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => {
        return <Box>{row?.creatorName ?? "-"}</Box>;
      },
    },
    {
      field: "societyName",
      headerName: t("pertubuhan"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => {
        return <Box>{row?.societyName ?? "-"}</Box>;
      },
    },
    {
      field: "tarikhAlih",
      headerName: t("tarikhAlir"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => row?.tarikhAlih ?? "-",
    },
    {
      field: "tarikhAntar",
      headerName: t("submissionDate"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => row?.submissionDate ?? "-",
    },
    {
      field: "roName",
      headerName: t("keputusanCawangan_RO"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => {
        return (
          <Box
            sx={{
              display: "flex",
              justifyItems: "center",
              alignItems: "center",
              textAlign: "center",
            }}
          >
            {row?.roName ?? "-"}
          </Box>
        );
      },
    },
    {
      field: "stateName",
      headerName: t("negeri"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => {
        return <Box>{row?.stateName ?? "-"}</Box>;
      },
    },
    {
      field: "actions",
      headerName: t("action"),
      flex: 1,
      align: "center",
      renderCell: ({ row }) => (
        <IconButton
          sx={{ minWidth: "3rem", minHeight: "3rem" }}
          color="primary"
          onClick={() =>
            navigate(
              `/pengurus-pertubuhan/keputusan-pertubuhan/kuiri/pendaftaran/${toBase64(
                row.id.toString()
              )}`
            )
          }
          // sx={{ color: "black", minWidth: 0, p: 0.5 }}
        >
          <EditIcon />
        </IconButton>
      ),
    },
  ];

  function generateItemOptionFromData(item: CategoryData) {
    return {
      label: capitalizeWords(getTranslation(item)),
      value: item.id,
    };
  }
  const handleFilter = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setSearchParams((prev) => ({
      ...prev,
      [name]: value.toLowerCase(),
    }));
  };
  const handleFilterSelect = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setSearchParams((prev) => ({
      ...prev,
      [name]: value,
      ...(name === "categoryCode" ? { subCategoryCode: "" } : {}),
    }));
    if (name === "categoryCode") {
      const subCategoriesOptions = allCategoriesData
        .filter((item) => item.pid === parseInt(value))
        .map(generateItemOptionFromData);
      setSubCategoryOptions(subCategoriesOptions);
    }
  };

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("senaraiKuiri")}
            </Typography>

            <Grid container>
              <Input
                name="categoryCode"
                label={t("kategoriPertubuhan")}
                onChange={handleFilterSelect}
                type="select"
                value={categoryCode}
                options={categoriesOptions}
                withClearIcon={categoryCode !== ""}
                onClearIconClicked={() =>
                  setSearchParams((prev) => ({
                    ...prev,
                    categoryCode: "",
                    subCategoryCode: "",
                  }))
                }
              />
              <Input
                disabled={categoryCode.length === 0}
                name="subCategoryCode"
                label={t("organizationSubCategory")}
                onChange={handleFilterSelect}
                type="select"
                value={subCategoryCode}
                options={subCategoriesOptions}
                withClearIcon={subCategoryCode !== ""}
                onClearIconClicked={() =>
                  setSearchParams((prev) => ({ ...prev, subCategoryCode: "" }))
                }
              />
              <Input
                name="societyName"
                label={t("namaPertubuhan")}
                onChange={handleFilter}
              />
            </Grid>
          </Box>
        </Box>
      </Box>

      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            textAlign: "center",
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",
            py: 2,
          }}
        >
          <Typography variant="h5" gutterBottom>
            {societyRegistrationTotal}
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
            {t("kuiriPendaftaranPertubuhanInduk")}
          </Typography>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mt: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("senaraiKuiri")}
          </Typography>

          <DataTable
            columns={columns}
            rows={dataList}
            page={page}
            rowsPerPage={pageSize}
            totalCount={societyRegistrationTotal}
            onPageChange={(newPage) => setValue("page", newPage)}
            onPageSizeChange={(newPageSize) => {
              setValue("page", 1);
              setValue("pageSize", newPageSize);
            }}
          />
        </Box>
      </Box>
    </>
  );
};

export default PendaftaranPertubuhanIndukTab;
