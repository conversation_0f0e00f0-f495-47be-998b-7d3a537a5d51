import React, { useState } from "react";
import type { TFunction } from "i18next";
import Box from "@mui/material/Box";
import { Grid, TextField, Typography } from "@mui/material";
import Input from "../../../../components/input/Input";
import {
  Control,
  Controller,
  FieldValues,
  UseFormGetValues,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { formatAndValidateNumber } from "@/helpers";

export const CreateLiabiliti: React.FC<{
  t: TFunction;
  control?: Control<FieldValues>; // Made optional by adding `?`
  setValue?: UseFormSetValue<FieldValues>; // Made optional by adding `?`
  getValues?: UseFormGetValues<FieldValues>; // Made optional by adding `?`
  watch?: UseFormWatch<FieldValues>; // Made optional by adding `?`
  checked?: boolean;
}> = ({ t, control, setValue, getValues, watch, checked }) => {
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);

  const renderInputGroup = (
    title: string,
    items: { label: string; variable: string }[]
  ) => (
    <>
      <Box
        sx={{
          background: "white",
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={4}>
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontSize: "14px",
                fontWeight: "500 !important",
              }}
            >
              {t("totalLiabilities")}
            </Typography>
          </Grid>

          <Grid item xs={8}>
            {items.map((item, index) =>
              control ? ( // Check if `control` is provided
                <Controller
                  name={item.variable}
                  control={control}
                  key={index}
                  defaultValue={
                    getValues ? getValues(item.variable) : undefined
                  } // Default value or empty string if `getValues` is undefined
                  render={({ field }) => (
                    <TextField
                      size="small"
                      fullWidth
                      disabled={checked || (!isManager && !isAliranTugasAccess)}
                      value={getValues ? getValues(item.variable) : undefined}
                      placeholder="0"
                      onChange={(e) => {
                        const formattedValue = formatAndValidateNumber(
                          e.target.value
                        );
                        if (formattedValue !== null) {
                          field.onChange(formattedValue);
                          if (setValue) {
                            setValue("totalLiability", formattedValue);
                          }
                        }
                      }}
                    />
                  )}
                />
              ) : (
                <Input
                  type="number"
                  label={t(item.label)}
                  required
                  key={index}
                  placeholder="0"
                  disabled={checked || (!isManager && !isAliranTugasAccess)}
                />
              )
            )}
          </Grid>
        </Grid>
      </Box>
    </>
  );

  return (
    <>
      {/* <Box
        sx={{
          background: "white",
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          py: 2,
          mb: 2,
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{
            color: "var(--primary-color)",
            borderRadius: "16px",
            fontSize: "14px",
            fontWeight: "500 !important",
          }}
        >
          {t("liabilities")}
        </Typography>
      </Box>

      {renderInputGroup("currentLiabilities", [
        { label: "creditors", variable: "creditorLiability" },
        { label: "unpaidTaxes", variable: "taxLiability" },
        { label: "loans", variable: "loanLiability" },
      ])}

      {renderInputGroup("longTermLiabilities", [
        { label: "longTermDebt", variable: "debtLiability" },
        { label: "unpaidDeferredTaxes", variable: "deferredTaxLiability" },
        { label: "longTermLoans", variable: "borrowLiability" },
      ])} */}

      {renderInputGroup("", [
        { label: "totalLiabilities", variable: "totalLiability" },
      ])}
    </>
  );
};

export default CreateLiabiliti;
