import {
  Box,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
  FormControl,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { handleSaveContent } from "../helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "@/helpers/utils";
import { FasalBebasProps } from "../Fasal";
import EditableFasalTextArea from "@/components/FasalBebasComponent/EditableFasalTextArea";
import FasalNameCom from "@/components/FasalBebasComponent/FasalNameCom";
import ReminderEditable from "@/components/FasalBebasComponent/ReminderEditable";
import ContentBox from "@/components/FasalBebasComponent/ContentBox";
import CheckContent from "@/components/FasalBebasComponent/CheckContent";
import { RegExNumbers } from "@/helpers";
import CustomPopover from "@/components/popover";
import { DisabledTextField } from "@/components";

export const FasalContentOtherCawangan: React.FC<FasalBebasProps> = ({
  activeStep,
  setActiveStep,
  clause,
  asalData,
  name,
}) => {
  const { id, clauseId } = useParams();
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [matlamatPertubuhan, setMatlamatPertubuhan] = useState("");

  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [clauseContent, setClauseContent] = useState("");
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [fasalNo, setFasalNo] = useState(id);
  const [fasalTajuk, setFasalTajuk] = useState("");
  const [checked, setChecked] = useState(false);

  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  useEffect(() => {
    if (clause) {
      setFasalTajuk(clause.clauseName || "");
      setDataId(clause.id);
      if (clause.clauseContentId) {
        setClauseContent(clause.clauseContent);
        setClauseContentId(clause.clauseContentId);
      }
      if (clause.constitutionValues.length > 0) {
        setMatlamatPertubuhan(
          clause.constitutionValues[0]?.definitionName ?? ""
        );
        setFasalTajuk(name);
      }
      setIsEdit(clause.edit);
    }
  }, [clause]);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!fasalTajuk) {
      errors.fasalTajuk = t("fieldRequired");
    }
    if (!matlamatPertubuhan) {
      errors.matlamatPertubuhan = t("fieldRequired");
    }

    return errors;
  };

  const handleFasalNo = (e: any) => {
    const value = e.target.value;
    setFasalNo(value);
    setFormErrors((prevErrors) => ({
      ...prevErrors,
      fasalNo: "",
    }));
  };

  const handleTajukFasal = (e: any) => {
    const value = e.target.value;
    setFasalTajuk(value);
    setFormErrors((prevErrors) => ({
      ...prevErrors,
      fasalTajuk: "",
    }));
  };

  const isViewMode = getLocalStorage("isViewMode", false);
  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography sx={{ mb: 1, ...sectionStyle }}>Maklumat Fasal</Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("NomborFasal")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              value={fasalNo}
              onKeyDown={(e) => {
                if (["e", "E", "+", "-", "."].includes(e.key)) {
                  e.preventDefault();
                }
              }}
              onChange={(e) => {
                handleFasalNo(e);
              }}
              name="fasalNo"
              type="number"
              inputProps={{ inputMode: "numeric", pattern: "[0-9]*" }}
              fullWidth
              sx={{ background: "#fff" }}
              error={!!formErrors.fasalNo}
              helperText={formErrors.fasalNo}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("NamaFasal")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              value={fasalTajuk}
              onChange={handleTajukFasal}
              placeholder={t("tajukfasal")}
              fullWidth
              sx={{ background: "#fff" }}
              error={!!formErrors.fasalTajuk}
              helperText={formErrors.fasalTajuk}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          background: "#fff",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clause")} {fasalNo} : {name}
        </Typography>

        <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
          <Typography sx={labelStyle}>{t("organizationGoals")}</Typography>
        </Box>
        <TextField
          fullWidth
          disabled={isViewMode}
          value={matlamatPertubuhan}
          variant="outlined"
          multiline
          minRows={3}
          onChange={(e) => {
            setMatlamatPertubuhan(e.target.value);
            setFormErrors((prevErrors) => ({
              ...prevErrors,
              matlamatPertubuhan: "",
            }));
          }}
          error={!!formErrors.matlamatPertubuhan}
          helperText={formErrors.matlamatPertubuhan}
          sx={{
            "& fieldset": { borderRadius: "12px" },
            "& .MuiInputBase-input": { color: "black" },
          }}
        />
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <Box
                sx={{
                  whiteSpace: "pre-wrap",
                  wordWrap: "break-word",
                  py: 2,
                  height: "100%",
                }}
              >
                <Typography
                  sx={{ fontWeight: "400 !important" }}
                  dangerouslySetInnerHTML={{ __html: matlamatPertubuhan }}
                ></Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              const errors = validateForm();
              if (Object.keys(errors).length > 0) {
                setFormErrors(errors);
                return;
              }
              handleSaveContent({
                i18n,
                societyId: societyDataRedux.id,
                societyName: societyDataRedux.societyName,
                // clauseNo: clauseId,
                // clauseName: name,
                clauseNo: fasalNo,
                clauseName: fasalTajuk,
                constitutionTypeId: 7,
                dataId,
                isEdit,
                createClauseContent,
                editClauseContent,
                description: matlamatPertubuhan,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: matlamatPertubuhan,
                    titleName: "Matlamat Pertubuhan",
                  },
                ],
                clause: "clause3",
                clauseCount: 3,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentOtherCawangan;
