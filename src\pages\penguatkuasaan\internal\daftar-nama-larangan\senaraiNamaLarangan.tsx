import {
  Box,
  Button,
  FormControl,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
  Typography,
  useTheme,
  IconButton,
  Chip,
  Pagination,
} from "@mui/material";
import { t } from "i18next";
import { LaranganBox } from "./component/LaranganBorder";
import { LaranganPaper } from "./component/LaranganPaper";
import "./larangan.css";
import { useEffect, useState } from "react";
import { SearchIcon } from "@/components/icons";
import { FilterList, KeyboardArrowDown } from "@mui/icons-material";
import { values } from "lodash";
import { Edit as EditIcon, Delete as DeleteIcon } from "@mui/icons-material";
import { DataTable } from "@/components/datatable";
import DialogActionFlow from "@/components/dialog/confirm/DialogActionFlow";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { useNavigate } from "react-router-dom";
import { NEW_PermissionNames, pageAccessEnum } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";
import { laranganService } from "@/services/laranganService";
import { NamaLarangan } from "@/types/larangan/namaLarangan";
import { useNotification } from "@refinedev/core";
import dayjs from "@/helpers/dayjs";
import { borderStyle } from "@/pages/internal-training/trainingConstant";

// Sample data structure based on the image
const sampleData = [
  {
    id: 1,
    kataKunci: "Mati ke liang lahat tanpa mati",
    tarikhTerbit: "12-4-2025",
    masaTerbit: "3:40 petang",
    catatan: "diharamkan kerana mereka bukan tuhan di antartika",
    status: "aktif",
  },
  {
    id: 2,
    kataKunci: "Mati ke liang lahat tanpa mati",
    tarikhTerbit: "12-4-2025",
    masaTerbit: "3:40 petang",
    catatan: "diharamkan kerana mereka bukan tuhan di antartika",
    status: "tidak_aktif",
  },
  // ... more data
];

const handleDelete = (id: number) => {
  console.log("Delete item:", id);
  // Add your delete logic here
};
// Column definitions using your existing DataTable pattern

export const SenaraiNamaLarangan = () => {
  const { open: openNotification } = useNotification();
  const params = new URLSearchParams(window.location.search);
  const tab = params.get("tab");
  const navigate = useNavigate();
  const theme = useTheme();
  const primary = theme.palette.primary.main;
  const [namaLaranganTab, setNamaLaranganTab] = useState(0);
  const [selectedActiveOption, setSelectedActiveOption] = useState("");
  const [page, setPage] = useState(1);
  const [totalPage, setTotalPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [senaraiLaranganData, setSenaraiLaranganData] = useState<
    NamaLarangan[]
  >([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState<"edit" | "delete">("edit");
  const [customDialogMessage, setCustomDialogMessage] = useState<string | null>(
    null
  );
  const [isLoadingSenarai, setIsLoadingSenarai] = useState(false);
  const [laranganType, setLaranganType] = useState("SENARAI_LARANGAN");
  const [isEditMode, setIsEditMode] = useState(false);
  const [isViewMode, setIsViewMode] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [currentType, setCurrentType] = useState("SENARAI_LARANGAN");
  const [status, setStatus] = useState<boolean | null>(null);
  const [selectedItemId, setSelectedItemId] = useState<number>(0);
  const senaraiType = [
    {
      index: 0,
      title: t("Nama Larangan"),
      type: "SENARAI_LARANGAN",
      tab: "larangan",
    },
    {
      index: 1,
      title: t("Senarai Kelabu"),
      type: "SENARAI_KELABU",
      tab: "kelabu",
    },
  ];

  function checkPermissionAndUserGroup(accessType: number) {
    const hasPermission: boolean = AuthHelper.hasPageAccess(
      NEW_PermissionNames.PENGUATKUASAAN.children.SENARAI_NAMA_LOGO_LARANGAN.label,
      accessType
    );
    if (localStorage.getItem("portal") === "2") {
      return hasPermission && true;
    } else {
      return hasPermission && false;
    }
  }

  const readAccess = checkPermissionAndUserGroup(pageAccessEnum.Read);
  const createAccess = checkPermissionAndUserGroup(pageAccessEnum.Create);
  const deleteAccess = checkPermissionAndUserGroup(pageAccessEnum.Delete);
  const updateAccess = checkPermissionAndUserGroup(pageAccessEnum.Update);

  useEffect(() => {
    if (tab != null || tab != undefined) {
      const tabNumber = senaraiType.find((item) => item.tab === tab)?.index;
      if (tabNumber != null || tabNumber != undefined) {
        // setNamaLaranganTab(senaraiType.findIndex((item) => item.tab === tab));
        // setLaranganType(tab);
        handleTabChange(tabNumber);
      }
    }
  }, [tab]);

  useEffect(() => {
    switchData(laranganType);
  }, [page, rowsPerPage, laranganType, status, currentType]);

  function switchData(type: string) {
    if (type !== currentType) {
      setPage(1);
    }
    searchByKeywordAndType(searchText);
    setCurrentType(laranganType);
    return;

    // switch (type) {
    //   case senaraiType[0].type:
    //     fettchSenaraiLarangan();
    //     break;
    //   case senaraiType[1].type:
    //     fetchSenaraiKelabu();
    //     break;
    //   default:
    //     fettchSenaraiLarangan();
    //     break;
    // }
    // console.log("change type");
    // setCurrentType(laranganType);
  }

  const swicthStatus = (status: string) => {
    setPage(1);
    switch (status) {
      case "all":
        setStatus(null);
        break;
      case "active":
        setStatus(true);
        break;
      case "inactive":
        setStatus(false);
        break;
      default:
        setStatus(null);
        break;
    }
  };

  const searchByKeywordAndType = async (keyword: string) => {
    try {
      // setSenaraiLaranganData([]);

      setIsLoadingSenarai(true);
      const response = await laranganService.searchSenarai(
        keyword,
        laranganType,
        page,
        rowsPerPage,
        status
      );
      setSenaraiLaranganData(response?.data?.data);
      setTotalPage(response?.data?.total);
      // console.log(response.data, "senarai larangan:", laranganType);
    } catch (error) {
      openNotification?.({
        type: "error",
        message: "Error",
        description: t("Failed to fetch senarai larangan"),
      });
      console.error(error);
    } finally {
      setIsLoadingSenarai(false);
    }
  };

  const handleSearch = async (keyword: string) => {
    // if (keyword === "") {
    //   switchData(laranganType);
    //   return;
    // }
    searchByKeywordAndType(keyword);
  };

  const handleEdit = (id: number) => {
    console.log("Edit item:", id);
    // Add your edit logic here
  };
  const handleConfirm = async () => {
    setOpenDialog(false);
    await handleDelete(selectedItemId);
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await laranganService.deleteNamaLarangan(id);
      if (response.code === 200) {
        if (page > 1) {
          if (senaraiLaranganData.length === 1) {
            setPage(page - 1);
          }
        }
        openNotification?.({
          type: "success",
          message: "Success",
          description: t("Successfully deleted nama larangan"),
        });
        searchByKeywordAndType(searchText);
      } else {
        openNotification?.({
          type: "error",
          message: "Error",
          description: t("Failed to delete nama larangan"),
        });
      }
    } catch (error) {
      openNotification?.({
        type: "error",
        message: "Error",
        description: t("Failed to delete nama larangan"),
      });
    }
  };

  const handleTabChange = (index: number) => {
    setNamaLaranganTab(index);
    setLaranganType(senaraiType[index].type);
    setSearchText("");
    setSelectedActiveOption("all");
    setStatus(null);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setRowsPerPage(newPageSize);
  };

  const activeFilterOption = [
    {
      value: "all",
      status: "Semua",
    },
    {
      value: "active",
      status: "Aktif",
    },
    {
      value: "inactive",
      status: "Tidak Aktif",
    },
  ];

  // TABLE COLUMNS
  const columns = [
    // {
    //   field: "",
    //   headerName: "No.",
    //   flex: 0.5,
    //   align: "center" as const,
    //   renderCell: ({ rowIndex }: { rowIndex: number }) => {
    //     return <Typography>{rowIndex + 1}</Typography>;
    //   },
    // },
    {
      field: "kataKunci",
      headerName: "Kata Kunci",
      flex: 2,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        return (
          <Typography sx={{ fontSize: "14px", textAlign: "center" }}>
            {row.keyword}
          </Typography>
        );
      },
    },
    {
      field: "tarikhTerbit",
      headerName: "Tarikh Terbit",
      flex: 1,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        const dateOnlyMY = dayjs(row.createdDate)
          .tz("Asia/Kuala_Lumpur")
          .format("DD-MM-YYYY");
        return <Typography sx={{ fontSize: "14px" }}>{dateOnlyMY}</Typography>;
      },
    },
    {
      field: "masaTerbit",
      headerName: "Masa Terbit",
      flex: 1,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        const date = new Date(row.createdDate);
        let hour = date.getHours();
        const minute = date.getMinutes().toString().padStart(2, "0");

        // Determine period of day
        let period;
        if (hour >= 5 && hour < 12) {
          period = "pagi";
        } else if (hour >= 12 && hour < 13) {
          period = "tengah hari";
        } else if (hour >= 13 && hour < 18) {
          period = "petang";
        } else {
          period = "malam";
        }

        // Convert to 12-hour format for display
        hour = hour % 12 || 12; // changes 0 to 12 for midnight, keeps 1–12

        // Final string
        const timeMalay = `${hour
          .toString()
          .padStart(2, "0")}:${minute} ${period}`;
        return <Typography sx={{ fontSize: "14px" }}>{timeMalay}</Typography>;
      },
    },
    {
      field: "remarks",
      headerName: "Catatan",
      flex: 3,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        return (
          <Typography
            sx={{
              fontSize: "14px",
              textAlign: "center",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              maxWidth: "25ch", // 👈 limit around 25 characters
              display: "block",
              margin: "0 auto", // keep it centered
            }}
          >
            {row.remarks}
          </Typography>
        );
      },
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        const isActive = row.active;
        return (
          <Chip
            label={isActive ? "Aktif" : "Tidak Aktif"}
            sx={{
              backgroundColor: "inherit",
              border: `2px solid ${isActive ? "#00B69B" : "#F44336"}`,
              height: "28px",
              width: "92px",
              fontSize: "10px",
            }}
          />
        );
      },
    },
    {
      field: "tindakan",
      headerName: "Tindakan",
      flex: 1,
      align: "center" as const,
      renderCell: ({ row }: { row: any }) => {
        return (
          <Box sx={{ display: "flex", gap: 1, justifyContent: "center" }}>
            {updateAccess && (
              <IconButton
                size="small"
                onClick={() =>
                  navigate(
                    `/penguatkuasaan/daftar_nama_larangan/tambah_rekod?id=${row.id}&tab=nama&mode=edit`
                  )
                }
                sx={{
                  color: "var(--primary-color)",
                  "&:hover": { backgroundColor: "rgba(12, 166, 166, 0.1)" },
                }}
              >
                <EditIcon sx={{ fontSize: "18px" }} />
              </IconButton>
            )}
            {deleteAccess && (
              <IconButton
                size="small"
                onClick={() => {
                  setOpenDialog(true);
                  setDialogType("delete");
                  setSelectedItemId(row.id);
                }}
                sx={{
                  color: "#F44336",
                  "&:hover": { backgroundColor: "rgba(244, 67, 54, 0.1)" },
                }}
              >
                <DeleteIcon sx={{ fontSize: "18px" }} />
              </IconButton>
            )}
            <IconButton
              size="small"
              onClick={() =>
                navigate(
                  `/penguatkuasaan/daftar_nama_larangan/tambah_rekod?id=${row.id}&tab=nama&mode=view`
                )
              }
              sx={{
                "&:hover": { backgroundColor: "rgba(12, 166, 166, 0.1)" },
              }}
            >
              <VisibilityIcon sx={{ fontSize: "18px" }} />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  return (
    <>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: 2,
        }}
      >
        <LaranganPaper
          sx={{
            padding: "5px",
          }}
        >
          <>
            <Box
              sx={{
                width: "100%",
                display: "flex",
                // gap: 1,
                // p: 0.5,
                borderRadius: "10px",
              }}
            >
              <Button
                className="larangan-tab-btn"
                fullWidth
                variant={namaLaranganTab == 0 ? "contained" : "text"}
                onClick={() => handleTabChange(0)}
              >
                Nama Larangan
              </Button>
              <Button
                className="larangan-tab-btn"
                fullWidth
                variant={namaLaranganTab == 1 ? "contained" : "text"}
                onClick={() => handleTabChange(1)}
              >
                Senarai Kelabu
              </Button>
            </Box>
          </>
        </LaranganPaper>
        <LaranganPaper>
          <LaranganBox>
            <Typography
              color={primary}
              sx={{
                fontSize: 14,
                fontWeight: "medium",
                marginBottom: "1.5rem",
              }}
            >
              {senaraiType[namaLaranganTab].title}
            </Typography>
            <Box
              sx={{
                mr: "auto",
                ml: "auto",
                width: "70%",
                display: "flex",
                flexDirection: "column",
                // gap: 1,
              }}
            >
              <Box sx={{ mb: 2, width: "100%" }}>
                <TextField
                  placeholder={t("kataKunci")}
                  variant="outlined"
                  fullWidth
                  size="small"
                  value={searchText}
                  onChange={(e) => {
                    setSearchText(e.target.value);
                    handleSearch(e.target.value);
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon sx={{ color: "#9CA3AF" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "10px",
                      "& fieldset": {
                        borderColor: "#E5E7EB",
                      },
                      "&:hover fieldset": {
                        borderColor: "#E5E7EB",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#E5E7EB",
                      },
                    },
                  }}
                />
              </Box>
              <Box
                sx={{
                  // border: "1px solid #E5E7EB",
                  borderRadius: "30px",
                  // padding: "30px 20px 20px 20px",
                  // boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    gap: 2,
                    justifyContent: "center",
                    ml: "auto",
                    mr: "auto",
                    width: "70%",
                    borderRadius: "20px",
                    boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
                    p: "3px",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      color: "#6B7280",
                      borderRight: "1px solid #E5E7EB",
                      pr: 2,
                      height: "40px",
                    }}
                  >
                    <FilterList sx={{ fontSize: 20, mr: 1 }} />
                    <Typography variant="body2">Tapis Berdasarkan</Typography>
                  </Box>
                  <FormControl
                    size="small"
                    sx={{
                      minWidth: 120,
                      borderRight: "1px solid #E5E7EB",
                      height: "40px",
                      // paddingRight: 2,
                      // marginRight: 2
                    }}
                  >
                    <Select
                      displayEmpty
                      placeholder="Status Paparan"
                      value={selectedActiveOption || ""}
                      onChange={
                        (e) => {
                          setSelectedActiveOption(e.target.value);
                          swicthStatus(e.target.value);
                        }
                        // handleActiveOptionChange(e.target.value);
                      }
                      IconComponent={(props) => (
                        <KeyboardArrowDown {...props} sx={{ marginLeft: 1 }} />
                      )}
                      sx={{
                        border: "none",
                        "& .MuiOutlinedInput-notchedOutline": {
                          border: "none",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          border: "none",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          border: "none",
                        },
                        "& .MuiSelect-select": {
                          paddingRight: "32px", // Make room for the dropdown icon
                          fontWeight: 400, // Normal font weight instead of bold
                          fontSize: "14px",
                          color: "#6B7280",
                        },
                      }}
                      renderValue={(selected) => {
                        if (!selected) return "Status Paparan";
                        const option = activeFilterOption.find(
                          (opt) => opt.value == selected
                        );
                        return option ? option.status : "Status Paparan";
                      }}
                    >
                      {/* <MenuItem defaultValue={-1} value={-1}>
                        <em>Status Paparan</em>
                      </MenuItem> */}
                      {activeFilterOption.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.status}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
              </Box>
            </Box>
          </LaranganBox>
          <LaranganBox>
            <DataTable
              columns={columns}
              rows={senaraiLaranganData}
              page={page}
              rowsPerPage={rowsPerPage}
              totalCount={totalPage}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              pagination={true}
              paginationType="custom"
              numbered={true}
              isLoading={isLoadingSenarai}
              clientPaginationMode={false}
              sx={{
                width: "100%",
                "& .MuiTableCell-head": {
                  // backgroundColor: "#f5f5f5",
                  fontWeight: 600,
                  fontSize: "14px",
                  color: "#666666",
                },
                "& .MuiTableCell-body": {
                  fontSize: "14px",
                  color: "#666666",
                },
              }}
            />
            {/* <Pagination
              count={Math.ceil(sampleData.length / rowsPerPage)}
              page={page + 1}
              onChange={(e, value) => handlePageChange(value - 1)}
            /> */}
          </LaranganBox>
        </LaranganPaper>
      </Box>
      <DialogActionFlow
        open={openDialog}
        onClose={() => {
          setOpenDialog(false);
          setCustomDialogMessage(null);
        }}
        onConfirm={customDialogMessage ? async () => {} : handleConfirm}
        hideOnError={false}
        confirmationText={
          "Adakah anda pasti untuk memadamkan nama larangan ini?"
        }
        successMessage={
          customDialogMessage
            ? customDialogMessage
            : "Nama larangan berjaya dipadam"
        }
      />
    </>
  );
};
