import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  sx?: React.CSSProperties;
}

export const ShareOutlinedIcon: React.FC<IconProps> = React.forwardRef<
  SVGSVGElement,
  IconProps
>(({ sx, color = "inherit", ...props }, ref) => {
  return (
    <svg
      ref={ref}
      width="13"
      height="13"
      viewBox="0 0 13 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ color, ...sx }}
      {...props}
    >
      <g clip-path="url(#clip0_32668_28030)">
        <path
          d="M6.49777 3.65234V7.34245M6.49777 7.34245L7.93793 5.62065M6.49777 7.34245L5.05762 5.62065"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M1.45703 4.38959C1.45703 2.99768 1.45703 2.30246 1.879 1.86998C2.30096 1.4375 2.97928 1.4375 4.33735 1.4375H8.65782C10.0159 1.4375 10.6942 1.4375 11.1162 1.86998C11.5381 2.30246 11.5381 2.99768 11.5381 4.38959V8.81772C11.5381 10.2096 11.5381 10.9048 11.1162 11.3373C10.6942 11.7698 10.0159 11.7698 8.65782 11.7698H4.33735C2.97928 11.7698 2.30096 11.7698 1.879 11.3373C1.45703 10.9048 1.45703 10.2096 1.45703 8.81772V4.38959Z"
          stroke="currentColor"
        />
        <path
          d="M1.45703 9.56055H7.21766M11.5381 9.56055H10.098"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_32668_28030">
          <rect width="13" height="13" fill="currentColor" />
        </clipPath>
      </defs>
    </svg>
  );
});
