import Box from "@mui/material/Box";
import { useTranslation } from "react-i18next";
import { Switch as CustomSwitch } from "@/components/switch";

import { Grid, Typography, Stack } from "@mui/material";
import CreatePernyataanPendapatanMaklumatAm from "./CreatePernyataPendapatanMaklumatAm";
import CreatePernyataanPendapatanPersatuan from "./CreatePernyataPendapatanPersatuan";
import CreatePernyataanPendapatanPembelajaan from "./CreatePernyataPendapatanPembelajaan";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { FieldValues, SubmitHandler, useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { useCustomMutation } from "@refinedev/core";
import {
  convertFormattedToNumber,
  filterEmptyValuesOnObject,
  formatNumberToCurrencyString,
} from "@/helpers/utils";
import { useEffect, useState } from "react";
import useQuery from "@/helpers/hooks/useQuery";
import { API_URL } from "@/api";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { useSenaraiContext } from "@/pages/pertubuhan/SenaraiContext";
import CreateAset from "../aset-dan-liabiliti/CreateAset";
import CreateLiabiliti from "../aset-dan-liabiliti/CreateLiabiliti";
import FileUploader from "@/components/input/fileUpload";
import {
  ApplicationStatus,
  COMMITTEE_TASK_TYPE,
  DocumentUploadType,
} from "@/helpers";
import { useBranchContext } from "@/pages/pertubuhan/BranchProvider";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

export const CawanganCreatePernyataPendapatan = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const {
    handleNextPenyataTahunan: handleNext,
    handleBackPenyataTahunan: handleBack,
  } = useSenaraiContext();

  const {
    isBlackListed,
    isAuthorized,
    isAliranModuleAccess,
    branchId,
    fetchAliranTugasAccess,
  } = useBranchContext();

  useEffect(() => {
    fetchAliranTugasAccess(COMMITTEE_TASK_TYPE.PENYATAAN_TAHUNAN);
  }, []);

  const isAccessible = !isBlackListed && (isAuthorized || isAliranModuleAccess);

  //@ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);

  const isviewStatement = useSelector(
    //@ts-ignore
    (state) => state?.statementData?.isViewStatement
  );
  const isDisabled =
    (!isAuthorized && !isAliranModuleAccess && !isBlackListed) ||
    isviewStatement;
  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;
  const [checked, setChecked] = useState(false);
  const [statementComplete, setStatementComplete] = useState(false);
  const [documentError, setDocumentError] = useState<string>("");
  const [hasUploadedDocument, setHasUploadedDocument] =
    useState<boolean>(false);
  const handleCheckToggle = (value: boolean) => {
    pendapatanForm.setValue("financialDeclaration", value);
    setChecked(value);

    // Clear form data when toggle is switched on
    if (value) {
      pendapatanForm.setValue("totalIncome", 0);
      pendapatanForm.setValue("totalExpense", 0);
      pendapatanForm.setValue("totalAsset", 0);
      pendapatanForm.setValue("totalLiability", 0);
      pendapatanForm.setValue("documents", []);
    }
  };

  const pendapatanForm = useForm<FieldValues>({
    defaultValues: {
      totalIncome: 0,
      totalExpense: 0,
      totalAsset: 0,
      totalLiability: 0,
      financialDeclaration: false,
      documents: [],
    },
  });

  const [id, setId] = useState<number | undefined>();

  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const { mutate: saveStatementFinancial, isLoading: isLoading } =
    useCustomMutation();

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    const payload = { ...data };
    // convert formatted string values to number before submitting
    const fieldsToConvert = [
      "totalIncome",
      "totalExpense",
      "totalLiability",
      "totalAsset",
    ];

    fieldsToConvert.forEach((field) => {
      const value = data[field];
      if (typeof value === "string") {
        const numeric = convertFormattedToNumber(value);
        payload[field] = numeric;
      }
    });

    const cleanedPayload = filterEmptyValuesOnObject(payload);
    // Validate document upload when toggle is off
    if (!checked && !hasUploadedDocument) {
      setDocumentError(
        t("documentUploadRequired") || "Document upload is required"
      );
      return;
    }

    // Clear any previous error
    setDocumentError("");

    if (isDisabled || statementComplete) {
      handleNext()
      navigate(`../cawangan-penyata-tahunan-laporan-aktiviti`, {
        state: {
          societyId: societyId,
          statementId: statementId,
          year: year,
        },
      });
    } else {
      saveStatementFinancial(
        {
          url: id
            ? `${API_URL}/society/statement/statement-financial/${statementId}/edit`
            : `${API_URL}/society/statement/statement-financial/create?societyId=${societyId}&statementId=${statementId}&branchId=${branchDataRedux.id}`,
          method: id ? "put" : "post",
          values: cleanedPayload,
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            return {
              message: data?.data?.msg,
              type: "success",
            };
          },
        },
        {
          onSuccess: (data) => {
            const id = data?.data?.data?.id;
            setId(id);
          },
        }
      );
    }
  };

  useQuery({
    url: `society/statement/statement-financial/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      const pendapatan = data?.data?.data || [];

      const fieldsToConvert = [
        "totalIncome",
        "totalExpense",
        "totalLiability",
        "totalAsset",
      ];

      Object.entries(pendapatan).forEach(([key, value]) => {
        const shouldFormat = fieldsToConvert.includes(key);
        const formattedValue =
          shouldFormat && typeof value === "number"
            ? formatNumberToCurrencyString(value)
            : value;

        pendapatanForm.setValue(key, formattedValue);
      });
      setId(pendapatan.id);
    },
  });

  useQuery({
    url: `society/statement/societyInfo/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "year", operator: "eq", value: year },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      if (
        Number(data?.data?.data?.applicationStatusCode) ===
        ApplicationStatus.SELESAI
      ) {
        setStatementComplete(true);
      }
    },
  });

  const handleBackActions = () => {
    handleBack();
    navigate(-1);
  };

  const handleNextActions = () => {  
    handleNext();
    navigate(`../cawangan-penyata-tahunan-laporan-aktiviti`, {
      state: {
        societyId: societyId,
        statementId: statementId,
        financialStatementId: id,
        year: year,
      },
    });
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const downloadFile = () => {
    const url = "https://files.fm/SELAMAT4NGO/f/ess3ra46u";
    fetch(url)
      .then((res) => res.blob())
      .then((blob) => {
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = "Templat Penyata Kewangan.pdf";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      })
      .catch(console.error);
    window.open(url, "_blank", "noreferrer,noopener");
  };

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "bold" }}>
              {t("peringatan")} :
            </span>{" "}
            <span style={{ fontSize: 12 }}>{t("incomeStatementInfo")}</span>
            <br />
            <br />
            <span style={{ fontSize: 12 }}>{t("incomeStatementInfo2")}</span>
          </Typography>
        </Box>
      </Box>

      <CreatePernyataanPendapatanMaklumatAm year={year} t={t} />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("pengakuan")}
          </Typography>

          <Grid container spacing={10} alignItems="flex-start" sx={{ mb: 1 }}>
            <Grid item sm={10}>
              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: "400 !important",
                }}
              >
                <span style={{ fontSize: 12 }}>
                  {t("peyataTahunan_pendapatan_pengakuan")}
                </span>
              </Typography>
            </Grid>
            <Grid item sm={2}>
              <CustomSwitch
                checked={checked}
                onChange={(e) => handleCheckToggle(e.target.checked)}
                disabled={isDisabled || statementComplete}
              />
            </Grid>
          </Grid>
        </Box>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            py: 2,
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              borderRadius: "16px",
              fontSize: "14px",
              fontWeight: "500 !important",
            }}
          >
            {t("financialInformation")}
          </Typography>
        </Box>

        <form onSubmit={pendapatanForm.handleSubmit(onSubmit)}>
          <CreatePernyataanPendapatanPersatuan
            control={pendapatanForm.control}
            watch={pendapatanForm.watch}
            setValue={pendapatanForm.setValue}
            getValues={pendapatanForm.getValues}
            checked={checked || statementComplete}
            isAccessible={isAccessible}
            t={t}
          />
          <CreatePernyataanPendapatanPembelajaan
            control={pendapatanForm.control}
            watch={pendapatanForm.watch}
            setValue={pendapatanForm.setValue}
            getValues={pendapatanForm.getValues}
            checked={checked || statementComplete}
            isAccessible={isAccessible}
            t={t}
          />

          <CreateAset
            control={pendapatanForm.control}
            watch={pendapatanForm.watch}
            setValue={pendapatanForm.setValue}
            getValues={pendapatanForm.getValues}
            t={t}
            checked={checked || statementComplete}
            isAccessible={isAccessible}
          />

          <CreateLiabiliti
            control={pendapatanForm.control}
            watch={pendapatanForm.watch}
            setValue={pendapatanForm.setValue}
            getValues={pendapatanForm.getValues}
            t={t}
            checked={checked || statementComplete}
            isAccessible={isAccessible}
          />

          {checked ? null : (
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mb: 2,
                display: "grid",
                gap: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("uploadFinancialStatements")}
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Box sx={{ mt: 3 }}>
                    <Typography sx={labelStyle}>
                      {t("completedAuditedFinancialStatement")}
                      {!checked && (
                        <Typography
                          component="span"
                          sx={{ color: "red", ml: 0.5, display: "inline" }}
                        >
                          *
                        </Typography>
                      )}
                    </Typography>
                    <Typography
                      sx={{
                        ...labelStyle,
                        mt: 3,
                        color: "var(--link)",
                        textDecoration: "link",
                        cursor: "pointer",
                      }}
                      onClick={downloadFile}
                    >
                      {t("DownloadFinancialStatementTemplate")}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <FileUploader
                    // title="addSupportingDocument"
                    type={DocumentUploadType.SUPPORTING_DOCUMENT}
                    disabled={isDisabled || statementComplete}
                    required={!checked}
                    societyId={societyId}
                    branchId={branchDataRedux?.id}
                    statementId={statementId}
                    onUploadComplete={() => {
                      setHasUploadedDocument(true);
                      setDocumentError("");
                    }}
                    sxContainer={{
                      border: "2px dashed #ccc",
                      background: "#fff",
                      mb: 3,
                    }}
                    maxFileSize={25 * 1024 * 1024}
                    validTypes={[
                      // "text/plain",
                      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                      "application/msword",
                      "application/pdf",
                    ]}
                  />
                  {documentError && (
                    <Typography
                      sx={{
                        color: "red",
                        fontSize: "12px",
                        mt: 1,
                        fontWeight: "400 !important",
                      }}
                    >
                      {documentError}
                    </Typography>
                  )}
                </Grid>
              </Grid>
            </Box>
          )}
          <Stack
            direction="row"
            spacing={2}
            sx={{ mt: 4, pl: 1 }}
            justifyContent="flex-end"
          >
            {isDisabled || statementComplete ? (
              <Stack
                direction="row"
                spacing={2}
                sx={{ pl: 1, mt: 2 }}
                justifyContent="flex-end"
              >
                <ButtonOutline onClick={handleBackActions}>
                  {t("back")} 
                </ButtonOutline>
                <ButtonPrimary onClick={handleNextActions}>
                  {t("next")} 
                </ButtonPrimary>
              </Stack>
            ) : (
              <>
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "flex-end",
                  }}
                  gap={2}
                >
                  <Box sx={{ display: "flex" }} gap={1}>
                    <ButtonOutline onClick={handleBackActions}>
                      {t("back")} 
                    </ButtonOutline>
                    <ButtonPrimary type="submit">{t("save")}</ButtonPrimary>
                  </Box>

                  <Box>
                    <ButtonPrimary onClick={handleNextActions}>
                      {t("next")} 
                    </ButtonPrimary>
                  </Box>
                </Box>
              </>
            )}
          </Stack>
        </form>
        {/* 
        <Stack
          direction="row"
          spacing={2}
          sx={{ pl: 1, mt: 2 }}
          justifyContent="flex-end"
        >
          <ButtonPrimary onClick={handleNextActions}>{t("next")}</ButtonPrimary>
        </Stack> */}
      </Box>
    </>
  );
};

export default CawanganCreatePernyataPendapatan;
