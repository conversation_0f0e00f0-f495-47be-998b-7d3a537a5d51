import React, { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { useCustom } from "@refinedev/core";
import { API_URL } from "../../../api";

import { Box, Typography } from "@mui/material";
import WrapContent from "../View/WrapContent";
import PembubaranTab from "./PembubaranTab";
import KeputusanIndukKelulusan from "./KelulusanTab";
import PegawaiAwamTab from "./PegaiAwamTab";
import PegawaiHartaTab from "./PegawaiHartaTab";
import PembaharuanSetiausahaTab from "./PembaharuanSetiausahaTab";
import PermohonanBukanWargaNegaraTab from "./PermohonanBukanWarganegaraTab";
import PembaharuanSetiausahaMigrasiTab from "./PembaharuanSetiausahaMigrasiTab";
import PindaanUndangUndangIndukTab from "./PindaanUndangUndangInduk";
import PendaftaranIndukMenugguUlasanLuarTab from "./PendaftaranIndukMenugguUlasanLuarTab";
import { NEW_PermissionNames, pageAccessEnum } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";
import ForbiddenPage from "@/pages/forbidden";

type Props = {
  title?: string;
};

interface KeputusanBoxesProps {
  data: {
    name: string;
    number: number;
  };
  isActive: boolean;
  disabled: boolean;
  onClick: () => void;
}
const primary = "var(--primary-color)";

const KeputusanBoxes: React.FC<KeputusanBoxesProps> = React.memo(
  ({ data, isActive, disabled, onClick }) => {
    return (
      <Box
        onClick={!disabled ? onClick : undefined}
        sx={{
          ...(!disabled && {
            "&:hover": {
              backgroundColor: isActive ? primary : "#f5f5f5",
              transform: "translateY(-2px)",
            },
          }),
          padding: "0.75rem",
          paddingTop: "0.5rem !important",
          borderRadius: "0.5rem",
          border: `1px solid ${!disabled ? primary : "var(--border-grey)"}`,
          backgroundColor: isActive
            ? primary
            : !disabled
            ? "white"
            : "var(--border-grey)",
          position: "relative",
          display: "grid",
          gap: 2,
          flexDirection: "column",
          justifyContent: "space-between",
          alignItems: "flex-start",
          height: "100%",
          minHeight: "80px",
          paddingBottom: 0,
          cursor: !disabled ? "pointer" : "default",
          transition: "all 0.2s ease-in-out",
          ...(isActive && {
            boxShadow: "4px 6px 12px 0 rgba(102, 102, 102, 0.3)",
          }),
        }}
      >
        <Box
          sx={{
            color: disabled
              ? "var(--text-grey-disabled)"
              : isActive
              ? "#fff"
              : "var(--primary-color)",
            fontWeight: 400,
            fontSize: "14px",
          }}
        >
          {data.name}
        </Box>
        <Typography
          sx={{
            position: "absolute",
            bottom: 2,
            right: "10px",
            color: disabled
              ? "var(--text-grey-disabled)"
              : isActive
              ? "#fff"
              : "var(--primary-color)",
            fontWeight: 500,
          }}
        >
          {data.number}
        </Typography>
      </Box>
    );
  }
);

const KeputusanInduk = ({ title }: Props) => {
  const { data: pendingSociety, isLoading } = useCustom<any>({
    url: `${API_URL}/society/roDecision/getAllPendingCount/society`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const [searchParams, setSearchParams] = useSearchParams();
  const defaultTab = searchParams.get("tab") || "pendaftaran-pertubuhan-induk";
  const [activeTab, setActiveTab] = useState(defaultTab);

  const setActiveTabContent = (slug: string) => {
    setActiveTab(slug);
    setSearchParams({ tab: slug });
  };

  const tab = [
    {
      name: "Pendaftaran pertubuhan Induk",
      slug: "pendaftaran-pertubuhan-induk",
      number: pendingSociety?.data?.data?.societyRegistrationPendingCount || 0,
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
          .KEPUTUSAN_INDUK.children.PENDAFTARAN_PERTUBUHAN_INDUK.label,
    },
    {
      name: "Pendaftaran Induk-menunggu ulasan luar",
      slug: "pendaftaran-induk-menuggu-ulasan-luar",
      number:
        pendingSociety?.data?.data?.societyExternalAgencyReviewPendingCount ||
        0,
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
          .KEPUTUSAN_INDUK.children.PENDAFTARAN_INDUK_MENUNGGU_ULASAN_LUAR
          .label,
    },
    {
      name: "Pindaan Perlembagaan",
      slug: "pindaan-Perlembagaan",
      number: pendingSociety?.data?.data?.societyAmendmentPendingCount || 0,
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
          .KEPUTUSAN_INDUK.children.PINDAAN_PERLEMBAGAAN.label,
    },
    {
      name: "Pembubaran",
      slug: "pembubaran",
      number: pendingSociety?.data?.data?.societyLiquidationPendingCount || 0,
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
          .KEPUTUSAN_INDUK.children.PEMBUBARAN.label,
    },
    {
      name: "Permohonan bukan Warganegara",
      slug: "permohonan-bukan-warganegara",
      number: pendingSociety?.data?.data?.societyNonCitizenPendingCount || 0,
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
          .KEPUTUSAN_INDUK.children.PERMOHONAN_BUKAN_WARGANEGARA.label,
    },
    {
      name: "Pembaharuan Setiausaha",
      slug: "pembaharuan-setiausaha",
      number:
        pendingSociety?.data?.data?.societyPrincipalSecretaryPendingCount || 0,
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
          .KEPUTUSAN_INDUK.children.PEMBAHARUAN_SETIAUSAHA.label,
    },
    {
      name: "Pembaharuan Setiausaha-Migrasi",
      slug: "pembaharuan-setiausaha-migrasi",
      number: 67,
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
          .KEPUTUSAN_INDUK.children.PEMBAHARUAN_SETIAUSAHA_MIGRASI.label,
    },
    {
      name: "Pegawai Awam",
      slug: "pegawai-awam",
      number: pendingSociety?.data?.data?.societyPublicOfficerPendingCount || 0,
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
          .KEPUTUSAN_INDUK.children.PEGAWAI_AWAM.label,
    },
    {
      name: "Pegawai Harta",
      slug: "pegawai-harta",
      number:
        pendingSociety?.data?.data?.societyPropertyOfficerPendingCount || 0,
      permissionNames:
        NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
          .KEPUTUSAN_INDUK.children.PEGAWAI_HARTA.label,
    },
  ];

  const enrichedTabs = tab.map((item) => ({
    ...item,
    hasPermission: AuthHelper.hasPageAccess(
      item.permissionNames,
      pageAccessEnum.Read
    ),
  }));

  const renderTab = () => {
    const currentTab = enrichedTabs.find((t) => t.slug === activeTab);

    if (!currentTab) return null;
    if (!currentTab.hasPermission) return <ForbiddenPage internal />;

    switch (activeTab) {
      case "pendaftaran-pertubuhan-induk":
        return (
          <KeputusanIndukKelulusan
            number={
              pendingSociety?.data?.data?.societyRegistrationPendingCount || 0
            }
          />
        );
      case "pendaftaran-induk-menuggu-ulasan-luar":
        return <PendaftaranIndukMenugguUlasanLuarTab />;
      case "pindaan-Perlembagaan":
        return (
          <PindaanUndangUndangIndukTab
            number={
              pendingSociety?.data?.data?.societyAmendmentPendingCount || 0
            }
          />
        );
      case "pembubaran":
        return (
          <PembubaranTab
            number={
              pendingSociety?.data?.data?.societyLiquidationPendingCount || 0
            }
          />
        );
      case "permohonan-bukan-warganegara":
        return (
          <PermohonanBukanWargaNegaraTab
            number={
              pendingSociety?.data?.data?.societyNonCitizenPendingCount || 0
            }
          />
        );
      case "pembaharuan-setiausaha":
        return (
          <PembaharuanSetiausahaTab
            number={
              pendingSociety?.data?.data
                ?.societyPrincipalSecretaryPendingCount || 0
            }
          />
        );
      case "pembaharuan-setiausaha-migrasi":
        return <PembaharuanSetiausahaMigrasiTab />;
      case "pegawai-awam":
        return (
          <PegawaiAwamTab
            number={
              pendingSociety?.data?.data?.societyPublicOfficerPendingCount
            }
          />
        );
      case "pegawai-harta":
        return (
          <PegawaiHartaTab
            number={
              pendingSociety?.data?.data?.societyPropertyOfficerPendingCount
            }
          />
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Box>
        <WrapContent title="Keputusan Induk">
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(180px, 1fr))",
              gap: "16px",
            }}
          >
            {enrichedTabs.map((data, index) => (
              <KeputusanBoxes
                key={index}
                data={data}
                isActive={data.slug === activeTab}
                disabled={!data.hasPermission}
                onClick={() => {
                  if (data.hasPermission) {
                    setActiveTabContent(data.slug);
                  }
                }}
              />
            ))}
          </Box>
        </WrapContent>

        {renderTab()}
      </Box>
    </>
  );
};

export default KeputusanInduk;
