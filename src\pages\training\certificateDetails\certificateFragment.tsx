import React, {useState} from "react";
import {Box, LinearProgress, Typography} from "@mui/material";
import {TrainingEnums} from "@/helpers";
import {ClauseProps} from "@/pages/pertubuhan/pengurusan-perlembagaan/UpdatePindaanPerlembagaan";
import {ButtonOutline, ButtonPrimary, DialogConfirmation} from "@/components";
import {useTranslation} from "react-i18next";
import {useNavigate} from "react-router-dom";
import {DurationIcon} from "@/components/icons/duration";
import {PaticipantsIcon} from "@/components/icons/participants";
import {TrainingRequiredIcon} from "@/components/icons/trainingRequired";
import {ViewCertIcon} from "@/components/icons/viewCert";
import {useCustomMutation} from "@refinedev/core";
import {API_URL} from "@/api";

interface CertificateFragmentProps {
  item: any;
  width?: string;
}

const CertificateFragment: React.FC<CertificateFragmentProps> = ({item, width = "100%"}) => {
  const {t, i18n} = useTranslation();
  const navigate = useNavigate();
  const [openModal, setOpenModal] = useState(false);

  const hour = Math.floor(item.duration / 60);
  const minute = item.duration % 60;

  console.log("item", item);
  const checkAnswers = () => {
    navigate("/latihan/info", {state: {enroll: item, answerScheme: true}});
  }

  const {mutate: enroll, isLoading: isLoadingEnroll} = useCustomMutation();
  const Enroll = (): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    enroll(
      {
        url: `${API_URL}/society/training/courses/enroll/${item.id}/retake`,
        method: "post",
        values: {},
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            const enroll = data?.data?.data;
            let msg = "";
            if (enroll.completionStatus === "COMPLETED") {
              msg = t("TRAINING_SUCCESS");
            } else if (enroll.completionStatus === "IN_PROGRESS") {
              msg = data?.data?.msg.includes("Successfully") ? t("TRAINING_ENROLL_SUCCESSFUL") : t("TRAINING_ENROLLED");
              navigate("/latihan/info", {state: {enroll: enroll}});
            }
            return {
              message: msg || data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  return (
    <>
      <Box sx={{height: 450,}}>
        <Box
          sx={{
            p: 2,
            //pl: {xs: 2, md: 6},
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            //flex: 1,
            m: 1,
            maxHeight: 350,
            width: width,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              //pt: 3,
              m: 1,
              fontWeight: "400",
              fontSize: 12,
            }}
          >
            {item.type}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              //pt: 3,
              m: 1,
              fontWeight: "500",
              fontSize: 14,
            }}
          >
            {item.title}
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              width: "25%"
            }}
          >
            <Box
              sx={{
                //display:"block",
                border: item.completionStatus === "COMPLETED" ? "1px solid #2E7CF6" : "1px solid #FF0000",
                backgroundColor: item.completionStatus === "COMPLETED" ? `#2E7CF64D` : `#FF00004D`,
                borderRadius: 5,
                margin: "auto",
                //ml:1,
                flex: 1,
                //width: "60%",
                py: 1,
                mb: 2
                //px: 2
              }}
            >
              <Typography
                sx={{
                  color: "#fff",
                  lineHeight: "100%",
                  fontWeight: "500",
                  fontSize: 10,
                  textAlign: "center"
                }}
              >
                {item.completionStatus === "COMPLETED" ? t("LULUS") : t("GAGAL")}
              </Typography>
            </Box>
          </Box>
          <Box
            sx={{
              //height: "100%",
              height: "150px",
              zIndex: 0,
              backgroundImage: `url(${item.poster ?? '/latihanSample/images5.jpg'})`,
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
              backgroundPosition: "center center",
              justifyContent: "left",
              display: "flex",
              px: 1,
              py: 1,
              borderRadius: 2.5,
            }}>
            <Box
              sx={{
                height: "32px",
                borderRadius: 1.5,
                backgroundColor: "#fff",
                justifyContent: "space-evenly",
                display: "flex",
                p: 1
                //px: 2,
                //py: 2,
                //mb: 1,
              }}
            >
              <TrainingRequiredIcon/>
              <Typography
                sx={{
                  color: "#666666",
                  //pt: 3,
                  fontWeight: "400",
                  fontSize: 10,
                }}
              >
                {item.required ? t("compulsoryCourse") : ""}
              </Typography>
            </Box>
          </Box>
          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              width: "45%"
            }}
          >
          </Box>
          <Box sx={{
            mt: 2,
            display: "flex",
            flexDirection: "row",
          }}>
            <LinearProgress
              variant="determinate"
              value={(item.currentStep / item.totalStep) * 100}
              sx={{
                flex: 1,
                height: 15,
                borderRadius: 3,
                backgroundColor: "#E0E0E0",
                "& .MuiLinearProgress-bar": {
                  backgroundColor: "#00BCD4",
                  borderRadius: 3,
                },
              }}
            />
            <Typography
              sx={{
                ml: 5,
                color: "#666666",
                lineHeight: "18px",
                fontWeight: "400",
                fontSize: 16,
              }}
            >
              {`${((item.currentStep / item.totalStep) * 100).toFixed(0)} %`}
            </Typography>
          </Box>
        </Box>
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            m: 1,
            width: "100%",
            gap: 1
            //p:2,
          }}
        >
          <Box sx={{width: "25%",}}>
            <Box
              sx={{
                p: 1,
                //pl: {xs: 2, md: 6},
                border: "1px solid #D9D9D9",
                borderRadius: "10px",
                height: 60
                //flex: 1,
              }}
            >
              <Box
                sx={{
                  p: 1,
                  //pl: {xs: 2, md: 6},
                  backgroundColor: item.completionStatus === "COMPLETED" ? "#0CA6A6" : "#D9D9D9",
                  //border: "1px solid #D9D9D9",
                  cursor: "pointer",
                  width: "100%",
                  height: "100%",
                  borderRadius: "5px",
                  //flex: 1,
                }}
                onClick={() => {
                  if (item.completionStatus === "COMPLETED") {
                    navigate('sijil', {state:{enrollmentData: item}});
                  }
                }}
              >
                <ViewCertIcon
                  sx={{
                    width: "100%",
                    height: "100%",
                  }}
                />
              </Box>
            </Box>
          </Box>
          <Box sx={{width: "75%"}}>
            <Box
              sx={{
                p: 1,
                //pl: {xs: 2, md: 6},
                border: "1px solid #D9D9D9",
                borderRadius: "10px",
                height: 60,
                display: "flex",
                flexDirection: "row",
                justifyContent: "space-evenly",
                gap: 1,
                //flex: 1,
              }}
            >
              <ButtonPrimary
                variant="contained"
                sx={{
                  width: "auto",
                  maxWidth: "50% !important",
                  minWidth: "10px",
                  minHeight: 40,
                  fontWeight: 400,
                  fontSize: 8
                }}
                //disabled={isUploading || isLoadingEdit || isLoadingCreate}
                onClick={() => setOpenModal(true)}
                //disabled={true}
              >
                {t("retakeQuiz")}
              </ButtonPrimary>
              <ButtonOutline
                sx={{
                  bgcolor: "white",
                  "&:hover": {bgcolor: "white"},
                  width: "auto",
                  maxWidth: "50% !important",
                  minWidth: "10px",
                  minHeight: 40,
                  fontWeight: 400,
                  fontSize: 8
                }}
                onClick={checkAnswers}
              >
                {t("answerScheme")}
              </ButtonOutline>
            </Box>
          </Box>
        </Box>
      </Box>
      <DialogConfirmation
        open={openModal}
        onClose={() => {
          setOpenModal(false);
        }}
        onAction={Enroll}
        isMutating={false}
        onConfirmationText={t("CONFIRM_RETAKE_QUIZ")}
      />
    </>
  )
    ;
}

export default CertificateFragment;
