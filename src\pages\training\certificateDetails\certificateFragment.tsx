import React, { useRef } from "react";
import { Typography } from "@mui/material";
import TrainingBreadcrumb from "@/pages/training/breadcrumb";
import Box from "@mui/material/Box";
import ERosesLogoWithName from "@/components/eroses-log-with-name";
import { ButtonPrimary } from "@/components";
import { useTranslation } from "react-i18next";
import { useCustom } from "@refinedev/core";
import { API_URL } from "@/api";
import { formatDate } from "@/helpers";
import { QRCodeSVG } from "qrcode.react";
import certificate from "@/pages/training/certificateDetails/certificate";

interface CertificateFragmentProps {
  enrollmentId: string | number;
  downloadable?: boolean;
  width?: string;
}

const CertificateFragment: React.FC<CertificateFragmentProps> = ({
  enrollmentId,
  downloadable = false,
  width = "80%",
}) => {
  const { t, i18n } = useTranslation();

  const svgRef = useRef<SVGSVGElement | null>(null);

  const { data: trainingEnrollmentData, isLoading: isEnrolledTrainingLoading } =
    useCustom({
      url: `${API_URL}/society/training/enrollments/${enrollmentId}`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
      queryOptions: {
        enabled: true,
        retry: false,
        cacheTime: 0,
      },
    });

  const trainingEnrollment = trainingEnrollmentData?.data?.data || {};
  console.log("trainingEnrollment", trainingEnrollment);

  const {
    data: trainingCertificateData,
    isLoading: isCertificateTrainingLoading,
  } = useCustom({
    url: `${API_URL}/society/training/certificates/${trainingEnrollment.certificateId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled:
        trainingEnrollment?.certificateId != null &&
        trainingEnrollment?.certificateId != "",
      retry: false,
      cacheTime: 0,
    },
  });

  const trainingCertificate = trainingCertificateData?.data?.data || {};
  console.log("trainingCertificate", trainingCertificate);

  const handleDownload = () => {
    const filePath = `${API_URL}/${trainingCertificate.certificatePath}`;
    window.open(filePath, "_blank");
  };

  return (
    <>
      <Box
        sx={{
          width: width,
          //flex: 5,
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //flex: 5,
            px: 5,
            py: 2,
            mb: 1,
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <ERosesLogoWithName textColor="#666666" />
            <Box>
              <Typography
                sx={{
                  color: "#666666",
                  fontWeight: "400",
                  fontSize: 12,
                  textAlign: "right",
                }}
              >
                No.Sijil: ER-be9c3edb-906b-afae-f27a24747475754fo
              </Typography>
              <Typography
                sx={{
                  color: "#666666",
                  fontWeight: "400",
                  fontSize: 12,
                  textAlign: "right",
                }}
              >
                URL SIjil: {`${API_URL}/${trainingCertificate.certificatePath}`}
              </Typography>
              <Typography
                sx={{
                  color: "#666666",
                  fontWeight: "400",
                  fontSize: 12,
                  textAlign: "right",
                }}
              >
                Nombor Rujukan: 00230
              </Typography>
            </Box>
          </Box>
          <Box>
            <Typography
              sx={{
                color: "#666666",
                pt: 3,
                fontWeight: "400",
                fontSize: 14,
              }}
            >
              Sijil Pencapaian
            </Typography>
          </Box>
          <Box>
            <Typography
              sx={{
                color: "#000000",
                pt: 3,
                fontWeight: "600",
                fontSize: 40,
              }}
            >
              {trainingEnrollment.title ?? ""}
            </Typography>
          </Box>
          <Box>
            <Typography
              sx={{
                color: "#666666",
                pt: 3,
                fontWeight: "400",
                fontSize: 14,
              }}
            >
              Tenaga Pengajar: Syakir Ahmad
            </Typography>
          </Box>
          <Box
            sx={{
              mt: 5,
              pt: 5,
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <Box>
              <Typography
                sx={{
                  color: "var(--primary-color)",
                  pt: 3,
                  fontWeight: "600",
                  fontSize: 30,
                }}
              >
                {trainingCertificate.user?.name ?? ""}
              </Typography>
              <Typography
                sx={{
                  color: "#666666",
                  pt: 3,
                  fontWeight: "400",
                  fontSize: 14,
                }}
              >
                Tarikh: {formatDate(trainingCertificate.issueDate)}
              </Typography>
              <Typography
                sx={{
                  color: "#666666",
                  pt: 3,
                  fontWeight: "400",
                  fontSize: 14,
                }}
              >
                Masa: 10.5 Keseluruhan Masa
              </Typography>
            </Box>
            <Box>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "flex-end",
                }}
              >
                <QRCodeSVG
                  value={trainingCertificate.verificationCode}
                  size={100}
                  level="H"
                  ref={svgRef}
                />
              </Box>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "flex-end",
                }}
              >
                <Typography
                  sx={{
                    width: "50%",
                    color: "#666666",
                    pt: 3,
                    fontWeight: "400",
                    fontSize: 12,
                    textAlign: "right",
                  }}
                >
                  Kod QR yang tertera pada e-sijil adalah imbasan buat
                  pengesahan.
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
        {downloadable ? (
          <Box sx={{ display: "flex", mt: 1, justifyContent: "flex-end" }}>
            <ButtonPrimary
              variant="outlined"
              sx={{
                borderColor: "#666666",
                bgcolor: "#fff",
                "&:hover": { bgcolor: "#fff", borderColor: "#666666" },
                color: "#666666",
                fontWeight: "400",
              }}
              onClick={handleDownload}
            >
              {t("download")}
            </ButtonPrimary>
          </Box>
        ) : (
          <></>
        )}
      </Box>
    </>
  );
};

export default CertificateFragment;
