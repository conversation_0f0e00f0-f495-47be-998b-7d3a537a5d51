import { Route, Outlet } from "react-router-dom";
import { CategoryList, CategoryCreate, CategoryEdit, CategoryShow } from "../pages/categories";
import { registerRoutes } from "../helpers/routeDetector";
import { RouteGuard } from "../components/RouteGuard";

// Layout component to wrap all category routes with protection
const CategoryLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Outlet />
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example: '/categories': 'internal',
  // Example: '/categories/create': 'internal',
  // Add your route registrations here
});

export const categories = {
  resources: [
    {
      name: "categories",
      list: "/categories",
      create: "/categories/create",
      edit: "/categories/edit/:id",
      show: "/categories/show/:id",
      meta: {
        canDelete: true,
      },
    },
  ],
  routes: (
    <Route path="/categories" element={<CategoryLayout />}>
      <Route index element={<CategoryList />} />
      <Route path="create" element={<CategoryCreate />} />
      <Route path="edit/:id" element={<CategoryEdit />} />
      <Route path="show/:id" element={<CategoryShow />} />
    </Route>
  ),
};
