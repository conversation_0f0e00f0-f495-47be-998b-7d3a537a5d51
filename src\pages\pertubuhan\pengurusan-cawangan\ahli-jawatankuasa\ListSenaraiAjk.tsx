import React, { useEffect, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Typography, IconButton } from "@mui/material";
import { ButtonPrimary } from "../../../../components/button";
import {
  ApplicationStatusEnum,
  MALAYSIA,
  OrganisationPositions,
  otherPositionSwitchList,
} from "../../../../helpers/enums";
import { EditIcon, EyeIcon, TrashIcon } from "../../../../components/icons";
import { API_URL } from "../../../../api";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { getLocalStorage } from "../../../../helpers/utils";
import { useQuery } from "@/helpers";
import { DataTable, DialogConfirmation } from "@/components";
import { FieldValues, useForm } from "react-hook-form";

export const ListSenaraiAjk: React.FC = () => {
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "16px",
    fontWeight: "500 !important",
    fontFamily: "Poppins",
  };

  const navigate = useNavigate();
  const { t } = useTranslation();
  const addressList = getLocalStorage("address_list", null);
  const [totalListNonCitizen, setTotalListNonCitizen] = useState<number>(0);
  const [rowDataNonCitizen, setRowDataNonCitizen] = useState<any[]>([]);
  const [searchParams] = useSearchParams();
  const [internalList, setInternalList] = useState([]);
  const [totalListCitizen, setTotalListCitizen] = useState<number>(0);
  const [societyAjkType, setSocietyAjkType] = useState("");
  const { id: societyId } = useParams();
  const branchId = searchParams.get("id");
  const [openModalDelete, setOpenModalDelete] = useState(false);
  const [idToDelete, setIdToDelete] = useState<string | number | null>();

  const { watch, setValue } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      pageNonCitizen: 1,
      pageSizeNonCitizen: 10,
    },
  });

  const pageNonCitizen = watch("pageNonCitizen") || 1;
  const pageSizeNonCitizen = watch("pageSizeNonCitizen") || 10;
  const page = watch("page") || 1;
  const pageSize = watch("pageSize") || 10;

  const [stateList, setStateList] = useState(
    addressList
      ?.filter((item: any) => item.pid === MALAYSIA)
      ?.map((item: any) => ({ value: item.id, label: item.name }))
  );

  const handleDaftarAJKBukanWn = () => {
    navigate(`../create-ajk-bukan-wn?id=${branchId}`);
  };

  const handleEditAJK = (memberId: any) => {
    navigate(`../create-ajk?id=${branchId}&mId=${memberId}`);
  };

  const handleEditAJKBukanWn = (memberId: any) => {
    navigate(`../create-ajk-bukan-wn?id=${branchId}&mId=${memberId}`);
  };

  const handleViewAJKBukanWn = (memberId: any) => {
    navigate(`../view-ajk-bukan-wn?id=${branchId}&mId=${memberId}`);
  };

  const {
    data: nonCitizenList,
    refetch: fetchNonCitizenAjkList,
    isLoading: isLoadingNonCitizenAJKList,
  } = useQuery({
    url: `society/nonCitizenCommittee/getAll`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchId },
      // {
      //   field: "pageSize",
      //   operator: "eq",
      //   value: watch("pageSizeNonCitizen"),
      // },
      // { field: "pageNo", operator: "eq", value: watch("pageNonCitizen") },
    ],
    // autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      console.log();
      setTotalListNonCitizen(data?.data?.data?.total ?? 0);
      setRowDataNonCitizen(data?.data?.data?.data ?? []);
    },
  });

  const {
    data: branchData,
    isLoading,
    refetch,
  } = useQuery({
    url: "society/branch/committee/getAllCommittee",
    filters: [
      {
        field: "branchId",
        value: branchId,
        operator: "eq",
      },
      { field: "pageSize", operator: "eq", value: watch("pageSize") },
      { field: "pageNo", operator: "eq", value: watch("page") },
    ],
    enabled: branchId !== null,
    onSuccess: (data) => {
      console.log("non", data);
      setInternalList(data.data.data?.data || []);
      setTotalListCitizen(data.data?.data?.total || 0);
    },
  });

  const { mutate: deleteExternalCommittee, isLoading: isLoadingCreate } =
    useCustomMutation();
  const DeleteExternalCommittee: (id: any) => void = (id) => {
    deleteExternalCommittee(
      {
        url: `${API_URL}/society/nonCitizenCommittee/${id}`,
        method: "delete",
        values: {
          id: branchId,
          societyId: branchData?.data?.data?.societyId,
          societyNo: branchData?.data?.data?.societyNo,
          branchNonCitizenCommittees: [{ id: id, applicationStatusCode: -1 }],
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          refetch();
          fetchNonCitizenAjkList({
            filters: [
              { field: "societyId", operator: "eq", value: societyId },
              { field: "branchId", operator: "eq", value: branchId },
            ],
          });
          setOpenModalDelete(false);
          setIdToDelete(null);
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { mutate: editNonCitizenIds, isLoading: isLoadingEdit } =
    useCustomMutation();

  const EditNonCitizenIds: (ids: any) => void = (ids) => {
    editNonCitizenIds(
      {
        url: `${API_URL}/society/nonCitizenCommittee/update-by-list`,
        method: "put",
        values: { ids },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          fetchNonCitizenAjkList({
            filters: [
              { field: "societyId", operator: "eq", value: societyId },
              { field: "branchId", operator: "eq", value: branchId },
            ],
          });
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const hantarNonCitizen = () => {
    const ids = rowDataNonCitizen
      ?.filter((item) => Number(item.applicationStatusCode) === 1)
      .map((item) => item.id);
    EditNonCitizenIds(ids);
  };

  const internalColumns = [
    {
      field: "designationCode",
      headerName: t("position"),
      headerAlign: "left",
      align: "left",
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box>
            {!otherPositionSwitchList.some(
              (item) => item.value === Number(row.designationCode)
            )
              ? t(
                  OrganisationPositions.find(
                    (item) => item.value === Number(row.designationCode)
                  )?.label || "-"
                )
              : t(
                  row?.otherPosition ??
                    t(
                      OrganisationPositions.find(
                        (item) => item.value === Number(row.designationCode)
                      )?.label || "-"
                    )
                )}
          </Box>
        );
      },
    },
    {
      field: "committeeName",
      headerName: t("name"),
      headerAlign: "center",
      align: "center",
    },
    {
      field: "email",
      headerName: t("email"),
      headerAlign: "center",
      align: "center",
    },
    {
      field: "committeeStateCode",
      headerName: t("state"),
      headerAlign: "center",
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        const state = stateList.find(
          (item: any) => item.value === Number(row?.committeeStateCode)
        );
        return (
          <Typography className="label">
            {state?.label ? state?.label : "-"}
          </Typography>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      align: "right",
      headerAlign: "right",
      sortable: false,
      flex: 0.5,
      renderCell: (params: any) => (
        <IconButton
          onClick={() => handleEditAJK(params.row.id)}
          sx={{ color: "var(--primary-color)" }}
        >
          <EditIcon sx={{ fontSize: "1rem", width: "1rem", height: "1rem" }} />
        </IconButton>
      ),
    },
  ];

  const externalColumns = [
    {
      field: "designationCode",
      headerName: t("position"),
      headerAlign: "left",
      align: "left",

      renderCell: ({ row }: any) => {
        return (
          <Box>
            {!otherPositionSwitchList.some(
              (item) => item.value === Number(row.designationCode)
            )
              ? t(
                  OrganisationPositions.find(
                    (item) => item.value === Number(row.designationCode)
                  )?.label || "-"
                )
              : t(
                  row?.otherPosition ??
                    t(
                      OrganisationPositions.find(
                        (item) => item.value === Number(row.designationCode)
                      )?.label || "-"
                    )
                )}
          </Box>
        );
      },
    },
    {
      field: "name",
      headerName: t("name"),
      headerAlign: "center",
      align: "center",
    },
    {
      field: "applicationStatusCode",
      headerName: t("applicationStatus"),
      headerAlign: "center",
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        const statusLabel = t(
          ApplicationStatusEnum[row.applicationStatusCode] ?? "-"
        );
        return (
          <Typography className="label">
            {statusLabel ? statusLabel : "-"}
          </Typography>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      align: "right",
      headerAlign: "right",
      renderCell: (params: any) => {
        const row = params?.row;
        if (Number(row.applicationStatusCode) === 1) {
          return (
            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              <IconButton
                onClick={() => handleEditAJKBukanWn(params.row.id)}
                sx={{ color: "var(--primary-color)" }}
              >
                <EditIcon
                  sx={{ fontSize: "1rem", width: "1rem", height: "1rem" }}
                />
              </IconButton>
              <IconButton
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  aspectRatio: "1/1",
                }}
                onClick={() => {
                  setIdToDelete(params.row.id);
                  setOpenModalDelete(true);
                }}
              >
                <TrashIcon
                  sx={{
                    fontSize: "0.9rem",
                    width: "0.9rem",
                    height: "0.9rem",
                    color: "red",
                  }}
                />
              </IconButton>
            </Box>
          );
        } else {
          return (
            <IconButton
              onClick={() => handleViewAJKBukanWn(params.row.id)}
              sx={{ color: "var(--primary-color)" }}
            >
              <EyeIcon
                sx={{ fontSize: "1rem", width: "1rem", height: "1rem" }}
              />
            </IconButton>
          );
        }
      },
    },
  ];

  const { isLoading: isLoadingSocietyAjkType } = useCustom({
    url: `${API_URL}/society/constitutioncontent/getAjkType`,
    method: "get",
    config: {
      filters: [{ field: "societyId", operator: "eq", value: societyId }],
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (data) => {
        const response = data?.data?.data?.societyAjkType;
        setSocietyAjkType(response);
      },
    },
  });

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 3,
          }}
        >
          <Box sx={{ display: "flex", flexDirection: "column", mb: 2, gap: 2 }}>
            <Typography variant="h6" component="h2" sx={sectionStyle}>
              {t("ajkList")}
            </Typography>
            {/* <ButtonPrimary
              sx={{
                alignSelf: "flex-end",
                px: 2,
                py: 1,
                bgcolor: "transparent",
                color: "#666666",
                boxShadow: "none",
                border: "1px solid #67D1D1",
                fontSize: "12px",
                fontWeight: "500 !important",
              }}
              onClick={handleDaftarAJK}
            >
              {t("registerAJK")}
            </ButtonPrimary> */}
          </Box>
          <DataTable
            columns={internalColumns as any}
            rows={internalList}
            page={page}
            rowsPerPage={pageSize}
            totalCount={totalListCitizen}
            onPageChange={(newPage) => setValue("page", newPage)}
            onPageSizeChange={(newPageSize) => {
              setValue("page", 1);
              setValue("pageSize", newPageSize);
            }}
            isLoading={isLoading}
          />
        </Box>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "white",
            borderRadius: "14px",
          }}
        >
          <Box sx={{ display: "flex", flexDirection: "column", mb: 2, gap: 2 }}>
            <Typography variant="h6" component="h2" sx={sectionStyle}>
              {t("nonCitizenAJK")}
            </Typography>
            <ButtonPrimary
              sx={{
                alignSelf: "flex-end",
                px: 2,
                py: 1,
                bgcolor: "transparent",
                color: "#666666",
                boxShadow: "none",
                border: "1px solid #67D1D1",
                fontSize: "12px",
                fontWeight: "500 !important",
              }}
              disabled={
                isLoadingSocietyAjkType || societyAjkType === "Warganegara"
              }
              onClick={handleDaftarAJKBukanWn}
            >
              {t("registerNonCitizenAJK")}
            </ButtonPrimary>
          </Box>
          <DataTable
            columns={externalColumns as any}
            rows={rowDataNonCitizen}
            page={pageNonCitizen}
            rowsPerPage={pageSizeNonCitizen}
            totalCount={totalListNonCitizen}
            onPageChange={(newPage) => setValue("pageNonCitizen", newPage)}
            onPageSizeChange={(newPageSize) => {
              setValue("pageNonCitizen", 1);
              setValue("pageSizeNonCitizen", newPageSize);
            }}
            isLoading={isLoadingNonCitizenAJKList}
            clientPaginationMode={true}
          />
          <Box
            sx={{ display: "flex", justifyContent: "flex-end", gap: 2, mt: 3 }}
          >
            <ButtonPrimary
              disabled={
                !rowDataNonCitizen ||
                rowDataNonCitizen.length === 0 ||
                isLoadingEdit ||
                rowDataNonCitizen?.every(
                  (item) => Number(item.applicationStatusCode) !== 1
                )
              }
              onClick={hantarNonCitizen}
            >
              {t("hantar")}
            </ButtonPrimary>
          </Box>
        </Box>
        <ButtonPrimary
          disabled={totalListCitizen + totalListCitizen < 7}
          onClick={() => navigate(`../dokumen-sokongan?id=${branchId}`)}
          sx={{ display: "block", ml: "auto", mt: 4 }}
        >
          {t("next")}
        </ButtonPrimary>
      </Box>
      <DialogConfirmation
        open={openModalDelete}
        onClose={() => {
          setOpenModalDelete(false);
        }}
        onAction={() => DeleteExternalCommittee(idToDelete)}
        isMutating={false}
        onConfirmationText={t("deleteNonCitizenCommittee")}
      />
    </>
  );
};

export default ListSenaraiAjk;
