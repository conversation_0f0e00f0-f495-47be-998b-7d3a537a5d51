import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
interface FasalContentEnamBelasProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
}

export const FasalContentEnamBelas: React.FC<FasalContentEnamBelasProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");

  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [clauseContent, setClauseContent] = useState("");
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  useEffect(() => {
    if (encodedId) {
      const decodedId = atob(encodedId);
      setSocietyId(decodedId);

      fetch(`${API_URL}/society/${decodedId}`, {
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      })
        .then((res) => res.json())
        .then((data) => {
          setNamaPertubuhan(data?.data?.societyName);
        })
        .catch((error) => {
          console.error("Error fetching society details:", error);
        });
    }
  }, []);

  // const clauseContent = `
  //             1. Sebarang bentuk perjudian seperti yang ditafsirkan dalam Akta
  //             Rumah Judi Terbuka 1953 adalah dilarang di premis pertubuhan.

  //             2. Pertubuhan atau ahli-ahlinya tidak harus cuba menghalang atau
  //             dengan apa cara juga mengganggu perniagaan atau harga
  //             barang-barang atau mengambil peranan dalam gerakan kesatuan
  //             sekerja seperti definisi dalam Akta Kesatuan Sekerja 1959.

  //             3. Pertubuhan ini tidak boleh menjalankan loteri sama ada
  //             dikhaskan kepada ahli-ahli atau tidak, atas nama Pertubuhan atau
  //             pegawai-pegawainya atau Jawatankuasa atau ahli tanpa kelulusan
  //             daripada pihak berkuasa yang berkenaan.

  //             4. "Faedah" seperti yang ditafsirkan di bawah Seksyen 2 Akta
  //             Pertubuhan 1966 tidak boleh diberikan oleh Pertubuhan kepada
  //             mana-mana ahlinya.

  //             5. Mengutip derma tanpa kebenaran pihak berkuasa yang berkenaan.
  //           `;

  const clause16 = localStorage.getItem("clause16");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause16Data = JSON.parse(clause16);
      setDataId(clause.id);
      setClauseContent(clause.clauseContent);
      setClauseContentId(clause.clauseContentId);
      //setNamaPertubuhan(clause16Data.societyName);
      setIsEdit(clause.edit);
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  const { id, clauseId } = useParams();

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                constitutionValues: [],
                clause: "clause16",
                clauseCount: 16,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentEnamBelas;
