import React, { useEffect, useRef, useState } from "react";
import {
  Box,
  TextField,
  Typography,
  Grid,
  Select,
  MenuItem,
  FormControl,
  useMediaQuery,
  Theme,
  Fade,
  FormHelperText,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { OrganizationStepper } from "../organization-stepper";
import {
  useCustom,
  useCreate,
  useCustomMutation,
  useNotification,
} from "@refinedev/core";
import { API_URL } from "@/api";
import dayjs from "@/helpers/dayjs";
import {
  ApplicationStatus,
  MALAYSIA,
  MeetingMethods,
  MeetingTypeOption,
} from "@/helpers";
import {
  capitalizeWords,
  DocumentUploadType,
  useQuery,
  useUploadPresignedUrl,
  DOCUMENT_MAX_FILE_SIZE,
} from "@/helpers";
import { LoadingOverlay } from "../../../../components/loading";
import InfoQACard from "../InfoQACard";
import { useMap } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import L from "leaflet";
import icon from "leaflet/dist/images/marker-icon.png";
import iconShadow from "leaflet/dist/images/marker-shadow.png";
import { useSelector } from "react-redux";
import {
  FormMeetingAttendees,
  FormMeetingAttendeesBaseRef,
} from "@/components/form/meeting/Attendees";
import { OrganizationManagementMeetingRequestBodyCreateOnlyAttendees } from "@/types";
import {
  FormMeetingDateTime,
  FormMeetingDateTimeRef,
} from "@/components/form/meeting/DateTime";
import AWSLocationMap from "@/components/geocoder/geocoder";
import {
  LocationClient,
  SearchPlaceIndexForTextCommand,
} from "@aws-sdk/client-location";
import { withAPIKey } from "@aws/amazon-location-utilities-auth-helper";
import { useCheckAndUpdateRegistration } from "@/helpers/hooks/useCheckAndupdateRegister";

interface FormData {
  meetingType: string | number;
  meetingPurpose: string;
  meetingPlace: string;
  meetingMethod: string;
  platformType: string;
  meetingDate: string;
  meetingTime: string;
  GISInformation: string;
  meetingAddress: string;
  state: string;
  district: string;
  city: string;
  postcode: string;
  totalAttendees: number;
  meetingContent: string;
  openingRemarks: string;
  mattersDiscussed: string;
  othersDiscussionRemarks: string;
  closingRemarks: string;
  providedBy: string;
  confirmBy: string;
  meetingMinute: string;
  icNo: number;
  present: number;
  createdBy: string;
  modifiedBy: string;
  memberAttendances: Member[];
}

interface Member {
  id: number;
  name: string;
  role: string;
  status: string;
}

// Add marker icon fix
const DefaultIcon = L.icon({
  iconUrl: icon,
  shadowUrl: iconShadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
});
L.Marker.prototype.options.icon = DefaultIcon;

type UploadParams = {
  type: DocumentUploadType;
  societyId: number | null;
  societyNo: number | null;
  branchId: number | null;
  branchNo: number | null;
  meetingId: number | null;
  societyCommitteeId: number | null;
  icNo: string;
  name: string;
};

export const CreateMesyuarat: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [activeStep, setActiveStep] = useState(1);
  const [generateMinutesButtonDisabled, setGenerateMinutesButtonDisabled] =
    useState(false);
  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const formMeetingAttendeesRef =
    useRef<
      FormMeetingAttendeesBaseRef<OrganizationManagementMeetingRequestBodyCreateOnlyAttendees>
    >();
  const formMeetingDateTimeRef = useRef<FormMeetingDateTimeRef>();

  const uploadParams: UploadParams = {
    type: DocumentUploadType.MEETING,
    societyId: null,
    societyNo: null,
    branchId: null,
    branchNo: null,
    meetingId: null,
    societyCommitteeId: null,
    icNo: "",
    name: "",
  };

  const [meetingType, setMeetingType] = useState(MeetingTypeOption[0].value);
  const [meetingMethod, setMeetingMethod] = useState("");
  const [platformType, setPlatformType] = useState("");
  const [meetingContent, setMeetingContent] = useState("");
  const [isCreated, setIsCreated] = useState(false);
  const [masaMula, setMasaMula] = useState("");
  const [masaTamat, setMasaTamat] = useState("");
  const [state, setState] = useState("");
  const [district, setDistrict] = useState("");
  const [city, setCity] = useState("");
  const [postcode, setPostcode] = useState("");
  const [members, setMembers] = useState<Member[]>([]);
  const [client, setClient] = useState<LocationClient | null>(null);
  const [locations, setLocations] = useState<string[]>([]);
  const PLACE_INDEX_NAME = import.meta.env.VITE_AWS_PLACE_INDEX;
  const AWS_REGION = import.meta.env.VITE_AWS_API_KEY_REGION;
  const API_KEY = import.meta.env.VITE_AWS_API_KEY;
  const { open } = useNotification();

  const [newMember, setNewMember] = useState({ name: "", role: "" });
  const [meetingId, setMeetingId] = useState("");
  const getUserDetails = localStorage.getItem("user-details");
  const userName = getUserDetails ? JSON.parse(getUserDetails).name : "";

  const [isLoadingData, setIsLoadingData] = useState(false);

  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const [fileNotUploaded, setFileNotUploaded] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [checked, setChecked] = useState(false);
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  const { data: addressList } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressDataFallback = addressList?.data?.data ?? [];

  const addressData = useSelector(
    // @ts-ignore
    (state) => state?.addressData?.data ?? addressDataFallback
  );

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id") ?? "";
  const decodedId = atob(encodedId);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const meetingCreateRequest = localStorage.getItem("meetingCreateRequest");
  const parsedMeetingRequest = meetingCreateRequest
    ? JSON.parse(meetingCreateRequest)
    : null;
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState<FormData>(() => {
    return (
      parsedMeetingRequest || {
        meetingType: MeetingTypeOption[0].value,
        meetingPlace: "",
        meetingPurpose: "",
        meetingMethod: "",
        platformType: "",
        meetingDate: "",
        meetingTime: "",
        GISInformation: "",
        meetingAddress: "",
        state: "",
        district: "",
        city: "",
        postcode: "",
        totalAttendees: 7,
        meetingContent: "",
        openingRemarks: "",
        mattersDiscussed: "",
        othersDiscussionRemarks: "",
        closingRemarks: "",
        providedBy: userName,
        confirmBy: "",
        meetingMinute: "",
        icNo: 123456789,
        present: 0,
        createdBy: "admin",
        modifiedBy: "admin",
        memberAttendances: [],
      }
    );
  });

  // ✅ AWS Location Client Initialization
  useEffect(() => {
    const initializeClient = async () => {
      try {
        const authHelper = await withAPIKey(API_KEY, AWS_REGION);
        const locationClient = new LocationClient(authHelper.getClientConfig());
        setClient(locationClient);
      } catch (error) {
        console.error("❌ Failed to initialize AWS Location Client:", error);
      }
    };
    initializeClient();
  }, []);

  const searchLocation = async (query: string) => {
    if (!query) return;
    if (!client) {
      console.warn("❌ AWS Location Client has not been initialized");
      return;
    }
    try {
      const command = new SearchPlaceIndexForTextCommand({
        IndexName: PLACE_INDEX_NAME,
        Text: query,
        MaxResults: 1,
        FilterCountries: ["MYS"],
      });
      const response = await client.send(command);
      console.log("place", response);
      if (!response?.Results || response.Results.length === 0) {
        console.warn("⚠️ No locations found");
        setLocations([]);
        return;
      }
      const firstResult = response.Results[0];
      console.log("firstResult", firstResult);

      if (firstResult?.Place?.Geometry?.Point) {
        const [longitude, latitude] = firstResult.Place.Geometry.Point;
        console.log("📍 Coordinates:", longitude, latitude);

        const city = firstResult.Place.Municipality || "";
        const postcode = firstResult.Place.PostalCode || "";
        const stateName = firstResult.Place.Region || "";
        const districtName = firstResult.Place.SubRegion || "";

        console.log("🏙️ City:", city);
        console.log("📮 Postcode:", postcode);
        console.log("🗺️ State name:", stateName);
        console.log("🗺️ District name:", districtName);
        setMeetingCoords([longitude, latitude]);
        setFormData((prev) => ({
          ...prev,
          city: city || prev.city,
          postcode: postcode || prev.postcode,
        }));
      }

      setLocations(
        response.Results.map(
          (place) => place.Place?.Label ?? "Unknown Location"
        )
      );
    } catch (error) {
      console.error("Error searching location:", error);
    }
  };

  const handleMeetingLocationSelected = (location: {
    fullAddress: string;
    state: string;
    district: string;
    city: string;
    postcode: string;
  }) => {
    setFormData((prev) => ({
      ...prev,
      meetingAddress: location.fullAddress,
      city: location.city,
      postcode: location.postcode,
    }));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
    setFormErrors((prev) => ({ ...prev, [name]: "" }));
    if (name === "meetingAddress") {
      clearTimeout((window as any).addressTimeout);
      (window as any).addressTimeout = setTimeout(() => {
        searchLocation(value);
      }, 500);
    }
  };

  const { mutateAsync: createMeeting, isLoading: isCreating } = useCreate();
  const { mutateAsync: updateMeeting, isLoading: isUpdating } =
    useCustomMutation();

  const {
    upload,
    progress,
    isLoading: isUploadingFile,
    returnId,
  } = useUploadPresignedUrl({
    onSuccessUpload: () => {
      setSelectedFile(null);
      refetchDocuments();
    },
  });

  const validateForm = () => {
    const attendeesErrors = formMeetingAttendeesRef.current?.getErrors();
    const dateTimeErrors = formMeetingDateTimeRef.current?.getErrors();
    const errors: { [key: string]: string } = {};
    if (!formData.meetingMethod) errors.meetingMethod = t("requiredValidation");
    if (!formData.meetingPurpose)
      errors.meetingPurpose = t("requiredValidation");
    if (dateTimeErrors?.meetingDate)
      errors.meetingDate = dateTimeErrors.meetingDate.message ?? "";
    if (attendeesErrors?.totalAttendees) {
      errors.totalAttendees = attendeesErrors.totalAttendees.message ?? "";
    }
    if (
      formData.meetingMethod == MeetingMethods.ATAS_TALIAN ||
      formData.meetingMethod == MeetingMethods.HYBRID
    ) {
      if (!formData.platformType) errors.platformType = t("requiredValidation");
    }
    if (dateTimeErrors?.meetingTime) {
      errors.masaMula = dateTimeErrors.meetingTime.message ?? "";
    }
    if (dateTimeErrors?.meetingTimeTo) {
      errors.masaTamat = dateTimeErrors.meetingTimeTo.message ?? "";
    }
    if (
      meetingMethod == MeetingMethods.BERSEMUKA ||
      meetingMethod == MeetingMethods.HYBRID
    ) {
      if (!formData.meetingPlace) {
        errors.meetingPlace = t("requiredValidation");
      }

      if (!formData.meetingAddress) {
        errors.meetingAddress = t("requiredValidation");
      }

      if (!formData.state) {
        errors.state = t("requiredValidation");
      }

      if (!formData.district) {
        errors.district = t("requiredValidation");
      }

      if (!formData.postcode) {
        errors.postcode = t("requiredValidation");
      } else if (!/^\d{5}$/.test(formData.postcode)) {
        errors.postcode = t("postcodeValidation");
      }

      // meetingPlace   meetingAddress state district postcode

      // { field: formData.meetingPlace, name: t("tempatMesyuarat") },
      // { field: formData.meetingAddress, name: t("alamatTempatMesyuarat") },
      // { field: formData.state, name: t("state") },
      // { field: formData.district, name: t("district") },
      // { field: formData.postcode, name: t("poskod") }
    }

    return errors;
  };

  const { mutate: deleteFile, isLoading: isLoadingDelete } =
    useCustomMutation();

  const DeleteFile: (id: number | undefined) => void = (id) => {
    if (!id) return;

    deleteFile(
      {
        url: `${API_URL}/society/document/deleteDocument?id=${id}`,
        method: "put",
        values: {},
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  function handleDeleteFile(id: number | undefined) {
    DeleteFile(id);
  }
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  const handleSave = async () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    if (
      formData.postcode.length !== 5 &&
      (formData.meetingMethod == MeetingMethods.BERSEMUKA ||
        formData.meetingMethod == MeetingMethods.HYBRID)
    ) {
      return {
        message: t("postcodeValidation"),
        type: "error",
      };
    }

    const attendeesValue = formMeetingAttendeesRef.current?.getValue();
    const dateTimeValue = formMeetingDateTimeRef.current?.getValue();
    const totalAttendees = attendeesValue?.totalAttendees ?? 7;
    const meetingDate = dateTimeValue.meetingDate;
    const formattedTime = dateTimeValue.meetingTime;
    const formattedTimeFrom = formattedTime;
    const formattedTimeTo = dateTimeValue.meetingTimeTo;

    if (societyDataRedux) {
      const data = {
        societyId: parseInt(decodedId),
        society_no: societyDataRedux.societyNo,
        meetingType: formData.meetingType ?? "",
        meetingPlace: formData.meetingPlace ?? "",
        meetingPurpose: formData.meetingPurpose ?? "",
        meetingMethod: formData.meetingMethod?.toString() ?? "",
        platformType: formData.platformType?.toString() ?? "",
        meetingDate: meetingDate,
        meetingTime: formattedTime ?? "",
        meetingTimeFrom: formattedTimeFrom ?? "",
        meetingTimeTo: formattedTimeTo ?? "",
        GISInformation: "",
        meetingAddress: formData.meetingAddress ?? "",
        state: formData.state?.toString() ?? "",
        district: formData.district?.toString() ?? "",
        city: formData.city ?? "",
        postcode: formData.postcode ?? "",
        totalAttendees,
        meetingContent: formData.meetingContent ?? "",
        providedBy: formData.providedBy ? formData.providedBy : userName,
        confirmBy: formData.confirmBy ?? "",
        meetingMinute: "",
        status: 1,
        openingRemarks: formData.openingRemarks,
        mattersDiscussed: formData.mattersDiscussed,
        otherMatters: formData.othersDiscussionRemarks,
        closing: formData.closingRemarks,
      };

      if (selectedFile || uploadedFiles?.length) {
        setFileNotUploaded(false);

        if (meetingId) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          data["id"] = meetingId;
          await updateMeeting(
            {
              method: "put",
              url: `${API_URL}/society/meeting/update`,
              values: data,
              config: {
                headers: {
                  portal: localStorage.getItem("portal"),
                  authorization: `Bearer ${localStorage.getItem(
                    "refine-auth"
                  )}`,
                },
              },
              successNotification: (data) => {
                setIsCreated(true);
                if (uploadedFiles?.length && selectedFile) {
                  handleDeleteFile(uploadedFiles[0].id);
                  uploadParams.name = selectedFile.name;
                  uploadParams.societyId = societyDataRedux.id;
                  uploadParams.societyNo = societyDataRedux.societyNo;
                  uploadParams.icNo = societyDataRedux.identificationNo;
                  uploadParams.meetingId = Number(meetingId);
                  upload({
                    file: selectedFile,
                    params: uploadParams,
                  });
                } else if (uploadedFiles?.length === 0 && selectedFile) {
                  uploadParams.name = selectedFile.name;
                  uploadParams.societyId = societyDataRedux.id;
                  uploadParams.societyNo = societyDataRedux.societyNo;
                  uploadParams.icNo = societyDataRedux.identificationNo;
                  uploadParams.meetingId = Number(meetingId);
                  upload({
                    file: selectedFile,
                    params: uploadParams,
                  });
                }

                return {
                  message: t("messageMaklumatMesyuaratPenubuhanSuccess"),
                  type: "success",
                };
              },
              errorNotification: (data) => {
                return {
                  message: t("messageMaklumatMesyuaratPenubuhanError"),
                  type: "error",
                };
              },
            },
            {
              onSuccess(data, variables, context) {},
            }
          );
        } else {
          await createMeeting({
            resource: "society/meeting/create",
            values: data,
            meta: {
              headers: {
                portal: localStorage.getItem("portal"),
                authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
              },
            },
            successNotification: (data) => {
              setIsSaved(true);
              setMeetingId(data?.data?.data);
              // uploadSelectedFiles(societyDataRedux, data);
              if (selectedFile) {
                uploadParams.name = selectedFile.name;
                uploadParams.societyId = societyDataRedux.id;
                uploadParams.societyNo = societyDataRedux.societyNo;
                uploadParams.icNo = societyDataRedux.identificationNo;
                uploadParams.meetingId = data?.data?.data;
                setIsCreated(true);
                uploadedFiles?.length;
                upload({
                  file: selectedFile,
                  params: uploadParams,
                });
              }

              return {
                message: t("messageMaklumatMesyuaratPenubuhanSuccess"),
                type: "success",
              };
            },
            errorNotification: (data) => {
              return {
                message: t("messageMaklumatMesyuaratPenubuhanError"),
                type: "error",
              };
            },
          });
        }
      } else {
        setFileNotUploaded(true);
      }
      // please don't add return here
      // after successfully create or update meeting data,
      // FE needs to get that data again with the updated one
      // return;
      await getMeetingData(encodedId);
    }
  };

  useEffect(() => {
    if (meetingCreateRequest) {
      setIsCreated(true);
    }
  }, [meetingCreateRequest]);

  const getMeetingData = async (encodedId: string) => {
    setIsLoadingData(true);
    try {
      const decodedId = atob(encodedId);
      const response = await fetch(
        `${API_URL}/society/meeting/findBySocietyId/${decodedId}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );
      const data = (await response.json()) as { data: any[] };
      if (data.data !== null) {
        const lastMeeting = data?.data[data?.data?.length - 1];
        setFormData((prev) => ({
          ...prev,
          meetingType: lastMeeting?.meetingType || MeetingTypeOption[0].value,
          meetingPlace: lastMeeting?.meetingPlace,
          meetingPurpose: lastMeeting?.meetingPurpose,
          meetingMethod: lastMeeting?.meetingMethod?.toString() || "",
          platformType: lastMeeting?.platformType?.toString() || "",
          meetingDate: lastMeeting?.meetingDate,
          meetingAddress: lastMeeting?.meetingAddress,
          state: lastMeeting?.state?.toString() || "",
          district: lastMeeting?.district?.toString() || "",
          city: lastMeeting?.city,
          postcode: lastMeeting?.postcode,
          totalAttendees: lastMeeting?.totalAttendees,
          meetingContent: lastMeeting?.meetingContent,
          openingRemarks: lastMeeting?.openingRemarks,
          mattersDiscussed: lastMeeting?.mattersDiscussed,
          othersDiscussionRemarks: lastMeeting?.otherMatters,
          closingRemarks: lastMeeting?.closing,
          providedBy: lastMeeting?.providedBy,
          confirmBy: lastMeeting?.confirmBy,
          memberAttendances: lastMeeting?.meetingMemberAttendances ?? [],
        }));

        setMeetingType(lastMeeting?.meetingType || MeetingTypeOption[0].value);
        setMeetingMethod(lastMeeting?.meetingMethod?.toString() || "");
        setPlatformType(lastMeeting?.platformType?.toString() || "");
        setMeetingContent(lastMeeting?.meetingContent);
        setState(lastMeeting?.state?.toString() || "");
        setDistrict(lastMeeting?.district?.toString() || "");
        setCity(lastMeeting?.city);
        setPostcode(lastMeeting?.postcode);
        setMeetingId(lastMeeting?.id);

        // Set meeting from time if available
        if (lastMeeting?.meetingTime) {
          const fromTime = lastMeeting?.meetingTime.split(":");
          setMasaMula(`${fromTime[0]}:${fromTime[1]}`);
        }

        // Set meeting to time if available
        if (lastMeeting?.meetingTimeTo) {
          const toTime = lastMeeting?.meetingTimeTo.split(":");
          setMasaTamat(`${toTime[0]}:${toTime[1]}`);
        }

        setIsCreated(true);
        setGenerateMinutesButtonDisabled(false);
      } else {
        setGenerateMinutesButtonDisabled(true);
      }
    } finally {
      setIsLoadingData(false);
    }
  };

  const downloadFile = (filePath: string, fileName: string) => {
    const link = document.createElement("a");
    link.href = filePath;
    link.download = fileName;
    link.click();
  };

  const janaMinit = async () => {
    const meetingDateTimeValue = formMeetingDateTimeRef.current?.getValue();

    const meetingDate = meetingDateTimeValue.meetingDate;
    const meetingTimeFrom = meetingDateTimeValue.meetingTime;
    const meetingTimeTo = meetingDateTimeValue.meetingTimeTo;
    // Validate mandatory fields
    const mandatoryFields = [
      { field: formData.meetingType, name: t("meetingType") },
      { field: formData.meetingMethod, name: t("meetingMethod") },
      { field: formData.meetingPurpose, name: t("tujuanMesyuarat") },
      { field: meetingDate, name: t("tarikhMesyuarat") },
      { field: meetingTimeFrom, name: t("startDate") },
      { field: meetingTimeTo, name: t("endDate") },
    ];

    // Add conditional mandatory fields based on meetingMethod
    if (
      formData.meetingMethod == MeetingMethods.BERSEMUKA ||
      formData.meetingMethod == MeetingMethods.HYBRID
    ) {
      mandatoryFields.push(
        { field: formData.meetingPlace, name: t("tempatMesyuarat") },
        { field: formData.meetingAddress, name: t("alamatTempatMesyuarat") },
        { field: formData.state, name: t("state") },
        { field: formData.district, name: t("district") },
        { field: formData.postcode, name: t("poskod") }
      );
    }

    // Add conditional mandatory fields based on meetingMethod
    if (
      formData.meetingMethod == MeetingMethods.ATAS_TALIAN ||
      formData.meetingMethod == MeetingMethods.HYBRID
    ) {
      mandatoryFields.push({
        field: formData.platformType,
        name: t("platformType"),
      });
    }

    // Check for empty mandatory fields
    const emptyFields = mandatoryFields.filter((field) => !field.field);

    if (emptyFields.length > 0) {
      alert(
        t("mandatoryFieldsAlert") +
          " : \n" +
          emptyFields.map((field) => field.name).join(", ")
      );
      return;
    }

    // If validation passes, proceed with existing janaMinit logic
    try {
      if (decodedId) {
        const attendeesValue = formMeetingAttendeesRef.current?.getValue();
        const meetingDateTimeValue = formMeetingDateTimeRef.current?.getValue();

        const meetingDate = meetingDateTimeValue.meetingDate;
        const meetingTimeFrom = meetingDateTimeValue.meetingTime;
        const meetingTimeTo = meetingDateTimeValue.meetingTimeTo;
        const response = await fetch(`${API_URL}/society/pdf/meetingMinutes`, {
          method: "post",
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
          body: JSON.stringify({
            societyId: decodedId,
            meetingTitle: "",
            meetingDescription: "",
            meetingDate: meetingDate,
            meetingTimeFrom: meetingTimeFrom,
            meetingTimeTo: meetingTimeTo,
            meetingLocation: formData.meetingPlace,
            openingRemarks: formData.openingRemarks,
            mattersDiscussed: formData.mattersDiscussed,
            othersDiscussionRemarks: formData.othersDiscussionRemarks,
            closingRemarks: formData.closingRemarks,
            preparedBy: formData.providedBy,
            approvedBy: formData.confirmBy,
          }),
        });
        const data = await response.json();
        if (data.status === "SUCCESS") {
          let base64String = data?.data?.byte ?? "";

          if (base64String.startsWith("JVB")) {
            base64String = `data:application/pdf;base64,${base64String}`;
          } else if (base64String.startsWith("data:application/pdf;base64")) {
            //alert("Not a valid Base64 PDF string. Please check");
          } else {
            //alert("Not a valid Base64 PDF string. Please check");
          }
          downloadFile(base64String, `minit-mesyuarat.pdf`);
        }
      }
    } catch (error) {
      console.error("error:", error);
      return null;
    }
  };

  const downloadPdfTemplate = async () => {
    const response = await fetch(
      `${API_URL}/society/pdf/getPDFTemplate?templateCode=MEETING_MINUTES_PDF`,
      {
        method: "get",
        headers: {
          "Content-Type": "application/json",
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      }
    );
    const data = (await response.json()) as {
      status: "SUCCESS";
      data: { meetingMinutes: string };
    };
    if (data.status === "SUCCESS") {
      const url = data?.data?.meetingMinutes ?? "";
      window.open(url, "_blank", "noreferrer,noopener");
    }
  };

  useEffect(() => {
    if (encodedId.length > 0) {
      getMeetingData(encodedId);
    }
  }, []);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    const allowedTypes = ["application/pdf"];

    if (!file) return;

    if (file.size > DOCUMENT_MAX_FILE_SIZE) {
      open?.({
        type: "error",
        message: t("inputValidationErrorFileSizeExceedsLimit"),
      });

      return;
    }
    // return alert(
    //   t("validation.documentSize", { maxSize: DOCUMENT_MAX_FILE_SIZE })
    // );

    if (!allowedTypes.includes(file.type)) {
      open?.({
        type: "error",
        message: t("validation.invalidFileType"),
      });
      return;
    }

    setSelectedFile(file);
    setFormData((prevState) => ({
      ...prevState,
      meetingMinute: file.name,
    }));
  };

  // Add this new function to handle form reset
  const handleReset = () => {
    // Reset all form fields to their initial values

    setFormData({
      meetingType: MeetingTypeOption[0].value,
      meetingPlace: "",
      meetingPurpose: "",
      meetingMethod: "",
      platformType: "",
      meetingDate: "",
      meetingTime: "",
      GISInformation: "",
      meetingAddress: "",
      state: "",
      district: "",
      city: "",
      postcode: "",
      totalAttendees: 7,
      meetingContent: "",
      openingRemarks: "",
      mattersDiscussed: "",
      othersDiscussionRemarks: "",
      closingRemarks: "",
      providedBy: "",
      confirmBy: "",
      meetingMinute: "",
      icNo: 123456789,
      present: 0,
      createdBy: "admin",
      modifiedBy: "admin",
      memberAttendances: [],
    });

    // Reset all other state variables
    setMeetingType(MeetingTypeOption[0].value);
    setMeetingMethod("");
    setPlatformType("");
    setMeetingContent("");
    setMasaMula("");
    setMasaTamat("");
    setState("");
    setDistrict("");
    setCity("");
    setPostcode("");
    setMembers([]);
    setNewMember({ name: "", role: "" });
    setSelectedFile(null);
  };

  // Add new state for coordinates
  const [meetingCoords, setMeetingCoords] = useState<[number, number]>([
    101.707021, 2.745564,
  ]);

  const { data, isLoading } = useCustom({
    url: `${API_URL}/society/admin/meeting/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const meetingData = data?.data?.data || [];

  const {
    data: documentData,
    refetch: refetchDocuments,
    isLoading: isLoadingDocument,
  } = useQuery({
    url: "society/document/documentByParam",
    filters: [
      {
        field: "type",
        operator: "eq",
        value: DocumentUploadType.MEETING,
      },
      {
        field: "societyId",
        operator: "eq",
        value: societyDataRedux?.id,
      },
      {
        field: "meetingId",
        operator: "eq",
        value: meetingId,
      },
    ],
    autoFetch: false,
  });

  useEffect(() => {
    if (meetingId && societyDataRedux?.id) {
      refetchDocuments();
    }
  }, [meetingId, societyDataRedux?.id]);

  useEffect(() => {
    setUploadedFiles(documentData?.data?.data);
  }, [documentData?.data?.data]);

  const { data: checkAndUpdateRegistrationData, fetch: fetchCheck } =
    useCheckAndUpdateRegistration({
      id: societyDataRedux?.id,
      pageNo: 2,
      enabled: false,
      onSuccessNotification: (data) => {
        const responseData = data?.data?.data;
        //  const message = data?.data?.msg
        //  if(!responseData){
        //   return {
        //     message: message,
        //     type: "error",
        //   };
        //  }
      },
      onSuccess: (data) => {
        const responseData = data?.data?.data;
        if (responseData) {
          navigate(`../perlembagaan?id=${encodedId}`);
        }
      },
    });

  const handleGoNext = () => {
    fetchCheck();
  };

  //AwsMapInit(meetingCoords[0], meetingCoords[1]);
  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box sx={{ width: "55vw" }}>
        <LoadingOverlay isLoading={isLoadingData} />

        <Fade in={true} timeout={500}>
          <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px" }}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mb: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {capitalizeWords(t("establishmentMeeting"))}
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("meetingType")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <FormControl fullWidth>
                    <Select
                      sx={{ backgroundColor: "#DADADA" }}
                      value={formData.meetingType}
                      disabled
                      displayEmpty
                      required
                      onChange={(e) => {
                        setFormData((prevState: any) => ({
                          ...prevState,
                          meetingType: e.target.value,
                        }));
                      }}
                    >
                      {MeetingTypeOption.map((item: any, index) => (
                        <MenuItem key={index} value={item.value}>
                          {item.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("meetingMethod")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <FormControl fullWidth error={!!formErrors.meetingMethod}>
                    <Select
                      value={
                        meetingCreateRequest
                          ? formData.meetingMethod
                          : meetingMethod || ""
                      }
                      name="meetingMethod"
                      displayEmpty
                      required
                      disabled={isLoading}
                      onChange={(e) => {
                        setMeetingMethod(e.target.value);
                        setFormData((prevState) => ({
                          ...prevState,
                          meetingMethod: e.target.value,
                        }));
                        if (e.target.value) {
                          setFormErrors((prev) => ({
                            ...prev,
                            meetingMethod: "",
                          }));
                        }
                      }}
                    >
                      <MenuItem value="" disabled>
                        {isLoading ? "Loading..." : t("pleaseSelect")}
                      </MenuItem>
                      {!isLoading &&
                        meetingData
                          .filter((item: any) => item.pid === 2)
                          .map((item: any) => (
                            <MenuItem key={item.id} value={item.id}>
                              {i18n.language === "en"
                                ? item.nameEn
                                : item.nameBm == "Hybrid"
                                ? "Hibrid"
                                : item.nameBm}
                            </MenuItem>
                          ))}
                    </Select>
                    {formErrors.meetingMethod && (
                      <FormHelperText>
                        {formErrors.meetingMethod}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
                {meetingMethod == MeetingMethods.ATAS_TALIAN ||
                meetingMethod == MeetingMethods.HYBRID ? (
                  <>
                    <Grid item xs={12} sm={4}>
                      <Typography sx={labelStyle}>
                        {t("platformType")}{" "}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <FormControl fullWidth error={!!formErrors.platformType}>
                        <Select
                          name="platformType"
                          value={
                            meetingCreateRequest
                              ? formData.platformType
                              : platformType || ""
                          }
                          displayEmpty
                          required
                          disabled={isLoading}
                          onChange={(e) => {
                            setPlatformType(e.target.value);
                            setFormData((prevState) => ({
                              ...prevState,
                              platformType: e.target.value,
                            }));
                            setFormErrors((prev) => ({
                              ...prev,
                              platformType: "",
                            }));
                          }}
                        >
                          <MenuItem value="" disabled>
                            {isLoading ? "Loading..." : t("pleaseSelect")}
                          </MenuItem>
                          {!isLoading &&
                            meetingData
                              .filter((item: any) => item.pid === 3)
                              .map((item: any) => (
                                <MenuItem key={item.id} value={item.id}>
                                  {i18n.language === "en"
                                    ? item.nameEn
                                    : item.nameBm}
                                </MenuItem>
                              ))}
                        </Select>
                        {formErrors.platformType && (
                          <FormHelperText>
                            {formErrors.platformType}
                          </FormHelperText>
                        )}
                      </FormControl>
                    </Grid>
                  </>
                ) : (
                  <></>
                )}
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("tujuanMesyuarat")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    required
                    name="meetingPurpose"
                    value={formData.meetingPurpose}
                    onChange={handleInputChange}
                    error={!!formErrors.meetingPurpose}
                    helperText={formErrors.meetingPurpose}
                  />
                </Grid>
              </Grid>
            </Box>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mb: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("masaDanTarikhMesyuarat")}
              </Typography>
              <FormMeetingDateTime
                // @ts-expect-error
                ref={formMeetingDateTimeRef}
                meetingTimeFromAttribute="meetingTime"
                defaultValues={{
                  meetingDate:
                    formData.meetingDate.length > 0
                      ? dayjs(formData.meetingDate, "YYYY-MM-DD")
                      : null,
                  meetingTime:
                    formData.meetingDate.length > 0
                      ? dayjs(
                          `${formData.meetingDate} ${
                            masaMula?.length > 0 ? masaMula : "00:00:00"
                          }`,
                          "YYYY-MM-DD HH:mm:[00]"
                        )
                      : null,
                  meetingTimeTo:
                    formData.meetingDate.length > 0
                      ? dayjs(
                          `${formData.meetingDate} ${
                            masaTamat?.length > 0 ? masaTamat : "00:00:00"
                          }`,
                          "YYYY-MM-DD HH:mm:[00]"
                        )
                      : null,
                }}
              />
            </Box>
            {meetingMethod == MeetingMethods.BERSEMUKA ||
            meetingMethod == MeetingMethods.HYBRID ? (
              <>
                <Box
                  sx={{
                    p: { xs: 1, sm: 2, md: 3 },
                    border: "1px solid #D9D9D9",
                    borderRadius: "14px",
                    mb: 2,
                  }}
                >
                  <Typography variant="subtitle1" sx={sectionStyle}>
                    {t("alamatTempatMesyuarat")}
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Typography sx={labelStyle}>
                        {t("tempatMesyuarat")}{" "}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <TextField
                        fullWidth
                        required
                        name="meetingPlace"
                        value={formData.meetingPlace}
                        onChange={handleInputChange}
                        error={!!formErrors.meetingPlace}
                        helperText={formErrors.meetingPlace}
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography sx={labelStyle}>
                        {t("meetingLocation")}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <div
                        style={{
                          height: "150px",
                          width: "100%",
                          borderRadius: "8px",
                        }}
                      >
                        <AWSLocationMap
                          longitude={meetingCoords[0]}
                          latitude={meetingCoords[1]}
                          // zoom={20}
                          onLocationSelected={handleMeetingLocationSelected}
                        />
                      </div>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography sx={labelStyle}>
                        {t("alamatTempatMesyuarat")}{" "}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <TextField
                        fullWidth
                        required
                        name="meetingAddress"
                        value={formData.meetingAddress}
                        error={!!formErrors.meetingAddress}
                        helperText={formErrors.meetingAddress}
                        onChange={handleInputChange}
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography sx={labelStyle}>
                        {t("state")} <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={8}>
                      <FormControl
                        fullWidth
                        required
                        error={!!formErrors.state}
                      >
                        <Select
                          value={formData.state}
                          displayEmpty
                          required
                          name="state"
                          //disabled={isLoadingAddress}
                          onChange={(e) => {
                            setState(e.target.value);
                            setFormData((prevState) => ({
                              ...prevState,
                              state: e.target.value,
                            }));
                            setFormErrors((prev) => ({
                              ...prev,
                              state: "",
                            }));
                          }}
                        >
                          <MenuItem value="" disabled>
                            {t("pleaseSelect")}
                          </MenuItem>
                          {addressData
                            .filter((item: any) => item.pid === MALAYSIA)
                            .map((item: any) => (
                              <MenuItem key={item.id} value={item.id}>
                                {item.name}
                              </MenuItem>
                            ))}
                        </Select>
                        {formErrors.state && (
                          <FormHelperText>{formErrors.state}</FormHelperText>
                        )}
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography sx={labelStyle}>
                        {t("district")} <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <FormControl
                        fullWidth
                        required
                        error={!!formErrors.district}
                      >
                        <Select
                          value={formData.district}
                          displayEmpty
                          required
                          name="district"
                          disabled={!formData.state}
                          onChange={(e) => {
                            setFormData((prevState) => ({
                              ...prevState,
                              district: e.target.value,
                            }));
                            setFormErrors((prev) => ({
                              ...prev,
                              district: "",
                            }));
                          }}
                        >
                          <MenuItem value="" disabled>
                            {t("pleaseSelect")}
                          </MenuItem>
                          {addressData
                            .filter((item: any) => item.pid == formData.state)
                            .map((item: any) => (
                              <MenuItem key={item.id} value={item.id}>
                                {item.name}
                              </MenuItem>
                            ))}
                        </Select>
                        {formErrors.district && (
                          <FormHelperText>{formErrors.district}</FormHelperText>
                        )}
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography sx={labelStyle}>{t("bandar")}</Typography>
                    </Grid>

                    <Grid item xs={12} sm={8}>
                      <TextField
                        fullWidth
                        name="city"
                        value={formData.city}
                        onChange={handleInputChange}
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography sx={labelStyle}>
                        {t("poskod")} <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                      <TextField
                        fullWidth
                        required
                        name="postcode"
                        value={formData.postcode}
                        onChange={(e: any) => {
                          const value = e.target.value.replace(/[^0-9]/g, "");
                          if (value.length <= 5) {
                            handleInputChange({
                              ...e,
                              target: {
                                name: "postcode",
                                value,
                              },
                            });
                          }
                        }}
                        error={
                          (formData.postcode.length > 0 &&
                            formData.postcode.length !== 5) ||
                          !!formErrors.postcode
                        }
                        helperText={
                          (formData.postcode.length > 0 &&
                          formData.postcode.length !== 5
                            ? t("masukkan5DigitPoskod")
                            : "") || formErrors.postcode
                        }
                        type="text"
                        onKeyDown={(e) => {
                          if (
                            e.key.toLowerCase() === "e" ||
                            e.key === "E" ||
                            e.key === "+" ||
                            e.key === "-"
                          ) {
                            e.preventDefault();
                          }
                        }}
                        inputProps={{
                          maxLength: 5,
                          pattern: "[0-9]*",
                        }}
                      />
                    </Grid>
                  </Grid>
                </Box>
              </>
            ) : (
              <></>
            )}

            <FormMeetingAttendees
              // @ts-expect-error
              ref={formMeetingAttendeesRef}
              defaultValues={{
                totalAttendees: formData.totalAttendees,
              }}
            />
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mb: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("minitMesyuarat")}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("minitMesyuarat")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Box
                    sx={{
                      border: "2px solid #DADADA",
                      borderRadius: "8px",
                      p: 2,
                      gap: 2,
                      textAlign: "center",
                      cursor: "pointer",
                      height: "200px",
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                    onClick={() => {
                      const element = document.getElementById("meetingMinute");
                      if (element) {
                        element.click();
                      }
                    }}
                  >
                    {selectedFile || uploadedFiles?.length ? (
                      <Typography sx={{ color: "#147C7C", mb: 1 }}>
                        {selectedFile
                          ? selectedFile.name
                          : uploadedFiles[0]?.name}
                      </Typography>
                    ) : (
                      <>
                        <Box
                          sx={{
                            width: 50,
                            aspectRatio: "1/1",
                            display: "flex",
                            justifyContent: "center",
                            alignContent: "center",
                            textAlign: "center",
                            borderRadius: 20,
                            mb: 2,
                            // p: 5,
                            bgcolor: "#F2F4F7",
                          }}
                        >
                          <img
                            width={30}
                            src={"/uploadFileIcon.svg"}
                            alt={"view"}
                          />
                        </Box>

                        <Typography
                          sx={{
                            color: "var(--primary-color)",
                            fontWeight: "500",
                            fontSize: "14px",
                          }}
                        >
                          {t("muatNaik")}
                        </Typography>
                        <Typography
                          sx={{
                            color: "#667085",
                            fontWeight: "400",
                            fontSize: "12px",
                          }}
                        >
                          {`PDF <25 MB`}
                        </Typography>
                      </>
                    )}
                    <input
                      id="meetingMinute"
                      type="file"
                      hidden
                      onChange={handleFileChange}
                      accept=".pdf"
                    />
                  </Box>

                  {fileNotUploaded &&
                  (uploadedFiles?.length === 0 || !selectedFile) ? (
                    <FormHelperText sx={{ color: "red" }}>
                      {t("fieldRequired")}
                    </FormHelperText>
                  ) : null}
                  <Typography
                    sx={{
                      mt: 3,
                      color: "#147C7C",
                      textDecoration: "underline",
                      cursor: "pointer",
                    }}
                    onClick={downloadPdfTemplate}
                  >
                    {t("downloadTemplate")}
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <Box
                    sx={{
                      px: { xs: 1, sm: 2, md: 3 },
                      py: 1,
                      border: "1px solid #D9D9D9",
                      backgroundColor: "#FFFFFF",
                      borderRadius: "14px",
                      mt: 2,
                      display: "flex",
                      justifyContent: "flex-start",
                      alignItems: "flex-start",
                    }}
                  >
                    <FormControlLabel
                      sx={{
                        alignItems: "flex-start",
                        color: "#666666",
                        "& .MuiFormControlLabel-label": {
                          fontWeight: 400,
                        },
                      }}
                      control={
                        <Checkbox
                          checked={checked}
                          onChange={handleChangeCheckbox}
                          sx={{ mt: -1 }}
                        />
                      }
                      label={
                        <>
                          {t("meetingInfoConfirmation")}{" "}
                          <span style={{ color: "red" }}>*</span>
                        </>
                      }
                    />
                  </Box>
                </Grid>
                <Grid
                  item
                  xs={12}
                  sx={{
                    mt: 2,
                    display: "flex",
                    flexDirection: isMobile ? "column" : "row",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <ButtonOutline
                    sx={{
                      bgcolor: "white",
                      "&:hover": { bgcolor: "white" },
                      width: isMobile ? "100%" : "auto",
                    }}
                    onClick={handleReset}
                  >
                    {t("semula")}
                  </ButtonOutline>
                  <ButtonPrimary
                    disabled={isCreating || isUpdating || !checked}
                    onClick={handleSave}
                    variant="contained"
                    sx={{ width: isMobile ? "100%" : "auto" }}
                  >
                    {isCreating ? t("saving") : t("update")}
                  </ButtonPrimary>
                </Grid>
              </Grid>
            </Box>
            <Box sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
              <ButtonPrimary
                disabled={!isCreated}
                onClick={() => handleGoNext()}
                variant="contained"
                sx={{
                  width: isMobile ? "100%" : "auto",
                  float: "right",
                }}
              >
                {t("next")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <OrganizationStepper
          activeStep={activeStep}
          hidePayment={
            societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI
          }
        />

        {societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI ? (
          <Box
            sx={{
              padding: 3,
              backgroundColor: "white",
              borderRadius: "15px",
              maxHeight: "60vh",
              maxWidth: "18vw",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("kuiri")}
            </Typography>
            <Box
              sx={{
                padding: 3,
                backgroundColor: "#DADADA",
                borderRadius: "15px",
                maxHeight: "60vh",
                maxWidth: "18vw",
              }}
            >
              <Typography
                sx={{
                  mb: 8,
                  fontSize: "12px",
                  color: "#666666",
                  fontWeight: "500 !important",
                }}
              >
                {societyDataRedux.queryText}
              </Typography>
            </Box>
          </Box>
        ) : null}

        <InfoQACard />
      </Box>
    </Box>
  );
};

export default CreateMesyuarat;
