import { useEffect, useState } from "react";
import {
  Box,
  Typography,
} from "@mui/material";
import { useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { OrganizationStepper } from "../organization-stepper";
import dayjs from "@/helpers/dayjs";
import {
  ApplicationStatus,
  MeetingTypeOption,
  useMutation,
} from "@/helpers";
import {
  DocumentUploadType,
  useQuery,
  useUploadPresignedUrl,
} from "@/helpers";
import { LoadingOverlay } from "../../../../components/loading";
import InfoQACard from "../InfoQACard";
import { useSelector } from "react-redux";
import { useFormManagementMeetingCreateBySocietyIdValidationSchema } from "@/controllers";
import { boolean, object } from "yup";
import { FormMeetingCreateSocietyRegistrationInner, MeetingCreateSocietyRegistrationCreateOrUpdateFormPayload, MeetingCreateSocietyRegistrationCreateOrUpdateRequestBody, MeetingCreateSocietyRegistrationCreateOrUpdateResponseBodyGet } from "@/components/form/meeting/create/SocietyRegistrationInner";
import { FormikHelpers } from "formik";
import { getUserDetails } from "@/redux/userReducer";
import { getSocietyDataRedux } from "@/redux/societyDataReducer";
import { useFileManagement } from "@/helpers/hooks/useFileManagement";
import { Formik } from "@/contexts";

function useTransform<
  Payload extends MeetingCreateSocietyRegistrationCreateOrUpdateFormPayload = MeetingCreateSocietyRegistrationCreateOrUpdateFormPayload,
  ReqBody extends MeetingCreateSocietyRegistrationCreateOrUpdateRequestBody = MeetingCreateSocietyRegistrationCreateOrUpdateRequestBody,
>() {
  const [params] = useSearchParams();
  const societyDataRedux = useSelector(getSocietyDataRedux)!;

  const encodedId = params.get("id") ?? null;
  const societyId = encodedId !== null ? atob(encodedId) : null;
  const society_no = societyDataRedux.societyNo;

  const transform = (input: Payload): ReqBody => {
    const meetingDate = dayjs(input.meetingDate).format("YYYY-MM-DD");
    const meetingTime = dayjs(input.meetingTime).format("HH:mm:[00]");
    const meetingTimeTo = dayjs(input.meetingTimeTo).format("HH:mm:[00]");
    const GISInformation = `${input.meetingLocationLatitude}; ${input.meetingLocationLongitude}`;

    return {
      ...input,
      societyId,
      society_no,
      meetingDate,
      meetingTime,
      meetingTimeTo,
      GISInformation
    } as unknown as ReqBody
  }

  return {
    transform
  }
}

function useInitialValue<
  ResponseBody extends MeetingCreateSocietyRegistrationCreateOrUpdateResponseBodyGet = MeetingCreateSocietyRegistrationCreateOrUpdateResponseBodyGet,
  DocList extends DocumentByParamResponseBodyGet = DocumentByParamResponseBodyGet,
  Payload extends MeetingCreateSocietyRegistrationCreateOrUpdateFormPayload = MeetingCreateSocietyRegistrationCreateOrUpdateFormPayload,
>() {
  const userData = useSelector(getUserDetails)
  const { getFileFromURL } = useFileManagement()

  const getInitialValue = async (input: ResponseBody | null = null, docLists: DocList[] = []): Promise<Payload> => {
    if (input === null) {
      return {
        id: null,
        meetingMethod: "",
        meetingType: MeetingTypeOption[0].value,
        meetingPlace: "",
        meetingPurpose: "",
        platformType: "",
        meetingDate: null,
        meetingTime: null,
        meetingTimeTo: null,
        GISInformation: "",
        meetingAddress: "",
        state: null,
        district: "",
        city: "",
        postcode: "",
        totalAttendees: 7,
        meetingContent: "",
        openingRemarks: "",
        mattersDiscussed: "",
        othersDiscussionRemarks: "",
        closingRemarks: "",
        providedBy: userData?.name ?? "",
        confirmBy: "",
        meetingMinute: "",
        meetingMinuteAttachment: null,
        icNo: 123456789,
        present: 0,
        createdBy: "admin",
        modifiedBy: "admin",
        memberAttendances: [],
        meetingLocationLatitude: 2.745564,
        meetingLocationLongitude: 101.707021,
        agreeToTheStatement: false,
      } as unknown as Payload
    }
    const { GISInformation, ...otherInput } = input
    const meetingMinuteAttachment = docLists?.[0]?.url
      ? await getFileFromURL(docLists[0].url, otherInput.meetingMinute)
      : null

    const [meetingLocationLatitude, meetingLocationLongitude] = GISInformation.length > 0
      ? GISInformation.split("; ").map((val) => parseFloat(val))
      : [2.745564, 101.707021]
    const meetingDate = dayjs(otherInput.meetingDate, "YYYY-MM-DD").toDate()
    const meetingTime = dayjs(`${otherInput.meetingDate} ${otherInput?.meetingTime ?? "00:00"}`, "YYYY-MM-DD HH:mm:[00]").toDate()
    const meetingTimeTo = dayjs(`${otherInput.meetingDate} ${otherInput?.meetingTimeTo ?? "00:00"}`, "YYYY-MM-DD HH:mm:[00]").toDate()
    return {
      ...otherInput,
      meetingLocationLatitude,
      meetingLocationLongitude,
      meetingDate,
      meetingTime,
      meetingTimeTo,
      meetingMinuteAttachment,
      agreeToTheStatement: true
    } as unknown as Payload
  }

  return {
    getInitialValue
  }
}

export interface DocumentByParamResponseBodyGet {
  id: string | number
  name: string
  url: string
  type: string
}

export const CreateMesyuarat = <
  ResponseBody extends MeetingCreateSocietyRegistrationCreateOrUpdateResponseBodyGet = MeetingCreateSocietyRegistrationCreateOrUpdateResponseBodyGet,
  Payload extends MeetingCreateSocietyRegistrationCreateOrUpdateFormPayload = MeetingCreateSocietyRegistrationCreateOrUpdateFormPayload,
  ReqBody extends MeetingCreateSocietyRegistrationCreateOrUpdateRequestBody = MeetingCreateSocietyRegistrationCreateOrUpdateRequestBody,
  DocList extends DocumentByParamResponseBodyGet = DocumentByParamResponseBodyGet
>() => {
  const { t } = useTranslation();
  const [activeStep] = useState(1);
  const [initialValue, setInitialValue] = useState<Payload>({} as Payload);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [meetingBySocietyIdData, setMeetingBySocietyIdData] = useState<ResponseBody | null>(null);
  const [defaultInitialValue, setDefaultInitialValue] = useState<Payload>(null!);

  const { getValidationSchema } =
    useFormManagementMeetingCreateBySocietyIdValidationSchema({
      withObjectWrapper: false
    });

  const [params] = useSearchParams();
  const encodedId = params.get("id") ?? null;
  const decodedId = encodedId !== null ? atob(encodedId) : null;
  const validationSchema = object({
    ...getValidationSchema() as any,
    agreeToTheStatement: boolean()
      .isTrue()
      .required()
  })
    .required()

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const {
    refetch: getMeetingBySocietyId,
    isLoading: isLoadingMeetingBySocietyIdResponse
  } = useQuery<{ data: ResponseBody[] | null }>({
    url: `society/meeting/findBySocietyId/${decodedId!}`,
    onSuccess: async (response) => {
      const meetingBySocietyIdDataInitial = !response?.data?.data
        ? null
        : (response?.data?.data ?? []);
      const meetingBySocietyIdData = meetingBySocietyIdDataInitial === null
        ? null
        : meetingBySocietyIdDataInitial[meetingBySocietyIdDataInitial.length - 1];
      setMeetingBySocietyIdData(meetingBySocietyIdData);
      if (meetingBySocietyIdData === null) {
        const initialValue = await getInitialValue(meetingBySocietyIdData)
        setInitialValue(initialValue);
      } else {
        await getDocuments(meetingBySocietyIdData.id!);
      }
    }
  });
  const { getInitialValue } = useInitialValue<ResponseBody, DocList, Payload>();
  const { transform } = useTransform<Payload, ReqBody>();
  const {
    data: documentData,
    refetch: refetchDocuments,
    isLoading: isLoadingDocuments,
  } = useQuery<{ data: DocList[] }>({
    url: "document/documentByParam",
    autoFetch: false,
    onSuccess: async (response) => {
      const initialValue = await getInitialValue(meetingBySocietyIdData, response?.data?.data ?? [])
      setInitialValue(initialValue);
    }
  });

  const getDocuments = async (meetingId: string | number) =>
    await refetchDocuments({
      filters: [
        {
          field: "type",
          operator: "eq",
          value: DocumentUploadType.MEETING,
        },
        {
          field: "societyId",
          operator: "eq",
          value: societyDataRedux?.id,
        },
        {
          field: "meetingId",
          operator: "eq",
          value: meetingId,
        },
      ]
    });

  const handleSubmit = async (initialPayload: Payload, { setSubmitting }: FormikHelpers<Payload>) => {
    setSubmitting(true);
    try {
      const reqBody = transform(initialPayload);
      if (initialPayload.id !== null) {
        await updateMeeting(reqBody);
        if (initialValue.meetingMinuteAttachment?.name !== reqBody.meetingMinuteAttachment?.name || (initialValue.meetingMinuteAttachment?.size !== reqBody.meetingMinuteAttachment?.size)) {
          await handleDeleteFile(uploadedFiles[0].id);
          await executeUpload({
            file: reqBody.meetingMinuteAttachment!,
            meetingId: reqBody.id!,
          });
        }
      } else {
        const responseCreate = await createMeeting(reqBody);
        await executeUpload({
          file: reqBody.meetingMinuteAttachment!,
          meetingId: responseCreate.data.data
        });
      }
    } finally {
      setSubmitting(false);
      await getMeetingBySocietyId();
    }
  }

  const executeUpload = async (options: { file: File; meetingId: string | number }) => {
    const { file, meetingId } = options;
    await upload({
      file,
      params: {
        name: file.name,
        societyId: societyDataRedux.id,
        societyNo: societyDataRedux.societyNo,
        icNo: societyDataRedux.identificationNo,
        meetingId,
        type: DocumentUploadType.MEETING
      },
    });
  }
  const {
    fetchAsync: createMeeting,
  } = useMutation<{ data: string }>({
    url: "society/meeting/create",
  })
  const { fetchAsync: updateMeeting } =
    useMutation({
      url: "society/meeting/update",
      method: "put",
    });

  const {
    upload,
  } = useUploadPresignedUrl({
    showSuccessNotification: false,
    onSuccessUpload: () => {
      refetchDocuments();
    },
  });

  const { fetchAsync: deleteDocumentById } =
    useMutation({
      method: "put",
      onSuccessNotification: (data) => ({
        message: data?.data?.msg,
        type: "success",
      })
    });

  const handleDeleteFile = async (id?: string | number) => {
    if (!id) return;
    await deleteDocumentById(
      {},
      () => `document/deleteDocument?id=${id}`
    );
  };
  const loadDefaultInitialValue = async () => {
    const defaultInitialValue = await getInitialValue();
    setDefaultInitialValue(defaultInitialValue);
  }
  const societyDataRedux = useSelector(getSocietyDataRedux)!;

  useEffect(() => {
    setUploadedFiles(documentData?.data?.data ?? []);
  }, [documentData?.data?.data]);

  useEffect(() => {
    loadDefaultInitialValue();
  }, []);

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box sx={{ width: "55vw" }}>
        <LoadingOverlay isLoading={isLoadingMeetingBySocietyIdResponse || isLoadingDocuments} />
        <Formik<Payload>
          initialValues={initialValue}
          onSubmit={handleSubmit}
          validationSchema={validationSchema}
          enableReinitialize
        >
          <FormMeetingCreateSocietyRegistrationInner initialValue={defaultInitialValue} />
        </Formik>
      </Box>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <OrganizationStepper
          activeStep={activeStep}
          hidePayment={
            societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI
          }
        />

        {societyDataRedux.applicationStatusCode == ApplicationStatus.KUIRI ? (
          <Box
            sx={{
              padding: 3,
              backgroundColor: "white",
              borderRadius: "15px",
              maxHeight: "60vh",
              maxWidth: "18vw",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("kuiri")}
            </Typography>
            <Box
              sx={{
                padding: 3,
                backgroundColor: "#DADADA",
                borderRadius: "15px",
                maxHeight: "60vh",
                maxWidth: "18vw",
              }}
            >
              <Typography
                sx={{
                  mb: 8,
                  fontSize: "12px",
                  color: "#666666",
                  fontWeight: "500 !important",
                }}
              >
                {societyDataRedux.queryText}
              </Typography>
            </Box>
          </Box>
        ) : null}

        <InfoQACard />
      </Box>
    </Box>
  );
};

export default CreateMesyuarat;
