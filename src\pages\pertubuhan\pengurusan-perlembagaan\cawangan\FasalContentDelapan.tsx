import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import { formatAndValidateNumber, RegExNumbers, RegExText, yuranSeumurHidupText } from "@/helpers";
import AsalContent from "@/components/asalFasalContent";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";

interface FasalContentDelapanCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: [];
  name: string;
}

export const FasalContentDelapanCawangan: React.FC<
  FasalContentDelapanCawanganProps
> = ({ activeStep, setActiveStep, clause, asalData, name }) => {
  const [asal, setAsal] = useState<any>(null);
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [yuranKemasukan, setYuranKemasukan] = useState("");
  const [yuranKemasukanKata, setYuranKemasukanKata] = useState("");
  const [jumlahYuran, setJumlahYuran] = useState("");
  const [jumlahYuranKata, setJumlahYuranKata] = useState("");
  const [yuranSeumurHidup, setYuranSeumurHidup] = useState("");
  const [yuranSeumurHidupKata, setYuranSeumurHidupKata] = useState("");
  const [jenisYuran, setJenisYuran] = useState(t("monthly"));
  const [tempohPembayaran, setTempohPembayaran] = useState("");
  const [ketetapanPembayaran, setKetetapanPembayaran] = useState(t("month"));
  const [tempohMaksimum, setTempohMaksimum] = useState(t("month"));
  const [sumbangan, setSumbangan] = useState("");
  const [aktivitiEkonomi, setAktivitiEkonomi] = useState("");
  const [bilangan, setBilangan] = useState("6");
  const [isShowYuranSeumurHidup,setIsShowYuranSeumurHidup] = useState("default");
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [clauseContentDescription, setClauseContentDescription] = useState<string|undefined>("");
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const { id, clauseId } = useParams();
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName } = responseData?.data?.data;
        setNamaPertubuhan(societyName);
      },
    },
  });

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!yuranKemasukan) {
      errors.yuranKemasukan = t("fieldRequired");
    }

    if (!yuranKemasukanKata) {
      errors.yuranKemasukanKata = t("fieldRequired");
    }

    if (!jumlahYuran) {
      errors.jumlahYuran = t("fieldRequired");
    }

    if (!jumlahYuranKata) {
      errors.jumlahYuranKata = t("fieldRequired");
    }

    if (!jenisYuran) {
      errors.jenisYuran = t("fieldRequired");
    }

    if (!tempohPembayaran) {
      errors.tempohPembayaran = t("fieldRequired");
    }

    if (tempohPembayaran && Number(tempohPembayaran) > 31) {
      errors.tempohPembayaran = t("nomorethan31");
    }

    if (!ketetapanPembayaran) {
      errors.ketetapanPembayaran = t("fieldRequired");
    }

    if (!tempohMaksimum) {
      errors.tempohMaksimum = t("fieldRequired");
    }

    return errors;
  };

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent);
        setClauseContentId(clause.clauseContentId);
      }
      if(clause.clauseDescription){
        setClauseContentDescription(clause.clauseDescription);
      }else{
        setClauseContentDescription(undefined);
      }
      //setNamaPertubuhan(clause.societyName);
      setYuranKemasukan(clause.constitutionValues[0]?.definitionName);
      setYuranKemasukanKata(clause.constitutionValues[1]?.definitionName);
      setJumlahYuran(clause.constitutionValues[2]?.definitionName);
      setJumlahYuranKata(clause.constitutionValues[3]?.definitionName);
      setYuranSeumurHidup(clause.constitutionValues[4]?.definitionName);
      setYuranSeumurHidupKata(clause.constitutionValues[5]?.definitionName);
      setJenisYuran(clause.constitutionValues[6]?.definitionName);
      setTempohPembayaran(clause.constitutionValues[7]?.definitionName);
      setKetetapanPembayaran(clause.constitutionValues[8]?.definitionName);
      setBilangan(clause.constitutionValues[9]?.definitionName);
      setTempohMaksimum(clause.constitutionValues[10]?.definitionName);
      setSumbangan(clause.constitutionValues[11]?.definitionName);
      setAktivitiEkonomi(clause.constitutionValues[12]?.definitionName);
      setIsEdit(clause?.clauseDescription ? true : clause.edit);
      setAsal(
        asalData.find((item: any) => item.clauseNo === clause.clauseNo
        ) || ""
      );
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);

  let clauseContent = clauseContentDescription ? clauseContentDescription : clause.clauseContent; 
  clauseContent = clauseContent.replaceAll(
    /<<bayaran masuk>>/gi,
    `<b>${yuranKemasukan || "<<bayaran masuk>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bayaran masuk-dalam perkataan>>/gi,
    `<b>${yuranKemasukanKata || "<<bayaran masuk-dalam perkataan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<amaun yuran>>/gi,
    `<b>${jumlahYuran || "<<amaun yuran>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<amaun yuran-dalam perkataan>>/gi,
    `<b>${jumlahYuranKata || "<<amaun yuran-dalam perkataan>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${tempohPelucutanWaktu || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<yuran seumur hidup>>/gi,
    `<b>${yuranSeumurHidup || "<<yuran seumur hidup>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahPengerusi || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<jawatan Timbalan Pengerusi>>/gi,
    `<b>${yuranSeumurHidupKata || "<<jawatan Timbalan Pengerusi>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis yuran>>/gi,
    `<b>${jenisYuran || "<<jenis yuran>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh bayaran yuran>>/gi,
    `<b>${tempohPembayaran || "<<tempoh bayaran yuran>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<ketetapan bayaran yuran>>/gi,
    `<b>${ketetapanPembayaran || "<<ketetapan bayaran yuran>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<maksimum tempoh tunggakan>>/gi,
    `<b>${tempohMaksimum || "<<maksimum tempoh tunggakan>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahBendahari || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<sumbangan>>/gi,
    `<b>${sumbangan || "<<sumbangan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kegiatan ekonomi>>/gi,
    `<b>${aktivitiEkonomi || "<<kegiatan ekonomi>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bilangan maksimum tempoh tunggakkan>>/gi,
    `<b>${bilangan || "<<bilangan maksimum tempoh tunggakkan>>"}</b>`
  );
  const hasYuranSemurHidup = clauseContent.includes(yuranSeumurHidupText);
 
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");
 
  const { data: clauseValueData } = useCustom({
    url: `${API_URL}/society/constitutionvalue/getAll`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: id,
        clauseContentId: 22,
        clauseNo: 4,
        amendmentId: amendmentId,
      },
    },
    queryOptions: {
      enabled: !!id,
      onSuccess: (responseData) => {
        const data = responseData?.data?.data?.data;
        if(data){
          setIsShowYuranSeumurHidup(data[9].definitionName)
        }
      },
    },
  }); 

  return (
    <>
      <AsalContent
        asalData={asal?.description || ""}
        clauseId={clauseId}
        name={asal?.clauseName}
      />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bayaranMasuk")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("bayaranMasuk")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              required
              placeholder="RM 0.00"
              disabled={isViewMode}
              value={yuranKemasukan}
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setYuranKemasukan(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranKemasukan: "",
                  }));
                } else {
                  setYuranKemasukan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranKemasukan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.yuranKemasukan}
              helperText={formErrors.yuranKemasukan}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              required
              disabled={isViewMode}
              placeholder="contoh : dua puluh"
              value={yuranKemasukanKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setYuranKemasukanKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranKemasukanKata: "",
                  }));
                } else {
                  setYuranKemasukanKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranKemasukanKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.yuranKemasukanKata}
              helperText={formErrors.yuranKemasukanKata}
            />
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("typeOfFee")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("typeOfFee")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth required error={!!formErrors.jenisYuran}>
              <Select
                size="small"
                value={jenisYuran}
                disabled={isViewMode}
                displayEmpty
                onChange={(e) => {
                  setJenisYuran(e.target.value as string);
                  if (e.target.value == t("monthly")) {
                    setKetetapanPembayaran(t("month"));
                    setTempohMaksimum(t("month"));
                    setBilangan("6");
                  } else if (e.target.value == t("annual")) {
                    setKetetapanPembayaran(t("year"));
                    setTempohMaksimum(t("year"));
                    setBilangan("3");
                  }
                  setFormErrors((prev) => ({
                    ...prev,
                    jenisYuran: "",
                  }));
                  setFormErrors((prev) => ({
                    ...prev,
                    jenisYuran: "",
                  }));
                }}
              >
                <MenuItem value={t("monthly")}>{t("monthly")}</MenuItem>
                <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
              </Select>
              {formErrors.jenisYuran && (
                <FormHelperText>{formErrors.jenisYuran}</FormHelperText>
              )}
            </FormControl>
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("feeAmount")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              placeholder="RM 0.00"
              value={jumlahYuran}
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setJumlahYuran(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahYuran: "",
                  }));
                } else {
                  setJumlahYuran("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahYuran: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.jumlahYuran}
              helperText={formErrors.jumlahYuran}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("writeInWords")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              disabled={isViewMode}
              fullWidth
              required
              placeholder="contoh : dua puluh"
              value={jumlahYuranKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setJumlahYuranKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahYuranKata: "",
                  }));
                } else {
                  setJumlahYuranKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    jumlahYuranKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.jumlahYuranKata}
              helperText={formErrors.jumlahYuranKata}
            />
          </Grid>
        </Grid>
      </Box>
       {hasYuranSemurHidup ? <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("lifetimeFee")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("lifetimeFee")} </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              placeholder="RM 0.00"
              value={yuranSeumurHidup}
              onChange={(e) => {
                const formattedValue = formatAndValidateNumber(e.target.value);
                if (formattedValue !== null) {
                  setYuranSeumurHidup(formattedValue);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranSeumurHidup: "",
                  }));
                } else {
                  setYuranSeumurHidup("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranSeumurHidup: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.yuranSeumurHidup}
              helperText={formErrors.yuranSeumurHidup}
            />
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("writeInWords")} </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              placeholder="contoh : dua puluh"
              required
              value={yuranSeumurHidupKata}
              onChange={(e) => {
                if (RegExText.test(e.target.value)) {
                  setYuranSeumurHidupKata(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranSeumurHidupKata: "",
                  }));
                } else {
                  setYuranSeumurHidupKata("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    yuranSeumurHidupKata: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.yuranSeumurHidupKata}
              helperText={formErrors.yuranSeumurHidupKata}
            />
          </Grid>
        </Grid>
      </Box> : null}

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("others")}
        </Typography>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("feePaymentStipulation")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.ketetapanPembayaran}
            >
              <Select
                disabled={isViewMode}
                size="small"
                value={ketetapanPembayaran}
                displayEmpty
                onChange={(e) => {
                  setKetetapanPembayaran(e.target.value as string);
                  setFormErrors((prev) => ({
                    ...prev,
                    ketetapanPembayaran: "",
                  }));
                }}
              >
                <MenuItem value={t("month")}>{t("month")}</MenuItem>
                <MenuItem value={t("year")}>{t("year")}</MenuItem>
              </Select>
              {formErrors.ketetapanPembayaran && (
                <FormHelperText>
                  {formErrors.ketetapanPembayaran}
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("maximumPeriod")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              fullWidth
              disabled={isViewMode}
              required
              value={bilangan}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setBilangan(e.target.value);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilangan: "",
                  }));
                } else {
                  setBilangan("");
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    bilangan: "Invalid Value",
                  }));
                }
              }}
              error={!!formErrors.bilangan}
              helperText={formErrors.bilangan}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth required error={!!formErrors.tempohMaksimum}>
              <Select
                size="small"
                disabled={isViewMode}
                value={tempohMaksimum}
                displayEmpty
                onChange={(e) => {
                  setTempohMaksimum(e.target.value as string);
                  setFormErrors((prev) => ({
                    ...prev,
                    tempohMaksimum: "",
                  }));
                }}
              >
                <MenuItem value={t("month")}>{t("month")}</MenuItem>
                <MenuItem value={t("year")}>{t("year")}</MenuItem>
              </Select>
              {formErrors.tempohMaksimum && (
                <FormHelperText>{formErrors.tempohMaksimum}</FormHelperText>
              )}
            </FormControl>
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("feePaymentPeriod")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              size="small"
              type="number"
              disabled={isViewMode}
              fullWidth
              required
              value={tempohPembayaran}
              onChange={(e) => {
                setTempohPembayaran(e.target.value as string);
                if (Number(e.target.value) > 31) {
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    tempohPembayaran: t("nomorethan31"),
                  }));
                  return;
                }
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  tempohPembayaran: "",
                }));
              }}
              error={!!formErrors.tempohPembayaran}
              helperText={formErrors.tempohPembayaran}
              inputProps={{
                max: 31,
                min: 1,
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth required>
              <Select
                disabled
                size="small"
                defaultValue={t("day")}
                value={t("day")}
                displayEmpty
              >
                <MenuItem value={t("day")}>{t("day")}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("contribution")}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <RadioGroup
              row
              value={sumbangan}
              onChange={(e) => setSumbangan(e.target.value)}
            >
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  disabled={isViewMode}
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("contributionOption")}
                  control={<Radio />}
                  label={t("contributionOption")}
                />
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  disabled={isViewMode}
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("none")}
                  control={<Radio />}
                  label={t("none")}
                />
              </Grid>
            </RadioGroup>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("economicActivity")}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <RadioGroup
              row
              value={aktivitiEkonomi}
              onChange={(e) => setAktivitiEkonomi(e.target.value)}
            >
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  disabled={isViewMode}
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("economicActivityOption")}
                  control={<Radio />}
                  label={t("economicActivityOption")}
                />
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControlLabel
                  sx={{
                    "&.MuiFormControlLabel-root .MuiFormControlLabel-label": {
                      fontSize: "14px !important",
                      color: "#666666",
                    },
                  }}
                  value={t("none")}
                  control={<Radio />}
                  label={t("none")}
                />
              </Grid>
            </RadioGroup>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}
      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              const errors = validateForm();
              if (Object.keys(errors).length > 0) {
                setFormErrors(errors);
                return;
              }

              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: yuranKemasukan,
                    titleName: "Bayaran Masuk",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: yuranKemasukanKata,
                    titleName: "Bayaran Masuk Dalam Perkataan",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahYuran,
                    titleName: "Amaun Yuran",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahYuranKata,
                    titleName: "Amaun Yuran Dalam Perkataan",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: yuranSeumurHidup,
                    titleName: "Yuran Seumur Hidup",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: yuranSeumurHidupKata,
                    titleName: "Yuran Seumur Hidup Dalam Perkataan",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jenisYuran,
                    titleName: "Jenis Yuran",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tempohPembayaran,
                    titleName: "Tempoh Pembayaran Yuran",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: ketetapanPembayaran,
                    titleName: "Ketetapan Pembayaran Yuran",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bilangan,
                    titleName: "bilangan maksimum tempoh tunggakkan",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tempohMaksimum,
                    titleName: "Maksimum Tempoh Tunggakan Yuran",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: sumbangan,
                    titleName: "Sumbangan",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: aktivitiEkonomi,
                    titleName: "Kegiatan Ekonomi",
                  },
                ],
                clause: "clause8",
                clauseCount: 8,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentDelapanCawangan;
