import React from "react";
import type { TFunction } from "i18next";
import Box from "@mui/material/Box";
import { Grid, TextField, Typography } from "@mui/material";
import Input from "@/components/input/Input";
import {
  Control,
  Controller,
  FieldValues,
  UseFormGetValues,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { useSelector } from "react-redux";
import { formatAndValidateNumber } from "@/helpers";

export const CreatePernyataanPendapatanPembelajaan: React.FC<{
  t: TFunction;
  control: Control<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  checked: boolean;
  isAccessible?: boolean;
}> = ({ t, control, setValue, getValues, checked, isAccessible }) => {
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);

  const renderInputGroup = (
    title: string,
    items: { label: string; variable: string }[]
  ) => (
    <>
      <Box
        sx={{
          background: "white",
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={4}>
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontSize: "14px",
                fontWeight: "500 !important",
              }}
            >
              {t("jumlahPerbelanjaan")}
            </Typography>
          </Grid>
          <Grid item xs={8}>
            {items.map((item, index) => (
              <Controller
                name={item.variable}
                control={control}
                key={index}
                defaultValue={getValues(item.variable)} // Default value or empty string
                render={({ field }) => (
                  <TextField
                    size="small"
                    fullWidth
                    disabled={checked || !isAccessible}
                    value={getValues(item.variable)}
                    placeholder="0"
                    onChange={(e) => {
                      const formattedValue = formatAndValidateNumber(
                        e.target.value
                      );
                      if (formattedValue !== null) {
                        field.onChange(formattedValue);
                        setValue("totalExpense", formattedValue);
                      }
                    }}
                  />
                )}
              />
            ))}
          </Grid>
        </Grid>
      </Box>
    </>
  );

  return (
    <>
      {/* {renderInputGroup("operatingExpenses", [
        { label: "fundraisingExpenses", variable: "taxExpense" },
        { label: "tax", variable: "taxExpense" },
        { label: "others", variable: "sumbExpense" },
        { label: "welfareExpenses", variable: "zakatWelfare" },
        { label: "generalCharity", variable: "generalWelfare" },
        { label: "deathCharity", variable: "deathWelfare" },
        { label: "giftsPresents", variable: "giftWelfare" },
        { label: "scholarship", variable: "scholarshipWelfare" },
      ])}

      {renderInputGroup("activityFundraisingExpenses", [
        {
          label: "activityOrganizationExpenses",
          variable: "organizedActivity",
        },
        { label: "organizationActivityPromotion", variable: "promoActivity" },
        { label: "entertainment", variable: "banquetActivity" },
        { label: "visitTourFamilyDay", variable: "tourActivity" },
        { label: "investmentExpenses", variable: "investmentActivity" },
        { label: "participationFees", variable: "feeActivity" },
        { label: "others", variable: "otherActivity" },
      ])}

      {renderInputGroup("administrativeCosts", [
        { label: "allowancesSalariesWages", variable: "salaryCost" },
        { label: "rental", variable: "rentalCost" },
        { label: "utilities", variable: "utilityCost" },
        { label: "officeSupplies", variable: "supplyCost" },
        { label: "membershipCard", variable: "cardCost" },
        { label: "bonus", variable: "bonusCost" },
        { label: "epfSocso", variable: "kwspCost" },
        { label: "insurance", variable: "insuranceCost" },
        { label: "uniformClothes", variable: "uniformCost" },
        { label: "maintenance", variable: "maintenanceCost" },
        { label: "modification", variable: "renovationCost" },
        { label: "transportation", variable: "transportationCost" },
        { label: "photocopy", variable: "photocopyCost" },
        { label: "bankCharges", variable: "bankChargeCost" },
        { label: "others", variable: "otherCost" },
      ])}

      {renderInputGroup("otherExpenses", [
        { label: "otherExpenses", variable: "otherExpense" },
      ])} */}

      {renderInputGroup("", [
        { label: "jumlahPerbelanjaan", variable: "totalExpense" },
      ])}
    </>
  );
};

export default CreatePernyataanPendapatanPembelajaan;
