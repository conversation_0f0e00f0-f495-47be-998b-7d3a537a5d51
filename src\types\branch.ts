export interface BranchListResponseBodyGet {
  applicationExpirationDate: string
  applicationStatusCode: number
  address: string
  branchApplicationNo: string
  city: string
  createdDate: string
  districtCode: string
  icNo: string
  id: number
  isExtended: number
  modifiedDate: string
  name: string
  noPPMCawangan: string | null
  paymentDate: string
  paymentId: number
  /**
 * possible values:
 * KAUNTER
 */
  migrate: boolean | null
  paymentMethod: string
  submissionDate: string
  status: string
  stateCode: string
  societyNo: string
  societyId: string
  branchNo: string
  migrateStat?: number | null
  isOnGoingExtensionTime?: boolean
}
