import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { SingleFileInput } from "../../../../../components/input";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { useSelector } from "react-redux";
import { API_URL } from "../../../../../api";
import FileInput from "../../../../../components/input/FileInput";
import { FasalContentProps } from "../Fasal";
import { DocumentUploadType, useUploadPresignedUrl } from "@/helpers";
import CustomPopover from "@/components/popover";

interface FasalContentDelapanBelasProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentDelapanBelas: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [keteranganBendera, setKeteranganBendera] = useState("");
  const [bendera, setBendera] = useState("");
  const [keteranganLambang, setKeteranganLambang] = useState("");
  const [lambang, setLambang] = useState("");
  const [keteranganLencana, setKeteranganLencana] = useState("");
  const [lencana, setLencana] = useState("");
  const [fileUrl, setFileUrl] = useState("");
  const [currentUpload, setCurrentUpload] = useState("");

  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  //const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  useEffect(() => {
    if (!fileUrl) return;

    if (currentUpload === "bendera") {
      setBendera(fileUrl);
    } else if (currentUpload === "lambang") {
      setLambang(fileUrl);
    } else if (currentUpload === "lencana") {
      setLencana(fileUrl);
    }
  }, [fileUrl]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const { upload } = useUploadPresignedUrl({
    onSuccessUpload: (data) => {
      const fileUrl = data?.data?.data?.url;

      setFileUrl(fileUrl);
    },
  });

  const uploadedFilesRef = useRef<(File | null)[]>([null, null, null]);

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;
    const isDuplicate = uploadedFilesRef.current.some((existingFile, i) => {
      return (
        existingFile &&
        existingFile.name === file.name &&
        existingFile.size === file.size &&
        i !== index
      );
    });
    if (isDuplicate) {
      event.target.value = "";
      return;
    }
    uploadedFilesRef.current[index] = file;
    const name = event.target.name;
    setCurrentUpload(name);
    upload({
      params: {
        type: DocumentUploadType.SOCIETY,
        societyId,
      },
      file,
    });
  };

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      //const clause = JSON.parse(clause18);
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause12Data.clauseContent);
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause.societyName);
      setKeteranganBendera(clause.constitutionValues[0]?.definitionName);
      setBendera(clause.constitutionValues[1]?.definitionName);
      setKeteranganLambang(clause.constitutionValues[2]?.definitionName);
      setLambang(clause.constitutionValues[3]?.definitionName);
      setKeteranganLencana(clause.constitutionValues[4]?.definitionName);
      setLencana(clause.constitutionValues[5]?.definitionName);
      setIsEdit(clause.edit);
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  societyId;

  const { id } = useParams();

  const benderaFile = `${
    bendera ? `<img width="200" src="${bendera}" alt="Bendera" />` : ""
  }`;
  const lambangFile = `${
    lambang ? `<img width="200" src="${lambang}" alt="Lambang" />` : ""
  }`;
  const lencanaFile = `${
    lencana ? `<img width="200" src="${lencana}" alt="Lencana" />` : ""
  }`;

  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<file Bendera>>/gi,
    `<b>${benderaFile || "<<file Bendera>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<file Lambang>>/gi,
    `<b>${lambangFile || "<<file Lambang>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<file Lencana>>/gi,
    `<b>${lencanaFile || "<<file Lencana>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<Keterangan Bendera>>/gi,
    `<b>${keteranganBendera || "<<Keterangan Bendera>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<Keterangan Lambang>>/gi,
    `<b>${keteranganLambang || "<<Keterangan Lambang>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<Keterangan Lencana>>/gi,
    `<b>${keteranganLencana || "<<Keterangan Lencana>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("bendera")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
     <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}> 
                 <Typography sx={labelStyle}>{t("bendera")}</Typography>
                 <CustomPopover
                     customStyles={{ maxWidth: "450px" }}
                     content={
                       <>
                         <Typography
                           variant="body2"
                           sx={{ lineHeight: 1.5, color: "#666666" }}
                         >
                        Pertubuhan tidak dibenarkan untuk menggunakan lambang atau sebahagian daripada lambang berikut: 
                         <ol>
                           <li>Jata Negara dan Negeri</li>
                           <li>Bendera Malaysia</li>
                           <li>Bendera Negeri-Negeri</li>
                           <li>Lambang Kebesaran Istana</li>
                           <li>Lambang Agensi</li>
                           <li>Kalimah Allah</li>
                           <li>Gangsterism</li> 
                         </ol> 
                         </Typography>
                       </>
                     }
                   />
                </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <FileInput
              acceptedFormats={[".jpg", ".jpeg", ".png"]}
              buttonText="clickToUpload"
              id="bendera"
              name="bendera"
              value={bendera}
              currentIndex={0}
              uploadedFilesRef={uploadedFilesRef}
              onChange={(e) => handleFileChange(e, 0)}
              t={t}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography sx={labelStyle}>{t("keteranganBendera")}</Typography>
              <CustomPopover
                content={
                  <>
                    <Typography
                      variant="body2"
                      sx={{ lineHeight: 1.5, color: "#666666" }}
                    >
                      Memberi keterangan mengenai setiap reka bentuk dan warna
                      yang digunakan.
                    </Typography>
                  </>
                }
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              placeholder={`${t("keteranganBendera")}`}
              fullWidth
              required
              value={keteranganBendera}
              onChange={(e) => setKeteranganBendera(e.target.value)}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("lambang")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}> 
            <Typography sx={labelStyle}>{t("lambang")}</Typography>
            <CustomPopover
                customStyles={{ maxWidth: "450px" }}
                content={
                  <>
                    <Typography
                      variant="body2"
                      sx={{ lineHeight: 1.5, color: "#666666" }}
                    >
                   Pertubuhan tidak dibenarkan untuk menggunakan lambang atau sebahagian daripada lambang berikut: 
                    <ol>
                      <li>Jata Negara dan Negeri</li>
                      <li>Bendera Malaysia</li>
                      <li>Bendera Negeri-Negeri</li>
                      <li>Lambang Kebesaran Istana</li>
                      <li>Lambang Agensi</li>
                      <li>Kalimah Allah</li>
                      <li>Gangsterism</li> 
                    </ol> 
                    </Typography>
                  </>
                }
              />
           </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <FileInput
              acceptedFormats={[".jpg", ".jpeg", ".png"]}
              buttonText="clickToUpload"
              id="lambang"
              name="lambang"
              value={lambang}
              currentIndex={1}
              uploadedFilesRef={uploadedFilesRef}
              onChange={(e) => handleFileChange(e, 1)}
              t={t}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography sx={labelStyle}>{t("keteranganLambang")}</Typography>
              <CustomPopover
                content={
                  <>
                    <Typography
                      variant="body2"
                      sx={{ lineHeight: 1.5, color: "#666666" }}
                    >
                      Memberi keterangan mengenai setiap reka bentuk dan warna
                      yang digunakan
                    </Typography>
                  </>
                }
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              placeholder={`${t("keteranganLambang")}`}
              fullWidth
              required
              value={keteranganLambang}
              onChange={(e) => setKeteranganLambang(e.target.value)}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("lencana")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}> 
            <Typography sx={labelStyle}>{t("lencana")}</Typography>
            <CustomPopover
                customStyles={{ maxWidth: "450px" }}
                content={
                  <>
                    <Typography
                      variant="body2"
                      sx={{ lineHeight: 1.5, color: "#666666" }}
                    >
                   Pertubuhan tidak dibenarkan untuk menggunakan lambang atau sebahagian daripada lambang berikut: 
                    <ol>
                      <li>Jata Negara dan Negeri</li>
                      <li>Bendera Malaysia</li>
                      <li>Bendera Negeri-Negeri</li>
                      <li>Lambang Kebesaran Istana</li>
                      <li>Lambang Agensi</li>
                      <li>Kalimah Allah</li>
                      <li>Gangsterism</li> 
                    </ol> 
                    </Typography>
                  </>
                }
              />
           </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <FileInput
              acceptedFormats={[".jpg", ".jpeg", ".png"]}
              buttonText="clickToUpload"
              id="lencana"
              name="lencana"
              value={lencana}
              currentIndex={2}
              uploadedFilesRef={uploadedFilesRef}
              onChange={(e) => handleFileChange(e, 2)}
              t={t}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography sx={labelStyle}>{t("keteranganLencana")}</Typography>
              <CustomPopover
                content={
                  <>
                    <Typography
                      variant="body2"
                      sx={{ lineHeight: 1.5, color: "#666666" }}
                    >
                      Memberi keterangan mengenai setiap reka bentuk dan warna
                      yang digunakan.
                    </Typography>
                  </>
                }
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              placeholder={`${t("keteranganLencana")}`}
              fullWidth
              required
              value={keteranganLencana}
              onChange={(e) => setKeteranganLencana(e.target.value)}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
                whiteSpace: "pre-wrap",
                wordWrap: "break-word",
              }}
            >
              <ol style={{ listStyle: "none", paddingLeft: 0 }}>
                {clauseContent.split(/\n\n+/).map((item, index) => (
                  <li key={index}>
                    {item
                      //.replace(/^\d+\.\s*/, "")
                      .split("\n")
                      .map((line, i) => {
                        if (line.includes("img")) {
                          return (
                            <Typography
                              component="p"
                              sx={{
                                marginBottom: 1,
                                color: "#666666",
                                fontWeight: "400 !important",
                                textAlign: "center",
                              }}
                              dangerouslySetInnerHTML={{ __html: line }}
                            />
                          );
                        } else {
                          return (
                            <Typography
                              key={i}
                              sx={{
                                marginBottom: 1,
                                color: "#666666",
                                fontWeight: "400 !important",
                              }}
                              dangerouslySetInnerHTML={{
                                __html: `${line.trim()}`,
                              }}
                            ></Typography>
                          );
                        }
                      })}
                  </li>
                ))}
              </ol>
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            let checkAllImgUpload = 0;

            if (bendera && lambang && lencana) {
              checkAllImgUpload = 0;
            } else {
              checkAllImgUpload = 1;
            }
            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              hideConstitution: checkAllImgUpload,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: keteranganBendera,
                  titleName: "Keterangan Bendera",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bendera,
                  titleName: "Bendera",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: keteranganLambang,
                  titleName: "Keterangan Lambang",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: lambang,
                  titleName: "Lambang",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: keteranganLencana,
                  titleName: "Keterangan Lencana",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: lencana,
                  titleName: "Lencana",
                },
              ],
              clause: "clause18",
              clauseCount: 18,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentDelapanBelas;
