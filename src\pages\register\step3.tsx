import { useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import {
  ChevronLeftRounded,
  DoneRounded,
  Visibility,
  VisibilityOff,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { useRegister } from "@refinedev/core";
import { ButtonPrimary, ButtonText } from "@/components/button";

export const Step3 = ({
  IC,
  name,
  email,
  phoneNumber,
  idType,
  citizenshipTitle,
  onSubmit,
  onBack,
  onNext,
}: {
  IC: string;
  name: string;
  email: string;
  phoneNumber: string;
  idType: number;
  citizenshipTitle: number | undefined;
  onSubmit: () => void;
  onBack: () => void;
  onNext: () => void;
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { mutate: register } = useRegister();

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [isVerified, setIsVerified] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm({
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  const password = watch("password");
  const confirmPassword = watch("confirmPassword");

  const calculatePasswordStrength = (password: string) => {
    if (!password) return 0;

    let score = 0;
    // Check length
    if (password.length >= 8) score += 25;
    // Check for numbers
    if (/\d/.test(password)) score += 25;
    // Check for lowercase letters
    if (/[a-z]/.test(password)) score += 25;
    // Check for uppercase letters or special characters
    if (/[A-Z]/.test(password) || /[!@#$%^&*(),.?":{}|<>]/.test(password))
      score += 25;

    return score;
  };

  function getGenderFromIC(ic: string): "L" | "P" {
    const lastDigit = parseInt(ic.slice(-1), 10);
    return lastDigit % 2 === 0 ? "P" : "L";
  }

  const onSubmitForm = (data: {
    password: string;
    confirmPassword: string;
  }) => {
    // const registrationData = JSON.parse(
    //   localStorage.getItem("registrationData") || "{}"
    // );
    const finalData = {
      identificationNo: IC,
      name: name,
      contact: phoneNumber,
      email: email,
      password: data.password,
      identificationType: idType,
      citizenshipTitle: citizenshipTitle,
      gender:
        Number(idType) === 1 || Number(idType) === 4
          ? getGenderFromIC(IC)
          : null,
      digitalIdUser: false,
    };

    console.log("finalData", finalData);

    register(finalData, {
      onSuccess: (response) => {
        if (response.success) {
          setIsVerified(true);
          localStorage.removeItem("registrationData"); // Clear temporary data
        }
        onNext();
      },
      onError: (error) => {
        console.error("Registration failed:", error);
      },
    });
  };

  const handleLoginRedirect = () => {
    navigate("/login");
  };

  const textFieldStyle = {
    "& .MuiOutlinedInput-root": {
      backgroundColor: "white", // Add white background to the input area
      borderRadius: "10px",
      "& fieldset": {
        borderColor: "rgba(0, 0, 0, 0.23)",
        borderRadius: "10px",
      },
      "&:hover fieldset": {
        borderColor: "rgba(0, 0, 0, 0.87)",
      },
      "&.Mui-focused fieldset": {
        borderColor: "#147C7C",
      },
    },
    "& .MuiInputBase-input": {
      fontFamily: "Poppins, sans-serif !important", // Add !important to ensure it overrides any other styles
      padding: "8px 14px",
    },
    // Add this line to force Poppins on all child elements of the TextField
    "& *": { fontFamily: "Poppins, sans-serif !important" },
  };

  return (
    <Box>
      {/* Password content */}
      {isVerified ? (
        <Box
          sx={{
            display: "grid",
            justifyItems: "left",
          }}
        >
          <Typography className="title-login">{t("password")}</Typography>
          <Box
            sx={{
              display: "grid",
              gap: 2,
              mt: { sm: 2, md: 3, lg: 6, xl: 12 },
              justifyItems: "center",
            }}
          >
            <Box
              sx={{
                width: 42,
                height: 42,
                bgcolor: "var(--primary-color)",
                borderRadius: "50%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <DoneRounded sx={{ color: "#fff", fontSize: 25 }} />
            </Box>
            <Typography
              sx={{
                fontFamily: "Poppins, sans-serif",
                color: "#55556D",
                textAlign: "center",
              }}
            >
              {t("passwordSettingSuccessful")}
            </Typography>
            <ButtonPrimary
              onClick={handleLoginRedirect}
              fullWidth
              className="btn-login"
              sx={{ mt: { sm: 2, md: 3, lg: 6, xl: 12 } }}
            >
              {t("clickToLogin")}
            </ButtonPrimary>
          </Box>
        </Box>
      ) : (
        <Box
          sx={{
            display: "grid",
            pl: { xs: 2, sm: 6, md: 6 },
            pr: { xs: 2, sm: 6, md: 6 },
          }}
          component="form"
          noValidate
          onSubmit={handleSubmit(onSubmitForm)}
        >
          <Typography
            sx={{ mb: { sm: 2, md: 3, lg: 3, xl: 6 } }}
            className="title-login"
          >
            {t("resetPasswordButton")}
          </Typography>
          <Controller
            name="password"
            control={control}
            rules={{
              required: t("fieldRequired"),
              minLength: {
                value: 8,
                message: t("passwordMinLength"),
              },
              pattern: {
                value:
                  /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{8,}$/,
                message: t("passwordPattern"),
              },
            }}
            render={({ field }) => (
              <>
                <Typography
                  variant="body1"
                  sx={{
                    fontFamily: "Poppins, sans-serif",
                    fontSize: "14px",
                    fontWeight: 400,
                    lineHeight: "21px",
                    textAlign: "left",
                    color: "#55556DA1",
                    mb: 0,
                  }}
                >
                  {t("password")}
                </Typography>
                <TextField
                  {...field}
                  required
                  fullWidth
                  type={showPassword ? "text" : "password"}
                  error={!!errors.password}
                  helperText={errors.password?.message as string}
                  onChange={(e) => {
                    field.onChange(e);
                    setPasswordStrength(
                      calculatePasswordStrength(e.target.value)
                    );
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    ...textFieldStyle,
                    mt: 0,
                    "& .MuiFormHelperText-root": {
                      fontSize: "12px",
                    },
                  }}
                  variant="outlined"
                />
                <Typography
                  variant="caption"
                  sx={{
                    color: "rgba(0, 0, 0, 0.6)",
                    display: "block",
                    fontSize: 11,
                    mt: 0.5,
                  }}
                >
                  {t("passwordRequirements2")}
                </Typography>
              </>
            )}
          />
          <Controller
            name="confirmPassword"
            control={control}
            rules={{
              required: t("fieldRequired"),
              validate: (value) =>
                value === password || t("passwordsMustMatch"),
            }}
            render={({ field }) => (
              <>
                <Typography
                  variant="body1"
                  sx={{
                    fontFamily: "Poppins, sans-serif",
                    fontSize: "14px",
                    fontWeight: 400,
                    lineHeight: "21px",
                    textAlign: "left",
                    color: "#55556DA1",
                    mb: 0,
                    mt: 2,
                  }}
                >
                  {t("PasswordConfirmation")}
                </Typography>
                <TextField
                  {...field}
                  required
                  fullWidth
                  type={showConfirmPassword ? "text" : "password"}
                  error={!!errors.confirmPassword}
                  helperText={errors.confirmPassword?.message as string}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() =>
                            setShowConfirmPassword(!showConfirmPassword)
                          }
                          edge="end"
                        >
                          {showConfirmPassword ? (
                            <VisibilityOff />
                          ) : (
                            <Visibility />
                          )}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    ...textFieldStyle,
                    mt: 0,
                    "& .MuiFormHelperText-root": {
                      fontSize: "12px",
                    },
                  }}
                  variant="outlined"
                />
              </>
            )}
          />
          <Box sx={{ mt: 2, mb: 2 }}>
            <Box sx={{ display: "flex", gap: 0.5 }}>
              {[20, 40, 60, 80, 100].map((threshold, index) => {
                const color =
                  passwordStrength >= threshold
                    ? passwordStrength < 40
                      ? "var(--error)"
                      : passwordStrength < 60
                      ? "#F39C12"
                      : passwordStrength < 80
                      ? "#82C341"
                      : "#2ECC71"
                    : "var(--text-grey-disabled)";

                return (
                  <Box
                    key={index}
                    sx={{
                      flex: 1,
                      height: 5,
                      borderRadius: "3px",
                      backgroundColor: color,
                    }}
                  />
                );
              })}
            </Box>
            <Typography
              className="error-text-login"
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                color:
                  passwordStrength < 20
                    ? "var(--text-grey)"
                    : passwordStrength < 40
                    ? "var(--error)"
                    : passwordStrength < 60
                    ? "#F39C12"
                    : passwordStrength < 80
                    ? "#82C341"
                    : "#2ECC71",
              }}
            >
              {passwordStrength < 20 && t("veryWeak")}
              {passwordStrength >= 20 && passwordStrength < 40 && t("weak")}
              {passwordStrength >= 40 && passwordStrength < 60 && t("fair")}
              {passwordStrength >= 60 && passwordStrength < 80 && t("good")}
              {passwordStrength >= 80 && t("strong")}
            </Typography>
          </Box>

          {password !== confirmPassword && confirmPassword && (
            <Typography
              variant="body2"
              color="error"
              sx={{
                mt: 1,
                textAlign: "center",
                fontSize: "12px",
                fontFamily: "Poppins, sans-serif",
              }}
            >
              {t("passwordsDoNotMatch")}
            </Typography>
          )}

          <ButtonPrimary
            type="submit"
            fullWidth
            disabled={password == "" || password !== confirmPassword}
            className="btn-login"
            sx={{
              mt: 10,
              mb: 6,
            }}
          >
            {t("Continue")}
          </ButtonPrimary>
          <ButtonText
            onClick={onBack}
            className="label"
            sx={{ textDecoration: "underline", color: "#666666B2", mr: 3 }}
            disableRipple
          >
            <ChevronLeftRounded /> {t("back")}
          </ButtonText>
        </Box>
      )}
    </Box>
  );
};
