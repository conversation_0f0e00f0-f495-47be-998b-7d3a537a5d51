{"acceptedPeriodDays": "Accepted Period (days)", "accessOrganizationActivities": "Access to all organization activities", "accessActivities": "Access to all activities", "accessOrganizationReport": "Access to all organization reports", "accountNumber": "Account number", "activity": "Activity", "accountsReceivable": "Accounts Receivable", "accruedTaxes": "Accrued Taxes", "activityFundraisingExpenses": "2.2 Expenditure activities/ generate funds", "activityOrganizationExpenses": "Organization activity expenses", "administrativeCosts": "2.3 Administrative Costs", "entertainment": "Entertainment", "uniformClothes": "Uniform/Clothes", "visitTourFamilyDay": "Visit/Tour/Family Day", "addCawangan": "Add Branch", "addContributionFromAbroad": "Add contribution from abroad", "addContributionToAbroad": "Add contribution to abroad", "addDocument": "Add Document", "addSupportingDocument": "Supporting Document", "address": "Address", "adakahAndaPastiUntukMenghantarPermohonanIni": "Are you sure you want to submit this application?", "adviser": "Adviser", "age": "Age", "agreementAcceptance": "Agreement Acceptance", "agreementText": "I hereby confirm that all the information provided is true. If the Department of Registration of Societies Malaysia finds fraud and falsehood in the documents I have given above, then the DEPARTMENT OF REGISTRATION OF SOCIETIES MALAYSIA is hereby entitled to reject my application and if found guilty, I may be fined not exceeding RM 2000 according to section 54 A, SOCIETIES ACT 1966", "agreementText2": "Committee members during their tenure are responsible for all reports and information while holding office.", "ahliJawatanKuasa": "Committee Member", "ajkCompletionNote": "*Please complete the AJK information for numbers 1-7 (MANDATORY)", "ajkCount": "Number of committee members: {{count}}", "ajkCountNonCitizen": "Number of Non-Citizen Committee Members: {{count}}", "ajkEligibilityCheck": "AJK Eligibility Check", "ajkEligibilityVerification": "AJK Eligibility Verification", "ajkInfoMessage": "Please ensure the number of Regular Committee Members follows the number in the constitution. For non-citizen committee members, please click.", "ajkInformation": "AJK Information", "ajkList": "AJK List", "ajkName": "AJK Name", "alamatPertubuhan": "Organization Address", "alamatPlaceholder": "Enter meeting place address", "alamatTempatMesyuarat": "Meeting Place Address", "alamatTempatUrusan": "Business Address", "allowancesSalariesWages": "Allowances, Salaries, Wages", "allowFinancialInfoDisplay": "I hereby allow this financial information to be displayed for public information.", "alreadyRegistered": "Already registered?", "allStates": "All States", "annual": "Annual", "annualMeetingInformation": "Annual Meeting Information", "annualMeetingSelectionNote": "Please select the annual statement presentation date in the dropdown below if there was a general meeting held this year. Fields marked with * are mandatory here.", "annualStatement": "Annual Statement", "annualStatementFrom": "Annual Statement From", "annualStatementTo": "Annual Statement To", "appointmentDate": "Appointment Date", "approvedNumberOfDays": "Approved Number of Days", "areYouSureContinueLiabilityRestriction": "Are you sure to continue $t(liabilityRestriction) to {{- name}}", "areYouSureContinueWhitelisting": "Are you sure to continue $t(whitelisting) to {{- name}}", "areYouSureFlowManagementTaskToAnotherCommittee_PENYATAAN_TAHUNAN": "Are you sure to flow the Annual Statement task to {{- name}}", "areYouSureFlowManagementTaskToAnotherCommittee_PERLEMBAGAAN": "Are you sure to flow the Constitutional Management task to {{- name}}", "areYouSureDeleteTrusteeRecord": "Are you sure to delete this trustee record?", "areYouSureYouWantToDeleteThisDraftComplaint": "Are You Sure You Want to Delete Draft Complaint {{- title}}", "areYouSureYouWantToResetTheInformationOnThisPage": "Are You Sure You Want to Reset the Information on This Page?", "areYouSureYouWantToSubmitThisComplaint": "Are you sure you want to submit this complaint?", "asistantSecretary": "Asistant Secretary", "asistantTreasurer": "Asistant Treasurer", "assets": "Assets", "assetsBuildingsPlantAndEquipment": "Buildings, plant, and equipment", "assetsAndLiabilities": "Assets and Liabilities", "assetsLiabilitiesInfo": "This information must be obtained from the verified Organization's Balance Sheet.", "assignAnOfficer": "Assign an Officer (RO/IO)", "associateMember": "Associate Member", "associateMemberFirstOption": "Open to those who do not meet the criteria for Regular Members. Can't vote and be voted on.", "associationIncome": "Association Income", "associationIncomeStatement": "Association Income Statement (RM)", "auditType": "Audit Type", "auditor": "Auditor", "auditorInfo": "Auditor Information", "auditorInformation": "Auditor Information", "auditorList": "Auditor List", "auditorName": "Auditor Name", "auditorType": "Auditor Type", "back": "Back", "ban": "Ban", "bandar": "City", "bandarPlaceholder": "Enter city name", "bankAccountInfoMandatory": "The organization's bank account information is mandatory. Please enter valid bank account information.", "bankCharges": "Bank Charges", "bankName": "Bank name", "bayaranOnlinePertubuhan": "Organization Online Payment", "bonus": "Bonus", "branchAddress": "Branch Address", "branchBusinessAddress": "$t(cawangan) $t(alamatTempatUrusan1)", "branchComplaint": "$t(cawangan) $t(complaint)", "branchCount": "Number of branches", "branchLiquidationInformation": "Branch Liquidation Information", "branchLevel": "Branch Level", "branchNumber": "No. Cawangan", "branchOrganization": "Branch Organization", "branchPublicOfficerInformations": "$t(cawangan) $t(publicOfficialsInformation)", "branchPropertyOfficerInformations": "$t(cawangan) $t(propertyOfficerInformation)", "branchState": "<PERSON><PERSON><PERSON>", "buildings": "Buildings", "businessAddress": "Business Address", "businessAddressLabel": "Business address", "businessOfficePhoneNumber": "Business Office Phone Number", "butiranAlasanPermohonanRayuan": "Details/Reasons for Appeal Application", "calendar": "Calendar", "calendarAllActivities": "All Activities", "calendarActionAddToGoogleCalendar": "Add to Google Calendar", "calendarActivityCollaboration": "Collaborations", "calendarActivityDescription": "Activity Description", "calendarActivityDetails": "Activity Details", "calendarActivityLists": "Activity Lists", "calendarActivityObjectives": "Activity Objectives", "calendarNextActivities": "Next Activities", "calendarThisMonth": "This Month", "calendarThisYear": "This Year", "calendarTitle": "JPPM Calendar Activities", "calendarSeeTheFullActivities": "See the full activities", "cancellation": "Cancellation", "cashInBank": "Cash in Bank", "cashInHand": "Cash in Hand", "cawanganMigrasi": "Migration Branch", "cawanganMigrasiInfo": "Please click the \"pencil\" icon to update the current Branch Secretary's name and address (year 2015). The Branch Secretary is responsible for submitting the Branch Annual Statement 2014.", "chairman": "Chairman", "charitySale": "Charity sale", "charitySaleIncome": "Charity sale", "checkBox": "Please verify the information on the content of the clause above", "checkTheConstitution": "Check the constitution", "citizenship": "Citizenship", "citizen": "Citizen", "city": "City", "civilServantRegistration": "Civil Servant Registration", "clause": "<PERSON>e", "clauseContent": "Clause Content", "clauseList": "Clause List", "clauseName": "Clause name", "clickHere": "here", "codeResent": "Code Resent", "committeeAndAuditorInformation": "Committee and Auditor Information", "committeeList": "Committee List", "committeMember": "Committee Member", "Committee updated successfully.": "Committee updated successfully.", "complainantDetails": "Complain<PERSON>", "complaint": "<PERSON><PERSON><PERSON><PERSON>", "complaintAction": "$t(complaint) Action", "complaintDecision": "$t(complaint) Decision", "complaintDetails": "$t(complaint) Details", "complaintHistory": "$t(complaint) History", "complaintInformation": "$t(complaint) $t(maklumat)", "complaintList": "$t(list) of $t(complaint)", "complaintOfOrganizationalMisconduct": "$t(complaint) of Organizational Misconduct", "complaintReferenceNumber": "$t(complaint) $t(referenceNumber)", "complaintSearch": "$t(complaint) Search", "complaintTopic": "$t(complaint) Topic", "complainantsName": "Complainant's Name", "complaintsBranch": "$t(complaint) Branch", "complete": "Complete", "confirmPassword": "Confirm New Password", "confirmPasswordPlaceholder": "Re-enter new password", "constitution": "Constitution", "constitutionalAmendment": "Constitutional Amendment", "constitutionDownload": "$t(download) $t(constitution)", "constitutionalInterpretation": "Constitutional Interpretation", "constitutionType": "Constitution type", "continueLiabilityRestrictionSuccess": "{{- name}} has been blacklisted and notice 49 has been successfully generated!", "contribution": "Contribution", "contributionFromAbroad": "Contribution from abroad", "contributionOption": "Organizations are allowed to receive donations in the form of money and goods. Donations in the form of money must be declared clearly and accurately in the Organization's Annual Statement.", "contributionProvider": "Contribution Provider", "contributionsFromAbroad": "Contributions From/To Abroad", "contributionToAbroad": "Contribution to abroad", "contributionToAbroadInformation": "Contribution to abroad information", "counter": "Counter", "country": "Country", "countryOfOrigin": "Country of Origin", "countryOfOriginRecipient": "Country of origin of recipient", "customerFeedback": "<PERSON><PERSON><PERSON>", "currentDate": "Current Date", "currentLiabilities": "Current liabilities", "currentInvestments": "Current Investments", "createAComplaint": "Create a Co<PERSON><PERSON>t", "creditors": "Creditors", "downloadTemplate": "Download Minute Template for reference", "daerah": "District", "daftarCawangan": "Register Branch", "dalamProsesPermohonanLanjutanMasa": "In process of TIME EXTENSION application", "dateApplicationRejected": "The date the application was rejected", "dateApplied": "Date applied", "dateFormatWithPrefix": "{{prefix}} {{dateFormat}}", "dateOfBirth": "Date of Birth", "dateOfBirthCapitalizedOnlyFirstLetter": "Date of birth", "dateOfCommencementOfTheOffence": "Date of Commencement of The Offence", "dateOfComplaint": "Date of Complaint", "dateOfOccurrence": "Date of Occurrence", "datePrefixOn": "[On]", "deathCharity": "Death Charity", "decisionMethod": "Decision Method", "deleteConfirmationMessage": "Are you sure you want to delete this application?", "deleteDocument": "Delete Document", "deleteInfo": "Delete information", "deleteOrgInfo": "Delete Organization Information", "deleteOrganizationInfo": "Delete Organization Info", "deletionWarning": "Your organization registration application will be deleted after 30 days from the start date of application. All your application information will be DELETED if it exceeds the registration application period.", "descent": "Descent", "dinnerEvent": "Dinner event", "dinnerEventIncome": "Dinner event", "disahkanOlehPengerusi": "Verified by Chairman", "disediakanOlehSetiausaha": "Prepared by Secretary", "displayFormattedDateTimeComplete": "{{date, dddd, D MMMM YYYY, h:mm A}}", "district": "District", "dividend": "Dividend", "dividendIncome": "Dividend income", "document": "Document", "documentName": "Document Name", "documentRemarks": "Document Remarks", "documentType": "$t(document) Type", "dokumenPermohonanRayuan": "Appeal Application Document", "donation": "Donation", "donationIncome": "Donation", "draft": "Draft", "draftComplaintSuccessfullyDeleted": "Draft Complaint {{- title}} Successfully Deleted!", "durationInMalaysia": "Duration in Malaysia", "economicActivity": "Economic activity", "economicActivityOption": "Organizations can carry out economic activities such as selling, buying, investing, renting, owning movable and immovable property as well as other economic activities. All money and profits obtained as a result of economic activities must be channeled back to the Organization to achieve the Organization's goals and cannot be used to pay benefits, profits or bonuses to any members of the Organization. However, this provision does not prevent any payment of salary or administrative expenses or both to any member or employee of the Society.", "electionOfAJK": "The selection of AJK is made in the General Meeting", "electionPeriod": "Committee election period", "eligibleVoterCount": "Number of members eligible to vote", "email": "Email", "emailPlaceholder": "Enter your email", "emailRequired": "This field is required", "emel": "Email", "emelPenerima": "Recipient's <PERSON><PERSON>", "emailWithoutDash": "Email", "employerAddress": "Employer Address", "employerInfo": "Employer Information", "employerInfoNote": "* Fill in if applicable", "employerName": "Employer Name", "endTime": "End Time", "enforcement": "Enforcement", "enforcement_management": "$t(enforcement) Management", "enterAjkName": "Enter AJK name", "enterAuditorName": "Enter Auditor Name", "enterMemberName": "Enter member name", "enterOrgName": "Please enter the organization name correctly.", "enterOrganizationName": "Enter organization name", "enterPropertyOfficerName": "Enter Property Officer Name", "enterPublicOfficerName": "Enter Public Officer Name", "enterTrusteeName": "Enter Trustee Name", "entranceFee": "Entrance fee", "entranceFeeIncome": "Entrance fee", "epfSocso": "EPF/SOCSO", "eRoses": "eRoses", "eROSES": "eROSES", "establishmentMeeting": "Establishment Meeting", "example": "Example", "expenditure": "Expenditure (RM)", "faxNumber": "Fax Number", "facebook": "Facebook", "feeAmount": "Fee amount", "feeManagement": "Fee Management", "feePaymentPeriod": "Fee payment period", "feePaymentStipulation": "Fee payment stipulation", "female": "Female", "filterButton": "Filter", "financialMangement": "Financial Management", "financialResources": "Financial Resources", "financialYearStart": "Financial year start", "financialYearStartsOn": "Financial Year Starts On", "firstGoals": "1. <goals 1>", "fixedAssets": "Fixed Assets", "fixedDepositInterest": "Fixed deposit interest", "fixedDepositInterestIncome": "Fixed deposit interest", "fixedDeposits": "Fixed Deposits", "flagEmblemBadges": "Flags, emblems, and badges", "forbidden": "Access Forbidden", "forbiddenMessage": "You don't have permission to access this resource. Please contact your administrator if you believe this is an error.", "forgotPassword": "Forgot password?", "forgotPasswordTitle": "Forgot Password", "fullName": "Full Name", "fullNameCapitalizedOnlyFirstLetter": "Full name", "fullNamePlaceholder": "Full name as in identification card", "fullNameRequired": "Full name is required", "fundraisingActivitiesIncome": "1.2 Fundraising Activities Income", "fundraisingActivitiesIncomeSection": "1.2 Fundraising Activities Income", "fundraisingExpenses": "Fundraising Expenses", "fundraisingIncome": "Fundraising", "furniture": "Furniture", "gazette": "Gazetting", "gender": "Gender", "generalCharity": "General Charity", "generalInformation": "General Information", "generalMeeting": "General Meeting", "giftsPresents": "Gifts/Presents", "github": "GitHub", "goals": "Goals", "goalsFormat": "Goals format:", "goHome": "Go Home", "governmentAgency": "Government agency", "governmentAgencyGrant": "Government agency grant", "grant": "<PERSON>", "grants": "1.4 Grants", "grantsSection": "1.4 Grants", "halHalLain": "Other Matters", "hantar": "Send", "hantarPermohonan": "Submit Application", "historyOfWhitening": "History of Whitening", "homeNumber": "Home Number", "honoraryMember": "Honorary Member", "honoraryMemberFirstOption": "Consists of those who have contributed to the community and can contribute to the Organization. They are determined by the committee and are exempt from paying fees.", "idNumber": "ID Number", "idNumberCapitalizedOnlyFirstLetter": "ID number", "idNumberPlaceholder": "Identification number", "idPlaceholder": "Registered ID Number", "idType": "Identification Type", "idTypeCapitalizedOnlyFirstLetter": "Identification type", "importanceOfPosition": "Briefly explain the importance of a non-citizen holding this position", "inactiveTaskFlowMemberList": "Inactive Task Flow Member List", "incidentDetails": "Incident Details", "inComplete": "Incomplete", "incomeStatement": "Income Statement", "incomeStatementInfo": "This information must be obtained from the Organization's Income Statement that has been verified in the Annual General Meeting or if no such general meeting was held in that year, it needs to be verified by the organization's auditor. The amount to be filled should be rounded to two decimal places, for example: RM 500.59 ~ RM 500.60", "incomeStatementInfo2": "<PERSON><PERSON><PERSON> yang perlu diisi hendaklah dibundarkan kepada dua tempat perpuluhan, contohnya: RM 500.59 ~ RM 500.60", "individual": "Individual", "individualGrant": "Individual grant", "individualReview": "$t(individual) Review", "inputValidationErrorDateAboveMax": "{{label}} must be at earlier than {{value}}", "inputValidationErrorDateBelowMin": "{{label}} must be later than {{value}}", "inputValidationErrorFileSizeExceedsLimit": "File size exceeds the limit of 25MB. Please upload a smaller file.", "inputValidationErrorFileSizeExceedsLimitDynamic": "File size exceeds the limit of {{limit}}. Please upload a smaller file.", "inputValidationErrorIdentityCardNumberAndNameAreNotInTheRecords": "Error: Identity Card Number and Name are not in the records of any organization", "inputValidationErrorNumberMustContainDigit": "{{label}} must contain {{digit}} digit.", "inputValidationErrorNumberMustContainDigit_plural": "{{label}} must contain {{digit}} digits.", "inputValidationErrorPleaseFillTheRequiredFields": "Please fill in the required fields.", "inputValidationErrorStringExceedsLimitWord": "{{label}} must not exceed {{value}} words", "inputValidationErrorTotalAttendeesBelowMin": "The organization must have at least {{value}} members present.", "inquiry": "Inquiry", "instagram": "Instagram", "instructionLetterReferenceNumber": "Instruction $t(letterReferenceNumber)", "inspection": "Inspection", "insurance": "Insurance", "intangibleAssets": "Intangible Assets", "integrationJPNActive": "The JPN integration has been opened and the checks involved with JPN are working.", "integrationJPNInactive": "The JPN integration has been closed and the checks involved with JPN are not working.", "integrationEPICActive": "EPIC integration has been opened and Online Payment Methods will be opened.", "integrationEPICInactive": "EPIC integration has been closed and Online Payment Methods will be closed.", "investigation": "Investigation", "investmentAssets": "Investment Assets", "investmentExpenses": "Investment Expenses", "investmentIncome": "1.3 Investment Income", "investmentIncomeSection": "1.3 Investment Income", "inventory": "Inventory", "isiMaklumatMesyuarat": "Meeting Details", "janaMinit": "Generate Minutes", "janaKodQR": "Generate QR Code", "jawatanDalamPertubuhan": "Position in Organization", "jawatanKuasaNotes": "*For committee members who are NOT WORKING/RETIRED, employer information does not need to be filled", "jemputanMesyuarat": "Meeting Invitation", "jenisMesyuarat": "Type of Meeting", "jenisPlatform": "Platform Type", "jenisPlatformPlaceholder": "Enter platform type", "jenisRayuan": "Type Of Appeal", "jppmUserCategoryCreatedSuccessfully": "Successfully added permissions for user role: {{name}}", "jppmUserCategoryUpdatedSuccessfully": "Successfully updated permissions for user role:  {{name}}", "jumlahKehadiran": "Attendance Count", "jumlahKehadiranPlaceholder": "Enter attendance count", "jumlahKehadiranAhliMesyuarat": "Number of members eligible to vote (Person)", "kaedahMesyuarat": "Meeting method", "kemaskiniMaklumatCawangan": "Update Branch Information", "kodQR": "QR Code", "land": "Land", "lanjutMasa": "More Time", "latestConstitution": "Latest Constitution", "letterReferenceNumber": "Letter $t(referenceNumber)", "liabilities": "Liabilities", "liabilityRestriction": "Liability Restrictions", "liabilityRestrictionCreate": "Create $t(liabilityRestriction)", "liabilityRestrictionInformation": "$t(liabilityRestriction) Information", "lihatSenaraiPenuh": "View Full List", "lifetimeFee": "Lifetime fee", "lifetimeMember": "Lifetime Member", "lifetimeMemberFirstOption": "Open to Regular Members who pay a lump sum fee.", "liquidation": "Liquidation", "liquidationInformation": "Liquidation Information", "liquidationWarning": {"main": "Please refer to the Dissolution Clause in your Organization's Constitution. If there is no dissolution provision in your Organization's Constitution, please comply with the following provisions:", "point1": "i-Dissolution is agreed upon by no less than 3/5 of the total registered members or 3/4 of the members for Mutual Benefit Organizations.", "point2": "ii-All valid debts and liabilities of the Organization according to the Constitution must be settled, and any remaining funds must be disposed of in a manner agreed upon at the relevant General Meeting.", "point3": "Please fill in the liquidation meeting details if the Meeting Type is not listed, or click <1>here</1> to enter the relevant meeting information.", "point4": "The method of settlement of the remaining assets must be in accordance with the decision of the general meeting of liquidation."}, "list": "List", "listOf": "List of", "listOfBannedNames": "$t(listOf) banned names", "listOfBranchCivilServants": "$t(listOf) branch civil servants", "listOfBranchCivilServantsAwaitingApproval": "$t(listOfBranchCivilServants) ($t(statusAwaitingApproval))", "listOfBranchPropertyOfficer": "$t(listOf) Branch Property officers", "listOfBranchPropertyOfficerAwaitingApproval": "$t(listOfBranchPropertyOfficer) ($t(statusAwaitingApproval))", "listOfContributionsFromAbroad": "$t(listOf) contributions from abroad", "listOfLiabilityRestriction": "$t(listOf) $t(liabilityRestriction)", "loans": "Loans", "localAuthority": "Local Authority", "login": "<PERSON><PERSON>", "loginButton": "<PERSON><PERSON>", "loginHere": "LOGIN", "loginTitle": "<PERSON><PERSON>", "logout": "Logout", "longTermDebt": "Long-term Debt", "longTermLiabilities": "Long-term Liabilities", "longTermLoans": "Long-term Loans", "meetingManagement": "Meeting Management", "machineryEquipment": "Machinery/Equipment", "mailingAddress": "Mailing Address", "maintenance": "Maintenance", "maklumatCawangan": "Branch Information", "maklumatMesyuarat": "Meeting Information", "maklumatMesyuaratPembubaranCawangan": "$t(cawangan) $t(liquidation) $t(maklumatMesyuarat)", "maklumatPermohonanRayuanPertubuhan": "Organization appeal application information", "maklumatMesyuaratPenubuhan": "Meeting Information", "male": "Male", "mandatoryInfo": "All fields marked with * are mandatory", "masa": "Time", "masaLajutInfo": "Time extension requests are only allowed once.", "masaPlaceholder": "Select time", "matter": "Matter", "mattersDiscussedPlaceholder": "Please enter the Activity Report, Financial Report, Election Results for New Committee Members, and Proposed Amendments to the Constitution, if applicable", "maximumPeriod": "Maximum period of fee arrears allowed", "meeting": "Meeting", "meetingDate": "Meeting date", "meetingInformationNumberOfMembersAttendingTheMeeting": "Number of members attending the meeting", "meetingInformationTheNumberOfMembersEntitledToVote": "The Number of members entitled to vote", "meetingInformationTheNumberOfMembersWhoAgreeToTheDissolution": "Number of members who agree to the dissolution", "ajkMeetingFrequency": "Committee meeting frequency", "meetingFrequency": "Meeting frequency", "memberFeeIncome": "Membership fee", "memberInformation": "Member Information", "memberList": "Member List", "memberName": "Member name", "memberNumber": "Member Number", "memberRegister": "Member Register", "membershipCard": "Membership Card", "membershipFee": "Membership fee", "membershipFeeIncome": "Membership fee", "minimumAge": "Minimum Age", "minitMesyuarat": "Meeting Minutes", "minitMesyuaratViewOnly": "Meeting Minutes", "mobile": "Mobile", "mobileNumber": "Mobile number", "modification": "Modification", "muatNaik": "Upload", "muatNaikDokumenDisini": "Upload document here", "muatTurun": "Download", "myComplaint": "My $t(aduan)", "mykad": "MyKad", "namaKodQR": "QR Code Name", "namaCawangan": "Branch Name", "name": "Name", "nameOfThePerpetrator": "Name of the Perpetrator", "tambahCawangan": "Create Branch", "penambahanCawangan": "<PERSON><PERSON><PERSON><PERSON>", "keteranganCawangan": "Description Cawangan", "namaPenuh": "Full Name", "namaPertubuhan": "Organization Name", "nameAbbreviation": "Name Abbreviation", "nameDefinition": "Name Definition", "nationalOrganizationNote": "• For NATIONAL organization status, AJK information must consist of 7 AJKs from different states.", "negeri": "State", "newPassword": "New Password", "newPasswordPlaceholder": "Enter new password", "next": "Next", "no": "No", "noKadPengenalan": "ID Card Number", "noRegister": "No Registration", "noTelefonBimbit": "Mobile Phone Number", "noTelefonPejabat": "Office Phone Number", "notWhitelisted": "Not Whitelisted", "noAssetsLiabilitiesDeclaration": "I hereby declare that this association has no assets and liabilities.", "none": "None", "nonCitizen": "Non-citizen", "nonCitizenAJK": "Non-Citizen AJK", "nonCitizenCommittee": "$t(nonCitizen) committee", "nonCitizenCommitteeSuccessfullyCreated": "$t(nonCitizenCommittee) successfully created.", "nonCitizenCommitteeSuccessfullyUpdated": "$t(nonCitizenCommittee) successfully updated.", "nonCitizenCommitteeInformation": "$t(nonCitizen) Committee Information", "noticeManagement": "Notice Management", "notRegistered": "Not registered yet?", "number": "Number", "numberOfMembersAttendingTheMeeting": "$t(bilangan) of members attending the $t(meeting)", "numbers": "Numbers", "organizationEmpowerment": "Organization empowerment", "organizationActivities": "List of organization activities", "occupation": "Occupation", "offenseSection": "Offense $t(section)", "officeEquipmentSupplies": "Office Equipment/Supplies", "officeNumber": "Office Number", "officerCount": "Number of officers", "officerCount2": "Officer Count: {{count}}", "officerReceivingReport": "Officer Receiving Report", "onePage": "One Page", "onlinePayment": "Online payment for organization", "onlineUpperscoredPayment": "ONLINE Payment", "open": "Open", "operatingExpenses": "2.1 Operating Expenses", "operatingIncome": "1.1 Operating Income", "operatingIncomeSection": "1.1 Operating Income", "orang": "person(s)", "organization": "Organization List", "organizationActivityPromotion": "Organization Activity Promotion", "organizationApplicationDate": "Orgnization Application Date", "organizationBankAccountInfo": "Organization bank account information", "organizationCategory": "Organization category", "organizationComplain": "Organization $t(complaint)", "organizationContactInfo": "Organization Contact Information", "organizationDocumentList": "Organization Document List", "organizationFaxNumber": "Organization fax number", "organizationGoals": "Organization Goals", "organizationInfo": "Organization Information", "organizationInformation": "Organization Information", "organizationLevel": "Organization level", "organizationManagement": "Organization Management", "organizationName": "Organization Name", "organizationNameHelper": "Organization name should start with Association, Society, or Club", "organizationNamePlaceholder": "Organization name already exists", "organizationNumber": "Organization Number", "organizationNumberLowerCase": "Organization Number", "organizationOnlinePayment": "Organization Online Payment", "organizationPhoneNumber": "Organization phone number", "organizationRegistration": "Organization Registration", "organizationReport": "Organization Report", "organizationStatus": "Organization Status", "organizationSubCategory": "Organization sub-category", "organizationTypeInfo": "The constitution must follow the template provided by JPPM", "originCountry": "Origin Country", "otherDocuments": "Other Documents", "otherExpenses": "2.4 Other Expenses", "otherFundraisingIncome": "Other fundraising income", "otherGrants": "Other grants", "otherInvestmentIncome": "Other investment income", "otherMatter": "Other matter", "otherOperatingIncome": "Other operating income", "otherOrganizationNote": "• For other organization statuses, no conditions are set for AJK information.", "otherSkillsCriteria": "Other skills criteria", "others": "Others", "orgDocList": "Organization Document List", "personalInfo": "Personal Information", "padamMaklumatPertubuhan": "Delete Organization Information", "paparanCawangan": "View Branch", "parent": "Parent", "parentComplaint": "$t(parent) $t(complaint)", "parentConstitution": "Parent Constitution", "parliament": "Parliament", "participationFees": "Participation Fees", "passport": "Passport", "password": "Password", "payment": "Payment", "paymentMethod": "Payment Method", "paymentNote": "NOTE: Please make payment at the nearest JPPM counter if you choose the counter payment method.", "paymentReferenceNumber": "Payment Reference Number", "paymentStatusAlert": "For online payment status, the status will be updated within 24 hours. If the payment status does not change, please refer to the nearest state.", "pekerjaan": "Occupation", "pembubaranNote": "Please select the date of the liquidation presentation in the dropdown below. Fields marked with * are mandatory here.", "PEMType": "PEM Type", "PEMWithNumber": "PEM {{number}}", "PEMNoteWithNumber": "PEM {{number}} Note", "PEMIINotePopoverText": "Requested to fill in notes related to complaints and resend PEM II if there are any complaints received from other branches", "pengurusanCawangan": "Branch Management", "pengurusanIndividu": "Individual management of organizations and memberships", "pengurusanPertubuhan": "Organization Management", "penutup": "Closing", "perakuanPemohonan": "Certificate of application", "periodOfDefendingOneself": "The period of defending oneself/ asking for an explanation from the member's dismissal", "periodOfDefendingOneselfTwo": "Period of defending oneself/ asking for an explanation from the committee's suspension/ stripping", "perkaraPerkara": "Things Discussed", "permitExpiryDate": "Permit Expiry Date", "permitNumber": "Permit Number", "permohonanCawanganLuput": "Branch Application Expired", "permohonanLanjutanMasa": "Time extension request", "peringatanHubungiJPPM": "Please contact the State JPPM <NAME_EMAIL> if your organization's branch record is not in this list.", "petaLokasiSuratMenyurat": "Peta lokasi surat menyurat", "phone": "Phone Number/R/P", "phoneNumber": "Phone Number", "pleaseSaveThisComplaintReferenceNumber": "Please Save This Reference Number. You Can Check The Status Of The Complaint By Entering The Reference Number", "mobilePhoneNumber": "Hand Phone Number", "clickToLogin": "Click Here to Login", "phoneNumberCapitalizedOnlyFirstLetter": "Phone number", "phoneNumberRequired": "Phone number is required", "photocopy": "Photocopy", "place": "Place", "placeOfBirth": "Place of Birth", "placeOfBirthCapitalizedOnlyFirstLetter": "Place of birth", "placeOfBusiness": "Place of business", "placeOfBusinessLocationMap": "$t(placeOfBusiness) $t(locationMap)", "pleaseSelectAtLeastOneCommitteeBeforeUpdating": "Please select at least 1 committee before updating.", "position": "Position", "positionOfAuthority": "Position of authority", "positionOfAuthorityTasks": "Position of authority tasks", "poskod": "Postcode", "poskodPlaceholder": "Enter postcode", "postcode": "Postcode", "ppmBranchNumber": "PPM $t(cawangan) Number", "previous": "Previous", "private": "Private", "privateAgency": "Private agency", "privateAgencyGrant": "Private agency grant", "profile": "Profile", "projectStatus": "Project Status", "propertyOfficer": "Property Officer", "propertyOfficerAndOrPublicOfficer": "$t(propertyOfficer) and/or $t(publicOfficer)", "propertyOfficerInformation": "Property Officer Information", "propertyOfficerList": "Property Officer List", "propertyOfficerName": "Property Officer Name", "propertyOfficerRegistration": "$t(propertyOfficer) Registration", "propertyProfit": "Property profit", "propertyProfitIncome": "Property profit", "prosecution": "<PERSON><PERSON><PERSON><PERSON>", "publicOfficialsInformation": "Public Officials Information", "publicOfficer": "Public Officer", "publicOfficerList": "$t(publicOfficer) List", "publicOfficerName": "$t(publicOfficer) Name", "publicOfficerCreatedSuccessfully": "$t(publicOfficer) created successfully", "publicOfficerUpdatedSuccessfully": "$t(publicOfficer) updated successfully", "purchasePurpose": "Purchase Purpose", "purposeAndPaymentSearch": "Search Purpose and Payment", "purposeInMalaysia": "Purpose in Malaysia", "purposeOfTheMeeting": "Purpose of the Meeting", "reportingManagement": "Reporting management", "race": "Race", "rayuan": "Appeal", "rayuanInformation": "Appeal Information", "redFlag": "Red Flag", "referenceNumber": "Reference number", "referToOtherAgencies": "Refer to Other Agencies (ROA)", "referToOtherComplaints": "Refer to Other Complaints (ROR)", "registerAJK": "Register AJK", "registerAuditor": "Register Auditor", "registerBankAccount": "Register bank account", "registerButton": "Register", "registerHere": "Register Here", "registerNonCitizenAJK": "Register Non-Citizen AJK", "registerOfOrganizationManagementGuidelines": "Register of Organization Management Guidelines", "registerOrg": "Register Organization", "registerOrganization": "Register Organization", "registerPropertyOfficer": "Register Property Officer", "registerPublicOfficer": "Register Public Officer", "registerTrustee": "Register Trustee", "registeredAddressAndPlaceOfBusinessOfTheBranch": "Registered Address and Place of Business of the Branch", "registeredAddressAndPlaceOfBusinessOfTheOrganization": "Registered Address and Place of Business of the Organization", "registeredMemberAttendanceCount": "Number of registered members in attendance on the meeting date", "registeredMemberCount": "Number of registered members", "religion": "Religion", "remarks": "Remarks", "rent": "Rent", "rental": "Rental", "rentalIncome": "Rental income", "reset": "Reset", "resetForm": "Reset", "resetPasswordButton": "Reset Password", "residentialAddress": "Residential Address", "residentialAddressCapitalizedOnlyFirstLetter": "Residential address", "residentialArea": "Area / State of residence of members", "reviewStatus": "Review status", "roRecommendation": "RO Recommendation", "rosieIntroduction": {"1": "Hi, I'm <PERSON> {{- emojiCode}}. Can I help you?", "2": "Am I blocking the screen? Click and move me somewhere else. {{- emojiCode}}", "closeMessage": "$t(status_Close) message"}, "sameAsAbove": "Check if same as above", "sameAsBusinessAddress": "Same as $t(alamatTempatUrusan1)", "save": "Save", "saveChanges": "Save Changes", "saveGoals": "Save goals", "sceneOfTheIncident": "Scene of the Incident", "scholarship": "Scholarship", "search": "Search", "searchDoc": "Search Document", "searchInfo": "Search Information", "secondGoals": "2. <goals 2>", "secretary": "Secretary", "section": "Section", "sectionDetails": "$t(section) Details", "selectAnnualStatementPresentationDate": "Select annual statement presentation date", "selectFile": "Select a file", "selectIdType": "Select identification type", "selectPaymentMethod": "Please select a payment method", "selectPlaceholder": "Please select", "selfEmployed": "Self-employed", "semula": "Back", "senaraiAJKYangHadir": "List of Committee Members Present", "senaraiAJKYangTidakHadir": "List of Committee Members Absent", "senaraiDokumenPertubuhan": "Organization Document List", "senaraiRayuanPertubuhan": "Organization appeal list", "sendingPEMNotification": "Sending PEM Notification", "services": "Services", "servicesIncome": "Services", "setNewPasswordButton": "Set New Password", "silaMasukkanNamaKodQR": "Please enter QR code name", "silaPilih": "Please select", "skillType": "Skill Type", "skills": "Skills", "social": "Social", "startTime": "Start Time", "state": "State", "stateOrganizationNote": "• For STATE organization status, you must be from the same state as the organization's state.", "statementYearlyCashFlowDeclaration": "I hereby declare that this association has no income & expenses for this year", "statusAwaitingApproval": "Awaiting <PERSON><PERSON><PERSON><PERSON>", "statusPermohonan": "Application Status", "statusPertubuhan": "Organization Status", "statusRayuan": "Appeal Status", "streetCatNGO": "Street Cat NGO", "supportingDocuments": "Supporting Documents", "suspensionAndDismissalOfMembers": "Suspension and Dismissal of Members", "systemBackup": "System backup: State", "saveReport": "Save Report", "tarafPertubuhan": "Organization Status", "titleLogin": "Welcome to e<PERSON>oses", "tangibleAssets": "Tangible assets", "tarikhMesyuarat": "Meeting Date", "tarikhPermohonan": "Application Date", "tarikhPlaceholder": "Select date", "tax": "Tax", "tempatMesyuarat": "Meeting Place", "tempohMasa": "Duration", "tempohPengisianMaklumatCawanganDibuka": "Branch Information Filling Period Open", "templateMinitMesyuarat": "Meeting Minutes Template", "terminationNotice": "Notice of termination of membership", "theEntityApplicationHasBeenRecorded": "{{entity}} application has been recorded.", "thirdGoals": "3. ...", "thisComplaintHasBeenSuccessfullyReset": "This Complaint Has Been Successfully Reset!", "thisComplaintHasBeenSuccessfullySubmittedAndRecorded": "This Complaint Has Been Successfully Submitted And Recorded!", "tiadaFailDipilih": "No file selected", "tidak": "No", "timeOfIncident": "Time of Incident", "timeUnitHour_one": "{{count}} hour", "timeUnitHour_other": "{{count}} hours", "times": "times", "title": "Title", "titleFeedback": "Title", "titleRegister": "Register New User", "totalAssets": "Total Assets", "totalCurrentAssets": "Total Current Assets", "totalIncome": "Total Income", "totalIncomeAmount": "Total Income", "totalLiabilities": "Total Liabilities", "transportation": "Transportation", "treasurer": "Treasurer", "trusteeName": "Trustee Name", "trusteeInformation": "Trustee Information", "trusteeList": "Trustee List", "tujuanMesyuarat": "Meeting Purpose", "twitter": "Twitter", "typeOfAction": "Type of Action", "typeOfFee": "Type of fee", "ucapanAluanPengerusi": "Chairman's Welcome Speech", "unpaidDeferredTaxes": "Unpaid Deferred Taxes", "unpaidTaxes": "Unpaid Taxes", "update": "Update", "updateBankAccount": "Update bank account", "updateDocument": "Update", "updateForm": "Update", "updateOrgInfo": "Update organization information", "updateOrganizationInfo": "Update Organization Info", "uploadButton": "Upload", "uploadDocument": "Upload Document", "uploadId": "Please upload a picture of your identification below", "uploadIdPlaceholder": "Upload your identification picture here", "uploadSelfie": "Please upload a selfie with your identification below", "uploadSelfiePlaceholder": "Upload your selfie with identification here", "userGuide": "User Guide", "utilities": "Utilities", "value": "Value", "vehicles": "Vehicles", "verification": "Verification", "verificationNote": "* Verification of your identity will take a maximum of 3 days. You will be able to log in after identity verification is complete. You will receive a notification via the email entered above. Please ensure the email is valid.", "verificationOfPropertyOfficers": "$t(pengesahan) of Property officers", "viceChairman": "Vice Chairman", "vicePresident": "Vice President", "view": "View", "viewAJKInfo": "View AJK information", "viewAuditorInfo": "View Auditor Information", "viewDocument": "View Document", "viewMemberInfo": "View Member Information", "viewOrg": "View organization", "viewPropertyOfficerInfo": "View Property Officer Information", "viewPublicOfficerInfo": "View Public Officer Information", "viewTrusteeInfo": "View Trustee Information", "visaExpiryDate": "Visa Expiry Date", "visaNumber": "Visa Number", "weekly": "Weekly", "webDesign": "Web Design", "website": "Website", "websiteMarkup": "Website Markup", "week": "Week", "welfareExpenses": "Welfare Expenses", "whitelistDate": "Whitelist Date", "whitelisted": "Whitelisted", "whitelisting": "Whitelisting", "whitelistingInformation": "$t(whitelisting) Information", "whitelistingSuccessMessage": "{{- name}} $t(whitelisted) successfully", "writeInWords": "Write in words", "ya": "Yes", "yearlyIncomeStatementHelper": "Please fill in the income statement information for this financial year.", "years": "years", "yes": "Yes", "yourComplaintHasBeenSuccessfullySubmitted": "Your Complaint Has Been Successfully Submitted", "yourComplaintWillBeInvestigatedWithinDay_one": "Your Complaint Will Be Investigated Within {{count}} Day", "yourComplaintWillBeInvestigatedWithinDay_other": "Your Complaint Will Be Investigated Within {{count}} Days", "youthMember": "Youth Member", "youthMemberFirstOption": "Open to those aged 18 and under. They need to obtain written permission from their parents or legal guardians. They cannot vote or hold office in the Organization.", "submitApplication": "Submit application?", "confirmSubmitApplication": "Are you sure you want to submit this application?", "bilanganHari": "Number of days", "constitutionInformation": "Constitution Information", "amendmentList": "List of Amendments", "clauseDetail": "<PERSON>e Detail", "category": "Category", "subCategory": "Sub-category", "download": "Download", "amendmentInformation": "Amendment Information", "amendmentAll": "Amendment All", "amendmentAllWithoutGuide": "Amendment All Without Guide", "meetingName": "Meeting Name", "meetingPlace": "Meeting Place", "note": "Note", "meetingTime": "Meeting Time", "attendance": "Attendance", "meetingMinutes": "Meeting Minutes", "amendedClause": "Amended Clause", "reviewed": "Reviewed", "notReviewed": "Not Reviewed", "reviewConstitution": "Review Constitution", "viewMore": "View More", "organizationKnownAs": "This organization is known as", "hereinafterReferredTo": "hereinafter referred to as the \"Organization\".", "level": "Level", "amendmentType": "Amendment Type", "applicationStatus": "Application Status", "applicationDate": "Application Date", "submissionDate": "Submission Date", "decisionDate": "Decision Date", "viewAmendment": "View Amendment", "deleteAmendment": "Delete Amendment", "constitutionFor": "Constitution For", "beforeAmendment": "Before Amendment", "afterAmendment": "After Amendment", "sudahMempunyaiAkaun": "ALREADY HAVE AN ACCOUNT? LOG IN", "PengesahanAkaun": "Account Verification", "EnterCode": "Enter code", "WeveSentAnActivationCodeToYourEmailAt": "We've sent an activation code to your email at", "VerificationCodeIsRequired": "Verification code is required", "PengesahanIdentity": "Identity Verification", "Langkah": "Step", "SendCodeAgain": "Send code again", "PengesahanKataLaluan": "Password Verification", "MaklumatPengenalan": "Personal Information", "SilaLakukanPengesahanKadPengenalanDiri": "Please perform identity verification with your ID card", "SilaLakukanPengesahanDiriBersamaKadPengenalanDiri": "Please perform identity verification by holding your ID card in your hands", "registeredEmail": "Email registered with eRoses account", "checkEmail": "Please check your email to continue the password reset process", "backToLogin": "Back to login", "notifikasi": "Notification", "committeeListForYear": "Committee members list for the year", "passCertificate": "Pass certificate", "senaraiMesyuaratHadir": "Attendance list of committee members", "jumlahKehadiranKodQr": "Number of attendance according to QR CODE", "jumlahKehadiranSeluruhAhliMesyuarat": "Total member attendance", "petaLokasi": "Location Map", "addAjk": "Add AJK", "jenisPertubuhan": "Organization Type", "pengurusanAjk": "Membership and Committee Management", "penubuhanInduk": "Main Establishment", "penubuhanCawangan": "Branch Establishment", "kuiriPertubuhan": "Organization Inquiry", "penubuhanCawangan2": "Branch Establishment", "kuiriCawangan": "Branch Inquiry", "lanjutanMasa": "Time Extension", "tarikhBayar": "Payment Date", "tarikhMohon": "Application Date", "namePemohon": "Applicant's Name", "kelulusanPermohonanLanjutanMasa": "Approval of application for time extension", "bilanganHariDipohon": "Number of Days Requested", "justifikasiPermohonan": "Application Justification", "noPPMCawangan": "Branch PPM Number", "noPPMInduk": "Main PPM Number", "emelPemohon": "Applicant's <PERSON><PERSON>", "roBertanggungjawab": "Responsible RO *", "tarikhAlir": "Flow Date", "catatan": "Notes (100 word limit)", "lulus": "Approved", "keputusanPermohonan": "Application Decision", "permohonanConfirmation": "Are you sure about this application decision?", "bukanWarganegaraInduk": "Non-Citizen Main", "bukanWarganegaraCawangan": "Non-Citizen Branch", "applicationType": "Application Type", "ppmNumber": "PPM Number", "responsibleRO": "Responsible RO", "roSection": "RO Section", "nonCitizenAJKDecision": "Non-Citizen AJK Decision", "applicationDecision": "Application Decision", "decision": "Decision", "nonCitizenAJKInfo": "Non-Citizen AJK Information", "ajkQualificationList": "AJK Qualification List", "importanceOfNonCitizen": "Importance of Non-Citizen", "limitedTo100Words": "Limited to 100 words", "applicantList": "Applicant List", "action": "Action", "residentialPurpose": "Residential", "awaitingDecision": "Awaiting Decision", "utama": "Main", "kelulusanPertubuhan": "Organization Approval", "penyelenggaraanPertubuhan": "General Organization Maintenance", "carianPertubuhan": "Organization Information Search", "kemaskiniPertubuhan": "Update Organization", "penyelenggaraan": "Maintenance", "pengurusanPengguna": "User Management", "pengurusanPeranan": "Role Management", "penyelenggaraanUmum": "General Maintenance", "auditTrail": "Audit Trail", "piagamPerlembagaan": "Constitutional Charter", "mejaBantuan": "Help Desk", "senaraiAduan": "List of Complaints/Notifications/Feedback", "perkhidmatanKaunter": "Counter Services", "senaraiWarta": "Gazette List", "muatTurunWarta": "Download Gazette", "papar": "View", "branchedStatus": "Branched Status", "viewAjk": "View AJK", "orgNumber": "Organization Number", "identificationNumber": "Identification Number", "reviewSummary": "Review Summary", "orgAndApprovalInfo": "Organization and Approval Information", "externalAgencyReviews": "External Agency Reviews", "answerDate": "Answer date", "rejectionReason": "Rejection Reason", "menungguAgensiLuar": "Waiting for External Agency", "queryHistory": "Query History", "acceptExternalAgencyReviews": "Accept External Agency Reviews", "reviewDate": "Review Date", "noteRO": "Note (Limited to 100 words)", "sectionRO": "RO Section", "maklumatPermohonan": "Applicant Information", "termsAndPrivacyAgreement": "By continuing this registration, you agree to the", "termsOfUse": "Terms of Use", "privacyPolicy": "Privacy Policy", "mainPage": "Main Page", "VerificationCodeMustBe6Digits": "Verification code must be 6 digits", "EmailVerification": "Email Verification", "ActivationCodeSentTo": "An activation code has been sent to", "Verify": "Verify", "YourEmailHasBeenVerified": "Your email has been verified.", "Continue": "Continue", "ResendCodeIn": "Resend code in", "PhoneVerification": "Phone Verification", "VerificationCodeSentTo": "Verification code has been sent to", "YourPhoneHasBeenVerified": "Your phone number has been verified.", "PasswordIsRequired": "Password is required", "PasswordMinLength": "Password must be at least 8 characters long", "ConfirmPasswordIsRequired": "Confirm password is required", "PasswordsMustMatch": "Passwords must match", "YourPasswordHasBeenVerified": "Your password has been verified.", "UseAtLeast8Characters": "Use at least 8 characters, including uppercase, lowercase, and one special character", "PasswordConfirmation": "Password confirmation", "PasswordsDoNotMatch": "The entered passwords do not match. Please try again.", "identityVerification": "Identity Verification", "informationNeeded": "Here is the information we need from you.", "selfVerification": "Self Verification", "takePhotoInstructions": "Please take a photo of your face according to the given instructions.", "idCardInformation": "ID Card Information", "verifyValidIdCard": "Please verify your valid identification card.", "agreeToProvideData": "By clicking Accept and Continue, you agree to provide the requested data.", "acceptAndContinue": "Accept and Continue", "invalidEmail": "Invalid email address", "setNewPassword": "Set New Password", "createNewPasswordInstructions": "Create a new password. Make sure it's different from the previous one for security.", "fieldRequired": "This field is required", "passwordMinLength": "Password must be at least 8 characters long", "passwordsMustMatch": "Passwords must match", "passwordsDoNotMatch": "The entered passwords do not match. Please try again.", "setPassword": "Set Password", "passwordSet": "Password Set", "passwordChangedSuccessfully": "Your password has been successfully changed. <PERSON><PERSON> continue to log in.", "identityVerificationConfirmed": "Your identity verification has been confirmed.", "scanQRCodeInstructions": "Scan the QR code below using your mobile phone", "howEKYCWorks": "How eKYC works", "scanQRCode": "Scan QR Code", "people": "People", "ordinaryCommitteeMember": "Ordinary Committee Member", "padamPermohonan": "Delete Application", "pilihMesyuaratPembentangan": "Choose a meeting to present the constitutional amendment", "keputusanPermohonanPindaan": "Decision on Constitutional Amendment Application", "keputusan": "Decision", "pindaanUndang": "Constitutional Amendment", "maintenancerGeneral": "General Maintenancer", "penggunaJPM": "User JPM", "categoryPenggunaJPM": "User JPM Category", "maklumatPindaan": "Amendment Information", "namaRingkasPertubuhan": "Organization Short Name", "pilihanPerlembagaan": "Constitutional Choice", "sertaPertubuhan": "With Organization", "pembaharuanSetiausaha": "Update Secretary", "pembaharuanSetiausahaCawangan": "Renewal of Branch Secretaries", "activityLists": "List of Activities", "saveSearch": "Save search", "searchInformation": "Search Information", "enterMeetingName": "Enter meeting name", "registerMeeting": "Register Meeting", "meetingList": "Meeting List", "meetingType": "Meeting Type", "status": "Status", "submodule": "Submodule", "delete": "Delete", "meetingInformation": "Meeting Information", "pleaseSelect": "Please select", "meetingMethod": "Meeting Method", "platformType": "Platform Type", "meetingPurpose": "Meeting Purpose", "time": "Time", "locationMap": "Location Map", "meetingPlaceAddress": "Meeting Place Address", "meetingCall": "Meeting Call", "fillMeetingInformation": "Fill Meeting Information", "meetingAttendanceList": "Meeting Attendance List", "attendanceByQR": "Attendance by QR", "updateAttendance": "Update Attendance", "present": "Present", "absent": "Absent", "taskFlowInfo": "The secretary is allowed to delegate tasks if necessary. The secretary is allowed to choose any committee member to handle the task of filling in meeting information. The secretary is allowed to choose more than one committee member representative to assist in filling in meeting information.", "taskFlow": "Task Flow", "selectTaskFlowTo": "Select task flow to", "taskFlowList": "Task Flow List", "taskFlowStatus": "Task Flow Status", "taskFlowDate": "Task Flow Date", "deactivationDate": "Deactivation Date", "active": "Active", "inactive": "Inactive", "confirmTaskFlowChange": "Are you sure you want to change the Meeting Management task flow to another Committee?", "deactivateTaskFlow": "Deactivate Task Flow", "confirmDeactivateTaskFlow": "Are you sure you want to deactivate the Meeting Management task flow from this Committee?", "organizationTitle": "{{name}} / {{number}}", "deleted": "DELETED", "maklumatMajikanWarning": "*For committee members who are NOT WORKING/RETIRED, employer information does not need to be filled", "marketField": "Market field", "mandatoryHere": "is mandatory here.", "ajkBukanWnWarning": "Holding Positions for Non-Citizens This is to register other committee members who are not Malaysian citizens.", "maklumat": "Information", "kemaskiniCawangan": "Branch Update", "pengurusCawangan": "Branch Management", "kelulusan": "Approval", "organizationEstablishmentGeneralMeeting": "Organization Establishment General Meeting", "mawarCommunityHallGombak": "Mawar Community Hall, Gombak", "notSubmitted": "Not Submitted", "completed": "Completed", "committeeMeeting": "Committee Meeting", "meetingNoticeSent": "MEETING NOTICE SENT", "awaitingCommitteeResponse": "AWAITING COMMITTEE RESPONSE", "enterOrgNameOrNumber": "Enter Organization Name or Number", "searchOrganization": "Search Organization", "noOrganizationFound": "No organization found in records", "join": "Join", "cancel": "Cancel", "alreadyMember": "Already a member", "maklumatPengguna": "Information User", "categoryPengguna": "User Category", "carianNama": "Name Search", "senaraiPengguna": "User List", "bilPengguna": "User Bill", "tambahPengguna": "Add User", "maklumatPenggunaJPPM": "Information User JPPM", "gelaran": "Title", "peranan": "Role", "statusLogMasuk": "Login Status", "idUser": "User ID", "createBy": "Created By", "lastUpdate": "Last Update", "lastUpdateChangePassword": "Last Password Change Update", "categoryPenggunaJPPM": "Category User JPPM", "addCategoryPenggunaJPPM": "Add Category User JPPM", "descriptionCategory": "Category Description", "ketetapanKebenaranAkses": "Access Permission Settings", "add": "Add", "keputusanJawatankuasa": "Committee Decision", "maklumatPermohonanPembaharuanSetiausaha": "Secretary Renewal Application Information", "maklumbalasJawatankuasa": "Committee Feedback", "warningPertubuhan": "! Organization not on record. Please contact the State JPPM to check the status of your organization", "confirmActiveTugas": "Are you sure you will activate the task of managing this meeting with the Judicial Committee?", "pengaktifanAlirFlow": "Task flow activation", "welcomeTo": "Welcome to", "welcome": "Welcome", "passwordRequired": "Password is required", "nationalEmblem": "National Emblem", "logo": "Logo", "phoneNumberPlaceholder": "1 23 45 67 89", "and": "and", "notReceivedEmail": "Haven't received the email?", "passwordSetSuccessfully": "Password has been successfully set", "continue": "Continue", "Password": "Password", "Step": "Step", "Login": "<PERSON><PERSON>", "identificationCard": "Identification Card", "identification": "Identification", "passportNumber": "Passport No.", "idNumberRequired": "Enter your registered personal identification number", "toEroses": "to eROSES", "resetPasswordInstructions": "We have sent a reset link to {{email}}. Please enter the 6-digit code provided in the email.", "day": "Day", "month": "Month", "year": "Year", "biennial": "Biennial", "setahun": "Annually", "duaTahun": "Every 2 years", "presiden": "President", "pengarah": "Director", "timbalanPengerusi": "Deputy Chairman", "timbalanPengarah": "Deputy Director", "naibPengerusi": "Vice Chairman", "naibPresiden": "Vice President", "naibPengarah": "Vice Director", "generalSecretary": "General Secretary", "generalAssistantSecretary": "General Assistant Secretary", "generalTreasurer": "General Treasurer", "chiefTreasurer": "Chief Treasurer", "honoraryTreasurer": "Honorary Treasurer", "generalAssistantTreasurer": "General Assistant Treasurer", "chiefAssistantTreasurer": "Chief Assistant Treasurer", "honoraryAssistantTreasurer": "Honorary Assistant Treasurer", "monthly": "Monthly", "tempohDibenarkan": "The period allowed to deposit surplus money into the bank", "tahunKewanganBermula": "The financial year begins", "jumlahWangTangan": "Amount of money allowed in hand", "kuasaPerbelanjaan": "Expenditure authority of the general meeting", "kuasaPerbelanjaanCaw": "Expenditure authority of the general meeting", "kuasaPerbelanjaanCawangan": "Expenditure authority of the branch general meeting", "kuasaPerbelanjaanCawangan2": "Expenditure authority of the branch general meeting", "kuasaPerbelanjaanJawatankuasa": "Expenditure authority of the committee", "perbelanjaanDibenarkan": "Authorized expenses to be approved by the Chairman, Secretary and Treasurer", "perbelanjaanDibenarkanCawangan": "Authorized expenses to be approved by the Branch Chairman, Branch Secretary and Branch Treasurer", "internal": "Internal", "external": "External", "internalAuditorNumber": "Internal Auditor Number", "externalAuditorNumber": "External Auditor Number", "jenisMesyuaratAgung": "Type of General Meeting", "tempohPelaksanaan": "The period of implementation of the new general meeting from the last date of the General Meeting", "kekerapanPelaksanaan": "The frequency of the implementation of new general meetings", "notisPanggilanMesyuarat": "Meeting Notice", "keteranganBendera": "Flag Description", "bendera": "Flag", "keteranganLambang": "Symbol Description", "lambang": "Symbol", "keteranganLencana": "Emblem Description", "lencana": "Emblem", "alamatUrusan": "Business Organization Address", "alamatSuratMenyurat": "Business Organization Postal Address", "bubarPertubuhan": "Dissolve Organization", "bubarPertubuhanConfirm": "This module aims to cancel the organization under Section 13(1)(a). Are you sure you want to dissolve the organization?", "bankruptcy": "bankruptcy", "saving": "Saving", "requiredValidation": "Please fill in the mandatory fields", "paparMesyuarat": "View Meeting", "checkAJK": "Semak AJK", "comingSoon": "Coming Soon", "comingSoonMessage": "Exciting features are coming soon", "internalUser": "Internal User", "organizationUser": "Organization User", "meetAndFinanceInformation": "Meeting and financial information", "maklumatAsset": "Asset Information", "maklumatAssetCampaign": "The method of settlement on the remaining assets must be in accordance with the decision of the liquidation general meeting.", "reference_number": "Reference Number", "organization_category": "Organization Category", "sub_category": "Sub-category", "organization_name": "Organization name", "organization_phone": "Organization Phone Number", "financial_year_start": "Financial Year Start", "registered_members_count": "Number of Registered Members", "office_bearers_count": "Number of Office Bearers", "office_bearers_count_disabled": "Number of Office Bearers", "branches_count": "Number of Organization Branches (if any)", "affiliations": "Affiliated with Local and/or International Organizations", "tolak": "Rejected", "positionInfo": "Position Information", "pending": "Pending", "infoPernyataTahunan": "The Annual Statement must be updated within 60 days after the date of the Annual General Meeting. For organizations that do not hold a meeting that year, the statement must be updated before March 1st.", "maklumatPemohon": "Information Applicant", "kuiri": "Inquiry", "catatanKuiri": "Inquiry Document", "personalInfoNewSec": "Personal Information New Secretary", "inputPlaceholder": "Please fill in", "mobileAddition": "(Mobile)", "houseAddition": "(House)", "officeAddition": "(Office)", "pelantikanSetiausaha": "New Secretary Appointment Meeting Information", "sebabTukarSetiausaha": "Secretary Change Cause", "personalInfoOldSecretary": "Old Secretary Personal Information", "confirmOldSecretary": "I declare that I am the person who has been appointed as the Secretary to update the organization's information.", "ulasan": "Review", "pengakuanPembaharuan": "The statement of the renewal application of the secretary of this organization is True.", "pengakuanTitle": "Acknowledgment of renewal of the secretary", "enterSearch": "Enter search", "maklumatSetiausahaBaru": "New Secretary Information", "maklumatSetiausahaLama": "Old Secretary Information", "maklumBalas": "<PERSON><PERSON><PERSON>", "statusPemegangJawatan": "Position holder status", "pengakuan": "Confession", "senaraiMaklumbalasJawatankuasa": "Committee Feedback List", "cetak": "Print", "statusCawangan": "Branch Status", "selectSociety": "Select Society", "selectBranch": "Select Branch", "cawanganNumber": "Branch Number", "pelantikanMesyuarat": "Meeting information for the appointment of a new branch secretary", "cawangan": "Branch", "checkSetiausahaBaruCawangan": "I declare that the nominee above is the person who has been appointed as the branch secretary to update this branch's information.", "pengesahan": "Confirmation", "infoPayment": "The application for registration of the organization has been recorded.", "infoPaymentCawangan": "The application for registration of the branch has been recorded.", "infoPaymentPindaanCawangan": "The application for registration of the branch amendment has been recorded.", "paymentAmount": "Amount to be paid", "noteKaunter": "Note: Please print this slip to bring when paying at the counter.", "noteOnline": "Note: You can print this slip for your own reference.", "paymentType": "Payment Type", "amanah": "OSOL/ Trust Code", "jabatan": "Position", "pusatPenerimaan": "Reception Center", "bayar": "Pay", "terms": "Terms & Conditions", "term1": "Payment information must be checked before payment can be made. Please make sure the information is accurate.", "term2": "Please make sure you close/Disable Popup Blocker on your web server (web browser) before making payment. Please follow the instructions on the transaction screen until the payment process is complete and the payment receipt is displayed. We are not responsible if the receipt screen and transaction confirmation screen cannot be displayed due to a popup blocker problem in your browser.", "term3": "Please do not close the 'pop-up page' that is displayed during the FPX transaction so that the transaction can be executed successfully.", "term4": "The FPX service charge charged for each transaction will be borne by the Malaysian Organizations Registration Department", "term5": "Please print or save the payment transaction receipt. This receipt is valid and can be used as proof of payment.", "term6": "The payment update period is for 2 working days (If there are no technical problems with the system).", "term7": "The operating hours of the payment service through FPX are from 7 am to 11 pm every day including holidays and public holidays.", "term8": "All payments made are non-refundable.", "term9": "Any problems please contact the Malaysian Organizations Registration Department at 03-8890 5776 / 03-8890 5778 <NAME_EMAIL>", "term10": "Credit Cards are not allowed in this system.", "setuju": "I Agree", "butiran": "Payment Details", "infoButiran": "Please fill in your details below to proceed with payment.", "perbankan": "Banking in italian", "modPembayaran": "Payment Mode", "pilihanBank": "Bank Selection", "noteButiran": "By clicking the 'Continue' button, you agree to the FPX Terms & Conditions.", "maklumatPertubuhanDanCawangan": "Organization and Branch Information", "kebangsaan": "Nationality", "pembayaran": "Payment", "warta": "Gazette", "migrasiPerlembagaan": "Constitutional Migration", "tempohPelantikanWakilCawangan": "Branch Representative Appointment Period", "tempohPelantikanWakilAhli": "Member Representative Appointment Period", "bilanganWakilCawangan": "Number of Selected Branch Representatives", "bilanganWakilCawanganAhli": "Number of First Member Branch Representatives", "bilanganWakilCawanganAhliSeterusnya": "Number of Next Member Branch Representatives", "bilanganWakilAhli": "Number of Elected Member Representatives", "bilanganWakilAhliPertama": "Number of First Member Representatives", "bilanganWakilAhliSeterusnya": "Next Number of Member Representatives", "maksimumWakilCawangan": "Maximum Number of Branch Representatives", "maksimumWakilAhli": "Maximum Number of Member Representatives", "invalidPhoneNumber": "Please enter a valid Malaysian phone number starting with +60", "phoneNumberHelper": "Format: +60", "emailHelper": "Format: <EMAIL>", "carianAlert": "Please make sure the information entered is accurate", "paymentDate": "Payment Date", "bilanganCarianDokumen": "Number of Document Searches: {{count}}", "senaraiMaklumatCarian": "Search List Information", "messageMaklumatAmSuccess": "Maklumat Am successfully saved", "messageMaklumatAmError": "Maklumat Am failed to save", "userProfile": "User Profile", "contactInfo": "Contact Information", "changePassword": "Change Password", "oldPassword": "Old Password", "rulePasswordOne": "Password must be at least 8 and maximum 15 characters long", "rulePasswordTwo": "Password must be a combination of upper and lower case letters and numbers followed by symbols (!,@%,#)", "aduanMakluman": "Complaints/Information", "pembayaranKaunter": "Counter Payment", "rekodPembayaran": "Payment Record", "semakanKaunterIndividu": "Individual Counter Review", "noRujukanPembayaran": "Payment Reference Number", "noKpPemohon": "Applicant's IC Number", "bayaranDiterima": "Payment Received", "keteranganPembayaran": "Payment Description", "amaunPembayaran": "Payment Amount", "kodHasil": "Revenue Code", "noResit": "Receipt Number", "namaPembayar": "Payer's Name", "noPengenalanPembayar": "Payer's Identification Number", "negeriBayaranDiterima": "State of Payment Received", "pegawaiBertugas": "Officer on Duty", "minimumJumlahAhliPertubuhan": "Minimum Number of Organization Members", "messageMaklumatMesyuaratPenubuhanSuccess": "Meeting information successfully saved", "messageMaklumatMesyuaratPenubuhanError": "Meeting information failed to save", "emailRegisteredPleaseLogin": "Your email has been registered, please login", "phoneNumRegisteredPleaseLogin": "Your phone number has been registered, please login", "emailOrPhoneRegisteredPleaseLogin": "Your email or phone number has been registered, please login", "kodOsolAmanah": "OSOL/Trust Code", "Successfully uploaded Document": "Successfully uploaded Document", "File sudah masuk database": "Successfully file inserted database", "BELUM_DIHANTAR": "Not Submitted", "MENUNGGU_KEPUTUSAN": "Awaiting Decision", "LULUS": "Approved", "TOLAK": "Rejected", "MENUNGGU_BAYARAN_KAUNTER": "Awaiting Counter Payment", "MENUNGGU_BAYARAN_ONLINE": "Awaiting Online Payment", "MAKLUMAT_TIDAK_LENGKAP": "Incomplete Information", "LUPUT": "Expired", "DIBENARKAN": "Allowed", "TIDAK_DIBENARKAN": "Not Allowed", "AKTIF": "Active", "MENUNGGU_PENGAKTIFAN_CAWANGAN": "Awaiting Branch Activation", "TIADA_MAKLUMAT_MIGRASI": "No Migration Information", "TAMAT_TEMPOH_CARIAN": "Search Period Expired", "SETIAUSAHA_BUKAN_WARGA": "Secretary Not a Citizen", "BUBAR": "Dissolved", "SELESAI": "Completed", "DISYORKAN": "Recommended", "TIDAK_DISYORKAN": "Not Recommended", "BATAL": "Cancelled", "MENUNGGU_SYOR_PEGAWAI_PEMPROSES": "Awaiting Officer's Recommendation", "TEST_DATA": "Test Data", "MENUNGGU_MAKLUMBALAS": "Awaiting <PERSON><PERSON><PERSON>", "PERTELINGKAHAN": "Dispute", "TARIK_BALIK": "Withdrawn", "BATAL_KHAS": "Special Cancellation", "MENUNGGU_PENGESAHAN_JAWATANKUASA_INDUK": "Awaiting Main Committee Confirmation", "LULUS_BERSYARAT": "Conditionally Approved", "DALAM_TINDAKAN_KIV": "Under KIV Action", "KUIRI": "Query", "PINDAH": "Transferred", "MENUNGGU_PENGESAHAN_BAYARAN": "Awaiting Payment Confirmation", "MENUNGGU_KEPUTUSAN_MENTERI": "Awaiting Minister's Decision", "MENUNGGU_ULASAN": "Awaiting Review", "MENUNGGU_ULASAN_AGENSI_LUAR": "Awaiting External Agency's Review", "NOTIS_MESYUARAT_DIHANTAR": "Meeting Notice Sent", "INAKTIF": "Inactive", "BAYARAN_GAGAL": "Payment Failed", "BAYARAN GAGAL": "Payment Failed", "confirmDeletePerlembagaan": "Are you sure you want to delete this constitution?", "confirmSend": "<PERSON><PERSON>h anda pasti untuk hantar AJK?", "deleteOrganizationConfirmation": "Are you sure you want to delete this organization?", "deleteSuccess": "Organization successfully deleted", "deleteError": "Failed to delete organization", "postcodeValidation": "Postcode must be exactly 5 digits", "postcodeHelper": "Enter a 5-digit postcode", "masukkan5DigitPoskod": "Enter 5 digit Postcode", "passwordPattern": "Passwords must meet the following requirements: minimum 8 characters, contain uppercase and lowercase letters, numbers, and special characters", "passwordRequirements": "Enter at least 8 characters with a mix of uppercase & lowercase letters, numbers, and special characters (@$!%*#?&)", "setiapTahun": "Every Year", "duaTahunSekali": "Every Two Years", "bilangan": "Number", "bilanganAhliJawatanKuasa": "Number of Committee Members", "singkatanNamaRequired": "Abbreviated name is required", "takrifNamaRequired": "Definition of name is required", "kawasan": "Area", "kampung": "Village", "mukim": "District", "tamanPerumahan": "Residential Area", "messageKeputusanPermohonanSuccess": "Main application decision successfully submitted", "messageKeputusanPermohonanError": "Failed to submit main application decision", "bantuanKelahiranAnakPertama": "First Child Birth Assistance", "bantuanKelahiranAnakKedua": "Second Child Birth Assistance", "bantuanKelahiranAnakKetiga": "Third Child Birth Assistance", "bantuanKelahiranAnakSeterusnya": "Subsequent Child Birth Assistance", "bantuanKematianAhliSendiri": "Own Member Death Assistance", "bantuanKematianPasanganAhli": "Spouse Member Death Assistance", "bantuanKematianAnakAhli": "Child Member Death Assistance", "bantuanKematianIbuBapaAhli": "Parent Member Death Assistance", "bantuanKematianNenekDatukAhli": "Grandparent Member Death Assistance", "bantuanPengebumian": "Funeral Assistance", "bantuanNafkahAhliKeuzuran": "Elderly Member Assistance", "bantuanNafkahAhliBalu": "Widow Member Assistance", "bantuanNafkahAhliBaluAnak": "Widow and Child Member Assistance", "bilanganOrangPemegangAmanah": "Number of Trust Holders", "rekodPertubuhan": "Organization Record", "idAlreadyRegistered": "Error : ID number is already registered in eRoses system. Please login.", "icValidationOff": "Our system is under maintenance, Please try again later.", "invalidIdNumber": "Error : Invalid ID number", "errorValidatingId": "Error validating ID", "otpSendToEmail": "OTP has been sent to email {{email}}. Please enter the 6-digit code for verification.", "enterOtp": "Enter OTP", "belumTerimEmel": "Haven't received the email?", "hantarSemula": "Resend", "otpSentMessage": "OTP has been sent to your email. Please check your email and reset your new password to continue the process.", "infoQaSatu": "Your organization registration application will be kept for 30 days from the date you first apply.", "jenisPerlembagaanPopover": "The constitution must follow the format provided by JPPM.", "semakFasal": "Check Clause", "isi": "Fill", "taraf": "Level", "nameDefinitionPlaceholder": "The definition of the name of this organization means.............", "bayaranMasuk": "Entry Fee", "permulaanTahunKewangan": "Start of Financial Year", "jenisMesyuaratAgungDanTempohLantikan": "Type of General Meeting and Appointment Period", "masaDanTarikhMesyuarat": "Meeting Date and Time", "kehadiranAhliMesyuarat": "Attendance of Members", "jumlahKehadiranMesyuarat": "Total Attendance of Members", "kehadiranAjk": "Attendance of AJK", "branchMeeting": "Branch Meeting", "tambahMesyuarat": "Add Meeting", "deactivate": "Deactivate", "peringatan": "REMINDER", "lanjutMasaWarning": "All fields marked * are required. Time extensions are allowed only once.", "permintaanSertaPertubuhan": "With Organization Request", "jumlahAhliPertubuhan": "Current Total Organization Members", "senaraiAhliPertubuhan": "Organization Members List", "maklumatPeribadiAhli": "Member Personal Information", "peringatanGunakanAlamatSah": "Please use a valid address to avoid the rejection of this branch registration approval request.", "petaMesyuarat": "Meeting Map", "bilanganAhliJawatankuasaTerkini": "Current Number of Committee Members", "maklumatMesyuaratPelantikanAjk": "Meeting Information for the Appointment of AJK", "bilanganAhliYangHadir": "Number of Members Present", "bilanganAhliJawatankuasaBukanWnTerkini": "Current Number of Non-Citizen Committee Members", "senaraiAjkBukanWn": "List of Non-Citizen AJK", "tarikhLantik": "Appointment Date", "bilanganPemegangAmanahTerkini": "Current Number of Trust Holders", "bilanganJuruauditTerkini": "Current Number of Auditors", "pilihanJuruaudit": "Auditor Selection", "namaAhliPertubuhan": "Organization Member Name", "maklumatJuruauditDalaman": "Internal Auditor Information", "maklumatJuruauditBertauliah": "Certified Auditor Information", "nomborLesen": "License Number", "pilihanPegawaiAwam": "Public Officer Selection", "jenisPegawai": "Officer Type", "member": "Member", "notMember": "Not Member", "registerAsAMember": "Register As A Member", "corporate": "Corporate", "newsHighlights": "News & Highlights", "businessPartners": "Business Partners", "connectWithUs": "Connect With Us", "scamAlerts": "Scam & Alerts", "procurement": "Procurement", "termsConditions": "Terms & Conditions", "securityPolicy": "Security Policy", "disclaimer": "Disclaimer", "onlineSecurityTips": "Online Security Tips", "otherLinks": "Other Links", "forms": "Forms", "resourceCentre": "Resource Centre", "achievements": "Achievements", "pengurusanPerlembagaan": "Constitutional Management", "perlembagaanPertubuhan": "Organization Constitution", "permohonanPindaanPerlembagaan": "Application for Constitutional Amendment", "maklumatMesyuaratPindaanPerlembagaan": "Constitutional Amendment Meeting Information", "maklumatPindaanPerlembagaan": "Constitutional Amendment Information", "bercawangan": "Branching", "permohonanMigrasiPerlembagaan": "Constitutional Migration Application", "mandatoryFieldsAlert": "Please fill in all mandatory fields", "dissolutionMeetingInformation": "Dissolution Meeting Information", "currentAssets": "Current Assets", "assetInformationList": "Asset Information List", "assetInformationRegister": "asset information register", "assetType": "Asset Type", "solution": "Solution", "username": "Username", "userStatus": "User Status", "jpnStatus": "JPN Status", "legal": "Legal", "illegal": "Illegal", "pembayaranOnline": "Online Payment", "jabatanPendaftaran": "Registration Department", "pertubuhanMalaysia": "Malaysian Organization", "ahli": "Member", "hubungi": "Get in touch", "logMasukAhli": "<PERSON><PERSON>", "terokaiBacaanTerkini": "Reading Highlights for you", "penyampaianPerkhidmatan": "Delivery of Services", "daftarAkauneRoses": "Register an eRoses Account", "latihan": "Training", "takwim": "Calendar", "semakanJPPM": "Review of JPPM", "hebahan": "Excitement", "COGG": "COGG", "panduanPengurusanPertubuhan": "Organization Management", "bacaanTerkiniUntukAnda.": "Latest Reading For You.", "pertubuhan": "Organization", "penarafanKepadaPertubuhan.": "Rating To Organizations.", "mediaSosial.": "Social Media.", "agensiKawalSeliaPertubuhanSelainJPPM": "Organization Regulatory Agency Other than JPPM", "nomborTelefonRumah": "Home phone number", "nomborTelefonPejabat": "Office phone number", "lihatSenaraiSemak": "See Checklist", "clickToUpload": "Click to upload", "uploadComplete": "Upload complete", "senaraiSemakRayuan": "Appeal checklist", "senaraiSemakRayuanContent": "1. Organization Appeal Application Letter\n\n2. Copy of Fee Payment Receipt Totaling RM 50.00\n\n3. Copy of Notice of Order to Submit Information (Notice Section 14/2)\n\n4. Copy of Notice of Organization Before Cancellation (Notice Section 13/2/ Section 13(1)(a)/ Subsection 14(5)/ Others)\n\n5. Copy of Organization Registration Certificate (Form 3)\n\n6. Complete Annual Statements by Year as Seen in Section 13(2) and Section 14(2) Notice\n\n\b \b \b \b \b a) List of Committee Members\n\b \b \b \b \b b) Meeting Minutes\n\b \b \b \b \b c) Financial Statements\n\nOther documents submitted are REQUIRED TO USE Malay in accordance with Section 2A (1)(c), Societies Act 1966.", "langkahPermohonanRayuan": "Appeal Application Steps", "bayaran": "Payment", "tarikhKeputusanDitolak": "Declined decision date", "filterBy": "Filter by", "uploading": "Uploading", "pengerusi": "Chairman", "setiausaha": "Secretary", "penolongSetiausaha": "Assistant Secretary", "bendahari": "Treasurer", "ahliJawatankuasa": "Committee Member", "penggunaBiasa": "Regular User", "penolongBendahari": "Assistant Treasurer", "ahliBiasa": "Ordinary Member", "perwakilanNegeri": "State Representative", "lain-lain": "Others", "MyPR": "MyPR", "emptyField": "Field cannot be empty", "date": "Date", "nonCitizenVerifyErrorMsg": "Your personal identification number has been registered in the eRoses system. Please log in.", "langkahCarianDokumen": "Document Search Steps", "carianPertubuhanDanMaklumat": "Search for organizations and information", "setujutext": "I hereby confirm that all the information provided is true. If the CORPORATION REGISTRATION DEPARTMENT finds that there is fraud and falsity in the information in the documents I have provided above, then the CORPORATION REGISTRATION DEPARTMENT has the right to reject my application and if convicted, I may be fined not exceeding RM 2000 in accordance with section 54 A, CORPORATION ACT 1966 and any law in force.", "silaMasukNamaPertubuhan": "Please enter the organization name correctly.", "dokumen": "Documents", "amaun": "Amount", "jumlah": "TOTAL", "seterusnya": "Next", "akuansetujuterima": "I agree to accept.", "paymentmethod": "Payment method", "notaSilamembuatbayaranJppm": "NOTE: Please make payment at the nearest JPPM counter if you choose the over-the-counter payment method.", "permohonanCarianDokumen": "Document Search Application", "silaHadir": "Please come to the nearest JPPM to search for information on registered organizations before:", "placeanddate": "16/01/2012 (Perak, Selangor, Kuala Lumpur, Johor, Negeri Sembilan)", "palceanddate2": "01/07/2012 (All States)", "carianDokumenLulusDalamTempoh": "Approved document searches must be downloaded during the period", "frompaydate": "from the date of payment.", "30days": "30 days", "sureToSubmit": "Are you sure you want to submit this application?", "back2": "Back", "feedbackHerotext": "Do you have any complaints or feedback?", "cadanganMaklumbalasbaru": "New Suggestions and Feedback", "cadanganDanMaklumBalas": "New Suggestions and Feedback", "SemakCadangan": "Review Suggestions and Feedback", "cadanganMaklumbalas": "Suggestions and Feedback", "aduan": "complaint", "cadanganOrMakluman": "Suggestions/Information", "pertanyaan": "Inquiry", "jenis": "Type", "tajuk": "Title", "butiranText": "Details", "lokasi": "Location", "jantina": "Gender", "lampiranCadanganDanMaklumbalas": "Attachment of suggestions and feedback", "silaPilihJenis": "Please select a type", "tajukRequired": "Title is required", "lokasiRequired": "Location required", "janitaRequired": "Gender is required", "negeriRequired": "State required", "daerahRequired": "District required", "poskodRequired": "zip code required", "bandarRequired": "city required", "jenisPengenalanDiriRequired": "Type of identification required", "nomborPengenalanDiriRequired": "Identification number required", "perkerjaanRequired": "Jobs required", "idType2": "Identification type", "semak": "Check", "jenispengenalandRequired": "Type of identification required", "uploadCompleted": "Upload complete", "disahkanOleh": "Verified By", "disediakanOleh": "Prepared By", "organizationSubCategory2": "Organization sub category", "feedbackList": "Feedback list", "jenisCadanganMaklumBalas": "Type of suggestion/feedback", "tajukButiran": "Title/Details", "feedback": "<PERSON><PERSON><PERSON>", "systemIssue": "system issue", "ExtraordinaryGeneralMeeting": "Extraordinary General Meeting", "feedBackSuccessSent": "Your Feedback Has Been Successfully Sent!", "noRujukanMaklumbalas": "Feedback Reference Number", "silaNoSemak": "Sila simpan nombor rujukan ini. <PERSON>a boleh menyemak status maklum balas dengan memasukkan nombor rujukan tersebutPlease save this reference number. You can check the status of your feedback by entering the reference number.", "thankyou": "Thank you", "AM": "Morning", "PM": "Evening", "administrasi": "Administration", "carian": "Search", "perlembagaan": "Constitution", "profil": "Profile", "petiMasuk": "Inbox", "keluar": "Logout", "tetapan": "Settings", "tarikhPemohonan": "Date of application", "namaTempatMesyuarat": "Nama Tempat Mesyuarat", "buang": "Throw away", "organizationNumber2": "Organization Number", "negaraAsal": "Country of origin", "nomborVisa": "Visa Number", "nomborPermit": "Permit Number", "tujuanDiMalaysia": "Destination in Malaysia", "tempohDiMalaysia": "Period of stay in Malaysia", "importanceOfPosition2": "Briefly explain the importance of non-citizens holding the position", "daftarCawanganBaru": "Register a new branch", "daftarSetiausahaCawanganBaru": "New branch secretary update", "dissolutionSteps": "Dissolution Steps", "financialInformation": "Financial Information", "applicationForDissolution": "Application For Dissolution", "committeeReview": "Committee Review", "noData": "No Data", "simpan": "save", "landing_text_1": "JPPM offers an efficient and open organization registration service, ensuring compliance with the law and simplifying the administrative process for community progress.", "panduanPengurusanPertubuhan_subtext": "Complete guidelines for association registration, constitution management, submission of annual statements, committee management and association management.", "pegawaiAwamDeclaration": "I declare that the appointed Public Officer is from among the members of the association. If the information is found to be false, it is subject to the Societies Act 1996 and the Regulations 1984.", "takwim_subtext": "Programs and activities organized by JPPM throughout the year for organizations.", "grant_subtext": "Government incentives to support the activities and development of organizations.", "cogg": "Code of Good Governance (COGG)", "cogg_subtext": "Basic guidelines for transparent and integrity-based corporate governance.", "latihan_subtext": "Development activities to improve skills, and knowledge in organization management.", "semakan": "Review", "semakan_subtext": "<PERSON><PERSON><PERSON><PERSON><PERSON> dan penilaian ter<PERSON><PERSON> pertubuh", "soalanlazim": "Frequently Asked Questions (FAQ)", "soalanlazim_subtext": "Get answers to common questions regarding services, procedures, and important information that are frequently asked to make your business easier.", "KetahuiLanjut": "Learn more", "PertubuhanSelainJPPM_text": "Other regulatory bodies are responsible for ensuring that certain organizations in Malaysia comply with established laws and regulations.", "jppm": "Registrar of Societies Malaysia.", "Perkhidmatan Teras": "Core Services", "kisahText": "Stories and Inspirations of the Association", "kisahText_1": "Efficient Management Methods", "kisahText_2": "Core community accountability", "kisahText_3": "Fostering Association Independence", "kisahText_4": "Madani Plural Association,", "kisahText_5": "A united society", "kisahText_6": "Association According to Religion", "agensiJppm_text": "Regulatory Agencies Other than JPPM", "bacaantitle": "Organization Inspiration", "bacaann_subtitle": "Interesting articles that highlight the activities, successes, and role of associations in strengthening communities and bringing about positive change.", "exploreMore": "Explore more", "tempohMuatTurun": "Tempoh Mu<PERSON>", "disahkanOleh ": "Confirmed by ", "noppm": "No. PPM", "jawatan": "position", "chairmanPersonalInfo": "Chairman's Personal", "keputusanInduk": "Keputusan Induk", "lihatpaparanpenuh": "Full view", "belumselesai": "Not Completed", "sureAjk": "Please make sure the committee information is complete.", "kliksemak": "CLICK TO CHECK", "namaDisenaraiKelabu": "The name of the organization is on the Grey List", "tandaJijaSamaDiatas": "Sign if same as above", "ajk_jawatankuasa_peringatan_1_point1": "Sila pastikan bilangan Ahli Jawatankuasa Biasa mengikut bilangan di dalam perlembagaan.", "ajk_jawatankuasa_peringatan_1_point2": "Bagi pembaharuan setia<PERSON>ha sila klik <1>disini</1> atau pada menu <2>Dashboard > Pembaharuan Setia<PERSON><2>", "ajk_jawatankuasa_peringatan_1_point3": "Pengkemaskinian maklumat AJK memerlukan anda untuk mengisi maklumat mesyuarat pelantikan AJK yang baru. Jika Senarai Mesyuarat tiada dalam pilihan , klik <1>disini</1> bagi kemasukan maklumat mesyuarat terlibat.", "ajk_jawatankuasa_peringatan_1_point4": "Perubahan maklumat AJK akan dikemaskini setelah anda membuat pengesahan di bahagian terakhir dan menekan butang “Kemaskini”.", "bilanganPemegangJawatan": "Bilangan Pemegang Jawatan", "ajk_jawatankuasa_peringatan_2_point1": "Bahagian ini adalah untuk mendaftarkan AJK yang bukan warganegara.", "ajk_jawatankuasa_peringatan_2_point2": "Pegang J<PERSON>tan bagi bukan warganegara ini perlu mendapat kebenaran dari Pendaftar Pertubuhan terlebih dahulu dengan menekan butang “Hantar”.", "ajk_jawatankuasa_peringatan_2_point3": "<PERSON><PERSON> per<PERSON> keb<PERSON>ran bukan warganegara telah <PERSON>, AJK bukan warganegara akan tersenarai dalam Senarai AJK.", "ketetapanPiagam": "Charter Provisions", "pengurusanIntegrasi": "Integration Management", "carianAuditTrail": "Audit trail search", "cari": "Search", "tiadaData": "No data", "tarikhDanMasaRekod": "Record date and time", "noPengenalanDiriDisemak": "ID number checked", "modul": "<PERSON><PERSON><PERSON>", "langkahPenyataTahunan": "<PERSON><PERSON><PERSON> pen<PERSON>ta ta<PERSON>an", "penyataPendapatanPerbelanjaan": "Financial Statements", "paparan": "<PERSON><PERSON>", "tahunPenyata": "<PERSON><PERSON>", "tambahPenyataTahunan": "Tambah Penya<PERSON>", "tiadaMaklumat": "Tiada Ma<PERSON>lumat", "penyataTahunan_peringatan_point": "Sila isi maklumat mesyuarat pembentangan penyata tahunan jika <PERSON> tiada dalam pilihan atau klik disini bagi kemasukan maklumat mesyuarat terlibat.", "maklumatMesyuaratPenyataTahunan": "Annual Statement Meeting Information", "penyataTahunan_ajk_peringatan_point_1": "Sila pastikan bilangan Ahli Jawatankuasa Biasa mengikut bilangan di dalam perlembagaan.", "penyataTahunan_ajk_peringatan_point_2": "Bagi kemaskini AJK sila klik disini atau pada AJK & Keahlian > Jawatankuasa", "penyataTahunan_ajk_peringatan_point_3": "<PERSON>gi kemaskini pembaharuan setia<PERSON>ha sila klik disini atau pada menu Dashboard > Pembaharuan Setia<PERSON>ha.", "penyataTahunan_ajk_peringatan_point_cawangan_3": "<PERSON>gi kemaskini pembaharuan setiausaha sila klik disini atau pada menu Dashboard > Pembaharuan Setia<PERSON><PERSON>.", "kemaskiniAJK": "Kemaskini AJK", "peyataTahunan_pendapatan_pengakuan": "Dengan ini saya mengaku bahawa persatuan ini tidak mempunyai sebarang Pendapatan & Perbelanjaan pada tahun kewangan ini.", "lainLainPendapatan": "Lain lain pendapatan", "jumlahPerbelanjaan": "<PERSON><PERSON><PERSON>", "daftarSumbanganDariLuarNegara": "Daftar sumbangan dari luar negara", "daftarSumbanganKeLuarNegara": "Daftar sumbangan ke luar negara", "listOfContributionsFromInbroad": "Sen<PERSON>i sumbangan ke luar negara", "contributionToInbroadInformation": "Maklumat sumbangan dari luar negara", "penerimaSumbangan": "<PERSON><PERSON><PERSON>", "muatTurunPenyataTahunan": "<PERSON>at turun penyata tahunan", "akuan": "<PERSON><PERSON><PERSON>", "pengakuan_description_1": "<PERSON><PERSON> dengan ini saya mengesahkan bahawa segala maklumat yang diberikan adalah benar. Jika pihak Jabatan Pendaftaran Pertubuhan Malaysia mendapati berlaku penipuan dan kepalsuan dalam keterangan dokumen yang telah saya berikan di atas, saya boleh dikenakan denda tidak melebihi denda RM2000 mengikut Seksyen 54A, <PERSON><PERSON><PERSON> 1966", "pengakuan_description_2": "ii. <PERSON><PERSON> dalam tempoh memegang jawatannya adalah bertang<PERSON> ke atas semua laporan dan maklumat semasa memegang jawatan.", "selenggara": "Maintenance", "semakanDanCarian": "Review and Search", "semakanDanCarian_subtext": "Review of organization related information including current status and blacklist records.", "penarafanPertubuhan": "Organization Rating", "penarafanPertubuhan_subtext": "JPPM evaluates the level of compliance of the organization with the laws and regulations set, as well as the effectiveness of the activities carried out.", "searchnoFoundOrga": "The organization is not on record. Please contact the State JPPM to check the status of your organization.", "informationOnDissolutionOrganizations": "Information on Dissolution of Organizations", "branchSecretary": "Branch Secretary", "aduanCadangan": "Complaint/Suggestion", "kepuasanPelanggan": "Customer Satisfaction", "faqMakluman": "FAQ/Announcement", "noPengenalanDiriPemohon": "Applicant Identification Number", "noPengenalanDiriPembayar": "Payer Identification Number", "jenisPembayaran": "Payment Type", "butiranPembayaranKaunter": "Counter Payment Details", "noResit1": "Receipt Number", "pembayaranKaunterDialogMsg1": "Are you sure the Receipt Number entered is correct?", "pembayaranKaunterDialogMsg2": "Payment record successfully updated", "negeriTerimaBayaran": "State Receiving Payment", "carianAduanCadangan": "Complaint/Suggestion Search", "tarikhTerimaAduan": "<PERSON><PERSON><PERSON><PERSON> Received Date", "muatTurunLaporanAduanCadangan": "Download Complaint/Suggestion Report", "senaraiAduanCadangan": "Complaint/Suggestion List", "tarikhAduanDiterima": "<PERSON><PERSON><PERSON><PERSON> Received Date", "tempohAduanDiterimaHari": "<PERSON><PERSON><PERSON><PERSON> Received Duration (days)", "mula": "Start", "akhir": "End", "maklumatAduanCadangan": "Complaint/Suggestion Information", "tarikhDanMasaAduanDihantar": "Complaint Submission Date and Time", "lampiran": "Attachment", "maklumatPenghantar": "Sender Information", "cawanganJPPM": "JPPM Branch", "sejarahAduanCadangan": "Complaint/Suggestion History", "auditTrailaduan": "Complaint Audit Trail", "paparSejarahAduanCadangan": "View Complaint/Suggestion History", "keputusanAduanCadangan": "Complaint/Suggestion Decision", "penerimaAduan": "<PERSON><PERSON><PERSON>t Recipient", "negeriMenerimaAduan": "State Receiving Complaint", "tindakanPegawai": "Officer's Action", "tahapAduan": "Complaint Level", "catatanPegawaiCaraPengyelesaian": "Officer's Notes (Resolution Method)", "aduanCadanganDialogMsg1": "Are you sure about this complaint decision?", "jumlahMaklumBalasKepuasanPelanggan": "Total Customer Satisfaction Feedback", "total": "Total", "senaraiMaklumBalasKepuasanPelanggan": "Customer Satisfaction Feedback List", "jenisMakluman": "Notification Type", "senaraiFAQ_Makluman": "FAQ/Announcement List", "kandungan": "Content", "tambahFAQ_Makluman": "Add FAQ/Announcement", "penambahanFAQ_Makluman": "FAQ/Announcement Addition", "kategoriPengguna": "User Category", "kategoriSoalan": "Question Category", "soalan": "Question", "jawapan": "Answer", "tajukMaklumanPengumuman": "Announcement/Notification Title", "maklumanPengumuman": "Announcement/Notification", "tarikhMulaMaklumanPengumuman": "Announcement/Notification Start Date", "tarikhAkhirMaklumanPengumuman": "Announcement/Notification End Date", "secretaryName": "Secretary Name", "secretaryPhone": "Secretary's Phone Number (Mobile/Home/Office)", "numberResponsesReceived": "Number of responses received", "pegawai": "Officer", "feedbackDialog_title": "Logout Successful !", "feedbackDialog_subtext1": "Thank you for using the eROSES service.", "feedbackDialog_subtext2": "Rate your experience.", "feedbackDialog_subtext3": "Thank you for your feedback!", "tidakMemuaskan": "Not Satisfied", "memuaskan": "Satisfied", "sangatMemuaskan": "Very Satisfied", "stateOrganization": "State of Organization", "nonCitizenSecretaryInformation": "Non-citizen secretary information", "daftarPenggunaJPPM": "JPPM User Register", "telephoneNoHome": "Telephone Number (Home)", "telephoneNoMobile": "Telephone Number (Mobile)", "externalUserupdate": "External User update", "userCannotActivateEmail": "If the User Still Cannot Activate the Account, Please Copy the Email Content and Send Using Your Own EMAIL Account Sir/<PERSON><PERSON>.", "clickUpdateToResend": "Click UPDATE if you want to resend the EMAIL via the eROSES SYSTEM.", "pendaftaranAkauneROSES": "eROSES Account Registration", "pengaktifanAkauneROSES": "eROSES Account Activation", "pendaftaranAkauneROSESDesc": "You are required to change the temporary password provided with your own password.", "thankYouForRegistering": "Thank you for registering with the e-ROSES System. By registering to use this system, you will be categorized as a User and therefore agree to be bound by these Terms of Use. If you do not agree to these Terms of Use and do not wish to be bound by them, you are advised not to use this system.", "thisEmailIsComputerGenerated": "This email is computer generated and does not require a reply. For any enquiries, please contact the nearest State Organization Registration Department.", "kandunganEMEL": "EMAIL content", "idNumberOnlyDigits": "Must be 12 digits long", "userExists": "This Id is already assigned to an existing user", "senaraiPenggunaMenungguKelulusan": "List of JPPM Users awaiting approval", "senaraiPenggunaJPPM": "List of JPPM users", "senaraiPenggunaLuar": "External user list", "jenis_MaklumBalas": "<PERSON><PERSON><PERSON>", "jenis_Aduan": "<PERSON><PERSON><PERSON><PERSON>", "jenis_IsuSistem": "Eroses System Issue", "jenis_KepuasanPelangan": "Customer Satisfaction", "status_New": "New", "status_InProgress": "In Progress", "status_Complete": "Complete", "status_PendingIT": "Pending IT", "status_Close": "Close", "status_Reopen": "Reopen", "officerName": "Office Name", "keputusanCawangan_penubuhanMenungguPendaftaranCawangan": "Branch Registration Application", "keputusanCawangan_seneraiPenubuhanMenungguPendaftaranCawangan": "List of Branch Registration Applications", "keputusanCawangan_namaPermohonan": "<PERSON><PERSON>", "keputusanCawangan_RO": "RO", "keputusanCawangan_MaklumatPermohonanPenubuhanCawangan": "Information on Application for Establishment of Branch", "namePertubuhanInduk": "Nama of parent organisation", "ppmCawangan": "PPM Cawangan", "tarikhPermohonanLanjutMasa": "<PERSON><PERSON><PERSON>", "maklumatPermohonanLanjutMasa": "Maklumat <PERSON>", "permohonanMenungguKeputusan": "<PERSON><PERSON><PERSON><PERSON>", "senaraiMenungguKeputusanPindaanNamaDanAlamat": "<PERSON><PERSON><PERSON>gu keputusan pindaan nama dan alamat", "maklumatAsalCawangan": "<PERSON><PERSON><PERSON><PERSON> asal cawangan", "namaAsal": "<PERSON><PERSON>", "alamatAsal": "<PERSON><PERSON><PERSON>", "maklumatPindaNamaDanAlamatCawangan": "Maklumat Pinda Nam<PERSON> dan <PERSON><PERSON>", "kodPengenalan": "<PERSON><PERSON>", "permohonanPegawaiAwamCawanganMenunggiKelulusan": "<PERSON><PERSON><PERSON><PERSON> (Menung<PERSON>)", "pengesahanPegawaiAwamCawangan": "Pengesahan Pegawai Awam <PERSON>", "permohonanPegawaiHartaCawanganMenunggiKelulusan": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "pengesahanPegawaiHartaCawangan": "Pengesahan Pegawai Harta Cawangan", "permohonanPembubaranCawangan": "<PERSON><PERSON><PERSON><PERSON>", "maklumatAset": "Maklumat Aset", "nameOfBranchOrganization": "Name of Branch Organization", "numberOfBranches": "Number of Branches", "branchReferenceNo": "Branch reference Number", "branchNameDetails": "Branch name details", "registrationDetails": "Registration Details", "tempohMasaMesyuaratDanAJK": "Time period (meeting and committee)", "approval_waiting_wlist": "<PERSON><PERSON><PERSON> kel<PERSON>an induk", "propertyOfficerConfirmation": "Property Officer Confirmation", "publicOfficialVerification": "Public Official Verification", "officePhoneNumber": "Office phone number", "homePhoneNumber": "Home phone number", "screenList": "Screen List", "read": "Read", "createAdd": "Create/Add", "recordedBy": "Recorded by", "JPPMUserCategoryList": "JPPM User Category List", "ExternalUserCategoryList": "External User Category List", "externalUserCategory": "External user category", "addCategory": "Add Category", "senaraiDokumen": "Documents List", "namaBahagian": "<PERSON><PERSON> b<PERSON>", "senaraiBahagian": "<PERSON><PERSON><PERSON> bah<PERSON>", "kodBahagian": "<PERSON><PERSON> b<PERSON>", "tambahBahagian": "<PERSON><PERSON> bahagian", "aktiviti": "Activiti", "penambahanBandar": "<PERSON><PERSON><PERSON><PERSON>", "wujudDaftar": "<PERSON><PERSON><PERSON> da<PERSON>", "kemaskiniTerakhir": "<PERSON><PERSON><PERSON> terakhir", "namaJawatan": "<PERSON><PERSON> jaw<PERSON>n", "gredJawatan": "<PERSON><PERSON>n", "senaraiJawatan": "<PERSON><PERSON><PERSON> jawatan", "penambahanJawatan": "<PERSON><PERSON><PERSON><PERSON>", "kemaskiniJawatan": "Kemaskini Jawatan", "keteranganJawatan": "Keterangan Jawatan", "namaGred": "<PERSON><PERSON> gred", "kodGred": "<PERSON>d gred", "keteranganGred": "Keterangan Gred", "senaraiGred": "<PERSON><PERSON><PERSON>", "tambahGred": "Tambah Gred", "penambahanGred": "<PERSON><PERSON><PERSON><PERSON>", "kemaskiniGred": "Kemaskini Gred", "namaPekerjaan": "<PERSON><PERSON>", "kodPekerjaan": "<PERSON><PERSON>", "keteranganPekerjaan": "Keterangan pek<PERSON>", "senaraiPekerjaan": "<PERSON><PERSON><PERSON>", "tambahPekerjaan": "<PERSON><PERSON> pek<PERSON>", "penambahanPekerjaan": "<PERSON><PERSON><PERSON><PERSON>", "kemaskiniPekerjaan": "<PERSON><PERSON><PERSON>", "namaNegara": "<PERSON><PERSON> negara", "kodNegara": "Kod negara", "shortKodNegara": "Short Code Country", "senaraiNegara": "<PERSON><PERSON><PERSON> negara", "tambahNegara": "Tambah negara", "penambahanNegara": "Penambahan negara", "kemaskiniNegara": "Kemaskini negara", "namaNegeri": "<PERSON><PERSON> negeri", "kodNegeri": "<PERSON><PERSON>", "shortKodNegeri": "<PERSON><PERSON>", "senaraiNegeri": "<PERSON><PERSON><PERSON> negeri", "tambahNegeri": "Tambah negeri", "penambahanNegeri": "<PERSON><PERSON><PERSON><PERSON> negeri", "kemaskiniNegeri": "Ke<PERSON><PERSON> negeri", "namaDaerah": "<PERSON><PERSON> da<PERSON>h", "kodDaerah": "<PERSON><PERSON> da<PERSON>h", "shortKodDaerah": "<PERSON><PERSON> pendek daerah", "senaraiDaerah": "<PERSON><PERSON><PERSON> da<PERSON>h", "tambahDaerah": "Tambah daerah", "penambahanDaerah": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>h", "kemaskiniDaerah": "<PERSON><PERSON><PERSON> da<PERSON>h", "namaBandar": "<PERSON><PERSON> bandar", "kodBandar": "Kod bandar", "senaraiBandar": "<PERSON><PERSON><PERSON> bandar", "tambahBandar": "Tambah bandar", "kemaskiniBandar": "Kemaskini bandar", "namaAgama": "<PERSON>a agama", "kodAgama": "<PERSON><PERSON> agama", "senaraiAgama": "<PERSON><PERSON><PERSON> agama", "tambahAgama": "Tambah agama", "kemaskiniAgama": "<PERSON><PERSON><PERSON> agama", "namaKeturunan": "<PERSON><PERSON> k<PERSON>", "kodKeturunan": "<PERSON>d ket<PERSON>", "senaraiKeturunan": "<PERSON><PERSON><PERSON> k<PERSON>", "tambahKeturunan": "Tambah keturunan", "kemaskiniKeturunan": "Kemaskini keturunan", "tarikhDiterima": "<PERSON><PERSON><PERSON>", "tarikhDihantar": "<PERSON><PERSON><PERSON>", "jenisCuti": "<PERSON><PERSON>i", "namaCuti": "<PERSON><PERSON> cuti", "namaJabatanInsolvensi": "Nama Jabatan Insolvensi", "tambahJabatanInsolvensi": "Tambah Jabatan Insolvensi", "kodInsolvensi": "<PERSON><PERSON>", "penambahanJabatanInsolvensi": "Penambahan Jabatan Insolvensi", "kemaskiniJabatanInsolvensi": "Kemaskini Jabatan Insolvensi", "kodCawangan": "<PERSON><PERSON>", "penambahanCawanganJPPM": "<PERSON><PERSON><PERSON><PERSON>ngan JPPM", "kemaskiniCawanganJPPM": "Kemaskini Cawangan JPPM", "tambahCawanganJPPM": "Tambah Cawangan JPPM", "historyInquiry": "History of Inquiry", "secretaryInformation": "Secretary Information", "secretaryAddress": "Secretary Address", "reasonForSecretaryChange": "Reason for Secretary Change", "otherReason": "Other reason", "pendaftaranPertubuhanInduk": "Pendaftaran pertubuhan induk", "pendaftaranCawangan": "Pendaftaran cawangan", "pindaanUndangUndangInduk": "Constitutional Amendment", "senaraiKuiri": "<PERSON><PERSON><PERSON>", "kategoriPertubuhan": "<PERSON><PERSON><PERSON>", "kuiriPendaftaranPertubuhanInduk": "<PERSON><PERSON>an <PERSON>", "pemohon": "<PERSON><PERSON><PERSON><PERSON>", "noPertubuhan": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "kuiriPendaftaranCawangan": "Kuiri Pendaftaran Cawangan", "kuiriPindaanUndangUndangInduk": "Constitutional Amendment Query", "kuiriRayuan": "<PERSON><PERSON>", "kuiriPembaharuanSetiausaha": "<PERSON><PERSON>", "frequencyOrdinaryGeneralMeetings": "Frequency of ordinary general meetings", "numberOfAuditors": "Number of auditors", "realEstateAdministration": "Real estate administration", "deadlineForOrdinaryGeneralMeeting": "Deadline for the ordinary general meeting", "feedbackFromSupremeCommittee": "Fe<PERSON>back from the Supreme Committee", "lastDateOrdinaryGeneralMeeting": "Last date of the ordinary general meeting", "docList": "<PERSON><PERSON><PERSON>", "statusDokumen": "Document Status", "choose": "<PERSON><PERSON><PERSON>", "noRecordForStatus": "<PERSON><PERSON><PERSON>an tiada dalam rekod. Sila hubungi JPPM Negeri untuk menyemak status pertubuhan anda.", "clauseContentOri": "<PERSON><PERSON><PERSON><PERSON>", "alirantugasPopText": "Are you sure you want to activate the Committee & Membership Management task flow from this Committee?", "pindaanRemindText": "Please fill in the information for the constitutional amendment meeting.", "here": "disini", "pindaanRemindText1": "bagi kemasukan maklumat mesyuarat terlibat.", "PurposeOfConstitutionalAmendment": "<PERSON><PERSON><PERSON> pin<PERSON>an <PERSON>", "decisionOfAppealApplication": "Decision of Appeal Application", "kelulusanRayuanTabTitle1": "Cancellation of registration of society under section 2A", "kelulusanRayuanTabTitle2": "Refusal to register the society under section 7", "kelulusanRayuanTabTitle3": "Refusal to grant exemption under section 9A (4)", "kelulusanRayuanTabTitle4": "Refusal to approve amendment to its rules under section 11", "kelulusanRayuanTabTitle5": "Refusal to approve establishment of a branch of society under section 12", "kelulusanRayuanTabTitle6": "Order prohibiting non-citizens section 13A (1)", "listOfApealApplications": "List of Appeal Applications", "applicant": "Applicant", "orgAppealApplicationInfo": "Organization appeal application information", "detailsForAppeal": "Details/reasons for appeal", "showConstitution": "Show the constitution", "showWhiteningHistory": "Show Whitening History", "selectStatementYear": "Select statement year", "queryTo": "Query To", "letterNo": "Letter No.", "rekodDijumpai": "<PERSON><PERSON><PERSON>", "senaraiPertubuhan": "<PERSON><PERSON><PERSON>", "noPPM": "No. PPM", "statusMigrasi": "Status Migrasi", "namaPersatuan": "<PERSON><PERSON> persatuan", "tarikhDidaftar": "<PERSON><PERSON><PERSON> didaftar", "tarikhBatal": "<PERSON><PERSON><PERSON>", "jikaAda": "<PERSON><PERSON> ada", "tarikhLulusPindaan": "<PERSON><PERSON><PERSON> lulus pindaan", "paparSenaraiPindaan": "<PERSON><PERSON> <PERSON><PERSON>", "paparSejarahPembatalan": "<PERSON><PERSON>", "notaPengarah": "<PERSON>a pen<PERSON>", "tarikhAlihPermohonan": "<PERSON><PERSON><PERSON> alih permohonan", "maklumatPembaharuanSetiausaha": "Maklumat Pembaharuan <PERSON>", "migrasi": "<PERSON><PERSON><PERSON>", "kekerapanMesyuaratAgungBiasa": "Kekerapan Mesyuarat Agung Biasa", "tarikhAkhirMesyuaratAgungBiasa": "<PERSON><PERSON><PERSON> untuk Mesyuarat Agung Biasa", "bilanganJuruaudit": "Bilangan juru<PERSON>it", "pentadbiranHartaTakAlih": "Pentadbiran Harta Tak Alih", "petaLokasiTempatUrusan": "Peta lokasi tempat urusan", "nomborFaks": "Nombor faks", "maklumatKebajikan": "Maklumat Kebajikan", "rekodBukanWarganegaraDijumpai": "Rekod bukan warganegara dijumpai", "tarikhCipta": "<PERSON><PERSON><PERSON> cipta", "maklumatBukanWarganegara": "Maklumat bukan warganegara", "pegawaiYangMeluluskan": "Approving officer", "noHPRumah": "No. HP/Rumah", "emelSetiausaha": "<PERSON><PERSON>", "sijilMigrasiCawanganPenukarSUCawangan": "Si<PERSON>l <PERSON>n/ Penukaran SU Cawangan", "senaraiPindaanNamadanAlamatCawangan": "<PERSON><PERSON><PERSON> dan <PERSON><PERSON>", "pemegangJawatan": "<PERSON><PERSON>egang jawatan", "ahliBukanWarganegara": "<PERSON><PERSON> bukan wargan<PERSON>ara", "senaraiCawangan": "<PERSON><PERSON><PERSON>", "nomborCawangan": "No Cawangan", "pegawaiBertanggungjawab": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "catatanJustifikasiKeputusan": "Catatan/Justifikasi Keputusan", "maklamatMesyuarat": "Matlamat <PERSON>ua<PERSON>", "paparMinitMesyuarat": "<PERSON><PERSON>", "senaraiPenukaranICSUCawanganMigrasi": "Senarai Penukaran IC SU Cawangan Migrasi", "senaraiAJKCawangan": "Senarai AJK Cawangan", "namaPemegangAwam": "<PERSON><PERSON> pem<PERSON>g awam", "maklumatNamaPegawai": "Mak<PERSON>at nama pegawai", "noTelefonHPRumahPejabat": "No. telefon (HP/ Rumah/ Pejabat)", "pegawaiHarta": "Pegawai harta", "negeriCawangan": "<PERSON><PERSON><PERSON>", "kemaskiniICSUCawangan": "Kemaskini IC SU Cawangan", "noPPMLama": "No PPM lama", "namaSUSemasa": "Nama SU semasa", "noKPSemasa": "No KP Semasa", "icBaru": "IC baru", "namaBaru": "<PERSON>a baru", "sijilMigrasiCawanganPeringatan": " Halaman ini memaparkan senarai cawangan MIGRASI yang telah mempunyai Setiausaha. Klik pada ikon “pensil” untuk kemaskini nama dan no pengenalan SU Cawangan.", "maklumatCawanganSemasa": "<PERSON><PERSON><PERSON><PERSON> cawangan semasa", "tarikhDibenarkan": "<PERSON><PERSON><PERSON>", "tarikhDitubuhkan": "<PERSON><PERSON><PERSON>", "statusCawanganSemasa": "Status cawangan semasa", "kemaskiniNamaCawangan": "<PERSON><PERSON><PERSON> nama cawangan", "noPPMLamaCawangan": "No PPM lama cawangan", "namaCawanganSemasa": "<PERSON><PERSON> ca<PERSON>an se<PERSON>a", "namaCawanganBaru": "<PERSON>a cawangan baru", "kemaskiniStatusCawanganSemasa": "Kemaskini status cawangan semasa", "statusSemasa": "Status semasa", "statusBaru": "Status baru", "pertubuhanKemaskiniPeringatan": " <PERSON><PERSON> boleh men<PERSON> maklum<PERSON>.", "maklumatPertubuhanSemasa": "Ma<PERSON><PERSON><PERSON> pertubuhan semasa", "statusPertubuhanSemasa": "Status pertubuhan semasa", "kategoriSemasa": "<PERSON><PERSON><PERSON>", "subKategoriSemasa": "Sub-kate<PERSON>i semasa", "tarikhDaftar": "<PERSON><PERSON><PERSON> da<PERSON>ar", "kemaskiniNamaPertubuhan": "<PERSON><PERSON><PERSON> nama per<PERSON>an", "namaAsalPertubuhan": "<PERSON><PERSON>", "namaSemasa": "<PERSON><PERSON> se<PERSON>a", "kemaskiniAlamatPertubuhan": "<PERSON><PERSON><PERSON> al<PERSON><PERSON>", "kemaskiniAlamatSuratMenyuratPertubuhan": "Ke<PERSON><PERSON> alamat surat menyurat per<PERSON>an", "kemaskiniStatusPertubuhan": "Kemaskini status pertubuhan", "kemaskiniKategoriPertubuhan": "<PERSON><PERSON><PERSON> kate<PERSON>i <PERSON>", "kategoriBaru": "<PERSON><PERSON><PERSON> baru", "subKategoriBaru": "Sub kategori baru", "kemaskiniTarafPertubuhan": "<PERSON><PERSON><PERSON> ta<PERSON><PERSON>", "tarafSemasa": "<PERSON><PERSON>", "tarafBaru": "<PERSON><PERSON> baru", "annualStatementRecord": "Annual statement record", "diluluskanOleh": "Application Name", "maklumatPermohonanPembubaranPertubuhan": "Maklumat <PERSON>", "sebabRayuan": "<PERSON><PERSON><PERSON>", "noPengenalanSetiausaha": "No. pengenalan setiausaha", "pppBertanggungjawab": "PPP Bertanggungjawab/syor", "kppBertanggungjawab": "KPP Bertanggungjawab/syor", "pendaftarSyor": "Pendaftar/syor", "statusTahunPenyata": "Status of Statement of Year", "maklumatTahunPenyata": "Mak<PERSON>at tahun penyata", "tahunPenyataSemasa": "<PERSON><PERSON> penyata semasa", "paparPeyataTahunan": "<PERSON><PERSON>", "noPendaftaranLama": "No pendaftaran lama", "senaraiKelabu": "<PERSON><PERSON><PERSON> k<PERSON>", "senaraiHitamKeputihan": "<PERSON><PERSON><PERSON> hitam / keputihan", "semakanIndividu": "Semakan individu", "semakanNoPendaftaranLama": "Semakan no pendaftaran lama", "noPPP": "No PPP", "semakanNoPengenalanDiriPemegangJawatan": "Semakan No. Pengenalan Diri Pemegang Jawatan", "maklumatPegawaiPenyemak": "Maklumat pegawai penyemak", "noPengenalanDiriPenyemak": "No. Pengenalan Diri penyemak", "namaPegawaiPenyemak": "Name pegawai <PERSON>k", "tarikhDanMasaSemak": "<PERSON><PERSON><PERSON> dan masa semak", "namaIndividu": "<PERSON><PERSON> individu", "noPengenalanBaru": "No. pengenalan baru", "noPengenalanLama": "No. pengenalan lama", "tarikhMati": "<PERSON><PERSON><PERSON> mati", "jikaBerkanaan": "<PERSON><PERSON>", "tarafPenduduk": "<PERSON><PERSON>", "alamatTetap": "<PERSON><PERSON><PERSON> tetap", "bilanganPertubuhanTerkini": "Bilangan per<PERSON> te<PERSON>i", "pertubuhanYangDianggotai": "<PERSON><PERSON><PERSON><PERSON> yang <PERSON>", "statusPenama": "Status penama", "bilanganCawanganTerkini": "Bilangan cawangan terkini", "cawanganYangDianggotai": "Cawangan yang di<PERSON>ai", "additionSecretaryRenewal": "Secretary <PERSON><PERSON>", "applicationName": "Application Name", "ROAction": "RO action", "JPPMComments": "JPPM Comments", "loading": "Loading", "branchRegistrationSteps": "Branch Registration Steps", "organizationMeeting": "Organization Meeting", "enableConfirmation": "Are you sure you want to enable this function?", "disableConfirmation": "Are you sure you want to disable this function?", "applicationSuccessfulySubmited": "Your application has been successfully submitted", "actualPosition": "Actual Position", "acceptMemberApplication": "Are you sure you want to accept this member's application?", "rejectMemberApplication": "Are you sure you want to reject this member's application?", "membershipApplicationSuccessful": "Membership application has been successful", "inCountry": "In the country", "abroad": "Aboard", "notWorking": "Not Working", "working": "Working", "associationAwaitingApproval": "Associations awaiting approval from the parent company", "branchNameHelper": "The name of the Organization must begin with Organization, Association or Club.", "memberAttendance": "Member Attendance", "organisationAppeal": "Organization appeal", "meetingAttendanceNotice": "Users are encouraged to fill in the attendance information of Committee Members", "mohon": "Request", "checkFeedbacks": "Check suggestions and feedback", "searchLocation": "Search Location", "invalidName": "Error : Identity Card Number and Name do not match as in MyKad.", "phoneNumberLessThan7": "Ralat : Phone number must be at least 7 digits.", "Thepurposeofthemeetingisto": "The purpose of the meeting is to…", "maklumatMesyuarat2": "Meeting information", "totalAttendMember": "Number of Meeting Members Present", "passwordRequirements2": "Enter at least 8 characters with a combination of uppercase letters.", "meetingLocation": "Meeting Location", "alamatTempatUrusan1": "Business Address", "registerNewUser": "Register New User", "byContinuingYouAgreeTo": "By continuing, you agree to the", "myKadNo": "MyKad Number", "myKadPlaceholder": "MyKad/MyPR/Passport Number", "OTPDialogConfTitle": "An OTP code has been sent to your email", "OTPDialogConfContent": "Please check your registered email and set a new password", "OTPVerification": "OTP Verification", "resetSuccessful": "Password reset successfully", "phoneNoVerification": "Phone number verification", "passwordSettingSuccessful": "Password setting successful.", "emailOTPFailed": "The code entered is incorrect, please try again.", "emailVerified": "Your email has been verified.", "nonCitizenIdNo": "User ID Number", "timRangeAlertMsg": "Start time cannot exceed the end time.", "emailVerificationSuccessful": "Email OTP verification Successful", "phoneVerificationSuccessful": "Phone Number OTP verification Successful", "emailVerificationFailed": "Email OTP verification failed", "phoneVerificationFailed": "Phone Number OTP verification failed", "veryWeak": "Very weak", "weak": "weak", "fair": "fair", "good": "Good", "strong": "Strong", "applicationSuccessDeleted": "Your application has been successfully deleted.", "applicationSuccessSubmited": "Your application has been successfully submitted.", "confirmDeleteApplication": "Are you sure you want to delete this application?", "confirmDeleteBranch": "Are you sure you want to delete the branch \"{{name}}\"?", "participation": "Quick Links", "association": "Association", "seeFullView": "See full view", "chatBox": "ChatBox", "chatWithKakRos": "<PERSON><PERSON> with <PERSON><PERSON>", "registerNameToolTip": "Please enter your full name as shown on your MyKad.", "registerNameToolTip2": "Please enter your full name.", "tarafPenubuhan": "Establishment status", "hari": "day", "mesyuaratPenubuhan": "Establishment Meeting", "branchNameHelper1": "Branch Name must begin with Branch, Division, State or District", "newBranchSecretaryInformation": "New Branch Secretary Information", "pleaseSelectRole": "Please select your role", "pleaseSelectNationality": "Please select your nationality to continue registration", "phoneNumberIncorrectFormat": "Error : The phone number entered is not in the correct format. Please re-enter it in the format provided.", "emailNumberIncorrectFormat": "Error : The email entered is not in the correct format. Please re-enter it in the correct format.", "exampleEmail": "<EMAIL>", "userExistsError": "Error : This user is already registered in the system.", "phoneExistsError": "Error : The phone number already exists in the system. Please use another number.", "emailExistsError": "Error : This email is already registered. Please use another email.", "integrationOffError": "Sorry. User registration is temporarily unavailable. Please try again in 4 hours. Thank you.", "terima": "Accept", "membershipAccpetApplicationSuccessful": "Membership application has been successful.", "rulePassword3": "Enter at least 8 characters with a combination of uppercase, lowercase, and special characters.", "rowsPerPage": "Rows per page:", "rowsPerPageListCategoryJPM": "$t(rowsPerPage)", "dateAndBirthPlace": "Date & place of birth", "payOnlineRenewIn24": "The annual statement must be submitted within 60 days after the General Meeting or within 60 days after the end of the calendar year if no general meeting is held in that year.", "pleaseReferState": "Please refer to nearby states if fees do not change.", "lanjutMasaWarning1": "All fields are marked.", "lanjutMasaWarning2": "must be filled in. Extension of time is allowed only once.", "renewalDate": "Renewal Date", "newBranchSecretary": "New branch secretary", "oldBranchSecretary": "Old branch secretary", "validation": {"required": "This field is required", "meetingTimeToInvalid": "The start time cannot exceed the end time.", "mustBeNumber": "This field must be a number.", "minValue": "Value must be at least {{value}}.", "invalidTimeDuration": "Invalid time duration", "documentSize": "File size exceeds the limit of {{maxSize}}MB. Please upload a smaller file", "invalidFileType": "Invalid file type. Please upload a supported file."}, "pindaanApproverTitle": "Association awaits approval of constitutional amendment application", "minute": "Minute", "permohonan": "Application", "pengerusiCawangan": "Branch Chairperson", "presidenCawangan": "Branch President", "pengarahCawangan": "Branch Director", "timbalanPresiden": "Deputy President", "naibPengerusiCawangan": "Branch Vice Chairperson", "naibPresidenCawangan": "Branch Vice President", "naibPengarahCawangan": "Branch Vice Director", "setiausahaAgung": "Secretary-General", "setiausahaCawangan": "Branch Secretary", "penolongSetiausahaAgung": "Assistant Secretary-General", "penolongSetiausahaCawangan": "Branch Assistant Secretary", "bendahariAgung": "General Treasurer", "ketuaBendahari": "Chief Treasurer", "bendahariKehormat": "Honorary Treasurer", "bendahariCawangan": "Branch Treasurer", "penolongBendahariAgung": "Assistant General Treasurer", "penolongKetuaBendahari": "Assistant Chief Treasurer", "penolongBendahariKehormat": "Assistant Honorary Treasurer", "penolongBendahariCawangan": "Branch Assistant Treasurer", "ahliJawatanBiasa": "Ordinary Committee Member", "lainLain": "Others", "biroEkonomi": "Economic Bureau", "biroKebajikan": "Welfare Bureau", "biroSukanDanSosial": "Sports and Social Bureau", "biroAgama": "Religious Bureau", "pindaanNameAlamatBranch": "Change of branch name and address", "pinda": "Amend", "choosePindaan": "Select branch for name and address change", "pinda_nama_alamat": "Change name and address", "pinda_nama": "Change name", "pinda_alamat": "Change address", "branchnameChangeInfo": "Branch name change information", "branchnameaddresschange": "Branch name and address change information", "areYouSureOfTheApplicationOutcome": "Are you sure about the outcome of this application?", "meetingPindaanTitle": "Meeting information approving amendments", "reminderPindaanTitle": "If the Meeting List is not available, click here to enter meeting information, change branch name and address.", "selectAmendBranch": "Select branch changes", "catatan300": "Note (has 300 words)", "masaUlusan": "Review time", "persatuanMenungguUlasanLuar": "Association awaiting Parent approval awaiting external review", "senaraiMenungguUlasanLuar": "Parent approval waiting list - awaiting external review", "sejarahUlasanLuar": "History of external agency reviews", "OrganizationNo": "Organization No", "resultdateif": "Result Date (If any)", "clear": "Clear", "roleNameAlreadyInUse": "Error: This User Category already exists.", "noAvailableDocuments": "No Available Documents", "ViewMeetings": "View Meetings", "updateConstitution": "Update the Constitution", "comfirmUpdatePindaan": "Are you sure you want to update this organization's constitution type? If so, the previously filled-in information will be deleted.", "individualAppeal": "Individual Appeal", "rayuanQA1": "An appeal to the Minister may be made within thirty days from the date of the decision.", "rayuanQA2": "The applicant is required to submit supporting documents and an application fee of RM50.00.", "PADAM": "Delete", "-": "-", "BUBAR_SEMENTARA": "Temporarily Dissolved", "BARU": "New", "TIDAK_AKTIF": "Not Active", "DILUPUSKAN": "Disposed", "DIGUGURKAN": "Dropped", "TIADA_MAKLUMAT": "No Information", "TEST": "Test", "PINDA_NAMA": "<PERSON><PERSON>", "PERTELINGKAHAN_SIASATAN": "Dispute Investigation", "TIDAK_AKTIF_PINDAH": "Not Active Transferred", "DALAM_PERHATIAN": "Under Attention", "TIDAK_AKTIF1": "Not Active 1", "DILUPUTKAN": "Forgotten", "CancellationRegistration2A": "Cancellation of registration of an organisation under Section 2A.", "RejectionApplication7": "Rejection of application for registration under Section 7.", "RejectionExemption9A1a_9A1b_9A1c": "Rejection of application for exemption of Section 9A(1)(a), 9A(1)(b) and 9A(1)(c).", "RejectionApplication11": "Rejection of application for amendment of the Law Section 11.", "RejectionApplicationBranch12": "Rejection of application for establishment of branch under Section 12.", "CancellationOrganisation13": "Cancellation of registration of an organisation under Section 13.", "Order_13A1": "Order 13A(1).", "Order_13A2": "Order 13A(2).", "RejectionApplicationAppointment144": "Rejection of application appointment of auditor under Section 14(4).", "Order_145": "Order 14(5).", "CancellationOrganisation16": "Cancellation of registration of organisation under Section 16.", "RejectionApplicationOffice49": "Rejection of application to hold office for those blacklisted under Section 49.", "mykad/lesen": "<PERSON><PERSON><PERSON>/<PERSON><PERSON>", "emelSyarikat": "<PERSON><PERSON>", "senaraiPermintaan": "Request List", "successful": "Successful", "ascending": "Ascending", "descending": "Descending", "KandunganPenuh": "Full Content", "senaraiDataDiperlukan": "Required data list", "applicationStatusCode": "Application Status", "paymentYear": "Payment Date", "pleaseUpdateFasalText": "Please update the content of your clause here.", "appealWaitingList": "Appeal Waiting List", "rayuanKaunterAlert": "Please clarify the payment at the counter within 30 days from the application date.", "errorUserExistUserCreation": "Users with this information are already registered.", "markAsRead": "<PERSON>", "noMoreNotification": "No more notifications", "noMoreNotification2": "We will let you know when there is something new for you.", "seeAllNotifications": "See all notifications", "hariIni": "Today", "semalam": "Yesterday", "7HariTerakhir": "Last 7 Days", "sebulanTerakhir": "Last Month", "sebelumnya": "Previous", "changeBranchName": "Branch name change information", "changeBranchAddress": "Branch address information", "confirmdeletefasalText": "Are you sure you want to delete this clause?", "deleteAhliConfirmation": "<PERSON><PERSON>h anda pasti untuk memadam rekod ahli ini?", "non-citizen": "Non-Citizen", "reminderPindaanTitle1": "If Meeting List is not in the options, click", "reminderPindaanTitle2": "for entering meeting information, amendment of branch name and address.", "clickhere": "here ", "IcDoesNotExist": "This identification number does not exist", "nonCitizenDecisionWaitingList": "Non-citizen decision waiting list", "applicationAwaitingDecision": "Application awaiting decision", "stepAmendmentBranchNameAndAddress": "Steps for changing branch name and address", "fillMeeting": "Meeting content", "selectBranchAmend": "Select amendment", "bacaanBerkaitan": "Related Article", "penyampaianPerkhidmatanFooter": "Delivery of Services", "success": "Success", "error": "Error", "sureToSubmitFeedback": "Are you sure to submit this feedback?", "personalIdNo": "Personal Identification Number", "usereROSESv2": "User verification of the eROSES System version 2.0", "AddClause": "Ad<PERSON>", "confirmBuyDokumen": "Are you sure you want to purchase this document?", "geran": "<PERSON>", "Pengumuman": "Announcement", "contohJan": "Example: January 1", "KebangsaanPopText": "Kebangsaan: Membership must be from at least 7 different states.", "negeriPopText": "State: Membership must be from the same state.", "lainlainPopText": "Other: Free to be composed of members from any state.", "pleaseEnterName": "Please enter a name", "pleaseSelectCategory": "Please select a category", "pleaseSelectState": "Please select a state", "pleaseSelectStatus": "Please select a status", "recordSuccesDeleted": "Your record has been successfully deleted", "branchNameDetails1": "Branch Name", "confirmSubmitStatementText": "Are you sure you want to submit this annual return?", "confessionRecorded": "confession recorded", "NEW": "NEW", "IN_PROGRESS": "In Progress", "COMPLETE": "Complete", "PENDING_IT": "Pending IT", "CLOSE": "Close", "REOPEN": "Reopen", "AJKNoUpdateConfirmation": "Are you sure you want to update the number of committee members? This action will affect the list of Committee members", "seorang": "a single", "tukarSetiausaha": "Change Secretary", "exampleYearPlaceholder": "Example: 1 year", "messageKeputusanPermohonanSuccessPindaanName": "The result of the application for amendment of branch name and address was successfully sent.", "messageKeputusanPermohonanErrorPindaanName": "The result of the application for amendment of branch name and address failed to be sent.", "messageKeputusanPermohonanSuccessLanjutmasa": "Time Extension Application successfully sent", "messageKeputusanPermohonanErrorLanjutmasa": "Time Extension Application failed to send", "stateOfBirth": "State of Birth", "max2000": "Maximum: RM2,000", "meetingName2": "Name of meeting place", "personalIdNo2": "Personal Identification Number", "memberRegistrationDate": "Member Registration Date", "memberType": "Member Type", "noNBIDCawangan": "Branch NBID No.", "VIEW_ALL": "View All", "ahliBersekutu": "Associate Member", "ahliKehormat": "Honorary Member", "seumurHidup": "Lifetime", "ahliRemaja": "Youth Member", "alamatSuratMenyuratCawangan": "Branch Correspondence Address", "alamatTempatUrusanCawangan": "Branch Business Address", "peringatanCapFirst": "Reminder", "pertubuhanAnda": "<PERSON><PERSON><PERSON><PERSON>", "organizationManagementSystem": "Organization Management System", "amendmentsConstitutionAlert": "Any Constitutional Amendment must be submitted to the registrar via the eROSES system within 60 days after the meeting that decided on the constitutional amendment.", "moreThan7ajk": "Number of AJK must be more than 7", "nomorethan31": "cannot be more than 31", "ordinaryCommitteeMember1": "Ordinary Branch Committee Member", "thepresident": "President", "thepresidentBranch": "Branch President", "Exceededtheperiod": "Exceeded the period", "pegawaiAwamMainTitle": "Civil servant application (awaiting approval)", "pengesahanPegawaiHarta": "Property officer confirmation", "confirmDeleteAudit": "Are you sure you want to delete this auditor?", "return": "Return", "auditorDeletedSuccessfully": "Auditor successfully deleted", "auditorAddConfirmation": "Are you sure you want to add this auditor ?", "auditorCreatedSuccessfully": "Auditor successfully added", "timbalanyYangDipertua": "Deputy <PERSON>", "naibYangDipertua": "Vice-President", "fasalCawangan": "Branch", "branchName": "Branch Name", "pengurusanPerlembangaanPayment": "Application for amendment of law has been recorded", "appealPayment": "The appeal application has been recorded", "newBranchName": "New Branch Name", "newBranchBusinessAddress": "New Address of Branch Business Place", "newBranchCorrespondenceAddress": "New Branch Correspondence Address", "BranchCorrespondenceAddress": "Branch Correspondence Address", "jobInformation": "Job Information", "workAddress": "Work Address", "noTelefon": "Phone No.", "noSyarikat": "Comapny No.", "companyAddress": "Company Address", "memberApprovalDate": "Member Registration Date (Member Approval Date)", "membershipNo": "Membership No.", "noTelephone": "Phone No.", "DivisionStateJPPMBranch": "Division / State / JPPM Branch", "noncitizenRegList": "Waiting list for non-citizen approval", "pegawaiAwamRegList": "Waiting list for approval of public officials", "pegawaiHartaRegList": "Waiting list for approval of property officials", "createMeetingReminder": "Please fill out the meeting in Meeting Management first.", "penambahanKalendar": "Create <PERSON><PERSON>", "identityCardNo": "Identity Card No.", "Viewlist": "View List", "confirmDeleteContribution": "Are you sure you want to delete this contribution", "meetingInfoConfirmation": "Confirm the information is correct", "roDirectorNote": "RO/Director's Note", "rayuanQA3": "Appeal queries must be answered within seven days from the date the query is received.", "confirmDeletePenyata": "Are you sure you want to delete this annual statement?", "furnitofficeEquipmentSuppliesure": "Office Equipment Supplies", "NumberofPositions": "Number of Positions", "JobTitle": "Job Title", "roleName": "Role Name", "completedAuditedFinancialStatement": "Completed audited financial statements", "uploadFinancialStatements": "Upload Financial Statements", "DownloadFinancialStatementTemplate": "Download Financial Statement Template", "confirmDeactivateTaskFlowPenyataTahunan": "Are you sure to deactivate the Annual Statement task flow from this Committee?", "confirmactivateTaskFlowPenyataTahunan": "Are you sure to activate the Annual Statement task flow from this Committee?", "confirmDeactivateTaskFlowMesyuarat": "Are you sure to deactivate the Meeting Management task flow from this Committee?", "confirmactivateTaskFlowMesyurat": "Are you sure to activate the Meeting Management task flow from this Committee?", "confirmDeactivateTaskFlowPerlembagaan": "Are you sure to deactivate the Constitution Management task flow from this Committee?", "confirmactivateTaskFlowPerlembagaan": "Are you sure to activate the Constitution Management task flow from this Committee?", "confirmDeactivateTaskFlowAJK": "Are you sure to deactivate the Committee & Membership task flow from this Committee?", "confirmactivateTaskFlowAJK": "Are you sure to activate the Committee task flow & Membership from this Committee?", "ApplicationApproved": "Application Approved", "ApplicationRejected": "Application Rejected", "ApplicationRequestInquiry": "Request Inquiry", "activityReport": "Activity Report", "updateActivityReport": "Upload Activity Report", "organizationalActivitiesAppendix": "Organizational Activities Appendix", "downloadReference": "Download Reference", "module": "<PERSON><PERSON><PERSON>", "noRecordDataLandingSearchTable": "No organization records", "termsConditionsTitle": "Terms of Use of the Malaysian Societies Registration Department Electronic System (eROSES V2.0)", "cancellationDate": "Cancellation Date", "typeOfNotice": "Type of Notice", "descriptionNotice": "Notice Description", "organizationCommittee": "Organization Committee", "organizationalDecisions": "Organizational Decisions", "societyDocumentInfo": "1. Land ownership documents\n2. Utility bills\n3. Photograph of the premises (front) if available\n4. Departmental authorization letter if the organization is under the responsibility of the government", "RegisteraNewOrganization": "Register a New Organization", "nonCitizenDocumentInfo": "Copy of the Insolvency Department's Bankruptcy Status Search Result for individuals who have been discharged from bankruptcy status", "hadMaksimumWangDalamTangan": "<PERSON><PERSON><PERSON><PERSON>", "morethan": "more than", "lessthan": "less than", "insertLink": "Insert Link", "appointment": "Appointment", "appointmentInfo": "Appointment Information", "loginMyDigitalId": "Login/ Register with MyDigitallD", "verificationInProgress": "Verification in progress", "userDoesntExist": "The identification number does not exist in eRoses, please ask the identification number holder to create an eRoses account.", "recordOfActivityAvailable": "There is a record of activity", "completeReportUpload": "Please complete the report using the template provided and upload pictures of the organization's activities.", "inactiveUserList": "Inactive User List", "auditorAppointmentDate": "Auditor Appointment Date", "appointmentDateAudit": "Appointment Date", "phoneDigitLimitWarning": "Please enter a valid Malaysian phone number (maximum 12 digits and starting with +60).", "blacklistedMsg": "User has been blacklisted.", "appointmentLetter": "Appointment Letter", "appealPeriod": "Appeal Period", "jawatankuasaConfirmation": "I hereby confirm that all committee members of the above organization are not disqualified from holding office under section 9A of the Organizations Act 1966. I further declare that the information given above is true.", "userStatusActive": "Active", "userStatusPending": "Awaiting Decision", "userStatusInactive": "Inactive", "userStatusDeactivated": "Deactivated", "sejarahAkaun": "Historical Account", "klikUntukLihatSenaraiPendaftaran": "Click to View Registration List", "userIdRequired": "User ID is required", "pengesahanPerubahanStatus": "Confirmation of Status Change", "confirmActiveUser": "An active user will be able to log in. Are you sure you want to set the status to Active?", "confirmInactiveUser": "An inactive user will not be able to log in. Are you sure you want to set the status to Inactive?", "confirmDeactivateUser": "A deactivated user will not be able to log in and will need to register a new account. Are you sure you want to deactivate this user?", "confirmDeleteUser": "Are you sure you want to delete this user?", "deleteUserSuccess": "User successfully deleted", "deleteUserError": "Failed to delete user", "noFasal": "Clause No.", "tajukfasal": "Clause Title", "yangDipertua": "Chairperson", "yangDipertuaCawangan": "Branch Chairperson", "timbalanYangDipertua": "Deputy Chairperson", "timbalanPengerusiCawangan": "Deputy Branch Chairman", "timbalanPresidenCawangan": "Deputy Branch President", "timbalanYangDipertuaCawangan": "Deputy Branch Chairperson", "naibYangDipertuaCawangan": "Vice Branch Chairperson", "bendahariKehormatCawangan": "Honorary Branch Treasurer", "penolongBendahariKehormatCawangan": "Assistant Honorary Branch Treasurer", "ahliJawatankuasaBiasa": "Ordinary Committee Member", "ahliJawatankuasaBiasaCawangan": "Ordinary Branch Committee Member", "parentLiquidationWaitingList": "Parent Liquidation Waiting List", "BELUM DIHANTAR": "<PERSON><PERSON>", "ajkAppointmentList": "AJK Appointment List", "documentUploadRequired": "Document upload is required when financial declaration is enabled", "submitKuiri": "Submit Query", "declarationInvalid": "Invalid Account", "registrationFailed": "Registration failed", "sessionExpired": "Session expired - please login again", "sessionExpiredTitle": "Session Expired", "noPermissionToAccessResource": "You don't have permission to access this resource", "accessForbiddenTitle": "Access Forbidden", "registerErrorTitle": "Registeration Error", "JPNError": "Error: Sorry, verification failed. Please try again after 4 hours.", "chronologyOfComplaints": "Chronology of Suggestions/Complaints", "flowToJPPMDivisionState": "Flow to JPPM division/state", "flowToOfficerAction": "Flow to officer action", "infoPaymentPegawaiAwam": "<PERSON><PERSON><PERSON><PERSON> pegawai awam telah <PERSON>.", "infoPaymentPegawaiHarta": "<PERSON><PERSON><PERSON><PERSON> pegawai harta telah direko<PERSON>.", "infoPaymentCawanganPegawaiAwam": "<PERSON><PERSON><PERSON><PERSON> cawangan pegawai awam telah <PERSON>.", "infoPaymentCawanganPegawaiHarta": "<PERSON><PERSON><PERSON><PERSON> cawangan pegawai harta telah dire<PERSON>.", "carianMaklumat": "<PERSON><PERSON> ma<PERSON>", "NomborFasal": "Nombor Fasal", "NamaFasal": "<PERSON><PERSON>", "createAuditor": "Create Auditor", "feedbackPhoneNoErr": "Phone number must be between 10 & 8 digits including +60", "roleConfirmation": "Are you sure you want to update this role?", "roleConfirmationCreate": "Are you sure you want to create this role?", "paymentResultProcessing": "Processing Payment Result", "paymentResultPleaseWait": "Please wait while we retrieve your payment information...", "noRecordForStatusBranch": "The branch is not on record. Please contact the State JPPM to check the status of your establishment.", "laporan": "Report", "dashboardStatistik": "Statistics Dashboard", "statistik": "Statistics", "analisis": "Analysis", "pengurusanPelaporan": "Reporting Management", "tabPertubuhan": "Society", "tabPentadbiran": "Administration", "tabPemerkasaan": "Empowerment", "tabPenguatkuasaan": "Enforcement", "tabApplikasiMobile": "Mobile Application", "failedLoadDashboardError": "Failed to load dashboard. Please try again later.", "loadingDashboard": "Loading dashboard...", "preparingDashboard": "Preparing dashboard...", "noDashboardFoundError": "No dashboard found for the specified criteria.", "kategoriStatistik": "Statistics Category", "jenisStatistik": "Type of Statistics", "kategori": "Category", "jenisLaporan": "Report Type", "meetingYear": "Meeting Year", "dateOfAppointmentLetter": "Date of Appointment Letter", "appliedYear": "<PERSON><PERSON>", "submissionYear": "<PERSON><PERSON>", "decisionYear": "<PERSON><PERSON>", "noticeType": "Type of Notice", "landing_mainSlide_1_titleTop": "Welcome to", "landing_mainSlide_1_description": "eROSES System \nversion 2.0", "landing_mainSlide_1_btn": "Learn more", "landing_mainSlide_2_description": "Easy Organization \nRegistration", "landing_mainSlide_2_titleBottom": "Explore the best organization management", "landing_mainSlide_2_btn": "More Info", "landing_mainSlide_3_description": "Always Committed to \nAssist You", "landing_mainSlide_3_titleBottom": "With you for convenience and support", "landing_secSlide_1_title": "Management", "landing_secSlide_1_desc": "Organization registration guide", "landing_secSlide_2_title": "Criteria", "landing_secSlide_2_desc": "Organization registration requirements", "landing_secSlide_3_title": "Management", "landing_secSlide_3_desc": "Basic eROSES Guidebook", "landing_secSlide_4_title": "Management", "landing_secSlide_4_desc": "Frequently Asked Questions", "landing_bacaan_tab_1": "All Categories", "landing_bacaan_tab_2": "Announcements", "landing_bacaan_tab_3": "Activities", "landing_bacaan_tab_4": "Promotions", "landing_logos_desc_1": "Registrar of Fishermen Organizations under the Ministry of Agriculture and Food Security", "landing_logos_desc_2": "Registrar and regulator of sports organizations under the Ministry of Youth and Sports (KBS)", "landing_logos_desc_3": "Registrar and regulator of Trustee Corporations under the Prime Minister's Department", "landing_logos_desc_4": "Registrar and regulator of military veteran organizations under the Ministry of Defence", "landing_logos_desc_5": "Registrar and regulator of Trade Unions under the Ministry of Human Resources", "landing_logos_desc_6": "Registrar and regulator of Malaysian companies", "landing_logos_desc_7": "Registrar and regulator of youth organizations under the Ministry of Youth and Sports (KBS)", "landing_logos_desc_8": "Registrar of Parent-Teacher Associations under the Ministry of Education", "landing_logos_desc_9": "Registrar of Cooperative-based Organizations", "takwim_title": "Malaysian Organization Department Activity Calendar (JPPM)", "takwim_subtitle": "Activity Calendar JPPM", "takwim_desc": "Explore the various events that will be held throughout the year by JPPM. Find events that suit your needs and have the potential to provide inspiration and support to improve the effectiveness of your association management.", "takwim_sidebar_nextEvent": "Next Event", "takwim_sidebar_viewFull": "View Full Event", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "CreateEvent": "Create Event", "fax": "fax", "DATA_CREATED": "Data created successfully", "DATA_UPDATED": "Data updated successfully", "DATA_FOUND": "Data found successfully", "NO_DATA_FOUND": "No data found", "DATA_DELETED": "Data deleted successfully", "EMAIL_SENT": "<PERSON>ail sent successfully", "PAYMENT_MADE": "Payment made successfully", "PAYMENT_CANCELLED": "Payment cancelled successfully", "NO_DATA_FOUND_BY_CRITERIA": "No data found by criteria", "OPERATION_COMPLETED": "Operation completed successfully", "RETRIEVAL_OK": "Retrieved successfully", "PDF_GENERATED": "PDF generated successfully", "APPLICATION_SENT": "Application have been successfully sent", "SOCIETY_NAME_TAKEN": "Society name is taken", "SOCIETY_NAME_AVAILABLE": "Society name is available", "SOCIETY_CREATED": "Society created successfully", "SOCIETY_UPDATED": "Society updated successfully", "SOCIETY_DELETED": "Society deleted successfully", "SOCIETY_FOUND": "Society found successfully", "SOCIETY_NOT_FOUND": "Society with the given id cannot be found", "SOCIETY_APPROVED": "Society approved successfully", "SOCIETY_REJECTED": "Society rejected successfully", "SOCIETY_CANCELLED": "Society cancelled successfully", "SOCIETY_SUBMITTED": "Society submitted successfully", "SOCIETY_DRAFT_SAVED": "Society draft saved successfully", "SOCIETY_PUBLISHED": "Society published successfully", "SOCIETY_UNPUBLISHED": "Society unpublished successfully", "SOCIETY_ACTIVATED": "Society activated successfully", "SOCIETY_DEACTIVATED": "Society deactivated successfully", "SOCIETY_SUSPENDED": "Society suspended successfully", "SOCIETY_REINSTATED": "Society reinstated successfully", "BRANCH_CREATED": "Branch created successfully", "BRANCH_UPDATED": "Branch updated successfully", "BRANCH_DELETED": "Branch deleted successfully", "BRANCH_FOUND": "Branch found successfully", "BRANCH_APPROVED": "Branch approved successfully", "BRANCH_REJECTED": "Branch rejected successfully", "BRANCH_SUBMITTED": "Branch submitted successfully", "COMMITTEE_CREATED": "Committee created successfully", "COMMITTEE_UPDATED": "Committee updated successfully", "COMMITTEE_DELETED": "Committee deleted successfully", "COMMITTEE_FOUND": "Committee found successfully", "COMMITTEE_APPROVED": "Committee approved successfully", "COMMITTEE_REJECTED": "Committee rejected successfully", "COMMITTEE_SUBMITTED": "Committee submitted successfully", "AMENDMENT_CREATED": "Amendment created successfully", "AMENDMENT_UPDATED": "Amendment updated successfully", "AMENDMENT_DELETED": "Amendment deleted successfully", "AMENDMENT_FOUND": "Amendment found successfully", "AMENDMENT_APPROVED": "Amendment approved successfully", "AMENDMENT_REJECTED": "Amendment rejected successfully", "AMENDMENT_SUBMITTED": "Amendment submitted successfully", "PROPERTY_OFFICER_APPLICATION_CREATED": "Property officer application created successfully", "PROPERTY_OFFICER_APPROVED": "Property officer approved successfully", "PROPERTY_OFFICER_UPDATED": "Property officer updated", "PROPERTY_OFFICER_APPLICATION_ALREADY_DELETED": "Property officer application is already deleted", "PROPERTY_OFFICER_APPLICATION_DELETED": "Property officer application deleted successfully", "DATA_NOT_FOUND": "Data not found", "DATA_NOT_FOUND_WITH_CRITERIA": "Data not found with criteria", "DATA_ALREADY_EXISTS": "Data already exists", "DATA_ALREADY_EXISTS_USER": "This User Category already exists.", "DATA_CREATE_FAILED": "Data create failed", "DATA_UPDATE_FAILED": "Data update failed", "DATA_DELETE_FAILED": "Data delete failed", "DATABASE_ERROR": "Database error occurred", "NULL_POINTER_EXCEPTION": "Null pointer exception occurred", "UNEXPECTED_ERROR": "An unexpected error occurred", "USER_NOT_AUTHENTICATED": "User is not authenticated", "TEMPLATE_NOT_FOUND": "Temp<PERSON> not found", "TEMPLATE_ALREADY_EXIST": "Template already exists", "EMAIL_NOT_SENT": "<PERSON><PERSON> not sent", "FILE_NOT_ATTACHED": "No file attached", "S3_UPLOAD_FAILED": "Failed to upload to S3", "PAYMENT_FAILED": "Payment failed", "INVALID_CODE": "Invalid code", "INVALID_USER": "Invalid user", "INVALID_REQUEST": "Invalid request", "INTERNAL_SERVER_ERROR": "Internal server error", "MISSING_REQUEST_DTO": "Missing request dto", "CODE_ALREADY_EXISTS": "Code already exists", "PROCESS_DOCUMENT_FAILED": "Failed to process document", "MISSING_DATA_REQUEST": "Missing data in request", "USER_ALREADY_BLACKLISTED": "User is already blacklisted", "NO_BLACKLIST_POLITIC_SOCIETY": "Invalid request: Politic society cannot be blacklisted", "USER_IS_BLACKLISTED": "User is blacklisted", "USER_ALREADY_IS_MEMBER_OF_SOCIETY": "User is already a member of society", "DOCUMENT_NOT_FOUND": "Document not found", "DUPLICATE_MEETING_DATE_TYPE": "Meeting with the same date and type already exists", "POSTING_NOT_FOUND": "Posting not found", "POSTING_ALREADY_EXISTS": "Posting already exists", "POSTING_CREATE_FAILED": "Failed to create posting", "POSTING_UPDATE_FAILED": "Failed to update posting", "POSTING_DELETE_FAILED": "Failed to delete posting", "POSTING_PUBLISH_FAILED": "Failed to publish posting", "POSTING_UNPUBLISH_FAILED": "Failed to unpublish posting", "INVALID_CATEGORY_FOR_SECRETARY": "Invalid category for secretary", "CANNOT_UPDATE_SUBMITTED_POSTING": "Cannot update submitted posting", "ALREADY_REPORTED_TODAY": "Already reported this posting today", "POSTING_REVIEW_ALREADY_SUBMITTED": "Posting review already submitted", "REJECTION_COMMENT_REQUIRED": "Rejection comment is required", "SOCIETY_NOT_FOUND_ID": "Society with the given id cannot be found", "NULL_BRANCH_COMMITTEE": "Branch committee id cannot be null", "BRANCH_COMMITTEE_NOT_FOUND": "Branch committee with the given id cannot be found", "COMMITTEE_NOT_FOUND": "Committee with the given id cannot be found", "COMMITTEE_TASK_EXIST": "Committee task for module already exists", "COMMITTEE_TASK_NOT_EXIST": "Committee task for module does not exist", "COMMITTEE_TASK_ALREADY_ENABLED": "Committee task already enabled", "COMMITTEE_TASK_MODULE_NOT_ENABLED": "Committee task not enabled", "COMMITTEE_TASK_FOR_MODULE_IS_NOT_APPLICABLE_FOR_BRANCH": "Committee task for module is not applicable for branch", "NON_ADDITIONAL_INFORMATION": "No additional information is available", "BRANCH_NOT_FOUND": "Branch not found", "TRUSTEE_NOT_FOUND": "Trustee not found", "TRUSTEE_ALREADY_EXISTS": "Trustee already exists", "TRUSTEE_CREATE_FAILED": "Failed to create trustee", "TRUSTEE_UPDATE_FAILED": "Failed to update trustee", "TRUSTEE_DEACTIVATE_FAILED": "Failed to deactivate trustee", "TRUSTEE_CREATE_AUDIT_TRAIL_FAILED": "Trustee created, but failed to create audit trail", "TRUSTEE_UPDATE_AUDIT_TRAIL_FAILED": "Trustee updated, but failed to create audit trail", "PUBLIC_OFFICER_CREATE_FAILED": "Failed to create public officer", "PUBLIC_OFFICER_CREATE_AUDIT_FAILED": "Public officer created but failed to insert audit log", "PUBLIC_OFFICER_NOT_FOUND": "Public officer not found", "PUBLIC_OFFICER_NOT_FOUND_WITH_ID": "Public officer not found", "PUBLIC_OFFICER_UPDATE_FAILED": "Failed to update public officer", "PUBLIC_OFFICER_AUDIT_TRAIL_FAILED": "Failed to create audit trail record", "PUBLIC_OFFICER_ALREADY_SUBMITTED": "Public officer already been submitted", "PUBLIC_OFFICER_DELETE_FAILED": "Failed to delete public officer", "PUBLIC_OFFICER_AUDIT_LOG_FAILED": "Failed to create audit log", "PUBLIC_OFFICER_APPLICATION_STATUS_UPDATE_FAILED": "Failed to update public officer application status", "RO_APPROVAL_CREATE_FAILED": "Failed to create RO Approval", "INVALID_APPROVAL_STATUS": "Only APPROVED and REJECTED is allowed", "PROPERTY_OFFICER_NOT_FOUND": "Property officer not found", "PROPERTY_OFFICER_APPLICATION_NOT_FOUND_WITH_ID": "Property officer application not found", "PROPERTY_OFFICER_CREATE_FAILED": "Failed to create property officer", "PROPERTY_OFFICER_DEACTIVATE_FAILED": "Failed to deactivate previous property officer", "PROPERTY_OFFICER_APPROVAL_STATUS_UPDATE_FAILED": "Failed to update approval status for property officer application", "UNSUPPORTED_STATUS": "Unsupported status", "UPDATE_OPERATION_NOT_ALLOWED": "The update operation is not allowed", "PROPERTY_OFFICER_UPDATE_FAILED": "Failed to update property officers", "INVALID_REQUEST_UPDATE_BOTH": "Invalid request, you can update application status code or officers but not both", "DRAFT_PROPERTY_OFFICER_DELETE_ONLY": "Only draft property officer application can be deleted", "PROPERTY_OFFICER_APPLICATION_UPDATE_FAILED": "Failed to update property officer application", "BLACKLIST_USER_NOT_FOUND": "Blacklist User not found", "_comment_additional_messages": "=== Additional API Messages ===", "PASSWORD_RESET_SUCCESS": "Password reset successfully", "LOGOUT_TRIGGER_COMPLETED": "Logout trigger completed", "USER_LOGGED_OUT_SUCCESSFULLY": "User logged out successfully", "USER_UPDATED_SUCCESSFULLY": "User updated successfully", "EMAIL_TEMPLATE_CREATED_SUCCESSFULLY": "EmailTemplate created successfully", "EMAIL_TEMPLATE_UPDATED_SUCCESSFULLY": "EmailTemplate updated successfully", "NOTIFICATION_CREATED_SUCCESSFULLY": "Notification created successfully", "NOTIFICATION_UPDATED_SUCCESSFULLY": "Notification updated successfully", "USER_ADDED_SUCCESSFULLY": "User added successfully", "SMS_SENT_SUCCESSFULLY": "SMS sent successfully", "PAGE_MANAGEMENT_ADDED_SUCCESSFULLY": "Page management added successfully", "PAGE_MANAGEMENT_UPDATED_SUCCESSFULLY": "Page management updated successfully", "ALL_NOTIFICATION_EMAILS_SENT_SUCCESSFULLY": "All notification emails were sent successfully", "SOME_NOTIFICATION_EMAILS_SENT_SUCCESSFULLY": "Some notification emails were sent successfully", "ALL_NOTIFICATION_EMAILS_FAILED": "All notification emails failed to send", "USER_LOGGED_IN_SUCCESSFULLY": "User logged in successfully", "FORGOT_PASSWORD_SUCCESS": "Success", "OTP_SENT_SUCCESSFULLY": "OTP sent successfully", "USER_APPROVAL_APPROVED_SUCCESSFULLY": "User approval has been successfully approved", "USER_APPROVAL_REJECTED_SUCCESSFULLY": "User approval has been successfully rejected", "OTP_INVALID_MESSAGE": "The code entered is incorrect. Please try again", "OTP_VERIFIED_SUCCESSFULLY": "OTP verified successfully", "OTP_ALREADY_USED": "OTP has been used", "OTP_EXPIRED": "Code has expired, please request a new code", "OTP_MODULE_TYPE_INVALID": "Module type is invalid", "OTP_IS_VALID": "OTP is valid", "USER_UPDATE_APPROVED_SUCCESSFULLY": "User update has been successfully approved", "USER_UPDATE_REJECTED_SUCCESSFULLY": "User update has been successfully rejected", "ALL_EMAILS_SENT": "All notification emails were sent successfully", "PARTIAL_EMAILS_SENT": "Partially successful: emails were sent", "POSTING_CREATED": "Posting created successfully", "POSTING_UPDATED": "Posting updated successfully", "POSTING_DELETED": "Posting deleted successfully", "POSTING_PUBLISHED": "Posting published successfully", "POSTING_UNPUBLISHED": "Posting unpublished successfully", "POSTING_FOUND": "Posting found successfully", "POSTINGS_FOUND": "Postings found successfully", "POSTING_REPORTED": "Posting reported successfully", "POSTING_REPORTS_FOUND": "Posting reports found successfully", "POSTING_REVIEWS_FOUND": "Posting reviews found successfully", "POSTING_EXPIRED_FOUND": "Posting expired found successfully", "ENGAGEMENT_STATS_FOUND": "Engagement statistics found successfully", "ENGAGEMENT_RECORDED": "Engagement recorded successfully", "POSTING_REVIEW_SUBMITTED": "Posting review submitted successfully", "NOTIFICATION_MARKED_AS_READ": "Notification marked as read successfully", "ALL_NOTIFICATIONS_MARKED_AS_READ": "All notifications marked as read successfully", "POSTING_USER_FOUND": "Posting user found successfully", "POSTING_PRE_PUBLISH": "Posting Pre Publish found successfully", "SUBMIT_SUCCESS": "Submitted successfully", "QUERY_SUBMIT_SUCCESS": "Query submitted successfully", "COMMITTEE_TASK_CREATED": "Committee task created successfully", "COMMITEE_FOUND": "Committee found successfully", "COMMITTEE_TASK_DEACTIVATED": "Committee task deactivated successfully", "MEETING_CREATED": "Meeting created successfully", "MEETING_UPDATED": "Meeting updated successfully", "TRUSTEE_CREATED": "Trustee created successfully", "TRUSTEE_UPDATED": "Trustee updated successfully", "TRUSTEE_DEACTIVATED": "Trustee deactivated successfully", "_comment_additional_error_messages": "--- Additional Error Messages ---", "USER_NOT_FOUND": "User not found", "OLD_PASSWORD_NOT_MATCH": "Old password does not match", "NEW_PASSWORD_SAME_AS_OLD": "New password cannot be the same as old password", "IDENTIFICATION_NUMBER_ALREADY_EXISTS": "This identification number already exists", "EMAIL_ADDRESS_ALREADY_EXISTS": "This email address already exists", "IDENTIFICATION_NOT_FOUND_IN_SYSTEM": "Identification number does not exist in the system. Please register a new account", "ACCOUNT_ALREADY_ACTIVATED": "Your account has been activated. Please contact the relevant party", "ACCOUNT_DEACTIVATED_REJECTED": "Your account has been blocked/deactivated. Please contact the relevant party", "INVALID_CREDENTIALS": "ID Card Number and Password do not match. Please try again", "USER_ALREADY_EXISTS": "User already exists", "INVALID_SERVICE": "Invalid service", "GENERIC_ERROR": "Last Error", "USER_IDENTIFICATION_ALREADY_EXISTS": "This user's identification number already exists", "INVALID_LOGIN_TYPE": "Invalid login type", "PERMISSION_DENIED": "Permission denied", "UNAUTHORIZED_ACCESS": "Unauthorized access", "INVALID_USER_GROUP": "Invalid user group", "NO_RECORDS_FOUND": "No records found", "NO_PARENT_ID_FOUND": "No parent id found", "INVALID_STATUS": "Invalid status", "PAGE_MANAGEMENT_NOT_FOUND": "Page management not found", "EMAIL_TEMPLATE_NOT_FOUND": "Email template not found", "VALIDATION_FAILED": "Validation failed", "DATABASE_UPDATE_ERROR": "Database error occurred while updating user", "NULL_DATA_ENCOUNTERED_USER": "Null data encountered while updating user", "UNEXPECTED_UPDATE_ERROR": "An unexpected error occurred while updating user", "DATABASE_EMAIL_TEMPLATE_ERROR": "Database error occurred while creating emailTemplate", "NULL_DATA_EMAIL_TEMPLATE": "Null data encountered while creating emailTemplate", "DATABASE_NOTIFICATION_ERROR": "Database error occurred while creating notification", "MYDIGITALID_LOGIN_URL_FAILED": "Failed to generate myDigitalId login URL", "MYDIGITALID_TOKEN_EXCHANGE_FAILED": "Failed to exchange code for token", "COMMITTEE_CREATE_AUDIT_TRAIL_FAILED": "Committee created, but failed to create audit trail", "COMMITTEE_INTERNAL_SERVER_ERROR": "A database error occurred while retrieving the committee", "COMMITEE_EMPTY": "Empty data was found while retrieving the committee", "COMMITEE_UNEXPECTEDERROR": "An unexpected error occurred while getting the committee", "COMMITEE_SEARCH_NOT_FOUND": "No committees found with the given search criteria", "CURRENT_SECRETARY_CANNOT_APPLY": "Current secretary cannot apply for secretary position", "APPEAL_CREATE_FAILED": "Unable to create Appeal case", "APPEAL_UPDATE_FAILED": "Appeal case unable to be updated", "APPEAL_REMOVE_FAILED": "Appeal case record failed to be updated", "PROPERTY_OFFICER_APPLICATION_CREATE_FAILED": "Failed to create property officer application", "PROPERTY_OFFICER_APPLICATION_NOT_FOUND": "Property officer application not found", "PROPERTY_OFFICER_RO_UPDATE_FAILED": "Failed to update property officer ro", "NO_TEMPLATE_FOUND": "Temp<PERSON> not found", "ALL_EMAILS_FAILED": "Failed to send any notification emails", "TITLE_REQUIRED": "Title is required", "USER_ID_REQUIRED": "User ID is required", "FIELD_NOT_FOUND": "Error processing field options", "PAGE_NUMBER_VALIDATION": "Page number must be greater than or equal to 1", "PAGE_SIZE_VALIDATION": "Page size must be greater than or equal to 1", "SOCIETY_ID_NULL": "Society ID cannot be null", "_comment_missing_success_messages": "--- Missing Success Messages from eroses-society ---", "COMMITTEE_TASK_ACTIVATED": "Committee task activated successfully", "SOCIETY_COMMITTEE_TASK_MODULE_ENABLED": "Committee task module enabled successfully", "SOCIETY_COMMITTEE_TASK_MODULE_DISABLED": "Committee task module disabled successfully", "BRANCH_COMMITTEE_TASK_MODULE_ENABLED": "Committee task module enabled successfully", "BRANCH_COMMITTEE_TASK_MODULE_DISABLED": "Committee task module disabled successfully", "APPEAL_CREATED": "Appeal case created successfully", "APPEAL_UPDATED": "Appeal case updated successfully", "APPEAL_REMOVED": "Appeal case removed successfully", "PUBLIC_OFFICER_CREATED": "Public officer created successfully", "PUBLIC_OFFICER_UPDATED": "Public officer updated successfully", "PUBLIC_OFFICER_REMOVED": "Public officer removed successfully", "PUBLIC_OFFICER_APPROVED": "Public officer approved successfully", "PUBLIC_OFFICER_RETRIEVED": "Public officer retrieved successfully", "BRANCH_PUBLIC_OFFICER_RETRIEVED": "Branch public officer retrieved successfully", "PUBLIC_OFFICER_COUNT_RETRIEVED": "Public officer count retrieved successfully", "BRANCH_PUBLIC_OFFICER_COUNT_RETRIEVED": "Branch public officer count retrieved successfully", "_comment_missing_error_messages": "--- Missing <PERSON><PERSON>r Messages from eroses-society ---", "COMMITTEE_DETAIL_NOT_FILLED": "Committee detail is not filled", "NON_CITIZEN_APPLICATION_NOT_SUBMITTED": "Non-citizen application is not submitted", "COMMITTEE_TASK_MODULE_ALREADY_ENABLED": "Committee task already enabled", "MEETING_NOT_CREATED": "Meeting not created", "_comment_notification_only_messages": "--- Notification Service Only Messages ---", "_comment_complaint_specific_messages": "--- Complaint Service Specific Messages ---", "_comment_profile_update_messages": "=== Profile Update Messages ===", "PROFILE_UPDATED_SUCCESSFULLY": "Profile updated successfully", "PROFILE_UPDATE_FAILED": "Failed to update profile", "INVALID_PROFILE_DATA": "Invalid profile data provided", "PROFILE_NOT_FOUND": "Profile not found", "UNAUTHORIZED_PROFILE_UPDATE": "Unauthorized to update this profile", "PROFILE_UPDATE_VALIDATION_ERROR": "Profile validation failed", "reorder": "Rearrange Rows", "NoChangesMade": "No changes made"}