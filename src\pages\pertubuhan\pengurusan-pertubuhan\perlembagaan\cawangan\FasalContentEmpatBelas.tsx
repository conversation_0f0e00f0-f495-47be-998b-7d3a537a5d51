import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { API_URL } from "../../../../../api";
import CustomPopover from "../../../../../components/popover";
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import { ConstitutionType, RegExNumbers } from "../../../../../helpers/enums";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
interface FasalContentEmpatBelasCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentEmpatBelasCawangan: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [minimumJumlahAhliPertubuhan, setMinimumJumlahAhliPertubuhan] =
    useState("");

  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  //const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [isRequiredConstitution, setIsRequiredConstitution] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  /*const clauseContent = `
1. Jawatankuasa Induk boleh membenarkan penubuhan cawangan di kawasan yang mempunyai sekurang-kurangnya ${
    minimumJumlahAhliPertubuhan !== ""
      ? `${minimumJumlahAhliPertubuhan}`
      : "<<minimum jumlah ahli pertubuhan >>"
  } ahli Pertubuhan. Penubuhan cawangan hendaklah mendapat kebenaran secara bertulis oleh Pendaftar Pertubuhan terlebih dahulu.

2. Jawatankuasa Induk boleh membubarkan cawangan sekiranya:

   a. jika bilangan ahli dalam tempoh enam (6) bulan berturut-turut kurang daripada ${
     minimumJumlahAhliPertubuhan !== ""
       ? `${minimumJumlahAhliPertubuhan}`
       : "<<minimum jumlah ahli pertubuhan >>"
   }; atau

   b. jika cawangan tidak mematuhi Perlembagaan Pertubuhan atau keputusan Mesyuarat Agung atau Jawatankuasa Induk, atau Jawatankuasa Induk mendapati Cawangan bersalah kerana perbuatannya mencemarkan Pertubuhan.

3. Bagi pembubaran cawangan atas sebab yang disebutkan dalam Fasal 14(2)(b) di atas, cawangan yang berkenaan hendaklah diberi suatu notis selama 30 hari terlebih dahulu dan diberi peluang untuk menjawab tuduhan-tuduhan tersebut. Keputusan membubarkan cawangan hendaklah dengan undi terbanyak dalam Mesyuarat Jawatankuasa Induk.

4. Perintah pembubaran hendaklah ditandatangani oleh Setiausaha Agung. Cawangan tidak boleh bergerak mulai dari tarikh penerimaan perintah tersebut kecuali untuk urusan penyelesaian/pembubaran. Mana-mana cawangan yang terkilan atas perintah pembubaran boleh, dengan mengemukakan rayuan secara bertulis kepada Setiausaha Agung dalam tempoh 30 hari dari tarikh penerimaan perintah tersebut untuk dibawa ke Mesyuarat Agung. Walaupun rayuan telah dibuat namun perintah pembubaran masih berkuatkuasa sehingga dibatalkan tetapi di dalam keadaan yang sedemikian Jawatankuasa Induk boleh melantik satu jawatankuasa penjaga daripada ahli-ahli untuk menguruskan hal ehwal cawangan itu sementara rayuan tersebut dipertimbangkan.

5. Jika sesuatu cawangan dibubar kerana sebab yang disebutkan dalam Fasal 14(2)(a) di atas, maka Jawatankuasa Induk hendaklah memindahkan ahli-ahli yang ada kepada cawangan yang hampir sekali, dan jika cawangan dibubar kerana sebab dalam Fasal 14(2)(b) di atas, maka ahli-ahli akan terhenti menjadi ahli Pertubuhan.

6. Adalah menjadi kewajipan Pengerusi, Setiausaha dan Bendahari bagi cawangan itu menyerahkan kepada Setiausaha Agung segala buku, rekod, wang dan harta-harta lainnya yang dimiliki oleh cawangan berserta dengan penyata kewangan cawangan itu daripada tarikh akhir pengemukaan penyata kewangan itu dibentangkan sehingga tarikh perintah pembubaran.

7. Jika ahli-ahli sesuatu cawangan memutuskan hendak keluar daripada Pertubuhan, maka pegawai-pegawai cawangan itu dengan serta-merta mestilah menyerahkan kepada Setiausaha Agung segala buku, rekod, wang dan harta-harta lain Pertubuhan dan seterusnya menyediakan dan menyerahkan penyata kewangan kepada Setiausaha Agung seperti dalam Fasal 14(6) di atas.
`;*/

  //const clause14 = localStorage.getItem("clause14");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause14Data = JSON.parse(clause14);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause14Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause14Data.societyName);
      setMinimumJumlahAhliPertubuhan(
        clause.constitutionValues[0]?.definitionName
      );
      setIsEdit(clause.edit);
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id } = useParams();

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};
    const number = Number(minimumJumlahAhliPertubuhan);
    const minValue = isRequiredConstitution ? 7 : 0;

    if (!minimumJumlahAhliPertubuhan) {
      errors.minimumJumlahAhliPertubuhan = t("fieldRequired");
    } else if (isRequiredConstitution) {
      if (number < minValue) {
        errors.minimumJumlahAhliPertubuhan = t("validation.minValue", {
          value: minValue,
        });
      }
    }

    return errors;
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<minimum jumlah ahli pertubuhan >>/gi,
    `<b>${
      minimumJumlahAhliPertubuhan || "<<minimum jumlah ahli pertubuhan >>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
      console.log(
        "Fasal 1 pengurusan-p Cawangan",
        societyDataRedux?.constitutionType
      );
      if (
        societyDataRedux?.constitutionType ===
          ConstitutionType.CawanganNGO[1] ||
        societyDataRedux?.constitutionType === ConstitutionType.CawanganAgama[1]
      ) {
        setIsRequiredConstitution(true);
      } else {
        setIsRequiredConstitution(false);
      }
    }
  }, [societyDataRedux]);

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clause")} {id}
        </Typography>

        <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
          <Typography sx={labelStyle}>
            {t("minimumJumlahAhliPertubuhan")}
          </Typography>
          <TextField
            size="small"
            fullWidth
            required
            value={minimumJumlahAhliPertubuhan}
            onChange={(e) => {
              const value = e.target.value;
              const number = Number(value);
              const minValue = isRequiredConstitution ? 7 : 0;

              if (value === "" || !RegExNumbers.test(value)) {
                setMinimumJumlahAhliPertubuhan(value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  minimumJumlahAhliPertubuhan: t("fieldRequired"),
                }));
                return;
              }

              setMinimumJumlahAhliPertubuhan(value);

              if (number < minValue) {
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  minimumJumlahAhliPertubuhan: t("validation.minValue", {
                    value: minValue,
                  }),
                }));
              } else {
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  minimumJumlahAhliPertubuhan: "",
                }));
              }
            }}
            error={!!formErrors.minimumJumlahAhliPertubuhan}
            helperText={formErrors.minimumJumlahAhliPertubuhan}
            type="number"
            inputProps={{ min: isRequiredConstitution ? 7 : 0 }}
          />
        </Box>
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            const errors = validateForm();
            if (Object.keys(errors).length > 0) {
              setFormErrors(errors);
              return;
            }
            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: minimumJumlahAhliPertubuhan,
                  titleName: "Minimum Jumlah Ahli Pertubuhan",
                },
              ],
              clause: "clause14",
              clauseCount: 14,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentEmpatBelasCawangan;
