import {
  Box,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import useQuery from "@/helpers/hooks/useQuery";
import { Auditor } from "../interface";
import {
  ApplicationStatus,
  ApplicationStatusEnum,
  COMMITTEE_TASK_TYPE,
} from "@/helpers/enums";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { useSenaraiContext } from "@/pages/pertubuhan/SenaraiContext";
import Input from "@/components/input/Input";
import ButtonPrevious from "@/components/button/ButtonPrevious";
import { Crud<PERSON>ilter, useCustomMutation } from "@refinedev/core";
import dayjs from "dayjs";
import {
  Controller,
  FieldValues,
  SubmitHandler,
  useForm,
} from "react-hook-form";
import { API_URL } from "@/api";
import { DataTable, IColumn } from "@/components";
import { useBranchContext } from "@/pages/pertubuhan/BranchProvider";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const JuruAudit: React.FC = () => {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const { t } = useTranslation();
  const {
    handleNextPenyataTahunan: handleNext,
    handleBackPenyataTahunan: handleBack,
  } = useSenaraiContext();
  const { control, handleSubmit, watch, setValue, getValues, reset } = useForm({
    defaultValues: {
      date: "",
    },
  });
  const isviewStatement = useSelector(
    // @ts-ignore
    (state) => state?.statementData?.isViewStatement
  );

  const {
    isBlackListed,
    isAuthorized,
    isAliranModuleAccess,
    branchId,
    fetchAliranTugasAccessHandle,
  } = useBranchContext();

  useEffect(() => {
    fetchAliranTugasAccessHandle(COMMITTEE_TASK_TYPE.PENYATAAN_TAHUNAN);
  }, []);

  const isDisabled =
    (!isAuthorized && !isAliranModuleAccess && !isBlackListed) ||
    isviewStatement;

  const [savedAppointmentDate, setSavedAppointmentDate] = useState<
    string | undefined
  >("");
  //@ts-ignore
  const statementDataRedux = useSelector((state) => state?.statementData?.data);
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const statementId = statementDataRedux.statementId;
  const year = statementDataRedux.statementYear;
  const societyId = statementDataRedux.societyId;
  const [statementComplete, setStatementComplete] = useState(false);
  const [auditDate, setAuditDate] = useState("");
  const [appointedDateList, setAppointedDatesList] = useState<any>([]);
  useQuery({
    url: `society/statement/societyInfo/get`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "year", operator: "eq", value: year },
    ],
    onSuccess: (data) => {
      if (
        Number(data?.data?.data?.applicationStatusCode) ===
        ApplicationStatus.SELESAI
      ) {
        setStatementComplete(true);
      }
    },
  });

  const [juruauditCount, setJuruauditCount] = useState<string | number>("");
  const [juruauditType, setJuruauditType] = useState<string>("");
  useQuery({
    url: `society/statement/getJuruauditInfo`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      const juruAuditInfp = data?.data?.data;
      setJuruauditCount(juruAuditInfp?.numberOfJuruaudit);
      setJuruauditType(juruAuditInfp?.juruauditType);
    },
  });

  const [auditorCount, setAuditorCount] = useState<number>(0);
  useQuery({
    url: `society/statement/auditor/countAuditors`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "statementId", operator: "eq", value: statementId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      const auditorCount = data?.data?.data || [];
      console.log(auditorCount);
      setAuditorCount(auditorCount.auditorCount);
    },
  });

  const [auditorList, setAuditorList] = useState<Auditor[]>([]);
  const {
    data,
    isLoading: auditorListIsLoading,
    refetch: fetchAuditorList,
  } = useQuery({
    url: `society/statement/auditor/list`,
    // filters: [
    //   { field: "societyId", operator: "eq", value: societyId },
    //   { field: "statementId", operator: "eq", value: statementId },
    //   { field: "branchId", operator: "eq", value: branchDataRedux.id },
    // ],
    autoFetch: false,
    onSuccess: (data) => {
      const auditorLst = data?.data?.data?.data || [];
      setAuditorList(auditorLst);
    },
  });

  const handleDateChange = (date: string | null) => {
    if (!date) {
      return;
    }
    const filters: CrudFilter[] = [
      { field: "appointmentDate", value: date, operator: "eq" },
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ];
    setSavedAppointmentDate(date);
    fetchAuditorList({ filters });
  };

  useEffect(() => {
    if (societyId) {
      const filters: CrudFilter[] = [
        { field: "societyId", operator: "eq", value: societyId },
        { field: "branchId", operator: "eq", value: branchDataRedux.id },
      ];

      fetchAuditorList({ filters });
    }
  }, [societyId]);

  const handleClearSearch = () => {
    setAuditDate("");
    setSavedAppointmentDate(undefined);
    handleDateChange(null);
    reset();
    const filters: CrudFilter[] = [
      { field: "appointmentDate", value: "", operator: "eq" },
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ];

    fetchAuditorList({ filters });
  };

  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const handleDaftarJuruaudit = () => {
    navigate("../../ajk/juruaudit");
  };

  const handleBackActions = () => {
    handleBack();
    navigate(-1);
  };

  const { data: AppointedDatesList, isLoading: AppointedDatesListIsLoading } =
    useQuery({
      url: `society/statement/auditor/getAuditorAppointmentDates`,
      filters: [
        { field: "societyId", operator: "eq", value: societyId },
        { field: "branchId", operator: "eq", value: branchDataRedux.id },
      ],
      onSuccess: (data) => {
        const responseData = data?.data?.data?.appointedDates;
        setAppointedDatesList(responseData);
      },
    });
  // const handleNextActions = () => {
  //   handleNext();
  //   navigate("../pendapatan", {
  //     state: {
  //       societyId: societyId,
  //       statementId: statementId,
  //       year: year,
  //     },
  //   });
  // };

  const { mutate: saveGeneralInfo, isLoading: isLoadingSaveBank } =
    useCustomMutation();

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    if (isDisabled || statementComplete) {
      handleNext();
      navigate("../cawangan-penyata-tahunan-pendapatan", {
        state: {
          societyId: societyId,
          statementId: statementId,
          branchId: branchDataRedux.id,
          year: year,
        },
      });
    } else {
      saveGeneralInfo(
        {
          url: `${API_URL}/society/statement/general/update`,
          method: "put",
          values: {
            societyId: societyId,
            statementId: statementId,
            branchId: branchDataRedux.id,
            ...(savedAppointmentDate && {
              juruauditAppointedDate: savedAppointmentDate,
            }),
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
        },
        {
          onSuccess: () => {
            handleNext();
            navigate("../cawangan-penyata-tahunan-pendapatan", {
              state: {
                societyId: societyId,
                statementId: statementId,
                branchId: branchDataRedux.id,
                year: year,
              },
            });
          },
        }
      );
    }
  };

  useQuery({
    url: `society/statement/general/get`,
    filters: [
      {
        field: "statementId",
        value: statementId,
        operator: "eq",
      },
      {
        field: "societyId",
        value: societyId,
        operator: "eq",
      },
      { field: "branchId", operator: "eq", value: branchDataRedux.id },
    ],
    onSuccess: (data) => {
      const date = data?.data?.data?.juruauditAppointedDate;
      setAuditDate(date);
      setSavedAppointmentDate(date);
    },
  });

  ///

  const handleChangePage = (newPage: number) => {
    const filters: CrudFilter[] = [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: newPage, operator: "eq" },
    ];
    setPage(newPage);
    fetchAuditorList({ filters });
  };

  const handlePageSizeChange = (newPageSize: number) => {
    const filters: CrudFilter[] = [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "pageSize", value: newPageSize, operator: "eq" },
      { field: "pageNo", value: page, operator: "eq" },
    ];
    setPageSize(newPageSize);
    fetchAuditorList({ filters });
  };

  const columns: IColumn[] = [
    {
      field: `${t("name")}`,
      headerName: `${t("name")}`,
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return <Box>{row.name ? row.name : "-"}</Box>;
      },
      cellClassName: "custom-cell",
    },
    {
      field: `${t("idNumber")}`,
      headerName: `${t("idNumber")}`,
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return <Box>{row.identificationNo ? row.identificationNo : "-"}</Box>;
      },
      cellClassName: "custom-cell",
    },
    {
      field: `${t("email")}`,
      headerName: `${t("email")}`,
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return <Box>{row.email}</Box>;
      },
      cellClassName: "custom-cell",
    },
    {
      field: `${t("tarikhLantik")}`,
      headerName: `${t("tarikhLantik")}`,
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return <Box>{row.appointmentDate ? row.appointmentDate : "-"}</Box>;
      },
      cellClassName: "custom-cell",
    },
    {
      field: `${t("status")}`,
      headerName: `${t("status")}`,
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return <Box>{t(ApplicationStatusEnum[row.status])}</Box>;
      },
      cellClassName: "custom-cell",
    },
  ];

  return (
    <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
        mb: 2,
      }}
    >
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography
          sx={{
            color: "#666666",
            fontSize: 14,
            fontWeight: "400 !important",
          }}
        >
          <span style={{ color: "red", fontWeight: "bold" }}>
            {t("peringatan")} :
          </span>{" "}
          Sila pastikan bilangan Juruaudit yang berstatus aktif mengikut
          bilangan di dalam perlembagaan.
        </Typography>
      </Box>

      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          textAlign: "center",
          p: 3,
          mb: 2,
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{
            color: "var(--primary-color)",
            fontSize: 18,
            fontWeight: "500 !important",
          }}
        >
          {t("bilanganJuruauditTerkini")}
        </Typography>
        <Typography
          sx={{
            color: "#666666",
            fontSize: 18,
            fontWeight: "500 !important",
          }}
        >
          {auditorCount} Orang
        </Typography>
      </Box>

      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          gap: 3,
          mb: 2,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: 14,
              fontWeight: "500 !important",
            }}
          >
            Jenis Juruaudit di dalam perlembagaan:{" "}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "600 !important",
            }}
          >
            {juruauditType}
          </Typography>
        </Box>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: 14,
              fontWeight: "500 !important",
            }}
          >
            Bilangan Juruaudit di dalam perlembagaan:{" "}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "600 !important",
            }}
          >
            {juruauditCount} Orang
          </Typography>
        </Box>
      </Box>
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("auditorAppointmentDate")}
        </Typography>
        <Controller
          name="date"
          // rules={{
          //   required: t("fieldRequired"),
          // }}
          control={control}
          render={({ field }) => {
            return (
              <Input
                type="date"
                required
                label={t("juruauditAppointmentList")}
                availableDate={appointedDateList}
                disabled={AppointedDatesListIsLoading || isviewStatement}
                value={savedAppointmentDate}
                onChange={(e) => {
                  setSavedAppointmentDate(e.target.value);
                  handleDateChange(e.target.value);
                  setValue("date", e.target.value);
                }}
              />
            );
          }}
        />
        <Box sx={{ display: "grid", justifyContent: "flex-end", mt: 2 }}>
          <ButtonPrevious
            variant="outlined"
            sx={{
              bgcolor: "white",
              "&:hover": { bgcolor: "white" },
            }}
            onClick={handleClearSearch}
          >
            {t("previous")}
          </ButtonPrevious>
        </Box>
      </Box>
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("auditorList")}
        </Typography>
        <Box sx={{ my: 3 }} display={"flex"} justifyContent={"flex-end"}>
          {isDisabled || statementComplete ? null : (
            <ButtonOutline onClick={handleDaftarJuruaudit}>
              {t("registerAuditor")}
            </ButtonOutline>
          )}
        </Box>

        <DataTable
          columns={columns}
          rows={auditorList}
          isLoading={auditorListIsLoading}
          page={page}
          rowsPerPage={pageSize}
          totalCount={total}
          onPageChange={handleChangePage}
          onPageSizeChange={handlePageSizeChange}
        />
      </Box>
      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline
          sx={{
            bgcolor: "white",
            "&:hover": { bgcolor: "white" },
            width: isMobile ? "100%" : "auto",
          }}
          onClick={handleBackActions}
        >
          {t("back")}
        </ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{
            width: isMobile ? "100%" : "auto",
          }}
          onClick={onSubmit}
          // disabled={createFeedBackIsloading}
        >
          {t("next")}
        </ButtonPrimary>
      </Grid>
    </Box>
  );
};

export default JuruAudit;
