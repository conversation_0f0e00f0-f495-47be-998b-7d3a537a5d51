import { config } from "dotenv-safe";

import react from "@vitejs/plugin-react";
import svgr from "vite-plugin-svgr";
import { defineConfig } from "vite";
import * as path from 'path';

config()

// export default defineConfig({
//   plugins: [react(), svgr()],
//   server: {
//     proxy: {
//       '/api': {
//         target: 'https://a-dev.ros.gov.my',
//         changeOrigin: true,
//         secure: false,

//       }
//     }
//   },
//   resolve: {
//     alias: {
//       '@': path.resolve(__dirname, './src'),
//     },
//   },
// })

export default defineConfig({
  plugins: [react(), svgr()],
  server: {
    proxy: {
      '/society': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
      },
      '/user': {
        target: 'http://localhost:8085',
        changeOrigin: true,
        secure: false,
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
})
