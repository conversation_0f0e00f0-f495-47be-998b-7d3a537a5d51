import { Route } from "react-router-dom";
import { paparanPertubuhan } from "./paparan-pertubuhan";
import { pengurusanPertubuhan } from "./pengurusan-pertubuhan";
import { pengurusanIndividu } from "./pengurusan-individu";
import { kelulusan } from "./kelulusan";
import { perlembagaan } from "./perlembagaan";
import { settings } from "./settings";
import { externalUser } from "./external-user";
import { pembaharuanSetiausaha } from "./pembaharuan-setiausaha";
import { society } from "./society";
import { senarai } from "./senarai";

export const pertubuhan = {
  resources: [
    {
      name: "society/admin/meeting/list",
      list: "/society/admin/meeting/list",
    },
    {
      name: "society/meeting/search",
      create: "/society/meeting/search",
    },
    {
      name: "society/meeting/create",
      create: "/society/meeting/create",
    },
    {
      name: "society/branch/getBranch",
      create: "/society/branch/getBranch",
    },
    {
      name: "society/branch/search",
      create: "/society/branch/search",
    },
  ],
  routes: (
    <Route path="/pertubuhan">
      {externalUser.routes}
      {society.routes}
      {senarai.routes}
      {pembaharuanSetiausaha.routes}
      {paparanPertubuhan.routes}
      {pengurusanPertubuhan.routes}
      {pengurusanIndividu.routes}
      {perlembagaan.routes}
      {kelulusan.routes}
      {settings.routes}
    </Route>
  ),
};
