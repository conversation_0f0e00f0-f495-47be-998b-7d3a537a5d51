import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Box,
  Button,
  Checkbox,
  Grid,
  TextField,
  Typography,
  Card,
  Dialog,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import { ButtonPrimary } from "../../../../components/button";
import Input from "../../../../components/input/Input";
import { useCustom } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { CitizenshipStatus, MALAYSIA } from "../../../../helpers/enums";

const CustomDialog = ({
  open,
  onClose,
  onConfirm,
}: {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      sx={{
        "& .MuiDialog-paper": {
          borderRadius: "16px",
          padding: "24px",
          width: "600px",
          maxWidth: "90%",
        },
      }}
    >
      <DialogContent sx={{ textAlign: "center", pb: 0 }}>
        {/* <Typography
          sx={{
            fontSize: '20px',
            fontWeight: 600,
            color: '#101828',
            mb: 2
          }}
        >
          Hantar permohonan?
        </Typography> */}
        <Typography
          sx={{
            fontSize: "14px",
            color: "#475467",
          }}
        >
          Adakah anda pasti untuk menghantar maklumbalas ini?
        </Typography>
      </DialogContent>
      <DialogActions
        sx={{
          justifyContent: "center",
          gap: 2,
          pt: 3,
          pb: 2,
          px: 3,
          flexDirection: "column-reverse",
        }}
      >
        <Button
          onClick={onClose}
          sx={{
            fontFamily: "Poppins",
            fontSize: "14px",
            fontWeight: 400,
            lineHeight: "12px",
            textAlign: "center",
            textDecorationLine: "underline",
            textDecorationStyle: "solid",
            textUnderlinePosition: "from-font",
            textDecorationSkipInk: "none",
            color: "#DADADA",
            textTransform: "none",
            "&:hover": {
              backgroundColor: "transparent",
              textDecoration: "underline",
            },
          }}
        >
          Kembali
        </Button>
        <Button
          onClick={onConfirm}
          variant="contained"
          sx={{
            backgroundColor: "var(--primary-color)",
            color: "white",
            textTransform: "none",
            fontSize: "14px",
            fontWeight: 500,
            minWidth: "100px",
            borderRadius: "8px",
            "&:hover": {
              backgroundColor: "#3BB0B0",
            },
          }}
        >
          Ya
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export const MaklumatPermohonan: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [pekerjaan, setPekerjaan] = useState("yes");
  const [employerAddress, setEmployerAddress] = useState("in");
  const [masaMula, setMasaMula] = useState("09:00");
  const [masaTamat, setMasaTamat] = useState("17:00");
  const [isiMaklumat, setIsiMaklumat] = useState("yes");

  const [members, setMembers] = useState([
    { id: 1, name: "Ahmad bin Daud", role: "Pengerusi", status: "Present" },
    { id: 2, name: "Mizz Nina", role: "Setiausaha", status: "Present" },
    { id: 3, name: "Ali bin Abu", role: "Bendahari", status: "Present" },
    { id: 4, name: "Siti Aminah", role: "AJK", status: "Present" },
  ]);

  const [newMember, setNewMember] = useState({ name: "", role: "" });
  const [isAdding, setIsAdding] = useState(false);

  const [dialogSaveOpen, setDialogSaveOpen] = useState(false);
  const [dialogAlertSaveOpen, setDialogAlertSaveOpen] = useState(false);

  const [state, setState] = useState({ id: 14, name: "Selangor" });
  const [district, setDistrict] = useState({ id: 1, name: "Petaling" });
  const [stateSecretary, setStateSecretary] = useState({
    id: 14,
    name: "Selangor",
  });
  const [districtSecretary, setDistrictSecretary] = useState({
    id: 1,
    name: "Petaling",
  });

  const [currentPage, setCurrentPage] = useState(1);

  const [attendanceInputs, setAttendanceInputs] = useState([
    { name: "Ahmad bin Daud", role: "Pengerusi" },
    { name: "Ali bin Abu", role: "Setiausaha" },
    { name: "Siti Aminah", role: "Bendahari" },
    { name: "Muhammad Ismail", role: "AJK" },
    { name: "Nurul Huda", role: "AJK" },
  ]);

  const [formValues, setFormValues] = useState({
    placeOfBirth: "Hospital Kuala Lumpur",
    residentialAddress: "123, Jalan Merdeka\nTaman Sejahtera\nPetaling Jaya",
    city: "Petaling Jaya",
    postcode: "46150",
    email: "<EMAIL>",
    phoneCountryCode: "60",
    phoneNumber: "123456789",
    homePhoneCountryCode: "60",
    homePhoneNumber: "987654321",
    officePhoneCountryCode: "60",
    officePhoneNumber: "456789123",

    employerName: "Syarikat Teknologi ABC Sdn Bhd",
    employerAddress:
      "456, Jalan Teknologi\nTaman Industri Digital\nPetaling Jaya",
    employerCity: "Petaling Jaya",
    employerPostcode: "46200",

    meetingPlace: "Dewan Komuniti Taman Sejahtera",
    meetingAddress: "789, Jalan Harmoni\nTaman Sejahtera\nPetaling Jaya",
    meetingCity: "Petaling Jaya",
    meetingPostcode: "46150",
    totalAttendees: "15",

    chairmanSpeech:
      "Bismillahirrahmanirrahim. Assalamualaikum dan salam sejahtera. Terima kasih kepada semua ahli jawatankuasa yang hadir pada mesyuarat hari ini. Mesyuarat ini bertujuan untuk membincangkan hal-hal berkaitan pengurusan pertubuhan dan aktiviti yang akan datang.",
    matters:
      "1. Pembentangan laporan kewangan suku tahunan\n2. Perancangan aktiviti kebajikan masyarakat\n3. Perbincangan tentang program pembangunan ahli\n4. Cadangan penambahbaikan infrastruktur pertubuhan",
    otherMatters:
      "1. Cadangan untuk mengadakan majlis berbuka puasa bersama ahli\n2. Perbincangan tentang kerjasama dengan pertubuhan lain",
    closing:
      "Mesyuarat ditangguhkan pada jam 5:00 petang. Terima kasih kepada semua yang hadir.",
    preparedBy: "Ali bin Abu (Setiausaha)",
    verifiedBy: "Ahmad bin Daud (Pengerusi)",

    oldSecretaryName: "Muhammad bin Abdullah",
    oldSecretaryId: "800101012345",

    meetingType: "Mesyuarat Agung Tahunan",
    meetingMethod: "Bersemuka",
    platformType: "Tidak Berkenaan",
    reasonForChange: "Setiausaha lama telah bersara",

    meetingDate: "2024-03-20",
    meetingStartTime: "09:00",
    meetingEndTime: "17:00",
    meetingVenue: "Dewan Komuniti Taman Sejahtera",
    meetingLocation: {
      address: "789, Jalan Harmoni, Taman Sejahtera",
      state: "Selangor",
      district: "Petaling",
      city: "Petaling Jaya",
      postcode: "46150",
    },
  });

  const handleConfirm = () => {
    setDialogSaveOpen(false);
    setDialogAlertSaveOpen(true);
  };

  const handleAddMember = () => {
    if (newMember.name && newMember.role) {
      const newMemberId = members.length + 1;
      setMembers([
        ...members,
        { id: newMemberId, ...newMember, status: "Present" },
      ]);
      setNewMember({ name: "", role: "" });
      setIsAdding(false);
    }
  };

  const handleDeleteMember = (id: number) => {
    setMembers(members.filter((member) => member.id !== id));
  };

  const calculateDuration = () => {
    if (masaMula && masaTamat) {
      const start = new Date(`2000-01-01T${masaMula}`);
      const end = new Date(`2000-01-01T${masaTamat}`);
      const diff = (end.getTime() - start.getTime()) / (1000 * 60 * 60);
      return diff.toFixed(2);
    }
    return "";
  };

  const { data: addressList, isLoading: isLoadingAddress } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData = addressList?.data?.data || [];

  const { data: meetingList, isLoading: isMeetingLoading } = useCustom({
    url: `${API_URL}/society/admin/meeting/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const meetingData = meetingList?.data?.data || [];

  const handleAttendanceChange = (
    index: number,
    field: "name" | "role",
    value: string
  ) => {
    const newInputs = [...attendanceInputs];
    newInputs[index][field] = value;
    setAttendanceInputs(newInputs);
  };

  const handleAddAttendance = () => {
    setAttendanceInputs([...attendanceInputs, { name: "", role: "" }]);
  };

  const handleDeleteAttendance = (index: number) => {
    const newInputs = attendanceInputs.filter((_, i) => i !== index);
    setAttendanceInputs(newInputs);
  };

  const handleClearAttendance = () => {
    setAttendanceInputs([{ name: "", role: "" }]);
  };

  return (
    <>
      <Box
        sx={{
          backgroundColor: "white",
          p: 3,
          borderRadius: "16px",
          marginBottom: 3,
        }}
      >
        <Card
          sx={{
            p: 3,
            backgroundColor: "var(--primary-color)",
            borderRadius: "15px",
            boxShadow: "none",
            height: "100px",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "flex-start",
            position: "relative",
            overflow: "hidden",
            "&::before": {
              content: '""',
              position: "absolute",
              top: 0,
              right: 0,
              width: "150px",
              height: "100%",
              backgroundImage: `url("/pattern.svg")`,
              backgroundSize: "contain",
              backgroundRepeat: "no-repeat",
              backgroundPosition: "right center",
              opacity: 0.2,
            },
          }}
        >
          <Box
            sx={{ display: "flex", alignItems: "center", gap: 2, zIndex: 1 }}
          >
            <Typography
              sx={{
                fontFamily: "Poppins",
                fontSize: "20px",
                fontWeight: 600,
                color: "white",
              }}
            >
              Persatuan Kucing Jalanan
            </Typography>
          </Box>
          <Typography
            sx={{
              fontFamily: "Poppins",
              fontSize: "14px",
              color: "white",
              mt: 1,
              zIndex: 1,
              opacity: 0.8,
            }}
          >
            PPM-013-10-21012015
          </Typography>
        </Card>
      </Box>

      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "16px" }}>
        {currentPage === 1 ? (
          <>
            <Box
              sx={{
                border: "1px solid #DADADA",
                borderRadius: "8px",
                p: { xs: 2, sm: 3 },
                backgroundColor: "transparent",
                marginBottom: 3,
              }}
            >
              <Typography
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "16px",
                  fontWeight: "bold",
                  mb: 2,
                }}
              >
                Pembaharuan setiausaha
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Grid container spacing={2} pl={4} pt={2}>
                  <Input disabled label={t("position")} value="Setiausaha" />
                </Grid>
              </Box>
            </Box>

            <Box
              sx={{
                border: "1px solid #DADADA",
                borderRadius: "8px",
                p: { xs: 2, sm: 3 },
                backgroundColor: "transparent",
                marginBottom: 3,
              }}
            >
              <Typography
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "16px",
                  fontWeight: "bold",
                  mb: 2,
                }}
              >
                Maklumat Peribadi Pengerusi
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Grid container spacing={2} pl={4} pt={2}>
                  <Input disabled label={t("idType")} value="MyKad" />
                  <Input disabled label={t("idNumber")} value="901011101191" />
                  <Input disabled label={t("gelaran")} value="Dato'" />
                  <Input
                    disabled
                    label={t("fullName")}
                    value="Ahmad Daud bin Sulaiman"
                  />
                  <Input disabled label={t("gender")} value="Lelaki" />
                  <Input
                    disabled
                    label={t("citizenship")}
                    type="select"
                    options={CitizenshipStatus.map((item) => ({
                      ...item,
                      label: t(item.label),
                    }))}
                    value={1}
                  />

                  <Input disabled label={t("dateOfBirth")} value="11/09/1990" />
                  <Input
                    disabled
                    label={t("placeOfBirth")}
                    placeholder={t("inputPlaceholder")}
                    value={formValues.placeOfBirth}
                  />
                  <Input
                    disabled
                    required
                    type="select"
                    value={pekerjaan}
                    onChange={(e: React.ChangeEvent<{ value: unknown }>) =>
                      setPekerjaan(e.target.value as string)
                    }
                    options={[
                      { value: "no", label: "Tidak Bekerja" },
                      {
                        value: "yes",
                        label: "Bekerja",
                      },
                    ]}
                    label={t("pekerjaan")}
                  />
                  <Input
                    disabled
                    required
                    label={t("residentialAddress")}
                    multiline
                    rows={3}
                    value={formValues.residentialAddress}
                  />
                  {/* <Input disabled label={t("country")} value="Malaysia" /> */}
                  {/* <Input disabled label={t("state")} value="Selangor" /> */}
                  <Input
                    disabled
                    required
                    type="select"
                    dynamicData={addressData}
                    isLoadingData={isLoadingAddress}
                    label={t("state")}
                    placeholder={t("selectPlaceholder")}
                    useDynamicData
                  />
                  <Input
                    disabled
                    required
                    type="select"
                    dynamicData={addressData}
                    isLoadingData={isLoadingAddress}
                    label={t("district")}
                    placeholder={t("selectPlaceholder")}
                    useDynamicData
                  />
                  <Input disabled label={t("city")} />
                  <Input disabled required label={t("postcode")} />
                  <Input disabled required label={t("email")} />
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Typography
                        style={{
                          fontFamily: "Poppins",
                          fontSize: "14px",
                          fontWeight: 400,
                          lineHeight: "18px",
                          textAlign: "left",
                          textUnderlinePosition: "from-font",
                          textDecorationSkipInk: "none",
                          color: "#666666",
                        }}
                      >
                        Nombor Telefon<span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8} sx={{ display: "flex", gap: 1 }}>
                      <TextField
                        disabled
                        size="small"
                        sx={{ width: "80px" }}
                        value={formValues.phoneCountryCode}
                      />
                      <TextField
                        disabled
                        fullWidth
                        size="small"
                        value={formValues.phoneNumber}
                      />
                    </Grid>

                    <Grid item xs={12} sm={4}>
                      <Typography
                        style={{
                          fontFamily: "Poppins",
                          fontSize: "14px",
                          fontWeight: 400,
                          lineHeight: "18px",
                          textAlign: "left",
                          textUnderlinePosition: "from-font",
                          textDecorationSkipInk: "none",
                          color: "#666666",
                        }}
                      >
                        Nombor Telefon Rumah
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8} sx={{ display: "flex", gap: 1 }}>
                      <TextField
                        disabled
                        size="small"
                        sx={{ width: "80px" }}
                        value={formValues.homePhoneCountryCode}
                      />
                      <TextField
                        disabled
                        fullWidth
                        size="small"
                        value={formValues.homePhoneNumber}
                      />
                    </Grid>

                    <Grid item xs={12} sm={4}>
                      <Typography
                        style={{
                          fontFamily: "Poppins",
                          fontSize: "14px",
                          fontWeight: 400,
                          lineHeight: "18px",
                          textAlign: "left",
                          textUnderlinePosition: "from-font",
                          textDecorationSkipInk: "none",
                          color: "#666666",
                        }}
                      >
                        Nombor telefon pejabat
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={8} sx={{ display: "flex", gap: 1 }}>
                      <TextField
                        disabled
                        size="small"
                        sx={{ width: "80px" }}
                        value={formValues.officePhoneCountryCode}
                      />
                      <TextField
                        disabled
                        fullWidth
                        size="small"
                        value={formValues.officePhoneNumber}
                      />
                    </Grid>
                  </Grid>
                </Grid>
              </Box>
            </Box>

            <Box
              sx={{
                border: "1px solid #DADADA",
                borderRadius: "8px",
                p: { xs: 2, sm: 3 },
                backgroundColor: "transparent",
                marginBottom: 3,
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography
                  sx={{
                    color: "#FF0000",
                    fontSize: "16px",
                    fontWeight: "bold",
                  }}
                >
                  Peringatan :
                </Typography>

                <Typography
                  sx={{
                    color: "#666666",
                    fontSize: "14px",
                  }}
                >
                  Bagi ahli jawatankuasa yang TIDAK BEKERJA/ PESARA, maklumat
                  majikan tidak perlu diisi
                </Typography>
              </Box>
            </Box>

            <Box
              sx={{
                border: "1px solid #DADADA",
                borderRadius: "8px",
                p: { xs: 2, sm: 3 },
                backgroundColor: "transparent",
                marginBottom: 3,
              }}
            >
              <Typography
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "16px",
                  fontWeight: "bold",
                  mb: 2,
                }}
              >
                Maklumat Majikan
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Grid container spacing={2} pl={4} pt={2}>
                  <Input disabled label="Nama Majikan" />
                  <Input
                    disabled
                    required
                    type="select"
                    dynamicData={addressData}
                    isLoadingData={isLoadingAddress}
                    label="Alamat Majikan"
                    placeholder={t("selectPlaceholder")}
                    useDynamicData
                  />
                  <Input disabled />
                  {/* <Input disabled label={t("country")} value="Malaysia" /> */}
                  {/* <Input disabled label={t("state")} value="Selangor" /> */}
                  <Input
                    disabled
                    required
                    type="select"
                    dynamicData={addressData}
                    isLoadingData={isLoadingAddress}
                    label={t("country")}
                    placeholder={t("selectPlaceholder")}
                    useDynamicData
                  />
                  <Input
                    disabled
                    required
                    type="select"
                    dynamicData={addressData}
                    isLoadingData={isLoadingAddress}
                    label={t("state")}
                    placeholder={t("selectPlaceholder")}
                    useDynamicData
                  />
                  <Input
                    disabled
                    required
                    type="select"
                    dynamicData={addressData}
                    isLoadingData={isLoadingAddress}
                    label={t("district")}
                    placeholder={t("selectPlaceholder")}
                    useDynamicData
                  />
                  <Input disabled label={t("city")} />
                  <Input disabled required label={t("postcode")} />
                </Grid>
              </Box>
            </Box>
          </>
        ) : (
          <>
            {/* {pekerjaan == "yes" && (
              <>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    backgroundColor: "#e0f2f1",
                    px: 2,
                    py: 1,
                    mb: 3,
                    borderRadius: "16px",
                  }}
                >
                  <Typography
                    variant="h6"
                    component="h2"
                    sx={{ fontWeight: "bold", color: "black", fontSize: "16px" }}
                  >
                    {t("employerInfo")}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ color: "red", fontWeight: "bold" }}
                  >
                    {t("maklumatMajikanWarning")}
                  </Typography>
                </Box>

                <Grid container spacing={2} pl={4} pt={2}>
                  <Input
                    label={t("employerName")}
                    placeholder={t("inputPlaceholder")}
                  />
                  <Input
                    type="select"
                    value={employerAddress}
                    onChange={(e: React.ChangeEvent<{ value: unknown }>) =>
                      setEmployerAddress(e.target.value as string)
                    }
                    options={[
                      { value: "out", label: "Luar Negara" },
                      {
                        value: "in",
                        label: "Dalam Negara",
                      },
                    ]}
                    label={t("employerAddress")}
                  />
                  <Input
                    isLabel={false}
                    label=""
                    placeholder={t("inputPlaceholder")}
                  />
                  <Input required type="select" options={[]} label={t("country")} />

                  {employerAddress == "in" && (
                    <>
                      <Input
                        type="select"
                        dynamicData={addressData}
                        isLoadingData={isLoadingAddress}
                        label={t("state")}
                        placeholder={t("selectPlaceholder")}
                        useDynamicData
                        onChange={(e) => setState({id:e.target.value, name: e.target.label})}
                        pid={MALAYSIA}
                      />
                      <Input
                        type="select"
                        dynamicData={addressData}
                        isLoadingData={isLoadingAddress}
                        label={t("district")}
                        placeholder={t("selectPlaceholder")}
                        useDynamicData
                        onChange={(e) => setDistrict({id:e.target.value, name: e.target.label})}
                        pid={state.id}
                      />
                      <Input
                        label={t("city")}
                        placeholder={t("inputPlaceholder")}
                      />
                      <Input
                        label={t("postcode")}
                        placeholder={t("inputPlaceholder")}
                      />
                    </>
                  )}
                </Grid>
              </>
            )} */}

            {/* <Typography
              variant="subtitle1"
              sx={{
                backgroundColor: "#e0f2f1",
                px: 2,
                py: 1,
                mb: 3,
                borderRadius: "16px",
              }}
            >
              {t("pelantikanSetiausaha")}
            </Typography> */}

            <Box
              sx={{
                border: "1px solid #DADADA",
                borderRadius: "8px",
                p: { xs: 2, sm: 3 },
                backgroundColor: "transparent",
                marginBottom: 3,
              }}
            >
              <Typography
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "16px",
                  fontWeight: "bold",
                  mb: 2,
                }}
              >
                {t("pelantikanSetiausaha")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Grid container spacing={2} pl={4} pt={2}>
                  <Input
                    disabled
                    required
                    type="select"
                    dynamicData={meetingData}
                    isLoadingData={isMeetingLoading}
                    label={t("meetingType")}
                    placeholder={t("selectPlaceholder")}
                    useDynamicData
                    value={formValues.meetingType}
                    onChange={(e) =>
                      setFormValues({
                        ...formValues,
                        meetingType: e.target.value,
                      })
                    }
                  />
                  <Input
                    disabled
                    required
                    type="select"
                    dynamicData={meetingData}
                    isLoadingData={isMeetingLoading}
                    label={t("meetingMethod")}
                    placeholder={t("selectPlaceholder")}
                    useDynamicData
                    pid={2}
                    value={formValues.meetingMethod}
                    onChange={(e) =>
                      setFormValues({
                        ...formValues,
                        meetingMethod: e.target.value,
                      })
                    }
                  />
                  <Input
                    disabled
                    required
                    type="select"
                    dynamicData={meetingData}
                    isLoadingData={isMeetingLoading}
                    label={t("platformType")}
                    placeholder={t("selectPlaceholder")}
                    useDynamicData
                    pid={3}
                    value={formValues.platformType}
                    onChange={(e) =>
                      setFormValues({
                        ...formValues,
                        platformType: e.target.value,
                      })
                    }
                  />
                  <Input
                    disabled
                    label={t("sebabTukarSetiausaha")}
                    placeholder={t("inputPlaceholder")}
                  />
                </Grid>
              </Box>
            </Box>

            <Box
              sx={{
                border: "1px solid #DADADA",
                borderRadius: "8px",
                p: { xs: 2, sm: 3 },
                backgroundColor: "transparent",
                marginBottom: 3,
              }}
            >
              <Typography
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "16px",
                  fontWeight: "bold",
                  mb: 2,
                }}
              >
                Masa dan Tarikh Mesyuarat
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Grid container spacing={2} pl={4} pt={2}>
                  <Grid item xs={12} sm={4} sx={{ "&.MuiGrid-root": { p: 0 } }}>
                    <Typography
                      style={{
                        fontFamily: "Poppins",
                        fontSize: "14px",
                        fontWeight: 400,
                        lineHeight: "18px",
                        textAlign: "left",
                        textUnderlinePosition: "from-font",
                        textDecorationSkipInk: "none",
                        color: "#666666",
                      }}
                    >
                      {t("tarikhMesyuarat")}{" "}
                      <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={8}
                    sx={{ "&.MuiGrid-root": { py: 0, pr: 0, pl: 1 } }}
                  >
                    <TextField
                      disabled
                      fullWidth
                      required
                      type="date"
                      value={formValues.meetingDate}
                      onChange={(e) =>
                        setFormValues({
                          ...formValues,
                          meetingDate: e.target.value,
                        })
                      }
                      inputProps={{
                        min: new Date().toISOString().split("T")[0],
                      }}
                      sx={{
                        "& .MuiOutlinedInput-input": {
                          padding: "8px 14px",
                        },
                      }}
                    />
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={4}
                    sx={{ "&.MuiGrid-root": { p: 0 }, mt: 1, mb: 1 }}
                  >
                    <Typography
                      style={{
                        fontFamily: "Poppins",
                        fontSize: "14px",
                        fontWeight: 400,
                        lineHeight: "18px",
                        textAlign: "left",
                        textUnderlinePosition: "from-font",
                        textDecorationSkipInk: "none",
                        color: "#666666",
                      }}
                    >
                      {t("masa")} <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={8}
                    sx={{
                      "&.MuiGrid-root": { py: 0, pr: 0, pl: 1 },
                      mt: 1,
                      mb: 1,
                    }}
                  >
                    <Box display="flex" alignItems="center">
                      <TextField
                        disabled
                        value={formValues.meetingStartTime}
                        onChange={(e) =>
                          setFormValues({
                            ...formValues,
                            meetingStartTime: e.target.value,
                          })
                        }
                        type="time"
                        sx={{
                          width: "150px",
                          "& .MuiOutlinedInput-input": {
                            padding: "8px 14px",
                          },
                        }}
                      />
                      <Box sx={{ mx: 1 }}>—</Box>
                      <TextField
                        disabled
                        value={formValues.meetingEndTime}
                        onChange={(e) =>
                          setFormValues({
                            ...formValues,
                            meetingEndTime: e.target.value,
                          })
                        }
                        type="time"
                        sx={{
                          width: "150px",
                          "& .MuiOutlinedInput-input": {
                            padding: "8px 14px",
                          },
                        }}
                      />
                      <Typography sx={{ ml: 2 }}>
                        {calculateDuration() &&
                          `${calculateDuration()} ${t("jam")}`}
                      </Typography>
                    </Box>
                  </Grid>
                  <Input
                    disabled
                    label={t("tempatMesyuarat")}
                    placeholder={t("inputPlaceholder")}
                  />
                  <Grid
                    item
                    xs={12}
                    sm={4}
                    sx={{ "&.MuiGrid-root": { p: 0 }, mb: 1 }}
                  >
                    <Typography
                      style={{
                        fontFamily: "Poppins",
                        fontSize: "14px",
                        fontWeight: 400,
                        lineHeight: "18px",
                        textAlign: "left",
                        textUnderlinePosition: "from-font",
                        textDecorationSkipInk: "none",
                        color: "#666666",
                      }}
                    >
                      {t("petaLokasi")} <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={8}
                    sx={{ "&.MuiGrid-root": { py: 0, pr: 0, pl: 1 }, mb: 1 }}
                  >
                    <Box>
                      <iframe
                        src="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d721169.4567849896!2d102.08600387522937!3d3.111625028262089!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sid!2sid!4v1727317814713!5m2!1sid!2sid"
                        width="100%"
                        height="250"
                        loading="lazy"
                      ></iframe>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Box>

            <Box
              sx={{
                border: "1px solid #DADADA",
                borderRadius: "8px",
                p: { xs: 2, sm: 3 },
                backgroundColor: "transparent",
                marginBottom: 3,
              }}
            >
              <Typography
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "16px",
                  fontWeight: "bold",
                  mb: 2,
                }}
              >
                Alamat Tempat Mesyuarat
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Grid container spacing={2} pl={4} pt={2}>
                  <Input
                    disabled
                    required
                    label={t("alamatTempatMesyuarat")}
                    placeholder={t("inputPlaceholder")}
                  />
                  <Input
                    disabled
                    required
                    type="select"
                    dynamicData={addressData}
                    isLoadingData={isLoadingAddress}
                    label={t("state")}
                    placeholder={t("selectPlaceholder")}
                    useDynamicData
                    onChange={(e) =>
                      setStateSecretary({
                        id: e.target.value,
                        name: e.target.label,
                      })
                    }
                    pid={MALAYSIA}
                  />
                  <Input
                    disabled
                    required
                    type="select"
                    dynamicData={addressData}
                    isLoadingData={isLoadingAddress}
                    label={t("district")}
                    placeholder={t("selectPlaceholder")}
                    useDynamicData
                    onChange={(e) =>
                      setDistrictSecretary({
                        id: e.target.value,
                        name: e.target.label,
                      })
                    }
                    pid={stateSecretary.id}
                  />
                  <Input
                    disabled
                    label={t("city")}
                    placeholder={t("inputPlaceholder")}
                  />
                  <Input
                    disabled
                    required
                    label={t("postcode")}
                    placeholder={t("inputPlaceholder")}
                  />
                </Grid>
              </Box>
            </Box>

            <Box
              sx={{
                border: "1px solid #DADADA",
                borderRadius: "8px",
                p: { xs: 2, sm: 3 },
                backgroundColor: "transparent",
                marginBottom: 3,
              }}
            >
              <Typography
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "16px",
                  fontWeight: "bold",
                  mb: 2,
                }}
              >
                Kehadiran Ahli Mesyuarat
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 2,
                }}
              >
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Input
                      label="Jumlah kehadiran mesyuarat*"
                      placeholder="Masukkan jumlah"
                      type="number"
                      disabled
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ display: "flex", gap: 2 }}>
                      <Typography
                        style={{
                          fontFamily: "Poppins",
                          fontSize: "14px",
                          fontWeight: 400,
                          lineHeight: "18px",
                          textAlign: "left",
                          textUnderlinePosition: "from-font",
                          textDecorationSkipInk: "none",
                          color: "#666666",
                        }}
                      >
                        Kehadiran AJK
                      </Typography>

                      <Box sx={{ flex: 1, marginLeft: 36 }}>
                        {attendanceInputs.map((input, index) => (
                          <Box
                            key={index}
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 2,
                              mb: 2,
                            }}
                          >
                            <TextField
                              size="small"
                              placeholder="Nama"
                              value={input.name}
                              onChange={(e) =>
                                handleAttendanceChange(
                                  index,
                                  "name",
                                  e.target.value
                                )
                              }
                              sx={{
                                flex: 1,
                                "& .MuiOutlinedInput-root": {
                                  borderRadius: "4px",
                                },
                              }}
                              disabled
                            />
                            <TextField
                              size="small"
                              placeholder="Jawatan"
                              value={input.role}
                              onChange={(e) =>
                                handleAttendanceChange(
                                  index,
                                  "role",
                                  e.target.value
                                )
                              }
                              sx={{
                                flex: 1,
                                "& .MuiOutlinedInput-root": {
                                  borderRadius: "4px",
                                },
                              }}
                              disabled
                            />
                            {/* <IconButton
                              onClick={() => handleDeleteAttendance(index)}
                              size="small"
                              sx={{
                                color: '#FF0000',
                                p: 0,
                                '&:hover': {
                                  backgroundColor: 'transparent',
                                  color: '#CC0000'
                                }
                              }}
                            >
                              <Delete />
                            </IconButton> */}
                          </Box>
                        ))}

                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "flex-end",
                            gap: 2,
                          }}
                        >
                          <Button
                            onClick={handleClearAttendance}
                            sx={{
                              color: "#979797",
                              fontSize: "12px",
                              fontWeight: 400,
                              lineHeight: "9.68px",
                              textAlign: "left",
                              textDecorationLine: "underline",
                              textDecorationStyle: "solid",
                              textUnderlinePosition: "from-font",
                              textDecorationSkipInk: "none",
                              "&:hover": {
                                backgroundColor: "transparent",
                                textDecoration: "underline",
                              },
                            }}
                            disabled
                          >
                            {t("semula")}
                          </Button>
                          <ButtonPrimary
                            onClick={handleAddAttendance}
                            sx={{
                              color: "white",
                              textTransform: "none",
                              fontSize: "14px",
                            }}
                            disabled
                          >
                            Tambah AJK
                          </ButtonPrimary>
                        </Box>
                      </Box>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Box>

            <Box
              sx={{
                border: "1px solid #DADADA",
                borderRadius: "8px",
                p: { xs: 2, sm: 3 },
                backgroundColor: "transparent",
                marginBottom: 3,
              }}
            >
              <Typography
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "16px",
                  fontWeight: "bold",
                  mb: 2,
                }}
              >
                Minit Mesyuarat
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Grid container spacing={2} pl={4} pt={2}>
                  <Grid
                    item
                    xs={12}
                    sm={4}
                    sx={{ "&.MuiGrid-root": { p: 0 }, mb: 1 }}
                  >
                    <Typography
                      style={{
                        fontFamily: "Poppins",
                        fontSize: "14px",
                        fontWeight: 400,
                        lineHeight: "18px",
                        textAlign: "left",
                        textUnderlinePosition: "from-font",
                        textDecorationSkipInk: "none",
                        color: "#666666",
                      }}
                    >
                      {t("minitMesyuarat")}{" "}
                      <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={8}
                    sx={{ "&.MuiGrid-root": { py: 0, pr: 0, pl: 1 }, mb: 1 }}
                  >
                    <Box
                      sx={{
                        border: "1px dashed #D0D5DD",
                        borderRadius: "8px",
                        p: 3,
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        gap: 1,
                        backgroundColor: "#FFFFFF",
                        cursor: "not-allowed",
                        "&:hover": {
                          backgroundColor: "#FFFFFF",
                        },
                      }}
                    >
                      <svg
                        width="46"
                        height="46"
                        viewBox="0 0 46 46"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <rect
                          x="3"
                          y="3"
                          width="40"
                          height="40"
                          rx="20"
                          fill="#F2F4F7"
                        />
                        <rect
                          x="3"
                          y="3"
                          width="40"
                          height="40"
                          rx="20"
                          stroke="#F9FAFB"
                          stroke-width="6"
                        />
                        <g clipPath="url(#clip0_9584_20829)">
                          <path
                            d="M26.3326 26.3352L22.9992 23.0019M22.9992 23.0019L19.6659 26.3352M22.9992 23.0019V30.5019M29.9909 28.3269C30.8037 27.8838 31.4458 27.1826 31.8158 26.334C32.1858 25.4855 32.2627 24.5379 32.0344 23.6408C31.8061 22.7436 31.2855 21.9481 30.5548 21.3797C29.8241 20.8113 28.925 20.5025 27.9992 20.5019H26.9492C26.697 19.5262 26.2269 18.6205 25.5742 17.8527C24.9215 17.0849 24.1033 16.4751 23.181 16.069C22.2587 15.663 21.2564 15.4713 20.2493 15.5084C19.2423 15.5455 18.2568 15.8104 17.3669 16.2832C16.477 16.7561 15.7058 17.4244 15.1114 18.2382C14.517 19.0519 14.1148 19.9898 13.9351 20.9814C13.7553 21.9729 13.8027 22.9923 14.0736 23.9629C14.3445 24.9335 14.8319 25.8301 15.4992 26.5852"
                            stroke="#475467"
                            stroke-width="1.66667"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </g>
                        <defs>
                          <clipPath id="clip0_9584_20829">
                            <rect
                              width="20"
                              height="20"
                              fill="white"
                              transform="translate(13 13)"
                            />
                          </clipPath>
                        </defs>
                      </svg>

                      <Typography
                        sx={{
                          color: "var(--primary-color)",
                          fontSize: "14px",
                          fontWeight: 500,
                          textAlign: "center",
                        }}
                      >
                        Muat naik
                      </Typography>

                      <Typography
                        sx={{
                          color: "#475467",
                          fontSize: "12px",
                          textAlign: "center",
                        }}
                      >
                        {"PDF, DOCX, DOC or TXT <25 MB"}
                      </Typography>
                    </Box>

                    {/* <ButtonText sx={{
                      mt: 2,
                      color: 'var(--primary-color)',
                      textDecoration: 'underline',
                      '&:hover': {
                        backgroundColor: 'transparent'
                      }
                    }}>
                      {t("downloadTemplate")}
                    </ButtonText> */}
                  </Grid>

                  <Input
                    disabled
                    label={t("ucapanAluanPengerusi")}
                    multiline
                    rows={3}
                  />
                  <Input
                    disabled
                    label={t("perkaraPerkara")}
                    multiline
                    rows={3}
                    placeholder={t("mattersDiscussedPlaceholder")}
                  />
                  <Input disabled label={t("halHalLain")} multiline rows={3} />
                  <Input disabled label={t("penutup")} multiline rows={3} />
                  <Input disabled label={t("disediakanOlehSetiausaha")} />
                  <Input disabled label={t("disahkanOlehPengerusi")} />
                </Grid>
              </Box>
            </Box>

            <Box
              sx={{
                border: "1px solid #DADADA",
                borderRadius: "8px",
                p: { xs: 2, sm: 3 },
                backgroundColor: "transparent",
                marginBottom: 3,
              }}
            >
              <Typography
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "16px",
                  fontWeight: "bold",
                  mb: 2,
                }}
              >
                {t("personalInfoOldSecretary")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Grid container spacing={2} pl={4} pt={2} mb={7}>
                  <Input disabled required label={t("name")} />
                  <Input required disabled label={t("idNumber")} />
                </Grid>
              </Box>
            </Box>

            <Box
              sx={{
                border: "1px solid #DADADA",
                borderRadius: "8px",
                p: { xs: 2, sm: 3 },
                backgroundColor: "transparent",
                marginBottom: 3,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Grid container spacing={2} pl={4} pt={2}>
                  <Box sx={{ fontSize: "14px" }}>
                    <Checkbox sx={{ p: 0, mr: 1 }} />
                    {t("confirmOldSecretary")}
                  </Box>
                </Grid>
              </Box>
            </Box>
          </>
        )}
        {/* Pagination */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center",
            gap: 1,
            mt: 3,
            mb: 3,
          }}
        >
          {currentPage === 2 && (
            <Button
              onClick={() => setCurrentPage(currentPage - 1)}
              sx={{
                minWidth: "32px",
                height: "32px",
                p: 0,
                borderRadius: "4px",
                borderColor: "#666666",
                color: "#666666",
              }}
            >
              {"Previous"}
            </Button>
          )}
          <Button
            variant={currentPage === 1 ? "contained" : "outlined"}
            onClick={() => setCurrentPage(1)}
            sx={{
              minWidth: "32px",
              height: "32px",
              p: 0,
              borderRadius: "4px",
              backgroundColor:
                currentPage === 1 ? "var(--primary-color)" : "transparent",
              borderColor: "var(--primary-color)",
              color: currentPage === 1 ? "white" : "var(--primary-color)",
              "&:hover": {
                backgroundColor:
                  currentPage === 1 ? "var(--primary-color)" : "transparent",
              },
            }}
          >
            1
          </Button>
          <Button
            variant={currentPage === 2 ? "contained" : "outlined"}
            onClick={() => setCurrentPage(2)}
            sx={{
              minWidth: "32px",
              height: "32px",
              p: 0,
              borderRadius: "4px",
              backgroundColor:
                currentPage === 2 ? "var(--primary-color)" : "transparent",
              borderColor: "var(--primary-color)",
              color: currentPage === 2 ? "white" : "var(--primary-color)",
              "&:hover": {
                backgroundColor:
                  currentPage === 2 ? "var(--primary-color)" : "transparent",
              },
            }}
          >
            2
          </Button>
          {currentPage === 1 && (
            <Button
              onClick={() => setCurrentPage(currentPage + 1)}
              sx={{
                minWidth: "32px",
                height: "32px",
                p: 0,
                borderRadius: "4px",
                borderColor: "#666666",
                color: "#666666",
              }}
            >
              {"Next"}
            </Button>
          )}
        </Box>

        {currentPage === 2 && (
          <Box
            sx={{ display: "flex", justifyContent: "flex-end", mt: 10, gap: 1 }}
          >
            <ButtonPrimary
              onClick={() =>
                navigate(
                  "/pertubuhan/pembaharuan-setiausaha/keputusan-jawatankuasa"
                )
              }
              sx={{
                textTransform: "none",
                fontSize: "14px",
                color: "#FFFFFF",
              }}
            >
              {t("hantar")}
            </ButtonPrimary>
          </Box>
        )}
      </Box>

      <CustomDialog
        open={dialogSaveOpen}
        onClose={() => setDialogSaveOpen(false)}
        onConfirm={handleConfirm}
      />

      <Dialog
        open={dialogAlertSaveOpen}
        onClose={() => setDialogAlertSaveOpen(false)}
        sx={{
          "& .MuiDialog-paper": {
            borderRadius: "16px",
            padding: "24px",
            width: "600px",
            height: "130px",
            maxWidth: "90%",
          },
        }}
      >
        <DialogContent sx={{ textAlign: "center", pb: 0 }}>
          {/* Message with background pattern */}
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              position: "relative",
              mb: 3,
            }}
          >
            <Typography
              sx={{
                fontSize: "14px",
                color: "#475467",
                zIndex: 1,
              }}
            >
              Maklumatbalas anda berjaya dihantar
            </Typography>
            <Box
              sx={{
                position: "absolute",
                left: "35%",
                top: "60%",
                transform: "translate(50%, -50%)",
                opacity: 1.5,
              }}
            >
              <svg
                width="83"
                height="89"
                viewBox="0 0 83 89"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clipPath="url(#clip0_9846_16230)">
                  <path
                    d="M64.9117 18.3628L54.9359 42.2968L39.9352 45.1657L49.9109 21.2317L64.9117 18.3628Z"
                    stroke="var(--primary-color)"
                    strokeOpacity="0.2"
                  />
                  <path
                    d="M69.057 57.1671L54.0562 60.036L39.9378 45.1642L54.9385 42.2953L69.057 57.1671Z"
                    stroke="var(--primary-color)"
                    strokeOpacity="0.2"
                  />
                  <path
                    d="M69.0564 57.1691L34.0925 71.9986L19.0919 74.8672L31.7283 69.5094L54.0556 60.038L69.0564 57.1691Z"
                    stroke="var(--primary-color)"
                    strokeOpacity="0.2"
                  />
                  <path
                    d="M54.056 60.0382L31.7287 69.5096L19.0923 74.8673L14.3861 69.9104L23.4088 49.2787L11.6434 36.8856L45.2072 16.2751L49.0116 20.2811L49.9134 21.2323L39.9376 45.1664L54.056 60.0382Z"
                    stroke="var(--primary-color)"
                    strokeOpacity="0.2"
                  />
                  <path
                    d="M64.9142 18.3619L49.9135 21.2308L49.0117 20.2796L45.2073 16.2736L60.2081 13.4047L64.9142 18.3619Z"
                    stroke="var(--primary-color)"
                    strokeOpacity="0.2"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_9846_16230">
                    <rect
                      width="64"
                      height="74"
                      fill="white"
                      transform="translate(20.5195) rotate(16.0981)"
                    />
                  </clipPath>
                </defs>
              </svg>
            </Box>
          </Box>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default MaklumatPermohonan;
