import { createAction, createSlice } from "@reduxjs/toolkit"

export interface UserResponseBodyGetOnlyData {
  id: number
  name: string
  citizenshipTitle: null | string
  identificationType: number
  identificationNo: string
  profilePicture: null
  titleCode: null
  gender: null
  address: null
  cityCode: null
  city: null
  districtCode: null
  stateCode: null
  postcode: null
  housePhone: null
  mobilePhone: null
  email: string
  status: number
  userGroup: number
  passwordChange: null
  passwordRenew: boolean
  renewDate: null
  count: number
}

export interface UserState<DataType extends UserResponseBodyGetOnlyData = UserResponseBodyGetOnlyData> {
  data: DataType | null
  token: string | null
  portal: null | number
  permission: boolean | null,
  aliranTugasAccess: boolean | null,
  subStatusCode:string | null,
  roles: string[]
}

const initialState: UserState = {
  data: null,
  token: null,
  portal: null,
  permission: null,
  aliranTugasAccess: null,
  subStatusCode:null,
  roles: []
}

const user = 'user'

export const setUserDataRedux = createAction<UserState['data']>(`${user}/setUserDataRedux`)

export const setUserTokenRedux = createAction<UserState['token']>(`${user}/setUserTokenRedux`)

export const setUserPortalRedux = createAction<UserState['portal']>(`${user}/setUserPortalRedux`)

export const setUserPermissionRedux = createAction<UserState['permission']>(`${user}/setUserPermissionRedux`)

export const setAliranTugasAccessRedux = createAction<UserState['permission']>(`${user}/setAliranTugasAccessRedux`)

export const setSubStatusCode = createAction<UserState['subStatusCode']>(`${user}/setSubStatusCodeRedux`)

export const setUserRoles = createAction<UserState['roles'] | null>(`${user}/setUserRoles`)

export const logoutRedux = createAction(`${user}/logout`)

export const userSlice = createSlice({
  name: user,
  initialState,
  reducers: {},
  selectors: {
    getAuthorizationHeader: (state) => {
      if (state?.portal && state?.token) {
        return {
          portal: `${state.portal}`,
          'Authorization': `Bearer ${state?.token ?? ''}`
        }
      }
      return null
    },
    getUserToken: (state) => state.token,
    getUserPortal: (state) => state.portal,
    getUserDetails: (state) => state.data,
    getUserPermission: (state) => state.permission ?? false,
    getAliranTugasAccess: (state) => state.aliranTugasAccess ?? false,
    getSubStatusCode: (state) => state.subStatusCode ?? false,
    getUserId: (state) => state.data?.id ?? null,
    getUserRoles: (state) => state.roles,
    checkIsLoggedInUser: (state) => typeof state.portal === "number",
  },
  extraReducers: (builder) =>
    builder
      .addCase(setUserDataRedux, (state, action) => {
        state.data = action.payload
      })
      .addCase(setUserTokenRedux, (state, action) => {
        state.token = action.payload
      })
      .addCase(setUserPortalRedux, (state, action) => {
        state.portal = action.payload
      })
      .addCase(setUserPermissionRedux, (state, action) => {
        state.permission = action.payload
      })
      .addCase(setAliranTugasAccessRedux, (state, action) => {
        state.aliranTugasAccess = action.payload
      })
      .addCase(setUserRoles, (state, action) => {
        state.roles = action.payload ?? []
      })
      .addCase(logoutRedux, (state) => {
        state.data = null
        state.token = null
        state.portal = null
        state.permission = null
        state.aliranTugasAccess = null
        state.roles = []
      })
})

export const {
  getAuthorizationHeader,
  getUserToken,
  getUserDetails,
  getUserPortal,
  getUserPermission,
  getAliranTugasAccess,
  getUserId,
  getUserRoles,
  checkIsLoggedInUser
} = userSlice.selectors

const userReducer = userSlice.reducer

export default userReducer
