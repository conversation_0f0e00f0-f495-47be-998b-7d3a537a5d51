import Box from "@mui/material/Box";
import React from "react";
import { Stack, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import ALiranTugas from "../AliranTugas";
import { ButtonPrimary } from "../../../components/button";
import { useNavigate } from "react-router-dom";
import { ListSenaraiPindaan } from "../perlembagaan/senarai-pindaan";
import JawatankuasaProvider from "../ajk/jawatankuasa/jawatankuasaProvider";
import { useSelector } from "react-redux";
import { getUserPermission } from "@/redux/userReducer";

export interface Organization {
  id: string | number;
  name: string;
  code: string;
}

export const MigrasiPerlembagaan: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const isManager = useSelector(getUserPermission);

  return (
    <>
      <Stack
        // sx={{ px: 6, py: 3 }}
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
        gap={2}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("perlembagaanPertubuhan")}
          </Typography>

          <Typography
            variant="subtitle1"
            sx={{
              color: "#FF0000",
              marginBottom: "16px",
              borderRadius: "16px",
              fontSize: "14px",
              fontWeight: "500 !important",
            }}
          >
            Tiada maklumat perlembagaan terkini. Sila lengkapkan perlembagaan di
            bahagian Migrasi Perlembagaan.
          </Typography>
        </Box>

        {/* <UpdateMaklumatPerlembagaanSection /> */}
      </Stack>

      <Stack
        // sx={{ px: 6, py: 3 }}
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
        gap={2}
      >
        <ListSenaraiPindaan />
        {/* <UpdatePindaanPerlembagaanSection /> */}
      </Stack>
      {isManager ? (
        <>
          <Stack
            // sx={{ px: 6, py: 3 }}
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
            gap={2}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("migrasiPerlembagaan")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{ color: "#666666", fontSize: 14 }}
                >
                  {t("permohonanMigrasiPerlembagaan")}
                </Typography>

                <ButtonPrimary onClick={() => navigate("pindaan-perlembagaan")}>
                  {t("add")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Stack>
          <Stack
            // sx={{ px: 6, py: 3 }}
            sx={{
              backgroundColor: "white",
              borderRadius: "14px",
            }}
            gap={2}
          >
            <JawatankuasaProvider>
              <ALiranTugas />
            </JawatankuasaProvider>
          </Stack>{" "}
        </>
      ) : (
        <></>
      )}
    </>
  );
};

export default MigrasiPerlembagaan;
