import PembayaranLayout from "@/pages/pembayaran/PembayaranLayout";
import PembayaranKaunter from "@/pages/pembayaran/perkhidmatan-kaunter/pembayaran-kaunter";
import { Route, Outlet, Navigate } from "react-router-dom";
import { RouteGuard } from "../../components/RouteGuard";
import { registerRoutes } from "../../helpers/routeDetector";
import Kemaskini from "../../pages/pembayaran/perkhidmatan-kaunter/pembayaran-kaunter/Kemaskini";
import RekodPembayaran from "../../pages/pembayaran/perkhidmatan-kaunter/rekod-pembayaran";
import PaymentKeputusan from "../../pages/pembayaran/keputusan";

// Layout component to wrap all pembayaran routes with protection
const PembayaranGuardedLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <PembayaranLayout>
      <Outlet />
    </PembayaranLayout>
  </RouteGuard>
);

// Register routes with their portal types
registerRoutes({
  // Add your route registrations here
});

export const pembayaran = {
  routes: (
    <>
      <Route element={<PembayaranGuardedLayout />}>
        <Route path="pembayaran">
          <Route index element={<Navigate to="perkhidmatan" replace />} />
          <Route path="perkhidmatan">
            <Route index element={<Navigate to="pembayaran-kaunter" replace />} />
            <Route path="pembayaran-kaunter" element={<PembayaranKaunter />} />
            <Route path="pembayaran-kaunter/kemaskini" element={<Kemaskini />} />

            <Route path="rekod-pembayaran" element={<RekodPembayaran />} />
          </Route>
        </Route>
      </Route>
      {/* Payment result page - outside of PembayaranLayout to avoid authentication issues */}
      <Route path="pembayaran/keputusan" element={<PaymentKeputusan />} />
    </>
  ),
};
