import PembayaranLayout from "@/pages/pembayaran/PembayaranLayout";
import PembayaranKaunter from "@/pages/pembayaran/perkhidmatan-kaunter/pembayaran-kaunter";
import { Route, Outlet, Navigate } from "react-router-dom";
import { RouteGuard } from "../../components/RouteGuard";
import { registerRoutes } from "../../helpers/routeDetector";
import Kemaskini from "../../pages/pembayaran/perkhidmatan-kaunter/pembayaran-kaunter/Kemaskini";
import RekodPembayaran from "../../pages/pembayaran/perkhidmatan-kaunter/rekod-pembayaran";
import PaymentKeputusan from "../../pages/pembayaran/keputusan";
import AuthHelper from "@/helpers/authHelper";
import { NEW_PermissionNames, pageAccessEnum } from "@/helpers";
import { TabGuard } from "@/layouts/tabGuard";

// Layout component to wrap all pembayaran routes with protection
const PembayaranGuardedLayout = () => {
  if (!AuthHelper.hasAuthority([NEW_PermissionNames.PEMBAYARAN.label])) {
    return <Navigate to="/forbidden" replace />;
  }
  return (
    <RouteGuard
      autoUpdatePortal={true}
      showDebugInfo={process.env.NODE_ENV === "development"}
    >
      <PembayaranLayout>
        <Outlet />
      </PembayaranLayout>
    </RouteGuard>
  );
};

// Register routes with their portal types
registerRoutes({
  "/pembayaran/perkhidmatan/pembayaran-kaunter": "internal",
  "/pembayaran/perkhidmatan/pembayaran-kaunter/kemaskini": "internal",
  "/pembayaran/perkhidmatan/rekod-pembayaran": "internal",
  // Add your route registrations here
});

export const pembayaran = {
  routes: (
    <>
      <Route element={<PembayaranGuardedLayout />}>
        <Route path="pembayaran">
          <Route index element={<Navigate to="perkhidmatan" replace />} />
          <Route path="perkhidmatan">
            <Route
              index
              element={<Navigate to="pembayaran-kaunter" replace />}
            />
            <Route
              path="pembayaran-kaunter"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PEMBAYARAN.children.PEMBAYARAN_KAUNTER
                      .label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <PembayaranKaunter />
                </TabGuard>
              }
            />
            <Route
              path="pembayaran-kaunter/kemaskini"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PEMBAYARAN.children.PEMBAYARAN_KAUNTER
                      .label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <Kemaskini />
                </TabGuard>
              }
            />

            <Route
              path="rekod-pembayaran"
              element={
                <TabGuard
                  permissionName={
                    NEW_PermissionNames.PEMBAYARAN.children.REKOD_PEMBAYARAN
                      .label
                  }
                  withAccess
                  permissionType={pageAccessEnum.Read}
                >
                  <RekodPembayaran />
                </TabGuard>
              }
            />
          </Route>
        </Route>
      </Route>
      {/* Payment result page - outside of PembayaranLayout to avoid authentication issues */}
      <Route path="pembayaran/keputusan" element={<PaymentKeputusan />} />
    </>
  ),
};
