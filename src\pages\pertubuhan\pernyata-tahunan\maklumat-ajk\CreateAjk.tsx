/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { Box, Typography, Grid } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { ButtonOutline } from "../../../../components/button";
import { useCustom } from "@refinedev/core";
import { API_URL } from "../../../../api";
import Input from "../../../../components/input/Input";
import { Controller, useForm } from "react-hook-form";
import useMutation from "../../../../helpers/hooks/useMutation";
import { useDispatch } from "react-redux";
import { removeAjk } from "../../../../redux/ajkReducer";
import {
  CitizenshipStatus,
  IdTypes,
  ListGelaran,
  MALAYSIA,
  OrganisationPositionLabel,
  OrganisationPositions,
} from "@/helpers/enums";
import { capitalizeWords, getLocalStorage } from "@/helpers";
import { DisabledTextField, FormFieldRow, Label } from "@/components";

interface ICommitte {
  jobCode: string;
  societyId: string | number;
  societyNo: string;
  titleCode: string;
  name: string;
  gender: string;
  nationalityStatus: string;
  identificationType: string;
  identificationNo: string;
  dateOfBirth: string; // Format: YYYY-MM-DD
  placeOfBirth: string;
  designationCode: keyof typeof OrganisationPositionLabel;
  otherDesignationCode: string;
  employerAddressStatus: string;
  employerName: string;
  employerAddress: string;
  employerPostcode: string;
  employerCountryCode: string;
  employerStateCode: string;
  employerCity: string;
  employerDistrict: string;
  residentialAddress: string;
  residentialPostcode: string;
  residentialAddressStatus: string;
  residentialCountryCode: string;
  residentialStateCode: string;
  residentialDistrictCode: string;
  residentialCity: string;
  email: string;
  telephoneNumber: string;
  phoneNumber: string;
  noTelP: string;
  status: number;
  applicationStatusCode: string;
  pegHarta: string;
  tarikhTukarSu: string; // Format: YYYY-MM-DD
  otherPosition: string;
  batalFlat: boolean;
  blacklistNotice: boolean;
  benarAjk: boolean;
  id: string | number;
}

export const CreateAjk: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const params = new URLSearchParams(window.location.search);

  const occupationList = getLocalStorage("occupation_list", []);
  const { id } = useParams();

  const value = params.get("value")
    ? JSON.parse(params.get("value") || "")
    : {};

  const { data: addressList, isLoading: isAddressLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData = addressList?.data?.data || [];

  const location = useLocation();
  const ajk: ICommitte = location.state?.ajk;

  console.log(ajk);

  const mutate = useMutation({
    url: "society/committee/create",
    onSuccess: () => {
      if (value?.no) {
        dispatch(removeAjk(value?.no));
      }
    },
  });
  const dispatch = useDispatch();

  const form = useForm<ICommitte>();

  const {
    register,
    formState: { errors },
    handleSubmit,
    control,
    watch,
    reset,
  } = form;

  return (
    <Box sx={{ display: "flex", gap: 3 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 2 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#fff",
          borderRadius: "14px",
          width: "100%",
        }}
      >
        <form
          style={{
            // border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
            width: "100%",
          }}
        >
          <Box
            sx={{
              mb: 3,
              p: 3,
              borderRadius: "16px",
              border: "1px solid #D9D9D9",
            }}
          >
            <Typography
              variant="h6"
              component="h2"
              sx={{
                fontWeight: "bold",
                fontSize: 14,
                color: "var(--primary-color)",
                pl: 2,
              }}
            >
              {t("positionInfo")}
            </Typography>

            <Box sx={{ pl: 2 }}>
              <Grid container>
                {ajk?.otherDesignationCode || ajk?.otherPosition ? (
                  <FormFieldRow
                    label={<Label text={t("position")} />}
                    value={
                      <DisabledTextField
                        value={
                          ajk?.otherDesignationCode &&
                          ajk?.otherDesignationCode !== "-"
                            ? ajk?.otherDesignationCode
                            : ajk?.otherPosition
                        }
                      />
                    }
                  />
                ) : (
                  <Controller
                    control={control}
                    name="designationCode"
                    rules={{
                      required: "Medan ini diperlukan",
                    }}
                    render={({ field }) => {
                      return (
                        <Input
                          required
                          {...field}
                          disabled
                          label={t("position")}
                          value={t(
                            `${
                              OrganisationPositions.find(
                                (item) =>
                                  item?.value === Number(ajk?.designationCode)
                              )?.label || ""
                            }`
                          )}
                        />
                      );
                    }}
                  />
                )}
              </Grid>
            </Box>
          </Box>

          <Box
            sx={{
              p: 3,
              borderRadius: "16px",
              border: "1px solid #D9D9D9",
            }}
          >
            <Box
              sx={{
                px: 2,
                py: 1,
                mb: 3,
                borderRadius: "16px",
              }}
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  fontWeight: "bold",
                  fontSize: 14,
                  color: "var(--primary-color)",
                }}
              >
                {t("personalInfo")}
              </Typography>
            </Box>

            <Box sx={{ pl: 2 }}>
              <Controller
                control={control}
                name="identificationType"
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("idType")}
                      // value={ajk.identificationType}
                      value={t(
                        `${
                          IdTypes.find(
                            (item) => item.value === ajk?.identificationType
                          )?.label || ""
                        }`
                      )}
                      disabled
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="identificationNo"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      disabled
                      label={t("idNumber")}
                      value={ajk.identificationNo}
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="titleCode"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      required
                      label={t("title")}
                      type="select"
                      options={ListGelaran}
                      value={ajk.titleCode}
                      disabled
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="name"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("fullName")}
                      value={ajk.name}
                      disabled
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="gender"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      type="select"
                      options={[
                        {
                          label: t("male"),
                          value: "L",
                        },
                        {
                          label: t("female"),
                          value: "P",
                        },
                      ]}
                      label={t("gender")}
                      value={ajk.gender}
                      disabled
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="nationalityStatus"
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      type="select"
                      options={CitizenshipStatus.map((item) => ({
                        ...item,
                        label: t(item.label),
                      }))}
                      label={t("citizenship")}
                      value={Number(ajk?.nationalityStatus)}
                      disabled
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="dateOfBirth"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      disabled
                      required
                      {...field}
                      type="date"
                      label={t("dateOfBirth")}
                      value={ajk.dateOfBirth}
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="placeOfBirth"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      disabled
                      label={t("placeOfBirth")}
                      value={ajk.placeOfBirth}
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="jobCode"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("occupation")}
                      type="select"
                      disabled
                      options={occupationList}
                      value={ajk.jobCode}
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="residentialAddress"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("residentialAddress")}
                      disabled
                      value={ajk.residentialAddress}
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="residentialStateCode"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("state")}
                      type="select"
                      fullWidth
                      disabled
                      value={ajk.residentialStateCode}
                      options={addressData
                        ?.filter((item: any) => item.pid == MALAYSIA)
                        .map((item: any) => ({
                          label: item.name,
                          value: "" + item.id,
                        }))}
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="residentialDistrictCode"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("district")}
                      type="select"
                      fullWidth
                      options={addressData
                        ?.filter(
                          (item: any) => item.pid == ajk.residentialStateCode
                        )
                        .map((item: any) => ({
                          label: item.name,
                          value: "" + item.id,
                        }))}
                      disabled
                      value={ajk.residentialDistrictCode}
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="residentialCity"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("city")}
                      disabled
                      value={ajk.residentialCity}
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="residentialPostcode"
                rules={{
                  required: t("idNumberRequired"),
                  maxLength: {
                    value: 5,
                    message: t("postcodeHelper"),
                  },
                  pattern: {
                    value: /^\d+$/,
                    message: t("numbersOnly"),
                  },
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      onChange={(e) => {
                        if (/^\d{0,5}$/.test(e.target.value)) {
                          field?.onChange(e.target.value);
                        }
                      }}
                      label={t("postcode")}
                      disabled
                      value={ajk.residentialPostcode}
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="email"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      required
                      {...field}
                      label={t("email")}
                      type="email"
                      disabled
                      value={ajk.email}
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="phoneNumber"
                rules={{
                  required: "Medan ini diperlukan",
                }}
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      required
                      label={t("phoneNumber")}
                      type="number"
                      disabled
                      value={ajk.phoneNumber}
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="telephoneNumber"
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      label={t("homeNumber")}
                      type="number"
                      disabled
                      value={ajk.telephoneNumber}
                    />
                  );
                }}
              />

              <Controller
                control={control}
                name="noTelP"
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      label={t("officeNumber")}
                      type="number"
                      disabled
                      value={ajk.phoneNumber}
                    />
                  );
                }}
              />
            </Box>
          </Box>

          {ajk.jobCode === "" || ajk.jobCode === "" ? null : (
            <>
              {" "}
              <Box
                sx={{
                  mt: 3,
                  p: 3,
                  borderRadius: "16px",
                  border: "1px solid #D9D9D9",
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    // justifyContent: "space-between",
                    // backgroundColor: "#e0f2f1",
                    px: 2,
                    py: 1,
                    mb: 3,
                    borderRadius: "16px",
                  }}
                >
                  <Typography
                    variant="h6"
                    component="h2"
                    sx={{
                      fontWeight: "bold",
                      fontSize: 14,
                      color: "var(--primary-color)",
                    }}
                  >
                    {t("employerInfo")}
                  </Typography>
                </Box>

                <Box sx={{ pl: 2 }}>
                  <Controller
                    control={control}
                    name="employerName"
                    render={({ field }) => {
                      return (
                        <Input
                          required
                          {...field}
                          label={t("employerName")}
                          disabled
                          value={ajk.employerName}
                        />
                      );
                    }}
                  />

                  {ajk.employerAddressStatus !== "1" ? (
                    <Controller
                      control={control}
                      name="employerCountryCode"
                      render={({ field }) => {
                        return (
                          <Input
                            required
                            {...field}
                            label={t("country")}
                            type={"select"}
                            options={addressData
                              .filter((item: any) => item.pid === 0)
                              .map((item: any) => ({
                                label:
                                  item.name.charAt(0) +
                                  item.name.slice(1).toLowerCase(),
                                value: item.id,
                              }))}
                            disabled
                            value={ajk.employerCountryCode}
                          />
                        );
                      }}
                    />
                  ) : null}

                  <Controller
                    control={control}
                    name="employerAddress"
                    render={({ field }) => {
                      return (
                        <Input
                          {...field}
                          label={t("employerAddress")}
                          multiline
                          rows={4}
                          disabled
                          value={ajk.employerAddress}
                        />
                      );
                    }}
                  />

                  {ajk.employerAddressStatus === "1" && (
                    <>
                      <Controller
                        control={control}
                        name="employerStateCode"
                        render={({ field }) => {
                          return (
                            <Input
                              required
                              {...field}
                              label={t("state")}
                              type="select"
                              fullWidth
                              multiline
                              rows={3}
                              options={addressData
                                ?.filter((item: any) => item.pid == MALAYSIA)
                                .map((item: any) => ({
                                  label: capitalizeWords(item.name, null, true),
                                  value: "" + item.id,
                                }))}
                              disabled
                              value={ajk.employerStateCode}
                            />
                          );
                        }}
                      />

                      <Controller
                        control={control}
                        name="employerDistrict"
                        render={({ field }) => {
                          return (
                            <Input
                              required
                              {...field}
                              label={t("district")}
                              type="select"
                              fullWidth
                              multiline
                              rows={3}
                              options={addressData
                                ?.filter(
                                  (item: any) =>
                                    item.pid == ajk.employerStateCode
                                )
                                .map((item: any) => ({
                                  label: capitalizeWords(item.name, null, true),
                                  value: "" + item.id,
                                }))}
                              disabled
                              value={ajk.employerDistrict}
                            />
                          );
                        }}
                      />

                      <Controller
                        control={control}
                        name="employerCity"
                        render={({ field }) => {
                          return (
                            <Input
                              required
                              {...field}
                              label={t("city")}
                              disabled
                              value={ajk.employerCity}
                            />
                          );
                        }}
                      />

                      <Controller
                        control={control}
                        name="employerPostcode"
                        rules={{
                          required: t("idNumberRequired"),
                          maxLength: {
                            value: 5,
                            message: t("postcodeHelper"),
                          },
                          pattern: {
                            value: /^\d+$/,
                            message: t("numbersOnly"),
                          },
                        }}
                        render={({ field }) => {
                          return (
                            <Input
                              required
                              {...field}
                              onChange={(e) => {
                                if (/^\d{0,5}$/.test(e.target.value)) {
                                  field?.onChange(e.target.value);
                                }
                              }}
                              label={t("postcode")}
                              disabled
                              value={ajk.employerPostcode}
                            />
                          );
                        }}
                      />
                    </>
                  )}
                </Box>
              </Box>
            </>
          )}

          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              mt: 2,
              gap: 2,
            }}
          >
            <ButtonOutline onClick={() => navigate(-1)}>
              {t("back")}
            </ButtonOutline>
          </Box>
        </form>
      </Box>
    </Box>
  );
};

export default CreateAjk;
