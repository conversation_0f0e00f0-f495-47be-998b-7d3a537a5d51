import { useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { handleNext } from "@/redux/secretaryBranchReformReducer";
import { useSecretaryBranchReformContext } from "../Provider";
import {
  autoDOBSetByIC,
  autoGenderSetByIC,
  getLocalStorage,
} from "@/helpers/utils";
import {
  MALAYSIA,
  ListGelaran,
  AddressOptions,
  IdTypes,
  CitizenshipStatus,
  ListGender,
} from "@/helpers/enums";

import { Box, Grid, Typography } from "@mui/material";
import { PageButton, NavButton } from "@/components/button/ButtonPagination";
import FormFieldRow from "@/components/form-field-row";
import Label from "@/components/label/Label";
import TextFieldController from "@/components/input/TextFieldController";
import SelectFieldController from "@/components/input/select/SelectFieldController";
import DatePickerController from "@/components/input/DatePickerController";
import DisabledTextField from "@/components/input/DisabledTextField";
import dayjs from "dayjs";
import { useICValidation } from "@/helpers/hooks/useICValidation.ts";

const SecretaryForm: React.FC = () => {
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const {
    control,
    watch,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useFormContext();
  const { isViewOnly } = useSecretaryBranchReformContext();

  const occupationList = getLocalStorage("occupation_list", []);
  const currentLanguage = i18n.language;
  const isMalaysiaLanguage = currentLanguage === "my";

  const addressList = getLocalStorage("address_list", []);
  const employerAddressOptions = AddressOptions(t);

  const jobCode = watch("jobCode");
  const committeeEmployerAddressStatus = watch(
    "committeeEmployerAddressStatus"
  );

  const disabledState = isViewOnly;

  const disabledEmployer = ["Pesara", "Tidak Bekerja"].includes(jobCode);

  const stateOptions = useMemo(
    () =>
      addressList
        .filter((item: any) => item.pid === MALAYSIA)
        .map((item: any) => ({
          value: String(item.id),
          label: item.name,
        })),
    [addressList]
  );
  const residenceDistrictOptions = useMemo(
    () =>
      addressList
        .filter(
          (item: any) =>
            item.pid === Number(watch("committeeResidenceStateCode"))
        )
        .map((item: any) => ({
          value: String(item.id),
          label: item.name,
        })),
    [addressList, watch("residenceStateCode")]
  );

  const employerDistrictOptions = useMemo(
    () =>
      addressList
        .filter(
          (item: any) =>
            item.pid === Number(watch("committeeEmployerStateCode"))
        )
        .map((item: any) => ({
          value: String(item.id),
          label: item.name,
        })),
    [addressList, watch("employerStateCode")]
  );

  const allCountryOptions = useMemo(
    () =>
      addressList
        ?.filter((i: any) => i.level === 0)
        ?.map((item: any) => ({
          value: item.id,
          label: item.name,
        }))
        ?.filter((i: any) => i.value !== MALAYSIA),
    [addressList]
  );

  const onSubmit = () => dispatch(handleNext());

  useEffect(() => {
    if (!watch("committeeIcNo") || watch("committeeIcNo").length !== 12) {
      setValue("gender", null);
    } else {
      if (parseInt(watch("committeeIcNo")?.slice(-1)) % 2 === 0) {
        setValue("gender", "P");
      } else {
        setValue("gender", "L");
      }
    }
    // Remove auto-population of placeOfBirth - let user input manually
  }, [watch("committeeIcNo")]);

  const [JPNError, setJPNError] = useState(false);
  const {
    userICCorrect,
    userNameMatchIC,
    triggerICValidation,
    setUserICCorrect,
    setUserNameMatchIC,
    resetICValidation,
    integrationStatus,
  } = useICValidation({
    idType: watch("idType"),
    idNumber: watch("committeeIcNo"),
    fullName: watch("committeeName"),
  });

  useEffect(() => {
    const isMyKad =
      Number(watch("idType")) === 1 || Number(watch("idType")) === 4;
    const nameReady = watch("committeeName")?.trim() !== "";
    const idReady = watch("committeeIcNo")?.length === 12;
    if (integrationStatus === 0 && isMyKad) {
      setJPNError(true);
    } else {
      setJPNError(false);
    }
    if (integrationStatus === 1 && isMyKad && nameReady && idReady) {
      triggerICValidation();
    }
  }, [
    watch("idType"),
    watch("committeeIcNo"),
    watch("committeeName"),
    integrationStatus,
  ]);

  useEffect(() => {
    const type = Number(watch("idType"));
    if (type === 1 || type === 4) {
      setValue(
        "gender",
        autoGenderSetByIC(type, undefined, watch("committeeIcNo")?.toString())
      );

      setValue(
        "dateOfBirth",
        dayjs(
          autoDOBSetByIC(type, undefined, watch("committeeIcNo")?.toString())
        ).format("YYYY-MM-DD")
      );
    }
  }, [watch("committeeIcNo")]);

  useEffect(() => {
    setValue("idType", "1");
    setValue("citizenship", 1);
  }, []);

  let committeeIcNoHelperText: string | undefined = undefined;
  if (watch("idType") === "1" || watch("idType") === "4") {
    if (typeof errors.committeeIcNo?.message === "string") {
      committeeIcNoHelperText = errors.committeeIcNo.message;
    } else if (
      watch("committeeIcNo")?.length === 12 &&
      watch("committeeName")?.trim() !== undefined &&
      !userICCorrect
    ) {
      committeeIcNoHelperText = t("IcDoesNotExist");
    } else if (watch("committeeIcNo")?.length < 12) {
      committeeIcNoHelperText = t("idNumberOnlyDigits");
    }
  } else if (typeof errors.committeeIcNo?.message === "string") {
    committeeIcNoHelperText = errors.committeeIcNo.message;
  }

  let committeeNameHelperText: string | undefined = undefined;

  const idType = watch("idType");
  const committeeIcNo = watch("committeeIcNo") ?? "";
  const committeeName = watch("committeeName") ?? "";

  if (idType === "1" || idType === "4") {
    if (typeof errors.committeeName?.message === "string") {
      committeeNameHelperText = errors.committeeName.message;
    } else if (
      committeeIcNo.length === 12 &&
      committeeName.trim() !== "" &&
      !userNameMatchIC
    ) {
      committeeNameHelperText = t("invalidName");
    }
  } else {
    if (typeof errors.committeeName?.message === "string") {
      committeeNameHelperText = errors.committeeName.message;
    }
  }

  useEffect(() => {
    setValue("idType", "1");
    setValue("citizenship", 1);
  }, []);
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "8px",
          p: { xs: 2, sm: 3 },
          backgroundColor: "transparent",
          marginBottom: 3,
        }}
      >
        <Typography
          sx={{
            color: "var(--primary-color)",
            fontSize: "14px",
            fontWeight: "500 !important",
            mb: 2,
          }}
        >
          {t("oldBranchSecretary")}
        </Typography>

        <Grid container spacing={2} pl={2} pt={2}>
          <FormFieldRow
            label={<Label text={t("name")} />}
            value={
              <TextFieldController
                control={control}
                name="oldSecretaryName"
                disabled
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("idNumber")} />}
            value={
              <TextFieldController
                control={control}
                name="oldSecretaryIdentificationNumber"
                disabled
              />
            }
          />
        </Grid>
      </Box>

      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "8px",
          p: { xs: 2, sm: 3 },
          backgroundColor: "transparent",
          marginBottom: 3,
        }}
      >
        <Typography
          sx={{
            color: "var(--primary-color)",
            fontSize: "14px",
            fontWeight: "500 !important",
            mb: 2,
          }}
        >
          {t("newBranchSecretary")}
        </Typography>

        <Grid container spacing={2} pl={2} pt={2}>
          <FormFieldRow
            label={<Label text={t("position")} />}
            value={<DisabledTextField value={t("secretary")} />}
          />
        </Grid>
      </Box>

      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "8px",
          p: { xs: 2, sm: 3 },
          backgroundColor: "transparent",
          marginBottom: 3,
        }}
      >
        <Typography
          sx={{
            color: "var(--primary-color)",
            fontSize: "14px",
            fontWeight: "500 !important",
            mb: 2,
          }}
        >
          {isMalaysiaLanguage
            ? "Maklumat Peribadi Setiausaha baru"
            : "Personal Information of the new Secretary"}
        </Typography>

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Grid container spacing={2} pl={2} pt={2}>
            <FormFieldRow
              label={<Label text={t("idType")} />}
              value={
                <SelectFieldController
                  control={control}
                  value={"1"}
                  name="idType"
                  options={IdTypes.map((item) => ({
                    ...item,
                    label: t(item.label),
                  }))}
                  disabled
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("idNumber")} />}
              value={
                <TextFieldController
                  control={control}
                  name="committeeIcNo"
                  error={!!committeeIcNoHelperText}
                  helperText={committeeIcNoHelperText as string}
                  helperTextComponentPlacement={
                    committeeIcNoHelperText ? "INSIDE" : undefined
                  }
                  onChange={(e) => {
                    const inputType = watch("idType");
                    let value = e.target.value;

                    if (inputType === "1" || inputType === "4") {
                      value = value.replace(/\D/g, "").slice(0, 12);
                    }

                    setValue("committeeIcNo", value);
                  }}
                  required
                  disabled={disabledState}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("gelaran")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="titleCode"
                  options={ListGelaran}
                  disabled={disabledState}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("fullName")} required />}
              value={
                <TextFieldController
                  control={control}
                  name="committeeName"
                  helperTextComponentPlacement={
                    committeeNameHelperText ? "INSIDE" : undefined
                  }
                  error={!!committeeNameHelperText}
                  helperText={committeeNameHelperText}
                  disabled={disabledState}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("gender")} />}
              value={
                <SelectFieldController
                  control={control}
                  name="gender"
                  options={ListGender.map((gender) => ({
                    ...gender,
                    label: t(gender.label),
                  }))}
                  disabled
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("citizenship")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="citizenship"
                  options={CitizenshipStatus.map((item) => ({
                    ...item,
                    label: t(item.label),
                  }))}
                  disabled
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("dateOfBirth")} required />}
              value={
                <DatePickerController
                  control={control}
                  name="dateOfBirth"
                  // disabled
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("placeOfBirth")} />}
              value={
                <TextFieldController
                  control={control}
                  name="placeOfBirth"
                  disabled={disabledState}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("pekerjaan")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="jobCode"
                  options={occupationList}
                  disabled={disabledState}
                  required
                />
              }
            />

            <FormFieldRow
              align="flex-start"
              label={<Label text={t("residentialAddress")} required />}
              value={
                <TextFieldController
                  control={control}
                  name="committeeResidenceAddress"
                  disabled={disabledState}
                  multiline
                  rows={3}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("state")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="committeeResidenceStateCode"
                  options={stateOptions}
                  disabled={disabledState}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("district")} required />}
              value={
                <SelectFieldController
                  control={control}
                  name="committeeResidenceDistrictCode"
                  options={residenceDistrictOptions}
                  disabled={
                    !watch("committeeResidenceStateCode") || disabledState
                  }
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("city")} />}
              value={
                <TextFieldController
                  control={control}
                  name="committeeResidenceCity"
                  disabled={disabledState}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("postcode")} required />}
              value={
                <TextFieldController
                  control={control}
                  name="committeeResidencePostcode"
                  disabled={disabledState}
                  type="number"
                  rules={{
                    validate: (value: string) => {
                      if (!/^\d{5}$/.test(value)) {
                        return t("postcodeValidation");
                      }
                      return true;
                    },
                  }}
                  isPostcode
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("email")} required />}
              value={
                <TextFieldController
                  control={control}
                  name="email"
                  type="email"
                  disabled={disabledState}
                  required
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("mobilePhoneNumber")} required />}
              value={
                <Box sx={{ display: "flex", gap: 2 }}>
                  <TextFieldController
                    control={control}
                    name="hpNoCode"
                    type="telCode"
                    sx={{ width: "80px" }}
                    inputProps={{ maxLength: 3 }}
                    sxControl={{ width: "80px" }}
                    disabled={disabledState}
                    required
                  />
                  <TextFieldController
                    control={control}
                    disabled={disabledState}
                    name="hpNo"
                    type="tel"
                    required
                  />
                </Box>
              }
            />

            <FormFieldRow
              label={<Label text={t("officePhoneNumber")} />}
              value={
                <Box sx={{ display: "flex", gap: 2 }}>
                  <TextFieldController
                    control={control}
                    name="workTelNoCode"
                    type="telCode"
                    inputProps={{ maxLength: 3 }}
                    sx={{ width: "80px" }}
                    sxControl={{ width: "80px" }}
                    disabled={disabledState}
                  />
                  <TextFieldController
                    control={control}
                    disabled={disabledState}
                    name="workTelNo"
                    type="tel"
                  />
                </Box>
              }
            />

            <FormFieldRow
              label={<Label text={t("homePhoneNumber")} />}
              value={
                <Box sx={{ display: "flex", gap: 2 }}>
                  <TextFieldController
                    control={control}
                    name="homeTelNoCode"
                    type="telCode"
                    sx={{ width: "80px" }}
                    inputProps={{ maxLength: 3 }}
                    sxControl={{ width: "80px" }}
                    disabled={disabledState}
                  />
                  <TextFieldController
                    control={control}
                    disabled={disabledState}
                    name="homeTelNo"
                    type="tel"
                  />
                </Box>
              }
            />
          </Grid>
        </Box>
      </Box>

      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "8px",
          p: { xs: 2, sm: 3 },
          backgroundColor: "transparent",
          marginBottom: 3,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Typography
            sx={{
              color: "#FF0000",
              fontSize: "12px",
              fontWeight: "500 !important",
            }}
          >
            {t("peringatan")} :
          </Typography>

          <Typography
            sx={{
              color: "#666666",
              fontSize: "12px",
              fontWeight: "400 !important",
            }}
          >
            {isMalaysiaLanguage
              ? "Bagi ahli jawatankuasa yang TIDAK BEKERJA/ PESARA, maklumat majikan tidak perlu diisi"
              : "For committee members who are UNWORKED/RETIRED, employer information does not need to be filled in"}
          </Typography>
        </Box>
      </Box>

      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "8px",
          p: { xs: 2, sm: 3 },
          backgroundColor: "transparent",
          marginBottom: 3,
        }}
      >
        <Typography
          sx={{
            color: "var(--primary-color)",
            fontSize: "14px",
            fontWeight: "500 !important",
            mb: 2,
          }}
        >
          {t("employerInfo")}
        </Typography>

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Grid container spacing={2} pl={2} pt={2}>
            <FormFieldRow
              label={<Label text={t("employerName")} />}
              value={
                <TextFieldController
                  control={control}
                  name="committeeEmployerName"
                  disabled={disabledState || disabledEmployer}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("employerAddress")} />}
              value={
                <SelectFieldController
                  control={control}
                  name="committeeEmployerAddressStatus"
                  options={employerAddressOptions}
                  disabled={disabledState || disabledEmployer}
                />
              }
            />

            {committeeEmployerAddressStatus === "0" && (
              <>
                <FormFieldRow
                  label={<Label text={t("country")} />}
                  value={
                    <SelectFieldController
                      control={control}
                      name="committeeEmployerCountryCode"
                      options={allCountryOptions}
                      disabled={disabledState || disabledEmployer}
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={""} />}
                  value={
                    <TextFieldController
                      control={control}
                      name="committeeEmployerAddress"
                      disabled={disabledState || disabledEmployer}
                      multiline
                      rows={3}
                    />
                  }
                />
              </>
            )}

            {committeeEmployerAddressStatus === "1" && (
              <>
                <FormFieldRow
                  label={<Label text={""} />}
                  value={
                    <TextFieldController
                      control={control}
                      name="committeeEmployerAddress"
                      disabled={disabledState || disabledEmployer}
                      multiline
                      rows={3}
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={t("state")} />}
                  value={
                    <SelectFieldController
                      control={control}
                      name="committeeEmployerStateCode"
                      options={stateOptions}
                      disabled={disabledState || disabledEmployer}
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={t("district")} />}
                  value={
                    <SelectFieldController
                      control={control}
                      name="committeeEmployerDistrictCode"
                      options={employerDistrictOptions}
                      disabled={
                        !watch("committeeEmployerStateCode") ||
                        disabledState ||
                        disabledEmployer
                      }
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={t("city")} />}
                  value={
                    <TextFieldController
                      control={control}
                      name="committeeEmployerCity"
                      disabled={disabledState || disabledEmployer}
                    />
                  }
                />

                <FormFieldRow
                  label={<Label text={t("postcode")} />}
                  value={
                    <TextFieldController
                      isPostcode
                      control={control}
                      name="committeeEmployerPostcode"
                      disabled={disabledState || disabledEmployer}
                    />
                  }
                />
              </>
            )}
          </Grid>
        </Box>
      </Box>

      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          alignItems: "center",
          gap: 1,
          mt: 3,
          mb: 3,
        }}
      >
        <PageButton active>1</PageButton>
        <PageButton type="submit">2</PageButton>

        <NavButton type="submit"> {t("next")}</NavButton>
      </Box>
    </form>
  );
};

export default SecretaryForm;
