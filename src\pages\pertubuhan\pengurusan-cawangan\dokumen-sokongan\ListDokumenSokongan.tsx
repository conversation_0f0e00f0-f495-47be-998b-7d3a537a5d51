import React from "react";
import { Box } from "@mui/material";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import ButtonPrimary from "../../../../components/button/ButtonPrimary";
import { useTranslation } from "react-i18next";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import FileUploader from "@/components/input/fileUpload";
import { DocumentUploadType } from "@/helpers";

export const ListDokumenSokongan: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const branchId = searchParams.get("id");
  const { id } = useParams<{ id: string }>();

  const { t } = useTranslation();

  const { mutate: updateBranch, isLoading: isUpdating } = useCustomMutation();

  const handleHantarClick = () => {
    handleHantar();
  };

  const handleHantar = async () => {
    try {
      // await updateBranch({
      //   url: `${API_URL}/society/branch/update`,
      //   method: "put",
      //   values: {
      //     id: branchId,
      //     applicationStatusCode: 2,
      //   },
      //   config: {
      //     headers: {
      //       portal: localStorage.getItem("portal"),
      //       authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      //     },
      //   },
      //   successNotification: (data) => {
      //     if (id) {
      //       navigate(`/pertubuhan/society/${id}/senarai/cawangan`);
      //     }
      //     return {
      //       message: data?.data?.msg || "Documents updated successfully",
      //       type: "success",
      //     };
      //   },
      //   errorNotification: (error) => ({
      //     message: error?.response?.data?.msg || "Error updating documents",
      //     type: "error",
      //   }),
      // });

      await updateBranch({
        url: `${API_URL}/society/branch/${branchId}/checkAndUpdate`,
        method: "put",
        values: {
          id: branchId,
          applicationStatusCode: 2,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.code === 200) {
            if (id) {
              navigate(`/pertubuhan/society/${id}/senarai/cawangan`);
            }
            return {
              message: data?.data?.msg || "Branch updated successfully",
              type: "success",
            };
          }
          return {
            message: data?.data?.msg,
            type: "error",
          };
        },
        errorNotification: (error) => ({
          message: error?.response?.data?.msg || t("error"),
          type: "error",
        }),
      });
    } catch (error) {
      console.error("Error updating Branch:", error);
    }
  };

  // const [uploadedIds, setUploadedIds] = useState<string[]>([]);
  // const [oldFiles, setOldFiles] = useState<string[]>([]);

  // const handleUploadComplete = (id: string) => {
  //   setUploadedIds((prev) => (prev.includes(id) ? prev : [...prev, id]));
  // };

  // const handleOldUploadedFiles = (files: any) => {
  //   setOldFiles(files);
  // };

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "#FCFCFC",
          borderRadius: "14px",
        }}
      >
        {id && (
          <FileUploader
            title="addSupportingDocument"
            type={DocumentUploadType.SUPPORTING_DOCUMENT}
            societyId={id}
            branchId={branchId ? branchId : null}
            // meetingId={null}
            validTypes={[
              "text/plain",
              "application/rtf",
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
              "application/msword",
              "application/vnd.oasis.opendocument.text",
              "application/pdf",
            ]}
            // onUploadComplete={handleUploadComplete}
            // hasOldFiles={handleOldUploadedFiles}
          />
        )}

        <ButtonPrimary
          sx={{ display: "block", ml: "auto", mt: 4 }}
          // disabled={uploadedIds.length === 0 && oldFiles.length === 0}
          onClick={handleHantarClick}
        >
          {t("hantar")}
        </ButtonPrimary>
      </Box>
    </>
  );
};

export default ListDokumenSokongan;
