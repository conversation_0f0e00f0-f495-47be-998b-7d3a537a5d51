import { ButtonPrimary } from "@/components";
import { SearchIcon, TrashIcon } from "@/components/icons";
import { removeFieldFromSection, setFieldField } from "@/redux/geranReducer";
import { ArrowDropDownCircleSharp } from "@mui/icons-material";
import DoNotDisturbOnIcon from "@mui/icons-material/DoNotDisturbOn";
import {
  Box,
  Grid,
  IconButton,
  InputAdornment,
  Switch,
  TextField,
  useTheme,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { Controller, FieldValues, useForm } from "react-hook-form";

type CardFormProps = {
  order: number;
  sectionId: string;
  fieldId: string;
};

const DropDownCardForm: React.FC<CardFormProps> = ({
  order,
  fieldId,
  sectionId,
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const field = useSelector((state: RootState) =>
    state.geran.sections
      .find((s: any) => s.id === sectionId)
      ?.fields.find((f: any) => f.id === fieldId)
  );

  if (!field) return null;

  const {
    fieldName,
    description,
    sectionName,
    isRequired,
    options = [],
  } = field;

  const handleInputChange = (index: number, value: string) => {
    const updated = [...options];
    updated[index] = value;
    dispatch(
      setFieldField({
        sectionId,
        fieldId,
        options: updated,
      })
    );
  };

  const handleAddInput = () => {
    dispatch(
      setFieldField({
        sectionId,
        fieldId,
        options: [...options, ""],
      })
    );
  };

  const { control } = useForm<FieldValues>({
    defaultValues: {
      fields: {},
    },
  });

  const handleRemoveInput = (index: number) => {
    const updated = [...options];
    updated.splice(index, 1);
    dispatch(
      setFieldField({
        sectionId,
        fieldId,
        options: updated,
      })
    );
  };

  return (
    <Box
      mt={2}
      sx={{
        borderRadius: 3,
        border: `0.5px solid ${theme.palette.primary.main}`,
        padding: "22px 34px",
      }}
    >
      <Grid container spacing={2} mb={2}>
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "stretch",
              borderRadius: "12px",
              backgroundColor: "#fff",
            }}
          >
            {/* LEFT SIDE */}
            <Box flex={1} display="flex" flexDirection="column" gap={2}>
              {order === 1 && (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      value={sectionName}
                      onChange={(e) =>
                        dispatch(
                          setFieldField({
                            sectionId,
                            fieldId,
                            sectionName: e.target.value,
                          })
                        )
                      }
                      placeholder="Tulis Tajuk Disini"
                      variant="outlined"
                      size="small"
                      fullWidth
                    />
                  </Grid>
                </Grid>
              )}
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <TextField
                    value={fieldName ?? ""}
                    onChange={(e) =>
                      dispatch(
                        setFieldField({
                          sectionId,
                          fieldId,
                          fieldName: e.target.value,
                        })
                      )
                    }
                    placeholder="Tulis Disini"
                    variant="outlined"
                    size="small"
                    fullWidth
                  />
                </Grid>

                <Grid item xs={12} sm={8}>
                  <Box display="flex" flexDirection="column" gap={1}>
                    <TextField
                      fullWidth
                      size="small"
                      disabled
                      placeholder="PlaceHolder"
                      InputProps={{
                        sx: {
                          borderRadius: "4px",
                        },
                        endAdornment: (
                          <InputAdornment position="end">
                            <ArrowDropDownCircleSharp color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />

                    <Controller
                      control={control}
                      name={`fields.${fieldId}.options`}
                      defaultValue={
                        field.options.length ? field.options : ["", ""]
                      }
                      render={({ field: { value, onChange } }) => (
                        <Box display="flex" flexDirection="column" gap={1}>
                          {value.map((option: string, index: number) => (
                            <Box
                              key={`${field.id}-${index}`}
                              display="flex"
                              alignItems="center"
                              gap={1}
                            >
                              <TextField
                                fullWidth
                                size="small"
                                value={option ?? ""}
                                placeholder={`Opsi ${index + 1}`}
                                onChange={(e) => {
                                  const newOptions = [...value];
                                  newOptions[index] = e.target.value;
                                  onChange(newOptions);
                                  dispatch(
                                    setFieldField({
                                      sectionId,
                                      fieldId,
                                      options: newOptions,
                                    })
                                  );
                                }}
                              />
                              {value.length > 2 && (
                                <IconButton
                                  color="error"
                                  onClick={() => {
                                    const newOptions = value.filter(
                                      (_: any, i: any) => i !== index
                                    );
                                    onChange(newOptions);
                                    dispatch(
                                      setFieldField({
                                        sectionId,
                                        fieldId,
                                        options: newOptions,
                                      })
                                    ); 
                                  }}
                                >
                                  <DoNotDisturbOnIcon fontSize="small" />
                                </IconButton>
                              )}
                            </Box>
                          ))}
                          <Box display="flex" justifyContent="flex-end">
                            <ButtonPrimary
                              type="button"
                              onClick={() => {
                                const newOptions = [...value, ""];
                                onChange(newOptions);
                                dispatch(
                                  setFieldField({
                                    sectionId,
                                    fieldId,
                                    options: newOptions,
                                  })
                                );
                              }}
                              sx={{
                                mt: 1,
                                fontSize: "10px",
                                backgroundColor: "#00b4b4",
                                "&:hover": {
                                  backgroundColor: "#00a0a0",
                                },
                                minWidth: "100px",
                              }}
                            >
                              Tambah
                            </ButtonPrimary>
                          </Box>
                        </Box>
                      )}
                    />
                  </Box>
                </Grid>
              </Grid>
            </Box>

            {/* RIGHT SIDE */}
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                ml: 2,
              }}
            >
              <Box
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "12px",
                  padding: "8px 16px",
                  marginBottom: "12px",
                  backgroundColor: "#fff",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Switch
                  checked={isRequired || false}
                  onChange={(e) =>
                    dispatch(
                      setFieldField({
                        sectionId,
                        fieldId,
                        isRequired: e.target.checked,
                      })
                    )
                  }
                />
              </Box>

              <Box
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "12px",
                  padding: "8px 16px",
                  backgroundColor: "#fff",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <IconButton
                  sx={{
                    color: "#FF0000",
                    p: 1,
                  }}
                  onClick={() =>
                    dispatch(removeFieldFromSection({ sectionId, fieldId }))
                  }
                >
                  <TrashIcon sx={{ width: 20, height: 20 }} />
                </IconButton>
              </Box>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DropDownCardForm;
