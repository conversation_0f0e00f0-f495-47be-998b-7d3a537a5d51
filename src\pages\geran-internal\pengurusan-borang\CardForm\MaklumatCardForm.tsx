import { TrashIcon } from "@/components/icons";
import { removeFieldFromSection, setFieldField } from "@/redux/geranReducer";
import { RootState } from "@/redux/store";
import {
  Box,
  Grid,
  IconButton,
  Switch,
  TextField,
  useTheme,
} from "@mui/material";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
type CardFormProps = {
  order: number;
  sectionId: string;
  fieldId: string;
};
const MaklumatCardForm: React.FC<CardFormProps> = ({
  order,
  fieldId,
  sectionId,
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { control, reset } = useForm<FieldValues>({
    defaultValues: {
      sectionName: "",
      fieldName: "",
      isRequired: false,
    },
  });

  const primary = theme.palette.primary.main;
  const field = useSelector((state: RootState) =>
    state.geran.sections
      .find((s: any) => s.id === sectionId)
      ?.fields.find((f: any) => f.id === fieldId)
  );
  const { fieldName, isRequired, options, sectionName } = field;

  const handleOptionChange = (index: number, value: string) => {
    const updated = [...options];
    updated[index] = value;
    dispatch(
      setFieldField({
        sectionId,
        fieldId,
        options: updated,
      })
    );
  };
  return (
    <Box
      mt={2}
      sx={{
        borderRadius: 3,
        border: `0.5px solid ${primary}`,
        padding: "22px 34px",
      }}
    >
      <Grid container spacing={2} mb={2}>
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "stretch",
              borderRadius: "12px",

              backgroundColor: "#fff",
            }}
          >
            {/* LEFT SIDE */}
            <Box
              display="flex"
              flexDirection="column"
              gap={2}
              flex={1}
              justifyContent="center"
            >
              {order === 1 && (
                <Grid container justifyContent="space-between">
                  <Grid item xs={12} sm={4}>
                    <Controller
                      control={control}
                      name="sectionName"
                      render={({ field }) => (
                        <TextField
                          {...field}
                          placeholder="Tulis Tajuk Disini"
                          variant="outlined"
                          size="small"
                          fullWidth
                          onChange={(e) =>
                            dispatch(
                              setFieldField({
                                sectionId,
                                fieldId,
                                sectionName: e.target.value,
                              })
                            )
                          }
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                borderColor: "primary.main",
                              },
                              "&:hover fieldset": {
                                borderColor: "primary.dark",
                              },
                              "&.Mui-focused fieldset": {
                                borderColor: "primary.main",
                              },
                            },
                            borderRadius: "3px",
                          }}
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              )}
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Controller
                    control={control}
                    name="fieldName"
                    render={({ field }) => (
                      <TextField
                        {...field}
                        placeholder="Tulis Disini"
                        variant="outlined"
                        size="small"
                        value={fieldName || ""}
                        fullWidth
                        onChange={(e) =>
                          dispatch(
                            setFieldField({
                              sectionId,
                              fieldId,
                              fieldName: e.target.value,
                            })
                          )
                        }
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            "& fieldset": {
                              borderColor: "primary.main",
                            },
                            "&:hover fieldset": {
                              borderColor: "primary.dark",
                            },
                            "&.Mui-focused fieldset": {
                              borderColor: "primary.main",
                            },
                          },
                          borderRadius: "3px",
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={7}>
                  <Controller
                    control={control}
                    name="description"
                    render={({ field }) => (
                      <TextField
                        {...field}
                        placeholder="Tulis Disini"
                        variant="outlined"
                        size="medium"
                        fullWidth
                        multiline
                        disabled
                        onChange={(e) => handleOptionChange(0, e.target.value)}
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            "& fieldset": {
                              borderColor: "primary.main",
                            },
                            "&:hover fieldset": {
                              borderColor: "primary.dark",
                            },
                            "&.Mui-focused fieldset": {
                              borderColor: "primary.main",
                            },
                          },
                          borderRadius: "3px",
                        }}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Box>

            {/* RIGHT SIDE */}
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                ml: 2,
              }}
            >
              {/* SWITCH */}
              <Box
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "12px",
                  padding: "8px 16px",
                  marginBottom: "12px",
                  backgroundColor: "#fff",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Controller
                  control={control}
                  name="isRequired"
                  render={({ field }) => (
                    <Switch
                      {...field}
                      checked={isRequired || false}
                      onChange={(e) =>
                        dispatch(
                          setFieldField({
                            sectionId,
                            fieldId,
                            isRequired: e.target.checked,
                          })
                        )
                      }
                    />
                  )}
                />
              </Box>

              {/* DELETE BUTTON */}
              <Box
                sx={{
                  border: "1px solid #E0E0E0",
                  borderRadius: "12px",
                  padding: "8px 16px",
                  backgroundColor: "#fff",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <IconButton
                  sx={{
                    color: "#FF0000",
                    p: 1,
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    dispatch(
                      removeFieldFromSection({
                        sectionId: sectionId,
                        fieldId: fieldId,
                      })
                    );
                  }}
                >
                  <TrashIcon sx={{ width: 20, height: 20 }} />
                </IconButton>
              </Box>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MaklumatCardForm;
