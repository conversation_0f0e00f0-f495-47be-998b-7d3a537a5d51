import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
// import { handleSaveValue } from "../../../helper/handleSaveValue";
import CustomPopover from "../../../../components/popover";
import { useSelector } from "react-redux";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import AsalContent from "@/components/asalFasalContent";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import Input from "@/components/input/Input";
interface FasalContentTigaCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: [];
  name: string;
}

export const FasalContentTigaCawangan: React.FC<
  FasalContentTigaCawanganProps
> = ({ activeStep, setActiveStep, clause, asalData, name }) => {
  const [asal, setAsal] = useState<any>(null);
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [matlamatPertubuhan, setMatlamatPertubuhan] = useState("");
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [dataId, setDataId] = useState<number | null>(null);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { id, clauseId } = useParams();

  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName, address, mailingAddress } =
          responseData?.data?.data;
        setNamaPertubuhan(societyName);
      },
    },
  });

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause3Data = JSON.parse(clause3);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        setClauseContent(clause.clauseContent);
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause3Data.societyName);
      setMatlamatPertubuhan(clause.constitutionValues[0]?.definitionName ?? "");
      setIsEdit(clause.edit);
      setAsal(
        asalData.find((item: any) => item.clauseNo === clause.clauseNo
        ) || ""
      );
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!matlamatPertubuhan) {
      errors.matlamatPertubuhan = t("fieldRequired");
    }

    return errors;
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  return (
    <>
      <AsalContent
        asalData={asal?.description || ""}
        clauseId={clauseId}
        name={name}
      />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clause")} {clauseId}
        </Typography>

        <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
          <Typography sx={labelStyle}>{t("organizationGoals")}</Typography>
          <CustomPopover
            content={
              <>
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  sx={{ mb: 1, color: "#666666" }}
                >
                  Format
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ lineHeight: 1.5, color: "#666666" }}
                >
                  1. &lt;matlamat 1&gt;
                  <br />
                  2. &lt;matlamat 2&gt;
                </Typography>
              </>
            }
          />
        </Box>
        {/* <TextField
          fullWidth
          disabled={isViewMode}
          value={matlamatPertubuhan}
          variant="outlined"
          multiline
          minRows={3}
          onChange={(e) => {
            setMatlamatPertubuhan(e.target.value);
            setFormErrors((prevErrors) => ({
              ...prevErrors,
              matlamatPertubuhan: "",
            }));
          }}
          error={!!formErrors.matlamatPertubuhan}
          helperText={formErrors.matlamatPertubuhan}
          sx={{
            "& fieldset": { borderRadius: "12px" },
            "& .MuiInputBase-input": { color: "black" },
          }}
        /> */}
        <Input
          isLabel={false}
          disabled={isViewMode}
          value={matlamatPertubuhan}
          isLabelNoSpace={false}
          fullFeatureRichText={true}
          name="decisionNotes"
          type="richText"
          rows={4}
          error={!!formErrors.matlamatPertubuhan}
          helperText={formErrors.matlamatPertubuhan}
          onChange={(e) => {
            setMatlamatPertubuhan(e.target.value);
            setFormErrors((prevErrors) => ({
              ...prevErrors,
              matlamatPertubuhan: "",
            }));
          }}
        />
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={matlamatPertubuhan} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              const errors = validateForm();
              if (Object.keys(errors).length > 0) {
                setFormErrors(errors);
                return;
              }
              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: matlamatPertubuhan,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: matlamatPertubuhan,
                    titleName: "Matlamat Pertubuhan",
                  },
                ],
                clause: "clause3",
                clauseCount: 3,
                // clauseContentId
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentTigaCawangan;
