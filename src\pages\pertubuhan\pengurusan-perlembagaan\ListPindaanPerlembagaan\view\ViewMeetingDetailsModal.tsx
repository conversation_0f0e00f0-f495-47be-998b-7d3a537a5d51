import { useTranslation } from "react-i18next";
import { ChangeEvent, useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  Box,
  Grid,
  Typography,
  useTheme,
  useMediaQuery,
  Theme,
  Button,
  IconButton,
} from "@mui/material";
import { useSelector } from "react-redux";
import {
  ConstitutionType,
  HideOrDisplayInherit,
  MeetingContent,
  MeetingMethods,
  MeetingTypeOption,
} from "@/helpers/enums";
import { getLocalStorage } from "@/helpers/utils";
import ConfirmationDialog from "@/components/dialog/confirm";
import useQuery from "@/helpers/hooks/useQuery";
import {
  ButtonPrimary,
  ButtonText,
  DisabledTextField,
  SelectFieldController,
} from "@/components";
import { <PERSON><PERSON>ontaine<PERSON>, <PERSON><PERSON>, TileLayer } from "react-leaflet";
import { FormMeetingDateTime } from "@/components/form/meeting/DateTime";
import dayjs from "dayjs";
import { FormMeetingAttendees } from "@/components/form/meeting/Attendees";
import { IMeetingOptions } from "@/types";
import { useForm<PERSON>ontext } from "react-hook-form";
import { DocumentUploadType } from "@/helpers";
import { EyeIcon } from "@/components/icons";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

export interface PindaanPerlembagaan {
  id: number;
  fasal: number;
  namaFasal: string;
  applicationStatusCode: 1 | 2;
}

export const ViewMeetingDetailsModal = ({
  onClose,
  data,
}: {
  onClose: () => void;
  data: any;
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const { control } = useFormContext();
  //GET ID PARAMS
  const { id } = useParams();
  const meetingOptions: IMeetingOptions[] = getLocalStorage("meeting_list", []);
  const [fileName, setFileName] = useState<string>("");
  const [fileUrl, setFileUrl] = useState<string>("");
  // const [fileInfo, setFileInfo] = useState(null);

  const { refetch: fetchDocuments, isLoading: fetchDocumentsIsLoading } =
    useQuery({
      url: `document/documentByParam`,
      filters: [
        { field: "societyId", operator: "eq", value: id },
        { field: "meetingId", operator: "eq", value: data?.id },
      ],
      onSuccess: (data) => {
        const fileInfo = data?.data?.data?.[0];
        if (fileInfo) {
          setFileName(fileInfo?.name);
          setFileUrl(fileInfo?.url);
        }
      },
    });

  const downloadFile = (filePath: string) => {
    window.open(filePath, "_blank");
  };

  const getMeetingLabel = (value: number): string => {
    const meeting = MeetingTypeOption.find(
      (option) => Number(option.value) === Number(value)
    );
    return meeting ? meeting.label : "Unknown Meeting Type";
  };

  const getMeetingName = (meetingId: string | number): string => {
    const meeting = meetingOptions.find((m) => m.id === meetingId);
    if (meeting) {
      return i18n.language === "my" ? meeting.nameBm : meeting.nameEn;
    }
    return "-";
  };

  const addressList = getLocalStorage("address_list", null);

  const getStateName = (stateCode: any) => {
    const stateName = addressList.filter(
      (i: any) => Number(i.id) === Number(stateCode)
    );
    return stateName[0]?.name;
  };

  const getDistrict = (val = null) => {
    const address = addressList
      .filter((items: any) => Number(items.id) === Number(val))
      .map((item: any) => ({ value: item.id, label: item.name }));
    return address?.[0]?.label || "-";
  };

  const platformType =
    data?.platformType?.length === 1
      ? null
      : MeetingTypeOption?.find((item) =>
          data?.platformType
            ? parseInt(data?.platformType) === item.value
            : false
        ) ?? null;

  const meetingPlatformOptions = useMemo(
    () =>
      MeetingTypeOption?.filter((item: any) => item.pid === 3).map(
        (item: any) => ({
          value: String(item.id),
          label: item.nameBm,
        })
      ),
    [MeetingTypeOption]
  );
  //console.log("data",data)
  const { data: existingDocuments, isLoading: isLoadingDocument } = useQuery({
    url: "document/documentByParam",
    filters: [
      {
        field: "societyId",
        operator: "eq",
        value: id,
      },
      {
        field: "type",
        operator: "eq",
        value: DocumentUploadType.MEETING,
      },
      {
        field: "meetingId",
        operator: "eq",
        value: data.meetingId,
      },
    ],
    //autoFetch: false,
  });

  const papar = () => {
    console.log("existingDocuments", existingDocuments);
    const url = existingDocuments?.data?.data[0]?.url ?? "";
    console.log("url", url);
    if (url) window.open(url, "_blank", "noreferrer,noopener");
  };
  // ===========================
  return (
    <>
      <Box sx={{ display: "flex", gap: 1 }}>
        <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px" }}>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("maklumatMesyuarat")}
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("meetingType")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  styleProfileId={2}
                  value={
                    data?.meetingType ? getMeetingLabel(data?.meetingType) : "-"
                  }
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("meetingMethod")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  styleProfileId={2}
                  value={
                    data?.meetingMethod
                      ? getMeetingName(parseInt(data?.meetingMethod))
                      : "-"
                  }
                />
              </Grid>

              {data?.meetingMethod &&
                [MeetingMethods.ATAS_TALIAN, MeetingMethods.HYBRID].includes(
                  data?.meetingMethod
                ) &&
                platformType && (
                  <Grid item sm={12}>
                    <SelectFieldController
                      name="platformType"
                      control={control}
                      options={meetingPlatformOptions}
                      placeholder={t("selectPlaceholder")}
                    />
                  </Grid>
                )}

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("tujuanMesyuarat")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  styleProfileId={2}
                  value={data?.meetingPurpose}
                />
              </Grid>
            </Grid>
          </Box>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("masaDanTarikhMesyuarat")}
            </Typography>
            <FormMeetingDateTime
              viewOnly
              meetingTimeFromAttribute="meetingTime"
              defaultValues={{
                meetingDate: data?.meetingDate
                  ? dayjs(data?.meetingDate)
                  : null,
                meetingTime: data?.meetingDate
                  ? dayjs(
                      `${data?.meetingDate} ${data?.meetingTime ?? "00:00:00"}`,
                      "YYYY-MM-DD HH:mm:[00]"
                    )
                  : null,
                meetingTimeTo: data?.meetingDate
                  ? dayjs(
                      `${data?.meetingDate} ${
                        data?.meetingTimeTo ?? "00:00:00"
                      }`,
                      "YYYY-MM-DD HH:mm:[00]"
                    )
                  : null,
              }}
            />
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("namaTempatMesyuarat")}{" "}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  styleProfileId={2}
                  value={data?.meetingPlace ?? "-"}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("locationMap")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <Box>
                  <MapContainer
                    /**
                     * @todo change meetingLocation with data from backend
                     */
                    center={[2.745564, 101.707021]}
                    zoom={13}
                    style={{
                      height: "10rem",
                      width: "100%",
                      borderRadius: "0.5rem",
                    }}
                  >
                    <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                    {/**
                     * @todo change meetingLocation with data from backend
                     */}
                    <Marker position={[2.745564, 101.707021]} />
                  </MapContainer>
                </Box>
              </Grid>
            </Grid>
          </Box>

          {data?.meetingMethod === MeetingMethods.ATAS_TALIAN ? null : (
            <>
              <Box
                sx={{
                  p: { xs: 1, sm: 2, md: 3 },
                  border: "1px solid #D9D9D9",
                  borderRadius: "14px",
                  mb: 2,
                }}
              >
                <Typography variant="subtitle1" sx={sectionStyle}>
                  {t("alamatTempatMesyuarat")}
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("meetingPlaceAddress")}{" "}
                      <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <DisabledTextField
                      styleProfileId={2}
                      value={data?.meetingPlace ?? "-"}
                    />
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("negeri")} <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <DisabledTextField
                      styleProfileId={2}
                      value={data?.state ? getStateName(data?.state) : "-"}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("bandar")} <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <DisabledTextField
                      styleProfileId={2}
                      value={data?.city ?? "-"}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("daerah")} <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <DisabledTextField
                      styleProfileId={2}
                      value={data?.district ? getDistrict(data?.district) : "-"}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("poskod")} <span style={{ color: "red" }}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <DisabledTextField
                      styleProfileId={2}
                      value={data?.postcode ?? "-"}
                    />
                  </Grid>
                </Grid>
              </Box>
            </>
          )}

          <Grid container spacing={2}>
            <Grid item sm={12}>
              <FormMeetingAttendees
                defaultValues={{
                  totalAttendees: data?.totalAttendees ?? 7,
                }}
                viewOnly
              />
            </Grid>
          </Grid>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("minitMesyuarat")}
            </Typography>
            <Box
              sx={{ display: "flex", justifyContent: "flex-end", px: 1, mt: 5 }}
            >
              <ButtonPrimary disabled={isLoadingDocument} onClick={papar}>
                {t("papar")}
              </ButtonPrimary>
            </Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("minitMesyuarat")}</Typography>
              </Grid>

              <Grid item xs={12} sm={8}>
                <Box
                  sx={{
                    border: "1px solid #DADADA",
                    borderRadius: "8px",
                    p: 3,
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: 1,
                    backgroundColor: "#FFFFFF",
                    "&:hover": {
                      backgroundColor: "#F9FAFB",
                    },
                  }}
                >
                  <Box
                    sx={{
                      width: "100%",
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      gap: 1,
                    }}
                  >
                    <Box
                      sx={{
                        width: "100%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <Box>
                        <Typography
                          sx={{
                            color: "#222222",
                            fontSize: "14px",
                            textAlign: "center",
                          }}
                        >
                          {fileName ? fileName : "-"}
                        </Typography>
                      </Box>
                      <IconButton
                        onClick={() => downloadFile(fileUrl)}
                        sx={{
                          padding: 0,
                        }}
                      >
                        <EyeIcon color="#666666" />
                      </IconButton>
                    </Box>
                  </Box>
                </Box>
              </Grid>

              {data?.meetingContent == MeetingContent.YA && (
                <>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("ucapanAluanPengerusi")}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <DisabledTextField
                      size="small"
                      multiline
                      row={4}
                      value={data?.openingRemarks ?? "-"}
                    />
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("perkaraPerkara")}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <DisabledTextField
                      size="small"
                      multiline
                      row={4}
                      value={data?.mattersDiscussed ?? "-"}
                    />
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>{t("penutup")}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <DisabledTextField
                      size="small"
                      value={data?.closing ?? "-"}
                      multiline
                      row={4}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("disediakanOleh")}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <DisabledTextField
                      size="small"
                      value={data?.providedBy ?? "-"}
                    />
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>{t("disahkanOleh")}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <DisabledTextField
                      size="small"
                      value={data?.confirmBy ?? "-"}
                    />
                  </Grid>
                </>
              )}
              {data?.meetingContent === MeetingContent.TIDAK &&
                uploadedFiles && (
                  <Box>
                    {uploadedFiles?.map((file, index) => (
                      <Box
                        key={file.id}
                        sx={{
                          border: "1px solid #E0E0E0",
                          borderRadius: "8px",
                          backgroundColor: "#fff",
                          p: 2,
                          mb: 1,
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                          }}
                        >
                          <Typography
                            sx={{
                              cursor: "pointer",
                              "&:hover": {
                                color: "var(--primary-color)",
                                textDecoration: "underline",
                              },
                            }}
                            // onClick={() => handlePreviewDocument(file.url)}
                          >
                            {file.name}
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 1,
                            }}
                          >
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M23 12L20.56 9.21L20.9 5.52L17.29 4.7L15.4 1.5L12 2.96L8.6 1.5L6.71 4.69L3.1 5.5L3.44 9.2L1 12L3.44 14.79L3.1 18.49L6.71 19.31L8.6 22.5L12 21.03L15.4 22.49L17.29 19.3L20.9 18.48L20.56 14.79L23 12ZM9.38 16.01L7 13.61C6.9073 13.5175 6.83375 13.4076 6.78357 13.2866C6.73339 13.1657 6.70756 13.036 6.70756 12.905C6.70756 12.774 6.73339 12.6443 6.78357 12.5234C6.83375 12.4024 6.9073 12.2925 7 12.2L7.07 12.13C7.46 11.74 8.1 11.74 8.49 12.13L10.1 13.75L15.25 8.59C15.64 8.2 16.28 8.2 16.67 8.59L16.74 8.66C17.13 9.05 17.13 9.68 16.74 10.07L10.82 16.01C10.41 16.4 9.78 16.4 9.38 16.01Z"
                                fill="var(--primary-color)"
                              />
                            </svg>
                          </Box>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                )}
            </Grid>
          </Box>

          <Grid container spacing={2}>
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <Button
                size="medium"
                variant="contained"
                onClick={onClose}
                sx={{ minWidth: "12rem", textTransform: "capitalize" }}
              >
                {t("back")}
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </>
  );
};

export default ViewMeetingDetailsModal;
