import { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { useTheme, useMediaQuery, Fade, Grid } from "@mui/material";
import { useCustom } from "@refinedev/core";
import Input from "../../../../components/input/Input";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { API_URL } from "../../../../api";
import { LoadingOverlay } from "../../../../components/loading";
import { parseDateToISO8601 } from "@/helpers";
import { useSelector } from "react-redux";
import { selectCalculatedPayment } from "@/redux/paymentReducer";

// Update the interface to match the API response
interface SocietyData {
  id: string | number;
  societyName: string;
  applicationNo: string;
  registeredDate: number[] | null;
}

export const Kaunter = () => {
  const { t } = useTranslation();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogAlertSuccessSaveOpen, setDialogAlertSuccessSaveOpen] =
    useState(false);
  const { id } = useParams();
  const societyId = id;
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const branchId = searchParams.get("id");
  const [societyName, setSocietyName] = useState("");

  // Get payment data from Redux
  const paymentRedux = useSelector(selectCalculatedPayment);

  // Update state type
  const [societyData, setSocietyData] = useState<SocietyData | null>(null);
  const [isLoadingData, setIsLoadingData] = useState(false);

  const [branchData, setBranchData] = useState<any>({});
  const { data: branchList, isLoading: isLoadingBranch } = useCustom<any>({
    url: `${API_URL}/society/branch/getById/${branchId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: branchId !== null,
      retry: false,
      cacheTime: 0,
    },
  });

  useEffect(() => {
    if (branchList?.data?.data) {
      setBranchData(branchList?.data?.data);
    }
  }, [branchList]);

  useEffect(() => {
    const fetchSocietyData = async () => {
      try {
        setIsLoadingData(true);

        if (!societyId) {
          console.error("Invalid society ID:", societyId);
          return;
        }

        console.log("Fetching data for society ID:", societyId);

        const response = await fetch(`${API_URL}/society/${societyId}/basic`, {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        });

        const result = await response.json();

        if (result.status === "SUCCESS" && result.data) {
          // Ensure we're accessing the correct properties
          const data = result.data;

          setSocietyName(data?.societyName);
          setSocietyData(data);

          // Add more specific error logging
          if (!data.societyName)
            console.warn("Society name is missing in response");
          if (!data.applicationNo)
            console.warn("Application number is missing in response");
        } else {
          console.error("API Error:", result);
        }
      } catch (error) {
        console.error("Error fetching society data:", error);
      } finally {
        setIsLoadingData(false);
      }
    };

    if (societyId) {
      fetchSocietyData();
    }
  }, [societyId]);

  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const downloadFile = (filePath: string) => {
    fetch(filePath)
      .then((res) => res.blob())
      .then((blob) => {
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = "Pendaftaran Cawangan (Pembayaran KAUNTER).pdf";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      })
      .catch(console.error);
  };

  const handleCetak = async () => {
    try {
      if (societyId) {
        const response = await fetch(`${API_URL}/society/pdf/paymentReceipt`, {
          method: "post",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            paymentType: "Pendaftaran Cawangan (Pembayaran KAUNTER)",
            paymentMethod: "c",
            societyId: societyId,
            branchId: branchId,
            registerDateTime: parseDateToISO8601(branchData?.createdDate),
            amount: paymentRedux?.totalAmount,
            referenceNo: branchData?.branchApplicationNo,
          }),
        });

        const data = await response.json();
        if (data.status === "SUCCESS") {
          const url = data?.data?.url;
          downloadFile(url);
        }
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  return (
    <Box sx={{ display: "flex", gap: 2 }}>
      <Box sx={{ width: "55vw" }}>
        <LoadingOverlay isLoading={isLoadingData} />
        <Fade in={true} timeout={500}>
          <Box
            sx={{
              backgroundColor: "white",
              border: 1,
              borderColor: "grey.300",
              borderRadius: 4,
              p: 3,
            }}
          >
            <Box
              sx={{
                border: 1,
                borderColor: "grey.300",
                borderRadius: 1,
                p: 2,
                mb: 2,
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "#00A7A7",
                    fontSize: 16,
                    fontWeight: 600,
                    paddingLeft: 2,
                  }}
                >
                  {t("payment")} - {t("pengesahan")}
                </Typography>
              </Box>

              <Typography
                sx={{
                  color: "#666666",
                  fontSize: 14,
                  fontWeight: 400,
                  paddingLeft: 2,
                }}
              >
                {t("infoPaymentCawangan")}
              </Typography>

              <Grid container spacing={2} pl={4} pt={2} mb={3} mt={3}>
                <Input
                  disabled
                  label={t("organizationName")}
                  value={societyName}
                />
                <Input
                  disabled
                  label={t("branchName")}
                  value={branchData?.name}
                />
                <Input
                  disabled
                  label={t("referenceNumber")}
                  value={branchData?.branchApplicationNo || ""}
                />
                <Input
                  disabled
                  label={t("paymentMethod")}
                  value="Pembayaran Kaunter"
                />
                <Input
                  disabled
                  label={t("paymentAmount")}
                  value={`RM ${paymentRedux?.totalAmount?.toFixed(2)}`}
                />
              </Grid>

              <Typography
                variant="body2"
                sx={{
                  color: "#402DFF",
                  fontSize: 14,
                  paddingLeft: 2,
                }}
              >
                {t("noteKaunter")}
              </Typography>
            </Box>

            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
                marginTop: 3,
              }}
            >
              <ButtonOutline
                onClick={() =>
                  navigate(`/pertubuhan/society/${societyId}/senarai/cawangan`)
                }
              >
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                onClick={handleCetak}
                sx={{
                  color: "white",
                }}
              >
                {t("cetak")}
              </ButtonPrimary>
            </Box>
          </Box>
        </Fade>

        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
              backgroundColor: "#fff",
              color: "#000",
              minWidth: fullScreen ? "100%" : "1000px",
              maxWidth: "100%",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogTitle sx={{ pb: 2.5 }}>
            <Box
              sx={{
                backgroundColor: "#e0f2f1",
                px: 2.5,
                py: 0.5,
                borderRadius: 2.5,
              }}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                variant="h6"
                component="h2"
                sx={{ fontWeight: "bold", fontSize: 16, color: "black" }}
              >
                {t("submitApplication")}
              </Typography>
              <IconButton onClick={handleCloseDialog} size="small">
                <CloseIcon sx={{ color: "black" }} />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ py: 4 }}>
            <DialogContentText
              id="alert-dialog-description"
              sx={{ color: "black", fontSize: 14, ml: 2 }}
            >
              {t("confirmSubmitApplication")}
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ py: 2, px: 3 }}>
            <ButtonOutline onClick={handleCloseDialog} sx={{ fontSize: 14 }}>
              {t("no")}
            </ButtonOutline>
            <ButtonPrimary color="primary" autoFocus sx={{ fontSize: 14 }}>
              {t("yes")}
            </ButtonPrimary>
          </DialogActions>
        </Dialog>

        <Dialog
          open={dialogAlertSuccessSaveOpen}
          onClose={() => setDialogAlertSuccessSaveOpen(false)}
          fullScreen={fullScreen}
          PaperProps={{
            style: {
              borderRadius: "8px",
            },
          }}
          slotProps={{
            backdrop: {
              style: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                backdropFilter: "blur(4px)",
              },
            },
          }}
        >
          <DialogContent sx={{ p: 4 }}>
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <img src="/success.gif" alt="success" width={200} height={200} />
            </Box>
            <Typography
              variant="h6"
              component="h2"
              sx={{ fontSize: 28, textAlign: "center" }}
            >
              Permohonan berjaya dihantar.
            </Typography>

            <Box
              sx={{ display: "flex", justifyContent: "center", gap: 1, mt: 3 }}
            >
              <ButtonPrimary
                sx={{ backgroundColor: "#51CA47", borderRadius: "18px" }}
                onClick={() => setDialogAlertSuccessSaveOpen(false)}
              >
                {t("Continue")}
              </ButtonPrimary>
            </Box>
          </DialogContent>
        </Dialog>
      </Box>
    </Box>
  );
};

export default Kaunter;
