import { createSlice } from '@reduxjs/toolkit';

interface SocietyByIdStore {
  data: any | null;
  loading: boolean;
  error: string | null;
}

const initialState: SocietyByIdStore = {
  data: null,
  loading: false,
  error: null,
};

export const societyByIdDataSlice = createSlice({
  name: 'societyByIdData',
  initialState,
  reducers: {
    setSocietyByIdDataRedux(state, action) {
      state.data = action.payload;
    },
    setSocietyByIdLoading(state, action) {
      state.loading = action.payload;
    },
    setSocietyByIdError(state, action) {
      state.error = action.payload;
    },
  },
  selectors: {
    getSocietyByIdData: (state) => state?.data ?? null
  }
});

export const { setSocietyByIdDataRedux, setSocietyByIdLoading, setSocietyByIdError } = societyByIdDataSlice.actions;
export const { getSocietyByIdData } = societyByIdDataSlice.selectors
export default societyByIdDataSlice.reducer;
