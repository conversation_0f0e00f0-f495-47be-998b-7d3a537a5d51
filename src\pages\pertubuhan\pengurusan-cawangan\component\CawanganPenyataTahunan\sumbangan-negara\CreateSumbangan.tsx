import { <PERSON>, Grid, Stack, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Input from "@/components/input/Input";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { useSenaraiContext } from "@/pages/pertubuhan/SenaraiContext";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Controller,
  FieldValues,
  SubmitHandler,
  useForm,
} from "react-hook-form";
import { AddressList, Sumbangan } from "../interface";
import useQuery from "@/helpers/hooks/useQuery";
import { get } from "http";
import { useCustomMutation } from "@refinedev/core";
import { filterEmptyValuesOnObject, getLocalStorage } from "@/helpers/utils";
import { API_URL } from "@/api";
import { useSelector } from "react-redux";
import { FormFieldRow, Label, TextFieldController } from "@/components";

const CreateSumbangan = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const addressList = getLocalStorage("address_list", null);
  const location = useLocation();
  const societyId = location.state?.societyId;
  const statementId = location.state?.statementId;
  const year = location.state?.year;
  const contributionId = location.state?.contributionId;
  const contributionCode = location.state?.contributionCode;
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { refetch: getContribution } = useQuery({
    url: `society/statement/statement-contribution/${contributionId}`,
    autoFetch: false,
    onSuccess: (data) => {
      const contributionData: Sumbangan = data?.data?.data || [];
      setValue("contribution", contributionData?.contribution);
      setValue("countryOrigin", contributionData?.countryOrigin);
      setValue("value", contributionData?.value);
    },
  });

  // Trigger the API call when bankId changes or when it's available
  useEffect(() => {
    if (contributionId) {
      getContribution(); // Call getBank() when bankId is not null
    }
  }, []); // Dependency on bankId to trigger the effect when it changes

  const { control, getValues, setValue, handleSubmit } = useForm<FieldValues>({
    defaultValues: {
      contribution: "",
      countryOrigin: "Malaysia",
      value: "",
    },
  });

  const { mutate: saveContribution, isLoading: isLoading } =
    useCustomMutation();

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    const payload = filterEmptyValuesOnObject(data);

    saveContribution(
      {
        url: contributionId
          ? `${API_URL}/society/statement/statement-contribution/${contributionId}/edit`
          : `${API_URL}/society/statement/statement-contribution/create?branchId=${branchDataRedux.id}&societyId=${societyId}&statementId=${statementId}&contributionCode=${contributionCode}`,
        method: contributionId ? "put" : "post",
        values: payload,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          navigate(-1);
        },
      }
    );
  };

  const handleBackActions = () => {
    navigate(-1);
  };

  const handleSaveActions = () => {};
  return (
    <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
      }}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box
          sx={{
            background: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            py: 2,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {contributionCode == "1"
              ? t("contributionToInbroadInformation")
              : t("contributionToAbroadInformation")}
          </Typography>
          <Grid item sm={12}>
            <FormFieldRow
              label={
                <Label
                  text={
                    contributionCode == "1"
                      ? t("contributionProvider")
                      : t("penerimaSumbangan")
                  }
                />
              }
              value={
                <TextFieldController
                  control={control}
                  name="contribution"
                  required
                />
              }
            />

            <Controller
              name="countryOrigin"
              control={control}
              defaultValue={getValues("countryOrigin")} // Default value or empty string
              render={({ field }) => (
                <Input
                  label={
                    contributionCode == "1"
                      ? t("countryOfOriginRecipient")
                      : t("countryOfOriginRecipient2")
                  }
                  type="select"
                  required
                  value={getValues("countryOrigin")}
                  onChange={(e) => {
                    setValue("countryOrigin", e.target.value);
                  }}
                  options={addressList
                    .filter((item: any) => item.pid === 0)
                    .map((item: any) => ({
                      label: item.name,
                      value: item.id.toString(),
                    }))}
                />
              )}
            />

            <FormFieldRow
              label={<Label text={t("value")} />}
              value={
                <TextFieldController
                  control={control}
                  type="number"
                  name="value"
                  required
                />
              }
            />
          </Grid>
        </Box>

        <Stack
          direction="row"
          spacing={2}
          sx={{ mt: 4, pl: 1 }}
          justifyContent="flex-end"
        >
          <ButtonOutline onClick={handleBackActions}>{t("back")}</ButtonOutline>
          <ButtonPrimary type="submit" onClick={handleSaveActions}>
            {t("save")}
          </ButtonPrimary>
        </Stack>
      </form>
    </Box>
  );
};

export default CreateSumbangan;
