import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "./store";
import { getLocalStorage } from '../helpers/utils';
import { IAddressNode } from "@/types";

// Define a type for the slice state

export interface AddressDataState {
  data: IAddressNode[]
}

// Define the initial state using that type
const initialState: AddressDataState = {
  data: getLocalStorage("address_list", []),
};

export const addressDataSlice = createSlice({
  name: "addressData",
  initialState,
  reducers: {
    setAddressDataRedux: (state, action) => {
      state.data = action.payload
    },
  },
  selectors: {
    getAddressListsRedux: (state) => state.data ?? [],
    getStateListsRedux: (state) => state.data.filter((item) => item.level === 1) ?? [],
    getDistrictListsRedux: (state) => state.data.filter((item) => item.level === 2) ?? []
  }
});

export const { setAddressDataRedux } = addressDataSlice.actions;

export const {
  getAddressListsRedux,
  getStateListsRedux,
  getDistrictListsRedux
} = addressDataSlice.selectors;

export default addressDataSlice.reducer;
