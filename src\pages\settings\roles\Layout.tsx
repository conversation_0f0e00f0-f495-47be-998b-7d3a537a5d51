import React, { useState, useEffect, useMemo } from "react";
import { Box, Grid, IconButton, Typography } from "@mui/material";
import Sidebar from "../../../components/layout/Sidebar";
import MenuItem from "../../../components/layout/MenuItem";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import { PermissionNames, pageAccessEnum } from "@/helpers";
import CustomLinkTabContainer from "@/components/customLinkTab";
import AuthHelper from "@/helpers/authHelper";
import { getNavItem, MENU_INTERNAL, NavItem } from "@/helpers/menuConfig";

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();

  const hasPenyelenggaraanUmumPermission = AuthHelper.hasPageAccess(PermissionNames.UMUM.label, pageAccessEnum.Read);

  useEffect(() => {
    if (!hasPenyelenggaraanUmumPermission) {
      navigate("/internal-user");
    }
  }, [hasPenyelenggaraanUmumPermission, navigate]);

  const [urlMenu, setUrlMenu] = useState<string>();

  // menu permission
  const menus = getNavItem(MENU_INTERNAL(), "selenggara");
  const [allowedMenuItems, setAllowedMenuItems] = useState<NavItem[]>(menus);

  useEffect(() => {
    const finalMenuItems = allowedMenuItems.filter((item) => {
      if (item.permissions.length == 0) {
        // If the permissions (length = 0), it means no permission is required.
        return true;
      } else {
        return AuthHelper.hasAuthority(item.permissions);
      }
    });

    if (JSON.stringify(finalMenuItems) !== JSON.stringify(allowedMenuItems)) {
      setAllowedMenuItems(finalMenuItems);
    }
  }, [allowedMenuItems]);
  // menu permission

  const breadcrumbs = [
    {
      label: t("selenggara"),
      path: "#",
    },
  ];

  const handleSectionClick = (sectionIndex: number) => {
    const section = allowedMenuItems[sectionIndex];

    navigate(section.path);
    setUrlMenu(section.path);
  };

  useEffect(() => {
    setUrlMenu(window.location.pathname);
  }, [location.pathname]);

  const PengurusanPenggunaTabs = [
    {
      name: t("senaraiPenggunaJPPM"),
      navigate_screen: "./list-pengurusan-pengguna-jpm",
      keyword: "pengurusan-pengguna-jpm",
    },
    {
      name: t("senaraiPenggunaLuar"),
      navigate_screen: "./list-pengurusan-pengguna-luar",
      keyword: "pengurusan-pengguna-luar",
    },
  ];

  // const PengurusanPerananTabs = [
  //   {
  //     name: t("categoryPenggunaJPPM"),
  //     navigate_screen: "./category-pengurusan-peranan-jpm",
  //     keyword: "pengurusan-peranan-jpm",
  //   },
  //   {
  //     name: t("externalUserCategory"),
  //     navigate_screen: "./category-pengurusan-peranan-luar",
  //     keyword: "pengurusan-peranan-luar",
  //   },
  // ];

  return (
    <>
      <Box
        sx={{
          mb: 3,
          display: "flex",
          alignItems: "center",
          gap: 1,
        }}
      >
        <IconButton onClick={() => navigate(-1)}>
          <svg
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8.03065 10.7726C8.17126 10.632 8.25024 10.4412 8.25024 10.2423C8.25024 10.0435 8.17126 9.85274 8.03065 9.7121L4.31815 5.9996L8.03065 2.2871C8.16727 2.14565 8.24287 1.95619 8.24116 1.75955C8.23945 1.5629 8.16057 1.37479 8.02152 1.23573C7.88246 1.09668 7.69435 1.0178 7.4977 1.01609C7.30106 1.01438 7.1116 1.08998 6.97015 1.2266L2.7274 5.46935C2.5868 5.60999 2.50781 5.80072 2.50781 5.9996C2.50781 6.19847 2.5868 6.3892 2.7274 6.52985L6.97015 10.7726C7.1108 10.9132 7.30153 10.9922 7.5004 10.9922C7.69928 10.9922 7.89001 10.9132 8.03065 10.7726Z"
              fill="#666666"
            />
          </svg>
        </IconButton>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {breadcrumbs.map((item, index) => (
            <React.Fragment key={item.path}>
              <Typography
                fontSize={18}
                fontWeight={500}
                sx={{
                  color: "#666666",
                  cursor: "pointer",
                  "&:hover": {
                    textDecoration: "underline",
                  },
                }}
              >
                {item.label}
              </Typography>

              {index < breadcrumbs.length - 1 && (
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 12 12"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8.03065 10.7726C8.17126 10.632 8.25024 10.4412 8.25024 10.2423C8.25024 10.0435 8.17126 9.85274 8.03065 9.7121L4.31815 5.9996L8.03065 2.2871C8.16727 2.14565 8.24287 1.95619 8.24116 1.75955C8.23945 1.5629 8.16057 1.37479 8.02152 1.23573C7.88246 1.09668 7.69435 1.0178 7.4977 1.01609C7.30106 1.01438 7.1116 1.08998 6.97015 1.2266L2.7274 5.46935C2.5868 5.60999 2.50781 5.80072 2.50781 5.9996C2.50781 6.19847 2.5868 6.3892 2.7274 6.52985L6.97015 10.7726C7.1108 10.9132 7.30153 10.9922 7.5004 10.9922C7.69928 10.9922 7.89001 10.9132 8.03065 10.7726Z"
                    fill="#666666"
                  />
                </svg>
              )}
            </React.Fragment>
          ))}
        </Box>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={2}>
          <Sidebar
            sx={{
              // minWidth: "190px",
              padding: "37px 26px",
              height: "fit-content",
            }}
          >
            {allowedMenuItems.map((section, idx) => (
              <Box
                key={section.id}
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "10px",
                  textAlign: "center",
                }}
              >
                <MenuItem
                  onClick={() => handleSectionClick(idx)}
                  active={
                    section?.keyword && urlMenu
                      ? section?.keyword.some((keyword) =>
                          urlMenu.includes(keyword)
                        )
                      : urlMenu?.includes(section.path)
                  }
                  icon={<></>}
                  sx={{
                    margin: 0,
                    fontSize: "14px",
                    color:
                      section?.keyword && urlMenu
                        ? section?.keyword.some((keyword) =>
                            urlMenu.includes(keyword)
                          )
                          ? "#fff"
                          : "#666666"
                        : urlMenu?.includes(section.path)
                        ? "#fff"
                        : "#666666",
                    padding: 1.2,
                    fontWeight: 500,
                  }}
                >
                  {section.label}
                </MenuItem>
                <Box
                  sx={{
                    overflow: "hidden",
                    transition: "max-height 0.3s ease, opacity 0.3s ease",
                  }}
                ></Box>
              </Box>
            ))}
          </Sidebar>
        </Grid>
        <Grid item xs={10}>
          {location.pathname.includes("pengurusan-pengguna") ? (
            <Box sx={{ display: "grid", gap: 2 }}>
              <CustomLinkTabContainer tabs={PengurusanPenggunaTabs} />
              <Box sx={{ flexGrow: 1 }}>{children}</Box>
            </Box>
          ) : (
            // ) : location.pathname.includes("pengurusan-peranan") ? (
            //   <Box sx={{ display: "grid", gap: 2 }}>
            //     <CustomLinkTabContainer tabs={PengurusanPerananTabs} />
            //     <Box sx={{ flexGrow: 1 }}>{children}</Box>
            //   </Box>
            <Box sx={{ flexGrow: 1 }}>{children}</Box>
          )}
        </Grid>
      </Grid>
    </>
  );
};

export default Layout;
