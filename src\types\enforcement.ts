export interface ICancelledSociety {
  id: string | number;
  societyId: string | number;
  societyNo: string;
  societyName: string;
  branchId: string | number;
  branchNo: string;
  branchName: string;
  cancelledDate: string;
  section: string;
  reason: string;
  societyStatusCode: string;
  branchStatusCode: string;
}

export interface ISection {
  code: string;
  description: string;
  section: string;
  isBlacklistPermanent: boolean;
}

export interface IDocumentGuidance {
  id: string | number;
  name: string;
  referenceNumber: string;
  category: number;
  effectiveDate: string;
  yearOfRelease: number;
  summary: string;
  uploadDate: string;
  uploadedBy: string;
  activationStatus: number;
  visibilityStatus: number;
  url: string;
  createdDate: string;
  createdBy: string | number;
  modifiedDate: string;
  modifiedBy: string | number;
  status: boolean;
  statusNotes: string;
}

export interface IDocumentGuidancePublic {
  name: string;
  category: number;
  effectiveDate: string;
  yearOfRelease: number;
  pdfUrl: string | null;
  ebookUrl: string | null;
}
