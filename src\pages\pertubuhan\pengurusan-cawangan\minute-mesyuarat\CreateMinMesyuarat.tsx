import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON>,
  TextField,
  Typography,
  Grid,
  useMediaQuery,
  Theme,
  Select,
  MenuItem,
  FormControl,
  FormHelperText,
  IconButton,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import {
  MALAYSIA,
  MeetingContent,
  MeetingMethods,
  MeetingTypeOption,
} from "../../../../helpers/enums";
import {
  capitalizeWords,
  DocumentUploadType,
  getLocalStorage,
  useQuery,
  useUploadPresignedUrl,
} from "@/helpers";
import {
  FormMeetingDateTime,
  FormMeetingDateTimeRef,
} from "@/components/form/meeting/DateTime";
import dayjs from "@/helpers/dayjs";
import {
  FormMeetingAttendees,
  FormMeetingAttendeesBaseRef,
} from "@/components/form/meeting";
import { OrganizationManagementMeetingRequestBodyCreateOnlyAttendees } from "@/types";
import { LocationClient } from "@aws-sdk/client-location";
import { withAPIKey } from "@aws/amazon-location-utilities-auth-helper";
import { LoadingOverlay } from "@/components/loading";
import { EditIcon, TrashIcon } from "@/components/icons";
import { Check } from "@mui/icons-material";
import { AWSLocationSearchMap } from "@/components";
import { Dayjs } from "dayjs";

interface MeetingFormData {
  meetingType: string | number;
  meetingPurpose: string;
  meetingPlace: string;
  meetingMethod: string;
  platformType: string;
  meetingDate: string;
  meetingTime: string;
  GISInformation: string;
  meetingAddress: string;
  state: string;
  district: string;
  city: string;
  postcode: string;
  totalAttendees: number;
  meetingContent: string;
  openingRemarks: string;
  mattersDiscussed: string;
  othersDiscussionRemarks: string;
  closingRemarks: string;
  providedBy: string;
  confirmBy: string;
  meetingMinute: string;
  icNo: number;
  present: number;
  createdBy: string;
  modifiedBy: string;
  memberAttendances: Member[];
}

type UploadParams = {
  type: DocumentUploadType;
  societyId: number | null;
  societyNo: number | null;
  branchId: number | null;
  branchNo: number | null;
  meetingId: number | null;
  societyCommitteeId: number | null;
  icNo: string;
  name: string;
};

interface Member {
  id?: number;
  tempId?: number;
  name: string;
  position: string;
  status: number;
}

export const CreateMinMesyuarat: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const [isSaved, setIsSaved] = useState(false);
  const uploadParams: UploadParams = {
    type: DocumentUploadType.MEETING,
    societyId: null,
    societyNo: null,
    branchId: null,
    branchNo: null,
    meetingId: null,
    societyCommitteeId: null,
    icNo: "",
    name: "",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "32px",
    borderRadius: "16px",
    fontSize: "16px",
    fontWeight: "500 !important",
  };
  const labelStyle = {
    color: "#666666",
    fontWeight: "400 !important",
    fontSize: "14px",
  };

  const formMeetingAttendeesRef =
    useRef<
      FormMeetingAttendeesBaseRef<OrganizationManagementMeetingRequestBodyCreateOnlyAttendees>
    >();
  const formMeetingDateTimeRef = useRef<FormMeetingDateTimeRef>();
  const [generateMinutesButtonDisabled, setGenerateMinutesButtonDisabled] =
    useState(false);

  const getUserDetails = localStorage.getItem("user-details");
  const userName = getUserDetails ? JSON.parse(getUserDetails).name : "";
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [isCreated, setIsCreated] = useState(false);
  const [meetingContent, setMeetingContent] = useState("");
  const [masaMula, setMasaMula] = useState("");
  const [masaTamat, setMasaTamat] = useState("");
  const [client, setClient] = useState<LocationClient | null>(null);
  const [locations, setLocations] = useState<string[]>([]);
  const PLACE_INDEX_NAME = import.meta.env.VITE_AWS_PLACE_INDEX;
  const AWS_REGION = import.meta.env.VITE_AWS_API_KEY_REGION;
  const API_KEY = import.meta.env.VITE_AWS_API_KEY;
  const [fileNotUploaded, setFileNotUploaded] = useState(false);
  const meetingCreateRequest = localStorage.getItem("meetingCreateRequest");
  const parsedMeetingRequest = meetingCreateRequest
    ? JSON.parse(meetingCreateRequest)
    : null;
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState<MeetingFormData>(() => {
    return (
      parsedMeetingRequest || {
        meetingType: MeetingTypeOption[0].value,
        meetingPlace: "",
        meetingPurpose: "",
        meetingMethod: "",
        platformType: "",
        meetingDate: "",
        meetingTime: "",
        GISInformation: "",
        meetingAddress: "",
        state: "",
        district: "",
        city: "",
        postcode: "",
        totalAttendees: 0,
        meetingContent: "",
        openingRemarks: "",
        mattersDiscussed: "",
        othersDiscussionRemarks: "",
        closingRemarks: "",
        providedBy: userName,
        confirmBy: "",
        meetingMinute: "",
        icNo: 123456789,
        present: 0,
        createdBy: "admin",
        modifiedBy: "admin",
        memberAttendances: [],
      }
    );
  });

  const [members, setMembers] = useState<Member[]>([]);
  // const [newMember, setNewMember] = useState({ name: "", position: "" });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const [meetingMethod, setMeetingMethod] = useState("");

  const [platformType, setPlatformType] = useState("");
  // const [meetingContent, setMeetingContent] = useState("");
  const [meetingId, setMeetingId] = useState("");
  const [branchRegistrationDate, setBranchRegistrationDate] = useState<
    Date | Dayjs | undefined | null
  >();

  const [searchParams] = useSearchParams();
  const branchId = searchParams.get("id");
  const addressData = getLocalStorage("address_list", null);
  const [checked, setChecked] = useState(false);
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  const { data, isLoading } = useCustom({
    url: `${API_URL}/society/admin/meeting/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const meetingData = data?.data?.data || [];

  const [markerPosition, setMarkerPosition] = useState<[number, number]>(
    formData.GISInformation
      ? (formData.GISInformation.split(",").map(Number) as [number, number])
      : [2.745564, 101.707021] // Default position if GISInformation is empty
  );

  const handleMeetingLocationSelected = (location: {
    fullAddress: string;
    state: string;
    district: string;
    city: string;
    postcode: string;
  }) => {
    setFormData((prev) => ({
      ...prev,
      meetingAddress: location.fullAddress,
      city: location.city,
      postcode: location.postcode,
    }));
  };

  const [societyData, setSocietyData] = useState<any>({});
  const [branchData, setBranchData] = useState<any>({});
  const { id } = useParams();

  const { mutate: deleteFile, isLoading: isLoadingDelete } =
    useCustomMutation();

  const { data: branchList, isLoading: isLoadingBranch } = useCustom<any>({
    url: `${API_URL}/society/branch/getById/${branchId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: branchId !== null,
      retry: false,
      cacheTime: 0,
    },
  });

  useEffect(() => {
    if (branchList?.data?.data) {
      setBranchData(branchList?.data?.data);
    }
  }, [branchList]);

  const [MeetingListId, setMeetingListId] = useState();
  const { data: branchMeetingList, isLoading: isLoadingBranchMeetingList } =
    useCustom({
      url: `${API_URL}/society/meeting/findByBranchId/${Number(branchId)}`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        // query: {
        //   identificationNo: email,
        // },
      },
      queryOptions: {
        enabled: !!branchId,
        retry: false,
        cacheTime: 0,
        onSuccess: (responseData) => {
          console.log("responseData", responseData?.data?.data);
          if (responseData?.data?.data[0]?.id) {
            setMeetingListId(responseData?.data?.data[0]?.id);
          }
        },
      },
    });

  const downloadPdfTemplate = async () => {
    const response = await fetch(
      `${API_URL}/society/pdf/getPDFTemplate?templateCode=MEETING_MINUTES_PDF`,
      {
        method: "get",
        headers: {
          "Content-Type": "application/json",
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      }
    );
    const data = (await response.json()) as {
      status: "SUCCESS";
      data: { meetingMinutes: string };
    };
    if (data.status === "SUCCESS") {
      const url = data?.data?.meetingMinutes ?? "";
      window.open(url, "_blank", "noreferrer,noopener");
    }
  };

  const [meetingCoords, setMeetingCoords] = useState<[number, number]>([
    101.707021, 2.745564,
  ]);

  const { data: meetingDetails, isLoading: isLoadingMeetingDetails } =
    useCustom({
      url: `${API_URL}/society/meeting/${Number(MeetingListId)}`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
      queryOptions: {
        enabled: !!MeetingListId,
        retry: false,
        cacheTime: 0,
      },
    });

  useEffect(() => {
    if (meetingDetails?.data?.data) {
      const lastMeeting = meetingDetails?.data?.data;
      setFormData((prev) => ({
        ...prev,
        meetingType: lastMeeting?.meetingType || MeetingTypeOption[0].value,
        meetingPlace: lastMeeting?.meetingPlace,
        meetingPurpose: lastMeeting?.meetingPurpose,
        meetingMethod: lastMeeting?.meetingMethod?.toString() || "",
        platformType: lastMeeting?.platformType?.toString() || "",
        meetingDate: lastMeeting?.meetingDate,
        meetingAddress: lastMeeting?.meetingAddress,
        state: lastMeeting?.state?.toString() || "",
        district: lastMeeting?.district?.toString() || "",
        city: lastMeeting?.city,
        postcode: lastMeeting?.postcode,
        totalAttendees: lastMeeting?.totalAttendees,
        meetingContent: lastMeeting?.meetingContent,
        openingRemarks: lastMeeting?.openingRemarks,
        mattersDiscussed: lastMeeting?.mattersDiscussed,
        othersDiscussionRemarks: lastMeeting?.otherMatters,
        closingRemarks: lastMeeting?.closing,
        providedBy: lastMeeting?.providedBy,
        confirmBy: lastMeeting?.confirmBy,
        memberAttendances: lastMeeting?.meetingMemberAttendances ?? [],
      }));

      setMeetingMethod(lastMeeting?.meetingMethod?.toString() || "");
      setPlatformType(lastMeeting?.platformType?.toString() || "");
      setMeetingContent(lastMeeting?.meetingContent);

      setMeetingId(lastMeeting?.id);

      // Set meeting from time if available
      if (lastMeeting?.meetingTime) {
        const fromTime = lastMeeting?.meetingTime.split(":");
        setMasaMula(`${fromTime[0]}:${fromTime[1]}`);
      }

      // Set meeting to time if available
      if (lastMeeting?.meetingTimeTo) {
        const toTime = lastMeeting?.meetingTimeTo.split(":");
        setMasaTamat(`${toTime[0]}:${toTime[1]}`);
      }

      setIsCreated(true);
      setGenerateMinutesButtonDisabled(false);
    } else {
      setGenerateMinutesButtonDisabled(true);
    }
  }, [meetingDetails]);

  useEffect(() => {
    if (id) {
      const fetchSocietyData = async () => {
        try {
          const response = await fetch(`${API_URL}/society/${id}/basic`, {
            method: "GET",
            headers: {
              portal: localStorage.getItem("portal") || "",
              Authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
              "Content-Type": "application/json",
            },
          });
          if (!response.ok) {
            throw new Error("Failed to fetch data");
          }
          const result = await response.json();
          const data = result?.data;
          setSocietyData(data);
        } catch (error) {
          console.error("Error fetching society data:", error);
        }
      };
      fetchSocietyData();
    }
  }, [id]);

  // ✅ AWS Location Client Initialization

  useEffect(() => {
    const initializeClient = async () => {
      try {
        const authHelper = await withAPIKey(API_KEY, AWS_REGION);
        const locationClient = new LocationClient(authHelper.getClientConfig());
        setClient(locationClient);
      } catch (error) {
        console.error("❌ Failed to initialize AWS Location Client:", error);
      }
    };
    initializeClient();
  }, []);

  // ✅ AWS Location Client Initialization
  useEffect(() => {
    const initializeClient = async () => {
      try {
        const authHelper = await withAPIKey(API_KEY, AWS_REGION);
        const locationClient = new LocationClient(authHelper.getClientConfig());
        setClient(locationClient);
      } catch (error) {
        console.error("❌ Failed to initialize AWS Location Client:", error);
      }
    };
    initializeClient();
  }, []);

  useEffect(() => {
    if (meetingCreateRequest) {
      setIsCreated(true);
    }
  }, [meetingCreateRequest]);

  useEffect(() => {
    if (meetingId && id && branchId) {
      refetchDocuments();
    }
  }, [meetingId, id, branchId]);

  const handleSave = async () => {
    const errors = validateForm();
    console.log("memberAttendances", formData?.memberAttendances);
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);

      console.log(errors);
      return;
    }

    if (
      formData.postcode.length !== 5 &&
      (formData.meetingMethod == MeetingMethods.BERSEMUKA ||
        formData.meetingMethod == MeetingMethods.HYBRID)
    ) {
      return {
        message: t("postcodeValidation"),
        type: "error",
      };
    }

    const dateTimeValue = formMeetingDateTimeRef.current?.getValue();
    const totalAttendees = formData?.totalAttendees ?? 0;
    const members = formData?.memberAttendances ?? [];
    const meetingDate = dateTimeValue.meetingDate;
    const formattedTime = dateTimeValue.meetingTime;
    const formattedTimeFrom = formattedTime;
    const formattedTimeTo = dateTimeValue.meetingTimeTo;

    const memberAttendances = members.map(({ id, ...rest }) =>
      String(id).startsWith("new-") ? rest : { id, ...rest }
    );
    const filteredAttendances = memberAttendances.filter(
      //@ts-ignore
      ({ id, status }) => !(status === -1 && !id)
    );

    if (societyData) {
      const data = {
        societyId: Number(id),
        branchId: branchId,
        society_no: societyData.societyNo,
        meetingType: formData.meetingType ?? "",
        meetingPlace: formData.meetingPlace ?? "",
        meetingPurpose: formData.meetingPurpose ?? "",
        meetingMethod: formData.meetingMethod?.toString() ?? "",
        platformType: formData.platformType?.toString() ?? "",
        meetingDate: meetingDate,
        meetingTime: formattedTime ?? "",
        meetingTimeFrom: formattedTimeFrom ?? "",
        meetingTimeTo: formattedTimeTo ?? "",
        GISInformation: "",
        meetingAddress: formData.meetingAddress ?? "",
        state: formData.state?.toString() ?? "",
        district: formData.district?.toString() ?? "",
        city: formData.city ?? "",
        postcode: formData.postcode ?? "",
        totalAttendees,
        meetingContent: formData.meetingContent ?? "",
        providedBy: formData.providedBy ? formData.providedBy : userName,
        confirmBy: formData.confirmBy ?? "",
        meetingMinute: "",
        status: 1,
        memberAttendances: filteredAttendances,
        openingRemarks: formData.openingRemarks,
        mattersDiscussed: formData.mattersDiscussed,
        otherMatters: formData.othersDiscussionRemarks,
        closing: formData.closingRemarks,
      };

      if (selectedFile || uploadedFiles?.length) {
        setFileNotUploaded(false);

        if (meetingId) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          data["id"] = meetingId;
          await updateMeeting(
            {
              method: "put",
              url: `${API_URL}/society/meeting/update`,
              values: data,
              config: {
                headers: {
                  portal: localStorage.getItem("portal"),
                  authorization: `Bearer ${localStorage.getItem(
                    "refine-auth"
                  )}`,
                },
              },
              successNotification: (data) => {
                setIsCreated(true);
                if (uploadedFiles?.length && selectedFile) {
                  handleDeleteFile(uploadedFiles[0].id);

                  uploadParams.name = selectedFile.name;
                  uploadParams.societyId = societyData.id;
                  uploadParams.societyNo = societyData.societyNo;
                  uploadParams.icNo = societyData.identificationNo;
                  uploadParams.meetingId = data?.data?.data;
                  uploadParams.branchId = Number(branchId);
                  upload({
                    file: selectedFile,
                    params: uploadParams,
                  });
                } else if (uploadedFiles?.length === 0 && selectedFile) {
                  uploadParams.name = selectedFile.name;
                  uploadParams.societyId = societyData.id;
                  uploadParams.societyNo = societyData.societyNo;
                  uploadParams.icNo = societyData.identificationNo;
                  uploadParams.meetingId = data?.data?.data;
                  uploadParams.branchId = Number(branchId);
                  upload({
                    file: selectedFile,
                    params: uploadParams,
                  });
                }

                return {
                  message: t("messageMaklumatMesyuaratPenubuhanSuccess"),
                  type: "success",
                };
              },
              errorNotification: (data) => {
                return {
                  message: t("messageMaklumatMesyuaratPenubuhanError"),
                  type: "error",
                };
              },
            },
            {
              onSuccess(data, variables, context) {},
            }
          );
        } else {
          await createMeeting({
            resource: "society/meeting/create",
            values: data,
            meta: {
              headers: {
                portal: localStorage.getItem("portal"),
                authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
              },
            },
            successNotification: (data) => {
              setIsSaved(true);
              setMeetingId(data?.data?.data);
              // uploadSelectedFiles(societyData, data);
              if (selectedFile) {
                uploadParams.name = selectedFile.name;
                uploadParams.societyId = societyData.id;
                uploadParams.societyNo = societyData.societyNo;
                uploadParams.icNo = societyData.identificationNo;
                uploadParams.meetingId = data?.data?.data;
                uploadParams.branchId = Number(branchId);
                setIsCreated(true);
                uploadedFiles?.length;
                upload({
                  file: selectedFile,
                  params: uploadParams,
                });
              }

              return {
                message: t("messageMaklumatMesyuaratPenubuhanSuccess"),
                type: "success",
              };
            },
            errorNotification: (data) => {
              return {
                message: t("messageMaklumatMesyuaratPenubuhanError"),
                type: "error",
              };
            },
          });
        }
      } else {
        setFileNotUploaded(true);
      }
    }
  };

  const downloadFile = (filePath: string, fileName: string) => {
    const link = document.createElement("a");
    link.href = filePath;
    link.download = fileName;
    link.click();
  };

  const janaMinit = async () => {
    const meetingDateTimeValue = formMeetingDateTimeRef.current?.getValue();

    const meetingDate = meetingDateTimeValue.meetingDate;
    const meetingTimeFrom = meetingDateTimeValue.meetingTime;
    const meetingTimeTo = meetingDateTimeValue.meetingTimeTo;
    // Validate mandatory fields
    const mandatoryFields = [
      { field: formData.meetingType, name: t("meetingType") },
      { field: formData.meetingMethod, name: t("meetingMethod") },
      { field: formData.meetingPurpose, name: t("tujuanMesyuarat") },
      { field: meetingDate, name: t("tarikhMesyuarat") },
      { field: meetingTimeFrom, name: t("startDate") },
      { field: meetingTimeTo, name: t("endDate") },
    ];

    // Add conditional mandatory fields based on meetingMethod
    if (
      formData.meetingMethod == MeetingMethods.BERSEMUKA ||
      formData.meetingMethod == MeetingMethods.HYBRID
    ) {
      mandatoryFields.push(
        { field: formData.meetingPlace, name: t("tempatMesyuarat") },
        { field: formData.meetingAddress, name: t("alamatTempatMesyuarat") },
        { field: formData.state, name: t("state") },
        { field: formData.district, name: t("district") },
        { field: formData.postcode, name: t("poskod") }
      );
    }

    // Add conditional mandatory fields based on meetingMethod
    if (
      formData.meetingMethod == MeetingMethods.ATAS_TALIAN ||
      formData.meetingMethod == MeetingMethods.HYBRID
    ) {
      mandatoryFields.push({
        field: formData.platformType,
        name: t("platformType"),
      });
    }

    // Check for empty mandatory fields
    const emptyFields = mandatoryFields.filter((field) => !field.field);

    if (emptyFields.length > 0) {
      alert(
        t("mandatoryFieldsAlert") +
          " : \n" +
          emptyFields.map((field) => field.name).join(", ")
      );
      return;
    }

    // If validation passes, proceed with existing janaMinit logic
    try {
      if (branchId) {
        const meetingDateTimeValue = formMeetingDateTimeRef.current?.getValue();
        // console.log(
        //   "attendeesValue?.memberAttendances",
        //   formData?.totalAttendees,
        //   formData?.memberAttendances
        // );
        const cleanedMemberAttendances = formData?.memberAttendances?.filter(
          (member) =>
            member.name?.trim() ||
            member.position?.trim() ||
            member?.status === -1
        );

        const meetingDate = meetingDateTimeValue.meetingDate;
        const meetingTimeFrom = meetingDateTimeValue.meetingTime;
        const meetingTimeTo = meetingDateTimeValue.meetingTimeTo;
        const response = await fetch(`${API_URL}/society/pdf/meetingMinutes`, {
          method: "post",
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
          body: JSON.stringify({
            societyId: id,
            branchId: branchId,
            meetingTitle: "",
            meetingDescription: "",
            meetingDate: meetingDate,
            meetingTimeFrom: meetingTimeFrom,
            meetingTimeTo: meetingTimeTo,
            meetingLocation: formData.meetingPlace,
            committees: cleanedMemberAttendances,
            expectedAttendance: formData?.totalAttendees,
            openingRemarks: formData.openingRemarks,
            mattersDiscussed: formData.mattersDiscussed,
            othersDiscussionRemarks: formData.othersDiscussionRemarks,
            closingRemarks: formData.closingRemarks,
            preparedBy: formData.providedBy,
            approvedBy: formData.confirmBy,
          }),
        });
        const data = await response.json();
        if (data.status === "SUCCESS") {
          let base64String = data?.data?.byte ?? "";

          if (base64String.startsWith("JVB")) {
            base64String = `data:application/pdf;base64,${base64String}`;
          } else if (base64String.startsWith("data:application/pdf;base64")) {
            //alert("Not a valid Base64 PDF string. Please check");
          } else {
            //alert("Not a valid Base64 PDF string. Please check");
          }
          downloadFile(base64String, `minit-mesyuarat.pdf`);
        }
      }
    } catch (error) {
      console.error("error:", error);
      return null;
    }
  };

  const {
    upload,
    progress,
    isLoading: isUploadingFile,
    returnId,
  } = useUploadPresignedUrl({
    onSuccessUpload: () => {
      setSelectedFile(null);
      navigate(`../ahlijawatankuasa?id=${branchId}`);
    },
  });

  const handleInputChange = (
    e:
      | React.ChangeEvent<HTMLInputElement>
      | { target: { name: string; value: string } }
  ) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const DeleteFile: (id: number | undefined) => void = (id) => {
    if (!id) return;

    deleteFile(
      {
        url: `${API_URL}/society/document/deleteDocument?id=${id}`,
        method: "put",
        values: {},
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  function handleDeleteFile(id: number | undefined) {
    DeleteFile(id);
  }

  const validateForm = () => {
    const attendeesErrors = formMeetingAttendeesRef.current?.getErrors();
    const dateTimeErrors = formMeetingDateTimeRef.current?.getErrors();
    const errors: { [key: string]: string } = {};
    if (!formData.meetingMethod) errors.meetingMethod = t("requiredValidation");
    if (!formData.meetingPurpose)
      errors.meetingPurpose = t("requiredValidation");
    if (dateTimeErrors?.meetingDate)
      errors.meetingDate = dateTimeErrors.meetingDate.message ?? "";
    if (attendeesErrors?.totalAttendees) {
      errors.totalAttendees = attendeesErrors.totalAttendees.message ?? "";
    }
    if (
      formData.meetingMethod == MeetingMethods.ATAS_TALIAN ||
      formData.meetingMethod == MeetingMethods.HYBRID
    ) {
      if (!formData.platformType) errors.platformType = t("requiredValidation");
    }
    if (dateTimeErrors?.meetingTime) {
      errors.masaMula = dateTimeErrors.meetingTime.message ?? "";
    }
    if (dateTimeErrors?.meetingTimeTo) {
      errors.masaTamat = dateTimeErrors.meetingTimeTo.message ?? "";
    }

    if (
      meetingMethod == MeetingMethods.BERSEMUKA ||
      meetingMethod == MeetingMethods.HYBRID
    ) {
      if (!formData.meetingPlace) {
        errors.meetingPlace = t("requiredValidation");
      }

      if (!formData.meetingAddress) {
        errors.meetingAddress = t("requiredValidation");
      }

      if (!formData.state) {
        errors.state = t("requiredValidation");
      }

      if (!formData.district) {
        errors.district = t("requiredValidation");
      }

      if (!formData.postcode) {
        errors.postcode = t("requiredValidation");
      } else if (!/^\d{5}$/.test(formData.postcode)) {
        errors.postcode = t("postcodeValidation");
      }
    }

    return errors;
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setFormData((prevState: any) => ({
        ...prevState,
        meetingMinute: file.name,
      }));
    }
  };

  const {
    data: existingDocuments,
    refetch: refetchDocuments,
    isLoading: isLoadingDocument,
  } = useQuery({
    url: "society/document/documentByParam",
    filters: [
      {
        field: "societyId",
        operator: "eq",
        value: id,
      },
      {
        field: "type",
        operator: "eq",
        value: DocumentUploadType.MEETING,
      },
      {
        field: "branchId",
        operator: "eq",
        value: branchId,
      },
      {
        field: "meetingId",
        operator: "eq",
        value: meetingId,
      },
    ],
    autoFetch: false,
  });

  useEffect(() => {
    setUploadedFiles(existingDocuments?.data?.data);
  }, [existingDocuments?.data?.data]);

  const { mutateAsync: createMeeting, isLoading: isCreating } = useCreate();
  const { mutateAsync: updateMeeting, isLoading: isUpdating } =
    useCustomMutation();

  const handleReset = () => {
    // Reset all form fields to their initial values

    setFormData({
      meetingType: MeetingTypeOption[0].value,
      meetingPlace: "",
      meetingPurpose: "",
      meetingMethod: "",
      platformType: "",
      meetingDate: "",
      meetingTime: "",
      GISInformation: "",
      meetingAddress: "",
      state: "",
      district: "",
      city: "",
      postcode: "",
      totalAttendees: 0,
      meetingContent: "",
      openingRemarks: "",
      mattersDiscussed: "",
      othersDiscussionRemarks: "",
      closingRemarks: "",
      providedBy: "",
      confirmBy: "",
      meetingMinute: "",
      icNo: 123456789,
      present: 0,
      createdBy: "admin",
      modifiedBy: "admin",
      memberAttendances: [],
    });

    // Reset all other state variables
    setMeetingMethod("");
    setPlatformType("");
    setMeetingContent("");
    setMasaMula("");
    setMasaTamat("");
    setMembers([]);
    setSelectedFile(null);
  };

  const deletedAttendeesRef = useRef<Record<string, any>>({}); // Store deleted attendees

  useEffect(() => {
    setFormData((prev) => {
      const existingAttendees = prev.memberAttendances.filter(
        (a) => a.status !== -1
      );
      const currentCount = existingAttendees.length;

      if (prev.totalAttendees > currentCount) {
        // Restore deleted attendees first
        const availableDeleted = Object.values(
          deletedAttendeesRef.current
        ).splice(0, prev.totalAttendees - currentCount);

        // Add new attendees if needed
        const newAttendees = Array.from(
          {
            length:
              prev.totalAttendees - currentCount - availableDeleted.length,
          },
          (_, index) => ({
            id: `new-${Date.now()}-${index}`,
            name: "",
            position: "",
            status: 0,
          })
        );

        return {
          ...prev,
          memberAttendances: [
            ...existingAttendees,
            ...availableDeleted,
            ...newAttendees,
          ],
        };
      } else if (prev.totalAttendees < currentCount) {
        // Store deleted attendees
        existingAttendees.slice(prev.totalAttendees).forEach((attendee) => {
          //@ts-ignore
          deletedAttendeesRef.current[attendee.id] = attendee;
        });

        return {
          ...prev,
          memberAttendances: existingAttendees.slice(0, prev.totalAttendees),
        };
      }
      return prev;
    });
    // console.log("MEMBERS", formData?.memberAttendances);
  }, [formData.totalAttendees]);

  const handleDelete = (id: any) => {
    setFormData((prev) => {
      const attendeeToDelete = prev.memberAttendances.find((a) => a.id === id);

      if (attendeeToDelete) {
        deletedAttendeesRef.current[id] = attendeeToDelete;
      }

      const updatedAttendees = prev.memberAttendances.map((attendee) =>
        attendee.id === id ? { ...attendee, status: -1 } : attendee
      );

      return {
        ...prev,
        memberAttendances: updatedAttendees,
        totalAttendees: Math.max(0, prev.totalAttendees - 1), // Decrease totalAttendees
      };
    });
  };
  // Save an attendee (move from edit to confirmed state)
  const handleSaveAttendees = (id: any) => {
    setFormData((prev) => ({
      ...prev,
      memberAttendances: prev.memberAttendances.map((attendee) =>
        attendee.id === id ? { ...attendee, status: 1 } : attendee
      ),
    }));
  };

  // Edit an attendee (move from confirmed to edit state)
  const handleEdit = (id: any) => {
    setFormData((prev) => ({
      ...prev,
      memberAttendances: prev.memberAttendances.map((attendee) =>
        attendee.id === id ? { ...attendee, status: 0 } : attendee
      ),
    }));
  };

  // Handle changes to totalAttendees
  const handleTotalAttendeesChange = (e: any) => {
    const newTotal = Number(e.target.value);
    setFormData((prev) => ({
      ...prev,
      totalAttendees: newTotal,
    }));
  };

  useEffect(() => {
    if (branchData) {
      const branchRegistrationDate: Date | Dayjs | undefined | null =
        branchData?.submissionDate ? dayjs(branchData?.submissionDate) : null;
      setBranchRegistrationDate(branchRegistrationDate);
    }
  }, [branchData]);

  return (
    <>
      <LoadingOverlay isLoading={isLoadingBranchMeetingList} />
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px" }}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {capitalizeWords(t("establishmentMeeting"))}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("meetingType")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FormControl fullWidth>
                <Select
                  sx={{ backgroundColor: "#DADADA" }}
                  value={formData.meetingType}
                  disabled
                  displayEmpty
                  required
                  onChange={(e) => {
                    setFormData((prevState: any) => ({
                      ...prevState,
                      meetingType: e.target.value,
                    }));
                  }}
                >
                  {MeetingTypeOption.map((item: any, index) => (
                    <MenuItem key={index} value={item.value}>
                      {item.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("meetingMethod")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FormControl fullWidth error={!!formErrors.meetingMethod}>
                <Select
                  value={
                    meetingCreateRequest
                      ? formData.meetingMethod
                      : meetingMethod || ""
                  }
                  name="meetingMethod"
                  displayEmpty
                  required
                  disabled={isLoading}
                  onChange={(e) => {
                    setMeetingMethod(e.target.value);
                    setFormData((prevState) => ({
                      ...prevState,
                      meetingMethod: e.target.value,
                    }));
                    if (e.target.value) {
                      setFormErrors((prev) => ({
                        ...prev,
                        meetingMethod: "",
                      }));
                    }
                  }}
                >
                  <MenuItem value="" disabled>
                    {isLoading ? "Loading..." : t("pleaseSelect")}
                  </MenuItem>
                  {!isLoading &&
                    meetingData
                      .filter((item: any) => item.pid === 2)
                      .map((item: any) => (
                        <MenuItem key={item.id} value={item.id}>
                          {i18n.language === "en"
                            ? item.nameEn
                            : item.nameBm == "Hybrid"
                            ? "Hibrid"
                            : item.nameBm}
                        </MenuItem>
                      ))}
                </Select>
                {formErrors.meetingMethod && (
                  <FormHelperText>{formErrors.meetingMethod}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            {meetingMethod == MeetingMethods.ATAS_TALIAN ||
            meetingMethod == MeetingMethods.HYBRID ? (
              <>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("platformType")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <FormControl fullWidth error={!!formErrors.platformType}>
                    <Select
                      name="platformType"
                      value={
                        meetingCreateRequest
                          ? formData.platformType
                          : platformType || ""
                      }
                      displayEmpty
                      required
                      disabled={isLoading}
                      onChange={(e) => {
                        setPlatformType(e.target.value);
                        setFormData((prevState) => ({
                          ...prevState,
                          platformType: e.target.value,
                        }));
                        setFormErrors((prev) => ({
                          ...prev,
                          platformType: "",
                        }));
                      }}
                    >
                      <MenuItem value="" disabled>
                        {isLoading ? "Loading..." : t("pleaseSelect")}
                      </MenuItem>
                      {!isLoading &&
                        meetingData
                          .filter((item: any) => item.pid === 3)
                          .map((item: any) => (
                            <MenuItem key={item.id} value={item.id}>
                              {i18n.language === "en"
                                ? item.nameEn
                                : item.nameBm}
                            </MenuItem>
                          ))}
                    </Select>
                    {formErrors.platformType && (
                      <FormHelperText>{formErrors.platformType}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              </>
            ) : (
              <></>
            )}
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("tujuanMesyuarat")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                fullWidth
                required
                name="meetingPurpose"
                value={formData.meetingPurpose}
                onChange={handleInputChange}
                error={!!formErrors.meetingPurpose}
                helperText={formErrors.meetingPurpose}
              />
            </Grid>
          </Grid>
        </Box>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("masaDanTarikhMesyuarat")}
          </Typography>
          <FormMeetingDateTime
            branchRegistrationDate={
              branchRegistrationDate ? dayjs(branchRegistrationDate) : undefined
            }
            // @ts-expect-error
            ref={formMeetingDateTimeRef}
            meetingTimeFromAttribute="meetingTime"
            defaultValues={{
              meetingDate:
                formData.meetingDate.length > 0
                  ? dayjs(formData.meetingDate, "YYYY-MM-DD")
                  : null,
              meetingTime:
                formData.meetingDate.length > 0
                  ? dayjs(
                      `${formData.meetingDate} ${
                        masaMula?.length > 0 ? masaMula : "00:00:00"
                      }`,
                      "YYYY-MM-DD HH:mm:[00]"
                    )
                  : null,
              meetingTimeTo:
                formData.meetingDate.length > 0
                  ? dayjs(
                      `${formData.meetingDate} ${
                        masaTamat?.length > 0 ? masaTamat : "00:00:00"
                      }`,
                      "YYYY-MM-DD HH:mm:[00]"
                    )
                  : null,
            }}
          />
        </Box>
        {meetingMethod == MeetingMethods.BERSEMUKA ||
        meetingMethod == MeetingMethods.HYBRID ? (
          <>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mb: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("alamatTempatMesyuarat")}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("tempatMesyuarat")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    required
                    name="meetingPlace"
                    value={formData.meetingPlace}
                    onChange={handleInputChange}
                    error={!!formErrors.meetingPlace}
                    helperText={formErrors.meetingPlace}
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("meetingLocation")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <div
                    style={{
                      height: "150px",
                      width: "100%",
                      borderRadius: "8px",
                    }}
                  >
                    <AWSLocationSearchMap
                      longitude={meetingCoords[0]}
                      latitude={meetingCoords[1]}
                      onLocationSelected={handleMeetingLocationSelected}
                    />
                  </div>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("alamatTempatMesyuarat")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    required
                    name="meetingAddress"
                    value={formData.meetingAddress}
                    error={!!formErrors.meetingAddress}
                    helperText={formErrors.meetingAddress}
                    onChange={handleInputChange}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("state")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <FormControl fullWidth required error={!!formErrors.state}>
                    <Select
                      value={formData.state}
                      displayEmpty
                      required
                      name="state"
                      //disabled={isLoadingAddress}
                      onChange={(e) => {
                        setFormData((prevState) => ({
                          ...prevState,
                          state: e.target.value,
                        }));
                        setFormErrors((prev) => ({
                          ...prev,
                          state: "",
                        }));
                      }}
                    >
                      <MenuItem value="" disabled>
                        {t("pleaseSelect")}
                      </MenuItem>
                      {addressData
                        .filter((item: any) => item.pid === MALAYSIA)
                        .map((item: any) => (
                          <MenuItem key={item.id} value={item.id}>
                            {item.name}
                          </MenuItem>
                        ))}
                    </Select>
                    {formErrors.state && (
                      <FormHelperText>{formErrors.state}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("district")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <FormControl fullWidth required error={!!formErrors.district}>
                    <Select
                      value={formData.district}
                      displayEmpty
                      required
                      name="district"
                      disabled={!formData.state}
                      onChange={(e) => {
                        setFormData((prevState) => ({
                          ...prevState,
                          district: e.target.value,
                        }));
                        setFormErrors((prev) => ({
                          ...prev,
                          district: "",
                        }));
                      }}
                    >
                      <MenuItem value="" disabled>
                        {t("pleaseSelect")}
                      </MenuItem>
                      {addressData
                        .filter((item: any) => item.pid == formData.state)
                        .map((item: any) => (
                          <MenuItem key={item.id} value={item.id}>
                            {item.name}
                          </MenuItem>
                        ))}
                    </Select>
                    {formErrors.district && (
                      <FormHelperText>{formErrors.district}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("bandar")}</Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    name="city"
                    value={formData.city}
                    onChange={handleInputChange}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("poskod")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    required
                    name="postcode"
                    value={formData.postcode}
                    onChange={(e: any) => {
                      const value = e.target.value.replace(/[^0-9]/g, "");
                      if (value.length <= 5) {
                        handleInputChange({
                          ...e,
                          target: {
                            name: "postcode",
                            value,
                          },
                        });
                      }
                    }}
                    error={
                      (formData.postcode.length > 0 &&
                        formData.postcode.length !== 5) ||
                      !!formErrors.postcode
                    }
                    helperText={
                      (formData.postcode.length > 0 &&
                      formData.postcode.length !== 5
                        ? t("masukkan5DigitPoskod")
                        : "") || formErrors.postcode
                    }
                    type="text"
                    onKeyDown={(e) => {
                      if (
                        e.key.toLowerCase() === "e" ||
                        e.key === "E" ||
                        e.key === "+" ||
                        e.key === "-"
                      ) {
                        e.preventDefault();
                      }
                    }}
                    inputProps={{
                      maxLength: 5,
                      pattern: "[0-9]*",
                    }}
                  />
                </Grid>
              </Grid>
            </Box>
          </>
        ) : (
          <></>
        )}

        <FormMeetingAttendees
          // @ts-expect-error
          ref={formMeetingAttendeesRef}
          defaultValues={{
            totalAttendees: formData.totalAttendees,
          }}
        />
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("minitMesyuarat")}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("minitMesyuarat")} <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <Box
                sx={{
                  border: "2px solid #DADADA",
                  borderRadius: "8px",
                  p: 2,
                  gap: 2,
                  textAlign: "center",
                  cursor: "pointer",
                  height: "200px",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "center",
                }}
                onClick={() => {
                  const element = document.getElementById("meetingMinute");
                  if (element) {
                    element.click();
                  }
                }}
              >
                {selectedFile || uploadedFiles?.length ? (
                  <Typography sx={{ color: "#147C7C", mb: 1 }}>
                    {selectedFile ? selectedFile.name : uploadedFiles[0]?.name}
                  </Typography>
                ) : (
                  <>
                    <Box
                      sx={{
                        width: 50,
                        aspectRatio: "1/1",
                        display: "flex",
                        justifyContent: "center",
                        alignContent: "center",
                        textAlign: "center",
                        borderRadius: 20,
                        mb: 2,
                        // p: 5,
                        bgcolor: "#F2F4F7",
                      }}
                    >
                      <img
                        width={30}
                        src={"/uploadFileIcon.svg"}
                        alt={"view"}
                      />
                    </Box>

                    <Typography
                      sx={{
                        color: "var(--primary-color)",
                        fontWeight: "500",
                        fontSize: "14px",
                      }}
                    >
                      {t("muatNaik")}
                    </Typography>
                    <Typography
                      sx={{
                        color: "#667085",
                        fontWeight: "400",
                        fontSize: "12px",
                      }}
                    >
                      {`PDF, DOCX, DOC or TXT <25 MB`}
                    </Typography>
                  </>
                )}
                <input
                  id="meetingMinute"
                  type="file"
                  hidden
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx,.txt"
                />
              </Box>

              {fileNotUploaded &&
              (uploadedFiles?.length === 0 || !selectedFile) ? (
                <FormHelperText sx={{ color: "red" }}>
                  {t("fieldRequired")}
                </FormHelperText>
              ) : null}
              <Typography
                sx={{
                  mt: 3,
                  color: "#147C7C",
                  textDecoration: "underline",
                  cursor: "pointer",
                }}
                onClick={downloadPdfTemplate}
              >
                {t("downloadTemplate")}.
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Box
                sx={{
                  px: { xs: 1, sm: 2, md: 3 },
                  py: 1,
                  border: "1px solid #D9D9D9",
                  backgroundColor: "#FFFFFF",
                  borderRadius: "14px",
                  mt: 2,
                  display: "flex",
                  justifyContent: "flex-start",
                  alignItems: "flex-start",
                }}
              >
                <FormControlLabel
                  sx={{
                    alignItems: "flex-start",
                    color: "#666666",
                    "& .MuiFormControlLabel-label": {
                      fontWeight: 400,
                    },
                  }}
                  control={
                    <Checkbox
                      checked={checked}
                      onChange={handleChangeCheckbox}
                      sx={{ mt: -1 }}
                    />
                  }
                  label={
                    <>
                      {t("meetingInfoConfirmation")}{" "}
                      <span style={{ color: "red" }}>*</span>
                    </>
                  }
                />
              </Box>
            </Grid>
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline
                sx={{
                  bgcolor: "white",
                  "&:hover": { bgcolor: "white" },
                  width: isMobile ? "100%" : "auto",
                }}
                onClick={handleReset}
              >
                {t("semula")}
              </ButtonOutline>
              <ButtonPrimary
                disabled={isCreating || isUpdating || !checked}
                onClick={handleSave}
                variant="contained"
                sx={{ width: isMobile ? "100%" : "auto" }}
              >
                {isCreating ? t("saving") : t("update")}
              </ButtonPrimary>
            </Grid>
            {meetingContent == MeetingContent.YA && (
              <>
                <Grid
                  item
                  xs={12}
                  sx={{
                    mt: 2,
                    display: "flex",
                    flexDirection: isMobile ? "column" : "row",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <ButtonPrimary
                    variant="contained"
                    onClick={janaMinit}
                    disabled={generateMinutesButtonDisabled}
                    sx={{
                      width: isMobile ? "100%" : "auto",
                    }}
                  >
                    {t("janaMinit")}
                  </ButtonPrimary>
                </Grid>
              </>
            )}
          </Grid>
        </Box>
        <Box sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
          <ButtonPrimary
            onClick={() => navigate(`../ahlijawatankuasa?id=${branchId}`)}
            variant="contained"
            sx={{
              width: isMobile ? "100%" : "auto",
              float: "right",
            }}
          >
            {t("next")}
          </ButtonPrimary>
        </Box>
      </Box>
    </>
  );
};

export default CreateMinMesyuarat;
