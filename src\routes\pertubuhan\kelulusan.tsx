import { Route, Outlet } from "react-router-dom";
import { RouteGuard } from "../../components/RouteGuard";
import { registerRoutes } from "../../helpers/routeDetector";
import PertubuhanLayout from "../../pages/pertubuhan/Layout";
import Layout from "../../pages/pertubuhan/kelulusan/Layout";
import ListKelulusanMasaLanjut from "../../pages/pertubuhan/kelulusan/masalanjut/ListKelulusanMasaLanjut";
import ListPenubuhanCawangan from "../../pages/pertubuhan/kelulusan/penubuhan-cawangan/ListPenubuhanCawangan";
import ListBukanWnInduk from "../../pages/pertubuhan/kelulusan/bukanwn-induk/ListBukanWnInduk";
import CreateBukanWnInduk from "../../pages/pertubuhan/kelulusan/bukanwn-induk/CreateBukanWnInduk";
import CreatePSetiausahaBwn from "../../pages/pertubuhan/kelulusan/bukanwn-induk/CreatePSetiausahaBwn";
import CreatePAjkBwn from "../../pages/pertubuhan/kelulusan/bukanwn-induk/CreatePAjkBwn";
import CreatePAjkBwnPpm from "../../pages/pertubuhan/kelulusan/bukanwn-induk/CreatePAjkBwnPpm";
import PenubuhanInduk from "../../pages/pertubuhan/kelulusan/penubuhan-induk";
import CreateMam from "../../pages/pertubuhan/kelulusan/penubuhan-induk/CreateMam";
import Semak from "../../pages/pertubuhan/kelulusan/penubuhan-induk/Semak";
import CreateMesyuarat from "../../pages/pertubuhan/kelulusan/penubuhan-induk/CreateMesyuarat";
import CreatePerlembagaan from "../../pages/pertubuhan/kelulusan/penubuhan-induk/CreatePerlembagaan";
import SemakPerlembagaan from "../../pages/pertubuhan/kelulusan/penubuhan-induk/SemakPerlembagaan";
import SenaraiAjk from "../../pages/pertubuhan/kelulusan/penubuhan-induk/SenaraiAjk";
import PaparAjkWargaNegara from "../../pages/pertubuhan/kelulusan/penubuhan-induk/PaparAjkWargaNegara";
import DokumenSokongan from "../../pages/pertubuhan/kelulusan/penubuhan-induk/DokumenSokongan";
import KeputusanPermohonan from "../../pages/pertubuhan/kelulusan/penubuhan-induk/KeputusanPermohonan";
import MenungguAgensiLuar from "../../pages/pertubuhan/kelulusan/menunggu-agensi-luar";
import Kemaskini from "../../pages/pertubuhan/kelulusan/menunggu-agensi-luar/Kemaskini";
import FormKelulusanMasaLanjut from "../../pages/pertubuhan/kelulusan/masalanjut/Form/KelulusanMasaLanjut";
import KelulusanMinitMesyuarat from "../../pages/pertubuhan/kelulusan/penubuhan-cawangan/Form/KelulusanMinitMesyuarat";
import KelulusanAhliJawatanKuasa from "../../pages/pertubuhan/kelulusan/penubuhan-cawangan/Form/KelulusanAhliJawantanKuasa";
import KelulusanDokumenSokongan from "../../pages/pertubuhan/kelulusan/penubuhan-cawangan/Form/KelulusanDokumenSokongan";
import KelulusanKeputusanPermohonan from "../../pages/pertubuhan/kelulusan/penubuhan-cawangan/Form/KelulusanKeputusanPermohonan";
import FormKelulusanPenubuhCawangan from "../../pages/pertubuhan/kelulusan/penubuhan-cawangan/Form/KelulusanPenubuhanCawangan";
import PerlembagaanLayout from "../../pages/pertubuhan/pengurusan-perlembagaan/PerlembagaanLayout";
import UpdateMaklumatPerlembagaan from "../../pages/pertubuhan/pengurusan-perlembagaan/UpdateMaklumatPerlembagaan";
import ListPindaanPerlembagaan from "../../pages/pertubuhan/pengurusan-perlembagaan/ListPindaanPerlembagaan";
import UpdatePindaanPerlembagaan from "../../pages/pertubuhan/pengurusan-perlembagaan/UpdatePindaanPerlembagaan";
import SemakanPerlembagaan from "../../pages/pertubuhan/pengurusan-perlembagaan/SemakPerlembagaan";
import ListSenaraiPindaan from "../../pages/pertubuhan/kelulusan/senarai-pindaan/ListSenaraiPindaan";
import PaparMaklumatPerlembagaan from "../../pages/pertubuhan/pengurusan-perlembagaan/PaparMaklumatPerlembagaan";
import PindaanUndang from "../../pages/pertubuhan/kelulusan/pindaan-undang";
import KemaskiniPerlembagaan from "../../pages/pertubuhan/kelulusan/pindaan-undang/KemaskiniPerlembagaan";
import SemakPerlembagaanPindaan from "../../pages/pertubuhan/kelulusan/pindaan-undang/SemakPerlembagaan";
import PembaharuanSetiausaha from "../../pages/pertubuhan/kelulusan/pembaharuan-setiausaha";
import KemaskiniPembaharuanSetiaUsaha from "../../pages/pertubuhan/kelulusan/pembaharuan-setiausaha/Kemaskini";
import Keputusan from "../../pages/pertubuhan/kelulusan/pembaharuan-setiausaha/Keputusan";
import PengurusPertubuhanInternalLayout from "../../pages/pengurusan-pertubuhan-internal/PengurusPertubuhanLayout";

// Layout component to wrap all kelulusan routes with protection
const KelulusanLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <PengurusPertubuhanInternalLayout>
      <Outlet />
    </PengurusPertubuhanInternalLayout>
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example route registrations (uncomment and modify as needed):
  // '/pertubuhan/kelulusan': 'internal',
  // '/pertubuhan/kelulusan/penubuhan-induk': 'internal',
  // '/pertubuhan/kelulusan/menunggu-agensi-luar': 'internal',
  // '/pertubuhan/kelulusan/masa-lanjut': 'internal',
  // '/pertubuhan/kelulusan/penubuhan-cawangan': 'internal',
  // '/pertubuhan/kelulusan/bukan-warganegara-induk': 'internal',
  // '/pertubuhan/kelulusan/pindaan-undang-undang': 'internal',
  // '/pertubuhan/kelulusan/pembaharuan-setiausaha': 'internal',
  // Add your route registrations here
});

export const kelulusan = {
  resources: [
    {
      name: "society/getAllPending",
      list: "/society/getAllPending",
    },
    {
      name: "society/:id/admin/updateRo",
      list: "/society/:id/admin/updateRo",
    },
    {
      name: "society/:id/admin/updateApprovalStatus",
      list: "/society/:id/admin/updateApprovalStatus",
    },
  ],
  routes: (
    <>
      <Route path="kelulusan" element={<KelulusanLayout />}>
        <Route path="penubuhan-induk">
          <Route index element={<PenubuhanInduk />} />

          <Route path="kemaskini/:id">
            <Route path="maklumat-am">
              <Route index element={<CreateMam />} />

              <Route path="semak" element={<Semak />} />
            </Route>

            <Route path="mesyuarat">
              <Route index element={<CreateMesyuarat />} />
            </Route>

            <Route path="perlembagaan">
              <Route index element={<CreatePerlembagaan />} />

              <Route
                path="semak-perlembagaan"
                element={<SemakPerlembagaan />}
              />
            </Route>

            <Route path="senarai-ajk">
              <Route index element={<SenaraiAjk />} />

              <Route
                path="papar-ajk-warganegara"
                element={<PaparAjkWargaNegara />}
              />
            </Route>

            <Route path="dokumen-sokongan">
              <Route index element={<DokumenSokongan />} />
            </Route>

            <Route path="keputusan-permohonan">
              <Route index element={<KeputusanPermohonan />} />
            </Route>
          </Route>
        </Route>

        <Route path="menunggu-agensi-luar">
          <Route index element={<MenungguAgensiLuar />} />

          <Route path="kemaskini" element={<Kemaskini />} />
        </Route>

        <Route path="masa-lanjut">
          <Route path="list">
            <Route index element={<ListKelulusanMasaLanjut />} />
          </Route>
          <Route path="update">
            <Route index element={<FormKelulusanMasaLanjut />} />
          </Route>
        </Route>

        <Route path="penubuhan-cawangan">
          <Route path="list">
            <Route index element={<ListPenubuhanCawangan />} />
          </Route>
          <Route path="update/:id">
            <Route index element={<FormKelulusanPenubuhCawangan />} />
            <Route
              path="meeting-minutes"
              element={<KelulusanMinitMesyuarat />}
            />
            <Route
              path="ahlijawatankuasa"
              element={<KelulusanAhliJawatanKuasa />}
            />
            <Route
              path="dokumen-sokongan"
              element={<KelulusanDokumenSokongan />}
            />
            <Route
              path="keputusan"
              element={<KelulusanKeputusanPermohonan />}
            />
          </Route>
        </Route>

        <Route path="bukan-warganegara-induk">
          <Route path="list">
            <Route index element={<ListBukanWnInduk />} />
          </Route>
          <Route path="update">
            <Route index element={<CreateBukanWnInduk />} />
          </Route>
          <Route path="update-setiausaha-bwn">
            <Route index element={<CreatePSetiausahaBwn />} />
          </Route>
          <Route path="update-ajk-bwn">
            <Route index element={<CreatePAjkBwn />} />
          </Route>
          <Route path="update-ajk-bwn-ppm">
            <Route index element={<CreatePAjkBwnPpm />} />
          </Route>
        </Route>

        <Route path="pindaan-undang-undang">
          <Route index element={<PindaanUndang />} />

          <Route path="kemaskini">
            <Route index element={<KemaskiniPerlembagaan />} />

            <Route path="semak" element={<SemakPerlembagaanPindaan />} />
          </Route>
        </Route>

        <Route path="pembaharuan-setiausaha">
          <Route index element={<PembaharuanSetiausaha />} />

          <Route path="kemaskini">
            <Route index element={<KemaskiniPembaharuanSetiaUsaha />} />

            <Route path="keputusan" element={<Keputusan />} />
          </Route>
        </Route>
      </Route>

      {/* <Route
        path="perlembagaan"
        element={
          <PerlembagaanLayout>
            <Outlet />
          </PerlembagaanLayout>
        }
      >
        <Route path="maklumat">
          <Route index element={<UpdateMaklumatPerlembagaan />} />

          <Route path="pindaan-perlembagaan">
            <Route index element={<ListPindaanPerlembagaan />} />

            <Route path="update/:id">
              <Route index element={<UpdatePindaanPerlembagaan />} />
            </Route>

            <Route path="semakan">
              <Route index element={<SemakanPerlembagaan />} />
            </Route>
          </Route>
        </Route>

        <Route path="pindaan">
          <Route index element={<ListSenaraiPindaan />} />

          <Route path="papar">
            <Route index element={<PaparMaklumatPerlembagaan />} />
          </Route>
        </Route>
      </Route> */}
    </>
  ),
};
