import { useState, useEffect } from "react";
import dayjs from "dayjs";
import { useForm } from "@refinedev/react-hook-form";
import useQuery from "../../../../helpers/hooks/useQuery";
import { useTranslation } from "react-i18next";
import {
  AuditTrailEnum,
  AuditTrailType,
  NEW_PermissionNames,
  pageAccessEnum,
} from "../../../../helpers/enums";
import { Box, Button, Grid, Typography } from "@mui/material";
import Input from "../../../../components/input/Input";
import { ButtonPrimary, ButtonOutline } from "../../../../components/button";
import DataTable, { IColumn } from "@/components/datatable";
import AuthHelper from "@/helpers/authHelper";

const tabs = [
  {
    label: "Pengguna Luar",
    value: "1",
    permissionNames:
      NEW_PermissionNames.SELENGGARA.children.AUDIT_TRAIL.children.PENGGUNA_LUAR
        .label,
  },
  {
    label: "Pengguna JPPM",
    value: "2",
    permissionNames:
      NEW_PermissionNames.SELENGGARA.children.AUDIT_TRAIL.children.PENGGUNA_JPPM
        .label,
  },
  {
    label: "My Identity",
    value: "0",
    permissionNames:
      NEW_PermissionNames.SELENGGARA.children.AUDIT_TRAIL.children.MY_IDENTITY
        .label,
  },
];

const enrichedTabs = tabs.map((item) => ({
  ...item,
  hasPermission: AuthHelper.hasPageAccess(
    item.permissionNames,
    pageAccessEnum.Read
  ),
}));

export default function AuditTrail() {
  const hasReadPermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.SELENGGARA.children.AUDIT_TRAIL.children.PENGGUNA_LUAR
      .label,
    pageAccessEnum.Read
  );

  const { t } = useTranslation();
  const [currentAudittrailList, setCurrentAudittrailList] = useState([]);
  const { watch, setValue, handleSubmit } = useForm({
    defaultValues: {
      identificationNo: "",
      module: "",
      userGroup: "1",
      page: 1,
      pageSize: 10,
    },
  });
  const page = watch("page");
  const pageSize = watch("pageSize");
  const isMyIdentityTab = !Number(watch("userGroup"));
  const [isFetched, setIsFetched] = useState(false);

  const columns: IColumn[] = [
    {
      field: "createdDate",
      headerName: isMyIdentityTab ? t("tarikhDanMasaRekod") : t("date"),
      flex: 1,
      renderCell: ({ row }: any) => {
        const parsedDate = dayjs(row.createdDate, "HH:mm DD:MM:YYYY", true);
        const dateFormat = isMyIdentityTab
          ? "DD-MM-YYYY hh.mm A"
          : "DD-MM-YYYY";
        const formattedDate = parsedDate.format(dateFormat);

        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {formattedDate}
          </Box>
        );
      },
    },
    {
      field: "userName",
      headerName: t("direkodOleh"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.userName}
          </Box>
        );
      },
    },
    ...(!isMyIdentityTab
      ? [
          {
            field: "module",
            headerName: t("modul"),
            flex: 1,
            renderCell: ({ row }: any) => {
              return (
                <Box sx={{ fontWeight: "500", textAlign: "center" }}>
                  {row?.module}
                </Box>
              );
            },
          },
        ]
      : []),
    {
      field: "identificationNo",
      headerName: t("referenceNumber"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.identificationNo}
          </Box>
        );
      },
    },
    {
      field: "societyNo",
      headerName: t("keterangan"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {AuditTrailEnum[row?.actionType]}
          </Box>
        );
      },
    },
  ];

  const {
    data: auditTrailData,
    isLoading,
    refetch: fetchAuditTrail,
  } = useQuery({
    url: "society/audit-trail/search",
    filters: [
      {
        field: "identificationNo",
        operator: "eq",
        value: watch("identificationNo"),
      },
      ...(watch("module")
        ? [
            {
              field: "module",
              operator: "eq" as const,
              value: watch("module"),
            },
          ]
        : []),
      { field: "userGroup", operator: "eq", value: watch("userGroup") },
      { field: "pageSize", operator: "eq", value: pageSize },
      { field: "pageNo", operator: "eq", value: page },
    ],
    autoFetch: false,
    onSuccess: (data) => {
      const alldata = data?.data?.data?.data;
      setCurrentAudittrailList(alldata || []);
    },
  });

  const onSubmit = () => {
    setIsFetched(true);
    fetchAuditTrail();
  };

  useEffect(() => {
    if (isFetched) {
      fetchAuditTrail();
    }
  }, [pageSize, page]);

  const totalRecords = auditTrailData?.data?.data?.total ?? 0;

  const getAuditTrailList = (data: any) => {
    if (!data) return;
    const uniqueMap = new Map();

    AuditTrailType.forEach((item: any) => {
      if (!uniqueMap.has(item.value)) {
        uniqueMap.set(item.value, item);
      }
    });
    const uniqueList = Array.from(uniqueMap.values());
    const finaList = uniqueList.map((item) => {
      return { label: item.value, value: item.value };
    });
    return finaList;
  };

  useEffect(() => {
    fetchAuditTrail({
      filters: [
        {
          field: "identificationNo",
          operator: "eq",
          value: watch("identificationNo"),
        },
        {
          field: "module",
          operator: "eq",
          value: watch("module"),
        },
        { field: "userGroup", operator: "eq", value: watch("userGroup") },
        { field: "pageSize", operator: "eq", value: pageSize },
        { field: "pageNo", operator: "eq", value: page },
      ],
    });
  }, [watch("module")]);

  return (
    <>
      <Box>
        <Box
          sx={{
            display: "flex",
            background: "#fff",
            borderRadius: "10px",
            p: 1,
            boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
            overflowX: "auto",
          }}
        >
          {enrichedTabs.map((tab) => (
            <Button
              key={tab.value}
              onClick={(e: any) => {
                if (tab.hasPermission) {
                  setValue("userGroup", tab.value);
                }
              }}
              disabled={!tab.hasPermission}
              sx={{
                textTransform: "none",
                fontWeight: watch("userGroup") === tab.value ? 600 : 400,
                color: watch("userGroup") === tab.value ? "#fff" : "#333",
                background:
                  watch("userGroup") === tab.value
                    ? "var(--primary-color)"
                    : "transparent",
                borderRadius: "5px",
                px: 3,
                py: 1,
                width: "100%",
                transition: "all 0.3s ease",
                "&:hover": {
                  background:
                    watch("userGroup") === tab.value
                      ? "var(--primary-color)"
                      : "#F1F4FA",
                },
              }}
            >
              {tab.label}
            </Button>
          ))}
        </Box>

        <Box
          sx={{
            border: "1px solid #dfdfdf",
            borderRadius: "10px",
            padding: "30px",
            background: "#fff",
            mt: 1,
          }}
        >
          <Grid container item xs={"auto"}>
            <Grid
              xs={12}
              sx={{ padding: 0, textAlign: "left", paddingBottom: 3 }}
              item
            >
              <Typography
                sx={{
                  fontFamily: "Poppins, sans-serif",
                  fontSize: "16px",
                  fontWeight: 600,
                  lineHeight: "24px",
                  color: "var(--primary-color)",
                }}
              >
                {t("carianAuditTrail")}
              </Typography>
            </Grid>
            <Grid xs={3} sx={{ padding: 0, textAlign: "left" }} item>
              <Typography
                sx={{
                  fontFamily: "Poppins, sans-serif",
                  fontSize: "16px",
                  lineHeight: "24px",
                  fontWeight: 300,
                }}
              >
                {t("idNumberPlaceholder")}
              </Typography>
            </Grid>
            <Grid xs={9} item>
              <Input
                value={watch("identificationNo")}
                onChange={(e: any) =>
                  setValue("identificationNo", e.target.value)
                }
                inputProps={{ maxLength: 12 }}
              />
            </Grid>
          </Grid>

          <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
            <ButtonOutline
              sx={{ marginRight: 2 }}
              onClick={() => {
                setValue("identificationNo", "");
                setValue("module", "");
                fetchAuditTrail({
                  filters: [
                    {
                      field: "identificationNo",
                      operator: "eq",
                      value: null,
                    },
                    {
                      field: "module",
                      operator: "eq",
                      value: null,
                    },
                    {
                      field: "userGroup",
                      operator: "eq",
                      value: watch("userGroup"),
                    },
                    { field: "pageSize", operator: "eq", value: pageSize },
                    { field: "pageNo", operator: "eq", value: 1 },
                  ],
                });
              }}
            >
              {t("semula")}
            </ButtonOutline>
            <ButtonPrimary
              onClick={handleSubmit(onSubmit)}
              disabled={
                !watch("identificationNo") || !watch("userGroup") || isLoading
              }
            >
              {t("cari")}
            </ButtonPrimary>
          </Box>
        </Box>

        {!isMyIdentityTab ? (
          <Box
            sx={{
              border: "1px solid #dfdfdf",
              borderRadius: "10px",
              padding: "30px",
              background: "#fff",
              mt: 1,
            }}
          >
            <Grid container item xs={"auto"}>
              <Grid
                xs={12}
                sx={{ padding: 0, textAlign: "left", paddingBottom: 3 }}
                item
              ></Grid>
              <Grid xs={12} item>
                <Input
                  label={t("module")}
                  disabled={isLoading}
                  options={getAuditTrailList(AuditTrailType)}
                  type="select"
                  value={watch("module")}
                  onChange={(e: any) => setValue("module", e.target.value)}
                  inputProps={{ maxLength: 12 }}
                />
              </Grid>
            </Grid>
          </Box>
        ) : null}

        <Box
          sx={{
            border: "1px solid #dfdfdf",
            borderRadius: "10px",
            padding: "30px",
            background: "#fff",
            mt: 1,
          }}
        >
          <Grid
            xs={12}
            sx={{ padding: 0, textAlign: "left", paddingBottom: 3 }}
            item
          >
            <Typography
              sx={{
                fontFamily: "Poppins, sans-serif",
                fontSize: "16px",
                fontWeight: 600,
                lineHeight: "24px",
                color: "var(--primary-color)",
              }}
            >
              {t("keputusan")}
            </Typography>
          </Grid>

          <DataTable
            columns={columns}
            rows={currentAudittrailList}
            page={page}
            rowsPerPage={pageSize}
            isLoading={isLoading}
            totalCount={totalRecords}
            onPageChange={(newPage) => setValue("page", newPage)}
            onPageSizeChange={(newPageSize) => {
              setValue("page", 1);
              setValue("pageSize", newPageSize);
            }}
          />
        </Box>
      </Box>
    </>
  );
}
