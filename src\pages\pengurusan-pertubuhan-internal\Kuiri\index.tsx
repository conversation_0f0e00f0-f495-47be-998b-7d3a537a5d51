import { NavLink } from "react-router-dom";
import { Outlet } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { Box } from "@mui/material";
import WrapContent from "../View/WrapContent";
import Menu from "./Menu";
import { SocietyQueryPendingCountResponseBodyGet } from "../../../types";
import useQuery from "@/helpers/hooks/useQuery";
import AuthHelper from "@/helpers/authHelper";
import ForbiddenPage from "@/pages/forbidden";
import { NEW_PermissionNames, pageAccessEnum } from "@/helpers";

const Kuiri = <
  QueryPendingData extends SocietyQueryPendingCountResponseBodyGet = SocietyQueryPendingCountResponseBodyGet
>() => {
  const { t } = useTranslation();

  const { data: queryPendingResponse, isLoading } = useQuery<{
    data: QueryPendingData;
  }>({
    url: "society/roDecision/getAllPendingCount/query",
  });
  const queryPendingData = queryPendingResponse?.data.data ?? null;

  interface Tab {
    number: number;
    name: string;
    slug: string;
    permissionName?: keyof typeof PERMISSIONS; // optional
  }

  const tab: Tab[] = [
    {
      name: t("pendaftaranPertubuhanInduk"),
      slug: "pendaftaran-pertubuhan-induk",
      number: queryPendingData?.societyRegistrationQueryPendingCount ?? 0,
      permissionName: "induk",
    },
    {
      name: t("pendaftaranCawangan"),
      slug: "pendaftaran-cawangan",
      number: queryPendingData?.branchRegistrationQueryPendingCount ?? 0,
      permissionName: "cawangan",
    },
    {
      name: t("pindaanUndangUndangInduk"),
      slug: "pindaan-undang-undang-induk",
      number: queryPendingData?.societyAmendmentQueryPendingCount ?? 0,
      permissionName: "pindaanUndangUndangInduk",
    },
    {
      name: t("rayuan"),
      slug: "rayuan",
      number: queryPendingData?.societyAppealQueryPendingCount ?? 0,
      permissionName: "rayuan",
    },
    {
      name: t("pembaharuanSetiausaha"),
      slug: "pembaharuan-setiausaha",
      number: queryPendingData?.societyPrincipalSecretaryPendingCount ?? 0,
      permissionName: "pembaharuanSetiausaha",
    },
  ];

  const PERMISSIONS = {
    induk:
      NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
        .KUIRI.children.PENDAFTARAN_PERTUBUHAN_INDUK_KUIRI.label,
    cawangan:
      NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
        .KUIRI.children.PENDAFTARAN_CAWANGAN_KUIRI.label,
    pindaanUndangUndangInduk:
      NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
        .KUIRI.children.PINDAAN_PERLEMBAGAAN_KUIRI.label,
    rayuan:
      NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
        .KUIRI.children.RAYUAN_KUIRI.label,
    pembaharuanSetiausaha:
      NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
        .KUIRI.children.PEMBAHARUAN_SETIAUSAHA_KUIRI.label,
  } as const;

  const permissionMap = Object.fromEntries(
    Object.entries(PERMISSIONS).map(([key, label]) => [
      key,
      AuthHelper.hasPageAccess(label, pageAccessEnum.Read),
    ])
  ) as Record<keyof typeof PERMISSIONS, boolean>;

  const visibleTabs = tab.map((tab) => ({
    ...tab,
    hasPermission: !tab.permissionName || permissionMap[tab.permissionName],
  }));

  if (visibleTabs.length === 0) {
    return <ForbiddenPage internal />;
  }

  return (
    <Box>
      <WrapContent title={t("kuiri")}>
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(180px, 1fr))",
            gap: "16px",
          }}
        >
          {visibleTabs.map((data, index) => {
            return (
              <NavLink
                key={`tab-query-${index}`}
                end={index === 0}
                to={`/pengurus-pertubuhan/keputusan-pertubuhan/kuiri${
                  data.slug === "pendaftaran-pertubuhan-induk"
                    ? ""
                    : `/${data.slug}`
                }`}
                style={{
                  textDecoration: "none",
                  pointerEvents: data.hasPermission ? "auto" : "none",
                }}
              >
                {({ isActive }) => (
                  <Menu
                    disabled={!data.hasPermission}
                    data={data}
                    isActive={!data.hasPermission ? false : isActive}
                    onClick={() => {}}
                    isLoading={isLoading}
                  />
                )}
              </NavLink>
            );
          })}
        </Box>
      </WrapContent>

      <Outlet context={{ queryPendingData }} />
    </Box>
  );
};

export default Kuiri;
