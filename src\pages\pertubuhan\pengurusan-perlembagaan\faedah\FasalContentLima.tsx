import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { capitalizeWords, getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import AsalContent from "@/components/asalFasalContent";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import MessageDialog from "@/components/dialog/message";
import { DialogConfirmation } from "@/components";
import RemoveIcon from "@mui/icons-material/Remove";
interface FasalContentLimaFaedahProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: [];
  name: string;
}

type addJawatanBilanganeProps = {
  tempId: string;
  constitutionContentId: null;
  societyName: string;
  definitionName: string;
  titleName: string;
  matchId?: string;
};

type addJawatanTitleProps = {
  tempId: string;
  constitutionContentId: null;
  societyName: string;
  definitionName: string;
  titleName: string;
  customJawatan: boolean;
  matchId?: string;
};
const AddAhliJawatanComponent = ({
  data,
  allBilanganData,
  removeHandler,
  onChangeTitleHandler,
  onChangeBilanganHandler,
}: {
  data: any;
  allBilanganData: any;
  removeHandler: (tempId: number) => void;
  onChangeTitleHandler: (
    tempId: number,
    field: keyof addJawatanTitleProps,
    value: string
  ) => void;
  onChangeBilanganHandler: (
    tempId: number,
    field: keyof addJawatanTitleProps,
    value: string
  ) => void;
}) => {
  const isViewMode = getLocalStorage("isViewMode", false);
  const getBilanganDataById = allBilanganData.filter(
    (items: any) => items.tempId === data.tempId
  )?.[0];
  return (
    <Grid
      container
      spacing={2}
      sx={{ mt: 0, display: "flex", alignItems: "center" }}
    >
      <Grid item xs={12} md={2}>
        <TextField
          disabled={isViewMode}
          type="number"
          onKeyDown={(e) => {
            if (
              e.key.toLowerCase() === "e" ||
              e.key === "E" ||
              e.key === "+" ||
              e.key === "-"
            ) {
              e.preventDefault();
            }
          }}
          size="small"
          placeholder="0"
          fullWidth
          required
          value={getBilanganDataById.definitionName}
          onChange={(e) => {
            onChangeBilanganHandler(
              data.tempId,
              "definitionName",
              e.target.value
            );
          }}
          InputProps={{
            inputProps: {
              inputMode: "numeric",
              pattern: "[0-9]*",
              min: 0,
            },
          }}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          disabled={isViewMode}
          type="text"
          size="small"
          fullWidth
          required
          value={data.definitionName}
          onChange={(e) =>
            onChangeTitleHandler(data.tempId, "definitionName", e.target.value)
          }
        />
      </Grid>
      <IconButton
        size="small"
        onClick={() => removeHandler(data.tempId)}
        sx={{ mt: 2, ml: 1 }}
      >
        <Box
          sx={{
            background: "#FF0000",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            borderRadius: "100%",
            height: "16px",
            width: "16px",
          }}
        >
          <RemoveIcon sx={{ color: "#fff", width: "16px" }} />
        </Box>
      </IconButton>
    </Grid>
  );
};

const ExistingAhliJawatanComponent = ({
  allBilanganData,
  ahliData,
  removeHandler,
  onChangeTitleHandler,
  onChangeBilanganHandler,
}: {
  allBilanganData: any;
  ahliData: any;
  removeHandler: (matchId: string | number, id: number) => void;
  onChangeTitleHandler: (
    id: number,
    field: keyof addJawatanTitleProps,
    value: string
  ) => void;
  onChangeBilanganHandler: (
    id: number,
    field: keyof addJawatanTitleProps,
    value: string
  ) => void;
}) => {
  const isViewMode = getLocalStorage("isViewMode", false);

  const id = ahliData.id;
  const aHliDataMatchid = ahliData.matchId;
  const getBilanganDataById = allBilanganData.filter(
    (data: any) => data.matchId === aHliDataMatchid
  )?.[0];

  return (
    <Grid
      container
      spacing={2}
      sx={{ mt: 0, display: "flex", alignItems: "center" }}
    >
      <Grid item xs={12} md={2}>
        <TextField
          disabled={isViewMode}
          type="number"
          onKeyDown={(e) => {
            if (
              e.key.toLowerCase() === "e" ||
              e.key === "E" ||
              e.key === "+" ||
              e.key === "-"
            ) {
              e.preventDefault();
            }
          }}
          size="small"
          placeholder="0"
          fullWidth
          required
          value={getBilanganDataById.definitionName}
          onChange={(e) => {
            onChangeBilanganHandler(
              getBilanganDataById.matchId,
              "definitionName",
              e.target.value
            );
          }}
          InputProps={{
            inputProps: {
              inputMode: "numeric",
              pattern: "[0-9]*",
              min: 0,
            },
          }}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          disabled={isViewMode}
          type="text"
          size="small"
          fullWidth
          required
          value={ahliData.definitionName}
          onChange={(e) =>
            onChangeTitleHandler(
              ahliData.matchId,
              "definitionName",
              e.target.value
            )
          }
        />
      </Grid>
      <IconButton
        size="small"
        onClick={() => removeHandler(ahliData.matchId, id)}
        sx={{ mt: 2, ml: 1 }}
      >
        <Box
          sx={{
            background: "#FF0000",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            borderRadius: "100%",
            height: "16px",
            width: "16px",
          }}
        >
          <RemoveIcon sx={{ color: "#fff", width: "16px" }} />
        </Box>
      </IconButton>
    </Grid>
  );
};

export const FasalContentLimaFaedah: React.FC<FasalContentLimaFaedahProps> = ({
  activeStep,
  setActiveStep,
  clause,
  asalData,
  name,
}) => {
  const [asal, setAsal] = useState<any>(null);
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const [showErrorAjk, setShowErrorAjk] = useState(false);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [tempohJawatan, setTempohJawatan] = useState(t("setahun"));
  const [minAjk, setMinAjk] = useState("0");
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [lainlain, setLainLain] = useState("");
  const [kekerapan, setKekerapan] = useState("");
  const [tempohPelucutan, setTempohPelucutan] = useState("");
  const [tempohPelucutanWaktu, setTempohPelucutanWaktu] = useState(t("day"));
  const [pengerusi, setPengerusi] = useState(t("chairman"));
  const [jumlahPengerusi, setJumlahPengerusi] = useState<any>("1");
  const [timbalan, setTimbalan] = useState(t("timbalanPengerusi"));
  const [jumlahTimbalan, setJumlahTimbalan] = useState<any>("");
  const [naib, setNaib] = useState(t("naibPengerusi"));
  const [jumlahNaib, setJumlahNaib] = useState<any>("");
  const [setiaUsaha, setSetiaUsaha] = useState(t("secretary"));
  const [jumlahSetiaUsaha, setJumlahSetiaUsaha] = useState<any>("1");
  const [penolongSetiaUsaha, setPenolongSetiaUsaha] = useState(
    t("asistantSecretary")
  );
  const [jumlahPenolongSetiaUsaha, setJumlahPenolongSetiaUsaha] =
    useState<any>("");
  const [bendahari, setBendahari] = useState(t("treasurer"));
  const [jumlahBendahari, setJumlahBendahari] = useState<any>("1");
  const [penolongBendahari, setPenolongBendahari] = useState(
    t("asistantTreasurer")
  );
  const [jumlahPenolongBendahari, setJumlahPenolongBendahari] =
    useState<any>("");
  const [ahliBiasa, setAhliBiasa] = useState(t("ordinaryCommitteeMember"));
  const [jumlahAhliBiasa, setJumlahAhliBiasa] = useState<any>("");

  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [clauseContentId, setClauseContentId] = useState("");
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");

  const [existingCustomJawatanBilangan, setExistingCustomJawatanBilangan] =
    useState<addJawatanTitleProps[]>([]);
  const [existingCustomJawatanAhli, setExistingCustomJawatanAhli] = useState<
    addJawatanBilanganeProps[]
  >([]);
  const [numberAddAhliBilangan, setNumberAddAhliBilangan] = useState<
    addJawatanBilanganeProps[]
  >([]);
  const [numberAddAhli, setNumberAddAhli] = useState<addJawatanTitleProps[]>(
    []
  );
  const [selectedDeleteId, setSelectedDeleteId] = useState<number | null>(null);
  const [selectedMatchId, setSelectedMatchId] = useState<
    number | string | null
  >(null);
  const [showConfirmDeleteDialog, setShowConfirmDeleteDialog] = useState(false);

  const [showIsRestrictedWord, setShowIsRestrictedWord] = useState(false);

  const restrictedTitles = [
    t("chairman"),
    t("presiden"),
    t("pengarah"),
    t("thepresident"),
    t("timbalanPengerusi"),
    t("vicePresident"),
    t("timbalanPengarah"),
    t("naibPengerusi"),
    t("naibPresiden"),
    t("naibPengarah"),
    t("secretary"),
    t("generalSecretary"),
    t("asistantSecretary"),
    t("generalAssistantSecretary"),
    t("treasurer"),
    t("chiefTreasurer"),
    t("asistantTreasurer"),
    t("chiefAssistantTreasurer"),
    t("ordinaryCommitteeMember"),
    "Juruaudit",
    "Juru audit",
    "Auditor",
    "Pemegang amanah",
    "Pegawai Awam",
    "Pegawai Harta",
    "Pentadbir Harta",
    "Pemeriksa Kira-kira",
    "Penaung",
    "Penasihat",
  ];

  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  const { id, clauseId } = useParams();

  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName, address, mailingAddress } =
          responseData?.data?.data;
        setNamaPertubuhan(societyName);
      },
    },
  });

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause = JSON.parse(clause5);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause3Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause.societyName);
      setTempohJawatan(clause.constitutionValues[0]?.definitionName);
      if (
        clause.constitutionValues[1]?.definitionName &&
        clause.constitutionValues[1]?.definitionName !== t("annual") &&
        clause.constitutionValues[1]?.definitionName !== t("biennial")
      ) {
        setPemilihanAjk(t("lainLain"));
        setLainLain(clause.constitutionValues[1]?.definitionName);
      } else {
        setPemilihanAjk(clause.constitutionValues[1]?.definitionName);
      }
      //setKekerapan(clause.constitutionValues[2]?.definitionName);
      setMinAjk(clause.constitutionValues[2]?.definitionName);
      setTempohPelucutan(clause.constitutionValues[3]?.definitionName);
      //setTempohPelucutanWaktu(clause.constitutionValues[4]?.definitionName);
      setPengerusi(clause.constitutionValues[5]?.definitionName);
      //setJumlahPengerusi(clause.constitutionValues[6]?.definitionName);
      setTimbalan(clause.constitutionValues[7]?.definitionName);
      setJumlahTimbalan(clause.constitutionValues[8]?.definitionName);
      setNaib(clause.constitutionValues[9]?.definitionName);
      setJumlahNaib(clause.constitutionValues[10]?.definitionName);
      setSetiaUsaha(clause.constitutionValues[11]?.definitionName);
      //setJumlahSetiaUsaha(clause.constitutionValues[12]?.definitionName);
      setPenolongSetiaUsaha(clause.constitutionValues[13]?.definitionName);
      setJumlahPenolongSetiaUsaha(
        clause.constitutionValues[14]?.definitionName
      );
      setBendahari(clause.constitutionValues[15]?.definitionName);
      //setJumlahBendahari(clause.constitutionValues[16]?.definitionName);
      setPenolongBendahari(clause.constitutionValues[17]?.definitionName);
      setJumlahPenolongBendahari(clause.constitutionValues[18]?.definitionName);
      setAhliBiasa(clause.constitutionValues[19]?.definitionName);
      setJumlahAhliBiasa(clause.constitutionValues[20]?.definitionName);
      //setMinAjk(clause.constitutionValues[21]?.definitionName);
      setIsEdit(clause.edit);
      setAsal(
        asalData.find(
          (item: any) => item.clauseContentId === clause.clauseContentId
        ) || ""
      );
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<tempoh pelantikan jawatankuasa>>/gi,
    `<b>${tempohJawatan || "<<tempoh pelantikan jawatankuasa>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${
      lainlain ? lainlain : pemilihanAjk || "<<jenis mesyuarat agung>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kekerapan mesyuarat>>/gi,
    `<b>${kekerapan || "<<kekerapan mesyuarat>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh membela diri pelucutan>>/gi,
    `<b>${tempohPelucutan || "<<tempoh membela diri pelucutan>>"}</b>`
  );

  clauseContent = clauseContent.replaceAll(
    /<<jawatan Pengerusi>>/gi,
    `<b>${pengerusi || "<<jawatan Pengerusi>>"}</b>`
  );

  if (Number(jumlahTimbalan) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Timbalan Pengerusi>>/gi,
      `<b>${timbalan || "<<jawatan Timbalan Pengerusi>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan Timbalan Pengerusi>>/gi,
      `<b>${
        Number(jumlahTimbalan) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahTimbalan} orang` || "<<bilangan Timbalan Pengerusi>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*ii\.\s*<<bilangan Timbalan Pengerusi>> <<jawatan Timbalan Pengerusi>>\s*[\r\n\t]?/gim,
      "    "
    );
  }

  if (Number(jumlahNaib) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Naib Pengerusi>>/gi,
      `<b>${naib || "<<jawatan Naib Pengerusi>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan Naib Pengerusi>>/gi,
      `<b>${
        Number(jumlahNaib) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahNaib} orang` || "<<bilangan Naib Pengerusi>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*iii\.\s*<<bilangan Naib Pengerusi>> <<jawatan Naib Pengerusi>>\s*[\r\n\t]?/gim,
      "    "
    );
  }

  clauseContent = clauseContent.replaceAll(
    /<<jawatan Bendahari Agung>>/gi,
    `<b>${bendahari || "<<jawatan Bendahari Agung>>"}</b>`
  );

  if (Number(jumlahPenolongSetiaUsaha) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Penolong Setiausaha>>/gi,
      `<b>${penolongSetiaUsaha || "<<jawatan Penolong Setiausaha>>"}</b>`
    );

    clauseContent = clauseContent.replaceAll(
      /<<bilangan pen. SU>>/gi,
      `<b>${
        Number(jumlahPenolongSetiaUsaha) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahPenolongSetiaUsaha} orang` || "<<bilangan pen. SU>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*v\.\s*<<bilangan pen. SU>> <<jawatan Penolong Setiausaha>>\s*[\r\n]?/gim,
      "    "
    );
  }

  if (Number(jumlahPenolongBendahari) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Penolong Bendahari>>/gi,
      `<b>${penolongBendahari || "<<jawatan Penolong Bendahari>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan pen. bendahari>>/gi,
      `<b>${
        Number(jumlahPenolongBendahari) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahPenolongBendahari} orang` || "<<bilangan pen. bendahari>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*vii\.\s*<<bilangan pen. bendahari>> <<jawatan Penolong Bendahari>>\s*[\r\n]?/gim,
      "    "
    );
  }

  clauseContent = clauseContent.replaceAll(
    /<<jawatan Setiausaha Agung>>/gi,
    `<b>${setiaUsaha || "<<jawatan Setiausaha Agung>>"}</b>`
  );

  if (Number(jumlahAhliBiasa) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Ahli Jawatankuasa Biasa>>/gi,
      `<b>${ahliBiasa || "<<jawatan Ahli Jawatankuasa Biasa>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan ajk>>/gi,
      `<b>${
        Number(jumlahAhliBiasa) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahAhliBiasa} orang` || "<<bilangan ajk>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*viii\.\s*<<bilangan ajk>> <<jawatan Ahli Jawatankuasa Biasa>>\s*[\r\n]?/gim,
      "\n\n"
    );
  }
  clauseContent = clauseContent.replaceAll(
    /<<bilangan orang ahli jawatankuasa untuk panggilan mesyuarat>>/gi,
    `<b>${
      minAjk || "<<bilangan orang ahli jawatankuasa untuk panggilan mesyuarat>>"
    }</b>`
  );

  const toRoman = (num:number) => {
    // handle 0 or negative number 
    if (num < 1) return num.toString();  
    const romanNumerals = [
      { value: 1000, symbol: 'm' },
      { value: 900, symbol: 'cm' },
      { value: 500, symbol: 'd' },
      { value: 400, symbol: 'cd' },
      { value: 100, symbol: 'c' },
      { value: 90, symbol: 'xc' },
      { value: 50, symbol: 'l' },
      { value: 40, symbol: 'xl' },
      { value: 10, symbol: 'x' },
      { value: 9, symbol: 'ix' },
      { value: 5, symbol: 'v' },
      { value: 4, symbol: 'iv' },
      { value: 1, symbol: 'i' }
    ];
  
    let result = '';
    for (const { value, symbol } of romanNumerals) {
      while (num >= value) {
        result += symbol;
        num -= value;
      }
    }
    return result;
  };

  //start,,,,handle existing custom jawatan
  if (existingCustomJawatanAhli && existingCustomJawatanAhli.length > 0) {
    
    //create placeholder for existing custom jawatan
    let replacementText = "";
    existingCustomJawatanAhli.forEach((data, index,array) => {
      const isLast = index === array.length - 1;  
      const romanNum = toRoman(9 + index); 
      const aHliDataMatchid = data.matchId;
      const getBilanganDataById = existingCustomJawatanBilangan.filter(
        (data: any) => data.matchId === aHliDataMatchid
      )?.[0];
      //hide if bilangan = 0
      if (Number(getBilanganDataById.definitionName) >= 1) {
        if (index > 0 && isLast) { 
          //handle last spacing
          replacementText += `    ${romanNum}. <<bilangan ${data.definitionName}>>  <<${data.definitionName}>>`;
        } else if(index > 0 ){
          //handle front spacing
          replacementText += `    ${romanNum}. <<bilangan ${data.definitionName}>>  <<${data.definitionName}>>\n`; 
        } else {
          replacementText += `${romanNum}. <<bilangan ${data.definitionName}>> <<${data.definitionName}>>\n`;
        }
      } else {
        replacementText += ``;
      }
    });

   
    clauseContent = clauseContent.replace(
    /<<jawatan telah diubahsuai>>\s*/gi,
    replacementText  
    );

    existingCustomJawatanAhli.forEach((data, index) => {
      const aHliDataMatchid = data.matchId;
      const getBilanganDataById = existingCustomJawatanBilangan.filter(
        (data: any) => data.matchId === aHliDataMatchid
      )?.[0];
      const regexPatternForTitle = `<<${data.definitionName}>>`;
      const regexTitle = new RegExp(regexPatternForTitle, "gi");
      //hide if bilangan = 0
      clauseContent = clauseContent.replaceAll(
        regexTitle,
        `<b>${data.definitionName || `<<${data.definitionName}>>`}</b>`
      );

      const regexPatternForBilangan = `<<bilangan ${data.definitionName}>>`;
      const regexBilangan = new RegExp(regexPatternForBilangan, "gi");

      clauseContent = clauseContent.replaceAll(
        regexBilangan,
        `<b>${
          Number(getBilanganDataById.definitionName) === 1
            ? capitalizeWords(t("seorang"))
            : `${getBilanganDataById.definitionName} orang` ||
              `<<bilangan ${data.definitionName}>>`
        }</b>`
      );
    });
  
  } else {
     clauseContent = clauseContent.replace(
      /<<jawatan telah diubahsuai>>\s*/gi,
      ''
    );
  }
  //end,,,,handle existing custom jawatan
  
  //=======================================
  
  //start,,,,handle add custom jawatan
  if (numberAddAhli && numberAddAhli.length > 0) {  
    //create placeholder for existing custom jawatan
    let replacementText = "";
    numberAddAhli.forEach((data, index) => {
      const romanNum = toRoman(9 + existingCustomJawatanAhli.length + index);  
      const aHliDataMatchid = data.tempId;
      const getBilanganDataById = numberAddAhliBilangan.filter(
        (data: any) => data.tempId === aHliDataMatchid
      )?.[0];
      //hide if bilangan = 0
       if (Number(getBilanganDataById.definitionName) >= 1) {
        if (index > 0) {
          //handle front spacing
          replacementText += `    ${romanNum}. <<bilangan ${data.definitionName}>>  <<${data.definitionName}>>\n`;
        }else if(existingCustomJawatanAhli && existingCustomJawatanAhli.length>0){
          replacementText += `\n\t${romanNum}. <<bilangan ${data.definitionName}>> <<${data.definitionName}>>\n`;
        } else {
          replacementText += `${romanNum}. <<bilangan ${data.definitionName}>> <<${data.definitionName}>>\n`;
        }
      } else {
        replacementText += ``;
      }
    });

 
    clauseContent = clauseContent.replace(
      /<<penambahan jawatan diubahsuai>>\s*/gi,
      '' + replacementText + `\n` 
    );

    numberAddAhli.forEach((data, index) => {
      const aHliDataMatchid = data.tempId;
      const getBilanganDataById = numberAddAhliBilangan.filter(
        (data: any) => data.tempId === aHliDataMatchid
      )?.[0];
      const regexPatternForTitle = `<<${data.definitionName}>>`;
      const regexTitle = new RegExp(regexPatternForTitle, "gi");
      //hide if bilangan = 0
      clauseContent = clauseContent.replaceAll(
        regexTitle,
        `<b>${data.definitionName || `<<${data.definitionName}>>`}</b>`
      );

      const regexPatternForBilangan = `<<bilangan ${data.definitionName}>>`;
      const regexBilangan = new RegExp(regexPatternForBilangan, "gi");

      clauseContent = clauseContent.replaceAll(
        regexBilangan,
        `<b>${
          Number(getBilanganDataById.definitionName) === 1
            ? capitalizeWords(t("seorang"))
            : `${getBilanganDataById.definitionName} orang` ||
              `<<bilangan ${data.definitionName}>>`
        }</b>`
      );
    });

    // clauseContent = clauseContent.replace(
    //   "<<penambahan jawatan diubahsuai>>",
    //   ""
    // );
  }else{
    clauseContent = clauseContent.replace(
      /\s*<<penambahan jawatan diubahsuai>>/gi, 
        ``
    );
  }
  //end,,,,handle add custom jawatan

  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  clauseContent = renumberRomanList(clauseContent);

  function renumberRomanList(content: any) {
    const romanNumerals = [
      "i",
      "ii",
      "iii",
      "iv",
      "v",
      "vi",
      "vii",
      "viii",
      "ix",
      "x",
    ];
    let index = 0;

    return content.replace(
      /^(\s*)(?:i{1,3}|iv|v?i{0,3}|ix|x)\.\s/gim,
      (_: any, indent: any) => {
        const roman = romanNumerals[index++] || `${index}.`;
        return `${indent}${roman}. `;
      }
    );
  }

  const totalBilangan =
    parseInt(jumlahPengerusi || 0) +
    parseInt(jumlahTimbalan || 0) +
    parseInt(jumlahNaib || 0) +
    parseInt(jumlahSetiaUsaha || 0) +
    parseInt(jumlahPenolongSetiaUsaha || 0) +
    parseInt(jumlahBendahari || 0) +
    parseInt(jumlahPenolongBendahari || 0) +
    parseInt(jumlahAhliBiasa || 0);

  const totalExistingCustomeJawatan = existingCustomJawatanBilangan.reduce(
    (sum, item) => {
      return sum + Number(item.definitionName);
    },
    0
  );

  const totalCustomeJawatan = numberAddAhliBilangan.reduce((sum, item) => {
    return sum + Number(item.definitionName);
  }, 0);

  const addAhliJawatanHandle = () => {
    const uniqueId = Math.random().toString(36).substring(2, 15);
    setNumberAddAhliBilangan([
      ...numberAddAhliBilangan,
      {
        tempId: uniqueId,
        constitutionContentId: null,
        societyName: namaPertubuhan,
        definitionName: "",
        titleName: "",
      },
    ]);

    setNumberAddAhli([
      ...numberAddAhli,
      {
        tempId: uniqueId,
        constitutionContentId: null,
        societyName: namaPertubuhan,
        definitionName: "",
        titleName: "",
        customJawatan: true,
      },
    ]);
  };

  const {
    data: customJawatanData,
    isLoading: customJawatanListIsLoading,
    refetch: refetchCustomJawatan,
  } = useCustom({
    url: `${API_URL}/society/constitutionvalue/callCustomJawatan`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: id,
        constitutionContentId: dataId,
        amendmentId: amendmentId,
      },
    },
    queryOptions: {
      enabled: !!amendmentId && !!dataId,
      cacheTime: 0,
      onSuccess: (data) => {
        const responseData = data?.data?.data || [];

        if (responseData && responseData.length > 0) {
          const newAhliBilanganItems = responseData.map(
            (data: any, index: number) => ({
              id: data?.countId,
              constitutionContentId: null,
              matchId: `${index + data?.id}`,
              societyName: namaPertubuhan,
              definitionName: data.countDefinitionName || "",
              titleName: data.countTitleName || "",
              amendmentId: data?.amendmentId,
            })
          );

          const newAhliItems = responseData.map((data: any, index: number) => ({
            id: data?.id,
            constitutionContentId: null,
            matchId: `${index + data?.id}`,
            societyName: namaPertubuhan,
            definitionName: data.definitionName || "",
            titleName: data.definitionName || "",
            customJawatan: true,
            amendmentId: data?.amendmentId,
          }));

          setExistingCustomJawatanBilangan((prev) => [
            ...prev,
            ...newAhliBilanganItems,
          ]);
          setExistingCustomJawatanAhli((prev) => [...prev, ...newAhliItems]);
        }
      },
    },
  });

  const { mutate: deleteCustomJawatan, isLoading: isDeleteCustomJawatan } =
    useCustomMutation();

  const deleteCustomJawatanFunc = () => {
    deleteCustomJawatan({
      url: `${API_URL}/society/constitutionvalue/deleteCustomJawatan?id=${selectedDeleteId}`,
      method: "delete",
      values: {},
      config: {
        headers: {
          "Content-Type": "application/json",
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
      successNotification: (data: any) => {
        setExistingCustomJawatanAhli(
          existingCustomJawatanAhli.filter(
            (items: any) => items.matchId !== selectedMatchId
          )
        );
        setExistingCustomJawatanBilangan(
          existingCustomJawatanBilangan.filter(
            (items: any) => items.matchId !== selectedMatchId
          )
        );
        setSelectedDeleteId(null);
        setSelectedMatchId(null);
        setShowConfirmDeleteDialog(false);
        return {
          message: data?.data?.msg,
          type: "success",
        };
      },
    });
  };

  const removeExistingAhliJawatanHandle = (
    matchId: number | string,
    id: number
  ) => {
    if (!id || !matchId) {
      return;
    }
    setSelectedDeleteId(id);
    setSelectedMatchId(matchId);
    setShowConfirmDeleteDialog(true);
  };

  const removeAhliJawatanHandle = (tempId: number) => {
    if (!tempId) {
      return;
    }

    setNumberAddAhliBilangan(
      numberAddAhliBilangan.filter((items: any) => items.tempId !== tempId)
    );
    setNumberAddAhli(
      numberAddAhli.filter((items: any) => items.tempId !== tempId)
    );
  };

  const handleOnchangeJawatanTitleChange = (
    tempId: number,
    field: keyof addJawatanTitleProps,
    value: string
  ) => {
    const normalizedInput = value.toLowerCase().trim();
    const isRestricted = restrictedTitles.some((term) =>
      normalizedInput.includes(term.toLowerCase())
    );

    if (isRestricted) {
      setShowIsRestrictedWord(true);
      return;
    }

    setNumberAddAhli(
      numberAddAhli.map((items: any) =>
        items.tempId === tempId
          ? { ...items, definitionName: value, titleName: value }
          : items
      )
    );
    setNumberAddAhliBilangan(
      numberAddAhliBilangan.map((items: any) =>
        items.tempId === tempId
          ? { ...items, titleName: `Bilangan ${value}` }
          : items
      )
    );
  };

  const handleOnchangeJawatanBilanganChange = (
    tempId: number,
    field: keyof addJawatanTitleProps,
    value: string
  ) => {
    let title = "";
    const findAhliId = numberAddAhli.filter(
      (items) => tempId?.toString() === items?.tempId?.toString()
    );
    if (findAhliId && findAhliId.length > 0) {
      title = findAhliId?.[0]?.definitionName;
    }
    setNumberAddAhliBilangan(
      numberAddAhliBilangan.map((items: any) =>
        items.tempId === tempId
          ? { ...items, definitionName: value, titleName: `Bilangan ${title}` }
          : items
      )
    );
  };

  // ======================================================

  const handleOnchangeExistingJawatanTitleChange = (
    id: number,
    field: keyof addJawatanTitleProps,
    value: string
  ) => {
    const normalizedInput = value.toLowerCase().trim();
    const isRestricted = restrictedTitles.some((term) =>
      normalizedInput.includes(term.toLowerCase())
    );

    if (isRestricted) {
      setShowIsRestrictedWord(true);
      return;
    }

    setExistingCustomJawatanAhli(
      existingCustomJawatanAhli.map((items: any) =>
        items.matchId === id
          ? { ...items, definitionName: value, titleName: value }
          : items
      )
    );
    setExistingCustomJawatanBilangan(
      existingCustomJawatanBilangan.map((items: any) =>
        items.matchId === id
          ? { ...items, titleName: `Bilangan ${value}` }
          : items
      )
    );
  };

  const handleOnchangeExistingJawatanBilanganChange = (
    id: number,
    field: keyof addJawatanTitleProps,
    value: string
  ) => {
    let title = "";
    const findAhliId = existingCustomJawatanAhli.filter(
      (items) => items?.matchId?.toString() === id.toString()
    );
    if (findAhliId && findAhliId.length > 0) {
      title = findAhliId?.[0]?.definitionName;
    }
    setExistingCustomJawatanBilangan(
      existingCustomJawatanBilangan.map((items: any) =>
        items.matchId === id
          ? { ...items, definitionName: value, titleName: `Bilangan ${title}` }
          : items
      )
    );
  };

  return (
    <>
      <AsalContent
        asalData={asal?.description || ""}
        clauseId={clauseId}
        name={name}
      />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("positionOfAuthority")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("jenisMesyuaratAgung")}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth required>
              <Select
                size="small"
                disabled={isViewMode}
                value={pemilihanAjk}
                displayEmpty
                onChange={(e) => {
                  setPemilihanAjk(e.target.value as string);
                  if ((e.target.value as string) == t("annual")) {
                    setLainLain("");
                    setTempohJawatan(t("setahun"));
                  } else if ((e.target.value as string) == t("biennial")) {
                    setLainLain("");
                    setTempohJawatan(t("duaTahun"));
                  }
                }}
              >
                <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
                <MenuItem value={t("lainLain")}>{t("lainLain")}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
        {pemilihanAjk === t("lainLain") ? (
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} md={4}></Grid>
            <Grid item xs={12} md={4}>
              <TextField
                type="text"
                size="small"
                placeholder="Yearly, Biannually, Tri-tahunan"
                fullWidth
                required
                value={lainlain}
                onChange={(e) => {
                  setLainLain(e.target.value as string);
                }}
              />
            </Grid>
          </Grid>
        ) : null}
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("electionPeriod")}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth required>
              <Select
                size="small"
                disabled={isViewMode}
                value={tempohJawatan}
                displayEmpty
                onChange={(e) => setTempohJawatan(e.target.value as string)}
              >
                <MenuItem value={t("setahun")}>{t("setahun")}</MenuItem>
                <MenuItem value={t("duaTahun")}>{t("duaTahun")}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("minAjkMeeting")}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              type="number"
              disabled={isViewMode}
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="4"
              fullWidth
              required
              value={minAjk}
              onChange={(e) => setMinAjk(e.target.value)}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item />
        </Grid>
        {/*<Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("meetingFrequency")}</Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="4"
              fullWidth
              required
              value={kekerapan}
              onChange={(e) => setKekerapan(e.target.value)}
              InputProps={{
                endAdornment: (
                  <Typography sx={{ ...labelStyle, mt: 1 }}>
                    {t("times")}
                  </Typography>
                ),
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item />
        </Grid>*/}
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("periodOfDefendingOneselfTwo")}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              disabled={isViewMode}
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              value={tempohPelucutan}
              onChange={(e) => setTempohPelucutan(e.target.value)}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth required>
              <Select
                size="small"
                disabled={isViewMode}
                value={tempohPelucutanWaktu}
                displayEmpty
                onChange={(e) =>
                  setTempohPelucutanWaktu(e.target.value as string)
                }
                readOnly={true}
              >
                <MenuItem value={t("day")}>{t("day")}</MenuItem>
                <MenuItem value={t("week")}>{t("week")}</MenuItem>
                <MenuItem value={t("month")}>{t("month")}</MenuItem>
                <MenuItem value={t("year")}>{t("year")}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("ahliJawatanKuasa")}
        </Typography>
        <Grid
          container
          spacing={2}
          sx={{ mt: 0, display: "flex", justifyContent: "flex-end" }}
        >
          <Grid
            item
            xs={12}
            md={6}
            gap={1}
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              alignItems: "center",
            }}
          >
            <Typography variant="subtitle1" sx={{ ...sectionStyle, mb: 0 }}>
              {t("NumberofPositions")}
            </Typography>
            <ButtonOutline onClick={addAhliJawatanHandle}>
              Tambah Ahli Jawatankuasa
            </ButtonOutline>
          </Grid>
          <Grid item xs={12} md={4} />
        </Grid>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("NumberofPositions")}
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("JobTitle")}
            </Typography>
          </Grid>
        </Grid>
        <Grid container spacing={2}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              disabled
              sx={{ background: "#E8E9E8" }}
              required
              value={jumlahPengerusi}
              onChange={(e) => setJumlahPengerusi(e.target.value)}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required>
              <Select
                size="small"
                value={pengerusi}
                disabled={isViewMode}
                displayEmpty
                onChange={(e) => setPengerusi(e.target.value as string)}
              >
                <MenuItem value={t("chairman")}>
                  {t("chairman")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                <MenuItem value={t("presiden")}>
                  {t("presiden")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                <MenuItem value={t("pengarah")}>
                  {t("pengarah")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              disabled={isViewMode}
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              value={jumlahTimbalan}
              onChange={(e) => setJumlahTimbalan(e.target.value)}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required>
              <Select
                size="small"
                disabled={isViewMode}
                value={timbalan}
                displayEmpty
                onChange={(e) => setTimbalan(e.target.value as string)}
              >
                <MenuItem value={t("timbalanPengerusi")}>
                  {t("timbalanPengerusi")}
                </MenuItem>
                <MenuItem value={t("vicePresident")}>
                  {t("vicePresident")}
                </MenuItem>
                <MenuItem value={t("timbalanPengarah")}>
                  {t("timbalanPengarah")}
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              disabled={isViewMode}
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              value={jumlahNaib}
              onChange={(e) => setJumlahNaib(e.target.value)}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required>
              <Select
                size="small"
                disabled={isViewMode}
                value={naib}
                displayEmpty
                onChange={(e) => setNaib(e.target.value as string)}
              >
                <MenuItem value={t("naibPengerusi")}>
                  {t("naibPengerusi")}
                </MenuItem>
                <MenuItem value={t("naibPresiden")}>
                  {t("naibPresiden")}
                </MenuItem>
                <MenuItem value={t("naibPengarah")}>
                  {t("naibPengarah")}
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              disabled
              sx={{ background: "#E8E9E8" }}
              value={jumlahSetiaUsaha}
              onChange={(e) => setJumlahSetiaUsaha(e.target.value)}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required>
              <Select
                disabled={isViewMode}
                size="small"
                value={setiaUsaha}
                displayEmpty
                onChange={(e) => setSetiaUsaha(e.target.value as string)}
              >
                <MenuItem value={t("secretary")}>
                  {t("secretary")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                <MenuItem value={t("generalSecretary")}>
                  {t("generalSecretary")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                <MenuItem value={t("branchSecretary")}>
                  {t("branchSecretary")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              disabled={isViewMode}
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              value={jumlahPenolongSetiaUsaha}
              onChange={(e) => setJumlahPenolongSetiaUsaha(e.target.value)}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required>
              <Select
                size="small"
                disabled={isViewMode}
                value={penolongSetiaUsaha}
                displayEmpty
                onChange={(e) =>
                  setPenolongSetiaUsaha(e.target.value as string)
                }
              >
                <MenuItem value={t("asistantSecretary")}>
                  {t("asistantSecretary")}
                </MenuItem>
                <MenuItem value={t("generalAssistantSecretary")}>
                  {t("generalAssistantSecretary")}
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              disabled
              sx={{ background: "#E8E9E8" }}
              value={jumlahBendahari}
              onChange={(e) => setJumlahBendahari(e.target.value)}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required>
              <Select
                size="small"
                value={bendahari}
                displayEmpty
                onChange={(e) => setBendahari(e.target.value as string)}
              >
                <MenuItem value={t("treasurer")}>
                  {t("treasurer")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                <MenuItem value={t("chiefTreasurer")}>
                  {t("chiefTreasurer")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem>
                {/* <MenuItem value={t("honoraryTreasurer")}>
                  {t("honoraryTreasurer")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </MenuItem> */}
              </Select>
            </FormControl>
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              disabled={isViewMode}
              type="number"
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              value={jumlahPenolongBendahari}
              onChange={(e) => setJumlahPenolongBendahari(e.target.value)}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required>
              <Select
                size="small"
                disabled={isViewMode}
                value={penolongBendahari}
                displayEmpty
                onChange={(e) => setPenolongBendahari(e.target.value as string)}
              >
                <MenuItem value={t("asistantTreasurer")}>
                  {t("asistantTreasurer")}
                </MenuItem>
                {/* <MenuItem value={t("honoraryAssistantTreasurer")}>
                  {t("honoraryAssistantTreasurer")}
                </MenuItem> */}
              </Select>
            </FormControl>
          </Grid>

          <Grid item />
        </Grid>

        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={2}>
            <TextField
              type="number"
              disabled={isViewMode}
              onKeyDown={(e) => {
                if (
                  e.key.toLowerCase() === "e" ||
                  e.key === "E" ||
                  e.key === "+" ||
                  e.key === "-"
                ) {
                  e.preventDefault();
                }
              }}
              size="small"
              placeholder="0"
              fullWidth
              required
              value={jumlahAhliBiasa}
              onChange={(e) => setJumlahAhliBiasa(e.target.value)}
              InputProps={{
                inputProps: {
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  min: 0,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required>
              <Select
                size="small"
                value={ahliBiasa}
                disabled={isViewMode}
                displayEmpty
                onChange={(e) => setAhliBiasa(e.target.value as string)}
              >
                <MenuItem value={t("ordinaryCommitteeMember")}>
                  {t("ordinaryCommitteeMember")}
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item />
        </Grid>
        {existingCustomJawatanAhli && existingCustomJawatanAhli.length > 0
          ? existingCustomJawatanAhli.map((data: any, index) => {
              return (
                <ExistingAhliJawatanComponent
                  key={index}
                  allBilanganData={existingCustomJawatanBilangan}
                  ahliData={data}
                  removeHandler={removeExistingAhliJawatanHandle}
                  onChangeTitleHandler={
                    handleOnchangeExistingJawatanTitleChange
                  }
                  onChangeBilanganHandler={
                    handleOnchangeExistingJawatanBilanganChange
                  }
                />
              );
            })
          : null}
        {/* add ahli jawantan */}
        {numberAddAhli && numberAddAhli.length > 0
          ? numberAddAhli.map((data: any, index) => {
              return (
                <AddAhliJawatanComponent
                  key={`${index}${data.tempId}`}
                  allBilanganData={numberAddAhliBilangan}
                  data={data}
                  removeHandler={removeAhliJawatanHandle}
                  onChangeTitleHandler={handleOnchangeJawatanTitleChange}
                  onChangeBilanganHandler={handleOnchangeJawatanBilanganChange}
                />
              );
            })
          : null}
        {/*  */}
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} md={8}>
            <Typography
              sx={{
                fontWeight: "500 !important",
                color: "#666666",
                border: "1px solid #DADADA",
                borderRadius: "5px",
                py: 1,
                display: "flex",
                justifyContent: "center",
              }}
            >
              {totalBilangan +
                totalCustomeJawatan +
                totalExistingCustomeJawatan}{" "}
              Bilangan Ahli Jawatankuasa
            </Typography>
          </Grid>
          <Grid item />
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              if (totalBilangan < 7) {
                setShowErrorAjk(true);
                return;
              }
              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tempohJawatan,
                    titleName: "Tempoh Pelantikan Jawatankuasa",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: lainlain ? lainlain : pemilihanAjk,
                    titleName: "Jenis Mesyuarat Agung",
                  },
                  /*{
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: kekerapan,
                  titleName: "Kekerapan Mesyuarat Jawatankuasa",
                },*/
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: minAjk,
                    titleName: "Bilangan Ahli Mesyuarat",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tempohPelucutan,
                    titleName:
                      "Bilangan tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tempohPelucutanWaktu,
                    titleName:
                      "Tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: pengerusi,
                    titleName: "Pengerusi",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahPengerusi,
                    titleName: "Bilangan Pengerusi",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: timbalan,
                    titleName: "Timbalan Pengerusi",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahTimbalan,
                    titleName: "Bilangan Timbalan Pengerusi",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: naib,
                    titleName: "Naib Pengerusi",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahNaib,
                    titleName: "Bilangan Naib Pengerusi",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: setiaUsaha,
                    titleName: "Setiausaha",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahSetiaUsaha,
                    titleName: "Bilangan Setiausaha",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: penolongSetiaUsaha,
                    titleName: "Penolong Setiausaha",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahPenolongSetiaUsaha,
                    titleName: "Bilangan Penolong Setiausaha",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: bendahari,
                    titleName: "Bendahari",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahBendahari,
                    titleName: "Bilangan Bendahari",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: penolongBendahari,
                    titleName: "Penolong Bendahari",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahPenolongBendahari,
                    titleName: "Bilangan Penolong Bendahari",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: ahliBiasa,
                    titleName: "Ahli Jawatankuasa Biasa",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: jumlahAhliBiasa,
                    titleName: "Bilangan Ahli Jawatankuasa Biasa",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: minAjk,
                    titleName: "Bilangan Ahli Mesyuarat",
                  },
                  ...numberAddAhliBilangan,
                  ...numberAddAhli,
                  ...existingCustomJawatanAhli,
                  ...existingCustomJawatanBilangan,
                ],
                clause: "clause5",
                clauseCount: 5,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
      <MessageDialog
        open={showErrorAjk}
        onClose={() => setShowErrorAjk(false)}
        message={t("moreThan7ajk")}
      />
      <MessageDialog
        open={showIsRestrictedWord}
        onClose={() => setShowIsRestrictedWord(false)}
        message={"Gelaran jawatan ini tidak dibenarkan di bawah AJK"}
      />
      {/* delete comfirmation */}
      <DialogConfirmation
        open={showConfirmDeleteDialog}
        onClose={() => {
          setShowConfirmDeleteDialog(false);
          setSelectedDeleteId(null);
          setSelectedMatchId(null);
        }}
        onAction={deleteCustomJawatanFunc}
        isMutating={isDeleteCustomJawatan}
        onConfirmationText={"Adakah anda pasti untuk padam jawatan ini?"}
      />
    </>
  );
};

export default FasalContentLimaFaedah;
