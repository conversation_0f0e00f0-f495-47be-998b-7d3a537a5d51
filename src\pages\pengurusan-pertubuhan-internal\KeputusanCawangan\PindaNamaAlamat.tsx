import { <PERSON>, Grid, Icon<PERSON>utton, Typography } from "@mui/material";
import { t } from "i18next";
import React, { useState } from "react";
import Input from "../../../components/input/Input";
import { ButtonPrimary } from "../../../components/button";
import DataTable, { IColumn } from "@/components/datatable";
import { NEW_PermissionNames, pageAccessEnum, useQuery } from "@/helpers";
import { EditIcon } from "@/components/icons";
import { useNavigate } from "react-router-dom";
import ButtonPrevious from "@/components/button/ButtonPrevious";
import { FieldValues, useForm } from "react-hook-form";
import AuthHelper from "@/helpers/authHelper";

interface FormValues {
  search: string | null;
}

const PindaNamaAlamatTab = () => {
  const hasReadPermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_CAWANGAN.children.PINDAAN_NAMA_DAN_ALAMAT.label,
    pageAccessEnum.Read
  );
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "30px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const [branchList, setBranchList] = useState<any>(null);
  const navigate = useNavigate();

  const columns: IColumn[] = [
    {
      field: "societyName",
      headerName: t("pertubuhan"),
      headerAlign: "center",
      align: "center",
      flex: 1,
    },
    {
      field: "branchName",
      headerName: t("namaCawangan"),
      headerAlign: "center",
      align: "center",
      flex: 1,
    },
    {
      field: "branchNo",
      headerName: t("ppmCawangan"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return <Box> {row.branchNo ? row.branchNo : "-"}</Box>;
      },
    },
    {
      field: "roName",
      headerName: t("keputusanCawangan_RO"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      renderCell: ({ row }: any) => {
        const ro = row?.roName;
        return <Box>{ro ? ro : "-"}</Box>;
      },
    },
    {
      field: "stateName",
      headerName: t("negeri"),
      headerAlign: "center",
      align: "center",
      flex: 1,
    },
    {
      field: "paymentDate",
      headerName: t("tarikhBayar"),
      headerAlign: "center",
      align: "center",
      flex: 1,
    },
    {
      field: "actions",
      headerName: t("action"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      cellClassName: "custom-cell",
      renderCell: (params: any) => {
        const row = params.row;
        return (
          <IconButton
            onClick={() => { 
              navigate(
                `/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pindaan-nama-dan-alamat/${row?.societyId}/${row?.id}`
              );
            }}
            disabled={!hasReadPermission}
            sx={{ minWidth: 0, p: 1 }}
          >
            <EditIcon
              sx={{
                color: hasReadPermission
                  ? "var(--primary-color)"
                  : "var(text-grey-disabled)",
                width: "1rem",
                height: "1rem",
              }}
            />
          </IconButton>
        );
      },
    },
  ];

  const [formValues, setFormValues] = useState<FormValues>({
    search: null,
  });

  const [refreshKey, setRefreshKey] = useState(0);
  const [searchQuery, setSearchQuery] = useState<string | null>(null);
  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      searchQuery: undefined,
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");

  const { data, isLoading: isSearchLoading } = useQuery({
    url: `society/roDecision/getAllPending/branch/amendment`,
    autoFetch: true,
    filters: [
      { field: "searchQuery", value: searchQuery, operator: "eq" },
      {
        field: "pageSize",
        value: pageSize,
        operator: "eq",
      },
      {
        field: "pageNo",
        value: page,
        operator: "eq",
      },
    ],
    onSuccess: (data) => {
      const responseData = data?.data?.data;
      setBranchList(responseData);
      console.log("responseData", responseData);
    },
  });

  const totalList = data?.data?.data?.total ?? 0;
  const rowData = data?.data?.data?.data ?? [];

  const handleChange = (e: any) => {
    const { name, value } = e.target;

    setFormValues({
      ...formValues,
      [name!]: value as string,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const updatedFormValues = Object.fromEntries(
      Object.entries(formValues).map(([key, value]) => [
        key,
        typeof value === "string" && value.trim() === "" ? null : value,
      ])
    );
    setValue("page", 1);
    setValue("pageSize", 10);
    setSearchQuery(updatedFormValues.search);
  };

  const handleClearSearch = () => {
    setFormValues({ search: null });
    setSearchQuery(null);
    setRefreshKey((prev) => prev + 1);
  };

  return (
    <>
      <Box>
        <Box
          sx={{
            padding: "22px 16px",
            background: "#FFF",
            borderRadius: "15px",
            boxShadow: "0px 12px 12px 0px #EAE8E866",
            marginBottom: 1,
          }}
        >
          <Box
            sx={{
              width: "100%",
              border: "0.5px solid #DADADA",
              borderRadius: "10px",
              padding: "22px",
            }}
            component="form"
            onSubmit={handleSubmit}
          >
            <Input
              value={formValues.search ? formValues.search : ""}
              name="search"
              onChange={handleChange}
              label={t("carian")}
            />
            <Grid container mt={3} spacing={2}>
              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrevious
                  variant="outlined"
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                  }}
                  onClick={handleClearSearch}
                >
                  {t("previous")}
                </ButtonPrevious>
                <ButtonPrimary
                  type="submit"
                  variant="contained"
                  sx={{
                    boxShadow: "none",
                  }}
                >
                  {t("search")}
                </ButtonPrimary>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </Box>

      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            textAlign: "center",
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",
            py: 2,
          }}
        >
          <Typography variant="h5" gutterBottom>
            {branchList && branchList?.total ? branchList.total : 0}
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
            {t("permohonanMenungguKeputusan")}
          </Typography>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mt: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("senaraiMenungguKeputusanPindaanNamaDanAlamat")}
          </Typography>

          <DataTable
            key={refreshKey}
            columns={columns}
            rows={rowData}
            page={page}
            rowsPerPage={pageSize}
            totalCount={totalList}
            isLoading={isSearchLoading}
            onPageChange={(newPage) => setValue("page", newPage)}
            onPageSizeChange={(newPageSize) =>
              setValue("pageSize", newPageSize)
            }
            customNoDataText={t("noRecordForStatus")}
          />
        </Box>
      </Box>
    </>
  );
};

export default PindaNamaAlamatTab;
