import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Typography, IconButton, CircularProgress } from "@mui/material";
import { ButtonPrimary } from "../../../../components/button";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import {
  AhliList,
  COMMITTEE_TASK_TYPE,
  OrganisationPositions,
  getCitizenshipLabel,
  useQuery,
} from "@/helpers";
import { EditIcon, EyeIcon, TrashIcon } from "../../../../components/icons";
import { DataTable } from "@/components";
import ConfirmationDialog from "@/components/dialog/confirm";
import { useSelector } from "react-redux";
import { getUserPermission } from "@/redux/userReducer";
import NewAlertDialog from "@/components/dialog/newAlert";
import { getLocalStorage } from "@/helpers";
import { AppDispatch } from "@/redux/store";
import { useDispatch } from "react-redux";
import { fetchIsManagerBySocietyId } from "@/redux/APIcalls/isManagerBySocietyThunks";

const AhliPertubuhan: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id: societyId } = useParams();
  const [pageSize, setPageSize] = useState(5);
  const [page, setPage] = useState(1);
  const [openConfirmDelete, setOpenConfirmDelete] = useState<boolean>(false);
  const [memberId, setMemberId] = useState<string>("");
  const [dialogAlertSaveOpen, setDialogAlertSaveOpen] = useState(false);
  const [isAliranModuleAccess, setIsAliranModuleAccess] = useState(false);
  const [isBlackListed, setIsBlackListed] = useState(false);

  const {
    data: societyData,
    refetch: fetchSociety,
    isLoading: fetchSocietyIsLoading,
  } = useQuery({
    url: `society/${societyId}`,
    onSuccess: (data) => {
      setIsBlackListed(data?.data?.data?.subStatusCode === "003" || false);
    },
  });

  const {
    refetch: fetchAliranTugasAccess,
    isLoading: fetchAliranTugasAccessIsLoading,
  } = useQuery({
    url: `society/committee-task/me`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      {
        field: "module",
        operator: "eq",
        value: COMMITTEE_TASK_TYPE.PENGURUSAN_AJK,
      },
    ],
    onSuccess: (data) => {
      const responseData = data?.data?.data?.enabled;
      setIsAliranModuleAccess(responseData);
    },
  });

  const dispatch: AppDispatch = useDispatch();

  useEffect(() => {
    if (societyId) {
      dispatch(fetchIsManagerBySocietyId({ id: societyId }));
    }
  }, [societyId]);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  console.log("societyData?.data?.data?.data", societyData?.data?.data);
  const {
    data,
    isLoading: isFetchAhli,
    refetch: fetchAhli,
  } = useQuery({
    url: `society/committee/listAjk`,
    filters: [
      { field: "pageNo", operator: "eq", value: page },
      { field: "pageSize", operator: "eq", value: pageSize },
      { field: "societyId", operator: "eq" as const, value: societyId },
      {
        field: "status",
        operator: "eq" as const,
        value:
          societyData?.data?.data?.applicationStatusCode === 2 ? "008" : "001",
      },
      { field: "listAll", operator: "eq" as const, value: 0 },
    ],
    autoFetch: true,
    queryOptions: {
      retry: false,
      cacheTime: 0,
    },
  });

  const { mutate: deleteMember, isLoading } = useCustomMutation();

  const clickDeleteMebmer = (memberId: string) => {
    setMemberId(memberId);
    setOpenConfirmDelete(true);
  };

  const handleDeleteMember = () => {
    deleteMember(
      {
        url: `${API_URL}/society/${societyId}/members/${memberId}/delete`,
        method: "put",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          setOpenConfirmDelete(false);
          setDialogAlertSaveOpen(true);
          fetchAhli();
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const isManager = useSelector(getUserPermission);

  useEffect(() => {
    fetchAhli();
  }, [page, pageSize]);

  const userDetail = getLocalStorage("user-details", {});

  function similarId(identificationNo: any) {
    if (userDetail?.identificationNo === identificationNo) {
      return true;
    } else {
      return false;
    }
  }

  const isAccessible = !isBlackListed && (isManager || isAliranModuleAccess);
  const apiIsLoading =
    fetchSocietyIsLoading || fetchAliranTugasAccessIsLoading || isFetchAhli;

  const columns = [
    {
      field: "bil",
      headerName: t("Bil."),
      width: 80,
      headerAlign: "center",
      align: "center",
      renderCell: (params: any) => {
        return <Box>{`${params.rowIndex + 1}.`}</Box>;
      },
      cellClassName: "custom-cell",
    },
    {
      flex: 1,
      field: "name",
      headerName: t("name"),
      headerAlign: "left",
      renderCell: (params: any) => {
        return <Box>{params.row.name ?? "-"}</Box>;
      },
      cellClassName: "custom-cell",
    },
    {
      flex: 1,
      field: "nationalityStatus",
      headerName: t("citizen"),
      headerAlign: "left",
      cellClassName: "custom-cell",
      renderCell: (params: any) => {
        return t(getCitizenshipLabel(Number(params.row.nationalityStatus)));
      },
    },
    {
      flex: 1,
      field: "position",
      headerName: t("position"),
      headerAlign: "left",
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box>
            {t(
              OrganisationPositions.find(
                (item) => item.value === Number(row.designationCode)
              )?.label || "-"
            )}
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      flex: 1.5,
      field: "actions",
      headerName: "",
      align: "right",
      renderCell: (params: any) => {
        const row = params?.row;
        return isAccessible ? (
          <>
            <IconButton
              size="small"
              sx={{ color: "#55556D" }}
              onClick={() =>
                navigate(`edit/${row.identificationNo}`, {
                  state: {
                    row: row,
                    isManager: isManager,
                    isAliranModuleAccess: isAliranModuleAccess,
                    isEdit: true,
                  },
                })
              }
            >
              <EditIcon
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "1rem",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>
            {AhliList.includes(Number(row?.designationCode)) ? (
              <IconButton
                size="small"
                sx={{ color: "#FF6B6B" }}
                onClick={() => clickDeleteMebmer(row.identificationNo)}
              >
                <TrashIcon
                  sx={{
                    fontSize: "1rem",
                    width: "1rem",
                    height: "1rem",
                  }}
                />
              </IconButton>
            ) : null}
          </>
        ) : similarId(row?.identificationNo) ? (
          <>
            {isAccessible && isManager ? (
              <IconButton
                size="small"
                sx={{ color: "#55556D" }}
                onClick={() =>
                  navigate(`edit/${row.identificationNo}`, {
                    state: {
                      row: row,
                      isManager: isManager,
                      isAliranModuleAccess: isAliranModuleAccess,
                      isEdit: true,
                    },
                  })
                }
              >
                <EditIcon
                  sx={{
                    color: "var(--primary-color)",
                    fontSize: "1rem",
                    width: "1rem",
                    height: "1rem",
                  }}
                />
              </IconButton>
            ) : null}

            <IconButton
              size="small"
              sx={{ color: "#55556D" }}
              onClick={() =>
                navigate(`edit/${row.identificationNo}`, {
                  state: {
                    row: row,
                    isManager: isManager,
                    isAliranModuleAccess: isAliranModuleAccess,
                    isEdit: false,
                  },
                })
              }
            >
              <EyeIcon
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "1rem",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>
          </>
        ) : (
          <>
            <IconButton
              size="small"
              sx={{ color: "#55556D" }}
              onClick={() =>
                navigate(`edit/${row.identificationNo}`, {
                  state: {
                    row: row,
                    isManager: isManager,
                    isAliranModuleAccess: isAliranModuleAccess,
                    isEdit: false,
                  },
                })
              }
            >
              <EyeIcon
                sx={{
                  color: "var(--primary-color)",
                  fontSize: "1rem",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>
          </>
        );
      },
      cellClassName: "custom-cell",
    },
  ];

  if (apiIsLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "300px",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            gap: 3,
            mb: 2,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 22,
              fontWeight: "700 !important",
            }}
          >
            {data?.data?.data?.total ?? 0}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            {t("jumlahAhliPertubuhan")}
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("senaraiAhliPertubuhan")}
          </Typography>

          <DataTable
            columns={columns as any[]}
            rows={data?.data?.data?.data || []}
            page={page}
            rowsPerPage={pageSize}
            totalCount={data?.data?.data?.total}
            onPageChange={(newPage: number) => setPage(newPage)}
            onPageSizeChange={(newRowsPerPage: number) =>
              setPageSize(newRowsPerPage)
            }
            isLoading={isFetchAhli}
          />
        </Box>
      </Box>
      {isAccessible ? (
        <>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("memberRegister")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  sx={{
                    color: "#666666",
                    fontWeight: "500!important",
                    fontSize: 14,
                  }}
                >
                  {t("memberRegister")}
                </Typography>

                <ButtonPrimary
                  onClick={() => {
                    navigate(`daftar`, {
                      state: {
                        isAliranModuleAccess: isAliranModuleAccess,
                        isManager: isManager,
                        isEdit: true,
                      },
                    });
                  }}
                >
                  {t("registerButton")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("sertaPertubuhan")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    color: "#666666",
                    fontWeight: "500!important",
                    fontSize: 14,
                  }}
                >
                  {t("permintaanSertaPertubuhan")}
                </Typography>

                <ButtonPrimary
                  onClick={() => {
                    navigate(`serta-pertubuhan`);
                  }}
                >
                  {t("senaraiPermintaan")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>{" "}
        </>
      ) : (
        <></>
      )}
      <ConfirmationDialog
        status={1}
        turn
        open={openConfirmDelete}
        onClose={() => setOpenConfirmDelete(false)}
        title={t("deleteAhliConfirmation")}
        message={`${t("deleteAhliConfirmation")}`}
        onConfirm={handleDeleteMember}
        onCancel={() => setOpenConfirmDelete(false)}
        isMutation={isLoading}
      />
      <NewAlertDialog
        open={dialogAlertSaveOpen}
        onClose={() => setDialogAlertSaveOpen(false)}
        message={t("recordSuccesDeleted")}
      />
    </>
  );
};

export default AhliPertubuhan;
