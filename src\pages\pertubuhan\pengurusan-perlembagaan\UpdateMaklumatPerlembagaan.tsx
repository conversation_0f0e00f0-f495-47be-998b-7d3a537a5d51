import Box from "@mui/material/Box";
import React, { useEffect, useState } from "react";
import { Stack, Typography, CircularProgress } from "@mui/material";
import ALiranTugas from "../AliranTugas";
import { ButtonPrimary } from "../../../components/button";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { ListSenaraiPindaan } from "../perlembagaan/senarai-pindaan";
import FasalContent from "../../../components/FasalContent";
import { useTranslation } from "react-i18next";
import { API_URL } from "../../../api";
import { useCustom } from "@refinedev/core";
import { useDispatch } from "react-redux";
import {
  setIsDisplayConstituition,
  setIsKuiri,
  setIsViewPindaan,
} from "../../../redux/fasalReducer";
import { removeLocalStorage } from "../../../helpers/utils";
import { removeFromStorage } from "../pengurusan-pertubuhan/perlembagaan/removeFasal";
import {
  ApplicationStatus,
  COMMITTEE_TASK_TYPE,
  ConstitutionType,
} from "../../../helpers/enums";
import JawatankuasaProvider, {
  usejawatankuasaContext,
} from "../ajk/jawatankuasa/jawatankuasaProvider";
import { useDownloadAndExportConstitutions } from "@/helpers/hooks/useDownloadConstitutions";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";

const Perlembagaan = () => {
  const [fasal, setFasal] = useState([]);
  const { id } = useParams();

  const {
    module,
    society,
    setModule,
    fetchAjkList,
    fetchSociety,
    fetchAddressList,
    fetchAliranTugas,
    fetchAliranTugasAccess,
    fetchAliranTugasStatus,
  } = usejawatankuasaContext();

  const isManager = useSelector(getUserPermission);

  const [shouldFetch, setShouldFetch] = useState<boolean>(true);
  useEffect(() => {
    fetchAjkList();
    fetchAddressList();
    fetchSociety();
    fetchAliranTugasAccess(module);
    fetchAliranTugasStatus(module);
    fetchAliranTugas(module);
    setShouldFetch(false);
  }, [module, shouldFetch]);

  useEffect(() => {
    setModule(COMMITTEE_TASK_TYPE.PERLEMBAGAAN);
  }, [module]);

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);
  const constitutionType = societyDataRedux?.constitutionType || null;

  const { data: clauseContentData, isLoading: loadingData } = useCustom({
    url: `${API_URL}/society/constitutioncontent/get`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: id,
        status:
          societyDataRedux?.applicationStatusCode === 2
            ? ApplicationStatus["MENUNGGU_KEPUTUSAN"]
            : societyDataRedux?.applicationStatusCode === 4
            ? ApplicationStatus["INAKTIF"]
            : ApplicationStatus["AKTIF"],
      },
    },
    queryOptions: {
      enabled: !!id,
    },
  });

  const clauseContent = clauseContentData?.data?.data?.data || [];

  const { data: constitutionData, isLoading: isConstitutionLoading } =
    useCustom({
      url: `${API_URL}/society/admin/constitutionTypeWithClauseContent/list`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
    });

  const allConstitutions = constitutionData?.data?.data || [];

  const generatePerlembagaan = () => {
    if (constitutionType) {
      const updatedConstitutions = clauseContent
        ?.map((item: any) => {
          const isBebasType =
            item.constitutionTypeId === 6 || item.constitutionTypeId === 7;
          return {
            ...item,
            content: item?.description,
            name: item?.clauseName,
            ...(isBebasType
              ? {
                  content: item.description,
                  clauseNo: item?.clauseNo,
                  constitutionTypeId: item?.constitutionTypeId,
                  description: item.description,
                  id: item?.id,
                  name: item?.clauseName,
                }
              : {}),
          };
        })
        .sort((a: any, b: any) => Number(a.clauseNo) - Number(b.clauseNo));
      setFasal(updatedConstitutions);
    } else {
      setFasal([]);
    }
  };

  // const generatePerlembagaanAfter = () => {
  //   if (constitutionType) {
  //     const updatedConstitutions = allConstitutions?.map((item: any) => {
  //       const updatedClauseContents = item?.clauseContents?.map(
  //         (clause: any, index: any) => {
  //           const existingItem = clauseContent.find(
  //             (item: any) => item.clauseContentId === clause.id
  //           );
  //           if (existingItem) {
  //             return {
  //               ...clause,
  //               description: existingItem?.description,
  //             };
  //           }
  //           return { ...clause, description: clause?.content };
  //         }
  //       );
  //       return {
  //         ...item,
  //         clauseContents: updatedClauseContents,
  //       };
  //     });
  //     updatedConstitutions?.map((item: any) => {
  //       if (item.name === constitutionType) {
  //         setFasal(item.clauseContents);
  //       }
  //     });
  //   } else {
  //     setFasal([]);
  //   }
  // };

  useEffect(() => {
    if (clauseContent) {
      generatePerlembagaan();
    }
  }, [constitutionType, constitutionData, clauseContent]);

  const isLoading = loadingData || isConstitutionLoading;
  const { getConstitutionsFile, isLoadingDownloadConstitutions } =
    useDownloadAndExportConstitutions();

  const hideIndex = clauseContent.findIndex(
    (p: any) => p.hideConstitution == true
  );

  const hideId = clauseContent?.[hideIndex]?.clauseContentId ?? null;

  // const getAllBebasTambahContent = clauseContent
  //   .filter(
  //     (item: any) =>
  //       item.clauseContent === null &&
  //       (Number(item.constitutionTypeId) === 6 ||
  //         Number(item.constitutionTypeId) === 7)
  //   )
  //   .map((item: any, index: number) => ({
  //     content: item.description,
  //     clauseNo: item?.clauseNo,
  //     constitutionTypeId: item?.constitutionTypeId,
  //     description: item.description,
  //     id: item?.id,
  //     name: item?.clauseName,
  //   }));

  // console.log(fasal);
  // const finalFasalContent = [...fasalContent, ...getAllBebasTambahContent];
  const finalSenaraiFasal = [...fasal];

  if (isLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "300px",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }
  return (
    <>
      {finalSenaraiFasal.length > 0 && (
        <FasalContent
          downloadButton
          fasalContent={finalSenaraiFasal}
          isLoadingDownload={isLoadingDownloadConstitutions}
          downloadFunction={() => id && getConstitutionsFile(id, 1)}
          hideId={hideId}
        />
      )}
    </>
  );
};

export interface Organization {
  id: number;
  name: string;
  code: string;
}

export const UpdateMaklumatPerlembagaan: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { society } = usejawatankuasaContext();
  const navigate = useNavigate();
  const { id } = useParams();
  const location = useLocation();
  const disabled = location.state?.disabled ?? false;

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const clearStateData = () => {
    dispatch(setIsViewPindaan(null));
    dispatch(setIsKuiri(false));
    removeLocalStorage("amendmentId");
    removeLocalStorage("isViewMode");
  };

  const goPage = (url: string) => {
    // removeLocalStorage("amendmentId");
    // removeLocalStorage("isViewMode");
    removeFromStorage();
    dispatch(setIsDisplayConstituition(false));
    navigate(url);
  };

  const { data: clauseContentData, isLoading: isClauseContentDataLoading } =
    useCustom({
      url: `${API_URL}/society/constitutioncontent/get`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        query: {
          societyId: id,
          status: ApplicationStatus["AKTIF"],
        },
      },
      queryOptions: {
        enabled: !!id,
      },
    });

  let clauseContent;
  if (clauseContentData) {
    clauseContent = clauseContentData?.data?.data?.data || [];
  }

  const isManager = useSelector(getUserPermission);
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isBlacklisted = society?.subStatusCode === "003";

  const isAccessible = !isBlacklisted && (isManager || isAliranTugasAccess);
  return (
    <>
      <Stack
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
        gap={2}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("perlembagaanPertubuhan")}
          </Typography>
          <Perlembagaan />
        </Box>
      </Stack>

      {isAccessible ? (
        <>
          <Stack
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
            gap={2}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("constitutionalAmendment")}
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  sx={{
                    color: "#666666",
                    fontWeight: "400!important",
                    fontSize: 14,
                  }}
                >
                  {t("permohonanPindaanPerlembagaan")}
                </Typography>
                <ButtonPrimary
                  disabled={disabled}
                  onClick={() => {
                    clearStateData();
                    goPage("pindaan-perlembagaan");
                  }}
                >
                  {t("permohonan")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Stack>
        </>
      ) : (
        <></>
      )}
      <Stack
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
        gap={2}
      >
        <ListSenaraiPindaan isBlackListed={!!isBlacklisted} />
      </Stack>

      {isAccessible ? (
        <ALiranTugas
          module={COMMITTEE_TASK_TYPE.PERLEMBAGAAN}
          confirmationMessage={(data) =>
            t(`areYouSureFlowManagementTaskToAnotherCommittee_${data.module}`, {
              name: `<b>${data.ajkName}</b>`,
            })
          }
        />
      ) : null}
      {/* <JawatankuasaProvider>
          <ALiranTugas />
        </JawatankuasaProvider>  */}
    </>
  );
};

const Page: React.FC = () => {
  return (
    <>
      <UpdateMaklumatPerlembagaan />
    </>
  );
};

export default Page;
