import { Route, Outlet, Navigate } from "react-router-dom";
import { RouteGuard } from "../../components/RouteGuard";
import { registerRoutes } from "../../helpers/routeDetector";
import LayoutSettings from "../../pages/settings/roles/Layout";
// import ListPenggunaJPM from "../../pages/settings/roles/pengguna-jpm/ListPenggunaJPM";
import ListCategoryJPM from "../../pages/settings/roles/category-jpm/internalCategory/ListCategoryJPM";
import CreatePenggunaJPM from "../../pages/settings/roles/pengguna-jpm/ListPenggunaJPM/createUserJPPM/CreatePenggunaJPM";
import CreateRoleJPM from "../../pages/settings/roles/category-jpm/internalCategory/CreateRoleJPM";
import AuditTrail from "../../pages/pertubuhan/audit/trail";
import Integrasi from "../../pages/pertubuhan/integrasi";
import EditMailLuar from "../../pages/settings/roles/pengguna-jpm/ListPenggunaLuar/editMailLuar/EditMailLuar";
import EditPenggunaLuar from "../../pages/settings/roles/pengguna-jpm/ListPenggunaLuar/editUserLuar/EditPenggunaLuar";
import ROApprovalPenggunaJPM from "../../pages/settings/roles/pengguna-jpm/ListPenggunaJPM/roApproval/ROApprovalPenggunaJPM";
import ListPenyelenggaranUmum from "../../pages/settings/roles/penyelenggaran-umum/index";
import PenyelenggaranUmum_Gred_Kemaskini from "../../pages/settings/roles/penyelenggaran-umum/Gred/Kemaskini";
import PenyelenggaranUmum_Gred_List from "../../pages/settings/roles/penyelenggaran-umum/Gred/List";
import PenyelenggaranUmum_Gred_Tambah from "../../pages/settings/roles/penyelenggaran-umum/Gred/Tambah";
import PenyelenggaranUmum_Pekerjaan_Kemaskini from "../../pages/settings/roles/penyelenggaran-umum/Pekerjaan/Kemaskini";
import PenyelenggaranUmum_Pekerjaan_List from "../../pages/settings/roles/penyelenggaran-umum/Pekerjaan/List";
import PenyelenggaranUmum_Pekerjaan_Tambah from "../../pages/settings/roles/penyelenggaran-umum/Pekerjaan/Tambah";
import PenyelenggaranUmum_Negara_Kemaskini from "../../pages/settings/roles/penyelenggaran-umum/Negara/Kemaskini";
import PenyelenggaranUmum_Negara_List from "../../pages/settings/roles/penyelenggaran-umum/Negara/List";
import PenyelenggaranUmum_Negara_Tambah from "../../pages/settings/roles/penyelenggaran-umum/Negara/Tambah";
import PenyelenggaranUmum_Negeri_Kemaskini from "../../pages/settings/roles/penyelenggaran-umum/Negeri/Kemaskini";
import PenyelenggaranUmum_Negeri_List from "../../pages/settings/roles/penyelenggaran-umum/Negeri/List";
import PenyelenggaranUmum_Negeri_Tambah from "../../pages/settings/roles/penyelenggaran-umum/Negeri/Tambah";
import PenyelenggaranUmum_Daerah_Kemaskini from "../../pages/settings/roles/penyelenggaran-umum/Daerah/Kemaskini";
import PenyelenggaranUmum_Daerah_List from "../../pages/settings/roles/penyelenggaran-umum/Daerah/List";
import PenyelenggaranUmum_Daerah_Tambah from "../../pages/settings/roles/penyelenggaran-umum/Daerah/Tambah";
import PenyelenggaranUmum_Agama_Kemaskini from "../../pages/settings/roles/penyelenggaran-umum/Agama/Kemaskini";
import PenyelenggaranUmum_Agama_List from "../../pages/settings/roles/penyelenggaran-umum/Agama/List";
import PenyelenggaranUmum_Agama_Tambah from "../../pages/settings/roles/penyelenggaran-umum/Agama/Tambah";
import PenyelenggaranUmum_Keturunan_Kemaskini from "../../pages/settings/roles/penyelenggaran-umum/Keturunan/Kemaskini";
import PenyelenggaranUmum_Keturunan_List from "../../pages/settings/roles/penyelenggaran-umum/Keturunan/List";
import PenyelenggaranUmum_Keturunan_Tambah from "../../pages/settings/roles/penyelenggaran-umum/Keturunan/Tambah";
import PenyelenggaranUmum_EmelQueue_List from "../../pages/settings/roles/penyelenggaran-umum/EmelQueue/List";
import PenyelenggaranUmum_EmelQueue_Kemaskini from "../../pages/settings/roles/penyelenggaran-umum/EmelQueue/Kemaskini";
import PenyelenggaranUmum_Kalendar_List from "../../pages/settings/roles/penyelenggaran-umum/Kalendar/List";
import PenyelenggaranUmum_Kalendar_Tambah from "../../pages/settings/roles/penyelenggaran-umum/Kalendar/Tambah";
import PenyelenggaranUmum_Kalendar_Kemaskini from "../../pages/settings/roles/penyelenggaran-umum/Kalendar/Kemaskini";
import PenyelenggaranUmum_JabatanInsolvensi_Kemaskini from "../../pages/settings/roles/penyelenggaran-umum/JabatanInsolvensi/Kemaskini";
import PenyelenggaranUmum_JabatanInsolvensi_List from "../../pages/settings/roles/penyelenggaran-umum/JabatanInsolvensi/List";
import PenyelenggaranUmum_JabatanInsolvensi_Tambah from "../../pages/settings/roles/penyelenggaran-umum/JabatanInsolvensi/Tambah";
import PenyelenggaranUmum_Cawangan_Kemaskini from "../../pages/settings/roles/penyelenggaran-umum/CawanganJPPM/Kemaskini";
import PenyelenggaranUmum_Cawangan_List from "../../pages/settings/roles/penyelenggaran-umum/CawanganJPPM/List";
import PenyelenggaranUmum_Cawangan_Tambah from "../../pages/settings/roles/penyelenggaran-umum/CawanganJPPM/Tambah";
import { MainPenggunaJPMList } from "@/pages/settings/roles/pengguna-jpm/ListPenggunaJPM/MainPenggunaJPMList";
import { MainPenggunaLuarList } from "@/pages/settings/roles/pengguna-jpm/ListPenggunaLuar/MainPenggunaLuarList";
import SejarahAkaunPenggunaJPM from "@/pages/settings/roles/pengguna-jpm/ListPenggunaJPM/sejarahAkaun/SejarahAkaunPenggunaJPM";

// Layout component to wrap all settings routes with protection
const SettingsLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === "development"}
  >
    <LayoutSettings>
      <Outlet />
    </LayoutSettings>
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example route registrations (uncomment and modify as needed):
  // '/pertubuhan/settings': 'internal',
  "/pertubuhan/settings/list-pengurusan-pengguna-jpm": "internal",
  "/pertubuhan/settings/list-pengurusan-pengguna-luar": "internal",
  "/pertubuhan/settings/create-pengurusan-pengguna-jpm": "internal",
  "/pertubuhan/settings/ro-approval-pengurusan-pengguna-jpm": "internal",
  "/pertubuhan/settings/penyelenggaran-umum": "internal",
  "/pertubuhan/settings/edit-pengurusan-pengguna-luar": "internal",
  "/pertubuhan/settings/mail-pengurusan-pengguna-luar": "internal",
  "/pertubuhan/settings/category-pengurusan-peranan-jpm": "internal",
  "/pertubuhan/settings/create-category-pengurusan-peranan-jpm": "internal",
  "/pertubuhan/settings/audit-trial": "internal",
  "/pertubuhan/settings/integrasi": "internal",
  "/pertubuhan/settings/penyelenggaran-umum/emel-queue": "internal",
  "/pertubuhan/settings/penyelenggaran-umum/gred-jawatan": "internal",
  "/pertubuhan/settings/penyelenggaran-umum/pekerjaan": "internal",
  "/pertubuhan/settings/penyelenggaran-umum/negara": "internal",
  "/pertubuhan/settings/penyelenggaran-umum/negeri": "internal",
  "/pertubuhan/settings/penyelenggaran-umum/daerah": "internal",
  "/pertubuhan/settings/penyelenggaran-umum/agama": "internal",
  "/pertubuhan/settings/penyelenggaran-umum/keturunan": "internal",
  "/pertubuhan/settings/penyelenggaran-umum/cawangan": "internal",
  "/pertubuhan/settings/penyelenggaran-umum/jabatan-insolvensi": "internal",
  "/pertubuhan/settings/penyelenggaran-umum/kalendar": "internal",
  // Add your route registrations here
});

export const settings = {
  routes: (
    <Route path="settings" element={<SettingsLayout />}>
      <Route
        index
        element={<Navigate to="list-pengurusan-pengguna-jpm" replace />}
      />
      <Route path="list-pengurusan-pengguna-jpm">
        <Route index element={<MainPenggunaJPMList />} />
      </Route>
      <Route path="list-pengurusan-pengguna-luar">
        <Route index element={<MainPenggunaLuarList />} />
      </Route>
      <Route path="create-pengurusan-pengguna-jpm">
        <Route index element={<CreatePenggunaJPM />} />
      </Route>
      <Route path="pengurusan-pengguna-jpm">
        <Route path="sejarah-akaun" element={<SejarahAkaunPenggunaJPM />} />
      </Route>
      <Route path="penyelenggaran-umum" element={<ListPenyelenggaranUmum />}>
        <Route index element={<Navigate to="emel-queue" replace />} />
        <Route path="emel-queue">
          <Route index element={<PenyelenggaranUmum_EmelQueue_List />} />
          <Route
            path="kemaskini/:id"
            element={<PenyelenggaranUmum_EmelQueue_Kemaskini />}
          />
        </Route>
        <Route path="gred-jawatan">
          <Route index element={<PenyelenggaranUmum_Gred_List />} />
          <Route path="tambah" element={<PenyelenggaranUmum_Gred_Tambah />} />
          <Route
            path="kemaskini/:id"
            element={<PenyelenggaranUmum_Gred_Kemaskini />}
          />
        </Route>
        <Route path="pekerjaan">
          <Route index element={<PenyelenggaranUmum_Pekerjaan_List />} />
          <Route
            path="tambah"
            element={<PenyelenggaranUmum_Pekerjaan_Tambah />}
          />
          <Route
            path="kemaskini/:id"
            element={<PenyelenggaranUmum_Pekerjaan_Kemaskini />}
          />
        </Route>
        <Route path="negara">
          <Route index element={<PenyelenggaranUmum_Negara_List />} />
          <Route path="tambah" element={<PenyelenggaranUmum_Negara_Tambah />} />
          <Route
            path="kemaskini/:id"
            element={<PenyelenggaranUmum_Negara_Kemaskini />}
          />
        </Route>
        <Route path="negeri">
          <Route index element={<PenyelenggaranUmum_Negeri_List />} />
          <Route path="tambah" element={<PenyelenggaranUmum_Negeri_Tambah />} />
          <Route
            path="kemaskini/:id"
            element={<PenyelenggaranUmum_Negeri_Kemaskini />}
          />
        </Route>
        <Route path="daerah">
          <Route index element={<PenyelenggaranUmum_Daerah_List />} />
          <Route path="tambah" element={<PenyelenggaranUmum_Daerah_Tambah />} />
          <Route
            path="kemaskini/:id"
            element={<PenyelenggaranUmum_Daerah_Kemaskini />}
          />
        </Route>
        <Route path="agama">
          <Route index element={<PenyelenggaranUmum_Agama_List />} />
          <Route path="tambah" element={<PenyelenggaranUmum_Agama_Tambah />} />
          <Route
            path="kemaskini/:id"
            element={<PenyelenggaranUmum_Agama_Kemaskini />}
          />
        </Route>
        <Route path="keturunan">
          <Route index element={<PenyelenggaranUmum_Keturunan_List />} />
          <Route
            path="tambah"
            element={<PenyelenggaranUmum_Keturunan_Tambah />}
          />
          <Route
            path="kemaskini/:id"
            element={<PenyelenggaranUmum_Keturunan_Kemaskini />}
          />
        </Route>
        <Route path="jabatan-insolvensi">
          <Route
            index
            element={<PenyelenggaranUmum_JabatanInsolvensi_List />}
          />
          <Route
            path="tambah"
            element={<PenyelenggaranUmum_JabatanInsolvensi_Tambah />}
          />
          <Route
            path="kemaskini/:id"
            element={<PenyelenggaranUmum_JabatanInsolvensi_Kemaskini />}
          />
        </Route>
        <Route path="cawangan">
          <Route index element={<PenyelenggaranUmum_Cawangan_List />} />
          <Route
            path="tambah"
            element={<PenyelenggaranUmum_Cawangan_Tambah />}
          />
          <Route
            path="kemaskini/:id"
            element={<PenyelenggaranUmum_Cawangan_Kemaskini />}
          />
        </Route>
        <Route path="kalendar">
          <Route index element={<PenyelenggaranUmum_Kalendar_List />} />
          <Route
            path="tambah"
            element={<PenyelenggaranUmum_Kalendar_Tambah />}
          />
          <Route
            path="kemaskini/:id"
            element={<PenyelenggaranUmum_Kalendar_Kemaskini />}
          />
        </Route>
      </Route>

      <Route path="ro-approval-pengurusan-pengguna-jpm">
        <Route index element={<ROApprovalPenggunaJPM />} />
      </Route>
      <Route path="edit-pengurusan-pengguna-luar">
        <Route index element={<EditPenggunaLuar />} />
      </Route>
      <Route path="mail-pengurusan-pengguna-luar">
        <Route index element={<EditMailLuar />} />
      </Route>
      <Route path="category-pengurusan-peranan-jpm">
        <Route index element={<ListCategoryJPM />} />
      </Route>
      {/* <Route path="category-pengurusan-peranan-luar">
        <Route index element={<ListCategoryExternal />} />
      </Route> */}
      <Route path="create-category-pengurusan-peranan-jpm">
        <Route index element={<CreateRoleJPM />} />
      </Route>
      {/* <Route path="create-category-pengurusan-peranan-luar">
        <Route index element={<CreateRoleExternal />} />
      </Route> */}
      <Route path="audit-trial">
        <Route index element={<AuditTrail />} />
      </Route>
      <Route path="integrasi">
        <Route index element={<Integrasi />} />
      </Route>
    </Route>
  ),
};
