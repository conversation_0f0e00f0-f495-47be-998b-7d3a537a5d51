import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { API_URL } from "../../../../../api";
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
interface FasalContentTujuhCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
}

export const FasalContentTujuhCawangan: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [clauseContentDescription, setClauseContentDescription] = useState<string|undefined>("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  /*const clauseContent = `
              1. Pengerusi dalam tempoh memegang jawatannya hendaklah
              mempengerusikan semua Mesyuarat Agung dan Mesyuarat Jawatankuasa
              serta bertanggungjawab atas kesempurnaan perjalanan mesyuarat.
              Pengerusi mempunyai undi pemutus dan beliau hendaklah
              menandatangani minit mesyuarat tersebut.

              2. Timbalan Pengerusi hendaklah membantu Pengerusi dalam urusan
              pentadbiran Pertubuhan dan memangku tugas Pengerusi semasa
              ketiadaannya.

              a. Naib Pengerusi hendaklah memangku jawatan Pengerusi dan
              Timbalan Pengerusi semasa ketiadaannya. (Jika ada)

              3. Setiausaha hendaklah menjalankan kerja pentadbiran Pertubuhan
              mengikut Perlembagaan dan menjalankan arahan-arahan Mesyuarat
              Agung dan Jawatankuasa. Setiausaha bertanggungjawab mengendalikan
              urusan surat-menyurat dan menyimpan semua rekod serta dokumen
              Pertubuhan, kecuali buku-buku akaun dan dokumen kewangan.
              Setiausaha hendaklah menyimpan buku daftar ahli yang mengandungi
              maklumat terperinci ahli mengikut kategori seperti nama, tempat
              dan tarikh lahir, nombor kad pengenalan, pekerjaan, nama dan
              alamat majikan dan alamat rumah kediaman tiap-tiap ahli.
              Setiausaha hendaklah hadir dalam semua mesyuarat dan membuat
              catatan mesyuarat kecuali atas sebab-sebab munasabah. Setiausaha
              hendaklah dalam masa 60 hari dari tarikh kelulusan Mesyuarat Agung
              diadakan mengemukakan Penyata Tahunan kepada Pendaftar Pertubuhan.

              4. Penolong Setiausaha hendaklah membantu Setiausaha menjalankan
              kerja-kerjanya dan memangku jawatan itu semasa ketiadaan
              Setiausaha.

              5. Bendahari hendaklah bertanggungjawab dalam semua hal ehwal
              kewangan Pertubuhan dan memastikan penyata kewangan Pertubuhan
              yang terdiri daripada penyata penerimaan dan perbelanjaan serta
              kunci kira-kira adalah tepat dan teratur.

              a. Penolong Bendahari hendaklah membantu Bendahari menjalankan
              kerja-kerjanya dan memangku jawatan itu semasa ketiadaan
              Bendahari. (Jika ada)

              6. Ahli Jawatankuasa Biasa hendaklah membantu Jawatankuasa
              melaksanakan tugas yang diarahkan kepadanya.
            `;*/

  //const clause7 = localStorage.getItem("clause7");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) { 
      
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause7Data = JSON.parse(clause7);
      setDataId(clause.id); 
      if (clause.clauseContentId) { 
        setClauseContentId(clause.clauseContentId);
      }
      if(clause.clauseDescription){
        setClauseContentDescription(clause.clauseDescription);
      }else{
        setClauseContentDescription(undefined);
      }
      //setNamaPertubuhan(clause7Data.societyName);
      setIsEdit(clause.edit);
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id } = useParams();
  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");
  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContentDescription ? clauseContentDescription : clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContentDescription ? clauseContentDescription : clauseContent,
              constitutionValues: [],
              clause: "clause7",
              clauseCount: 7,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentTujuhCawangan;
