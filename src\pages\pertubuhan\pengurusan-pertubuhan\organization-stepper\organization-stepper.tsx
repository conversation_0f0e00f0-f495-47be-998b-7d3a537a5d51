import React from "react";
import {
  Stepper,
  Step,
  StepLabel,
  useMediaQuery,
  Theme,
  StepConnector,
  Box,
  Typography,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { ApplicationStatus, capitalizeWords } from "@/helpers";
import CheckIcon from "@mui/icons-material/Check";

const steps = [
  "generalInformation",
  "establishmentMeeting",
  "constitution",
  "committeeList",
  "supportingDocuments",
  "payment",
];

interface OrganizationStepperProps {
  isMaklumatCreated?: boolean;
  ajkBukanWn?:
    | {
        id: string | number;
        jawatan: string;
        namaAJK: string;
        statusPermohonan: string;
      }[]
    | undefined;
  activeStep: number;
  applicationStatusCode?: number;
  hidePayment?: boolean;
  isFasalFinished?: boolean;
}

export const OrganizationStepper: React.FC<OrganizationStepperProps> = ({
  isMaklumatCreated = true,
  ajkBukanWn = undefined,
  activeStep,
  applicationStatusCode,
  hidePayment,
  isFasalFinished = false,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const params = new URLSearchParams(window.location.search);
  const paramId = params.get("id");

  const handleStepClick = (step: number) => {
    const encodedId = paramId;
    //const encodedId = localStorage.getItem("societyIdEncoded") || paramId;
    //const currentCode = localStorage.getItem("applicationStatusCode");

    const navigateToStep = (path: string) => {
      /*setTimeout(() => {
        if (currentCode) {
          localStorage.setItem("applicationStatusCode", currentCode);
        }
      }, 0);*/
      navigate(path);
    };

    switch (step) {
      case 0:
        navigateToStep(
          `/pertubuhan/pengurusan-pertubuhan/pendaftaran/maklumat-am?id=${encodedId}`
        );
        break;
      case 1:
        navigateToStep(
          `/pertubuhan/pengurusan-pertubuhan/pendaftaran/mesyuarat-penubuhan?id=${encodedId}`
        );
        break;
      case 2:
        navigateToStep(
          `/pertubuhan/pengurusan-pertubuhan/pendaftaran/perlembagaan?id=${encodedId}`
        );
        break;
      case 3:
        //if(!isFasalFinished) break;
        navigateToStep(
          `/pertubuhan/pengurusan-pertubuhan/pendaftaran/senarai-ajk?id=${encodedId}`
        );
        break;
      case 4:
        //if(!isFasalFinished) break;
        navigateToStep(
          `/pertubuhan/pengurusan-pertubuhan/pendaftaran/dokumen-sokongan?id=${encodedId}`
        );
        break;
      case 5:
        //if(!isFasalFinished) break;
        navigateToStep(
          `/pertubuhan/pengurusan-pertubuhan/pendaftaran/bayaran?id=${encodedId}`
        );
        break;
      default:
        navigateToStep(
          `/pertubuhan/pengurusan-pertubuhan/pendaftaran/maklumat-am?id=${encodedId}`
        );
    }
  };

  // Filter steps based on applicationStatusCode
  const visibleSteps = steps.filter((_, index) => {
    if (index === 5) {
      // Payment step
      return applicationStatusCode !== ApplicationStatus.TEST_DATA;
    }
    return true;
  });

  return (
    <Box
      sx={{
        padding: 3,
        backgroundColor: "white",
        borderRadius: "15px",
        maxHeight: "60vh",
        maxWidth: "18vw",
      }}
    >
      <Typography
        sx={{
          mb: 8,
          fontSize: "16px",
          color: "var(--primary-color)",
          fontWeight: "500 !important",
        }}
      >
        Langkah Daftar Pertubuhan
      </Typography>
      <Stepper
        sx={{ cursor: "pointer" }}
        activeStep={activeStep}
        orientation="vertical"
        connector={null}
      >
        {visibleSteps
          .filter((item) => {
            if (
              hidePayment ||
              applicationStatusCode == ApplicationStatus.KUIRI
            ) {
              if (item === "payment") {
                return false;
              }
            }
            return item;
          })
          .map((stepKey, index) => (
            <Step
              key={stepKey}
              onClick={
                isMaklumatCreated
                  ? () => handleStepClick(index)
                  : () => navigate("../maklumat-am")
              }
            >
              <StepLabel
                icon={
                  <Box
                    sx={{
                      width: 20,
                      height: 20,
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      borderRadius: "4px",
                      border: `2px solid ${
                        index < activeStep || index === activeStep
                          ? "var(--primary-color)"
                          : "#DADADA"
                      }`,
                      backgroundColor:
                        index < activeStep
                          ? "var(--primary-color)"
                          : "transparent",
                    }}
                  >
                    {index < activeStep && (
                      <CheckIcon
                        sx={{
                          fontSize: 14,
                          color: "#FFF",
                        }}
                      />
                    )}
                  </Box>
                }
              >
                <Typography
                  sx={{
                    fontWeight: "500 !important",
                    color:
                      index < activeStep || index === activeStep
                        ? "var(--primary-color)" // Completed and active steps
                        : "#DADADA", // Inactive steps
                  }}
                >
                  {capitalizeWords(t(stepKey))}
                </Typography>
              </StepLabel>
            </Step>
          ))}
      </Stepper>
    </Box>
  );
};
