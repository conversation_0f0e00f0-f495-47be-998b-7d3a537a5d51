import { Box } from "@mui/material";
import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import {
  SettingsJPPMUserCreateRequestBody,
  useFormManagementSettingsJPPMUserCreateHandleSubmit,
  useFormManagementSettingsJPPMUserCreateInitialValue,
  useFormManagementSettingsJPPMUserCreateValidationSchema,
} from "@/controllers";
import { Formik } from "formik";
import { CreateOrUpdateUserJPPMInner } from "./CreateOrUpdateInner";
import { useQuery } from "@/helpers";
import Input from "@/components/input/Input";
import { useTranslation } from "react-i18next";

function CreatePenggunaJPM<
  UserData extends SettingsJPPMUserCreateRequestBody = SettingsJPPMUserCreateRequestBody
>() {
  const [searchParams] = useSearchParams();
  const [triggerGetUserData, setTriggerGetUserData] = useState(false);
  const { t } = useTranslation();
  const userId = searchParams.get("id");
  const {
    data: userResponse,
    refetch: fetchUserData,
    isLoading: isLoadingUser,
  } = useQuery<{ data: UserData }>({
    url: `user/admin/${userId}`,
    autoFetch: false,
  });
  const userData = userResponse?.data?.data ?? null;
  const { getInitialValue } =
    useFormManagementSettingsJPPMUserCreateInitialValue<UserData>();
  const initialValue = getInitialValue(userData);
  const hasUserId = !!userId;

  const { handleSubmit } =
    useFormManagementSettingsJPPMUserCreateHandleSubmit<UserData>({
      initialValue,
    });
  const { getValidationSchema } =
    useFormManagementSettingsJPPMUserCreateValidationSchema();

  const getUserData = async () => {
    try {
      await fetchUserData();
    } finally {
      setTriggerGetUserData(false);
    }
  };

  useEffect(() => {
    if (hasUserId) {
      setTriggerGetUserData(true);
    }
  }, [hasUserId]);
  useEffect(() => {
    if (triggerGetUserData) {
      getUserData();
    }
  }, [triggerGetUserData]);

  return (
    <Box sx={{ display: "grid", gap: 2 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Formik
            initialValues={initialValue}
            onSubmit={handleSubmit}
            validationSchema={getValidationSchema()}
            enableReinitialize
          >
            <CreateOrUpdateUserJPPMInner
              initialValues={initialValue}
              isLoadingUserData={isLoadingUser}
              onRefreshUserData={() => {
                if (hasUserId) {
                  fetchUserData();
                }
              }}
            />
          </Formik>
        </Box>
      </Box>

      {userData?.remarks ? (
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            backgroundColor: "white",
            borderRadius: "14px",
          }}
        >
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
              mb: 2,
            }}
          >
            <Input
              value={userData.remarks}
              name="remarks"
              // required
              disabled
              multiline
              label={t("remarks")}
            />
          </Box>
        </Box>
      ) : null}
    </Box>
  );
}

export default CreatePenggunaJPM;
