import { Box, Grid, Icon<PERSON>utton, Switch, Typography } from "@mui/material";
import { LaranganBox } from "./component/LaranganBorder";
import { LaranganPaper } from "./component/LaranganPaper";
import TakwimInput from "@/components/input/TakwimInput";
import TakwimTextField from "@/components/input/TakwimTextField";
import TakwimSelect from "@/components/input/TakwimSelect";
import { ButtonPrimary } from "@/components";
import DialogActionFlow from "@/components/dialog/confirm/DialogActionFlow";
import { useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import TextareaWithLabel from "@/components/input/TextareaWithLabel";
import { LogoLarangan } from "@/types/larangan/logoLarangan";
import dayjs from "dayjs";

interface TabLogoTerlarangData {
  id: number;
  readAccess: boolean;
  createAccess: boolean;
  updateAccess: boolean;
  deleteAccess: boolean;
  mode: string | null;
  isEditMode: boolean;
  isDisabled: boolean;
}
export const TabLogoTerlarang = ({
  id,
  readAccess,
  createAccess,
  updateAccess,
  deleteAccess,
  mode,
  isEditMode,
  isDisabled,
}: TabLogoTerlarangData) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [customDialogMessage, setCustomDialogMessage] = useState<string | null>(
    null
  );
  const [logoLaranganFormData, setLogoLaranganFormData] = useState<
    Partial<LogoLarangan>
  >({});
  const handleConfirm = async () => {
    handleSave();
  };

  const handleSave = () => {
    const logoLarangan: Partial<LogoLarangan> = {
      activeRemarks: logoLaranganFormData.activeRemarks,
      remarks: logoLaranganFormData.remarks,
      status: logoLaranganFormData.status,
      createdDate: logoLaranganFormData.createdDate,
    };
  };

  useEffect(() => {
    // Fetch data based on id
    setLogoLaranganFormData({
      createdDate: dayjs().format("DD/MM/YYYY HH:mm"),
    });
  }, []);

  function handleFormChange<K extends keyof typeof logoLaranganFormData>(
    key: K,
    value: (typeof logoLaranganFormData)[K]
  ) {
    setLogoLaranganFormData((prev) => ({
      ...prev,
      [key]: value,
    }));
  }
  return (
    <>
      <LaranganPaper>
        <LaranganBox
          sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Box>
            <Typography
              sx={{
                color: "primary.main",
              }}
            >
              Aktivasi Kata Kunci Terlarang
            </Typography>
          </Box>
          <Box>
            <Switch
              onChange={(e) => handleFormChange("status", e.target.checked)}
              disabled={isDisabled}
            />
          </Box>
        </LaranganBox>

        <LaranganBox>
          <Typography
            sx={{
              color: "primary.main",
            }}
          >
            Butiran
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: 2,
            }}
          >
            {/* IMAGE SECTION */}
            <Grid container spacing={2} alignItems={"flex-start"}>
              <Grid item xs={12} sm={3}>
                <Typography
                  variant="body1"
                  sx={{
                    color: "#666666",
                    fontSize: "14px",
                    mt: 1,
                  }}
                >
                  Gambar Larangan
                  {/* {required && <span style={{ color: 'red' }}>*</span>} */}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={9}>
                <Box
                  sx={{
                    border: "1px solid #ccc",
                    borderRadius: 1,
                    p: 3,
                    textAlign: "center",
                    cursor: "pointer",
                    position: "relative",
                    minHeight: "200px",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    width: "100%",
                  }}
                  onClick={() =>
                    document.getElementById("posterUpload")?.click()
                  }
                >
                  {/* <TakwimInput label="Kata Kunci" value="" onChange={() => {}} /> */}
                  <input
                    type="file"
                    id="posterUpload"
                    hidden
                    accept="image/*"
                    disabled={isDisabled}
                    // onChange={handleBannerImageChange}
                  />
                  {/* <IconButton
                    sx={{
                      position: "absolute",
                      top: -10,
                      right: -10,
                      bgcolor: "white",
                      "&:hover": { bgcolor: "#f5f5f5" },
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      // setBannerPreview("");
                      // setBannerDeleted(true);
                    }}
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton> */}
                  <>
                    <CloudUploadIcon
                      sx={{ fontSize: 40, color: "#4DB6AC", mb: 1 }}
                    />
                    <Typography variant="body2" color="textSecondary">
                      Muat naik
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      SVG, PNG, JPG or GIF (max. 800x400px)
                    </Typography>
                  </>
                </Box>
              </Grid>
            </Grid>
            <TextareaWithLabel
              label="Catatan"
              value={logoLaranganFormData.remarks}
              onChange={(e) => {
                handleFormChange("remarks", e.target.value);
              }}
              disabled={isDisabled}
            />
            <TakwimInput
              label="Tarikh dan Masa Terbit"
              value={logoLaranganFormData.createdDate}
              // onChange={() => {}}
            />
          </Box>
        </LaranganBox>
        <LaranganBox>
          <Typography
            sx={{
              color: "primary.main",
            }}
          >
            Catatan Status
          </Typography>
          <TextareaWithLabel
            label="Catatan Status"
            value={logoLaranganFormData.activeRemarks}
            onChange={(e) => {
              handleFormChange("activeRemarks", e.target.value);
            }}
            disabled={isDisabled}
          />
        </LaranganBox>
        <Box sx={{ p: "15px", textAlign: "right" }}>
          {!isDisabled && (
            <ButtonPrimary
              sx={{
                fontWeight: 300,
              }}
              onClick={() => setOpenDialog(true)}
            >
              Kemaskini
            </ButtonPrimary>
          )}
        </Box>
      </LaranganPaper>
      <DialogActionFlow
        open={openDialog}
        onClose={() => {
          setOpenDialog(false);
          setCustomDialogMessage(null);
        }}
        onConfirm={customDialogMessage ? async () => {} : handleConfirm}
        hideOnError={false}
        confirmationText={
          customDialogMessage
            ? customDialogMessage
            : "Adakah anda pasti untuk menyimpan logo larangan ini?"
        }
        successMessage={
          customDialogMessage
            ? customDialogMessage
            : "Logo larangan berjaya disimpan"
        }
      />
    </>
  );
};
