import { API_URL } from "@/api";
import { NamaLarangan } from "@/types/larangan/namaLarangan";

const EVENT_BASE_ENDPOINT = `${API_URL}/society/forbidden`;

interface laranganServiceState {
  data: any | null;
  loading: boolean;
  error: string | null;
}

export interface ApiResponse<T> {
  data: T;
  status: string;
  msg?: string;
  code?: number;
}
class LaranganService {
  private state: laranganServiceState = {
    data: null,
    loading: false,
    error: null,
  };

  private baseEndpoint = `${API_URL}/society/event`;

  // State getters
  getData = () => this.state.data;
  getLoading = () => this.state.loading;
  getError = () => this.state.error;

  private setState = (newState: Partial<laranganServiceState>) => {
    this.state = { ...this.state, ...newState };
  };

  // Get authentication headers
  private getAuthHeaders = () => ({
    portal: localStorage.getItem("portal") || "",
    authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
    "Content-Type": "application/json",
  });


  getSenaraiLarangan = async (): Promise<ApiResponse<NamaLarangan[]>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/get-all/senarai-larangan`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            "Content-Type": "application/json",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );
      const result: ApiResponse<NamaLarangan[]> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch senarai larangan");
      }
      this.setState({ data: result.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch senarai larangan";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };
}

export const laranganService = new LaranganService();
