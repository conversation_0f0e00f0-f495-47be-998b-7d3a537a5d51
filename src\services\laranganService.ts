import { API_URL } from "@/api";
import { LogoLarangan } from "@/types/larangan/logoLarangan";
import { NamaLarangan, NamaLaranganResponse } from "@/types/larangan/namaLarangan";

const FORBIDDEN_BASE_ENDPOINT = `${API_URL}/society/forbidden`;

interface laranganServiceState {
  data: any | null;
  loading: boolean;
  error: string | null;
}

export interface ApiResponse<T> {
  data: T;
  status: string;
  msg?: string;
  code?: number;
  pageNo: number;
  pageSize: number;
  total: number;
  empty: boolean;
}

export interface ApiResponsePagination<T> {
  data: {
    total: number;
    data: T;
  };
  status: string;
  msg?: string;
  code?: number;
  pageNo: number;
  pageSize: number;
  total: number;
  empty: boolean;
}
class LaranganService {
  private state: laranganServiceState = {
    data: null,
    loading: false,
    error: null,
  };

  // State getters
  getData = () => this.state.data;
  getLoading = () => this.state.loading;
  getError = () => this.state.error;

  private setState = (newState: Partial<laranganServiceState>) => {
    this.state = { ...this.state, ...newState };
  };

  // Get authentication headers
  private getAuthHeaders = () => ({
    portal: localStorage.getItem("portal") || "",
    authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
    "Content-Type": "application/json",
  });

  getSenaraiLaranganByPage = async (
    page: number,
    pageSize: number
  ): Promise<ApiResponsePagination<NamaLarangan[]>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${FORBIDDEN_BASE_ENDPOINT}/get-all/senarai-larangan/by-page?pageNo=${page}&pageSize=${pageSize}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            "Content-Type": "application/json",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );
      const result: ApiResponsePagination<NamaLarangan[]> =
        await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch senarai larangan");
      }
      this.setState({ data: result.data.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch senarai larangan";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  getNamaLaranganById = async (id: number | string): Promise<ApiResponse<NamaLaranganResponse>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${FORBIDDEN_BASE_ENDPOINT}/nama-larangan/${id}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            "Content-Type": "application/json",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );
      const result: ApiResponse<NamaLaranganResponse> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch senarai larangan");
      }
      this.setState({ data: result?.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch senarai larangan";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  getLogoLaranganById = async (id: number | string): Promise<ApiResponse<LogoLarangan>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${FORBIDDEN_BASE_ENDPOINT}/larangan-logo/${id}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            "Content-Type": "application/json",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );
      const result: ApiResponse<LogoLarangan> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch senarai larangan");
      }
      this.setState({ data: result?.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch senarai larangan";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };


  searchSenarai = async (
    keyword: string,
    type: string,
    page: number,
    pageSize: number,
    activeStatus: boolean | null
  ): Promise<ApiResponsePagination<NamaLarangan[]>> => {
    this.setState({ loading: true, error: null });
    console.log(page, keyword, "request");
    const withOutStatus = `${FORBIDDEN_BASE_ENDPOINT}/search?keyword=${keyword}&type=${type}&pageNo=${page}&pageSize=${pageSize}`;
    const withStatus = `${FORBIDDEN_BASE_ENDPOINT}/search?keyword=${keyword}&type=${type}&pageNo=${page}&pageSize=${pageSize}&activeStatus=${activeStatus}`;
    try {
      const response = await fetch(
        activeStatus === null ? withOutStatus : withStatus,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            "Content-Type": "application/json",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );
      const result: ApiResponsePagination<NamaLarangan[]> =
        await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to search senarai");
      }
      this.setState({ data: result?.data?.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to search senarai";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  searchSenaraiLogo = async (
    catatan: string,
    page: number,
    pageSize: number,
    activeStatus: boolean | null
  ): Promise<ApiResponsePagination<LogoLarangan[]>> => {
    this.setState({ loading: true, error: null });
    const withOutStatus = `${FORBIDDEN_BASE_ENDPOINT}/larangan-logo/search?pageNo=${page}&pageSize=${pageSize}&catatan=${catatan}`;
    const withStatus = `${FORBIDDEN_BASE_ENDPOINT}/larangan-logo/search?pageNo=${page}&pageSize=${pageSize}&catatan=${catatan}&activeStatus=${activeStatus}`;
    try {
      const response = await fetch(
        activeStatus === null ? withOutStatus : withStatus,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            "Content-Type": "application/json",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );
      const result: ApiResponsePagination<LogoLarangan[]> =
        await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to search senarai logo");
      }
      this.setState({ data: result?.data?.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to search senarai logo";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  deleteLaranganLogo = async (id: number | string): Promise<ApiResponse<any>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${FORBIDDEN_BASE_ENDPOINT}/larangan-logo/delete/${id}`,
        {
          method: "DELETE",
          headers: {
            portal: localStorage.getItem("portal") || "",
            "Content-Type": "application/json",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );
      const result: ApiResponse<any> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to delete larangan logo");
      }
      this.setState({ data: result?.data?.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to delete larangan logo";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  deleteNamaLarangan = async (id: number | string): Promise<ApiResponse<any>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${FORBIDDEN_BASE_ENDPOINT}/senarai/delete/${id}`,
        {
          method: "POST",
          headers: {
            portal: localStorage.getItem("portal") || "",
            "Content-Type": "application/json",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );
      const result: ApiResponse<any> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to delete larangan");
      }
      this.setState({ data: result?.data?.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete larangan";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  createLarangan = async( laranganData: Partial<NamaLarangan>): Promise<ApiResponse<any>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${FORBIDDEN_BASE_ENDPOINT}/senarai/create`,
        {
          method: "POST",
          headers: {
            portal: localStorage.getItem("portal") || "",
            "Content-Type": "application/json",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
          body: JSON.stringify(laranganData)
        }
      );
      const result: ApiResponse<any> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to create larangan");
      }
      this.setState({ data: result?.data?.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create larangan";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  }

  createLaranganLogo = async (laranganData: Partial<LogoLarangan>): Promise<
    ApiResponse<any>
  > => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch( `${FORBIDDEN_BASE_ENDPOINT}/larangan-logo/create`, {
        method: "POST",
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(laranganData)
      });
      const result: ApiResponse<any> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to create larangan logo");
      }
      this.setState({ data: result?.data?.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create larangan logo";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  }

  updateLarangan = async (laranganData: Partial<NamaLarangan>, id: number| string | null): Promise<
    ApiResponse<any>
  > => {
    if (id === null) {
      throw new Error("ID is null");
    }
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${FORBIDDEN_BASE_ENDPOINT}/senarai/update/${id}`,
        {
          method: "POST",
          headers: {
            portal: localStorage.getItem("portal") || "",
            "Content-Type": "application/json",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
          body: JSON.stringify(laranganData)
        }
      );
      const result: ApiResponse<any> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to update larangan");
      }
      this.setState({ data: result?.data?.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update larangan";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  }

  updateLaranganLogo = async (laranganData: Partial<LogoLarangan>, id: number| string | null): Promise<
    ApiResponse<any>
  > => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${FORBIDDEN_BASE_ENDPOINT}/larangan-logo/update/${id}`,
        {
          method: "POST",
          headers: {
            portal: localStorage.getItem("portal") || "",
            "Content-Type": "application/json",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
          body: JSON.stringify(laranganData)
        }
      );
      const result: ApiResponse<any> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to update larangan logo");
      }
      this.setState({ data: result?.data?.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update larangan logo";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  }
}


export const laranganService = new LaranganService();
