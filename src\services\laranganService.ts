import { API_URL } from "@/api";
import { NamaLarangan } from "@/types/larangan/namaLarangan";

const FORBIDDEN_BASE_ENDPOINT = `${API_URL}/society/forbidden`;

interface laranganServiceState {
  data: any | null;
  loading: boolean;
  error: string | null;
}

export interface ApiResponse<T> {
  data: T;
  status: string;
  msg?: string;
  code?: number;
  pageNo: number;
  pageSize: number;
  total: number;
  empty: boolean;
}

export interface ApiResponsePagination<T> {
  data: {
    total: number;
    data: T;
  };
  status: string;
  msg?: string;
  code?: number;
  pageNo: number;
  pageSize: number;
  total: number;
  empty: boolean;
}
class LaranganService {
  private state: laranganServiceState = {
    data: null,
    loading: false,
    error: null,
  };


  // State getters
  getData = () => this.state.data;
  getLoading = () => this.state.loading;
  getError = () => this.state.error;

  private setState = (newState: Partial<laranganServiceState>) => {
    this.state = { ...this.state, ...newState };
  };

  // Get authentication headers
  private getAuthHeaders = () => ({
    portal: localStorage.getItem("portal") || "",
    authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
    "Content-Type": "application/json",
  });

  getSenaraiLaranganByPage = async (
    page: number,
    pageSize: number
  ): Promise<ApiResponsePagination<NamaLarangan[]>> => {
    this.setState({ loading: true, error: null });
    try {
      console.log(page, pageSize, "PAGEs");

      const response = await fetch(
        `${FORBIDDEN_BASE_ENDPOINT}/get-all/senarai-larangan/by-page?pageNo=${page}&pageSize=${pageSize}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            "Content-Type": "application/json",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );
      const result: ApiResponsePagination<NamaLarangan[]> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch senarai larangan");
      }
      this.setState({ data: result.data.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch senarai larangan";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  getSenaraiKelabuByPage = async (
    page: number,
    pageSize: number
  ): Promise<ApiResponsePagination<NamaLarangan[]>> => {
    this.setState({ loading: true, error: null });
    try {
      console.log(page, pageSize, "PAGEs");

      const response = await fetch(
        `${FORBIDDEN_BASE_ENDPOINT}/get-all/senarai-kelabu/by-page?pageNo=${page}&pageSize=${pageSize}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            "Content-Type": "application/json",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );
      const result: ApiResponsePagination<NamaLarangan[]> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch senarai kelabu");
      }
      this.setState({ data: result.data.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch senarai kelabu";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };
}

export const laranganService = new LaranganService();
