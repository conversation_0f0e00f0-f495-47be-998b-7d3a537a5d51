import { PORTAL_INTERNAL, PORTAL_EXTERNAL } from './constants';

export type UserPortalType = 'internal' | 'external' | 'shared' | 'unknown';

// Route registry for storing route portal types
interface RouteConfig {
  portalType: UserPortalType;
}

class RouteRegistry {
  private routes: Map<string, RouteConfig> = new Map();

  /**
   * Register a route with its portal type
   */
  register(path: string, config: RouteConfig) {
    this.routes.set(this.normalizePath(path), config);
  }

  /**
   * Register multiple routes at once
   */
  registerRoutes(routes: Record<string, RouteConfig>) {
    Object.entries(routes).forEach(([path, config]) => {
      this.register(path, config);
    });
  }

  /**
   * Get route configuration
   */
  get(path: string): RouteConfig | undefined {
    const normalizedPath = this.normalizePath(path);

    // Try exact match first
    if (this.routes.has(normalizedPath)) {
      return this.routes.get(normalizedPath);
    }

    // Try pattern matching for dynamic routes
    for (const [registeredPath, config] of this.routes.entries()) {
      if (this.matchesPattern(normalizedPath, registeredPath)) {
        return config;
      }
    }

    return undefined;
  }

  /**
   * Check if a route is registered
   */
  has(path: string): boolean {
    return this.get(path) !== undefined;
  }

  /**
   * Get all registered routes
   */
  getAll(): Map<string, RouteConfig> {
    return new Map(this.routes);
  }

  /**
   * Clear all routes
   */
  clear() {
    this.routes.clear();
  }

  private normalizePath(path: string): string {
    return path.toLowerCase().replace(/\/+$/, '') || '/';
  }

  private matchesPattern(path: string, pattern: string): boolean {
    // Convert pattern to regex (handle :param and * wildcards)
    const regexPattern = pattern
      .replace(/:[^/]+/g, '[^/]+')  // :param -> [^/]+
      .replace(/\*/g, '.*')         // * -> .*
      .replace(/\//g, '\\/');       // escape slashes

    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(path);
  }
}

// Global route registry instance
export const routeRegistry = new RouteRegistry();

/**
 * Gets the portal type for a route from the registry
 * @param routePath - The route path to check
 * @returns UserPortalType or 'unknown' if not registered
 */
export const getRoutePortalType = (routePath: string): UserPortalType => {
  const registeredRoute = routeRegistry.get(routePath);
  return registeredRoute ? registeredRoute.portalType : 'unknown';
};

/**
 * Checks if current user has access to the given route based on their portal
 * @param routePath - The route path to check
 * @param userPortal - Current user's portal (from localStorage or Redux)
 * @returns boolean indicating if user has access
 */
export const hasRouteAccess = (routePath: string, userPortal?: string): boolean => {
  const portalType = getRoutePortalType(routePath);

  if (portalType === 'shared') {
    return true;
  }

  if (!userPortal) {
    userPortal = localStorage.getItem('portal') || '';
  }

  switch (portalType) {
    case 'internal':
      return userPortal === PORTAL_INTERNAL;
    case 'external':
      return userPortal === PORTAL_EXTERNAL;
    default:
      return true; // Allow access for unknown/shared routes (safer default)
  }
};

/**
 * Gets the appropriate redirect path based on user portal
 * @param userPortal - User's portal type
 * @returns Default route for the portal
 */
export const getDefaultRouteForPortal = (userPortal: string): string => {
  switch (userPortal) {
    case PORTAL_INTERNAL:
      return '/internal-user';
    case PORTAL_EXTERNAL:
      return '/pertubuhan';
    default:
      return '/';
  }
};

/**
 * Utility function to get current user portal from various sources
 * @returns Current user portal or null
 */
export const getCurrentUserPortal = (): string | null => {
  // Try localStorage first
  const portalFromStorage = localStorage.getItem('portal');
  if (portalFromStorage) {
    return portalFromStorage;
  }

  // Could also check Redux store here if needed
  // const state = store.getState();
  // return state.user.portal;

  return null;
};

/**
 * Quick check if route is internal-only
 * @param routePath - The route path to check
 * @returns boolean
 */
export const isInternalRoute = (routePath: string): boolean => {
  return getRoutePortalType(routePath) === 'internal';
};

/**
 * Quick check if route is external-only
 * @param routePath - The route path to check
 * @returns boolean
 */
export const isExternalRoute = (routePath: string): boolean => {
  return getRoutePortalType(routePath) === 'external';
};

/**
 * Quick check if route is shared between both portals
 * @param routePath - The route path to check
 * @returns boolean
 */
export const isSharedRoute = (routePath: string): boolean => {
  return getRoutePortalType(routePath) === 'shared';
};

/**
 * Helper function to register routes with their portal types
 * This should be called in route definition files
 */
export const registerRoutes = (routes: Record<string, UserPortalType | RouteConfig>) => {
  const normalizedRoutes: Record<string, RouteConfig> = {};

  Object.entries(routes).forEach(([path, config]) => {
    if (typeof config === 'string') {
      normalizedRoutes[path] = { portalType: config };
    } else {
      normalizedRoutes[path] = config;
    }
  });

  routeRegistry.registerRoutes(normalizedRoutes);
};

/**
 * Helper to register a single route
 */
export const registerRoute = (path: string, portalType: UserPortalType) => {
  routeRegistry.register(path, { portalType });
};
