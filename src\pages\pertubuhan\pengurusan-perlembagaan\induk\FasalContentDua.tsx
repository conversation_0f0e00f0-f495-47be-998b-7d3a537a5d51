import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { Switch } from "@/components/switch";
import { useTranslation } from "react-i18next";
import { ChangeEvent, useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import AsalContent from "@/components/asalFasalContent";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import { capitalizeWords, getDistrictNameById, getStateNameById, MALAYSIA, toTitleCase } from "@/helpers";

const sectionStyleCustom = {
  color: "var(--primary-color)",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
  mt: 1,
};
interface ClauseValueProps {
  definitionName: string;
}

interface FasalContentDuaProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: [];
  name: string;
}

export const FasalContentDua: React.FC<FasalContentDuaProps> = ({
  activeStep,
  setActiveStep,
  clause,
  asalData,
  name,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const [businessState, setBusinessState] = useState("");
  const [businessDistrict, setBusinessDistrict] = useState("");
  const [sameAddress, setSameAddress] = useState(false);
  const [mailingState, setMailingState] = useState("");
  const [mailingDistrict, setMailingDistrict] = useState("");

  const [namaPertubuhan, setNamaPertubuhan] = useState("");

  const [asal, setAsal] = useState<any>(null);
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);

  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [societyId, setSocietyId] = useState<any>(null);
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  const [formData, setFormData] = useState({
    stateCode: "",
    districtCode: "",
    city: "",
    postcode: "",
    address: "",
    mailingStateCode: "",
    mailingDistrictCode: "",
    mailingCity: "",
    mailingPostcode: "",
    mailingAddress: "",
  });

 const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if(name == "stateCode" || name == "districtCode" || name == "city" || name == "postcode" || name == "address"){
      setSameAddress(false);
    }
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
    setFormErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const handleSwitchOnChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { checked } = event.target;

    setSameAddress(checked);

    if (checked) {
      setFormData((prevState) => ({
        ...prevState,
        mailingStateCode: prevState.stateCode,
        mailingDistrictCode: prevState.districtCode,
        mailingCity: prevState.city,
        mailingPostcode: prevState.postcode,
        mailingAddress: prevState.address,
      }));

      setMailingState(businessState);
      setMailingDistrict(businessDistrict);
    }
  };

  const { data:dataAddress, isLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const addressData = dataAddress?.data?.data || [];

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { id, clauseId } = useParams();

  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName, address, mailingAddress } =
          responseData?.data?.data;
        setNamaPertubuhan(societyName);
        setSocietyId(id);
      },
    },
  });

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);
 
  useEffect(() => {
    if (societyDataRedux && clause && clause.constitutionValues.length <= 0) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
      setFormData({ 
        stateCode: societyDataRedux.stateCode,
        districtCode: societyDataRedux.districtCode,
        city: societyDataRedux.city,
        postcode: societyDataRedux.postcode,
        address: societyDataRedux.address,
        mailingAddress: societyDataRedux.mailingAddress,
        mailingStateCode: societyDataRedux.mailingStateCode,
        mailingDistrictCode: societyDataRedux.mailingDistrictCode,
        mailingCity: societyDataRedux.mailingCity,
        mailingPostcode: societyDataRedux.mailingPostcode,     
      });

      const isAddressMatching =
      societyDataRedux.stateCode === societyDataRedux.mailingStateCode &&
      societyDataRedux.districtCode ===
        societyDataRedux.mailingDistrictCode &&
      societyDataRedux.city === societyDataRedux.mailingCity &&
      societyDataRedux.postcode === societyDataRedux.mailingPostcode &&
      societyDataRedux.address === societyDataRedux.mailingAddress;
      setBusinessState(societyDataRedux.stateCode); 
      setBusinessDistrict(societyDataRedux.districtCode);  
      setMailingState(societyDataRedux.mailingStateCode);
      setMailingDistrict(societyDataRedux.mailingDistrictCode); 
      setSameAddress(isAddressMatching);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause2Data = JSON.parse(clause2);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause2Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      } 

      if(clause.constitutionValues.length > 0){
        const fieldMappings: Record<string, (value: string) => void> = { 
          "Alamat Tempat Urusan": (value) => setFormData(prev => ({...prev, address: value})),
          "Negeri": (value) => {
            setBusinessState(value)
            setFormData(prev => ({...prev, stateCode: value}))},
          "Daerah": (value) => {
            setBusinessDistrict(value)
            setFormData(prev => ({...prev, districtCode: value}))},
          "Bandar": (value) => setFormData(prev => ({...prev, city: value})),
          "Poskod": (value) => setFormData(prev => ({...prev, postcode: value})),
          "Alamat Surat Menyurat": (value) => setFormData(prev => ({...prev, mailingAddress: value})),
          "Negeri Surat Menyurat": (value) => {
            setMailingState(value)
            setFormData(prev => ({...prev, mailingStateCode: value}))},
          "Daerah Surat Menyurat": (value) => {
            setMailingDistrict(value)
            setFormData(prev => ({...prev, mailingDistrictCode: value}))},
          "Bandar Surat Menyurat": (value) => setFormData(prev => ({...prev, mailingCity: value})),
          "Poskod Surat Menyurat": (value) => setFormData(prev => ({...prev, mailingPostcode: value})),
        };
           if(clause.constitutionValues){
          clause.constitutionValues.forEach((item:any) => {
            const setter = fieldMappings[item.titleName];
            if (setter && item.definitionName) {
              setter(item.definitionName);
            }
          });
        }
   
        setIsEdit(clause.edit);
      }
 
      setAsal(
        asalData.find((item: any) => item.clauseNo === clause.clauseNo
        ) || ""
      );
    }
  }, [clause]);

  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};
    if (!formData.stateCode) errors.stateCode = t("requiredValidation");
    if (!formData.districtCode) errors.districtCode = t("requiredValidation");
    if (!formData.postcode) errors.postcode = t("requiredValidation");
    if (!formData.address) errors.address = t("requiredValidation");
    if (!formData.mailingStateCode)
      errors.mailingStateCode = t("requiredValidation");
    if (!formData.mailingDistrictCode)
      errors.mailingDistrictCode = t("requiredValidation");
    if (!formData.mailingPostcode)
      errors.mailingPostcode = t("requiredValidation");

    if (!formData.mailingAddress) {
      errors.mailingAddress = t("requiredValidation");
    }
    // Add postcode validation
    if (!formData.postcode) {
      errors.postcode = t("requiredValidation");
    } else if (!/^\d{5}$/.test(formData.postcode)) {
      errors.postcode = t("postcodeValidation");
    }

    if (!formData.mailingPostcode) {
      errors.mailingPostcode = t("requiredValidation");
    } else if (!/^\d{5}$/.test(formData.mailingPostcode)) {
      errors.mailingPostcode = t("postcodeValidation");
    }

    return errors;
  };

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);
  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<ALAMAT TEMPAT URUSAN>>/gi,
    `<b>${formData.address || "<<ALAMAT TEMPAT URUSAN>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<NEGERI>>/gi,
    `<b>${getStateNameById(formData.stateCode) || "<<NEGERI>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<DAERAH>>/gi,
    `<b>${toTitleCase(getDistrictNameById(formData.districtCode)) || "<<DAERAH>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<BANDAR>>/gi,
    `<b>${formData.city || "<<BANDAR>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<POSKOD>>/gi,
    `<b>${formData.postcode || "<<POSKOD>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<ALAMAT SURAT MENYURAT>>/gi,
    `<b>${formData.mailingAddress || "<<ALAMAT SURAT MENYURAT>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<NEGERI SURAT MENYURAT>>/gi,
    `<b>${getStateNameById(formData.mailingStateCode)  || "<<NEGERI SURAT MENYURAT>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<DAERAH SURAT MENYURAT>>/gi,
    `<b>${toTitleCase(getDistrictNameById(formData.mailingDistrictCode)) || "<<DAERAH SURAT MENYURAT>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<BANDAR SURAT MENYURAT>>/gi,
    `<b>${formData.mailingCity || "<<BANDAR SURAT MENYURAT>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<POSKOD SURAT MENYURAT>>/gi,
    `<b>${formData.mailingPostcode || "<<POSKOD SURAT MENYURAT>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");
  return (
    <>
      <AsalContent
        asalData={asal?.description || ""}
        clauseId={clauseId}
      name={asal?.clauseName}
      />

     <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        border: "1px solid #D9D9D9",
        backgroundColor: "#FFFFFF",
        borderRadius: "14px",
        mb: 2,
      }}
    >
      <Typography variant="subtitle1" sx={sectionStyle}>
        {t("alamatTempatUrusan1")}
      </Typography>

      <Grid container spacing={2}>
        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>
            {t("alamatTempatUrusan")} <span style={{ color: "red" }}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            fullWidth
            size="small"
            required
            disabled={isViewMode}
            name="address"
            value={formData.address}
            onChange={handleInputChange}
            error={!!formErrors.address}
            helperText={formErrors.address}
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>
            {t("state")} <span style={{ color: "red" }}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <FormControl fullWidth required error={!!formErrors.stateCode}>
            <Select
              value={formData.stateCode}
              displayEmpty
              size="small"
              required
              disabled={isLoading || isViewMode}
              onChange={(e) => {
                setBusinessState(e.target.value);
                setSameAddress(false);
                setFormData((prevState) => ({
                  ...prevState,
                  stateCode: e.target.value,
                }));
                setFormErrors((prev) => ({ ...prev, stateCode: "" }));
              }}
            >
              <MenuItem value="" disabled>
                {isLoading ? "Loading..." : t("pleaseSelect")}
              </MenuItem>
              {!isLoading &&
                addressData
                  .filter((item: any) => item.pid === MALAYSIA)
                  .map((item: any) => (
                    <MenuItem key={item.id} value={`${item.id}`}>
                      {capitalizeWords(item.name, null, true)}
                    </MenuItem>
                  ))}
            </Select>
            {formErrors.stateCode && (
              <FormHelperText>{formErrors.stateCode}</FormHelperText>
            )}
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>
            {t("district")} <span style={{ color: "red" }}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <FormControl fullWidth required error={!!formErrors.districtCode}>
            <Select
              value={formData.districtCode}
              displayEmpty
              size="small"
              required
              onChange={(e) => {
                setBusinessDistrict(e.target.value);
                setSameAddress(false);
                setFormData((prevState) => ({
                  ...prevState,
                  districtCode: e.target.value,
                }));
                setFormErrors((prev) => ({ ...prev, districtCode: "" }));
              }}
              disabled={isLoading || !businessState || isViewMode}
            >
              <MenuItem value="" disabled>
                {isLoading ? "Loading..." : t("selectPlaceholder")}
              </MenuItem>
              {!isLoading &&
                addressData
                  .filter((item: any) => item.pid == businessState)
                  .map((item: any) => (
                    <MenuItem key={item.id} value={`${item.id}`}>
                      {capitalizeWords(item.name, null, true)}
                    </MenuItem>
                  ))}
            </Select>
            {formErrors.districtCode && (
              <FormHelperText>{formErrors.districtCode}</FormHelperText>
            )}
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>{t("city")}</Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size="small"
            fullWidth
            name="city"
            disabled={isViewMode}
            value={formData.city}
            error={!!formErrors.city}
            helperText={formErrors.city}
            onChange={handleInputChange}
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>
            {t("postcode")} <span style={{ color: "red" }}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size="small"
            fullWidth
            required
            disabled={isViewMode}
            name="postcode"
            value={formData.postcode}
            error={!!formErrors.postcode}
            helperText={formErrors.postcode || t("postcodeHelper")}
            onChange={(e: any) => {
              if (/^\d{0,5}$/.test(e.target.value)) {
                handleInputChange(e);
              }
            }}
            inputProps={{ pattern: "\\d{5}" }}
          />
        </Grid>
      </Grid>
    </Box>

    <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        border: "1px solid #D9D9D9",
        backgroundColor: "#FFFFFF",
        borderRadius: "14px",
        mb: 2,
      }}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyleCustom}>
          {t("mailingAddress")}
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {" "}
          {/* Add spacing */}
          <Typography
            variant="body2"
            sx={{ fontSize: "12px", color: "#66666680" }}
          >
            {t("sameAsAbove")}
          </Typography>
          <Switch checked={sameAddress} onChange={handleSwitchOnChange} />
        </Box>
      </Box>
      {!sameAddress && (
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("businessAddress")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <TextField
              fullWidth
              required
              size="small"
              disabled={sameAddress || isViewMode}
              error={!!formErrors.mailingAddress}
              helperText={formErrors.mailingAddress}
              name="mailingAddress"
              value={formData.mailingAddress}
              onChange={handleInputChange}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("state")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.mailingStateCode}
            >
              <Select
                value={mailingState}
                displayEmpty
                required
                size="small"
                disabled={isLoading || sameAddress || isViewMode}
                onChange={(e) => {
                  setMailingState(e.target.value);
                  setFormData((prevState) => ({
                    ...prevState,
                    mailingStateCode: e.target.value,
                  }));
                  setFormErrors((prev) => ({ ...prev, mailingStateCode: "" }));
                }}
              >
                <MenuItem value="" disabled>
                  {isLoading ? "Loading..." : t("pleaseSelect")}
                </MenuItem>
                {!isLoading &&
                  addressData
                    .filter((item: any) => item.pid === MALAYSIA)
                    .map((item: any) => (
                      <MenuItem key={item.id} value={`${item.id}`}>
                        {capitalizeWords(item.name, null, true)}
                      </MenuItem>
                    ))}
              </Select>
              {formErrors.mailingStateCode && (
                <FormHelperText>{formErrors.mailingStateCode}</FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("district")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <FormControl
              fullWidth
              required
              error={!!formErrors.mailingDistrictCode}
            >
              <Select
                value={mailingDistrict}
                displayEmpty
                size="small"
                required
                onChange={(e) => {
                  setMailingDistrict(e.target.value);
                  setFormData((prevState) => ({
                    ...prevState,
                    mailingDistrictCode: e.target.value,
                  }));
                  setFormErrors((prev) => ({ ...prev, mailingDistrictCode: "" }));
                }}
                disabled={isLoading || !mailingState || sameAddress || isViewMode}
              >
                <MenuItem value="" disabled>
                  {isLoading ? "Loading..." : t("selectPlaceholder")}
                </MenuItem>
                {!isLoading &&
                  addressData
                    .filter((item: any) => item.pid == mailingState)
                    .map((item: any) => (
                      <MenuItem key={item.id} value={`${item.id}`}>
                        {capitalizeWords(item.name, null, true)}
                      </MenuItem>
                    ))}
              </Select>
              {formErrors.mailingDistrictCode && (
                <FormHelperText>
                  {formErrors.mailingDistrictCode}
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>{t("city")}</Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <TextField
              fullWidth
              name="mailingCity"
              size="small"
              value={formData.mailingCity}
              error={!!formErrors.mailingCity}
              helperText={formErrors.mailingCity}
              onChange={handleInputChange}
              disabled={sameAddress || isViewMode}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("postcode")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <TextField
              fullWidth
              required
              size="small"
              name="mailingPostcode"
              value={formData.mailingPostcode}
              error={!!formErrors.mailingPostcode}
              helperText={formErrors.mailingPostcode || t("postcodeHelper")}
              onChange={(e: any) => {
                if (/^\d{0,5}$/.test(e.target.value)) {
                  handleInputChange(e);
                }
              }}
              inputProps={{ pattern: "\\d{5}" }}
              disabled={sameAddress || isViewMode}
            />
          </Grid>
        </Grid>
      )}
    </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}
      <Grid
        item
        xs={12}
        sx={{ mt: 2, display: "flex", justifyContent: "flex-end", gap: 1 }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              const errors = validateForm();
              if (Object.keys(errors).length > 0) {
                setFormErrors(errors);
                return;
              }
              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: formData.address,
                    titleName: "Alamat Tempat Urusan",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: formData.stateCode ? formData.stateCode : "",
                    titleName: "Negeri",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: formData.districtCode ? formData.districtCode : "",
                    titleName: "Daerah",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: formData.city,
                    titleName: "Bandar",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: formData.postcode,
                    titleName: "Poskod",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: formData.mailingAddress,
                    titleName: "Alamat Surat Menyurat",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    // definitionName: formData.mailingStateCode ? getStateNameById(formData.mailingStateCode) : "",
                    definitionName: formData.mailingStateCode ? formData.mailingStateCode : "",
                    titleName: "Negeri Surat Menyurat",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    // definitionName: formData.mailingDistrictCode ? toTitleCase(getDistrictNameById(formData.mailingDistrictCode)) : "",
                    definitionName: formData.mailingDistrictCode ? formData.mailingDistrictCode : "",
                    titleName: "Daerah Surat Menyurat",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: formData.mailingCity,
                    titleName: "Bandar Surat Menyurat",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: formData.mailingPostcode,
                    titleName: "Poskod Surat Menyurat",
                  },
                ],
                clause: "clause2",
                clauseCount: 2,
              });
            }}
            disabled={isCreatingContent || isEditingContent || !checked}
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentDua;
