import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { API_URL } from "../../../../../api";
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import { RegExNumbers } from "../../../../../helpers/enums";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
interface FasalContentDuaBelasFaedahProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentDuaBelasFaedah: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [bilanganOrangPemegangAmanah, setBilanganOrangPemegangAmanah] =
    useState("");

  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  /*const clauseContent = `
              1. ${
                bilanganOrangPemegangAmanah ||
                "<<bilangan orang pemegang amanah>>"
              } orang pemegang amanah yang berumur lebih 21 tahun hendaklah dilantik dalam Mesyuarat Agung dan akan berkhidmat selama yang dikehendaki oleh Pertubuhan. Semua harta tetap dan pelaburan kumpulan wang Pertubuhan akan diamanahkan kepada pemegang amanah. Ahli yang memegang jawatan Setiausaha dan Bendahari dalam Pertubuhan ini tidak boleh dilantik menjadi pemegang amanah.

2. Satu (1) salinan bagi setiap pelantikan pemegang amanah hendaklah ditandatangani oleh pemegang amanah tersebut dan disahkan oleh Setiausaha sebelum dihantar kepada Pendaftar Pertubuhan.

3. Pemegang amanah boleh dilucutkan jawatan dalam Mesyuarat Agung atas sebab masalah kesihatan, tidak siuman, tidak berada dalam negeri, tidak dapat menjalankan tugas atau pekerjaannya dengan memuaskan. Jika berlaku kematian, peletakan jawatan atau pelucutan jawatan seseorang pemegang amanah, maka kekosongan itu boleh diisi oleh pemegang amanah baharu yang dilantik dalam Mesyuarat Agung.

            `;*/

  //const clause12 = localStorage.getItem("clause12");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      //const clause12Data = JSON.parse(clause12);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause10Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause12Data.societyName);
      setBilanganOrangPemegangAmanah(
        clause.constitutionValues[0]?.definitionName
      );
      setIsEdit(clause.edit);
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id } = useParams();

  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<bilangan orang pemegang amanah>>/gi,
    `<b>${
      bilanganOrangPemegangAmanah || "<<bilangan orang pemegang amanah>>"
    }</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");
  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("generalMeeting")}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("bilanganOrangPemegangAmanah")}
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              size="small"
              fullWidth
              value={bilanganOrangPemegangAmanah}
              onChange={(e) => {
                if (RegExNumbers.test(e.target.value)) {
                  setBilanganOrangPemegangAmanah(e.target.value);
                } else {
                  setBilanganOrangPemegangAmanah("");
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bilanganOrangPemegangAmanah,
                  titleName: "Bilangan Orang Pemegang Amanah",
                },
              ],
              clause: "clause12",
              clauseCount: 12,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentDuaBelasFaedah;
