import React from "react";
import { Box, Grid, Typography } from "@mui/material";
import { PaticipantsIcon } from "@/components/icons/participants";
import { DurationIcon } from "@/components/icons/duration";
import { TrainingRequiredIcon } from "@/components/icons/trainingRequired";
import TrainingFragment from "@/pages/training/trainingFragment";
import { TrainingEnums } from "@/helpers";
import { ButtonPrimary } from "@/components";
import { useCustom } from "@refinedev/core";
import { API_URL } from "@/api";


const TrainingModule: React.FC = () => {

  const sampleImages = [
    "images1.png",
    "images2.png",
    "images3.png",
    "images4.png",
    "images1.png",
    "images2.png",
    "images3.png",
    "images4.png",
  ]

  const sampleColors = [
    "#FFD10080",
    "#9747FFB2",
    "#CE4444B2",
    "#FFD10080",
    "#9747FFB2",
    "#CE4444B2",
  ]

  const shuffleArray = (arr: string[]): string[] => {
    for (let i = arr.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [arr[i], arr[j]] = [arr[j], arr[i]];
    }
    //console.log("random", arr);
    return arr;
  }

  const baseItem = {
    required: true,
    requiredIcon: <TrainingRequiredIcon />,
    image: '',
    type: "Kepimpinan",
    title: "Kursus Induksi Jabatan Pendaftaran Pertubuhan Malaysia (JPPM)",
    level: "Tahap Mudah",
    levelColor: "",
    points: 0,
    participants: 0,
    participantsIcon: <PaticipantsIcon />,
    duration: "1.5 Jam pembelajaran",
    durationIcon: <DurationIcon />,
    progress: 0
  }

  /*const latihanSayaArray = [];
  const dilihatTerkiniArray = [];
  const modulLatihanArray = [];
  let randomImages = shuffleArray(sampleImages);
  let randomColors = shuffleArray(sampleColors);
  for (let i = 0; i < 2; ++i) {
    const temp = Object.create(baseItem);
    temp.image = `/latihanSample/${randomImages[i]}`;
    temp.levelColor = randomColors[i];
    temp.progress = ~~(Math.random() * 100);
    temp.points = ~~(Math.random() * 100);
    temp.participants = ~~(Math.random() * 100000);
    latihanSayaArray.push(temp);
  }
  randomImages = shuffleArray(sampleImages);
  randomColors = shuffleArray(sampleColors);
  for (let i = 0; i < 5; ++i) {
    const temp = Object.create(baseItem);
    temp.image = `/latihanSample/${randomImages[i]}`;
    temp.levelColor = randomColors[i];
    temp.points = ~~(Math.random() * 100);
    temp.participants = ~~(Math.random() * 100000);
    dilihatTerkiniArray.push(temp);
  }
  randomImages = shuffleArray(sampleImages);
  randomColors = shuffleArray(sampleColors);
  for (let i = 0; i < 5; ++i) {
    const temp = Object.create(baseItem);
    temp.image = `/latihanSample/${randomImages[i]}`;
    temp.levelColor = randomColors[i];
    temp.points = ~~(Math.random() * 100);
    temp.participants = ~~(Math.random() * 100000);
    modulLatihanArray.push(temp);
  }*/

  const { data: enrolledTrainingData, isLoading: isEnrolledTrainingLoading } = useCustom({
    url: `${API_URL}/society/training/enrollments`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const enrolledTraining = enrolledTrainingData?.data?.data || [];
  console.log("enrolledTraining", enrolledTraining)

  const { data: publishedTrainingData, isLoading: isPublishedTrainingLoading } = useCustom({
    url: `${API_URL}/society/training/courses`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const publishedTraining = publishedTrainingData?.data?.data || [];
  console.log("publishedTraining", publishedTraining)

  // @ts-ignore
  // @ts-ignore
  // @ts-ignore
  // @ts-ignore
  return (
    <>
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        {enrolledTraining.length > 0 ?
          <Box
            sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              border: "1px solid #D9D9D9",
              //flex: 5,
              //display: "inline",
              px: 2,
              py: 2,
              mb: 1,
            }}
          >
            <Typography
              sx={{
                color: "var(--primary-color)",
                pt: 3,
                fontWeight: "500",
                fontSize: 14,
              }}
            >
              Latihan Saya
            </Typography>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                backgroundColor: "white",
                paddingBottom: "8px",
                //justifyContent: "space-evenly",
                borderRadius: "10px",
                px: 1,
                py: 1,
                mb: 1,
              }}
            >
              <Grid container spacing={2}>
                {enrolledTraining.map((item: any, index: number) => (
                  <Grid item xs={3}>
                    <TrainingFragment key={index} item={item} type={TrainingEnums.Assigned} />
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Box> : <></>}
      </Box>
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              pt: 3,
              fontWeight: "500",
              fontSize: 14,
            }}
          >
            Dilihat Terkini
          </Typography>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "white",
              paddingBottom: "8px",
              //justifyContent: "space-evenly",
              borderRadius: "10px",
              px: 1,
              py: 1,
              mb: 1,
            }}
          >
            <Grid container spacing={2}>
              {publishedTraining.map((item: any, index: number) => (
                <Grid item xs={3}>
                  <TrainingFragment key={index} item={item} type={TrainingEnums.History} />
                </Grid>
              ))}
            </Grid>
          </Box>
          <Box sx={{ mt: 2, display: "flex", justifyContent: "center" }}>
            <ButtonPrimary
              variant="outlined"
              sx={{
                bgcolor: "white",
                "&:hover": { bgcolor: "white" },
                color: "#0CA6A6",
                fontWeight: "400",
              }}
            //onClick={handleCloseFilterModal}
            >
              Tunjuk selebihnya
            </ButtonPrimary>
          </Box>
        </Box>
      </Box>
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              pt: 3,
              fontWeight: "500",
              fontSize: 14,
            }}
          >
            Modul Latihan
          </Typography>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "white",
              paddingBottom: "8px",
              //justifyContent: "space-evenly",
              borderRadius: "10px",
              px: 1,
              py: 1,
              mb: 1,
            }}
          >
            <Grid container spacing={2}>
              {publishedTraining.map((item: any, index: number) => (
                <Grid item xs={3}>
                  <TrainingFragment key={index} item={item} type={TrainingEnums.All} />
                </Grid>
              ))}
            </Grid>
          </Box>
          <Box sx={{ mt: 2, display: "flex", justifyContent: "center" }}>
            <ButtonPrimary
              variant="outlined"
              sx={{
                bgcolor: "white",
                "&:hover": { bgcolor: "white" },
                color: "#0CA6A6",
                fontWeight: "400",
              }}
            //onClick={handleCloseFilterModal}
            >
              Lihat lebih lanjut
            </ButtonPrimary>
          </Box>
        </Box>
      </Box>
    </>
  );
}

export default TrainingModule;
