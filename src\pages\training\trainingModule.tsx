import React, {useEffect, useState} from "react";
import {Box, Grid, Typography} from "@mui/material";
import {PaticipantsIcon} from "@/components/icons/participants";
import {DurationIcon} from "@/components/icons/duration";
import {TrainingRequiredIcon} from "@/components/icons/trainingRequired";
import TrainingFragment from "@/pages/training/trainingFragment";
import {TrainingEnums} from "@/helpers";
import {ButtonPrimary} from "@/components";
import {useCustom} from "@refinedev/core";
import {API_URL} from "@/api";
import {useTranslation} from "react-i18next";

export interface TrainingModuleProps {
  searchValue: string,
  enrolledTraining: any[]
}

const TrainingModule: React.FC<TrainingModuleProps> = ({searchValue, enrolledTraining}) => {

  const {t, i18n} = useTranslation();

  const [publishedTraining, setPublishedTraining] = useState<any[]>([])
  const [publishedTrainingHistory, setPublishedTrainingHistory] = useState<any[]>([])
  const [showMore, setShowMore] = useState(false);
  const [filter, setFilter] = useState("all");
  const [allPublishedTraining, setAllPublishedTraining] = useState<any[]>([])

  const {data: publishedTrainingData, isLoading: isPublishedTrainingLoading, refetch: refetchTraining} = useCustom({
    url: `${API_URL}/society/training/courses`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      filters: [
        {
          field: "title",
          operator: "eq",
          value: searchValue,
        },
      ],
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const tempPublishedTraining = publishedTrainingData?.data?.data || [];
  //console.log("publishedTraining", tempPublishedTraining)

  const {
    data: publishedTrainingHistoryData,
    isLoading: isPublishedTrainingHistoryLoading,
    refetch: refetchTrainingHistory
  } = useCustom({
    url: `${API_URL}/society/training/courses-history`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      filters: [
        {
          field: "title",
          operator: "eq",
          value: searchValue,
        },
      ],
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const tempPublishedTrainingHistory = publishedTrainingHistoryData?.data?.data || [];
  //console.log("publishedTrainingHistory", tempPublishedTraining)

  useEffect(() => {
    //refetchTraining()
    //refetchTrainingHistory()
    let tempA = [];
    let tempB = [];
    const regex = new RegExp(searchValue);
    if(searchValue){
      tempA = tempPublishedTraining.filter((a:any) => {
        return regex.test(a.title);
      });
      tempB = tempPublishedTrainingHistory.filter((a:any) => {
        return regex.test(a.title);
      });
    }else{
      tempA = tempPublishedTraining;
      tempB = tempPublishedTrainingHistory
    }
    setPublishedTraining(tempA);
    setPublishedTrainingHistory(tempB);
    setAllPublishedTraining(tempA);
  }, [searchValue])

  useEffect(() => {
    if (tempPublishedTraining.length > 0) {
      setPublishedTraining(tempPublishedTraining);
      setAllPublishedTraining(tempPublishedTraining);
    }
  }, [publishedTrainingData])

  useEffect(() => {
    if (tempPublishedTrainingHistory.length > 0) {
      setPublishedTrainingHistory(tempPublishedTrainingHistory);
    }
  }, [publishedTrainingHistoryData])

  const tabs = [
    {label: "Semua", filter: "all"},
    {label: "Asas", filter: "asas"},
    {label: "Sederhana", filter: "sederhana"},
    {label: "Mahir", filter: "mahir"},
  ];

  const handleFilter = (val: any) => {
    setFilter(val)
  }

  useEffect(() => {
    let temp = [];
    if(filter != 'all'){
      temp = tempPublishedTraining.filter((a:any) => {
        return a.difficultyLevel.toLowerCase() === filter;
      })
    }else{
      temp = tempPublishedTraining;
    }
    setAllPublishedTraining(temp);
  },[filter])

  return (
    <>
      {!showMore ?
        <>
          <Box
            sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              //flex: 5,
              //display: "inline",
              px: 2,
              py: 2,
              mb: 1,
            }}
          >
            {enrolledTraining.length > 0 ?
              <Box
                sx={{
                  borderRadius: 2.5,
                  backgroundColor: "#fff",
                  border: "1px solid #D9D9D9",
                  //flex: 5,
                  //display: "inline",
                  px: 2,
                  py: 2,
                  mb: 1,
                }}
              >
                <Typography
                  sx={{
                    color: "var(--primary-color)",
                    pt: 3,
                    fontWeight: "500",
                    fontSize: 14,
                  }}
                >
                  {t("myTraining")}
                </Typography>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    backgroundColor: "white",
                    paddingBottom: "8px",
                    //justifyContent: "space-evenly",
                    borderRadius: "10px",
                    px: 1,
                    py: 1,
                    mb: 1,
                  }}
                >
                  <Grid container spacing={2}>
                    {enrolledTraining.filter((t) => t.completionStatus === "IN_PROGRESS").map((item: any, index: number) => (
                      <Grid item xs={3} key={index}>
                        <TrainingFragment item={item} type={TrainingEnums.Assigned}/>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              </Box> : <></>}
          </Box>
          <Box
            sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              //flex: 5,
              //display: "inline",
              px: 2,
              py: 2,
              mb: 1,
            }}
          >
            <Box
              sx={{
                borderRadius: 2.5,
                backgroundColor: "#fff",
                border: "1px solid #D9D9D9",
                //display: "inline",
                px: 2,
                py: 2,
                mb: 1,
              }}
            >
              <Typography
                sx={{
                  color: "var(--primary-color)",
                  pt: 3,
                  fontWeight: "500",
                  fontSize: 14,
                }}
              >
                {t("lastSeen")}
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  backgroundColor: "white",
                  paddingBottom: "8px",
                  //justifyContent: "space-evenly",
                  borderRadius: "10px",
                  px: 1,
                  py: 1,
                  mb: 1,
                }}
              >
                <Grid container spacing={2}>
                  {publishedTrainingHistory.map((item: any, index: number) => (
                    <Grid item xs={3} key={index}>
                      <TrainingFragment item={item} type={TrainingEnums.History} height={350}/>
                    </Grid>
                  ))}
                </Grid>
              </Box>
              <Box sx={{mt: 2, display: "flex", justifyContent: "center"}}>
                <ButtonPrimary
                  variant="outlined"
                  sx={{
                    bgcolor: "white",
                    "&:hover": {bgcolor: "white"},
                    color: "#0CA6A6",
                    fontWeight: "400",
                  }}
                  onClick={() => setShowMore(true)}
                >
                  {t("showMore")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>
          <Box
            sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              //flex: 5,
              //display: "inline",
              px: 2,
              py: 2,
              mb: 1,
            }}
          >
            <Box
              sx={{
                borderRadius: 2.5,
                backgroundColor: "#fff",
                border: "1px solid #D9D9D9",
                //display: "inline",
                px: 2,
                py: 2,
                mb: 1,
              }}
            >
              <Typography
                sx={{
                  color: "var(--primary-color)",
                  pt: 3,
                  fontWeight: "500",
                  fontSize: 14,
                }}
              >
                {t("TRAINING_MODULE")}
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  backgroundColor: "white",
                  paddingBottom: "8px",
                  //justifyContent: "space-evenly",
                  borderRadius: "10px",
                  px: 1,
                  py: 1,
                  mb: 1,
                }}
              >
                <Grid container spacing={2}>
                  {publishedTraining.map((item: any, index: number) => (
                    <Grid item xs={3} key={index}>
                      <TrainingFragment item={item} type={TrainingEnums.All} height={350}/>
                    </Grid>
                  ))}
                </Grid>
              </Box>
              <Box sx={{mt: 2, display: "flex", justifyContent: "center"}}>
                <ButtonPrimary
                  variant="outlined"
                  sx={{
                    bgcolor: "white",
                    "&:hover": {bgcolor: "white"},
                    color: "#0CA6A6",
                    fontWeight: "400",
                  }}
                  onClick={() => setShowMore(true)}
                >
                  {t("showMore")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box></> :
        <Box>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              backgroundColor: "white",
              paddingBottom: "8px",
              justifyContent: "space-evenly",
              borderRadius: "10px",
              px: 2,
              py: 1,
              mb: 1,
            }}
          >
            {tabs.map((tab, index) => {
              // Tentukan apakah tab saat ini aktif berdasarkan URL
              const isActive = filter === tab.filter;
              return (
                <React.Fragment key={index}>
                  <Box
                    sx={{
                      flex: 1,
                      backgroundColor: isActive ? "#0CA6A6" : "#FFFFFF",
                      p: 1,
                      //mx:1,
                      borderRadius: "5px",
                    }}>
                    <Box
                      key={index}
                      onClick={() => {
                        //handleNavigation(tab.path)
                        handleFilter(tab.filter)
                      }}
                      sx={{
                        cursor: "pointer",
                        color: isActive ? "#FFFFFF" : "#666666",
                        transition: "color 0.3s, border-bottom 0.3s",
                      }}
                    >
                      <Typography sx={{fontWeight: "400 !important", textAlign: "center", fontSize: "14px"}}>
                        {tab.label}
                      </Typography>
                    </Box>
                  </Box>
                </React.Fragment>
              );
            })}</Box>
          <Box
            sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              //display: "inline",
              px: 2,
              py: 2,
              mb: 1,
            }}
          >
            <Typography
              sx={{
                color: "var(--primary-color)",
                pt: 3,
                fontWeight: "500",
                fontSize: 14,
              }}
            >
              {t("TRAINING_MODULE")}
            </Typography>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                backgroundColor: "white",
                paddingBottom: "8px",
                //justifyContent: "space-evenly",
                borderRadius: "10px",
                px: 1,
                py: 1,
                mb: 1,
              }}
            >
              <Grid container spacing={2}>
                {allPublishedTraining.map((item: any, index: number) => (
                  <Grid item xs={3} key={index}>
                    <TrainingFragment item={item} type={TrainingEnums.All} height={350}/>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Box>
        </Box>
      }
    </>
  );
}

export default TrainingModule;
