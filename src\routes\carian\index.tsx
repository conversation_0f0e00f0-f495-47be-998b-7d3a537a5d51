import { Route, Outlet } from "react-router-dom";
import Carian from "../../pages/carian";
import CarianMaklumat from "../../pages/carian/carianMaklumat";
import Online from "../../pages/carian/Bayaran/Online";
import Term from "../../pages/carian/Bayaran/Term";
import Butiran from "../../pages/carian/Bayaran/Butiran";
import ListBayaran from "../../pages/carian/Bayaran/ListBayaran";
import Kaunter from "../../pages/carian/Bayaran/Kaunter";
import { registerRoutes } from "../../helpers/routeDetector";
import { RouteGuard } from "../../components/RouteGuard";

// Layout component to wrap all carian routes with protection
const CarianLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Outlet />
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example: '/carian': 'external',
  // Example: '/carian/maklumat': 'external',
  // Example: '/carian/maklumat/bayaran': 'external',
  // Add your route registrations here
});

// Recommended: Layout-based protection (more efficient and cleaner)
export const carian = {
  routes: (
    <>
      <Route path="/carian" element={<CarianLayout />}>
        <Route index element={<Carian />} />
        <Route path="maklumat">
          <Route index element={<CarianMaklumat />} />
          <Route path="bayaran">
            <Route index element={<ListBayaran />} />
            <Route path="kaunter" element={<Kaunter />} />
            <Route path="online">
              <Route index element={<Online />} />
              <Route path="term" element={<Term />} />
              <Route path="butiran" element={<Butiran />} />
            </Route>
          </Route>
        </Route>
      </Route>
    </>
  ),
};
