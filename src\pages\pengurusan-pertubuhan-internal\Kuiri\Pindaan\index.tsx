import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  Grid,
  TextField,
  Typography,
  Dialog,
  DialogContent,
  useMediaQ<PERSON>y,
  useTheme,
  Button,
  Theme,
} from "@mui/material";
import MaklumatPemohonSection from "./views/MaklumatPemohonSection";
import MaklumatAmSection from "./views/MaklumatAmSection";
import MaklumatPerlembagaanSection from "./views/MaklumatPerlembagaanSection";
// import DialogConfirmation from "./views/DialogConfirmation";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import {
  ApplicationStatusList,
  DecisionOptionsCode,
  NEW_PermissionNames,
  pageAccessEnum,
  ROApprovalType,
} from "@/helpers/enums";
import useMutation from "@/helpers/hooks/useMutation";
import CheckIcon from "@mui/icons-material/Check";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
// import TextArea from "@/components/input/TextArea";
import { FieldValues, useForm } from "react-hook-form";
import {
  DialogConfirmation,
  SelectFieldController,
  TextFieldController,
} from "@/components";
import AccordionComp from "../../View/Accordion";
import AuthHelper from "@/helpers/authHelper";

const labelStyle = {
  fontSize: "16px",
  color: "#666666",
  fontWeight: "400 !important",
};

const subTitleStyle = {
  color: "#0CA6A6",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

type ReadStatusType = {
  [key: number]: boolean;
};

function KuiriPindaanDetails() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { amendmentId, id } = useParams();
  const decodedId = atob(id || "");
  const hasUpdatePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children.KUIRI
      .children.PINDAAN_PERLEMBAGAAN_KUIRI.label,
    pageAccessEnum.Update
  );

  const [dialogSejarahKuiriSaveOpen, setDialogSejarahKuiriSaveOpen] =
    useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));
  const [query, setQuery] = useState([]);
  const [readStatus, setReadStatus] = useState<ReadStatusType>({});
  const decisionOptions = DecisionOptionsCode(t);
  const [societyName, setSocietyName] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [amendmentInfo, setAmendmentInfo] = useState<any>(null);
  const [roExist, setRoExist] = useState(false);

  const {
    control,
    handleSubmit,
    getValues,
    watch,
    setValue,
    setError,
    formState: { errors },
  } = useForm<FieldValues>({
    defaultValues: {
      amendmentId: amendmentId,
      societyId: decodedId,
      applicationStatusCode: "",
      noteKuiri: "",
      roApprovalType: ROApprovalType.SOCIETY_AMENDMENT.code,
      note: "",
      rejectReason: "",
    },
  });
  const onSubmit = () => setIsDialogOpen(true);

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const { data } = useCustom({
    url: `${API_URL}/society/${decodedId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName } = responseData?.data?.data;
        setSocietyName(societyName);
      },
    },
  });

  const sectionItems = [
    {
      subTitle: t("generalInformation"),
      component: <MaklumatAmSection data={amendmentInfo} />,
    },
    {
      subTitle: t("constitutionInformation"),
      component: <MaklumatPerlembagaanSection />,
    },
    {
      subTitle: t("maklumatPemohon"),
      component: <MaklumatPemohonSection />,
    },
  ];

  const handleDialogClose = () => setIsDialogOpen(false);

  const handleChangeCurrentExpandSection =
    (item: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);

      if (isExpanded) {
        setReadStatus((prevState) => {
          const updatedStatus = sectionItems.reduce((acc, _, i) => {
            if (i + 1 <= item) {
              acc[i + 1] = true;
            } else {
              acc[i + 1] = !!prevState[i + 1] || false;
            }
            return acc;
          }, {} as Record<number, boolean>);
          return updatedStatus;
        });
      }
    };

  const { mutate: updateApprovalStatus, isLoading: isUpdatingStatus } =
    useCustomMutation();

  const handleFormSubmit = () => {
    const payload = getValues();
    updateApprovalStatus(
      {
        url: `${API_URL}/society/roDecision/updateApprovalStatus`,
        method: "patch",
        values: payload,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess(data, variables, context) {
          setIsSuccess(true);
          navigate(-1);
        },
      }
    );
  };

  const { mutate: getQuery, isLoading: isGetQuery } = useCustomMutation();
  const handleOpenKuiri = () => {
    setDialogSejarahKuiriSaveOpen(!dialogSejarahKuiriSaveOpen);
  };

  useEffect(() => {
    if (!amendmentInfo) return;

    const payload = {
      societyId: amendmentInfo.societyId,
      amendmentId: amendmentId,
      roApprovalType: ROApprovalType.SOCIETY_AMENDMENT.code,
      liquidationId: amendmentInfo.id,
    };
    getQuery(
      {
        method: "post",
        url: `${API_URL}/society/roQuery/getQuery`,
        values: payload,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess(data) {
          const queryData = data?.data?.data;
          setQuery(queryData);
          if (queryData?.length > 0) {
            setValue("noteKuiri", queryData[0].note || "");
          }
        },
      }
    );
  }, [amendmentInfo]);

  const methodsRoAction = useForm();

  const {
    handleSubmit: handleSubmitRoAction,
    control: controlRoAction,
    setValue: setValueRoAction,
    getValues: getValuesRoAction,
    watch: watchRoAction,
  } = methodsRoAction;

  const { data: roListData, isLoading: isRoListLoading } = useCustom({
    url: `${API_URL}/society/user/getRoList`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: decodedId,
      },
    },
  });

  const roList = roListData?.data?.data ?? [];

  const roListOptions =
    roList.map((item: any) => ({
      value: item.id,
      label: item.name,
    })) || [];

  const { mutate: updateRo, isLoading: isLoadingUpdateRO } =
    useCustomMutation();

  const onSubmitRoAction = (data: FieldValues) => {
    updateRo(
      {
        url: `${API_URL}/society/roDecision/updateRo`,
        method: "patch",
        values: {
          roId: data.ro,
          noteRo: data.noteRo,
          amendmentId: data.amendmentId ?? amendmentId,
          roApprovalType: ROApprovalType.SOCIETY_AMENDMENT.code,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  ///

  const { data: amendMentData, isLoading: amendMentDataLoading } = useCustom({
    url: `${API_URL}/society/amendment/getAmendmentByParam`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        id: amendmentId,
      },
    },
    queryOptions: {
      enabled: !!amendmentId,
      onSuccess(data) {
        const amendmenData = data?.data?.data?.data?.[0];
        setAmendmentInfo(amendmenData);
        setValueRoAction("ro", Number(amendmenData?.ro) || "");
        setValueRoAction("noteRo", (amendmenData as any)?.noteRo || "");
        if ((amendmenData as any)?.ro) {
          setRoExist(true);
        }
      },
    },
  });

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            {societyName}
          </Box>
        </Box>

        <Box sx={{ mt: 4 }}>
          {!amendMentDataLoading &&
            sectionItems.map((item, index) => {
              return (
                <AccordionComp
                  key={index}
                  subTitle={item.subTitle}
                  currentIndex={index + 1}
                  currentExpand={currentExpandSection}
                  readStatus={readStatus}
                  onChangeFunc={handleChangeCurrentExpandSection}
                >
                  {item.component}
                </AccordionComp>
              );
            })}
        </Box>
        {query.length > 0 && query ? (
          <Box
            sx={{
              backgroundColor: "white",
              p: 3,
              borderRadius: "15px",
              mt: 2,
            }}
          >
            <Box
              sx={{
                pl: 2,
                p: 3,
                mt: 1,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
              }}
            >
              <Box
                sx={{
                  mb: 3,
                }}
              >
                <Typography color={"primary"}>{t("kuiri")}</Typography>
              </Box>
              <Grid container>
                <Grid
                  item
                  xs={12}
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                    alignContent: "center",
                    pb: 4,
                  }}
                  gap={2}
                >
                  <Box
                    sx={{
                      background: "var(--primary-color)",
                      cursor: "pointer",
                      display: "flex",
                      justifyContent: "center",
                      alignContent: "center",
                      borderRadius: "10px",
                      p: 1,
                    }}
                  >
                    <img width={26} height={26} src="/addkuiri.svg" alt="" />
                  </Box>
                  <ButtonOutline onClick={() => handleOpenKuiri()}>
                    {t("queryHistory")}
                  </ButtonOutline>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("catatanKuiri")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    name="noteKuiri"
                    value={getValues("noteKuiri")}
                    disabled
                  />
                </Grid>
              </Grid>
            </Box>
          </Box>
        ) : (
          ""
        )}
        <Box
          sx={{
            backgroundColor: "white",
            p: 3,
            borderRadius: "15px",
            mt: 2,
          }}
        >
          <form onSubmit={handleSubmitRoAction(onSubmitRoAction)}>
            <Box
              sx={{
                p: 3,
                mb: 3,
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography variant="h6" component="h2" sx={subTitleStyle}>
                {t("ROAction")}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("responsibleRO")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <SelectFieldController
                    name="ro"
                    control={controlRoAction}
                    options={roListOptions}
                    disabled={!hasUpdatePermission || roExist}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("remarks")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={controlRoAction}
                    name="noteRo"
                    disabled={!hasUpdatePermission || roExist}
                    multiline
                    defaultValue={getValuesRoAction("noteRo")}
                    sx={{
                      minHeight: "92px",
                    }}
                    sxInput={{
                      minHeight: "92px",
                    }}
                  />
                </Grid>
              </Grid>
            </Box>

            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonPrimary
                variant="contained"
                type="submit"
                sx={{ width: isMobile ? "100%" : "auto", mb: 3 }}
                disabled={!hasUpdatePermission || isLoadingUpdateRO || roExist}
              >
                {t("update")}
              </ButtonPrimary>
            </Grid>
          </form>

          <form onSubmit={handleSubmit(onSubmit)}>
            <Box
              sx={{
                pl: 2,
                p: 3,
                mt: 1,
                borderRadius: "10px",
                border: "0.5px solid #dfdfdf",
              }}
            >
              <Box
                sx={{
                  mb: 3,
                }}
              >
                <Typography variant="h6" component="h2" sx={subTitleStyle}>
                  {t("keputusan")}
                </Typography>
              </Box>
              <Grid container>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("statusPermohonan")}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                  <SelectFieldController
                    control={control}
                    name="applicationStatusCode"
                    options={decisionOptions}
                    disabled={!hasUpdatePermission}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("remarks")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={control}
                    name="note"
                    multiline
                    sx={{
                      minHeight: "92px",
                    }}
                    sxInput={{
                      minHeight: "92px",
                    }}
                    required={watch("applicationStatusCode") === 36}
                    disabled={!hasUpdatePermission}
                  />
                </Grid>
              </Grid>
            </Box>

            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                marginTop: "34px",
                gap: "10px",
              }}
            >
              <Box
                sx={{
                  mt: 5,
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: 2,
                }}
              >
                <ButtonOutline sx={{ py: 1 }} onClick={() => navigate(-1)}>
                  {t("back2")}
                </ButtonOutline>
                <ButtonPrimary
                  disabled={!hasUpdatePermission}
                  type="submit"
                  sx={{ py: 1 }}
                >
                  {t("hantar")}
                </ButtonPrimary>
              </Box>
            </Box>
          </form>
        </Box>
      </Box>

      <Dialog
        open={dialogSejarahKuiriSaveOpen}
        onClose={() => setDialogSejarahKuiriSaveOpen(false)}
        fullScreen={fullScreen}
        PaperProps={{
          style: {
            borderRadius: "8px",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogContent sx={{ py: 4 }}>
          <Box
            sx={{
              p: 3,
              mt: 1,
              borderRadius: "10px",
              border: "0.5px solid #dfdfdf",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                pb: 4,
                color: "var(--primary-color)",
              }}
              gap={2}
            >
              <ChevronLeftIcon
                sx={{ cursor: "pointer" }}
                onClick={() =>
                  setDialogSejarahKuiriSaveOpen(!dialogSejarahKuiriSaveOpen)
                }
              />
              <Typography sx={{ fontWeight: "400!important" }}>
                {t("queryHistory")}
              </Typography>
            </Box>
            {query &&
              query.length > 0 &&
              query.map((item: any, index) => {
                const isCompleted = item?.finished;
                const lastIndex = query.length - 1;
                return (
                  <Box sx={{ display: "flex", mb: 4 }} key={index}>
                    <Box sx={{ mr: 2 }}>
                      <Box
                        sx={{
                          width: 35,
                          height: 35,
                          borderRadius: "50%",
                          border: isCompleted
                            ? "1px solid var(--primary-color)"
                            : "1px solid #FF0000",
                          backgroundColor: isCompleted
                            ? "#41C3C380"
                            : "#FF000080",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        {isCompleted ? (
                          <CheckIcon sx={{ color: "white" }} />
                        ) : null}
                      </Box>
                      {lastIndex === index ? (
                        ""
                      ) : (
                        <>
                          <Box
                            sx={{
                              width: 2,
                              height: "100%",
                              backgroundColor: "#DADADA",
                              ml: 2,
                            }}
                          />
                        </>
                      )}
                    </Box>
                    <Box sx={{ width: "100%" }}>
                      <Box sx={{ display: "flex", gap: 3 }}>
                        <Typography
                          sx={{
                            fontWeight: "300",
                            border: "1px solid #5088FF",
                            background: "#5088FF80",
                            borderRadius: "9px",
                            color: "#fff",
                            px: 2,
                            py: 1,
                            fontSize: "12px",
                          }}
                        >
                          {t("kuiri")} #{item.id}
                        </Typography>
                        <Typography
                          sx={{
                            fontWeight: "300",
                            border: "1px solid #DADADA",
                            background: "transparent",
                            borderRadius: "9px",
                            color: "#666666",
                            px: 2,
                            py: 1,
                            fontSize: "12px",
                          }}
                        >
                          {item.createdDate}
                        </Typography>
                        <Typography
                          sx={{
                            fontWeight: "300",
                            border: "1px solid #DADADA",
                            background: "#transparent",
                            borderRadius: "9px",
                            color: "#666666",
                            px: 2,
                            py: 1,
                            fontSize: "12px",
                          }}
                        >
                          {item.officerName}
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          p: 3,
                          border: "1px solid #D9D9D9",
                          borderRadius: "14px",
                          width: "100%",
                          mt: 2,
                          minHeight: "150px",
                          position: "relative",
                        }}
                      >
                        <Typography sx={{ mb: 3, color: "#666666" }}>
                          {item.note ? item.note : t("sureAjk")}
                        </Typography>
                        <span
                          style={{
                            fontFamily: '"Poppins", sans-serif',
                            backgroundColor: isCompleted
                              ? "#41C3C380"
                              : "#FF000080",
                            border: isCompleted
                              ? "1px solid #41C3C380"
                              : "1px solid #FF0000",
                            padding: "6px 20px",
                            borderRadius: "18px",
                            color: "#fff",
                            fontSize: "14px",
                            fontWeight: "400",
                            position: "absolute",
                            bottom: "20px",
                            right: "20px",
                          }}
                        >
                          {isCompleted ? t("SELESAI") : t("belumselesai")}
                        </span>
                      </Box>
                    </Box>
                  </Box>
                );
              })}

            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <Button
                sx={{
                  textDecoration: "underline",
                  color: "var(--primary-color)",
                  mt: 2,
                }}
              >
                {t("lihatpaparanpenuh")}
              </Button>
            </Box>
          </Box>
        </DialogContent>
      </Dialog>

      <DialogConfirmation
        open={isDialogOpen}
        onClose={handleDialogClose}
        onAction={handleFormSubmit}
        isMutating={isUpdatingStatus}
        onConfirmationText={t("permohonanConfirmation")}
        onSuccessText={t("applicationSuccessSubmited")}
        isSuccess={isSuccess}
        decisionLabel={
          ApplicationStatusList.find(
            (item: any) => item.id === getValues().applicationStatusCode
          )?.value || "-"
        }
      />
    </>
  );
}

export default KuiriPindaanDetails;
