import Grid from "@mui/material/Grid/Grid";
import { useTranslation } from "react-i18next";
import { useEffect, useState, useMemo } from "react";
import dayjs from "../../helpers/dayjs";
import "dayjs/locale/ms";
import { useCustom, useGetIdentity } from "@refinedev/core";
import { IIdentity } from "../../components/header/header-sidebar-authenticated";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { getUserPermission } from "@/redux/userReducer";
import AuthHelper from "@/helpers/authHelper";
import {
  NEW_PermissionNames,
  pageAccessEnum,
} from "@/helpers/enums";

import { JumbotronPublic } from "../../components/jumbotron/Public";
import { CardCalendarActivityList } from "../../components/card/calendar/ActivityList";
import { CardCalendarSidebar } from "../../components/card/calendar/Sidebar";
import {
  Paper,
  Typography,
  Stack,
  Button,
  Box,
  IconButton,
  TextField,
  InputAdornment,
  FormControl,
  Select,
  MenuItem,
} from "@mui/material";
import {
  Search as SearchIcon,
  FilterList,
  KeyboardArrowDown,
} from "@mui/icons-material";
import { Events } from "leaflet";
import { time } from "console";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import AddIcon from "@mui/icons-material/Add";
import { size } from "lodash";
import { eventService } from "@/services/eventService";
import { useTakwim } from "@/contexts/takwimProvider";
import { IEvent } from "@/types/event";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import MonthYearPagination from "../../components/pagination/MonthYearPagination";
import { useNotification } from "@refinedev/core";
import BackButton from "@/components/BackButton";
import { Address } from "cluster";
import { API_URL } from "@/api";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker, StaticDatePicker, PickersDay } from "@mui/x-date-pickers";
import { Badge } from "@mui/material";
import MultiDatePicker from "@/components/input/MultiDatePicker";

interface TakwimContextType {
  isAuthenticated: boolean;
}

const malayDayNames = [
  "Ahad",
  "Isnin",
  "Selasa",
  "Rabu",
  "Khamis",
  "Jumaat",
  "Sabtu",
];

const TakwimActivityListsPage = () => {
  const { data: user } = useGetIdentity<IIdentity>();
  const [loading, setLoading] = useState(true);
  const { t } = useTranslation();
  const [detailActiveTab, setDetailActiveTab] = useState(0);
  const navigate = useNavigate();
  const {
    upcomingEvents,
    setUpcomingEvents,
    isEventAdmin,
    setSelectedMonthYear,
    selectedMonthYear,
    selectedFilterDates,
    setSelectedFilterDates,
  } = useTakwim();
  const [pastEvents, setPastEvents] = useState<IEvent[]>([]);
  const [totalDisplayEvent, setTotalDisplayEvent] = useState(0);
  const [selectedYear, setSelectedYear] = useState<number>(0);
  const [selectedDates, setSelectedDates] = useState<dayjs.Dayjs[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFilterState, setSelectedFilterState] = useState<number | null>(
    null
  );

  const { data: addressList, isLoading: isAddressLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData: Address[] = addressList?.data?.data || [];

  // Add permission checks
  const userPermission = useSelector(getUserPermission);
  function checkPermissionAndUserGroup(accessType: number) {
    const hasPermission: boolean = AuthHelper.hasAuthority([
      NEW_PermissionNames.TAKWIM.label,
    ]);

    // 2 is internal user
    if (localStorage.getItem("portal") === "2") {
      return hasPermission && true;
    } else {
      return hasPermission && false;
    }
  }

  const stateOptions = addressData
    .filter((item: any) => item.pid === 152)
    .map((item: any) => ({
      value: item.id,
      label: item.name,
    }));

  // Check if user has permission to create events
  const hasCreateEventPermission = checkPermissionAndUserGroup(
    pageAccessEnum.Create
  );
  const hasEditEventPermission = checkPermissionAndUserGroup(
    pageAccessEnum.Update
  );

  // Redirect if user doesn't have view permission
  // useEffect(() => {
  //   if (!hasTakwimViewPermission) {
  //     navigate("/internal-user");
  //   }
  // }, [hasTakwimViewPermission, navigate]);

  const handleTabClick = (tabIndex: number) => {
    setDetailActiveTab(tabIndex);

    // Filter events based on tab
    const now = dayjs();
    switch (tabIndex) {
      case 0: // Public events
        setTotalDisplayEvent(
          upcomingEvents?.filter((event) => event.visibility == "PUBLIC").length
        );
        break;
      case 1: // Private events
        // Removed the date check
        setTotalDisplayEvent(
          upcomingEvents?.filter((event) => event.visibility === "PRIVATE")
            .length
        );
        break;
      case 2: // Past events
        setTotalDisplayEvent(pastEvents.length);
        // setTotalDisplayEvent(
        //   upcomingEvents?.filter((event) =>
        //     dayjs(event.eventStartDate).isBefore(now, "day")
        //   )
        //   // setTotalDisplayEvent(upcomingEvents?.filter((event) => dayjs(event.eventStartDate).isBefore(now, "day")).length)
        // );
        break;
      default:
        setTotalDisplayEvent(upcomingEvents.length);
    }
  };

  const fetchPublishedEvents = async (year: number) => {
    try {
      // setIsLoading(true);
      //IF ADMIN
      if ((hasCreateEventPermission || hasEditEventPermission) && user) {
        const allEvents = await eventService.getAllEvents(year);
        setUpcomingEvents(allEvents);
        setTotalDisplayEvent(
          allEvents?.filter((event) => event.visibility == "PUBLIC").length
        );
        return;
      }

      //IF NOT ADMIN AND NORMAL USER
      const publishedEvents = await eventService.getPublishedEvents(year);
      setUpcomingEvents(publishedEvents.data);
      setTotalDisplayEvent(
        publishedEvents?.data.filter((event) => event.visibility == "PUBLIC")
          .length
      );

      // setOrganizer(organiserData); // Set the organizer state with the returned data
    } catch (error) {
      const { open: openNotification } = useNotification();
      openNotification?.({
        message: t("Failed to fetch events"),
        description:
          error instanceof Error
            ? error.message
            : t("An unknown error occurred"),
        type: "error",
      });

      // Rethrow the error for potential upstream handling
      throw new Error(
        error instanceof Error ? error.message : "Failed to fetch events"
      );
    } finally {
      // setIsLoading(false);
    }
  };
  const fetchPastEvents = async (year: number) => {
    try {
      const pastEvents = await eventService.getPastEvent(year);
      setPastEvents(pastEvents);
    } catch (error) {
      console.log(error);
    } finally {
      // setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPublishedEvents(Number(selectedMonthYear.format("YYYY")));
  }, [selectedYear]);

  useEffect(() => {
    if (user) {
      fetchPastEvents(Number(selectedMonthYear.format("YYYY")));
    }
  }, [user, selectedYear]);

  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 3000);
  }, []);

  // Group events by date
  const groupedEvents = (
    detailActiveTab === 2 ? pastEvents : upcomingEvents
  )?.reduce((acc: { [key: string]: IEvent[] }, event) => {
    const dateKey = dayjs(event.eventStartDate).format("YYYY-MM-DD");
    if (!acc[dateKey]) {
      acc[dateKey] = [];
    }
    acc[dateKey].push(event);
    return acc;
  }, {});

  // console.log(groupedEvents, "groupedEvents");

  // Sort dates
  const sortedDates = Object.keys(groupedEvents || {}).sort(
    (a, b) => dayjs(a).valueOf() - dayjs(b).valueOf()
  );

  const formatDateHeader = (date: string) => {
    const dateObj = dayjs(date).locale("ms");
    const isToday = dateObj.isSame(dayjs(), "day");

    if (isToday) {
      return "Hari ini";
    }

    // Format: "Isnin, 01 Januari 2024"
    return `${dateObj.format("dddd")}, ${dateObj.format("DD MMMM YYYY")}`;
  };

  const handleMonthChange = (newDate: dayjs.Dayjs) => {
    if (Number(newDate.format("YYYY")) != selectedYear) {
      setSelectedYear(Number(newDate.format("YYYY")));
    }

    setSelectedMonthYear(newDate);
  };

  useEffect(() => {
    // Filter events based on selected month and active tab
    const eventsToCount = detailActiveTab === 2 ? pastEvents : upcomingEvents;
    const filteredEvents = eventsToCount?.filter((event) => {
      const eventDate = dayjs(event.eventStartDate);
      const isInSelectedMonth =
        eventDate.isSame(selectedMonthYear, "month") &&
        eventDate.isSame(selectedMonthYear, "year");

      if (!isInSelectedMonth) return false;

      switch (detailActiveTab) {
        case 0:
          return event.visibility === "PUBLIC";
        case 1:
          return event.visibility === "PRIVATE";
        case 2:
          return true;
        default:
          return true;
      }
    });

    setTotalDisplayEvent(filteredEvents?.length || 0);
  }, [selectedMonthYear, detailActiveTab, upcomingEvents, pastEvents]);

  // Filter events based on search term, selected dates, and other criteria
  const filteredEvents = useMemo(() => {
    const eventsToFilter = detailActiveTab === 2 ? pastEvents : upcomingEvents;

    return eventsToFilter?.filter((event) => {
      // First filter by month/year
      const eventDate = dayjs(event.eventStartDate);
      const isInSelectedMonth =
        eventDate.isSame(selectedMonthYear, "month") &&
        eventDate.isSame(selectedMonthYear, "year");

      if (!isInSelectedMonth) return false;

      // Filter by selected dates if any are selected
      if (selectedFilterDates.length > 0) {
        const eventDateStr = eventDate.format("YYYY-MM-DD");
        const isDateSelected = selectedFilterDates.some(
          (selectedDate) => selectedDate.format("YYYY-MM-DD") === eventDateStr
        );

        if (!isDateSelected) return false;
      }

      // Filter by state if selected
      if (selectedFilterState) {
        // Check if event's state includes the selected state
        const eventStates = Array.isArray(event.state)
          ? event.state
          : [event.state];
        const hasSelectedState = eventStates.some(
          (state) => Number(state) === selectedFilterState
        );

        // Also check stateAddress if available
        const hasSelectedStateAddress =
          event.stateAddress === selectedFilterState;

        if (!hasSelectedState && !hasSelectedStateAddress) return false;
      }

      // Then filter by tab selection
      let passesTabFilter = true;
      switch (detailActiveTab) {
        case 0: // Public events
          passesTabFilter = event.visibility === "PUBLIC";
          break;
        case 1: // Private events
          passesTabFilter = event.visibility === "PRIVATE";
          break;
        case 2: // Past events
          passesTabFilter = true;
          break;
      }

      if (!passesTabFilter) return false;

      // Finally, filter by search term
      if (!searchTerm) return true;

      // Search in event name
      return event.eventName?.toLowerCase().includes(searchTerm.toLowerCase());
    });
  }, [
    detailActiveTab,
    pastEvents,
    upcomingEvents,
    selectedMonthYear,
    searchTerm,
    selectedFilterDates,
    selectedFilterState,
  ]);

  // Update the TextField to use the search function
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Update the total display count when search changes
  useEffect(() => {
    setTotalDisplayEvent(filteredEvents?.length || 0);
  }, [filteredEvents]);

  return (
    <>
      <div className="content">
        <BackButton label="Takwim" to="" />

        <Paper
          elevation={1}
          sx={{
            boxShadow: "0 0.75rem 0.75rem 0 #EAE8E866",
            borderRadius: "10px",
            padding: "5px",
            // position: "sticky",
            top: "6rem",
            mb: 2,
          }}
        >
          <Box
            sx={{
              width: "100%",
              display: "flex",
              gap: 2,
              p: 0.5,
              borderRadius: "10px",
            }}
          >
            <>
              <Button
                className="tab-btn"
                fullWidth
                variant={detailActiveTab === 0 ? "contained" : "text"}
                onClick={() => handleTabClick(0)}
              >
                Acara Umum
              </Button>
              <Button
                className="tab-btn"
                fullWidth
                variant={detailActiveTab === 1 ? "contained" : "text"}
                onClick={() => handleTabClick(1)}
              >
                Acara Dalaman
              </Button>
              {user && (
                <Button
                  className="tab-btn"
                  fullWidth
                  variant={detailActiveTab === 2 ? "contained" : "text"}
                  onClick={() => handleTabClick(2)}
                >
                  Acara Terdahulu
                </Button>
              )}
            </>
          </Box>
        </Paper>

        <Paper
          elevation={1}
          sx={{
            boxShadow: "0 0.75rem 0.75rem 0 #EAE8E866",
            borderRadius: "1.25rem",
            padding: "1.5rem",
            // top: "6rem",
            display: "flex",
            alignContent: "center",
            mb: 2,
          }}
        >
          <Box
            sx={{
              width: "50%",
              margin: "0 auto",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <Box sx={{ mb: 2, width: "100%" }}>
              <TextField
                placeholder="Nama Acara"
                variant="outlined"
                fullWidth
                size="small"
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon sx={{ color: "#9CA3AF" }} />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "10px",
                    "& fieldset": {
                      borderColor: "#E5E7EB",
                    },
                    "&:hover fieldset": {
                      borderColor: "#E5E7EB",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "#E5E7EB",
                    },
                  },
                }}
              />
            </Box>

            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 2,
                width: "100%",
                borderRadius: "8px",
                padding: "10px",
                justifyContent: "center",
              }}
            >
              <Box
                sx={{
                  // border: "1px solid #E5E7EB",
                  borderRadius: "30px",
                  // padding: "30px 20px 20px 20px",
                  // boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
                }}
              >
                <Box sx={{ display: "flex", gap: 2 }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      color: "#6B7280",
                      borderRight: "1px solid #E5E7EB",
                      pr: 2,
                      height: "40px",
                    }}
                  >
                    <FilterList sx={{ fontSize: 20, mr: 1 }} />
                    <Typography variant="body2">Tapis Berdasarkan</Typography>
                  </Box>
                  <FormControl
                    size="small"
                    sx={{
                      minWidth: 120,
                      borderRight: "1px solid #E5E7EB",
                      height: "40px",
                      // paddingRight: 2,
                      // marginRight: 2
                    }}
                  >
                    <Select
                      displayEmpty
                      value={selectedFilterState || ""}
                      onChange={(e) =>
                        setSelectedFilterState(e.target.value as number)
                      }
                      IconComponent={(props) => (
                        <KeyboardArrowDown {...props} sx={{ marginLeft: 1 }} />
                      )}
                      sx={{
                        border: "none",
                        "& .MuiOutlinedInput-notchedOutline": {
                          border: "none",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          border: "none",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          border: "none",
                        },
                        "& .MuiSelect-select": {
                          paddingRight: "32px", // Make room for the dropdown icon
                          fontWeight: 400, // Normal font weight instead of bold
                          fontSize: "14px",
                          color: "#6B7280",
                        },
                      }}
                      renderValue={(selected) => {
                        if (!selected) return "Negeri";
                        const option = stateOptions.find(
                          (opt) => opt.value === selected
                        );
                        return option ? option.label : "Negeri";
                      }}
                    >
                      <MenuItem value="">
                        <em>Semua Negeri</em>
                      </MenuItem>
                      {stateOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <FormControl size="small" sx={{ minWidth: 120 }}>
                    <MultiDatePicker onMonthYearChange={handleMonthChange} />
                  </FormControl>
                </Box>
              </Box>
            </Box>
          </Box>
        </Paper>
        <Paper
          elevation={1}
          sx={{
            boxShadow: "0 0.75rem 0.75rem 0 #EAE8E866",
            borderRadius: "1.25rem",
            padding: "1.5rem",
            top: "6rem",
          }}
        >
          <Paper
            elevation={1}
            sx={{
              border: "1px solid #dadada99",

              borderRadius: "1.25rem",
              padding: "1.5rem",
              boxShadow: "0 0.75rem 0.75rem 0 #EAE8E866",
            }}
          >
            <Typography
              variant="body1"
              sx={{
                color: "primary.main",
                mb: 4,
                textAlign: "start",
                maxWidth: "800px",
              }}
            >
              Senarai Acara
            </Typography>
            <Grid container justifyContent={"end"} mb={1}>
              {user && hasCreateEventPermission && (
                <Button
                  variant="outlined"
                  sx={{
                    fontSize: "12px",
                    textTransform: "none",
                  }}
                  startIcon={<AddIcon />}
                  onClick={() => navigate("/takwim/create-event")}
                >
                  {t("Cipta acara")}
                </Button>
              )}
            </Grid>
            <Grid item sm={12} md={9} sx={{ width: "100%" }}>
              <Grid
                sx={{ display: "flex", flexDirection: "column", gap: "50px" }}
              >
                {sortedDates.map((date) => {
                  // Filter events for this date from our pre-filtered list
                  const dateEvents = filteredEvents?.filter(
                    (event) =>
                      dayjs(event.eventStartDate).format("YYYY-MM-DD") === date
                  );

                  // Skip rendering completely if no events for this date
                  if (!dateEvents || dateEvents.length === 0) {
                    return null;
                  }

                  // --- Sort dateEvents by startTime (ascending) ---
                  const sortedDateEvents = [...dateEvents].sort((a, b) => {
                    // If startTime is missing, treat as "00:00"
                    const timeA = a.startTime || "00:00";
                    const timeB = b.startTime || "00:00";
                    return dayjs(timeA, "HH:mm").diff(dayjs(timeB, "HH:mm"));
                  });

                  return (
                    <Grid
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "5px",
                      }}
                      key={date}
                    >
                      <Typography
                        variant="body1"
                        sx={{
                          mb: 1,
                          textAlign: "start",
                          maxWidth: "800px",
                        }}
                      >
                        {formatDateHeader(date)}
                      </Typography>
                      {sortedDateEvents.map((event) => {
                        const eventDate = dayjs(event.eventStartDate);
                        const isSelectedMonth = eventDate.isSame(
                          selectedMonthYear,
                          "month"
                        );
                        const isSelectedYear = eventDate.isSame(
                          selectedMonthYear,
                          "year"
                        );

                        if (!isSelectedMonth || !isSelectedYear) return null;

                        return (
                          <CardCalendarActivityList
                            key={event.id}
                            loading={loading}
                            data={[
                              {
                                id: event.id,
                                eventName: event.eventName,
                                eventStartDate: event.eventStartDate,
                                eventEndDate: event.eventEndDate,
                                venue: event.venue,
                                address1: "",
                                visibility: event.visibility as
                                  | "PUBLIC"
                                  | "PRIVATE",
                                startTime: event.startTime,
                                endTime: event.endTime,
                                description: "",
                                eventNo: event.eventNo,
                                eventAdminId: 0,
                                collaboratorName: "",
                                regStartDate: "",
                                regEndDate: "",
                                createdDate: null,
                                modifiedDate: null,
                                address2: null,
                                state: [],
                                postcode: "",
                                eventOrganisers: [],
                                maxParticipants: event.maxParticipants,
                                hasMax: event.hasMax,
                                status: null,
                                published: false,
                                privateEventSocieties: null,
                                organiserId: null,
                                createdBy: null,
                                modifiedBy: null,
                                mapUrl: "",
                                feedbackName: "",
                                position: [],
                                bannerUrl: undefined,
                                societiesId: null,
                                city: [],
                                district: [],
                                organisationLevel: "",
                                organisationCategory: null,
                                picContactNo: null,
                                stateAddress: null,
                                districtAddress: null,
                                cityAddress: "",
                                postcodeAddress: "",
                                totalRegisteredParticipants:
                                  event.totalRegisteredParticipants,
                              },
                            ]}
                          />
                        );
                      })}
                    </Grid>
                  );
                })}
                {totalDisplayEvent < 1 && (
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      width: "100%",
                      py: 4,
                    }}
                  >
                    <CalendarTodayIcon
                      sx={{
                        width: 64,
                        height: 64,
                        marginBottom: 2,
                        color: "#DADADA",
                      }}
                    />
                    <Typography variant="body1" color="#DADADA">
                      {detailActiveTab === 2
                        ? "Tiada acara terdahulu"
                        : detailActiveTab === 1
                        ? "Tiada acara dalaman untuk pertubuhan anda ketika ini"
                        : "Tiada acara umum ketika ini"}
                    </Typography>
                  </Box>
                )}
              </Grid>
            </Grid>
          </Paper>
        </Paper>
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            mt: 3,
            pb: 2,
            pr: 2,
          }}
        >
          <MonthYearPagination
            selectedDate={selectedMonthYear}
            onDateChange={handleMonthChange}
          />
        </Box>
      </div>
    </>
  );
};

export default TakwimActivityListsPage;
