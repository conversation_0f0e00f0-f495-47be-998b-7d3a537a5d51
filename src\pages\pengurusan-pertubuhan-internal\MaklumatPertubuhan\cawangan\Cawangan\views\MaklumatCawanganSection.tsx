import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { Typography, Box, Grid } from "@mui/material";
import DisabledTextField from "../../../../../../components/input/DisabledTextField";
import { getLocalStorage, MALAYSIA } from "@/helpers";
import Input from "@/components/input/Input";
import { API_URL } from "@/api";
import { useCustom } from "@refinedev/core";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
  "& span": {
    color: "red",
  },
};

const MaklumatCawanganSection = () => {
  const { t } = useTranslation();
  const { societyId, branchId } = useParams();
  const decodedSocietyId = atob(societyId || "");
  const decodedBranchId = atob(branchId || "");

  const { data: branchList, isLoading: isLoadingBranch } = useCustom<any>({
    url: `${API_URL}/society/branch/getById/${decodedBranchId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: decodedBranchId !== null,
      retry: false,
      cacheTime: 0,
    },
  });

  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${decodedSocietyId}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const addressList = getLocalStorage("address_list", null);
  const StateList = addressList
    .filter((item: any) => item.pid === MALAYSIA)
    .map((item: any) => ({ value: item.id, label: item.name }));

  const [districtList, setDistrictList] = useState<[]>([]);

  useEffect(() => {
    if (branchList?.data?.data?.stateCode) {
      setDistrictList(
        addressList
          .filter(
            (item: any) =>
              item.pid === Number(branchList?.data?.data?.stateCode)
          )
          .map((item: any) => ({ value: item.id, label: item.name }))
      );
    }
  }, [branchList?.data?.data]);

  return (
    <Box>
      <Box
        sx={{
          pl: 2,
          p: 3,
          border: "0.5px solid #DADADA",
          borderRadius: "10px",
          width: "100%",
          marginBottom: "0.5rem",
        }}
      >
        <Grid container spacing={2} marginBottom={1} alignItems="center">
          <Grid item xs={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Typography sx={labelStyle}>{t("organizationName")}</Typography>
            </Box>
          </Grid>
          <Grid item xs={8}>
            <DisabledTextField
              value={societyData?.data?.data?.societyName ?? "-"}
            />
          </Grid>
        </Grid>

        <Grid container spacing={2} marginBottom={1} alignItems="center">
          <Grid item xs={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Typography sx={labelStyle}>
                {t("namaRingkasPertubuhan")}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={8}>
            <DisabledTextField
              value={societyData?.data?.data?.shortName ?? "-"}
            />
          </Grid>
        </Grid>

        <Grid container spacing={2} marginBottom={1} alignItems="center">
          <Grid item xs={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Typography sx={labelStyle}>{t("branchNameDetails")}</Typography>
            </Box>
          </Grid>
          <Grid item xs={8}>
            <DisabledTextField value={branchList?.data?.data?.name ?? "-"} />
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={{
          borderRadius: "10px",
          width: "100%",
        }}
      >
        <Box
          sx={{
            p: 3,
            mb: 3,
            border: "1px solid #DADADA",
            borderRadius: "10px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("alamatTempatUrusan")}
          </Typography>

          {/* <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>
                  {t("placeOfBusinessLocationMap")}
                </Typography>
                <span style={{ color: "red" }}>*</span>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <MapContainer
                center={organizationCoords}
                scrollWheelZoom={false}
                zoom={13}
                style={{
                  height: "10rem",
                  width: "100%",
                  borderRadius: "5px",
                }}
              >
                <TileLayer
                  url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  attribution={`&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors`}
                />
                <Marker position={organizationCoords} />
              </MapContainer>
            </Grid>
          </Grid> */}

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>
                  {t("alamatTempatUrusan")}
                </Typography>
                <span style={{ color: "red" }}>*</span>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <DisabledTextField
                value={branchList?.data?.data?.address ?? "-"}
              />
            </Grid>
          </Grid>
          <Input
            value={Number(branchList?.data?.data?.stateCode)}
            name="state"
            disabled
            options={StateList}
            type="select"
            label={t("negeri")}
          />
          <Input
            value={Number(branchList?.data?.data?.districtCode)}
            name="district"
            disabled
            label={t("daerah")}
            type="select"
            options={districtList}
          />

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t("city")}</Typography>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <DisabledTextField value={branchList?.data?.data?.city ?? "-"} />
            </Grid>
          </Grid>

          <Grid container spacing={2} marginBottom={1} alignItems="center">
            <Grid item xs={4}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography sx={labelStyle}>{t("postcode")}</Typography>
                <span style={{ color: "red" }}>*</span>
              </Box>
            </Grid>
            <Grid item xs={8}>
              <DisabledTextField
                value={branchList?.data?.data?.postcode ?? "-"}
              />
            </Grid>
          </Grid>
        </Box>
        {/*
        <Box
          sx={{
            p: 3,
            mb: 3,
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("ROAction")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("responsibleRO")}
                <span style={{ marginLeft: "0.5rem", color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <SelectFieldController
                name="roId"
                control={control}
                options={roListOptions}
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("remarks")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextFieldController
                control={control}
                name="noteRo"
                multiline
                sx={{
                  minHeight: "126px",
                }}
                sxInput={{
                  minHeight: "126px",
                }}
              />
            </Grid>
          </Grid>

          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonPrimary type="submit" disabled={isLoading} sx={{}}>
              {isLoading ? <CircularProgress size={24} /> : t("update")}
            </ButtonPrimary>
          </Grid>
        </Box> */}
      </Box>
    </Box>
  );
};

export default MaklumatCawanganSection;
