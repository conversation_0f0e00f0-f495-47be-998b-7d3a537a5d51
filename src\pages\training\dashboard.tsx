import React, {useState} from "react";
import {Box, Typography} from "@mui/material";
import TrainingModule from "@/pages/training/trainingModule";
import CertificateModule from "@/pages/training/certificateModule";


const TrainingDashboard: React.FC = () => {

  const [page, setPage] = useState("training");

  const tabs = [
    {label: "<PERSON><PERSON><PERSON>", path: "training"},
    {label: "Sijil", path: "certificate"},
  ];

  const handleNavigation = (path: any) => {
    setPage(path);
  }

  return (
    <>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          backgroundColor: "white",
          paddingBottom: "8px",
          justifyContent: "space-evenly",
          borderRadius: "10px",
          px: 2,
          py: 1,
          mb: 1,
        }}
      >
        {tabs.map((tab, index) => {
          // Tentukan apakah tab saat ini aktif berdasarkan URL
          const isActive = page === tab.path;
          return (
            <React.Fragment key={index}>
              <Box
                sx={{
                  flex: 1,
                  backgroundColor: isActive ? "#0CA6A6" : "#FFFFFF",
                  p: 1,
                  //mx:1,
                  borderRadius: "5px",
                }}>
                <Box
                  key={index}
                  onClick={() => {
                    handleNavigation(tab.path)
                  }}
                  sx={{
                    cursor: "pointer",
                    color: isActive ? "#FFFFFF" : "#666666",
                    transition: "color 0.3s, border-bottom 0.3s",
                  }}
                >
                  <Typography sx={{fontWeight: "400 !important", textAlign: "center", fontSize: "14px"}}>
                    {tab.label}
                  </Typography>
                </Box>
              </Box>
            </React.Fragment>
          );
        })}
      </Box>
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box
          sx={{
            height: "100%",
            zIndex: 0,
            backgroundImage: `url('/aranprime.png')`,
            backgroundSize: "cover",
            backgroundRepeat: "no-repeat",
            backgroundPosition: "center center",
            justifyContent: "left",
            display: "flex",
            px: 5,
            py: 5,
            borderRadius: 2.5,
          }}>
          <Box>
            <Box sx={{width: "40%"}}>
              <Typography
                sx={{
                  color: "#FFF",
                  fontSize: "35px",

                }}
              >
                Latihan Jabatan Pendaftaran Pertubuhan Malaysia (JPPM)
              </Typography>
            </Box>
            <Box sx={{width: "40%"}}>
              <Typography
                sx={{
                  mt: 2,
                  color: "#FFF",
                  fontSize: "12px",
                }}
              >
                Sistem MyLatihan JPPM telah dibangunkan oleh Bahagian Pengurusan Teknologi Maklumat JPPM dengan kerjasama
                Unit Latihan JPPM untuk kemudahan warga JPPM menguruskan penyertaan latihan kursus JPPM.
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
      {page === "training" ? <TrainingModule /> : <CertificateModule /> }
    </>
  );
}

export default TrainingDashboard;
