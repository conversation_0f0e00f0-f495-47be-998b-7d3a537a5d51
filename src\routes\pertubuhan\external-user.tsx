import { Outlet, Route } from "react-router-dom";
import { ExternalUser } from "../../pages/dashboard/external-user";
import { RouteGuard } from "../../components/RouteGuard";
import { registerRoutes } from "../../helpers/routeDetector";

registerRoutes({
  '/pertubuhan': 'external',
});

export const externalUser = {
  routes: (
    <Route
      index
      element={
        <RouteGuard
          autoUpdatePortal={true}
          showDebugInfo={process.env.NODE_ENV === 'development'}
        >
          <ExternalUser />
        </RouteGuard>
      }
    />
  ),
};
