import React, { useState, useEffect, ChangeEvent } from "react";
import {
  Box,
  TextField,
  Typography,
  Grid,
  useMediaQuery,
  Theme,
} from "@mui/material";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { MALAYSIA } from "../../../../helpers/enums";
import { useSelector } from "react-redux";
import { capitalizeWords, formatArrayDate } from "@/helpers";
import dayjs from "dayjs";
import { getSocietyDataRedux } from "@/redux/societyDataReducer";
import {
  DisabledTextField,
  SelectFieldController,
  TextFieldController,
} from "@/components";
import { yupResolver } from "@hookform/resolvers/yup";
import { number, object, string } from "yup";
import AWSLocationMap from "@/components/geocoder/geocoder";
import {
  LocationClient,
  SearchPlaceIndexForTextCommand,
} from "@aws-sdk/client-location";
import { withAPIKey } from "@aws/amazon-location-utilities-auth-helper";
import { Switch } from "@/components/switch";
import { FieldValues, Resolver, useForm } from "react-hook-form";

const headers: Record<string, string> = {
  portal: localStorage.getItem("portal") || "",
  Authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
  "Content-Type": "application/json",
};

export const CreateMam: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const [organizationCoords, setOrganizationCoords] = useState<
    [number, number]
  >([101.707021, 2.745564]);

  const [organizationCoords2, setOrganizationCoords2] = useState<
    [number, number]
  >([101.707021, 2.745564]);

  const [searchParams] = useSearchParams();
  const branchId = searchParams.get("id");

  const [bName, setBName] = useState("");
  const [pass, setPass] = useState(true);
  const [sameAddress, setSameAddress] = useState(false);

  const [formData, setFormData] = useState({
    name: "",
    societyName: "",
    societyId: "",
    societyLevel: "",
    stateCode: "",
    districtCode: "",
    city: "",
    postcode: "",
    address: "",
    mailingStateCode: "",
    mailingDistrictCode: "",
    mailingCity: "",
    mailingPostcode: "",
    phoneNumber: "",
    email: "",
    mailingAddress: "",
    faxNumber: "",
    tarikhPermohonan: "",
    applicationExpirationDate: "",
    submissionDate: "",
    branchCoords: [],
  });

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const labelStyle = {
    color: "#666666",
    fontWeight: "400 !important",
    fontSize: "14px",
  };

  // @ts-ignore
  const addressData = useSelector((state) => state.addressData.data);

  const params = new URLSearchParams(window.location.search);

  const [branchData, setBranchData] = useState<any>({});
  const { data: branchList, isLoading: isLoadingBranch } = useCustom<any>({
    url: `${API_URL}/society/branch/getById/${branchId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: branchId !== null,
      retry: false,
      cacheTime: 0,
    },
  });

  useEffect(() => {
    if (branchList?.data?.data) {
      setBranchData(branchList?.data?.data);
    }
  }, [branchList]);

  const societyDataById = useSelector(getSocietyDataRedux);

  const fieldsToConvert = [
    "stateCode",
    "districtCode",
    "mailingStateCode",
    "mailingDistrictCode",
  ];

  useEffect(() => {
    if (branchData && societyDataById) {
      console.log(branchData);

      Object.entries(branchData).forEach(([key, value]) => {
        const addressAndDistrict = fieldsToConvert.includes(key);
        const finalValue =
          addressAndDistrict && typeof value === "string"
            ? parseInt(value)
            : value;
        form.setValue(key, finalValue);
      });
    }
  }, [branchData, societyDataById]);

  const showNBID =
    branchData?.applicationStatusCode === 5 ||
    branchData?.applicationStatusCode === 6 ||
    branchData?.applicationStatusCode === 9;

  const submissionDate = branchData?.submissionDate
    ? dayjs(branchData?.submissionDate)
    : null;
  const expirationDate = branchData?.applicationExpirationDate
    ? dayjs(branchData?.applicationExpirationDate)
    : null;

  const daysLeft =
    submissionDate && expirationDate
      ? expirationDate.diff(submissionDate, "day")
      : "-";

  // =================
  const resolver = yupResolver(
    object()
      .shape({
        name: string()
          .label(t("namaCawangan"))
          .test({
            name: "branch_name_exist",
            message: t("branchNameAlreadyExist"),
            test: async function (name) {
              const trimmedName = name?.trim().toLowerCase();
              const trimmedOldName = branchData?.name?.trim().toLowerCase();
              // Skip API call if name is too short
              if (!trimmedName || trimmedName.length <= 6) {
                return true;
              }

              // Skip API call if name hasn't changed
              if (bName === trimmedName) {
                return pass;
              }

              //skip API call if old and new name are the same
              if (trimmedOldName === trimmedName) {
                return pass;
              }

              try {
                const response = await fetch(
                  `${API_URL}/society/branch/checkBranchNameExist?branchName=${trimmedName}&societyId=${societyDataById?.id}`,
                  {
                    method: "GET",
                    headers: {
                      portal: localStorage.getItem("portal") || "",
                      Authorization: `Bearer ${localStorage.getItem(
                        "refine-auth"
                      )}`,
                    },
                  }
                );

                if (response.ok) {
                  const data = await response.json();
                  // If data exists, the name is taken (return error)
                  if (!data?.data) {
                    setBName(trimmedName);
                    setPass(false);
                    return this.createError({
                      message: t("organizationNamePlaceholder"),
                    });
                  }
                  // If data doesn't exist, the name is available

                  setBName(trimmedName);
                  setPass(true);
                  return true;
                }

                // If response not OK, consider invalid

                setBName(trimmedName);
                setPass(false);
                return false;
              } catch (e) {
                setBName(trimmedName);
                setPass(false);
                return this.createError({ message: t("error") });
              }
            },
          })
          .required(),
        address: string().label(t("businessAddressLabel")).required(),
        stateCode: number().label(t("state")).required(),
        districtCode: number().label(t("district")).required(),
        postcode: string()
          .label(t("postcode"))
          .test({
            name: "postcode_validation",
            test: (val: string | undefined, context) => {
              if (typeof val === "string") {
                if (
                  !val.split("").every((num) => !Number.isNaN(parseInt(num)))
                ) {
                  return context.createError({
                    message: t("validation.mustBeNumber"),
                  });
                }
                return val.length === 5;
              }
              return true;
            },
            message: t("postcodeValidation"),
          }),
        mailingAddress: string().label(t("mailingAddress")).required(),
        mailingStateCode: number().label(t("mailingStateCode")).required(),
        mailingDistrictCode: number()
          .label(t("mailingDistrictCode"))
          .required(),
        mailingPostcode: string()
          .label(t("mailingPostcode"))
          .test({
            name: "mailingPostcode_validation",
            test: (val: string | undefined, context) => {
              if (typeof val === "string") {
                if (
                  !val.split("").every((num) => !Number.isNaN(parseInt(num)))
                ) {
                  return context.createError({
                    message: t("validation.mustBeNumber"),
                  });
                }
                return val.length === 5;
              }
              return true;
            },
            message: t("postcodeValidation"),
          }),
      })
      .required()
  ) as unknown as Resolver<FieldValues>;

  const form = useForm<FieldValues>({
    defaultValues: {
      id: branchId,
      societyId: societyDataById?.id,
      name: "",
      address: "",
      stateCode: "",
      districtCode: "",
      city: "",
      postcode: "",
      mailingAddress: "",
      mailingCity: "",
      mailingDistrictCode: "",
      mailingPostcode: "",
      mailingStateCode: "",
    },
    resolver,
    mode: "all",
  });

  const {
    control,
    handleSubmit,
    formState: { isValid, isSubmitting },
    watch,
    setValue,
    getValues,
  } = form;

  const { name, stateCode, postcode, mailingStateCode, mailingPostcode } =
    watch();

  const { mutate: updateCawangan, isLoading: isLoadingUpdateCawangan } =
    useCustomMutation();

  const onSubmit = async (values: FieldValues) => {
    const endpoint = `${API_URL}/society/branch/update`;

    if (sameAddress) {
      values.mailingAddress = values.address;
      values.mailingCity = values.city;
      values.mailingDistrictCode = values.districtCode;
      values.mailingPostcode = values.postcode;
      values.mailingStateCode = values.stateCode;
    }

    if (branchId) {
      values.id = branchId;
    }

    let finalSubmitValue = {
      address: values.address,
      city: values.city,
      districtCode: values.districtCode,
      id: branchId,
      mailingAddress: values.mailingAddress,
      mailingCity: values.mailingCity,
      mailingDistrictCode: values.mailingDistrictCode,
      mailingPostcode: values.mailingPostcode,
      mailingStateCode: values.mailingStateCode,
      name: values.name,
      postcode: values.postcode,
      stateCode: values.stateCode,
    };

    await updateCawangan(
      {
        url: endpoint,
        method: "put",
        values: finalSubmitValue,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          console.log("successNotification", data);
          return {
            message: data?.data?.msg,
            type: "success",
          };
          // if (data?.data?.status === "SUCCESS") {
          //   if (data?.data?.data) {
          //     setBranchId(data.data.data.toString());
          //   }
          //   setIsSubmitSuccess(true);
          //   return {
          //     message: data?.data?.msg,
          //     type: "success",
          //   };
          // } else {
          //   return {
          //     message: data?.data?.msg,
          //     type: "error",
          //   };
          // }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
          // setIsSubmitSuccess(false);
        },
      }
    );
  };

  // =================
  //aws map
  // aws location
  const PLACE_INDEX_NAME = import.meta.env.VITE_AWS_PLACE_INDEX;
  const AWS_REGION = import.meta.env.VITE_AWS_API_KEY_REGION;
  const API_KEY = import.meta.env.VITE_AWS_API_KEY;
  const [client, setClient] = useState<LocationClient | null>(null);
  const [locations, setLocations] = useState<string[]>([]);

  // ✅ AWS Location Client Initialization
  useEffect(() => {
    const initializeClient = async () => {
      try {
        const authHelper = await withAPIKey(API_KEY, AWS_REGION);
        const locationClient = new LocationClient(authHelper.getClientConfig());
        setClient(locationClient);
      } catch (error) {
        console.error("❌ Failed to initialize AWS Location Client:", error);
      }
    };
    initializeClient();
  }, []);

  const addressValue = watch("address");

  const addressValue2 = watch("mailingAddress");

  useEffect(() => {
    // Only search if the address is not empty and has more than 3 characters
    if (addressValue && addressValue.length > 3) {
      // Debounce the search to avoid too many calls
      const delayDebounceFn = setTimeout(() => {
        searchLocation(addressValue, "address");
      }, 500); // 500ms delay

      return () => clearTimeout(delayDebounceFn);
    }
  }, [addressValue, client]); // re-run if address or client changes

  useEffect(() => {
    // Only search if the addressValue2 is not empty and has more than 3 characters
    if (addressValue2 && addressValue2.length > 3) {
      // Debounce the search to avoid too many calls
      const delayDebounceFn = setTimeout(() => {
        searchLocation2(addressValue2, "address");
      }, 500); // 500ms delay

      return () => clearTimeout(delayDebounceFn);
    }
  }, [addressValue2, client]); // re-run if address or client changes

  const searchLocation = async (query: string, type: "address") => {
    if (!query) return;
    if (!client) {
      console.warn("❌ AWS Location Client has not been initialized");
      return;
    }
    try {
      const command = new SearchPlaceIndexForTextCommand({
        IndexName: PLACE_INDEX_NAME,
        Text: query,
        MaxResults: 1,
        FilterCountries: ["MYS"],
      });
      const response = await client.send(command);
      console.log("place", response);
      if (!response?.Results || response.Results.length === 0) {
        console.warn("⚠️ No locations found");
        setLocations([]);
        return;
      }
      const firstResult = response.Results[0];
      console.log("firstResult", firstResult);

      if (firstResult?.Place?.Geometry?.Point) {
        const [longitude, latitude] = firstResult.Place.Geometry.Point;
        console.log("📍 Coordinates:", longitude, latitude);

        const city = firstResult.Place.Municipality || "";
        const postcode = firstResult.Place.PostalCode || "";
        const stateName = firstResult.Place.Region || "";
        const districtName = firstResult.Place.SubRegion || "";

        console.log("🏙️ City:", city);
        console.log("📮 Postcode:", postcode);
        console.log("🗺️ State name:", stateName);
        console.log("🗺️ District name:", districtName);

        if (type === "address") {
          setOrganizationCoords([longitude, latitude]);

          setValue("city", city || watch("city"));

          setValue("postcode", postcode || watch("postcode"));
        }
      }

      setLocations(
        response.Results.map(
          (place) => place.Place?.Label ?? "Unknown Location"
        )
      );
    } catch (error) {
      console.error("Error searching location:", error);
    }
  };

  const searchLocation2 = async (query: string, type: "address") => {
    if (!query) return;
    if (!client) {
      console.warn("❌ AWS Location Client has not been initialized");
      return;
    }
    try {
      const command = new SearchPlaceIndexForTextCommand({
        IndexName: PLACE_INDEX_NAME,
        Text: query,
        MaxResults: 1,
        FilterCountries: ["MYS"],
      });
      const response = await client.send(command);
      console.log("place", response);
      if (!response?.Results || response.Results.length === 0) {
        console.warn("⚠️ No locations found");
        setLocations([]);
        return;
      }
      const firstResult = response.Results[0];
      console.log("firstResult", firstResult);

      if (firstResult?.Place?.Geometry?.Point) {
        const [longitude, latitude] = firstResult.Place.Geometry.Point;
        console.log("📍 Coordinates:", longitude, latitude);

        const city = firstResult.Place.Municipality || "";
        const postcode = firstResult.Place.PostalCode || "";
        const stateName = firstResult.Place.Region || "";
        const districtName = firstResult.Place.SubRegion || "";

        console.log("🏙️ City:", city);
        console.log("📮 Postcode:", postcode);
        console.log("🗺️ State name:", stateName);
        console.log("🗺️ District name:", districtName);

        if (type === "address") {
          setOrganizationCoords2([longitude, latitude]);

          setValue("mailingCity", city || watch("mailingCity"));

          setValue(
            "mailingPostcode",

            postcode || watch("mailingPostcode")
          );
        }
      }

      setLocations(
        response.Results.map(
          (place) => place.Place?.Label ?? "Unknown Location"
        )
      );
    } catch (error) {
      console.error("Error searching location:", error);
    }
  };

  const handleLocationSelected = (location: {
    fullAddress: string;
    state: string;
    district: string;
    city: string;
    postcode: string;
  }) => {
    setValue("address", location.fullAddress);

    setValue("city", location.city);

    setValue("postcode", location.postcode);
  };

  const handleLocationSelected2 = (location: {
    fullAddress: string;
    state: string;
    district: string;
    city: string;
    postcode: string;
  }) => {
    setValue("mailingAddress", location.fullAddress);

    setValue("mailingCity", location.city);

    setValue("mailingPostcode", location.postcode);
  };

  //

  const handleSwitchOnChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { checked } = event.target;

    setValue("mailingAddress", watch("address"));

    setValue("mailingCity", watch("city"));

    setValue("mailingDistrictCode", watch("districtCode"));

    setValue("mailingPostcode", watch("postcode"));

    setValue("mailingStateCode", watch("stateCode"));

    setSameAddress(checked);
  };

  const resetForm = () => {
    setValue("name", "");
    setValue("address", "");
    setValue("stateCode", "");
    setValue("districtCode", "");
    setValue("city", "");
    setValue("postcode", "");
    setValue("mailingAddress", "");
    setValue("mailingCity", "");
    setValue("mailingDistrictCode", "");
    setValue("mailingPostcode", "");
    setValue("mailingStateCode", "");
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("maklumatCawangan")}
            </Typography>

            <Grid container spacing={2} sx={{ mb: 6, alignItems: "center" }}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("namaPertubuhan")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  value={societyDataById?.societyName || "-"}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("tarafPertubuhan")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  value={societyDataById?.societyLevel || "-"}
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("namaCawangan")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                {/* <DisabledTextField value={getValues("name") || "-"} /> */}
                <TextFieldController
                  name="name"
                  control={control}
                  fullWidth
                  required
                  placeholder="Contoh: Cawangan Perak, Bahagian Perak"
                  FormHelperTextProps={{
                    sx: {
                      color: "#FF0B0B",
                      fontSize: "12px",
                      fontWeight: "600",
                      marginLeft: "-1px",
                      visibility: "visible",
                    },
                  }}
                />
              </Grid>
              {showNBID && (
                <>
                  <Grid item xs={12} sm={4}>
                    <Typography sx={labelStyle}>
                      {t("noNBIDCawangan")}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={8}>
                    <DisabledTextField
                      value={
                        getValues("branchNo")
                          ? getValues("branchNo")
                          : getValues("branchApplicationNo") || "-"
                      }
                    />
                  </Grid>
                </>
              )}

              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("tarikhPermohonan")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                {" "}
                <DisabledTextField
                  value={
                    getValues("createdDate")
                      ? formatArrayDate(getValues("createdDate"))
                      : "-"
                  }
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("tempohMasa")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <DisabledTextField
                  value={
                    daysLeft === 0 ? t("LUPUT") : `${daysLeft} ${t("day")}`
                  }
                />
              </Grid>
            </Grid>
          </Box>

          {/* =================================== */}

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("branchBusinessAddress")}
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("meetingLocation")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <div
                  style={{
                    height: "150px",
                    width: "100%",
                    borderRadius: "8px",
                  }}
                >
                  <AWSLocationMap
                    longitude={organizationCoords[0]}
                    latitude={organizationCoords[1]}
                    // zoom={20}
                    onLocationSelected={handleLocationSelected}
                  />
                </div>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("alamatTempatUrusanCawangan")}
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextFieldController
                  name="address"
                  control={control}
                  // {...register("address")}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("state")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <SelectFieldController
                  name="stateCode"
                  control={control}
                  displayEmpty={true}
                  fullWidth
                  required
                  options={[
                    {
                      label: t("pleaseSelect"),
                      value: "",
                      disabled: true,
                    },
                    ...addressData
                      ?.filter((item: any) => item.pid === MALAYSIA)
                      ?.map((item: any) => ({
                        label: capitalizeWords(item.name, null, true),
                        value: item.id,
                      })),
                  ]}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("district")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <SelectFieldController
                  name="districtCode"
                  control={control}
                  fullWidth
                  required
                  displayEmpty
                  disabled={!stateCode}
                  options={[
                    {
                      label: t("pleaseSelect"),
                      value: "",
                      disabled: true,
                    },
                    ...addressData
                      ?.filter((item: any) => item.pid === stateCode)
                      ?.map((item: any) => ({
                        label: capitalizeWords(item.name, null, true),
                        value: item.id,
                      })),
                  ]}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("city")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextFieldController name="city" control={control} fullWidth />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("postcode")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextFieldController
                  name="postcode"
                  control={control}
                  fullWidth
                  required
                  type="number"
                  isPostcode
                />
              </Grid>
            </Grid>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                mb: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("registeredAddressAndPlaceOfBusinessOfTheBranch")}
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                {" "}
                {/* Add spacing */}
                <Typography
                  variant="body2"
                  sx={{ fontSize: "12px", color: "#66666680" }}
                >
                  {t("sameAsAbove")}
                </Typography>
                <Switch checked={sameAddress} onChange={handleSwitchOnChange} />
              </Box>
            </Box>
            {!sameAddress && (
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("meetingLocation")}{" "}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <div
                    style={{
                      height: "150px",
                      width: "100%",
                      borderRadius: "8px",
                    }}
                  >
                    <AWSLocationMap
                      longitude={organizationCoords2[0]}
                      latitude={organizationCoords2[1]}
                      // zoom={20}
                      onLocationSelected={handleLocationSelected2}
                    />
                  </div>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("alamatSuratMenyuratCawangan")}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    name="mailingAddress"
                    control={control}
                    disabled={sameAddress}
                    fullWidth
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("state")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <SelectFieldController
                    name="mailingStateCode"
                    control={control}
                    displayEmpty={true}
                    fullWidth
                    required
                    disabled={sameAddress}
                    options={[
                      {
                        label: t("pleaseSelect"),
                        value: "",
                        disabled: true,
                      },
                      ...addressData
                        ?.filter((item: any) => item.pid === MALAYSIA)
                        ?.map((item: any) => ({
                          label: capitalizeWords(item.name, null, true),
                          value: item.id,
                        })),
                    ]}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("district")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <SelectFieldController
                    name="mailingDistrictCode"
                    control={control}
                    fullWidth
                    required
                    displayEmpty
                    disabled={!mailingStateCode || sameAddress}
                    options={[
                      {
                        label: t("pleaseSelect"),
                        value: "",
                        disabled: true,
                      },
                      ...addressData
                        ?.filter((item: any) => item.pid === mailingStateCode)
                        ?.map((item: any) => ({
                          label: capitalizeWords(item.name, null, true),
                          value: item.id,
                        })),
                    ]}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("city")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    name="mailingCity"
                    control={control}
                    disabled={sameAddress}
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("postcode")} <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    name="mailingPostcode"
                    control={control}
                    disabled={sameAddress}
                    fullWidth
                    required
                    type="number"
                    isPostcode
                  />
                </Grid>
              </Grid>
            )}
            {/* <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              mt: 2,
              gap: 1,
            }}
          >
            <ButtonOutline
              sx={{
                bgcolor: "white",
                "&:hover": { bgcolor: "white" },
                width: isMobile ? "100%" : "auto",
              }}
              onClick={() => {
                reset(); // Clears all fields
                // navigate(-1);
              }}
            >
              {t("semula")}
            </ButtonOutline>
            <ButtonPrimary
              variant="contained"
              sx={{
                width: isMobile ? "100%" : "auto",
                color: "white",
              }}
              type="submit"
              disabled={!isValid || isSubmitting}
            >
              {t("update")}
            </ButtonPrimary>
          </Box> */}
          </Box>
          {/* =================================== */}

          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "flex-end",
              alignItems: "flex-end",
              gap: 2,
            }}
          >
            <Box sx={{ display: "flex" }} gap={1}>
              <ButtonOutline
                sx={{ width: isMobile ? "100%" : "auto" }}
                onClick={() => resetForm()}
              >
                {t("reset")}
              </ButtonOutline>
              <ButtonPrimary
                variant="contained"
                sx={{ width: isMobile ? "100%" : "auto" }}
                type="submit"
                // onClick={() =>
                //   navigate(`../minute-mesyuarat?id=${params.get("id")}`)
                // }
              >
                {t("kemaskini")}
              </ButtonPrimary>
            </Box>

            <ButtonPrimary
              variant="contained"
              sx={{ width: isMobile ? "100%" : "auto" }}
              onClick={() =>
                navigate(`../minute-mesyuarat?id=${params.get("id")}`)
              }
            >
              {t("seterusnya")}
            </ButtonPrimary>
          </Box>
        </Box>
      </form>
    </>
  );
};

export default CreateMam;
