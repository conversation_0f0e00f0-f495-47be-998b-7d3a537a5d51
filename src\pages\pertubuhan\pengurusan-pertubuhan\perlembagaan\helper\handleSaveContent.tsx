import { API_URL } from "../../../../../api";
import { useNavigate } from "react-router-dom";

export const handleSaveContent = ({
  i18n,
  societyId,
  societyName,
  dataId,
  isEdit,
  createClauseContent,
  editClauseContent,
  description,
  constitutionValues,
  clause,
  clauseCount,
  amendmentId,
  clauseContentId,
  clauseNo,
  clauseName,
  modifiedTemplate,
  constitutionTypeId,
  hideConstitution,
  isContentTextDescription = false,
  amendmentToggle,
}: any) => {
  
  if (isEdit) {

    const values: any = {
      id: dataId,
      checkUpdate: 1,
      societyId,
      societyNo: null,
      clauseContentId,
      constitutionValues,
      amendmentId: amendmentId,
      clauseNo: clauseNo,
      clauseName: clauseName,
      modifiedTemplate: modifiedTemplate,
      constitutionTypeId,
      hideConstitution,
      amendmentToggle,
    };

    if (!isContentTextDescription) {
      values.description = description
        .replaceAll("<b>", "")
        .replaceAll("</b>", "");
    }

    return editClauseContent(
      {
        url: `${API_URL}/society/constitutioncontent/update`,
        method: "put",
        values: values,

        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data: any) => {
          return {
            message:
              i18n?.language == "en"
                ? data?.data?.msg
                : "Maklumat fasal berjaya dikemaskini",
            type: "success",
          };
        },
        errorNotification: (data: any) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onSuccess(data: any, variables: any, context: any) {
          const clause = localStorage.getItem(`clause${clauseCount}`);
          if (clause) {
            localStorage.setItem(
              `clause${clauseCount}`,
              JSON.stringify({
                ...JSON.parse(clause),
                status: 2,
              })
            );
          }
          /*if (clause) {
            localStorage.removeItem(`clause${clauseCount}`);
          }*/
          //const navigate = useNavigate();
          //navigate(-1);
          history.go(-1);
          //location.reload();
          //navigate(-1);
          /*localStorage.setItem(
            `clause${clauseCount}`,
            JSON.stringify({
              id: dataId,
              societyName,
              description,
              constitutionValues,
            })
          );*/
        },
      }
    );
  } else {
    const values: any = {
      societyId,
      checkUpdate: 1,
      societyNo: null,
      clauseContentId,
      constitutionValues,
      amendmentId: amendmentId,
      clauseNo: clauseNo,
      clauseName: clauseName,
      modifiedTemplate: modifiedTemplate,
      constitutionTypeId: constitutionTypeId,
      hideConstitution,
    };

    if (!isContentTextDescription) {
      values.description = description
        .replaceAll("<b>", "")
        .replaceAll("</b>", "");
    }

    return createClauseContent(
      {
        resource: "society/constitutioncontent/create",
        values: values,
        meta: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data: any) => {
          return {
            message:
              i18n?.language == "en"
                ? data?.data?.msg
                : "Maklumat fasal berjaya dikemaskini",
            type: "success",
          };
        },
        errorNotification: (data: any) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onSuccess(data: any, variables: any, context: any) {
          /*if (clause) {
            localStorage.removeItem(`clause${clauseCount}`);
          }*/
          //const navigate = useNavigate();
          //navigate(-1);
          const clause = localStorage.getItem(`clause${clauseCount}`);
          if (clause) {
            localStorage.setItem(
              `clause${clauseCount}`,
              JSON.stringify({
                ...JSON.parse(clause),
                status: 2,
              })
            );
          }
          history.go(-1);
          //location.reload();
          //navigate(-1);
          /*localStorage.setItem(
            `clause${clauseCount}`,
            JSON.stringify({
              id: data?.data?.data,
              societyName,
              description,
              constitutionValues,
            })
          );*/
        },
      }
    );
  }
};
