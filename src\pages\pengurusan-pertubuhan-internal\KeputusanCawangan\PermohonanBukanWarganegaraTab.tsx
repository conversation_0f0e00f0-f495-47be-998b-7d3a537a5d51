import { Box, IconButton, Typography, debounce } from "@mui/material";
import { t } from "i18next";
import { ChangeEvent, useCallback, useEffect, useState } from "react";
import FormFieldRow from "../../../components/form-field-row";
import Label from "../../../components/label/Label";
import SelectFieldController from "../../../components/input/select/SelectFieldController";
import TextFieldController from "../../../components/input/TextFieldController";
import { FieldValues, useForm } from "react-hook-form";
import DataTable, { IColumn } from "@/components/datatable";
import { CrudFilter } from "@refinedev/core";
import {
  getLocalStorage,
  NEW_PermissionNames,
  pageAccessEnum,
  useQuery,
} from "@/helpers";
import { useNavigate } from "react-router-dom";
import { EditIcon } from "@/components/icons";
import AuthHelper from "@/helpers/authHelper";

const PermohonanBukanWarganegaraTab = () => {
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "30px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const hasReadPermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_CAWANGAN.children.PERMOHONAN_BUKAN_WARGANEGARA_CAWANGAN.label,
    pageAccessEnum.Read
  );

  const handleSearchChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setSearchTerm(e.target.value);
    debouceRequest(e.target.value);
  };
  const navigate = useNavigate();
  const [pendingNonCitizenList, setPendingNonCitizenList] = useState([]);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const { control, setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      organizationCategory: "",
      subCategoryCode: "",
      societyName: "",
      page: 1,
      pageSize: 10,
    },
  });
  const {
    data: searchResult,
    isLoading: isSearchLoading,
    refetch: fetchResult,
  } = useQuery({
    url: `society/roDecision/getAllPending/branch/nonCitizen`,
    autoFetch: false,
    onSuccess: (data) => {
      const responseData = data?.data?.data?.data;
      setPendingNonCitizenList(responseData || []);
    },
  });

  const request = debounce((value) => {
    fetchResult({
      filters: [
        {
          field: "pageSize",
          value: pageSize,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: page,
          operator: "eq",
        },
        {
          field: "isQuery",
          operator: "eq",
          value: "0",
        },
        {
          field: "categoryCode",
          value: watch("organizationCategory") || "",
          operator: "eq",
        },
        {
          field: "subCategoryCode",
          value: watch("subOrganizationCategory") || "",
          operator: "eq",
        },
        {
          field: "societyName",
          value: value,
          operator: "eq",
        },
      ],
    });
  }, 800);

  const debouceRequest = useCallback((value: any) => request(value), []);

  const totalList = searchResult?.data?.data?.total ?? 0;
  const handleChangePage = (newPage: number) => {
    const filters: CrudFilter[] = [
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: newPage, operator: "eq" },
      {
        field: "searchQuery",
        value: searchTerm,
        operator: "eq",
      },
    ];
    setPage(newPage);
    fetchResult({ filters });
  };
  const handleChangePageSize = (pageSize: number) => {
    const filters: CrudFilter[] = [
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: 1, operator: "eq" },
      {
        field: "searchQuery",
        value: null,
        operator: "eq",
      },
    ];
    setPageSize(pageSize);
    fetchResult({ filters });
  };

  const categories = getLocalStorage("category_list", []);
  const mainCategories = categories
    .filter((cat: any) => cat.level === 1)
    .map((cat: any) => ({
      value: cat.id,
      label: cat.categoryNameEn,
    }));
  const subCategories = categories
    .filter((cat: any) => cat.level === 2)
    .map((cat: any) => ({
      value: cat.id,
      label: cat.categoryNameEn,
    }));

  const columns: IColumn[] = [
    {
      field: "name",
      headerName: t("ajkName"),
      align: "center",
      flex: 1,
      renderCell: ({ row }) => {
        return row?.name;
      },
    },
    {
      field: "branchName",
      headerName: t("branchNameDetails"),
      align: "center",
      flex: 1,
      renderCell: ({ row }) => {
        return row.branchName ?? "-";
      },
    },
    {
      field: "branchNo",
      headerName: t("noPPMCawangan"),
      align: "center",
      flex: 1,
      renderCell: (params: any) =>
        params?.row?.branchNo
          ? params?.row?.branchNo
          : params?.row?.branchApplicationNo
          ? params?.row?.branchApplicationNo
          : "-",
    },
    {
      field: "roName",
      headerName: t("keputusanCawangan_RO"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => params?.row?.roName ?? "-",
    },
    {
      field: "negeri",
      headerName: t("negeri"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => params?.row?.stateName ?? "-",
    },
    {
      field: "createdDate",
      headerName: t("tarikhPermohonan"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => params?.row?.createdDate ?? "-",
    },
    {
      field: "activity",
      headerName: t("action"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params.row;
        return (
          <IconButton
            onClick={() => {
              const id = btoa(row.id);
              navigate(
                `/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/permohonan-bukan-warganegara/${id}`
              );
            }}
            disabled={!hasReadPermission}
          >
            <EditIcon
              sx={{
                color: hasReadPermission
                  ? "var(--primary-color)"
                  : "var(--text-grey-disabled)",
                width: "1rem",
                height: "1rem",
              }}
            />
          </IconButton>
        );
      },
    },
  ];

  useEffect(() => {
    fetchResult({
      filters: [
        {
          field: "pageSize",
          value: pageSize,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: page,
          operator: "eq",
        },
        {
          field: "isQuery",
          operator: "eq",
          value: "0",
        },
        {
          field: "categoryCode",
          value: watch("organizationCategory") || "",
          operator: "eq",
        },
        {
          field: "subCategoryCode",
          value: watch("subOrganizationCategory") || "",
          operator: "eq",
        },
        {
          field: "societyName",
          value: watch("organizationName") || "",
          operator: "eq",
        },
      ],
    });
  }, [watch("organizationCategory"), watch("subOrganizationCategory")]);

  const organizationCategory = watch("organizationCategory");

  useEffect(() => {
    setValue("subOrganizationCategory", "");
  }, [organizationCategory]);

  const { data: roDecisionPendingBranchCount, refetch: fetchSociety } =
    useQuery({
      url: `society/roDecision/getAllPendingCount/branch`,
      autoFetch: false,
    });

  return (
    <>
      <Box
        sx={{
          padding: "22px 16px",
          background: "#FFF",
          borderRadius: "15px",
          boxShadow: "0px 12px 12px 0px #EAE8E866",
          marginBottom: 1,
        }}
      >
        <Box
          sx={{
            width: "100%",
            border: "0.5px solid #DADADA",
            borderRadius: "10px",
            padding: "22px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("nonCitizenDecisionWaitingList")}
          </Typography>

          <FormFieldRow
            label={<Label text={t("organization_category")} />}
            value={
              <SelectFieldController
                name="organizationCategory"
                control={control}
                options={mainCategories}
                placeholder={t("selectPlaceholder")}
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("organizationSubCategory2")} />}
            value={
              <SelectFieldController
                name="subOrganizationCategory"
                control={control}
                options={subCategories}
                placeholder={t("selectPlaceholder")}
                // disabled={!category}
              />
            }
          />

          <FormFieldRow
            label={<Label text={t("namaPertubuhan")} />}
            value={
              <TextFieldController
                name="societyName"
                control={control}
                onChange={handleSearchChange}
              />
            }
          />
        </Box>
      </Box>

      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            textAlign: "center",
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",
            py: 2,
          }}
        >
          <Typography variant="h5" gutterBottom>
            {roDecisionPendingBranchCount?.data?.data
              ? Number(
                  roDecisionPendingBranchCount.data.data
                    .branchNonCitizenPendingCount
                )
              : 0}
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
            {t("applicationAwaitingDecision")}
          </Typography>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mt: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("nonCitizenDecisionWaitingList")}
          </Typography>

          <DataTable
            columns={columns}
            rows={pendingNonCitizenList}
            page={page}
            rowsPerPage={pageSize}
            totalCount={totalList}
            onPageChange={handleChangePage}
            onPageSizeChange={handleChangePageSize}
            customNoDataText={t("noData")}
          />
        </Box>
      </Box>
    </>
  );
};

export default PermohonanBukanWarganegaraTab;
