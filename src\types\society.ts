export interface ISocietyDetail {
  id: number;
  societyNo: string;
  applicationNo: string;
  societyName: string;
  shortName: string;
  societyLevel: string;
  registeredDate: string;
  approvedDate: any;
  identificationNo: string;
  categoryCodeJppm: string;
  subCategoryCode: string;
  countryCode: string;
  stateCode: string;
  districtCode: string;
  smallDistrictCode: string;
  cityCode: string;
  city: string;
  postcode: string;
  address: string;
  mailingCountryCode: string;
  mailingStateCode: string;
  mailingDistrictCode: string;
  mailingSmallDistrictCode: string;
  mailingCityCode: string;
  mailingCity: string;
  mailingPostcode: string;
  mailingAddress: string;
  phoneNumber: string;
  faxNumber: string;
  email: string;
  statusCode: string;
  statement: string;
  statementDate: number[];
  paymentMethod: string;
  paymentId: number;
  idOsol: number;
  kodPtj: string;
  receiptNo: string;
  paymentDate: number[];
  inquire: string;
  applicationStatusCode: number;
  receiptStatus: number;
  noPPMLama: any;
  noPPPLama: any;
  bankName: string;
  bankReferenceNo: string;
  ro: any;
  roBatal: string;
  roUpdate: string;
  akuiAjk: string;
  bubar: boolean;
  appealStatus: number;
  originName: string;
  originAddress: string;
  originStateCode: string;
  originDistrictCode: string;
  originSmallDistrictCode: string;
  originPostcode: string;
  originCity: string;
  notis1: string;
  migrateStat: number;
  migrateAjk: string;
  migrateUndang: string;
  tarikhAlih: number[];
  replyNotis: string;
  noteRo: string;
  statBebas: boolean;
  statPindaKecaw: boolean;
  appealStatement: string;
  reconcileDate: number[];
  flatPadam: boolean;
  rujuk: string;
  benarAjk: boolean;
  hasBranch: any;
  constitutionType: any;
  isQueried: any;
  createdBy: string;
  createdDate: number[];
  modifiedBy: string;
  modifiedDate: number[];
}

export interface ISocietyBranchDetail {
  id: number;
  createdDate: string;
  createdBy: number;
  modifiedDate: string;
  modifiedBy: string;
  branchApplicationNo: string;
  branchNo: string;
  societyId: number;
  societyNo: string;
  icNo: string;
  name: string;
  branchLevel: string;
  requestDate: string;
  address: string;
  countryCode: string;
  stateCode: string;
  districtCode: string;
  smallDistrictCode: string;
  cityCode: string;
  city: string;
  postcode: string;
  phoneNumber: string;
  faxNumber: string;
  email: string;
  branchStatusCode: number;
  confession: string;
  confessionDate: string;
  paymentId: number;
  paymentMethod: string;
  paymentDate: string;
  submissionDate: string;
  bodyDate: string;
  receiptNumber: string;
  status: string;
  applicationStatusCode: number;
  noPPMInduk: string;
  noPPMCawangan: string;
  bankName: string;
  bankReferenceNo: string;
  receiptStatus: string;
  migrateStat: any;
  migrateAjk: string;
  ro: string;
  appealStatus: string;
  transferDate: string;
  reconcileDate: string;
  cancelSociety: string;
  query: string;
  noteRo: string;
  isQueried: boolean;
  applicationExpirationDate: string;
  isExtended: any;
  branchCommittees: IBranchCommittee[];
  branchNonCitizenCommittees: any[];
  committeeTaskEnabled: boolean;
  applicantName: string;
}

export interface ICommittee {
  id: number;
  jobCode: string;
  societyId: number;
  societyNo: string;
  titleCode: string;
  name: string;
  gender: string;
  nationalityStatus: string;
  identificationType: string;
  identificationNo: string;
  dateOfBirth: number[];
  placeOfBirth: string;
  designationCode: string;
  otherDesignationCode: any;
  employerAddressStatus: any;
  employerName: string;
  employerAddress: string;
  employerPostcode: string;
  employerCountryCode: any;
  employerStateCode: any;
  employerCity: any;
  employerDistrict: any;
  residentialAddress: string;
  residentialPostcode: string;
  residentialAddressStatus: any;
  residentialCountryCode: any;
  residentialStateCode: string;
  residentialDistrictCode: string;
  residentialCity: any;
  email: string;
  telephoneNumber: any;
  phoneNumber: string;
  noTelP: any;
  membershipNo: any;
  status: string;
  applicationStatusCode: string;
  pegHarta: any;
  tarikhTukarSu: any;
  otherPosition: string;
  batalFlat: boolean;
  blacklistNotice: any;
  benarAjk: any;
  createdBy: string;
  createdDate: number[];
  modifiedBy: string;
  modifiedDate: number[];
  meetingId: number;
}

export interface IBranchCommittee {
  id: number;
  createdDate: string;
  createdBy: number;
  modifiedDate: string;
  modifiedBy?: number;
  branchId: number;
  branchNo: string;
  designationCode: number;
  titleCode: string;
  committeeName: string;
  gender?: string;
  citizenshipStatus: string;
  identityType?: string;
  committeeIcNo: string;
  dateOfBirth: string;
  placeOfBirth?: string;
  jobCode: string;
  committeeAddressStatus?: string;
  committeeAddress?: string;
  committeeCountryCode?: string;
  committeeStateCode?: string;
  committeeDistrict?: string;
  committeeSmallDistrict?: string;
  committeeCity?: string;
  postcode?: string;
  email: string;
  homePhoneNumber?: string;
  phoneNumber?: string;
  officePhoneNumber?: string;
  committeeEmployerName?: string;
  committeeEmployerAddressStatus?: string;
  committeeEmployerAddress?: string;
  committeeEmployerCountryCode?: string;
  committeeEmployerStateCode?: string;
  committeeEmployerDistrict?: string;
  committeeEmployerCity?: string;
  committeeEmployerPostcode?: string;
  status: string;
  batalFlat?: string;
  applicationStatusCode: string;
  pegHarta?: string;
  otherPosition?: string;
  tarikhTukarSu?: string;
  idSu: number;
}

export interface ISocietyBranchList {
  id: number;
  branchNo: string;
  name: string;
}
export interface SocietyCategoryResponseBodyGet {
  id: number;
  pid: number;
  categoryCodeJppm: string | null;
  categoryNameEn: string;
  categoryNameBm: string;
  level: number;
}

export interface SocietyQueryPendingCountResponseBodyGet {
  branchRegistrationQueryPendingCount: number;
  societyAmendmentQueryPendingCount: number;
  societyAppealQueryPendingCount: number;
  societyPrincipalSecretaryPendingCount: number;
  societyRegistrationQueryPendingCount: number;
}

export interface SocietyQueryRegistrationResponseBodyGet {
  applicationNo: string;

  creatorName: string;

  id: number;
  /**
   * format dd/mm/yyyy
   */
  modifiedDate: string;
  /**
   * format dd/mm/yyyy
   */
  paymentDate: string;
  /**
   * format dd/mm/yyyy
   */
  registeredDate: string;

  roName: string | null;

  societyName: string;

  stateName: string;

  tarikhAlih: string | null;

  tarikhAntar: string | null;
  submissionDate: string | null;
}

/**
 * @todo move to branch.ts instead
 */
export interface BranchQueryRegistrationResponseBodyGet {
  id: string;
  branchApplicationNo: string;
  branchCreatorName: string;
  name: string;
  paymentDate: number[] | null;
  roName: string | null;
  societyId: string | null;
  societyName: string | null;
  stateName: string | null;
  submissionDate: number[] | null;
  transferDate: number[] | null;
}

export interface ISocietyCategory {
  id: number;
  pid: number;
  categoryCodeJppm: string;
  categoryNameEn: string;
  categoryNameBm: string;
  level: number;
  cawStat: number;
  status: number;
}
