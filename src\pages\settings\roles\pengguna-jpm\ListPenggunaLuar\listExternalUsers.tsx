import { Box, Icon<PERSON>utton, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
// import { Edit as EditIcon } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { useContext, useEffect, useState } from "react";
import {
  IdTypes,
  ListUserStatus,
  NEW_PermissionNames,
  pageAccessEnum,
} from "../../../../../helpers/enums";
import { SearchContext } from "../../../../../contexts/searchProvider";
import { EditIcon, EyeIcon, TrashIcon } from "../../../../../components/icons";
import { MailIcon } from "../../../../../components/icons/mail";
import { DataTable } from "@/components";
import ConfirmationDialog from "../../../../../components/dialog/confirm";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../../api";
import AuthHelper from "@/helpers/authHelper";

function ListExternalUsers() {
  const { t } = useTranslation();
  const {
    pageExternal,
    setPageExternal,
    pageSizeExternal,
    setPageSizeExternal,
    searchExternalResult,
    setSearchExternalResult,
  } = useContext(SearchContext);
  const [userList, setUserList] = useState<any>([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const hasDeletePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.SELENGGARA.children.PENGURUSAN_PENGGUNA.children
      .SENARAI_PENGGUNA_LUAR.label,
    pageAccessEnum.Delete
  );
  const hasUpdatePermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.SELENGGARA.children.PENGURUSAN_PENGGUNA.children
      .SENARAI_PENGGUNA_LUAR.label,
    pageAccessEnum.Update
  );

  useEffect(() => {
    if (searchExternalResult?.data?.data) {
      // const formatedList = searchExternalResult?.data?.data?.map(
      //   (item: any) => ({
      //     ...item.user,
      //     roles: item.roles,
      //   })
      // );

      setTotalUsers(searchExternalResult?.data?.total);
      setUserList(searchExternalResult?.data?.data);
    } else {
      setUserList([]);
    }
  }, [searchExternalResult]);

  const navigate = useNavigate();

  const { mutate: deleteUser, isLoading: isDeleting } = useCustomMutation();

  const handleChangePage = (newPage: number) => {
    setPageExternal(newPage);
  };

  const handleChangePageSize = (newPageSize: number) => {
    setPageSizeExternal(newPageSize);
  };

  const handleDeleteClick = (userId: string) => {
    setSelectedUserId(userId);
    setOpenConfirmDialog(true);
  };

  const handleConfirmDelete = () => {
    if (selectedUserId) {
      deleteUser(
        {
          url: `${API_URL}/user/admin/external/${selectedUserId}`,
          method: "delete",
          values: {},
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: {
            message: t("deleteUserSuccess"),
            type: "success",
          },
          errorNotification: {
            message: t("deleteUserError"),
            type: "error",
          },
        },
        {
          onSuccess: () => {
            setOpenConfirmDialog(false);
            setSelectedUserId(null);
            // Refresh the table by triggering a new search
            refreshUserList();
          },
        }
      );
    }
  };

  const refreshUserList = () => {
    // Trigger a refresh by calling the search function from the search component
    // We'll use the existing search result to maintain current filters
    if (searchExternalResult) {
      // Re-fetch with current search parameters
      fetch(`${API_URL}/user/admin/getExternalUsers`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        body: JSON.stringify({
          state: null,
          status: null,
          name: null,
          position: null,
          pageNo: pageExternal,
          pageSize: pageSizeExternal,
        }),
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error("Error when fetching external users");
          }
          return response.json();
        })
        .then((data) => {
          setSearchExternalResult(data);
        })
        .catch((error) => {
          console.error("Error:", error);
        });
    }
  };

  const columns = [
    {
      field: "name",
      headerName: t("name"),
      flex: 1,
      align: "left",
      headerAlign: "left",
    },
    {
      field: "identification",
      headerName: t("identification"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        const idType = IdTypes.find(
          (item) => item.value === row?.identificationType?.toString()
        );
        return (
          <Typography className="label">
            {idType?.label ? t(idType?.label) : "-"}
          </Typography>
        );
      },
    },
    {
      field: "identificationNo",
      headerName: t("noKadPengenalan"),
      flex: 1,
      headerAlign: "center",
      align: "center",
    },
    {
      field: "status",
      headerName: t("status"),
      width: 150,
      headerAlign: "center",
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        const status = ListUserStatus.find(
          (item) => item.value === Number(row?.status)
        );
        const isActive = status?.value === 1;
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Typography
              className="status-pertubuhan-text"
              sx={{
                backgroundColor: "#fff",
                border: `2px solid ${
                  isActive ? "var(--success)" : "var(--error)"
                }`,
              }}
            >
              {status?.label ? t(status?.label) : "-"}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      align: "right",
      renderCell: (params: any) => {
        const row = params?.row;
        const id = row.id;

        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "right",
              alignItems: "right",
              gap: 1,
            }}
          >
            <IconButton
              onClick={() => {
                navigate(`../edit-pengurusan-pengguna-luar?id=${id}`);
              }}
              disabled={!hasUpdatePermission}
            >
              <EditIcon
                sx={{
                  fontSize: "2rem",
                  width: "1rem",
                  height: "1rem",
                  color: hasUpdatePermission
                    ? "var(--primary-color)"
                    : "var(--text-grey-disabled)",
                }}
              />
            </IconButton>
            <IconButton
              onClick={() => handleDeleteClick(id)}
              disabled={!hasDeletePermission || isDeleting}
            >
              <TrashIcon
                sx={{
                  fontSize: "2rem",
                  width: "1rem",
                  height: "1rem",
                  color: hasDeletePermission
                    ? "red"
                    : "var(--text-grey-disabled)",
                }}
              />
            </IconButton>
            <IconButton
              onClick={() => {
                navigate(`../edit-pengurusan-pengguna-luar?id=${id}&type=view`);
              }}
            >
              <EyeIcon
                sx={{
                  fontSize: "2rem",
                  width: "1rem",
                  height: "1rem",
                  color: "var(--grey-text)",
                }}
              />
            </IconButton>
            <IconButton
              onClick={() => {
                navigate(`../mail-pengurusan-pengguna-luar?id=${id}`);
              }}
              disabled={!hasUpdatePermission}
            >
              <MailIcon
                sx={{
                  fontSize: "2rem",
                  width: "1rem",
                  height: "1rem",
                  color: !hasUpdatePermission
                    ? "var(--text-grey-disabled)"
                    : "",
                }}
              />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  return (
    <>
      <Typography className={"title"}>{t("senaraiPengguna")}</Typography>

      <Box
        sx={{
          borderRadius: "14px",
          mt: 3,
          overflow: "scroll",
        }}
      >
        <DataTable
          rows={userList}
          columns={columns as any}
          page={pageExternal}
          rowsPerPage={pageSizeExternal}
          totalCount={totalUsers}
          onPageChange={handleChangePage}
          onPageSizeChange={handleChangePageSize}
          customNoDataText={t("noData")}
          rowHeight={90}
        />
      </Box>

      <ConfirmationDialog
        open={openConfirmDialog}
        onClose={() => setOpenConfirmDialog(false)}
        title={t("delete")}
        message={t("confirmDeleteUser")}
        onConfirm={handleConfirmDelete}
        onCancel={() => setOpenConfirmDialog(false)}
        isMutation={isDeleting}
      />
    </>
  );
}

export default ListExternalUsers;
