import React, {useEffect, useState} from "react";
import {Box, Radio, TextField, Typography} from "@mui/material";
import {useTranslation} from "react-i18next";
import {ButtonPrimary, DialogConfirmation} from "@/components";
import {useNavigate} from "react-router-dom";
import {headerStyle, labelStyle} from "@/pages/internal-training/trainingConstant";
import {useCustomMutation, useCustom} from "@refinedev/core";
import {API_URL} from "@/api";

interface TrainingFeedbackFragmentProps {
  quizAttemptId?: string;
  isAdmin: boolean;
  handleNext?: (val: number) => void,
}

interface feedbackAnswer {
  questionText: string,
  answer: string,
}

const TrainingFeedback: React.FC<TrainingFeedbackFragmentProps> = ({
                                                                     quizAttemptId,
                                                                     isAdmin,
                                                                     handleNext
                                                                   }) => {
  const {t, i18n} = useTranslation();
  //const [questions, setQuestions] = useState<any[]>([]);
  const [disabled, setDisabled] = useState<boolean>(false);
  const [answers, setAnswers] = useState<feedbackAnswer[]>([]);
  const [openModal, setOpenModal] = useState(false);

  const navigate = useNavigate();
  console.log("props",quizAttemptId, isAdmin);

  const questions = [
    {
      questionText: "Adakah objektif latihan ini jelas disampaikan?",
      options: [
        {
          optionText: "Tidak Jelas",
        },
        {
          optionText: "Kurang Jelas",
        },
        {
          optionText: "Jelas",
        },
        {
          optionText: "Sangat Jelas",
        },
      ],
    },
    {
      questionText: "Adakah bahan latihan (slide, nota, kuiz) membantu pemahaman anda?",
      options: [
        {
          optionText: "Tidak Jelas",
        },
        {
          optionText: "Kurang Jelas",
        },
        {
          optionText: "Jelas",
        },
        {
          optionText: "Sangat Jelas",
        },
      ],
    },
    {
      questionText: "Adakah topik latihan berkaitan dengan tugas/kerja anda? ",
      options: [
        {
          optionText: "Tidak Berkaitan",
        },
        {
          optionText: "Kurang Berkaitan",
        },
        {
          optionText: "Berkaitan",
        },
        {
          optionText: "Sangat Berkaitan",
        },
      ],
    },
    {
      questionText: "Sejauh mana anda berpuas hati dengan latihan ini secara keseluruhan?",
      options: []
    },
    {
      questionText: "Apakah penambahbaikkan yang perlu diperbaiki?",
      options: []
    },
  ];

  const next = () => {
    /*navigate("/latihan/sijil", {
      state: { courseId: courseId, enrollId: enrollId },
    });*/
    //navigate("/latihan");
    setOpenModal(true);
  };

  const handleAnswer = (q: string, val: string, i: number) => {
    /*console.log("handleAnswer",val)
    answers.forEach((a) => {
      if(a.questionText === q){
        a.answer = val
      }
    })
    console.log("answers",answers,answers[i].answer);*/
    setAnswers(prevAnswers => prevAnswers.map(answer =>
      answer.questionText === q ? { ...answer, answer: val } : answer
    ));
  }

  useEffect(() => {
    //setQuestions(demoData);
    const temp = questions.map((q) => {
      return {
        questionText: q.questionText,
        answer: ""
      }
    });
    setAnswers(temp);
  }, [quizAttemptId]);

  const {mutate: submitFeedback, isLoading: isLoadingSubmit} = useCustomMutation();
  const SubmitFeedback = (): void => {
    submitFeedback(
      {
        url: `${API_URL}/society/training/quiz/attempt/feedback`,
        method: "post",
        values: {
          quizAttemptId: quizAttemptId,
          feedbackJson: JSON.stringify(answers),
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onSuccess(data) {
          const submitData = data?.data?.data;
          setOpenModal(false);
          if(handleNext) handleNext(-99);
          navigate("/latihan");
        },
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const {data: feedbackData, isLoading: isFeedbackDataLoading} = useCustom({
    url: `${API_URL}/society/admin/training/quiz/attempt/feedback/${quizAttemptId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: isAdmin && quizAttemptId != "0",
      retry: false,
      cacheTime: 0,
    },
  });

  useEffect(() => {
    if(feedbackData?.data?.data){
      const temp = JSON.parse(feedbackData?.data?.data);
      setAnswers(temp);
    }
  }, [feedbackData]);

  return (
    <>
      <Box
        sx={{
          //flex: 5,
          width: "100%",
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box
          sx={{
            //height: "100%",
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //flex: 5,
            px: 5,
            py: 2,
            mb: 1,
          }}
        >
          <Box sx={{display: "flex", justifyContent: "start", gap: 1, mb: 5}}>
            <Typography
              sx={{
                color: "#0CA6A6",
                //pt: 3,
                fontWeight: "500",
                fontSize: 20,
              }}
            >
              {"Maklumbalas"}
            </Typography>
          </Box>
          {questions.map((item: any, index: number) => {
            return (
              <Box
                key={index}
                sx={{
                  display: "flex",
                  //justifyContent: "space-between",
                  gap: 1,
                  mb: 3,
                }}
              >
                <Box>
                  <Typography
                    sx={headerStyle}
                  >
                    {`${index + 1}. ${item.questionText}`}
                  </Typography>
                  <Box sx={{pl: 1, display: "flex", justifyContent: "space-between", gap: 1, width: 800, mb: 2, mt: 2}}>
                    {item.options.length > 0 ? item.options.map((item2: any, index2: number) => {
                      return (
                        <Typography
                          key={index2}
                          sx={{fontWeight: "400", fontSize: 14}}
                        >
                          <Radio
                            disabled={isAdmin || disabled}
                            value={item2.optionText}
                            checked={(answers[index] != null && answers[index].answer === item2.optionText)}
                            onChange={(e) => {
                              handleAnswer(item.questionText, e.target.value, index);
                            }}
                            sx={{p: 0, ml: 1, mr: 1}}
                          />
                          {`${item2.optionText}`}
                        </Typography>
                      );
                    }) : <TextField
                      size={"small"}
                      fullWidth
                      required
                      multiline
                      rows={4}
                      name="feedback"
                      value={(answers[index] != null ? answers[index].answer : "")}
                      onChange={(e) => {
                        handleAnswer(item.questionText, e.target.value, index);
                      }}
                      inputProps={{
                        readOnly: isAdmin || disabled
                      }}
                      //value={formData.description}
                      //error={!!formErrors.description}
                      //helperText={formErrors.description}
                      //onChange={handleInputChange}
                    />}
                  </Box>
                </Box>
              </Box>
            );
          })}
        </Box>
        <Box
          sx={{display: "flex", mt: 1, justifyContent: "flex-end", gap: 1}}
        >
          {!isAdmin ?
          <ButtonPrimary
            variant="outlined"
            sx={{
              borderColor: "#0CA6A6",
              bgcolor: "#0CA6A6",
              "&:hover": {bgcolor: "#0CA6A6", borderColor: "#0CA6A6"},
              color: "#fff",
              fontWeight: "400",
            }}
            onClick={() => next()}
          >
            {t("next")}
          </ButtonPrimary> : <></> }
        </Box>
      </Box>
      <DialogConfirmation
        open={openModal}
        onClose={() => {
          setOpenModal(false);
        }}
        onAction={SubmitFeedback}
        isMutating={false}
        onConfirmationText={t("SUBMIT_TRAINING_QUIZ")}
      />
    </>
  );
};

export default TrainingFeedback;
