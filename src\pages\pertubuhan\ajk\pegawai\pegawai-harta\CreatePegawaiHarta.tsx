import {
  Box,
  Typography,
  Paper,
  Grid,
  Checkbox,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  IconButton,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import {
  ConstitutionType,
  OrganisationPositionLabel,
  OrganisationPositions,
} from "../../../../../helpers/enums";
import { useEffect, useState } from "react";
import { styled } from "@mui/styles";
import { usejawatankuasaContext } from "../../jawatankuasa/jawatankuasaProvider";
import Input from "@/components/input/Input";
import { EyeIcon } from "@/components/icons";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import NewAlertDialog from "@/components/dialog/newAlert";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import { useSelector } from "react-redux";

interface OfficerItem {
  id: number;
  name: string;
  // add if needed...
}

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};
const CustomCheckbox = styled(Checkbox)(({ theme }) => ({
  width: 20,
  height: 20,
  padding: 1,
  borderRadius: "6px",
  border: "2px solid #979797",
  color: "#979797",
  "&.Mui-checked": {
    backgroundColor: "var(--primary-color)",
    color: "#FFFFFF",
    border: "2px solid var(--primary-color)",
  },
  "&.Mui-disabled": {
    border: "2px solid #ccc",
    backgroundColor: "#f5f5f5",
    color: "#ccc",
  },
  "&.Mui-checked.Mui-disabled": {
    backgroundColor: "#d1d1d1",
    color: "#ffffff",
    border: "2px solid #d1d1d1",
  },
  "& .MuiSvgIcon-root": {
    display: "none",
  },
}));
export const CreatePegawaiHarta: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { id: societyId } = useParams();
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state?.societyByIdData?.data);

  const constitutionType = societyDataRedux?.constitutionType;
  const isBebasType =
    constitutionType === ConstitutionType.Bebas[1] ||
    constitutionType === ConstitutionType.CawanganBebas[1];
  const isOld = societyDataRedux?.migrateStat === 1;

  const limitChecks = isOld ? false : isBebasType ? false : true;

  const [checked, setChecked] = useState<{ [ajkId: number]: boolean }>({});

  const [selected, setSelected] = useState<OfficerItem[]>([]);

  const location = useLocation();
  const propertyOfficerIds = location.state?.propertyOfficerIds;
  const propertyOfficerApplicationId =
    location.state?.propertyOfficerApplicationId;
  const view = location.state?.view;

  const [selectedIds, setSelectedIds] = useState<number[]>(
    propertyOfficerIds ? propertyOfficerIds : []
  );

  // Use useEffect to sync selectedIds with checked state
  useEffect(() => {
    // Initialize checked state based on selectedIds
    const initialCheckedState: { [ajkId: number]: boolean } = {};

    selectedIds.forEach((id) => {
      initialCheckedState[id] = true; // Mark those IDs as checked
    });

    setChecked(initialCheckedState); // Update checked state with initial values
  }, [selectedIds]); // This will run every time selectedIds changes

  const handleCheckboxChange = (ajk: any) => {
    setSelected((prev) => {
      const current = prev || [];
      const exists = current.some((i) => i.id === ajk.id);

      if (exists) {
        // remove from selected
        return current.filter((i) => i.id !== ajk.id);
      } else {
        // only allow if under limit
        if (limitChecks && current.length >= 3) {
          return current; // Do nothing if already 3 selected
        }
        return [...current, ajk];
      }
    });

    const ajkId = ajk?.id;
    setChecked((prev) => {
      const current = !!prev[ajkId];
      if (!current && limitChecks && selected?.length >= 3) {
        return prev; // Prevent updating state if over limit
      }
      return { ...prev, [ajkId]: !current };
    });
  };

  const handleBack = () => {
    navigate(-1);
  };

  const [dialogAlertOpen, setDialogAlertOpen] = useState(false);

  const { ajkList, addressList, fetchAjkList, fetchAddressList } =
    usejawatankuasaContext();

  useEffect(() => {
    fetchAjkList();
    fetchAddressList();
  }, [fetchAjkList, fetchAddressList]);

  const handleCreatePropertyOfficer = () => {
    // Get an array of ajkIds where the checkbox is checked
    const selectedIds = Object.keys(checked)
      .filter((ajkId) => checked[Number(ajkId)])
      .map((ajkId) => Number(ajkId)); // Convert to an array of numbers

    if (selectedIds.length !== 3) {
      setDialogAlertOpen(true);
      return;
    }

    setSelectedIds(selectedIds);
    createPropertyOfficer(selectedIds);
  };

  const { mutate: saveOfficer, isLoading: isSaveTrustee } = useCustomMutation();

  const createPropertyOfficer = (selectedIds: number[]) => {
    saveOfficer(
      {
        url: !propertyOfficerApplicationId
          ? `${API_URL}/society/property_officer/create`
          : `${API_URL}/society/property_officer/${propertyOfficerApplicationId}`,
        method: !propertyOfficerApplicationId ? "post" : "put",
        values: {
          propertyOfficerIds: selectedIds.map(Number),
          societyId: societyId,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: () => {
          return {
            message: t(`propertyOfficer${propertyOfficerApplicationId ? "Updated" : "Created"}Successfully`),
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
        onSuccess: (response) => {
          const id = response?.data?.data.id;
          const createdDate = response?.data?.data.createdDate;
          navigate("bayaran", {
            state: {
              createdDate: createdDate,
              societyId: societyId,
              propertyOfficerId: id,
              publicOfficerName: selected,
            },
          });
        },
      }
    );
  };

  useEffect(() => {
    if (limitChecks && ajkList.length > 0) {
      const top3HighRank = ajkList
        .filter((ajk) => {
          const matchedPosition = OrganisationPositions.find(
            (item) => item?.value === Number(ajk?.designationCode)
          );
          // @ts-ignore
          return [1, 4, 6].includes(matchedPosition?.rank);
        })
        .slice(0, 3);

      const selectedMap: Record<string, boolean> = {};
      top3HighRank.forEach((ajk) => {
        selectedMap[ajk.id] = true;
      });

      setSelected(top3HighRank);
      setChecked(selectedMap);
    }
  }, [limitChecks, ajkList]);

  return (
    <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
        mb: 2,
      }}
    >
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography
          sx={{
            color: "#666666",
            fontSize: 14,
            fontWeight: "400 !important",
          }}
        >
          <span style={{ color: "red", fontWeight: "bold" }}>
            {t("peringatan")} :
          </span>{" "}
          Sila lantik 3 orang Pegawai Harta bagi pertubuhan ini.
        </Typography>
      </Box>

      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("jenisPegawai")}
        </Typography>

        <Input
          disabled
          required
          label={t("jenisPegawai")}
          value={t("pegawaiHarta")}
        />
      </Box>

      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("ajkList")}
        </Typography>

        <TableContainer
          component={Paper}
          sx={{
            boxShadow: "none",
            backgroundColor: "white",
            borderRadius: 2.5 * 1.5,
            mb: 3,
          }}
        >
          <Table>
            <TableHead>
              <TableRow>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                    textAlign: "center",
                  }}
                >
                  {t("position")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                    textAlign: "center",
                  }}
                >
                  {t("name")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                    textAlign: "center",
                  }}
                >
                  {t("email")}
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                    textAlign: "center",
                  }}
                >
                  {t("state")}
                </TableCell>
                <TableCell
                  align="right"
                  sx={{
                    fontWeight: "bold",
                    borderBottom: "1px solid #e0e0e0",
                    color: "#666666",
                    p: 1,
                    textAlign: "center",
                  }}
                ></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {ajkList.map((ajk, index) => (
                <TableRow key={ajk.id}>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                      textAlign: "left",
                    }}
                  >
                    {/* {t(OrganisationPositionLabel[ajk.designationCode])} */}
                    {t(
                      `${
                        OrganisationPositions.find(
                          (item) => item?.value === Number(ajk.designationCode)
                        )?.label || "-"
                      }`
                    )}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                      textAlign: "left",
                    }}
                  >
                    {ajk.name}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                      textAlign: "left",
                    }}
                  >
                    {ajk.email}
                  </TableCell>
                  <TableCell
                    sx={{
                      color: "#666666",
                      borderBottom: "1px solid #e0e0e0",
                      p: 1,
                      textAlign: "center",
                    }}
                  >
                    {
                      addressList.find(
                        (item: any) =>
                          item.id.toString() === ajk.residentialStateCode
                      )?.name
                    }
                  </TableCell>
                  <TableCell
                    align="right"
                    sx={{ borderBottom: "1px solid #e0e0e0", p: 1 }}
                  >
                    <>
                      {(() => {
                        const matchedPosition = OrganisationPositions.find(
                          (item) => item?.value === Number(ajk?.designationCode)
                        );
                        const highRankAJK = [1, 4, 6].includes(
                          // @ts-ignore
                          matchedPosition?.rank
                        );
                        return (
                          <>
                            <IconButton
                              onClick={() =>
                                navigate(
                                  `/pertubuhan/society/${societyId}/senarai/ajk/jawatankuasa/create-ajk`,
                                  {
                                    state: {
                                      ajk: ajk,
                                      view: true,
                                    },
                                  }
                                )
                              }
                            >
                              <EyeIcon />
                            </IconButton>
                            <CustomCheckbox
                              disabled={view || (!highRankAJK && limitChecks)}
                              key={ajk.id}
                              checked={!!checked[ajk.id]}
                              onChange={() => handleCheckboxChange(ajk)}
                            />
                          </>
                        );
                      })()}
                    </>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 2 }}>
        <ButtonOutline onClick={handleBack}>{t("back")}</ButtonOutline>
        {!view ? (
          <ButtonPrimary onClick={handleCreatePropertyOfficer}>
            {t("update")}
          </ButtonPrimary>
        ) : null}
      </Box>

      <NewAlertDialog
        open={dialogAlertOpen}
        onClose={() => setDialogAlertOpen(false)}
        message={"Sila lantik 3 orang Pegawai Harta bagi pertubuhan ini."}
      />
    </Box>
  );
};

export default CreatePegawaiHarta;
