import { useMemo, useEffect } from "react";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useForm, FieldValues } from "react-hook-form";
import { useKeputusanIndukPembaharuanSetiausahaContext } from "..";
import {
  getAddressListName,
  ListGelaran,
  useMutation,
  getSocietyMainCategories,
  getSocietySubCategories,
  ListGender,
  getCitizenshipLabel,
} from "@/helpers";

import {
  Typography,
  Box,
  Grid,
  useMediaQuery,
  Theme,
  CircularProgress,
} from "@mui/material";
import FormFieldRow from "../../../../components/form-field-row";
import Label from "../../../../components/label/Label";
import DisabledTextField from "../../../../components/input/DisabledTextField";

import { FileAddIcon } from "../../../../components/icons";

interface MaklumatSetiausahaSectionProps {
  onClickMaklumbalas: () => void;
}

const MaklumatSetiausahaSection: React.FC<MaklumatSetiausahaSectionProps> = ({
  onClickMaklumbalas,
}) => {
  const { t, i18n } = useTranslation();

  const { secretaryDetailData, societyDetailData } =
    useKeputusanIndukPembaharuanSetiausahaContext();

  const isMyLanguage = i18n.language === "my";
  const feedbacks = secretaryDetailData?.feedbacks ?? [];
  const mainCategory = getSocietyMainCategories(
    Number(societyDetailData?.categoryCodeJppm)
  );
  const subCategory = getSocietySubCategories(
    Number(societyDetailData?.subCategoryCode)
  );
  const getGender =
    ListGender.find((gender) => gender.value === secretaryDetailData?.gender)
      ?.label ?? "";

  const getGelaranName = (id: string): string =>
    ListGelaran.find((gelaran) => gelaran.value === id)?.label ?? "-";

  return (
    <Box
      sx={{
        borderRadius: "10px",
        width: "100%",
      }}
    >
      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #DADADA",
          borderRadius: "10px",
          marginBottom: 1,
        }}
      >
        <Typography
          fontSize="14px"
          color="var(--primary-color)"
          fontWeight="500 !important"
          marginBottom="20px"
        >
          {t("maklumatPermohonan")}
        </Typography>

        <FormFieldRow
          label={<Label text={t("namaPertubuhan")} />}
          value={
            <DisabledTextField value={societyDetailData?.societyName ?? "-"} />
          }
        />

        <FormFieldRow
          label={<Label text={t("organizationCategory")} />}
          value={
            <DisabledTextField value={mainCategory?.categoryNameBm ?? "-"} />
          }
        />

        <FormFieldRow
          label={<Label text={t("organizationSubCategory2")} />}
          value={
            <DisabledTextField value={subCategory?.categoryNameBm ?? "-"} />
          }
        />

        <FormFieldRow
          label={<Label text={t("branchOrganization")} />}
          value={
            <DisabledTextField
              value={societyDetailData?.hasBranch ? "Ya" : "Tidak"}
            />
          }
        />

        <FormFieldRow
          label={
            <Label
              text={
                isMyLanguage
                  ? "Maklum Balas AJK Tertinggi"
                  : "Feedback from the Supreme Committee"
              }
            />
          }
          value={
            feedbacks.length > 0 ? (
              <Box
                sx={{
                  display: "flex",
                  gap: "12px",
                  alignItems: "center",
                  justifyContent: "center",
                  width: "100%",
                  height: "32px",
                  borderRadius: "10px",
                  border: "1px solid var(--primary-color)",
                }}
              >
                <FileAddIcon color="var(--primary-color)" />

                <Typography
                  fontSize="14px"
                  fontWeight="400 !important"
                  color="#666666"
                  sx={{
                    span: {
                      cursor: "pointer",
                      color: "#402DFF",
                      "&:hover": {
                        textDecoration: "underline",
                      },
                    },
                  }}
                >
                  Lihat Maklum balas AJK:{" "}
                  <span onClick={onClickMaklumbalas}>{feedbacks.length}</span>
                </Typography>
              </Box>
            ) : (
              <DisabledTextField
                value={
                  isMyLanguage
                    ? "Tiada Maklum Balas Diterima"
                    : "No Feedback Received"
                }
              />
            )
          }
        />
      </Box>

      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #DADADA",
          borderRadius: "10px",
          marginBottom: 1,
        }}
      >
        <Typography
          fontSize="14px"
          color="var(--primary-color)"
          fontWeight="500 !important"
          marginBottom="20px"
        >
          {t("personalInfoNewSec")}
        </Typography>

        <FormFieldRow
          label={<Label text={t("idType")} />}
          value={<DisabledTextField value="MyKad" />}
        />

        <FormFieldRow
          label={<Label text={t("idNumberPlaceholder")} />}
          value={
            <DisabledTextField
              value={secretaryDetailData?.identificationNo ?? "-"}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("gelaran")} />}
          value={
            <DisabledTextField
              value={getGelaranName(secretaryDetailData?.titleCode ?? "")}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("fullName")} />}
          value={
            <DisabledTextField
              value={secretaryDetailData?.newSecretaryName ?? "-"}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("gender")} />}
          value={<DisabledTextField value={t(getGender) ?? "-"} />}
        />

        <FormFieldRow
          label={<Label text={t("citizenship")} />}
          value={
            <DisabledTextField
              value={t(
                getCitizenshipLabel(
                  Number(secretaryDetailData?.citizenshipStatus)
                )
              )}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("dateOfBirth")} />}
          value={
            <DisabledTextField
              value={secretaryDetailData?.dateOfBirth ?? "-"}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("placeOfBirth")} />}
          value={
            <DisabledTextField
              value={secretaryDetailData?.placeOfBirth ?? "-"}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("occupation")} />}
          value={
            <DisabledTextField value={secretaryDetailData?.jobCode ?? "-"} />
          }
        />

        <FormFieldRow
          label={<Label text={t("residentialAddress")} />}
          value={
            <DisabledTextField
              value={secretaryDetailData?.residenceAddress ?? "-"}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("negeri")} />}
          value={
            <DisabledTextField
              value={getAddressListName(
                Number(secretaryDetailData?.residenceStateCode) ?? null
              )}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("daerah")} />}
          value={
            <DisabledTextField
              value={getAddressListName(
                Number(secretaryDetailData?.residenceDistrictCode) ?? null
              )}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("bandar")} />}
          value={
            <DisabledTextField
              value={secretaryDetailData?.residenceCity ?? "-"}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("poskod")} />}
          value={
            <DisabledTextField
              value={secretaryDetailData?.residencePostcode ?? "-"}
            />
          }
        />

        <FormFieldRow
          label={<Label text={t("email")} />}
          value={
            <DisabledTextField value={secretaryDetailData?.email ?? "-"} />
          }
        />

        <FormFieldRow
          label={<Label text={t("mobilePhoneNumber")} />}
          value={
            <Box sx={{ display: "flex", gap: "12px" }}>
              <DisabledTextField
                value={secretaryDetailData?.hpNo?.split(" ")[0] ?? "-"}
                sx={{ width: "80px" }}
              />
              <DisabledTextField
                value={secretaryDetailData?.hpNo?.split(" ")[1] ?? "-"}
              />
            </Box>
          }
        />

        <FormFieldRow
          label={<Label text={t("homePhoneNumber")} />}
          value={
            <Box sx={{ display: "flex", gap: "12px" }}>
              <DisabledTextField
                value={secretaryDetailData?.homeTelNo?.split(" ")[0] ?? "-"}
                sx={{ width: "80px" }}
              />
              <DisabledTextField
                value={secretaryDetailData?.homeTelNo?.split(" ")[1] ?? "-"}
              />
            </Box>
          }
        />

        <FormFieldRow
          label={<Label text={t("officePhoneNumber")} />}
          value={
            <Box sx={{ display: "flex", gap: "12px" }}>
              <DisabledTextField
                value={secretaryDetailData?.workTelNo?.split(" ")[0] ?? "-"}
                sx={{ width: "80px" }}
              />
              <DisabledTextField
                value={secretaryDetailData?.workTelNo?.split(" ")[1] ?? "-"}
              />
            </Box>
          }
        />
      </Box>

      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #DADADA",
          borderRadius: "10px",
          marginBottom: 1,
        }}
      >
        <Typography
          fontSize="14px"
          color="var(--primary-color)"
          fontWeight="500 !important"
          marginBottom="20px"
        >
          {t("employerInfo")}
        </Typography>

        <FormFieldRow
          label={<Label text={t("employerName")} />}
          value={
            <DisabledTextField
              value={secretaryDetailData?.employerName ?? "-"}
            />
          }
        />

        {secretaryDetailData?.employerAddressStatus === "0" && (
          <>
            <FormFieldRow
              label={<Label text={t("country")} />}
              value={
                <DisabledTextField
                  value={getAddressListName(
                    Number(secretaryDetailData?.employerCountryCode) ?? null
                  )}
                />
              }
            />
            <FormFieldRow
              align="flex-start"
              label={<Label text={t("employerAddress")} />}
              value={
                <DisabledTextField
                  value={secretaryDetailData?.employerAddress ?? "-"}
                  multiline
                  row={3}
                />
              }
            />
          </>
        )}

        {secretaryDetailData?.employerAddressStatus === "1" && (
          <>
            <FormFieldRow
              align="flex-start"
              label={<Label text={t("employerAddress")} />}
              value={
                <DisabledTextField
                  value={secretaryDetailData?.employerAddress ?? "-"}
                  multiline
                  row={3}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("negeri")} />}
              value={
                <DisabledTextField
                  value={getAddressListName(
                    Number(secretaryDetailData?.employerStateCode) ?? null
                  )}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("daerah")} />}
              value={<DisabledTextField value="-" />}
            />

            <FormFieldRow
              label={<Label text={t("bandar")} />}
              value={
                <DisabledTextField
                  value={secretaryDetailData?.employerCity ?? "-"}
                />
              }
            />

            <FormFieldRow
              label={<Label text={t("poskod")} />}
              value={
                <DisabledTextField
                  value={secretaryDetailData?.employerPostcode ?? "-"}
                />
              }
            />
          </>
        )}
      </Box>
    </Box>
  );
};

export default MaklumatSetiausahaSection;
