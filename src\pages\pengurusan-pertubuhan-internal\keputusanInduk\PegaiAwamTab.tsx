import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { formatDate, getLocalStorage } from "../../../helpers/utils";
import { NEW_PermissionNames, pageAccessEnum } from "../../../helpers/enums";

import {
  Box,
  Grid,
  Typography,
  IconButton,
  TextField,
  Select,
  MenuItem,
  debounce,
  FormHelperText,
  FormControl,
} from "@mui/material";
import DataTable, { IColumn } from "../../../components/datatable";

import { EditIcon } from "../../../components/icons";
import { CrudFilter } from "@refinedev/core";
import { useCallback, useEffect, useState } from "react";
import useQuery from "@/helpers/hooks/useQuery";
import AuthHelper from "@/helpers/authHelper";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
  "& span": {
    color: "red",
  },
};

type Props = {
  number?: number;
};

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "30px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const PegawaiAwamTab = ({ number }: Props) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState({
    organizationName: "",
    subOrganizationCategory: "",
    organizationCategory: "",
  });

  const hasReadPermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_INDUK.children.PEGAWAI_AWAM.label,
    pageAccessEnum.Read
  );

  const request = debounce((value) => {
    fetchPendingResult({
      filters: [
        {
          field: "pageSize",
          value: pageSize,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: page,
          operator: "eq",
        },
        {
          field: "isQuery",
          operator: "eq",
          value: "0",
        },
        {
          field: "categoryCode",
          value: formData.organizationCategory || "",
          operator: "eq",
        },
        {
          field: "subCategoryCode",
          value: formData.subOrganizationCategory || "",
          operator: "eq",
        },
        {
          field: "societyName",
          value: value,
          operator: "eq",
        },
      ],
    });
  }, 800);

  const debouceRequest = useCallback((value: any) => request(value), []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPage(1);
    const { value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      organizationName: value,
    }));
    debouceRequest(value);
    setFormErrors((prev) => ({ ...prev, organizationName: "" }));
  };

  const categories = getLocalStorage("category_list", []);
  const mainCategories = categories.filter((cat: any) => cat.level === 1);
  const subCategories = categories.filter((cat: any) => cat.level === 2);
  const subCategoriesOptions = subCategories
    ?.filter((items: any) => {
      return items.pid === parseInt(formData.organizationCategory);
    })
    .map((category: any) => ({
      value: category.id.toString(),
      label: category.categoryNameEn,
    }));

  const columns: IColumn[] = [
    {
      field: "societyNo",
      headerName: "No. PPM/NSID",
      flex: 1,
      align: "center",
    },
    {
      field: "name",
      headerName: t("officerName"),
      flex: 1,
      align: "center",
      // renderCell: (params: any) => params?.row?.creatorName ?? "-",
    },
    {
      field: "societyName",
      headerName: t("namaPertubuhan"),
      flex: 1,
      align: "center",
    },
    {
      field: "identificationNo",
      headerName: t("identificationNumber"),
      flex: 1,
      align: "center",
    },
    {
      field: "transferDate",
      headerName: t("tarikhAlir"),
      flex: 1,
      align: "center",
    },
    {
      field: "paymentDate",
      headerName: t("tarikhBayar"),
      flex: 1,
      align: "center",
      renderCell: (params: any) =>
        params?.row?.paymentDate ? formatDate(params?.row?.paymentDate) : "-",
    },
    {
      field: "roName",
      headerName: "RO",
      flex: 1,
      align: "center",
      renderCell: (params: any) => params?.row?.roName ?? "-",
    },
    {
      field: "stateName",
      headerName: t("state"),
      flex: 1,
      align: "center",
    },
    {
      field: "actions",
      headerName: t("action"),
      align: "center",
      renderCell: ({ row }: any) => {
        const id = btoa(row.id);
        return (
          <IconButton
            onClick={() =>
              navigate(
                `/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-induk/pegawai-awam/${id}`
              )
            }
            disabled={!hasReadPermission}
          >
            <EditIcon
              sx={{
                color: hasReadPermission
                  ? "var(--primary-color)"
                  : "var(--text-grey-disabled)",
                width: "1rem",
                height: "1rem",
              }}
            />
          </IconButton>
        );
      },
    },
  ];

  const {
    data: searchResult,
    isLoading: isSearchLoading,
    refetch: fetchPendingResult,
  } = useQuery({
    url: `society/roDecision/getAllPending/society/public_officer`,
    autoFetch: false,
  });

  let dataRo = [
    // {
    //   id: 1,
    //   societyName: "test",
    //   applicationType: "test",
    //   ro: "test",
    //   tarikhAlir: "test",
    //   stateName: "test",
    //   tarikhPermohonan: "test",
    //   societyNo: "test",
    // },
  ];
  if (!isSearchLoading) {
    dataRo = searchResult?.data?.data?.data || [];
  }

  const handleChangePage = (newPage: number) => {
    const filters: CrudFilter[] = [
      { field: "pageSize", value: pageSize, operator: "eq" },
      { field: "pageNo", value: newPage, operator: "eq" },
      { field: "isQuery", value: "0", operator: "eq" },
      {
        field: "categoryCode",
        value: formData.organizationCategory || "",
        operator: "eq",
      },
      {
        field: "subCategoryCode",
        value: formData.subOrganizationCategory || "",
        operator: "eq",
      },
      {
        field: "societyName",
        value: formData.organizationName || "",
        operator: "eq",
      },
    ];
    setPage(newPage);
    fetchPendingResult({ filters });
  };

  const handleRowPerPage = (newPageSize: number) => {
    const filters: CrudFilter[] = [
      { field: "pageSize", value: newPageSize, operator: "eq" },
      { field: "pageNo", value: 1, operator: "eq" },
      { field: "isQuery", value: "0", operator: "eq" },
      {
        field: "categoryCode",
        value: formData.organizationCategory || "",
        operator: "eq",
      },
      {
        field: "subCategoryCode",
        value: formData.subOrganizationCategory || "",
        operator: "eq",
      },
      {
        field: "societyName",
        value: formData.organizationName || "",
        operator: "eq",
      },
    ];
    setPageSize(newPageSize);
    fetchPendingResult({ filters });
  };

  useEffect(() => {
    setPage(1);
    fetchPendingResult({
      filters: [
        {
          field: "pageSize",
          value: pageSize,
          operator: "eq",
        },
        {
          field: "pageNo",
          value: 1,
          operator: "eq",
        },
        {
          field: "isQuery",
          operator: "eq",
          value: "0",
        },
        {
          field: "categoryCode",
          value: formData.organizationCategory || "",
          operator: "eq",
        },
        {
          field: "subCategoryCode",
          value: formData.subOrganizationCategory || "",
          operator: "eq",
        },
        {
          field: "societyName",
          value: formData.organizationName || "",
          operator: "eq",
        },
      ],
    });
  }, [formData.organizationCategory, formData.subOrganizationCategory]);

  const totalList = searchResult?.data?.data?.total ?? 0;

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("pegawaiAwamRegList")}
            </Typography>
            <Grid container spacing={2}>
              {/* organization category */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("organizationCategory")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <FormControl
                  fullWidth
                  required
                  error={!!formErrors.organizationCategory}
                >
                  <Select
                    size="small"
                    value={formData.organizationCategory}
                    displayEmpty
                    onChange={(e) => {
                      setFormData((prevState) => ({
                        ...prevState,
                        organizationCategory: e.target.value,
                      }));

                      // Reset sub-category when main category changes
                      setFormData((prevState) => ({
                        ...prevState,
                        subOrganizationCategory: "",
                      }));

                      setFormErrors((prev) => ({
                        ...prev,
                        organizationCategory: "",
                      }));
                    }}
                    renderValue={(selected) =>
                      selected
                        ? mainCategories.find(
                            (cat: any) => cat.id === parseInt(selected)
                          )?.categoryNameEn
                        : t("selectPlaceholder")
                    }
                  >
                    <MenuItem value="" disabled>
                      {t("selectPlaceholder")}
                    </MenuItem>
                    {mainCategories.map((category: any) => (
                      <MenuItem
                        key={category.id}
                        value={category.id.toString()}
                      >
                        {category.categoryNameEn}
                      </MenuItem>
                    ))}
                  </Select>
                  {formErrors.organizationCategory && (
                    <FormHelperText>
                      {formErrors.organizationCategory}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {/* sub organization category */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("organizationSubCategory2")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <FormControl
                  fullWidth
                  required
                  error={!!formErrors.subOrganizationCategory}
                >
                  <Select
                    size="small"
                    value={formData.subOrganizationCategory}
                    displayEmpty
                    onChange={(e) => {
                      setFormData((prevState) => ({
                        ...prevState,
                        subOrganizationCategory: e.target.value,
                      }));
                      setFormErrors((prev) => ({
                        ...prev,
                        subOrganizationCategory: "",
                      }));
                    }}
                    sx={{ 
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#DADADA !important",
                      },
                      backgroundColor:
                        subCategoriesOptions.length === 0
                          ? "#66666626"
                          : "#FFF",
                    }}
                    renderValue={(selected) => {
                      if (!selected) {
                        return (
                          <span style={{ color: "#aaa" }}>
                            {t("pleaseSelect")}
                          </span>
                        );
                      }
                      const selectedOption = subCategoriesOptions.find(
                        (item: any) => item.value === selected
                      );
                      return selectedOption ? selectedOption.label : selected;
                    }}
                    disabled={!formData.organizationCategory}
                  >
                    <MenuItem value="" disabled>
                      {t("selectPlaceholder")}
                    </MenuItem>
                    {subCategoriesOptions.map((item: any) => (
                      <MenuItem key={item.value} value={item.value}>
                        {item.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {formErrors.subOrganizationCategory && (
                    <FormHelperText>
                      {formErrors.subOrganizationCategory}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {/* organization name */}
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>{t("organizationName")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  fullWidth
                  required
                  name="organizationName"
                  value={formData.organizationName}
                  onChange={handleInputChange}
                  error={!!formErrors.organizationName}
                  helperText={formErrors.organizationName}
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/* ============= */}
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: "13px",
              padding: "15px 0",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              marginBottom: 1,
              backgroundColor: "var(--primary-color)",
            }}
          >
            <Typography
              fontWeight="500 !important"
              fontSize="36px"
              color="#FFF"
              textAlign="center"
              lineHeight="30px"
              sx={{
                "& span": {
                  fontSize: "20px",
                },
              }}
            >
              {totalList}
              <br />
              <span>{t("pegawaiAwamMainTitle")}</span>
            </Typography>
          </Box>

          <Box
            sx={{
              width: "100%",
              border: "0.5px solid #DADADA",
              borderRadius: "10px",
              padding: "22px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="37px"
            >
              {t("pegawaiAwamRegList")}
            </Typography>
            <DataTable
              columns={columns}
              rows={dataRo}
              page={page}
              rowsPerPage={pageSize}
              totalCount={totalList}
              isLoading={isSearchLoading}
              onPageChange={handleChangePage}
              onPageSizeChange={(newPageSize) => {
                setPage(1);
                handleRowPerPage(newPageSize);
              }}
            />
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default PegawaiAwamTab;
