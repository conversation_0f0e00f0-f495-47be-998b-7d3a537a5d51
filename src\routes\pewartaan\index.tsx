import { Route } from "react-router-dom";
import Pewartaan from '../../pages/pewartaan';
import { registerRoutes } from "../../helpers/routeDetector";
import { RouteGuard } from "../../components/RouteGuard";

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example: '/pewartaan': 'external',
  // Add your route registrations here
});

export const pewartaan = {
  routes: (
    <>
      <Route
        path="/pewartaan"
        element={
          <RouteGuard
            autoUpdatePortal={true}
            showDebugInfo={process.env.NODE_ENV === 'development'}
          >
            <Pewartaan />
          </RouteGuard>
        }
      />
    </>
  ),
};
