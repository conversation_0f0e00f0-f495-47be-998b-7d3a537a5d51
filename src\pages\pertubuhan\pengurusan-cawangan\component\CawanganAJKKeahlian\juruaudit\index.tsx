import { Box, Icon<PERSON>utton, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { ButtonOutline } from "@/components/button";
import {
  ApplicationStatusList,
  COMMITTEE_TASK_TYPE,
  JenisJuruaudit,
  JenisJuruauditType,
} from "@/helpers/enums";
import { EditIcon, EyeIcon, TrashIcon } from "@/components/icons";
import ConfirmationDialog from "@/components/dialog/confirm";
import useQuery from "@/helpers/hooks/useQuery";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { DataTable, IColumn } from "@/components";
import { FieldValues, useForm } from "react-hook-form";
import { useBranchContext } from "@/pages/pertubuhan/BranchProvider";
import { formatDate, useMutation } from "@/helpers";
import NewAlertDialog from "@/components/dialog/newAlert";

const JuruAudit: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [shouldFetch, setShouldFetch] = useState<boolean>(true);
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const { id: societyId } = useParams();
  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      searchQuery: undefined,
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");
  const {
    data: auditorListData,
    isLoading,
    refetch: fetchAuditorList,
  } = useQuery({
    url: "society/statement/auditor/getAll",
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux?.id },
      {
        field: "pageSize",
        value: pageSize,
        operator: "eq",
      },
      {
        field: "pageNo",
        value: page,
        operator: "eq",
      },
    ],
  });

  const totalList = auditorListData?.data?.data?.total ?? 0;
  const rowData = auditorListData?.data?.data?.data ?? [];

  const {
    isBlackListed,
    isAuthorized,
    isAliranModuleAccess,
    fetchAliranTugasAccessHandle,
    branchData,
  } = useBranchContext();

  useEffect(() => {
    fetchAliranTugasAccessHandle(COMMITTEE_TASK_TYPE.PENGURUSAN_AJK);
  }, []);

  useEffect(() => {
    fetchAuditorList();
    setShouldFetch(false);
  }, [shouldFetch]);

  const [openConfirm, setOpenConfirm] = useState<boolean>(false);

  const handleDaftarJuruaudit = () => {
    navigate("create");
  };

  const handleEditJuruaudit = (auditorId: string | number) => {
    navigate("create", {
      state: {
        auditorId: auditorId,
      },
    });
  };

  const handlViewJuruaudit = (auditorId: string | number) => {
    navigate("view", {
      state: {
        auditorId: auditorId,
        view: true,
      },
    });
  };

  const { fetchAsync: deleteJuruaudit, isLoading: isDeleting } = useMutation({
    method: "put",
    onSuccess: () => {
      setOpenConfirm(false);
      setDialogAlertSaveOpen(true);
      fetchAuditorList();
    },
  });

  const confirmDeleteJuruaudit = async () => {
    await deleteJuruaudit(
      {},
      () => `society/statement/auditor/${auditorId}/delete`
    );
  };

  const [auditorId, setAuditorId] = useState<string | number>();

  const handleDeleteJuruaudit = (auditorId: string | number) => {
    setAuditorId(auditorId);
    setOpenConfirm(true);
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const isPendingStatus =
    Number(branchData?.applicationStatusCode) === 2 &&
    branchData?.status === "008";
    
  const isAccessible =
    !isBlackListed &&
    (isAuthorized || isAliranModuleAccess) &&
    !isPendingStatus;

  const [dialogAlertSaveOpen, setDialogAlertSaveOpen] = useState(false);

  const closeSuccess = () => {
    setDialogAlertSaveOpen(false);
  };

  const columns: IColumn[] = [
    {
      field: "name",
      headerName: t("name"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>{row?.name}</Box>
        );
      },
    },
    {
      field: "auditorType",
      headerName: t("auditorType"),
      flex: 1,
      renderCell: ({ row }: any) => {
        const auditor = row?.auditorType as keyof JenisJuruauditType;
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {JenisJuruaudit[auditor]}
          </Box>
        );
      },
    },
    {
      field: "mykad/lesen",
      headerName: t("mykad/lesen"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.auditorType === "D" ? row?.identificationNo : row?.licenseNo}
          </Box>
        );
      },
    },
    {
      field: "email",
      headerName: t("email"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.email}
          </Box>
        );
      },
    },
    {
      field: "tarikhLantik",
      headerName: t("tarikhLantik"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {formatDate(row?.appointmentDate)}
          </Box>
        );
      },
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {t(
              ApplicationStatusList.find((item) => item.id === row.status)
                ?.value || "-"
            )}
          </Box>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params.row;
        return (
          <Box sx={{ display: "flex", justifyContent: "center" }}>
            {row.status === "001" ? (
              isAccessible ? (
                <>
                  {/* <IconButton onClick={() => handleEditJuruaudit(row.id)}>
                    <EditIcon
                      sx={{
                        color: "var(--primary-color)",
                        width: "1rem",
                        height: "1rem",
                      }}
                    />
                  </IconButton> */}
                  <IconButton onClick={() => handlViewJuruaudit(row.id)}>
                    <EyeIcon
                      sx={{
                        color: "#666666",
                        width: "1rem",
                        height: "1rem",
                      }}
                    />
                  </IconButton>
                  <IconButton onClick={() => handleDeleteJuruaudit(row.id)}>
                    <TrashIcon
                      sx={{
                        color: "var(--error)",
                        width: "1rem",
                        height: "1rem",
                      }}
                    />
                  </IconButton>
                </>
              ) : (
                <></>
              )
            ) : (
              <IconButton onClick={() => handlViewJuruaudit(row.id)}>
                <EyeIcon
                  sx={{
                    color: "#666666",
                    width: "1rem",
                    height: "1rem",
                  }}
                />
              </IconButton>
            )}
          </Box>
        );
      },
    },
  ];

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "bold" }}>
              {t("peringatan")} :
            </span>{" "}
            Sila pastikan bilangan Juruaudit yang berstatus aktif mengikut
            bilangan di dalam perlembagaan.
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            textAlign: "center",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {t("bilanganJuruauditTerkini")}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {totalList} Orang
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("auditorList")}
          </Typography>
          <Box sx={{ my: 3 }} display={"flex"} justifyContent={"flex-end"}>
            {isAccessible ? (
              <ButtonOutline onClick={handleDaftarJuruaudit}>
                {t("createAuditor")}
              </ButtonOutline>
            ) : (
              <></>
            )}
          </Box>

          <DataTable
            columns={columns}
            rows={rowData}
            page={page}
            rowsPerPage={pageSize}
            totalCount={totalList}
            onPageChange={(newPage) => setValue("page", newPage)}
            onPageSizeChange={(newPageSize) => {
              setValue("page", 1);
              setValue("pageSize", newPageSize);
            }}
            isLoading={isLoading}
          />
        </Box>
      </Box>

      <ConfirmationDialog
        status={1}
        open={openConfirm}
        onClose={() => setOpenConfirm(false)}
        title={t("confirmDelete")}
        message={`${t("confirmDeleteAudit")}?`}
        onConfirm={confirmDeleteJuruaudit}
        onCancel={() => setOpenConfirm(false)}
        turn={true}
        buttonPrimaryLabel={t("return")}
        isMutation={isDeleting}
      />

      <NewAlertDialog
        open={dialogAlertSaveOpen}
        onClose={() => closeSuccess()}
        message={t("auditorDeletedSuccessfully")}
      />
    </>
  );
};

export default JuruAudit;
