import React from "react";
import type { TFunction } from "i18next";
import Box from "@mui/material/Box";
import { Grid, TextField, Typography } from "@mui/material";
import Input from "../../../../components/input/Input";
import {
  Control,
  Controller,
  FieldValues,
  UseFormGetValues,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { formatAndValidateNumber } from "@/helpers";

export const CreatePernyataanPendapatanPersatuan: React.FC<{
  t: TFunction;
  control: Control<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  watch: UseFormWatch<FieldValues>;
  checked: boolean;
}> = ({ t, watch, setValue, getValues, control, checked }) => {
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isManager = useSelector(getUserPermission);

  const renderInputGroup = (
    title: string,
    items: { label: string; variable: string }[]
  ) => (
    <>
      <Box
        sx={{
          background: "white",
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Grid container spacing={2} sx={{display:"flex"}}>
          <Grid item xs={4}>
            <Typography
              sx={{
                color: "var(--primary-color)",
                fontSize: "14px",
                fontWeight: "500 !important",
              }}
            >
              {t("totalIncome")} 
            </Typography>
          </Grid>
          <Grid item xs={8}>
            {items.map((item, index) => (
              <Controller
                name={item.variable}
                key={index}
                control={control}
                defaultValue={getValues(item.variable)}  
                render={({ field }) => {
                  return (
                    <TextField
                      size="small"
                      fullWidth
                      disabled={checked || (!isManager && !isAliranTugasAccess)} 
                      value={getValues(item.variable)}
                      placeholder="0"
                      onChange={(e) => {
                        const formattedValue = formatAndValidateNumber(
                          e.target.value
                        );
                        if (formattedValue !== null) {
                          field.onChange(formattedValue);  
                          setValue("totalIncome", formattedValue);  
                        }
                      }}
                    />
                  );
                }}
              />
            ))}
          </Grid>
        </Grid>
      </Box>
    </>
  );

  return (
    <>
      {/* {renderInputGroup("operatingIncomeSection", [
        { label: "entranceFeeIncome", variable: "feeIncome" },
        { label: "membershipFeeIncome", variable: "feeIncomeMember" },
        { label: "donationIncome", variable: "donationIncome" },
        { label: "otherOperatingIncome", variable: "othersIncome" },
      ])}

      {renderInputGroup("fundraisingActivitiesIncomeSection", [
        { label: "dinnerEventIncome", variable: "foodRevenue" },
        { label: "charitySaleIncome", variable: "bookRevenue" },
        { label: "servicesIncome", variable: "serviceRevenue" },
        { label: "otherFundraisingIncome", variable: "otherRevenue" },
      ])}

      {renderInputGroup("investmentIncomeSection", [
        { label: "rentalIncome", variable: "rentalInvestment" },
        { label: "dividendIncome", variable: "dividendInvestment" },
        { label: "fixedDepositInterestIncome", variable: "interestInvestment" },
        { label: "propertyProfitIncome", variable: "propertyInvestment" },
        { label: "otherInvestmentIncome", variable: "otherInvestment" },
      ])}

      {renderInputGroup("grantsSection", [
        { label: "governmentAgencyGrant", variable: "govGrant" },
        { label: "privateAgencyGrant", variable: "privateGrant" },
        { label: "individualGrant", variable: "individualGrant" },
        { label: "otherGrants", variable: "otherGrant" },
      ])}

      {renderInputGroup("lainLainPendapatan", [
        { label: "lainLainPendapatan", variable: "otherIncome" },
      ])} */}

      {renderInputGroup("", [
        { label: "totalIncome", variable: "totalIncome" },
      ])}
      
    </>
  );
};

export default CreatePernyataanPendapatanPersatuan;
