import { <PERSON>, Grid, TextField, Typography } from "@mui/material";
import { t } from "i18next";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  addressType,
  CitizenshipStatus,
  DocumentUploadType,
  getAddressList,
  getLocalStorage,
  IdTypes,
  ListGelaran,
  ListGender,
  OrganisationPositions,
  otherPositionSwitchList,
  useQuery,
} from "@/helpers";
import FileUploader from "@/components/input/fileUpload";
import { API_URL } from "@/api";
import { useCustom } from "@refinedev/core";
import { ButtonPrimary } from "@/components";
import Input from "@/components/input/Input";
import { useSelector } from "react-redux";

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

export interface Committee {
  id: string | number;
  applicationStatusCode: string;
  batalFlat: string | null;
  branchId: string | number;
  branchNo: string | null;
  citizenshipStatus: any;
  committeeAddress: any;
  committeeAddressStatus: string | null;
  committeeCity: any;
  committeeCountryCode: any;
  committeeDistrict: any;
  committeeEmployerAddress: any;
  committeeEmployerAddressStatus: any;
  committeeEmployerCity: any;
  committeeEmployerCountryCode: any;
  committeeEmployerDistrict: any;
  committeeEmployerName: any;
  committeeEmployerPostcode: any;
  committeeEmployerStateCode: any;
  committeeIcNo: string;
  committeeName: string;
  committeeSmallDistrict: any;
  committeeStateCode: any;
  createdBy: string;
  createdDate: string;
  dateOfBirth: any;
  designationCode: any;
  email?: any;
  phoneNumber?: any;
  homePhoneNumber?: any;
  officePhoneNumber?: any;
  titleCode?: any;
  gender?: any;
  identityType?: any;
  jobCode: any;
  placeOfBirth: string;
  postcode?: any;
  pegHarta?: any;
  otherPosition?: any;
  otherDesignationCode?: any;
}

function BranchPaparanAJK() {
  // const ajkPaparan = useSelector(AJKpaparan);
  const occupationList = getLocalStorage("occupation_list", null);
  const addressList = getLocalStorage("address_list", null);
  const { id, mid } = useParams();

  const branchId = id;
  const memberId = mid;
  const [committee, setCommittee] = useState<Committee>();

  const { data: branchList, isLoading: isLoadingBranch } = useCustom<any>({
    url: `${API_URL}/society/branch/getById/${branchId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: branchId !== null,
      retry: false,
      cacheTime: 0,
    },
  });

  const navigate = useNavigate();
  const addressData = useSelector(getAddressList);

  const getDistrict = (val = null) => {
    const address = addressList
      .filter((items: any) => items.id === Number(val))
      .map((item: any) => ({ value: item.id, label: item.name }));

    return address?.[0]?.label || "-";
  };

  const getStateName = (stateCode: any) => {
    const stateName = addressList.filter((i: any) => i.id == stateCode);
    return stateName[0]?.name;
  };
  const [formData, setFormData] = useState({
    position: "",
    idType: "",
    icNo: "",
    title: "",
    name: "",
    gender: "",
    citizen: "",
    dateOfBirth: "",
    placeOfBirth: "",
    pekerjaan: "",
    residentialAddress: "",
    committeeStateCode: "",
    committeeDistrict: "",
    committeeCity: "",
    postcode: "",
    email: "",
    phoneNumber: "",
    homePhoneNumber: "",
    officePhoneNumber: "",
    committeeEmployerName: "",
    committeeEmployerAddress: "",
    committeeEmployerStateCode: "",
    committeeEmployerDistrict: "",
    committeeEmployerCity: "",
    committeeEmployerAddressStatus: null,
    committeeEmployerCountryCode: null,
    committeeEmployerPostcode: "",
    jobCode: "",
    designationCode: null,
    otherPosition: null,
    otherDesignationCode: null,
  });

  const [positionsTranslatedList, setPositionsTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);

  useEffect(() => {
    const newPList = OrganisationPositions.map((item) => ({
      ...item,
      label: t(item.label),
    }));

    setPositionsTranslatedList(newPList);
  }, [t]);

  const {
    data: branchCommittee,
    isLoading,
    refetch,
  } = useQuery({
    url: `society/branch/committee/${memberId}`,
    enabled: memberId !== null,
    onSuccess: (data) => {
      setCommittee(data?.data?.data);
      // console.log("committee", data?.data?.data);
    },
  });

  useEffect(() => {
    if (committee) {
      setFormData({
        ...formData,
        position: positionsTranslatedList[committee.designationCode]?.label,
        idType: committee?.identityType,
        icNo: committee?.committeeIcNo,
        title: committee?.titleCode,
        name: committee?.committeeName,
        gender: committee?.gender,
        citizen: committee.citizenshipStatus,
        dateOfBirth: committee?.dateOfBirth ? committee?.dateOfBirth : "",
        placeOfBirth: committee?.placeOfBirth,
        pekerjaan: committee.jobCode,
        residentialAddress: committee?.committeeAddress,
        committeeStateCode: committee?.committeeStateCode
          ? getStateName(committee?.committeeStateCode)
          : "-",
        committeeDistrict: committee?.committeeDistrict
          ? getDistrict(committee.committeeDistrict)
          : "-",
        committeeCity: committee?.committeeCity,
        postcode: committee?.postcode,
        email: committee?.email,
        phoneNumber: committee?.phoneNumber,
        homePhoneNumber: committee?.homePhoneNumber,
        officePhoneNumber: committee?.officePhoneNumber,
        committeeEmployerName: committee?.committeeEmployerName,
        committeeEmployerAddress: committee?.committeeEmployerAddress,
        committeeEmployerAddressStatus:
          committee?.committeeEmployerAddressStatus,
        committeeEmployerStateCode: committee?.committeeEmployerStateCode
          ? getStateName(committee?.committeeEmployerStateCode)
          : "-",
        committeeEmployerDistrict: committee?.committeeEmployerDistrict
          ? getDistrict(committee?.committeeEmployerDistrict)
          : "-",
        committeeEmployerCity: committee?.committeeEmployerCity,
        committeeEmployerPostcode: committee?.committeeEmployerPostcode,
        committeeEmployerCountryCode: committee?.committeeEmployerCountryCode,
        jobCode: committee?.jobCode,
        designationCode: committee?.designationCode,
        otherPosition: committee?.otherPosition,
        otherDesignationCode: committee?.otherDesignationCode,
      });
    }
  }, [committee]);

  const goback = () => {
    navigate(-1);
  };

  // console.log("formData", formData);

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            <Typography
              fontWeight="500 !important"
              fontSize="14px"
              lineHeight="21px"
            >
              {branchList?.data?.data?.name}
            </Typography>
          </Box>
        </Box>
      </Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        {/*  */}
        <Box
          sx={{
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("chairman")}
          </Typography>

          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("jawatan")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  sx={{
                    backgroundColor: (theme) =>
                      theme.palette.action.disabledBackground,
                  }}
                  value={
                    !otherPositionSwitchList.some(
                      (item) => item.value === Number(formData.designationCode)
                    )
                      ? t(
                          OrganisationPositions.find(
                            (item) =>
                              item.value === Number(formData.designationCode)
                          )?.label || "-"
                        )
                      : t(
                          formData?.otherPosition ??
                            t(
                              OrganisationPositions.find(
                                (item) =>
                                  item.value ===
                                  Number(formData.designationCode)
                              )?.label || "-"
                            )
                        )
                  }
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      position: e.target.value,
                    })
                  }
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/*  */}
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("chairmanPersonalInfo")}
          </Typography>

          <Box>
            <Input
              value={formData.idType}
              name="idType"
              disabled
              required
              type="select"
              options={IdTypes.map((item) => ({
                ...item,
                label: t(item.label),
              }))}
              label={t("idTypeCapitalizedOnlyFirstLetter")}
            />
            <Input
              disabled
              value={formData.icNo}
              name="identificationNo"
              required
              label={t("idNumberCapitalizedOnlyFirstLetter")}
            />
            <Input
              disabled
              value={formData.title}
              name="title"
              required
              type="select"
              options={ListGelaran.map((item) => ({
                ...item,
                label: t(item.label),
              }))}
              label={t("title")}
            />
            <Input
              disabled
              value={formData.name ?? "-"}
              name="fullName"
              required
              label={t("fullName")}
            />

            <Input
              required
              type="select"
              value={formData.gender}
              options={ListGender.map((option) => ({
                ...option,
                label: t(option.label),
              }))}
              disabled
              label={t("gender")}
            />
            <Input
              required
              label={t("citizenship")}
              value={Number(formData.citizen)}
              options={CitizenshipStatus.map((item) => ({
                ...item,
                label: t(item.label),
              }))}
              type="select"
              disabled
            />
            <Input
              disabled
              value={formData.dateOfBirth}
              name="dateOfBirth"
              required
              label={t("dateOfBirth")}
              type="date"
            />
            <Input
              disabled
              value={formData.placeOfBirth}
              name="placeOfBirth"
              required
              label={t("placeOfBirth")}
            />
            <Input
              disabled
              value={formData.jobCode}
              name="pekerjaan"
              options={occupationList.map((item: any) => ({
                ...item,
                label: t(item.label),
              }))}
              type="select"
              required
              label={t("occupation")}
            />
            <Input
              disabled
              value={formData.residentialAddress ?? "-"}
              name="residentialAddress"
              required
              label={t("residentialAddress")}
            />

            {/*=========== committeeStateCode section ========*/}
            <Input
              disabled
              value={formData.committeeStateCode ?? "-"}
              name="committeeStateCode"
              required
              label={t("state")}
            />
            <Input
              disabled
              value={formData.committeeDistrict ?? "-"}
              name="committeeDistrict"
              required
              label={t("district")}
            />
            <Input
              disabled
              value={formData.committeeCity ?? "-"}
              name="committeeCity"
              required
              label={t("city")}
            />

            <Input
              disabled
              value={formData.postcode ?? "-"}
              name="postcode"
              required
              label={t("postcode")}
            />
            <Input
              disabled
              value={formData.email ?? "-"}
              name="email"
              required
              label={t("email")}
            />
            <Input
              disabled
              value={
                formData.phoneNumber && formData.officePhoneNumber.trim() !== ""
                  ? formData.phoneNumber
                  : "-"
              }
              name="phoneNumber"
              required
              label={t("phoneNumber")}
            />
            <Input
              disabled
              value={
                formData.homePhoneNumber &&
                formData.homePhoneNumber.trim() !== ""
                  ? formData.homePhoneNumber
                  : "-"
              }
              name="homePhoneNumber"
              required
              label={t("homePhoneNumber")}
            />
            <Input
              disabled
              value={
                formData.officePhoneNumber &&
                formData.officePhoneNumber.trim() !== ""
                  ? formData.officePhoneNumber
                  : "-"
              }
              name="officePhoneNumber"
              required
              label={t("officePhoneNumber")}
            />
          </Box>
        </Box>
        {/*  */}
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("employerInfo")}
          </Typography>

          <Box>
            <Input
              disabled
              value={formData.committeeEmployerName ?? "-"}
              name="committeeEmployerName"
              required
              label={t("employerName")}
            />
            <Input
              disabled
              value={formData.committeeEmployerAddressStatus ?? "-"}
              name="committeeEmployerAddress"
              required
              type="select"
              options={addressType.map((item) => ({
                ...item,
                label: t(item.label),
              }))}
              label={t("employerAddress")}
            />
            <Input
              isLabel={false}
              value={formData.committeeEmployerAddress ?? "-"}
              multiline
              rows={3}
              disabled
            />
            {formData?.committeeEmployerAddressStatus === "0" ? (
              <Input
                label={t("country")}
                value={Number(formData?.committeeEmployerCountryCode) ?? "-"}
                type="select"
                options={addressData
                  .filter((item: any) => item.pid === 0)
                  .map((item: any) => ({
                    label:
                      item.name.charAt(0) + item.name.slice(1).toLowerCase(),
                    value: item.id,
                  }))}
                disabled
              />
            ) : null}
            {formData?.committeeEmployerAddressStatus === "1" ? (
              <>
                <Input
                  value={
                    formData.committeeEmployerStateCode
                      ? formData.committeeEmployerStateCode
                      : "-"
                  }
                  name="committeeEmployerStateCode"
                  disabled
                  // options={StateList}
                  // type="select"
                  label={t("state")}
                />
                <Input
                  value={
                    formData.committeeEmployerDistrict
                      ? formData.committeeEmployerDistrict
                      : "-"
                  }
                  name="committeeEmployerDistrict"
                  label={t("district")}
                  disabled
                />
                <Input
                  value={
                    formData.committeeEmployerCity
                      ? formData.committeeEmployerCity
                      : "-"
                  }
                  disabled
                  name="committeeEmployerCity"
                  label={t("city")}
                />
                <Input
                  value={
                    formData.committeeEmployerPostcode
                      ? formData.committeeEmployerPostcode
                      : "-"
                  }
                  disabled
                  name="committeeEmployerPostcode"
                  label={t("postcode")}
                />
              </>
            ) : null}
          </Box>
        </Box>
        {/*  */}

        <Box>
          <Box sx={{ textAlign: "left", mt: 2 }}>
            {committee?.id && (
              <FileUploader
                key={committee?.id}
                title={t("ajkEligibilityCheck")}
                type={DocumentUploadType.CITIZEN_COMMITTEE}
                societyId={branchList?.data?.data?.societyId}
                branchId={branchId}
                icNo={committee?.committeeIcNo}
                disabled={true}
                branchCommitteeId={committee?.id}
                validTypes={[
                  "text/plain",
                  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                  "application/msword",
                  "application/pdf",
                ]}
                // onUploadComplete={handleUploadComplete}
              />
            )}
          </Box>
        </Box>

        <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
          <ButtonPrimary onClick={goback}>{t("back")}</ButtonPrimary>
        </Box>
      </Box>
    </>
  );
}

export default BranchPaparanAJK;
