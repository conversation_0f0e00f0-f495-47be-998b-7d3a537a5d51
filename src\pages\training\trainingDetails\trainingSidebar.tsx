import React, {useState} from "react";
import {LinearProgress, Typography} from "@mui/material";
import Box from "@mui/material/Box";
import {TrainingRequiredIcon} from "@/components/icons/trainingRequired";
import {PaticipantsIcon} from "@/components/icons/participants";
import {DurationIcon} from "@/components/icons/duration";
import {TrainingDoneIcon} from "@/components/icons/trainingDone";
import TrainingStepper from "@/pages/training/trainingDetails/stepper";
import {Simulate} from "react-dom/test-utils";
import progress = Simulate.progress;
import {trainingLevelColors} from "@/pages/training/trainingFragment";
import {useTranslation} from "react-i18next";


interface TrainingSidebarProps {
  course: any;
  item: any[];
  currentPage: number;
  currentProgress: number;
}

const TrainingSidebar: React.FC<TrainingSidebarProps> = ({course, item , currentPage, currentProgress}) => {

  /*const item = {
    required: true,
    requiredIcon: <TrainingRequiredIcon/>,
    image: '../latihanSample/images4.png',
    type: "Kepimpinan",
    title: "Kursus Induksi Jabatan Pendaftaran Pertubuhan Malaysia (JPPM)",
    level: "Tahap Mudah",
    levelColor: "#CE4444B2",
    points: 0,
    participants: 0,
    participantsIcon: <PaticipantsIcon/>,
    duration: "1.5 Jam pembelajaran",
    durationIcon: <DurationIcon/>,
    progress: 50
  }*/
  const {t, i18n} = useTranslation();
  const hour = Math.floor(course.duration/60);
  const minute = course.duration % 60;
  //console.log(currentProgress,course);

  // @ts-ignore
  const backgroundColor: string = trainingLevelColors[item.difficultyLevel];

  return (
    <>
      <Box sx={{width: "30%",}}>
        <Box
          sx={{
            //flex: 5,
            borderRadius: 2.5,
            backgroundColor: "#fff",
            //flex: 5,
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Box
            sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              border: "1px solid #D9D9D9",
              //flex: 5,
              px: 5,
              py: 2,
              mb: 1,
            }}
          >
            <Box>
              <Typography
                sx={{
                  color: "#666666",
                  pt: 3,
                  fontWeight: "500",
                  fontSize: 40,
                }}
              >
                {course.title}
              </Typography>
            </Box>
            <Box>
              <Typography
                sx={{
                  color: "#666666",
                  pt: 3,
                  fontWeight: "400",
                  fontSize: 14,
                }}
              >
                {course.description}
              </Typography>
            </Box>
            <Box
              sx={{
                display: "flex",
                flexDirection: "row",
                width: "55%"
              }}
            >
              <Box
                sx={{
                  //display:"block",
                  backgroundColor: backgroundColor,
                  borderRadius: 5,
                  margin: "auto",
                  //ml:1,
                  flex: 1,
                  //width: "60%",
                  py: 1,
                  //px: 2
                }}
              >
                <Typography
                  sx={{
                    color: "#fff",
                    lineHeight: "100%",
                    fontWeight: "500",
                    fontSize: 5,
                    textAlign: "center"
                  }}
                >
                  {course.difficultyLevel}
                </Typography>
              </Box>
              <Box
                sx={{flex: 1, py: 1, px: 1}}
              >
                <Typography
                  sx={{
                    color: "var(--primary-color)",
                    fontWeight: "500",
                    lineHeight: "18px",
                    fontSize: 9,
                  }}
                >
                  {`${course.totalPoints ?? 0} Points`}
                </Typography>
              </Box>
            </Box>
            <Box sx={{
              p: 0,
              m: 0,
              height: 30,
              display: "flex",
              flexDirection: "row",
            }}>
              <Box
                sx={{py: 1, px: 1}}
              >
                <DurationIcon/>
              </Box>
              <Box
                sx={{py: 1, px: 0}}
              >
                <Typography
                  sx={{
                    color: "#666666",
                    lineHeight: "18px",
                    fontWeight: "400",
                    fontSize: 10,
                  }}
                >
                  {`${t("timeUnitHour", {
                    count: hour,
                  })} ${t("timeUnitMinute", {
                    count: minute,
                  })}`}
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
        <Box
          sx={{
            //flex: 5,
            borderRadius: 2.5,
            backgroundColor: "#fff",
            //flex: 5,
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Box
            sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              border: "1px solid #D9D9D9",
              //flex: 5,
              px: 5,
              py: 2,
              mb: 1,
            }}
          >
            <Typography
              sx={{
                color: "#666666",
                lineHeight: "18px",
                fontWeight: "400",
                fontSize: 12,
              }}
            >
              {t("learningContents")}
            </Typography>
            <Typography
              sx={{
                color: "#666666",
                lineHeight: "18px",
                fontWeight: "400",
                fontSize: 12,
                textAlign: "right",
              }}
            >
              {`${currentProgress.toFixed(0) ?? 0} %`}
            </Typography>
            <LinearProgress
              variant="determinate"
              value={currentProgress ?? 0}
              sx={{
                mt:1,
                flex: 1,
                height: 15,
                borderRadius: 3,
                backgroundColor: "#E0E0E0",
                "& .MuiLinearProgress-bar": {
                  backgroundColor: "#00BCD4",
                  borderRadius: 3,
                },
              }}
            />
          </Box>
        </Box>
        {
          item.map((e,i) => {
            return <TrainingStepper key={i} currentPage={currentPage} page={i} title={e.title} isChapter={true}/>
          })
        }
        <TrainingStepper currentPage={currentPage} page={item.length}  title={"Kuiz dan pemahaman"}/>
        <TrainingStepper currentPage={currentPage} page={item.length + 1} title={"Keputusan"}/>
        <TrainingStepper currentPage={currentPage} page={item.length+2} title={"Sijil dan maklumbalas"}/>
      </Box>
    </>
  );
}

export default TrainingSidebar;
