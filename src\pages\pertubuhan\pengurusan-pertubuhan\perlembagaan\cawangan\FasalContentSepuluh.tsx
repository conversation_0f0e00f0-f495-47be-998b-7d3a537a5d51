import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { ButtonOutline, ButtonPrimary } from "../../../../../components/button";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustomMutation } from "@refinedev/core";
import { handleSaveContent } from "../helper/handleSaveContent";
import { handleSaveValue } from "../helper/handleSaveValue";
import { API_URL } from "../../../../../api";
import { useSelector } from "react-redux";
import { FasalContentProps } from "../Fasal";
import { RegExNumbers } from "../../../../../helpers/enums";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";

interface FasalContentSepuluhCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clauseContent: string;
}

export const FasalContentSepuluhCawangan: React.FC<FasalContentProps> = ({
  activeStep,
  setActiveStep,
  clause,
}) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [jenisJuruAudit, setJenisJuruAudit] = useState("");
  const [bilanganInternal, setBilanganInternal] = useState("");
  const [bilanganEksternal, setBilanganEksternal] = useState("");
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [tempohJawatan, setTempohJawatan] = useState(t("setahun"));

  const [dataId, setDataId] = useState(0);
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  //const [clauseContent, setClauseContent] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);

  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!bilanganInternal && jenisJuruAudit === t("internal")) {
      errors.bilanganInternal = t("fieldRequired");
    }

    if (!bilanganEksternal && jenisJuruAudit === t("external")) {
      errors.bilanganEksternal = t("fieldRequired");
    }

    if (!pemilihanAjk) {
      errors.pemilihanAjk = t("fieldRequired");
    }

    return errors;
  };

  /*const clauseContent = `
              ${
                jenisJuruAudit == t("internal")
                  ? `1. ${
                      bilanganInternal != ""
                        ? bilanganInternal
                        : "<<Bilangan Juruaudit Dalam>>"
                    } orang yang bukan Ahli Jawatankuasa Pertubuhan hendaklah dilantik dalam Mesyuarat Agung ${
                      pemilihanAjk != ""
                        ? pemilihanAjk
                        : "<<Jenis Mesyuarat Agung>>"
                    } sebagai Juruaudit Dalam. Mereka yang memegang jawatan selama ${
                      tempohJawatan != ""
                        ? tempohJawatan
                        : "<<Tempoh Pelantikan>>"
                    } boleh dilantik semula.`
                  : `1. Juruaudit luar bertauliah boleh dilantik dalam Mesyuarat Agung/ Mesyuarat Jawatankuasa bagi tempoh ${
                      tempohJawatan != ""
                        ? tempohJawatan
                        : "<<Tempoh Pelantikan Jawatankuasa>>"
                    }.`
              }

              2. Juruaudit dalam dan/atau luar bertauliah adalah dikehendaki memeriksa penyata kewangan Pertubuhan bagi setiap berakhirnya tahun kewangan. Juruaudit hendaklah membuat perakuan dan menandatangi penyata kewangan tersebut untuk pertimbangan Mesyuarat Jawatankuasa atau Mesyuarat Agung ${
                pemilihanAjk != "" ? pemilihanAjk : "<<Jenis Mesyuarat Agung>>"
              }. Mereka juga dikehendaki oleh Pengerusi untuk mengaudit penyata kewangan Pertubuhan pada bila-bila masa dalam tempoh perkhidmatan mereka dan membuat laporan kepada Jawatankuasa.

              3. Penyata kewangan bagi tempoh 12 bulan hendaklah disediakan oleh Bendahari dan diperiksa oleh Juruaudit dalam dan/atau luar bertauliah dengan segera setelah tamat tahun kewangan. Penyata kewangan yang telah diaudit itu hendaklah diedarkan untuk makluman ahli-ahli dan dikemukakan untuk diluluskan oleh Mesyuarat Agung ${
                pemilihanAjk != "" ? pemilihanAjk : "<<Jenis Mesyuarat Agung>>"
              } yang berikut. Setiap salinan dokumen tersebut hendaklah disimpan di alamat tempat urusan Pertubuhan untuk makluman ahli.

              4. Jika seorang Juruaudit meninggal dunia atau meletakkan jawatan atau ditamatkan perkhidmatan, maka Jawatankuasa hendaklah melantik ahli lain atau Juruaudit dalam dan/atau luar bertauliah untuk mengisi kekosongan tersebut sehingga Mesyuarat Agung ${
                pemilihanAjk != "" ? pemilihanAjk : "<<Jenis Mesyuarat Agung>>"
              } diadakan.
`;*/

  //const clause10 = localStorage.getItem("clause10");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  useEffect(() => {
    if (clause) {
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      setIsEdit(clause.edit);
      //const clause10Data = JSON.parse(clause10);
      setDataId(clause.id);
      if (clause.clauseContentId) {
        //setClauseContent(clause10Data.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      if(clause.constitutionValues.length > 0){ 
      const fieldMappings: Record<string, (value: string) => void> = {
        "Jenis Juruaudit": setJenisJuruAudit,
        "Bilangan Juruaudit Dalam": setBilanganInternal,
        "Bilangan Juruaudit Luar": setBilanganEksternal,
        "Jenis Mesyuarat Agung": setPemilihanAjk,
        "Tempoh Pelantikan Jawatankuasa": setTempohJawatan
      };

      Object.values(fieldMappings).forEach(setter => setter(''));
      
      if(clause.constitutionValues){
        clause.constitutionValues.forEach((item:any) => {
          const setter = fieldMappings[item.titleName];
          if (setter && item.definitionName) {
            setter(item.definitionName);
          }
        });
      } 
    }
    }
  }, [clause]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id } = useParams();

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  const internalAuditor =
    "1. <<bilangan juruaudit dalam>> orang yang bukannya Ahli Jawatankuasa Pertubuhan hendaklah dilantik dalam Mesyuarat Agung <<jenis mesyuarat agung>> sebagai Juruaudit dalam. Mereka yang memegang jawatan selama <<tempoh pelantikan jawatankuasa>> boleh dilantik semula.";
  const externalAuditor =
    "1. Juruaudit luar bertauliah boleh dilantik dalam Mesyuarat Agung/Mesyuarat Jawatankuasa bagi tempoh <<tempoh pelantikan jawatankuasa>> tahun.";

  let clauseContent = clause.clauseContent;

  clauseContent = clauseContent.replaceAll(
    /Display for both jenis juruaudit \(continue from point 1 above\);/gi,
    ""
  );
  clauseContent = clauseContent.replaceAll(
    /If Jenis juruaudit = dalam, display/gi,
    ""
  );
  clauseContent = clauseContent.replaceAll(
    /If Jenis juruaudit = luar, display/gi,
    ""
  );
  clauseContent = clauseContent.replaceAll(
    new RegExp(internalAuditor, "gi"),
    ""
  );
  clauseContent = clauseContent.replaceAll(
    new RegExp(externalAuditor, "gi"),
    "<<jenis juruaudit>>"
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis juruaudit>>/gi,
    jenisJuruAudit === t("internal") ? internalAuditor : externalAuditor
  );
  //clauseContent = clauseContent.replaceAll(/<<jumlah wang tangan yang dibenarkan dalam tangan>>/gi, `<b>${jenisJuruAudit || '<<jumlah wang tangan yang dibenarkan dalam tangan>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<bilangan juruaudit dalam>>/gi,
    `<b>${bilanganInternal || "<<bilangan juruaudit dalam>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<perbelanjaan yg dibenarkan>>/gi, `<b>${bilanganEksternal || '<<perbelanjaan yg dibenarkan>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${pemilihanAjk || "<<jenis mesyuarat agung>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${tempohPelucutanWaktu || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<tempoh pelantikan jawatankuasa>>/gi,
    `<b>${tempohJawatan || "<<tempoh pelantikan jawatankuasa>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("auditorType")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("auditorType")}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth>
              <Select
                size="small"
                displayEmpty
                value={jenisJuruAudit}
                onChange={(e) => {
                  setJenisJuruAudit(e.target.value);
                  /*if(e.target.value === t("internal")) {
                    clauseContent = clauseContent.replaceAll(/<<jenis juruaudit>>/gi, "1. <<bilangan juruaudit dalam>> orang yang bukannya Ahli Jawatankuasa Pertubuhan hendaklah dilantik dalam Mesyuarat Agung <<jenis mesyuarat agung>> sebagai Juruaudit dalam. Mereka yang memegang jawatan selama <<tempoh pelantikan jawatankuasa>> boleh dilantik semula.");
                  }else if(e.target.value === t("external")) {
                    clauseContent = clauseContent.replaceAll(/<<jenis juruaudit>>/gi,"1. Juruaudit luar bertauliah boleh dilantik dalam Mesyuarat Agung/ Mesyuarat Jawatankuasa bagi tempoh <<tempoh pelantikan jawatankuasa>> tahun.");
                  }*/
                }}
              >
                <MenuItem value={t("internal")}>{t("internal")}</MenuItem>
                <MenuItem value={t("external")}>{t("external")}</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {jenisJuruAudit == t("internal") && (
            <>
              <Grid item xs={12} md={4}>
                <Typography sx={labelStyle}>
                  {t("internalAuditorNumber")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </Typography>
              </Grid>
              <Grid item xs={12} md={8}>
                <TextField
                  size="small"
                  fullWidth
                  required
                  value={bilanganInternal}
                  onChange={(e) => {
                    if (RegExNumbers.test(e.target.value)) {
                      setBilanganInternal(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        bilanganInternal: "",
                      }));
                    } else {
                      setBilanganInternal("");
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        bilanganInternal: "Invalid Value",
                      }));
                    }
                  }}
                  error={!!formErrors.bilanganInternal}
                  helperText={formErrors.bilanganInternal}
                  type="number"
                  InputProps={{
                    inputProps: {
                      inputMode: "numeric",
                      pattern: "[0-9]*",
                      min: 0,
                    },
                  }}
                />
              </Grid>
            </>
          )}

          {jenisJuruAudit == t("external") && (
            <>
              <Grid item xs={12} md={4}>
                <Typography sx={labelStyle}>
                  {t("externalAuditorNumber")}{" "}
                  <Typography sx={{ display: "inline", color: "red" }}>
                    *
                  </Typography>
                </Typography>
              </Grid>
              <Grid item xs={12} md={8}>
                <TextField
                  fullWidth
                  required
                  value={bilanganEksternal}
                  onChange={(e) => {
                    if (RegExNumbers.test(e.target.value)) {
                      setBilanganEksternal(e.target.value);
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        bilanganEksternal: "",
                      }));
                    } else {
                      setBilanganEksternal("");
                      setFormErrors((prevErrors) => ({
                        ...prevErrors,
                        bilanganEksternal: "Invalid Value",
                      }));
                    }
                  }}
                  error={!!formErrors.bilanganEksternal}
                  helperText={formErrors.bilanganEksternal}
                  type="number"
                  InputProps={{
                    inputProps: {
                      inputMode: "numeric",
                      pattern: "[0-9]*",
                      min: 0,
                    },
                  }}
                />
              </Grid>
            </>
          )}
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("jenisMesyuaratAgungDanTempohLantikan")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("jenisMesyuaratAgung")}{" "}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required error={!!formErrors.pemilihanAjk}>
              <Select
                size="small"
                value={pemilihanAjk}
                displayEmpty
                onChange={(e) => {
                  setPemilihanAjk(e.target.value as string);
                  setFormErrors((prevErrors) => ({
                    ...prevErrors,
                    pemilihanAjk: "",
                  }));
                }}
              >
                <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                <MenuItem value={t("biennial")}>{t("biennial")}</MenuItem>
              </Select>
            </FormControl>
            {formErrors.pemilihanAjk && (
              <FormHelperText>{formErrors.pemilihanAjk}</FormHelperText>
            )}
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("electionPeriod")}
              <Typography sx={{ display: "inline", color: "red" }}>
                *
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <FormControl fullWidth required>
              <Select
                size="small"
                value={tempohJawatan}
                displayEmpty
                onChange={(e) => setTempohJawatan(e.target.value as string)}
              >
                <MenuItem value={t("setahun")}>{t("setahun")}</MenuItem>
                <MenuItem value={t("duaTahun")}>{t("duaTahun")}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {id}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          px: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Grid item xs={12}>
          <FormControlLabel
            sx={{
              color: "#666666",
              "&.MuiFormControlLabel-label": {
                fontWeight: "400 !important",
              },
            }}
            control={
              <Checkbox checked={checked} onChange={handleChangeCheckbox} />
            }
            label={`${t("checkBox")}`}
          />
          <span style={{ color: "red" }}>*</span>
        </Grid>
      </Box>

      <Grid
        item
        xs={12}
        sx={{
          mt: 2,
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          justifyContent: "flex-end",
          gap: 1,
        }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        <ButtonPrimary
          variant="contained"
          sx={{ width: isMobile ? "100%" : "auto" }}
          onClick={() => {
            const errors = validateForm();
            if (Object.keys(errors).length > 0) {
              console.log(errors);
              setFormErrors(errors);
              return;
            }

            handleSaveContent({
              i18n,
              societyId,
              societyName: namaPertubuhan,
              dataId,
              isEdit,
              clauseNo: clauseNo,
              clauseName: clauseName,
              createClauseContent,
              editClauseContent,
              description: clauseContent,
              constitutionValues: [
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: jenisJuruAudit,
                  titleName: "Jenis Juruaudit",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bilanganInternal,
                  titleName: "Bilangan Juruaudit Dalam",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: bilanganEksternal,
                  titleName: "Bilangan Juruaudit Luar",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: pemilihanAjk,
                  titleName: "Jenis Mesyuarat Agung",
                },
                {
                  constitutionContentId: null,
                  societyName: namaPertubuhan,
                  definitionName: tempohJawatan,
                  titleName: "Tempoh Pelantikan Jawatankuasa",
                },
              ],
              clause: "clause10",
              clauseCount: 10,
              clauseContentId,
            });
          }}
          disabled={isCreatingContent || isEditingContent || !checked}
        >
          {isCreatingContent || isEditingContent
            ? t("saving")
            : clause.constitutionValues.length > 0
            ? t("update")
            : t("save")}
        </ButtonPrimary>
      </Grid>
    </>
  );
};

export default FasalContentSepuluhCawangan;
