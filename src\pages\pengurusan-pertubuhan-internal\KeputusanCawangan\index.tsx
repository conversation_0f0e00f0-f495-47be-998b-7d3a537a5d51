import { useEffect, useMemo } from "react";
import { NavLink, Outlet } from "react-router-dom";
import { Box } from "@mui/material";
import WrapContent from "../View/WrapContent";
import Menu from "./Menu";
import useQuery from "../../../helpers/hooks/useQuery";
import { NEW_PermissionNames, pageAccessEnum } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";

const KeputusanCawangan = () => {
  const { data: roDecisionPendingBranchCount, refetch: fetchSociety } =
    useQuery({
      url: `society/roDecision/getAllPendingCount/branch`,
      autoFetch: false,
    });

  const pendingBranchCount = roDecisionPendingBranchCount?.data?.data;

  useEffect(() => {
    fetchSociety();
  }, []);

  const tab = useMemo(
    () => [
      {
        name: "Pendaftaran Cawangan",
        slug: "pendaftaran",
        number: roDecisionPendingBranchCount?.data?.data
          ? Number(
              roDecisionPendingBranchCount.data.data
                .branchRegistrationPendingCount
            )
          : 0,
        permissionNames:
          NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
            .KEPUTUSAN_CAWANGAN.children.PENDAFTARAN_CAWANGAN.label,
      },
      {
        name: "Lanjut Masa",
        slug: "lanjut-masa",
        number: roDecisionPendingBranchCount?.data?.data
          ? Number(
              roDecisionPendingBranchCount.data.data
                .branchExtensionTimePendingCount
            )
          : 0,
        permissionNames:
          NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
            .KEPUTUSAN_CAWANGAN.children.LANJUT_MASA.label,
      },
      {
        name: "Pindaan Nama dan Alamat",
        slug: "pindaan-nama-dan-alamat",
        number: roDecisionPendingBranchCount?.data?.data
          ? Number(
              roDecisionPendingBranchCount.data.data.branchAmendmentPendingCount
            )
          : 0,
        permissionNames:
          NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
            .KEPUTUSAN_CAWANGAN.children.PINDAAN_NAMA_DAN_ALAMAT.label,
      },
      {
        name: "Pembubaran",
        slug: "pembubaran",
        number: roDecisionPendingBranchCount?.data?.data
          ? Number(
              roDecisionPendingBranchCount.data.data
                .branchLiquidationPendingCount
            )
          : 0,
        permissionNames:
          NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
            .KEPUTUSAN_CAWANGAN.children.PEMBUBARAN_CAWANGAN.label,
      },
      {
        name: "Permohonan bukan Warganegara",
        slug: "permohonan-bukan-warganegara",
        number: roDecisionPendingBranchCount?.data?.data
          ? Number(
              roDecisionPendingBranchCount.data.data
                .branchNonCitizenPendingCount
            )
          : 0,
        permissionNames:
          NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
            .KEPUTUSAN_CAWANGAN.children.PERMOHONAN_BUKAN_WARGANEGARA_CAWANGAN
            .label,
      },
      {
        name: "Pegawai Awam",
        slug: "pegawai-awam",
        number: roDecisionPendingBranchCount?.data?.data
          ? Number(
              roDecisionPendingBranchCount.data.data
                .branchPublicOfficerPendingCount
            )
          : 0,
        permissionNames:
          NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
            .KEPUTUSAN_CAWANGAN.children.PEGAWAI_AWAM_CAWANGAN.label,
      },
      {
        name: "Pegawai Harta",
        slug: "pegawai-harta",
        number: roDecisionPendingBranchCount?.data?.data
          ? Number(
              roDecisionPendingBranchCount.data.data
                .branchPropertyOfficerPendingCount
            )
          : 0,
        permissionNames:
          NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
            .KEPUTUSAN_CAWANGAN.children.PEGAWAI_HARTA_CAWANGAN.label,
      },
    ],
    [roDecisionPendingBranchCount]
  );

  const enrichedTabs = tab.map((item) => ({
    ...item,
    hasPermission: AuthHelper.hasPageAccess(
      item.permissionNames,
      pageAccessEnum.Read
    ),
  }));

  return (
    <Box>
      <WrapContent title="Keputusan Cawangan">
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(180px, 1fr))",
            gap: "1rem",
          }}
        >
          {enrichedTabs.map((data, index) => {
            return (
              <NavLink
                key={index}
                to={`/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan${
                  data.slug === "pendaftaran" ? "" : `/${data.slug}`
                }`}
                end={index === 0}
                style={{
                  textDecoration: "none",
                  pointerEvents: data.hasPermission ? "auto" : "none",
                }}
              >
                {({ isActive }) => (
                  <Menu
                    disabled={!data.hasPermission}
                    data={data}
                    isActive={!data.hasPermission ? false : isActive}
                    onClick={() => {}}
                  />
                )}
              </NavLink>
            );
          })}
        </Box>
      </WrapContent>

      <Outlet context={{ pendingBranchCount }} />
    </Box>
  );
};

export default KeputusanCawangan;
