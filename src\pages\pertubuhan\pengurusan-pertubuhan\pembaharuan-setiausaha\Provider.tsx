/* eslint-disable react-refresh/only-export-components */
import {
  useState,
  useEffect,
  useContext,
  createContext,
  PropsWithChildren,
} from "react";
import { useNotification } from "@refinedev/core";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import { useGetIdentity } from "@refinedev/core";
import { FieldValues, FormProvider, useForm } from "react-hook-form";
import { omitKeysFromObject, useQuery } from "@/helpers";

import {
  ISocietyDetail,
  IMeetingDetail,
  IUser,
  IApiResponse,
  ISecretaryDetail,
} from "@/types";
import { useTranslation } from "react-i18next";

interface SecretaryReformContextProps {
  secretaryDetailData?: ISecretaryDetail | null;
  formStep: number;
  societyData: ISocietyDetail | null;
  meetingData: IMeetingDetail | null;
  handleNext: (secretaryId?: string | number) => void;
  handleBack: VoidFunction;
  handleNextStep: VoidFunction;
  handleSetFormStep: (step: number) => void;
  handleSetSocietyData: (data: ISocietyDetail | null) => void;
  handleSecretaryReformStatus: (status: boolean) => void;
  isSecretaryReformSuccess: boolean;
  isLoadingSecretary: boolean;
  isLoadingSociety: boolean;
  isEditable: boolean;
  isViewOnly: boolean;
  isFeedback: boolean;
}

const SecretaryReformContext = createContext<
  SecretaryReformContextProps | undefined
>(undefined);

export const useSecretaryReformContext = (): SecretaryReformContextProps => {
  const context = useContext(SecretaryReformContext);

  if (!context) {
    throw new Error(
      "useSecretaryReformContext must be used within a SecretaryReformContextProvider"
    );
  }
  return context;
};

const SecretaryReformProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const navigate = useNavigate();
  const { open } = useNotification();
  const location = useLocation();
  const { societyId: societyIdParams, secretaryId } = useParams();
  const { data: user } = useGetIdentity<IUser>();
  const { t } = useTranslation();
  const [meetingId, setMeetingId] = useState<string | number | null>(null);
  const paths = location.pathname.split("/").filter(Boolean);
  const lastPath = paths[paths.length - 1];

  const [formStep, setFormStep] = useState(1);
  const [isSecretaryReformSuccess, setIsSecretaryReformSuccess] =
    useState(false);
  const [societyData, setSocietyData] = useState<ISocietyDetail | null>(null);

  const methods = useForm<FieldValues>({
    defaultValues: {
      id: "",
      societyId: "",
      societyNo: "",
      titleCode: "",
      committeeName: "",
      sex: "",
      committeePosition: 3,
      jobCode: "",
      citizenshipStatus: "1",
      dateOfBirth: "",
      placeOfBirth: "",
      identificationType: 1,
      identificationNo: "",
      email: "",
      residenceAddress: "",
      residencePostcode: "",
      residenceAddressStatus: "",
      residenceCountryCode: "",
      residenceStateCode: "",
      residenceDistrictCode: "",
      residenceCityCode: "",
      residenceCity: "",
      homeTelNoCode: "",
      homeTelNo: "",
      hpNoCode: "",
      hpNo: "",
      workTelNoCode: "",
      workTelNo: "",
      employerName: "",
      employerAddress: "",
      employerAddressStatus: "",
      employerPostcode: "",
      employerCountryCode: "",
      employerStateCode: "",
      employerDistrictCode: "",
      employerCityCode: "",
      employerCity: "",
      oldSecretaryName: "",
      oldSecretaryIdentificationNumber: "",
      meetingDate: "",
      meetingType: "",
      reasonOfChange: "",
    },
  });
  const { setValue, watch } = methods;

  const societyId = watch("societyId");

  const {
    data: secretaryResponse,
    refetch: fetchSecretary,
    isLoading: isLoadingSecretary,
  } = useQuery<IApiResponse<ISecretaryDetail>>({
    url: `society/secretary/principal/${secretaryId}`,
    autoFetch: false,
    onSuccess: (res) => {
      const data = res?.data?.data;
      if (!data) return;

      const keysToSkip = ["workTel", "hpNo", "homeTel"];
      const filteredData = omitKeysFromObject(data, keysToSkip);

      Object.entries(filteredData).forEach(([key, value]) => {
        setValue(key as keyof FieldValues, value);
      });

      ["workTel", "hpNo", "homeTel"].forEach((key) => {
        const value = data[key as keyof ISecretaryDetail];
        const [code = "", number = ""] =
          typeof value === "string" ? value.split(" ") : ["", ""];
        setValue(`${key}Code`, code);
        setValue(key, number);
      });
      if (data?.employerCountryCode) {
        setValue("employerCountryCode", Number(data?.employerCountryCode));
      }
      if (data?.meetingId) {
        setMeetingId(data?.meetingId);
        fetchMeeting();
      }
      fetchSociety();
    },
  });

  const { refetch: fetchSociety, isLoading: isLoadingSociety } = useQuery<
    IApiResponse<ISocietyDetail>
  >({
    url: `society/${societyIdParams ?? societyId}/basic`,
    autoFetch: false,
    onSuccess: (res) => {
      const societyData = res?.data?.data;

      if (societyData) {
        setSocietyData(societyData);
      } else {
        open?.({
          type: "error",
          message: t("error"),
          description: "Society not found.",
        });
        navigate("/pertubuhan/pembaharuan-setiausaha");
      }
    },
  });

  const secretaryDetailData = secretaryResponse?.data?.data ?? null;
  const statusCode = secretaryDetailData?.applicationStatusCode ?? null;

  const { data: meetingRes, refetch: fetchMeeting } = useQuery<
    IApiResponse<IMeetingDetail>
  >({
    url: `society/meeting/${meetingId}`,
    autoFetch: !!meetingId,
  });

  const VIEW_ONLY_STATUSES = [2, 3, 4];
  const EDITABLE_STATUSES = [1, 36];

  const meetingData = meetingRes?.data?.data || null;
  const isViewOnly =
    statusCode !== null && VIEW_ONLY_STATUSES.includes(statusCode);
  const isEditable =
    statusCode !== null && EDITABLE_STATUSES.includes(statusCode);
  const isFeedback = lastPath === "feedback";

  const handleNextStep = () => setFormStep((prev) => prev + 1);

  const handleNext = (secretaryId?: string | number) => {
    setFormStep((prev) => prev + 1);

    if (secretaryId) {
      navigate(
        `/pertubuhan/pembaharuan-setiausaha/tambah/${societyId}/${secretaryId}`
      );
    }
  };
  const handleBack = () => setFormStep(formStep - 1);
  const handleSecretaryReformStatus = (status: boolean) =>
    setIsSecretaryReformSuccess(status);
  const handleSetFormStep = (step: number) => setFormStep(step);
  const handleSetSocietyData = (data: ISocietyDetail | null) =>
    setSocietyData(data);

  useEffect(() => {
    if (secretaryId) fetchSecretary();
  }, [secretaryId]);

  useEffect(() => {
    if (!societyData) return;

    setValue("societyId", societyData?.id);
    setValue("societyNo", societyData?.societyNo);
  }, [societyData]);

  useEffect(() => {
    if (!user) return;

    setValue("identificationType", user?.identificationType ?? "");
    setValue("identificationNo", user?.identificationNo ?? "");
  }, [user]);

  useEffect(() => {
    if (!societyData && societyIdParams) fetchSociety();
  }, [societyIdParams]);

  return (
    <SecretaryReformContext.Provider
      value={{
        secretaryDetailData,
        formStep,
        societyData,
        meetingData,
        isEditable,
        isViewOnly,
        isFeedback,
        handleNext,
        handleBack,
        handleNextStep,
        handleSetFormStep,
        handleSetSocietyData,
        handleSecretaryReformStatus,
        isSecretaryReformSuccess,
        isLoadingSecretary,
        isLoadingSociety,
      }}
    >
      <FormProvider {...methods}>{children}</FormProvider>
    </SecretaryReformContext.Provider>
  );
};

export default SecretaryReformProvider;
