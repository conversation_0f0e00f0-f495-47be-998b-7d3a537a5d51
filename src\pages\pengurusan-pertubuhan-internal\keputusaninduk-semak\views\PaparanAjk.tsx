import { <PERSON>, Grid, TextField, Typography } from "@mui/material";
import { t } from "i18next";
import { useEffect, useState } from "react";
import { ButtonPrimary } from "../../../../components/button";
import { useNavigate, useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { AJKpaparan } from "@/redux/ajkReducer";
import {
  DocumentUploadType,
  getLocalStorage,
  ListGelaran,
  OrganisationPositions,
  otherPositionSwitchList,
  IdTypes,
  ListGender,
  CitizenshipStatus,
  formatDate,
  getAddressList,
  capitalizeWords,
  MALAYSIA,
  addressType,
} from "@/helpers";
import FileUploader from "@/components/input/fileUpload";
import Input from "@/components/input/Input";

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

function PaparanAjk() {
  const ajkPaparan = useSelector(AJKpaparan);

  const { id } = useParams();

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);
  const addressData = useSelector(getAddressList);

  const navigate = useNavigate();
  const occupationList = getLocalStorage("occupation_list", []);

  const [formData, setFormData] = useState({
    position: "",
    identificationType: "",
    identificationNo: "",
    titleCode: "",
    name: "",
    gender: "",
    nationalityStatus: "",
    dateOfBirth: "",
    placeOfBirth: "",
    jobCode: "",
    residentialAddress: "",
    residentialStateCode: "",
    residentialDistrictCode: "",
    residentialCity: "",
    residentialPostcode: "",
    email: "",
    phoneNumber: "",
    telephoneNumber: "",
    employerName: "",
    employerAddress: "",
    employerAddressStatus: undefined,
    employerStateCode: "",
    employerCountryCode: "",
    employerDistrict: "",
    employerCity: "",
    employerPostcode: "",
    designationCode: null,
    otherDesignationCode: null,
    otherPosition: null,
  });

  useEffect(() => {
    if (ajkPaparan) {
      setFormData({
        ...formData,
        position: ajkPaparan?.designationCode,
        identificationType: ajkPaparan?.identificationType,
        identificationNo: ajkPaparan?.identificationNo,
        titleCode: ajkPaparan?.titleCode ? ajkPaparan?.titleCode : "-",
        name: ajkPaparan?.name,
        gender: ajkPaparan?.gender,
        nationalityStatus: ajkPaparan?.nationalityStatus
          ? ajkPaparan.nationalityStatus
          : "-",
        dateOfBirth: ajkPaparan?.dateOfBirth,
        placeOfBirth: ajkPaparan?.placeOfBirth,
        jobCode: ajkPaparan?.jobCode,
        residentialAddress: ajkPaparan?.residentialAddress,
        residentialStateCode: ajkPaparan?.residentialStateCode
          ? ajkPaparan.residentialStateCode
          : "-",
        residentialDistrictCode: ajkPaparan?.residentialDistrictCode
          ? ajkPaparan?.residentialDistrictCode
          : "-",
        residentialCity: ajkPaparan?.residentialCity,
        residentialPostcode: ajkPaparan?.residentialPostcode,
        email: ajkPaparan?.email,
        phoneNumber: ajkPaparan?.phoneNumber,
        telephoneNumber: ajkPaparan?.telephoneNumber,
        employerName: ajkPaparan?.employerName,
        employerAddress: ajkPaparan?.employerAddress,
        employerCountryCode: ajkPaparan?.employerCountryCode
          ? ajkPaparan.employerCountryCode
          : "-",
        employerDistrict: ajkPaparan?.employerDistrict
          ? ajkPaparan?.employerDistrict
          : "-",
        employerCity: ajkPaparan?.employerCity,
        employerPostcode: ajkPaparan?.employerPostcode,
        designationCode: ajkPaparan?.designationCode,
        otherDesignationCode: ajkPaparan?.otherDesignationCode,
        otherPosition: ajkPaparan?.otherPosition,
        employerAddressStatus: ajkPaparan?.employerAddressStatus,
        employerStateCode: ajkPaparan?.employerStateCode,
      });
    }
  }, [ajkPaparan]);

  const goback = () => {
    navigate(-1);
  };

  return (
    <Box>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",
            px: 2,
            py: 4,
          }}
        >
          {societyDataRedux?.societyName}
        </Box>
      </Box>

      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        {/*  */}
        <Box
          sx={{
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("chairman")}
          </Typography>

          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography className="label">
                  {t("jawatan")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size="small"
                  disabled
                  fullWidth
                  required
                  value={
                    !otherPositionSwitchList.some(
                      (item) => item.value === Number(formData.designationCode)
                    )
                      ? t(
                          OrganisationPositions.find(
                            (item) =>
                              item.value === Number(formData.designationCode)
                          )?.label || "-"
                        )
                      : formData?.otherDesignationCode
                      ? t(
                          formData?.otherDesignationCode ??
                            t(
                              OrganisationPositions.find(
                                (item) =>
                                  item.value ===
                                  Number(formData.designationCode)
                              )?.label || "-"
                            )
                        )
                      : formData?.otherPosition
                  }
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      position: e.target.value,
                    })
                  }
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/*  */}
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
          }}
        >
          <Typography variant="h6" component="h2" sx={subTitleStyle}>
            {t("chairmanPersonalInfo")}
          </Typography>

          <Box>
            <Input
              label={t("idType")}
              value={formData.identificationType}
              options={IdTypes.map((item) => ({
                value: item.value,
                label: t(item.label),
              }))}
              type="select"
              disabled
            />
            <Input
              label={t("idNumber")}
              value={formData.identificationNo ?? "-"}
              disabled
            />
            <Input
              label={t("title")}
              value={formData.titleCode?.toString()}
              options={ListGelaran.map((item) => ({
                value: item.value,
                label: t(item.label),
              }))}
              type="select"
              disabled
            />
            <Input
              label={t("fullName")}
              value={formData.name ?? "-"}
              disabled
            />
            <Input
              label={t("gender")}
              value={formData.gender}
              options={ListGender.map((item) => ({
                value: item.value,
                label: t(item.label),
              }))}
              type="select"
              disabled
            />
            <Input
              label={t("citizenship")}
              value={Number(formData.nationalityStatus)}
              options={CitizenshipStatus.map((item) => ({
                ...item,
                label: t(item.label),
              }))}
              type="select"
              disabled
            />
            <Input
              label={t("dateOfBirth")}
              value={formatDate(formData.dateOfBirth) ?? "-"}
              type="date"
              disabled
            />
            <Input
              label={t("placeOfBirth")}
              value={formData.placeOfBirth ?? "-"}
              disabled
            />
            <Input
              label={t("occupation")}
              value={formData.jobCode}
              options={occupationList}
              type="select"
              disabled
            />
            <Input
              label={t("residentialAddress")}
              value={formData.residentialAddress ?? "-"}
              disabled
            />
            <Input
              label={t("state")}
              value={Number(formData.residentialStateCode)}
              options={addressData.map((item: any) => ({
                value: item.id,
                label: t(item.name),
              }))}
              type="select"
              disabled
            />
            <Input
              label={t("district")}
              value={Number(formData.residentialDistrictCode)}
              options={addressData.map((item: any) => ({
                value: item.id,
                label: t(item.name),
              }))}
              type="select"
              disabled
            />
            <Input
              label={t("city")}
              value={formData.residentialCity}
              disabled
            />
            <Input
              label={t("postcode")}
              value={formData.residentialPostcode ?? "-"}
              disabled
            />
            <Input label={t("email")} value={formData.email ?? "-"} disabled />
            <Input
              label={t("phoneNumber")}
              value={
                formData.phoneNumber && formData.phoneNumber.trim() !== ""
                  ? formData.phoneNumber
                  : "-"
              }
              disabled
            />
            <Input
              label={t("nomborTelefonPejabat")}
              value={
                formData.telephoneNumber &&
                formData.telephoneNumber.trim() !== ""
                  ? formData.telephoneNumber
                  : "-"
              }
              disabled
            />
          </Box>
        </Box>
        {/*  */}
        <Box mt={3}>
          {formData?.jobCode !== "Tidak Bekerja" &&
          formData?.jobCode !== "Pesara" ? (
            <Box
              sx={{
                borderRadius: "10px",
                padding: "41px 25px 25px",
                border: "0.5px solid #DADADA",
                marginBottom: "13px",
              }}
            >
              <Typography
                fontSize="14px"
                color="var(--primary-color)"
                fontWeight="500 !important"
                marginBottom="20px"
              >
                {t("employerInfo")}
              </Typography>

              <Input
                label={t("employerName")}
                value={formData.employerName ?? "-"}
                disabled
              />
              <Input
                label={t("employerAddress")}
                value={formData?.employerAddressStatus}
                type="select"
                options={addressType.map((item) => ({
                  ...item,
                  label: t(item.label),
                }))}
                disabled
              />
              <Input
                isLabel={false}
                value={formData.employerAddress}
                rows={3}
                multiline
                disabled
              />
              {formData?.employerAddressStatus === "0" ? (
                <Input
                  label={t("country")}
                  value={Number(formData?.employerCountryCode)}
                  type="select"
                  options={addressData
                    .filter((item: any) => item.pid === 0)
                    .map((item: any) => ({
                      label:
                        item.name.charAt(0) + item.name.slice(1).toLowerCase(),
                      value: item.id,
                    }))}
                  disabled
                />
              ) : null}
              {formData?.employerAddressStatus === "1" ? (
                <>
                  <Input
                    label={t("state")}
                    value={formData.employerStateCode ?? "-"}
                    options={
                      addressData
                        ?.filter((item: any) => item.pid == MALAYSIA)
                        .map((item: any) => ({
                          label: capitalizeWords(item.name, null, true),
                          value: `${item.id}`,
                        })) ?? []
                    }
                    type="select"
                    disabled
                  />
                  <Input
                    label={t("district")}
                    value={formData.employerDistrict ?? "-"}
                    type="select"
                    options={addressData
                      ?.filter(
                        (item: any) => item.pid == formData?.employerStateCode
                      )
                      .map((item: any) => ({
                        label: capitalizeWords(item.name, null, true),
                        value: `${item.id}`,
                      }))}
                    disabled
                  />
                  <Input
                    label={t("city")}
                    value={formData.employerCity ?? "-"}
                    disabled
                  />
                  <Input
                    label={t("poskod")}
                    value={formData.employerPostcode ?? "-"}
                    disabled
                  />
                </>
              ) : null}
            </Box>
          ) : null}
        </Box>
        {/*  */}

        <Box>
          <Box sx={{ textAlign: "left", mt: 2 }}>
            {ajkPaparan?.id && (
              <FileUploader
                key={ajkPaparan?.id}
                title={t("ajkEligibilityCheck")}
                type={DocumentUploadType.CITIZEN_COMMITTEE}
                societyId={ajkPaparan?.societyId}
                icNo={ajkPaparan?.identificationNo}
                societyCommitteeId={ajkPaparan?.id}
                disabled={true}
                validTypes={[
                  "text/plain",
                  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                  "application/msword",
                  "application/pdf",
                ]}
              />
            )}
          </Box>
        </Box>

        <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
          <ButtonPrimary onClick={goback}>{t("back")}</ButtonPrimary>
        </Box>
      </Box>
    </Box>
  );
}

export default PaparanAjk;
