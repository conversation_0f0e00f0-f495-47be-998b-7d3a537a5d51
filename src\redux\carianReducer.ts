import { createSlice,PayloadAction } from '@reduxjs/toolkit';

interface fasal{
  data:fasalItems

}

interface fasalItems{
  currentSocietyId:string|number|undefined;
  amount:string|number;
  dokumen:any[];
  currentInfoId:number|string|null;
  isPendingPayment:boolean;
  dokumenTemplateForPendingPayment:any[]
  carianPaymentReferenceNo:string;
}

const initialState:fasal = { data:{ currentSocietyId:"",amount:"",dokumen:[],dokumenTemplateForPendingPayment:[],currentInfoId:null,isPendingPayment:false,carianPaymentReferenceNo:""}}

export const carianSlice = createSlice({
  name: 'fasal',
  initialState:initialState,
  reducers: {
    setAmount:(state,action:PayloadAction<string|number>)=>{
        state.data.amount = action.payload
    },
    setCurrentSocietyId:(state,action:PayloadAction<string|number|undefined>)=>{
      state.data.currentSocietyId = action.payload
    },
    setDokumen:(state,action:PayloadAction<any[]>)=>{
      state.data.dokumen =  action.payload;
    },
    setCurrentInfoId:(state,action:PayloadAction<string|number>)=>{
      state.data.currentInfoId = action.payload
    },
    setIsPendingPayment:(state,action:PayloadAction<boolean>)=>{
      state.data.isPendingPayment = action.payload
    },
    setDokumenTemplateForPendingPayment:(state,action:PayloadAction<any[]>)=>{
      state.data.dokumenTemplateForPendingPayment = action.payload
    },
    setCarianPaymentReferenceNo:(state,action:PayloadAction<string>)=>{
      state.data.carianPaymentReferenceNo = action.payload
    }, 
  },
});

export const { setAmount,setCurrentSocietyId,setDokumen ,setCurrentInfoId , setIsPendingPayment , setDokumenTemplateForPendingPayment,setCarianPaymentReferenceNo} = carianSlice.actions;

export default carianSlice.reducer;
