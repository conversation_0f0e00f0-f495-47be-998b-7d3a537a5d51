import { createSlice,PayloadAction } from '@reduxjs/toolkit';

interface fasal{
  data:fasalItems

}

interface fasalItems{
  currentSocietyId:string|number;
  amount:string|number;
  dokumen:any[];
  currentInfoId:number|string|null;
  isPendingPayment:boolean;
}

const initialState:fasal = { data:{ currentSocietyId:"",amount:"",dokumen:[],currentInfoId:null,isPendingPayment:false}}

export const carianSlice = createSlice({
  name: 'fasal',
  initialState:initialState,
  reducers: {
    setAmount:(state,action:PayloadAction<string|number>)=>{
        state.data.amount = action.payload
    },
    setCurrentSocietyId:(state,action:PayloadAction<string|number>)=>{
      state.data.currentSocietyId = action.payload
    },
    setDokumen:(state,action:PayloadAction<any[]>)=>{
      state.data.dokumen =  action.payload;
    },
    setCurrentInfoId:(state,action:PayloadAction<string|number>)=>{
      state.data.currentInfoId = action.payload
    },
    setIsPendingPayment:(state,action:PayloadAction<boolean>)=>{
      state.data.isPendingPayment = action.payload
    },
  },
});

export const { setAmount,setCurrentSocietyId,setDokumen ,setCurrentInfoId , setIsPendingPayment} = carianSlice.actions;

export default carianSlice.reducer;
