import { <PERSON>, Grid, Icon<PERSON>utton, Typography } from "@mui/material";
import { t } from "i18next";
import React, { useState } from "react";
import Input from "../../../components/input/Input";
import { ButtonOutline, ButtonPrimary } from "../../../components/button";
import { useNavigate } from "react-router-dom";
import { EditIcon } from "../../../components/icons";
import useQuery from "../../../helpers/hooks/useQuery";
import { DataTable, IColumn } from "@/components";
import { FieldValues, useForm } from "react-hook-form";
import AuthHelper from "@/helpers/authHelper";
import { NEW_PermissionNames, pageAccessEnum } from "@/helpers";

interface FormValues {
  search: string | null;
}

const PendaftaranTab = () => {
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "30px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const navigate = useNavigate();

  const hasReadPermission = AuthHelper.hasPageAccess(
    NEW_PermissionNames.PERTUBUHAN.children.KEPUTUSAN_PERTUBUHAN.children
      .KEPUTUSAN_CAWANGAN.children.PENDAFTARAN_CAWANGAN.label,
    pageAccessEnum.Read
  );

  const columns: IColumn[] = [
    {
      field: "societyName",
      headerName: t("organizationName"),
      headerAlign: "center",
      align: "center",
      flex: 1,
    },
    {
      field: "name",
      headerName: t("cawangan"),
      headerAlign: "center",
      align: "center",
      flex: 1,
    },
    {
      field: "paymentDate",
      headerName: t("tarikhBayar"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return <Box> {row.paymentDate ? row.paymentDate : "-"}</Box>;
      },
    },
    {
      field: "roName",
      headerName: t("keputusanCawangan_RO"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return <Box> {row.roName ? row.roName : "-"}</Box>;
      },
    },
    {
      field: "transferDate",
      headerName: t("tarikhAlir"),
      headerAlign: "center",
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return <Box> {row.transferDate ? row.transferDate : "-"}</Box>;
      },
    },
    {
      field: "stateName",
      headerName: t("negeri"),
      headerAlign: "center",
      align: "center",
      flex: 1,
    },
    {
      field: "actions",
      headerName: t("action"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      cellClassName: "custom-cell",
      renderCell: ({ row }: any) => {
        return (
          <IconButton
            onClick={() => {
              navigate(
                `/pengurus-pertubuhan/keputusan-pertubuhan/keputusan-cawangan/pendaftaran/${row.id}`
              );
            }}
            disabled={!hasReadPermission}
          >
            <EditIcon
              sx={{
                color: hasReadPermission
                  ? "var(--primary-color)"
                  : "var(--text-grey-disabled)",
                width: "1rem",
                height: "1rem",
              }}
            />
          </IconButton>
        );
      },
    },
  ];

  const { data: roDecisionPendingBranchCount, isLoading } = useQuery({
    url: `society/roDecision/getAllPendingCount/branch`,
    autoFetch: false,
  });
  const [formValues, setFormValues] = useState<FormValues>({
    search: null,
  });

  const [refreshKey, setRefreshKey] = useState(0);
  const [searchQuery, setSearchQuery] = useState<string | null>(null);
  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      searchQuery: undefined,
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");

  const { data, isLoading: isSearchLoading } = useQuery({
    url: `society/roDecision/getAllPending/branch/registration`,
    autoFetch: true,
    filters: [
      { field: "search", value: searchQuery, operator: "eq" },
      { field: "isQuery", operator: "eq", value: 0 },
      {
        field: "pageSize",
        value: pageSize,
        operator: "eq",
      },
      {
        field: "pageNo",
        value: page,
        operator: "eq",
      },
    ],
  });

  const totalList = data?.data?.data?.total ?? 0;
  const rowData = data?.data?.data?.data ?? [];

  const handleChange = (e: any) => {
    const { name, value } = e.target;

    setFormValues({
      ...formValues,
      [name!]: value as string,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const updatedFormValues = Object.fromEntries(
      Object.entries(formValues).map(([key, value]) => [
        key,
        typeof value === "string" && value.trim() === "" ? null : value,
      ])
    );
    setValue("page", 1);
    setValue("pageSize", 10);
    setSearchQuery(updatedFormValues.search);
  };

  const handleClearSearch = () => {
    setFormValues({ search: null });
    setSearchQuery(null);
    setRefreshKey((prev) => prev + 1);
  };

  return (
    <>
      <Box>
        <Box
          sx={{
            padding: "22px 16px",
            background: "#FFF",
            borderRadius: "15px",
            boxShadow: "0px 12px 12px 0px #EAE8E866",
            marginBottom: 1,
          }}
        >
          <Box
            sx={{
              width: "100%",
              border: "0.5px solid #DADADA",
              borderRadius: "10px",
              padding: "22px",
            }}
            component="form"
            onSubmit={handleSubmit}
          >
            <Input
              value={formValues.search ? formValues.search : ""}
              name="search"
              onChange={handleChange}
              label={t("carian")}
            />
            <Grid container mt={3} spacing={2}>
              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonOutline onClick={handleClearSearch}>
                  {t("previous")}
                </ButtonOutline>
                <ButtonPrimary type="submit">{t("search")}</ButtonPrimary>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </Box>

      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}>
        <Box
          sx={{
            textAlign: "center",
            color: "#fff",
            borderRadius: "13px",
            backgroundColor: "var(--primary-color)",
            py: 2,
          }}
        >
          <Typography variant="h5" gutterBottom>
            {roDecisionPendingBranchCount?.data?.data
              ? Number(
                  roDecisionPendingBranchCount.data.data
                    .branchRegistrationPendingCount
                )
              : 0}
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
            {t("keputusanCawangan_penubuhanMenungguPendaftaranCawangan")}
          </Typography>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mt: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("keputusanCawangan_seneraiPenubuhanMenungguPendaftaranCawangan")}
          </Typography>

          <DataTable
            key={refreshKey}
            columns={columns}
            rows={rowData}
            page={page}
            rowsPerPage={pageSize}
            totalCount={totalList}
            isLoading={isLoading || isSearchLoading}
            onPageChange={(newPage) => setValue("page", newPage)}
            onPageSizeChange={(newPageSize) =>
              setValue("pageSize", newPageSize)
            }
          />
        </Box>
      </Box>
    </>
  );
};

export default PendaftaranTab;
