import { OrganisationPositionLabel } from "@/helpers";

export type Bank = {
  id: string | number;
  societyNo: string;
  branchNo: string;
  statementId: string | number;
  bankName: string;
  accountNo: string;
  applicationStatusCode: number;
  createdBy: string | number;
  createdDate: [];
  modifiedBy: string | number;
  modifiedDate: [];
};

export type BankType = {
  bankCode: string;
  bankName: string;
  id: string | number;
  status: number;
};

export type Meeting = {
  createdBy: string | number;
  createdDate: string;
  modifiedBy: string | number;
  modifiedDate: string;
  id: string | number;
  societyId: string | number;
  societyNo: string;
  branchId: string | number;
  branchNo: string;
  statementId: string | number;
  meetingType: string;
  meetingPurpose: string;
  meetingPlace: string;
  meetingMethod: "8" | "9" | "10";
  platformType: string;
  meetingDate: string;
  meetingTime: string;
  meetingTimeTo: string;
  meetingAddress: string;
  state: string;
  district: string;
  city: string;
  postcode: string;
  totalAttendees: number;
  openingRemarks: string;
  meetingContent: string;
  mattersDiscussed: string;
  otherMatters: string;
  closing: string;
  providedBy: string;
  confirmBy: string;
  meetingMinute: string;
  status: string;
  meetingMemberAttendances: MeetingAttendee[];
  GISInformation: string;
};

export type AddressList = {
  code: string;
  id: string | number;
  lovel: number;
  name: string;
  pid: string | number;
  shortCode: string;
  status: number;
};

export type MeetingAttendee = {
  createdBy: string | number;
  createdDate: string;
  modifiedBy: string | number;
  modifiedDate: string;
  id: string | number;
  meetingId: string | number;
  societyId: string | number;
  societyNo: string;
  branchId: string | number;
  branchNo: string;
  meetingDate: [];
  icNo: number;
  present: number;
  name: string;
  position: string;
  status: number;
};

export type Society = {
  categoryCodeJppm: string;
  subCategoryCode: string;
  committeeTaskEnabled: boolean;
  societyNo: number;
  societyName: string;
};

export type Trustee = {
  id: string | number;
  societyId: string | number;
  societyNo: string;
  branchId: string | number;
  titleCode: string;
  name: string;
  gender: string;
  citizenshipStatus: string;
  identificationType: string;
  identificationNo: string;
  dateOfBirth: number[];
  placeOfBirth: string;
  occupationCode: string;
  address: string;
  countryCode: string;
  stateCode: string;
  districtCode: string;
  subDistrictCode: string;
  city: string;
  postalCode: string;
  email: string;
  officePhoneNumber: string;
  homePhoneNumber: string;
  mobilePhoneNumber: string;
  appointmentDate: number[];
  status: number;
  branchTrustee: string;
};

export type PublicOfficer = {
  id: string | number;
  societyId: string | number;
  societyNo: string;
  branchId: string | number;
  titleCode: string;
  name: string;
  gender: string;
  citizenshipStatus: string;
  identificationType: string;
  identificationNo: string;
  dateOfBirth: number[];
  placeOfBirth: string;
  occupationCode: string;
  address: string;
  countryCode: string;
  stateCode: string;
  districtCode: string;
  subDistrictCode: string;
  city: string;
  postalCode: string;
  email: string;
  officePhoneNumber: string;
  homePhoneNumber: string;
  mobilePhoneNumber: string;
  appointmentDate: number[];
  status: number;
  applicationStatusCode: string;
  branchTrustee: string;
};

export type PropertyOfficer = {
  id: string | number;
  societyId: string | number;
  societyNo: string;
  branchId: string | number;
  titleCode: string;
  name: string;
  gender: string;
  citizenshipStatus: string;
  identificationType: string;
  identificationNo: string;
  dateOfBirth: number[];
  placeOfBirth: string;
  occupationCode: string;
  address: string;
  countryCode: string;
  stateCode: string;
  districtCode: string;
  subDistrictCode: string;
  city: string;
  postalCode: string;
  email: string;
  officePhoneNumber: string;
  homePhoneNumber: string;
  mobilePhoneNumber: string;
  appointmentDate: number[];
  status: number;
  applicationStatusCode: string;
  branchTrustee: string;
  societyCommitteeId: string | number;
  propertyOfficerApplicationId: string | number;
};

export type Ajk = {
  id: string | number;
  jobCode: string;
  societyId: string | number;
  societyNo: string;
  titleCode: string;
  committeeName: string;
  committeeStateCode: string;
  committeeEmployerStateCode?: string;
  name: string;
  gender: string;
  nationalityStatus: string;
  identificationType: string;
  identificationNo: string;
  dateOfBirth: number[];
  placeOfBirth: string;
  designationCode: keyof typeof OrganisationPositionLabel;
  otherDesignationCode: string;
  employerAddressStatus: string;
  employerName: string;
  employerAddress: string;
  employerPostcode: string;
  employerCountryCode: string;
  employerStateCode: string;
  employerCity: string;
  employerDistrict: string;
  residentialAddress: string;
  residentialPostcode: string;
  residentialAddressStatus: string;
  residentialCountryCode: string;
  residentialStateCode: string;
  residentialDistrictCode: string;
  residentialCity: string;
  email: string;
  telephoneNumber: string;
  phoneNumber: string;
  noTelP: number;
  status: string;
  applicationStatusCode: string;
  pegHarta: string;
  tarikhTukarSu: [];
  otherPosition: string;
  batalFlat: boolean;
  blacklistNotice: boolean;
  benarAjk: boolean;
  createdBy: string;
  createdDate: [];
  modifiedBy: string;
  modifiedDate: [];
};

export type AjkNonCiizen = {
  id: string | number;
  name: string;
  committeeEmployerStateCode: string;
  citizenshipStatus: string;
  identificationType: string;
  identificationNo: string;
  applicantCountryCode: string;
  gender: string;
  visaExpirationDate: any; // ISO 8601 format
  permitExpirationDate: any; // ISO 8601 format
  visaNo: string;
  permitNo: string;
  designationCode: keyof typeof OrganisationPositionLabel;
  stayDurationDigit: number;
  stayDurationUnit: string;
  durationType: string;
  visaPermitNo: string;
  tujuanDMalaysia: string;
  tempohDMalaysia: string;
  summary: string;
  societyName: string;
  societyId: string | number;
  societyNo: string;
  branchId: string | number;
  branchNo: string;
  applicationStatusCode: string;
  status: number;
  ro: string; // Regional Office
  pembaharuanSu: string; // Renewal Required
  pemCaw: string; // Yes or No
  otherDesignationCode: string;
  transferDate: string; // ISO 8601 format
  noteRo: string; // Note for Regional Office
};

export type Auditor = {
  id: string | number;
  societyId: string | number;
  societyNo: string;
  branchId: string | number;
  branchNo: string;
  statementId: string | number;
  auditorType: "L" | "D";
  titleCode: string;
  name: string;
  licenseNo: string;
  companyName: string;
  gender: string;
  nationalityStatus: string;
  identificationType: string;
  identificationNo: string;
  dateOfBirth: number[];
  placeOfBirth: string;
  employmentCode: string;
  address: string;
  countryCode: string;
  stateCode: string;
  districtCode: string;
  smallDistrictCode: string;
  city: string;
  postcode: string;
  email: string;
  telephoneNo: string;
  phoneNo: string;
  appointmentDate: number[];
  createdBy: string | number;
  createdDate: string[];
  modifiedBy: string | number;
  modifiedDate: number[];
  status: string;
  deleteStatus: string;
  pemCaw: string;
};

export interface Sumbangan {
  id: string | number;
  statementId: string | number;
  societyId: string | number;
  societyNo: string;
  branchId: string | number;
  branchNo: string;
  contributionCode: string;
  contribution: string;
  countryOrigin: string;
  value: number;
  applicationStatusCode: string;
  createdBy: string;
  createdDate: number[];
  modifiedBy: string;
  modifiedDate: number[];
}

export interface AliranTugas {
  id: string | number;
  societyId: string | number;
  societyNo: string;
  branchId: string | number;
  branchNo: string;
  societyCommitteeId: string | number;
  identificationNo: string;
  designationCode: keyof typeof OrganisationPositionLabel;
  statementId: string | number;
  name: string;
  status: string;
  taskActivateDate: [] | null;
  taskDeactivateDate: [] | null;
  createdBy: string | number;
  createdDate: [];
  modifiedBy: string | number;
  modifiedDate: [];
}

export interface Document {
  createdBy: string | number;
  createdDate: string;
  modifiedBy: string | number;
  modifiedDate: string;
  id: string | number;
  type: number;
  societyId: string | number;
  societyNo: string;
  branchId: string | number;
  branchNo: string;
  meetingId: string | number;
  societyCommitteeId: string | number;
  societyNonCitizenCommitteeId: string | number;
  branchCommitteeId?: string | number;
  appealId: string | number;
  statementId: string | number;
  amendmentId: string | number;
  liquidationId: string | number;
  feedbackId: string | number;
  icNo: number;
  name: string;
  note: string;
  url: string;
  doc: string;
  status: number;
}
