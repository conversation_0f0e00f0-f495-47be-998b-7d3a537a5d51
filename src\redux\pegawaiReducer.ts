import { createSlice,PayloadAction } from '@reduxjs/toolkit';

interface pegawai{
  data:pegawaiProps

}

interface OfficerItem {
  id: string | number;
  name: string;
  identificationNo: string; 
}

interface pegawaiProps{
  publicOfficerId:string|number|undefined; 
  propertyOfficerId:string|number|undefined; 
  publicOfficerName:string | OfficerItem[];
  createdDate:string;
  paymentReferenceNo:string;
}
 

const initialState:pegawai= { data:{publicOfficerId:undefined,propertyOfficerId:undefined,publicOfficerName:"",createdDate:"",paymentReferenceNo:""}}

export const pegawaiSlice = createSlice({
  name: 'pegawai',
  initialState:initialState,
  reducers: {
    setPublicOfficerId:(state,action:PayloadAction<string|number>)=>{
        state.data.publicOfficerId = action.payload
    },
    setPropertyOfficerId:(state,action:PayloadAction<string|number>)=>{
    state.data.propertyOfficerId = action.payload
    },
    setPublicOfficerName:(state,action:PayloadAction<string|OfficerItem[]>)=>{
      state.data.publicOfficerName = action.payload
    },  
    setPegawaiCreatedDate:(state,action:PayloadAction<string>)=>{
      state.data.createdDate = action.payload
    },  
    setPegawaiPaymentReferenceNo:(state,action:PayloadAction<string>)=>{
      state.data.paymentReferenceNo = action.payload
    },   
    resetPegawaiState: (state) => { 
      state.data = { ...initialState.data };
    }, 
  },
});
 
export const { setPublicOfficerId,setPropertyOfficerId,setPublicOfficerName,setPegawaiCreatedDate,setPegawaiPaymentReferenceNo,resetPegawaiState} = pegawaiSlice.actions;
export const getPegawaiState = (state: any) => state.pegawai.data;
export default pegawaiSlice.reducer;
