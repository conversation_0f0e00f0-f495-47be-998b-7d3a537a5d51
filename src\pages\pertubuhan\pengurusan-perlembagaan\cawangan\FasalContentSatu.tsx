import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import { ConstitutionType, OrganizationLevelOption, useQuery } from "@/helpers";
import AsalContent from "@/components/asalFasalContent";
import FasalDisplayContent from "@/components/FasalComponent/FasalDisplayContent";
import { setSocietyDataRedux } from "@/redux/societyDataReducer";

interface FasalContentSatuCawanganProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: [];
  name: string;
}

export const FasalContentSatuCawangan: React.FC<
  FasalContentSatuCawanganProps
> = ({ activeStep, setActiveStep, clause, asalData, name }) => {
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [singkatanNama, setSingkatanNama] = useState("");
  const [takrifNama, setTakrifNama] = useState("");
  const [clauseContentId, setClauseContentId] = useState<string|number>("");;
  const [tarafPertubuhan, setTarafPertubuhan] = useState("");
  const [asal, setAsal] = useState<any>(null);
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  //previous content
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  const [isNameExist, setIsNameExist] = useState("");
  const [oldName, setOldName] = useState("");

  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { id, clauseId } = useParams();

  const { data } = useCustom({
    url: `${API_URL}/society/${id}/basic`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      onSuccess: (responseData) => {
        const { societyName, societyLevel } = responseData?.data?.data;
        if (!isEdit) {
          setNamaPertubuhan(societyName);
          setTarafPertubuhan(societyLevel);
        }
      },
    },
  });

  const { data: amendmentData } = useCustom({
    url: `${API_URL}/society/amendment/getAmendmentByParam`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        id: amendmentId,
      },
    },
    queryOptions: {
      enabled: !!amendmentId,
    },
  });

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const errors: { [key: string]: string } = {};
    if (namaPertubuhan?.length === 0) {
      errors.namaPertubuhan = t("fieldRequired");
    } else if (namaPertubuhan.length < 7) {
      errors.namaPertubuhan = "Sila masukkan sekurang-kurangnya 5 aksara";
    }
    return errors;
  };

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  let clauseContent = clause.clauseContent;
  clauseContent = clauseContent.replaceAll(
    /<<NAMA PERTUBUHAN>>/gi,
    `<b>${namaPertubuhan || "<<NAMA PERTUBUHAN>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<SINGKATAN NAMA>>/gi,
    `<b>${singkatanNama || "<<SINGKATAN NAMA>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<TAKRIF NAMA>>/gi,
    `<b>${takrifNama || "<<TAKRIF NAMA>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<TARAF PERTUBUHAN>>/gi,
    `<b>${tarafPertubuhan || "<<TARAF PERTUBUHAN>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  useEffect(() => {
    if (clause) {  
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      setDataId(clause.id);
      const fieldMappings: Record<string, (value: string) => void> = {
        "Nama Pertubuhan": setNamaPertubuhan,
        "Singkatan Nama": setSingkatanNama,
        "Takrif Nama": setTakrifNama,
        "Taraf Pertubuhan": setTarafPertubuhan,
      };

      Object.values(fieldMappings).forEach((setter) => setter(""));

      if (clause.constitutionValues) {
        clause.constitutionValues.forEach((item: any) => {
          const setter = fieldMappings[item.titleName];
          if (setter && item.definitionName) {
            setter(item.definitionName);
          }
        });
      }
      setIsEdit(clause.edit);
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent);
        setClauseContentId(clause.clauseContentId);
      }
      setAsal(
        asalData.find(
          (item: any) => item.clauseNo === clause.clauseNo
        ) || ""
      );
    }
  }, [clause]);

  useEffect(() => {
    if (societyDataRedux && !isEdit) {
      setTarafPertubuhan(societyDataRedux.societyLevel);
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
      setOldName(societyDataRedux.societyName); 
    }
  }, [societyDataRedux]);

  useEffect(() => {
    setFormErrors({});
  }, [i18n?.language]);

  const [debouncedSocietyName, setDebouncedSocietyName] = useState("");
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSocietyName(namaPertubuhan);
    }, 500); // Adjust 500ms delay as needed

    return () => clearTimeout(timer);
  }, [namaPertubuhan]);

  useEffect(() => {
    if (debouncedSocietyName !== oldName && debouncedSocietyName?.length > 5) {
      refetch();
    }
  }, [debouncedSocietyName]);

  const {
    data: societyNameExist,
    isLoading: isLoadingSocietyNameExist,
    refetch,
  } = useQuery({
    autoFetch: false,
    url: `society/checkSocietyNameExists`,
    filters: [
      {
        field: "societyName",
        operator: "eq",
        value: debouncedSocietyName,
      },
    ],
    onSuccess: (data) => {
      const response = data?.data;

      const errors: { [key: string]: string } = {};

      if (response?.data === true) {
        errors.societyName = t(response?.msg);
        setIsNameExist(t(response?.msg));
      } else {
        setIsNameExist("");
      }

      if (!namaPertubuhan) {
        // Organization name validation
      } else {
        const namePattern = /^(pertubuhan|persatuan|kelab)\s/i;
        if (!namePattern.test(namaPertubuhan)) {
          errors.societyName =
            "Nama pertubuhan hendaklah bermula dengan Pertubuhan/ persatuan/ kelab";
          setIsNameExist(
            "Nama pertubuhan hendaklah bermula dengan Pertubuhan/ persatuan/ kelab"
          );
        }
      }
      setFormErrors(errors);
    },
  });

  const handleUpdateSociety = () => {
    Edit();
  };
  const dispatch = useDispatch();

  const { mutate: edit, isLoading: isLoadingEdit } = useCustomMutation();
  const Edit = (): void => {
    edit(
      {
        url: `society/${societyId}/edit`,
        method: "put",
        values: {
          societyName: namaPertubuhan,
          societyLevel: tarafPertubuhan,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status === "SUCCESS") {
            const temp = {
              ...societyDataRedux,
              societyName: namaPertubuhan,
              societyLevel: tarafPertubuhan,
            };
            dispatch(setSocietyDataRedux(temp));
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  return (
    <>
      <AsalContent
        asalData={asal?.description || ""}
        clauseId={clauseId}
        name={asal?.clauseName}
      />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clause")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("namaPertubuhan")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              name="namaPertubuhan"
              size="small"
              value={namaPertubuhan}
              placeholder={`${t("namaPertubuhan")}`}
              fullWidth
              required
              disabled={isViewMode}
              sx={{ background: isViewMode && "#E8E9E8" }}
              error={!!isNameExist || !!formErrors.namaPertubuhan}
              helperText={isNameExist}
              onChange={(e) => {
                setNamaPertubuhan(e.target.value);

                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  namaPertubuhan: "",
                }));
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>
              {t("taraf")} <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <Select
              size="small"
              fullWidth
              required
              value={tarafPertubuhan}
              displayEmpty
              disabled={isViewMode}
              sx={{ background: isViewMode && "#E8E9E8" }}
              onChange={(e) => {
                setTarafPertubuhan(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  tarafPertubuhan: "",
                }));
              }}
              renderValue={(selected) =>
                selected
                  ? OrganizationLevelOption.find(
                      (item) => item.value === selected
                    )?.value
                  : t("organizationLevel")
              }
            >
              {OrganizationLevelOption.map((item) => (
                <MenuItem key={item.value} value={item.value}>
                  {item.value}
                </MenuItem>
              ))}
            </Select>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("nameAbbreviation")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              disabled={isViewMode}
              sx={{ background: isViewMode && "#E8E9E8" }}
              size="small"
              value={singkatanNama}
              fullWidth
              onChange={(e) => {
                setSingkatanNama(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  singkatanNama: "",
                }));
              }}
              error={!!formErrors.singkatanNama}
              helperText={formErrors.singkatanNama}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography sx={labelStyle}>{t("nameDefinition")}</Typography>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              disabled={isViewMode}
              sx={{ background: isViewMode && "#E8E9E8" }}
              size="small"
              value={takrifNama}
              placeholder={`${t("nameDefinitionPlaceholder")}`}
              fullWidth
              multiline
              rows={5}
              onChange={(e) => {
                setTakrifNama(e.target.value);
                setFormErrors((prevErrors) => ({
                  ...prevErrors,
                  takrifNama: "",
                }));
              }}
              error={!!formErrors.takrifNama}
              helperText={formErrors.takrifNama}
            />
          </Grid>
        </Grid>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("clauseContent")} {clauseId}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <FasalDisplayContent clauseContent={clauseContent} />
            </Box>
          </Grid>
        </Grid>
      </Box>

      {isViewMode ? null : (
        <Box
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Grid item xs={12}>
            <FormControlLabel
              sx={{
                color: "#666666",
                "&.MuiFormControlLabel-label": {
                  fontWeight: "400 !important",
                },
              }}
              control={
                <Checkbox checked={checked} onChange={handleChangeCheckbox} />
              }
              label={`${t("checkBox")}`}
            />
            <span style={{ color: "red" }}>*</span>
          </Grid>
        </Box>
      )}
      <Grid
        item
        xs={12}
        sx={{ mt: 2, display: "flex", justifyContent: "flex-end", gap: 1 }}
      >
        <ButtonOutline onClick={() => navigate(-1)}>{t("back")}</ButtonOutline>
        {isViewMode ? null : (
          <ButtonPrimary
            variant="contained"
            sx={{ width: isMobile ? "100%" : "auto" }}
            onClick={() => {
              const errors = validateForm();
              if (Object.keys(errors).length > 0) {
                setFormErrors(errors);
                return;
              }

              // handleUpdateSociety();
              handleSaveContent({
                i18n,
                societyId: id,
                societyName: namaPertubuhan,
                amendmentId: amendmentId,
                clauseContentId,
                dataId,
                isEdit,
                clauseNo: clauseNo,
                clauseName: clauseName,
                createClauseContent,
                editClauseContent,
                description: clauseContent,
                constitutionValues: [
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: namaPertubuhan,
                    titleName: "Nama Pertubuhan",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: singkatanNama,
                    titleName: "Singkatan Nama",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: takrifNama,
                    titleName: "Takrif Nama",
                  },
                  {
                    constitutionContentId: null,
                    societyName: namaPertubuhan,
                    definitionName: tarafPertubuhan,
                    titleName: "Taraf Pertubuhan",
                  },
                ],
                clause: "clause1",
                clauseCount: 1,
              });
            }}
            disabled={
              isCreatingContent ||
              isEditingContent ||
              !checked ||
              isNameExist?.length > 0 ||
              namaPertubuhan?.length < 7
            }
          >
            {isCreatingContent || isEditingContent ? t("saving") : t("save")}
          </ButtonPrimary>
        )}
      </Grid>
    </>
  );
};

export default FasalContentSatuCawangan;
