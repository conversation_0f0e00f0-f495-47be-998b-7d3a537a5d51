import Box from "@mui/material/Box";
import React, { useState } from "react";
import { Grid, InputLabel, Stack } from "@mui/material";
import { useTranslation } from "react-i18next";
import SectionHeader from "../../../components/header/section/SectionHeader";
import { PaparMaklumatPerlembagaanSection } from "./PaparMaklumatPerlembagaanSection";
import { PaparPindaanPerlembagaanSection } from "./PaparPindaanPerlembagaanSection";

export interface Organization {
  id: string | number;
  name: string;
  code: string;
}

export const PaparMaklumatPerlembagaan: React.FC = () => {
  const { t } = useTranslation();
  const [organization, setOrganization] = useState<Organization>({
    id: 1,
    name: "Pendidikan Anak Selangor",
    code: "PPM-013-10-21012015",
  });

  return (
    <Stack
      // sx={{ px: 6, py: 3 }}
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        border: "1px solid #D9D9D9",
        backgroundColor: "#FCFCFC",
        borderRadius: "14px",
      }}
      gap={2}
    >
      {/* <SectionHeader
        title={`${t("organization")} ${organization.name} / ${
          organization.code
        }`}
        sx={{
          backgroundColor: "#E8E9E8",
          boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
        }}
      /> */}

      <PaparMaklumatPerlembagaanSection />
      <PaparPindaanPerlembagaanSection />
    </Stack>
  );
};

export default PaparMaklumatPerlembagaan;
