export interface IFeedbackQuestion {
  id: number;
  question: string;
  publised: boolean;
}

export interface IEventFeedbackQuestion {
  feedbackQuestionId: number;
  question: string;
}
export interface EventFeedbackAnswer {
  feedbackScaleCode: string;
  feedbackQuestionId: number;
}
export interface EventFeedbackAnswer2 {
  eventFeedbackScaleCode: string;
  feedbackQuestionId: number;
}

export interface IEventFeedback {
  feedbackQuestionId: number;

}

export interface FeedbackScale{
  id: number;
  code: string;
  label: string;
  sortOrder: number;
}
