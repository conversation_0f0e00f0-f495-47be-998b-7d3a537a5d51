export interface IFeedbackQuestion {
  id: string | number;
  question: string;
  publised: boolean;
}

export interface IEventFeedbackQuestion {
  feedbackQuestionId: string | number;
  question: string;
}
export interface EventFeedbackAnswer {
  feedbackScaleCode: string;
  feedbackQuestionId: string | number;
}
export interface EventFeedbackAnswer2 {
  eventFeedbackScaleCode: string;
  feedbackQuestionId: string | number;
}

export interface IEventFeedback {
  feedbackQuestionId: string | number;

}

export interface FeedbackScale {
  id: string | number;
  code: string;
  label: string;
  sortOrder: number;
}
